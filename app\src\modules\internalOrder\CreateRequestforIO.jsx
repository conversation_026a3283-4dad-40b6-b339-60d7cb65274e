import React, { useEffect, useState, useMemo, useRef } from "react";
import { Box, Button, DialogActions, DialogContent, Grid, Icon<PERSON>utton, Step, StepButton, Stepper, Typography } from "@mui/material";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import { ArrowCircleLeftOutlined, WarningOutlined } from "@mui/icons-material";
import { useNavigate, useLocation } from "react-router-dom";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { button_Outlined, button_Primary } from "@components/common/commonStyles";
import { colors } from "@constant/colors";
import useLang from "@hooks/useLang";
import { APP_END_POINTS } from "@constant/appEndPoints";
import RequestHeaderIO from "./RequestHeaderIO";
import useGenericDtCall from "@hooks/useGenericDtCall";
import { useDispatch, useSelector } from "react-redux";
import { setRequestHeaderDTIO, setTabValue, resetInternalOrderState } from "./slice/InternalOrderSlice";
import { setLocalStorage } from "@helper/glhelper";
import { LOCAL_STORAGE_KEYS, MODULE_MAP, ARTIFACTNAMES, REQUEST_STATUS, REQUEST_TYPE } from "@constant/enum";
import RequestDetailsIO from "./RequestDetailsIO";
import useIOdropdownData from "./hooks/useIOdropdownData";
import useDisplayInternalOrderData from "./hooks/useDisplayInternalOrderData";
import AttachmentsCommentsTab from "@components/RequestBench/RequestPages/AttachmentsCommentsTab";
import PreviewPage from "@components/RequestBench/PreviewPage";

const CreateRequestforIO = () => {
  const { t } = useLang();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const urlSearchParams = new URLSearchParams(location.search.split("?")[1]);
  const requestId = urlSearchParams.get("RequestId");
  const queryParams = new URLSearchParams(location.search);
  const RequestType = queryParams.get("RequestType");
  const reqBench = queryParams.get("reqBench");

  const { getDtCall, dtData } = useGenericDtCall();
  const { getDtCall: getAttachmentDt, dtData: attachmentDtData } = useGenericDtCall();
  const [isDialogVisible, setisDialogVisible] = useState(false);
  const [completed, setCompleted] = useState([false]);
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(false);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const { fetchAllDropdownIOData } = useIOdropdownData();
  const { getDisplayInternalOrderData, loading: displayLoading, error: displayError } = useDisplayInternalOrderData();
  const savedRequestData = useSelector((state) => state.internalOrder.savedReqData);
  const tabValue = useSelector((state) => state.internalOrder.tabValue);
  const [attachmentsData, setAttachmentsData] = useState([]);
  const [displayDataLoaded, setDisplayDataLoaded] = useState(false);
  const [lastLoadedRequestId, setLastLoadedRequestId] = useState(null);
  const IOpayloadData = useSelector((state) => state.internalOrder.IOpayloadData);

  const steps = [t("Request Header"), t("Internal Order List"), t("Attachments & Remarks"), t("Preview")];

  const handleTabChange = (index) => {
    dispatch(setTabValue(index));
  };

  const handleYes = () => {
    if (requestId && !reqBench) {
      navigate(APP_END_POINTS?.MY_TASK);
    } else if (reqBench) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    } else if (!requestId && !reqBench) {
      navigate(APP_END_POINTS?.INTERNAL_ORDER);
    }
  };
  const handleCancel = () => {
    setisDialogVisible(false);
  };

  const fetchHeaderFieldsFromDt = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_FMD_REQUEST_HEADER_CONFIG",
      version: "v2",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_MAT_MODULE_NAME": MODULE_MAP.IO,
        },
      ],
    };
    getDtCall(payload);
  };

  const fetchAttachmentFieldsFromDt = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": MODULE_MAP.IO,
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    getAttachmentDt(payload);
  };

  useEffect(() => {
    if (dtData) {
      let responseData = dtData?.result[0]?.MDG_MAT_REQUEST_HEADER_CONFIG;
      const formattedData = responseData
        .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
        .map((item) => ({
          fieldName: item.MDG_MAT_UI_FIELD_NAME,
          sequenceNo: item.MDG_MAT_SEQUENCE_NO,
          fieldType: item.MDG_MAT_FIELD_TYPE,
          maxLength: item.MDG_MAT_MAX_LENGTH,
          value: item.MDG_MAT_DEFAULT_VALUE,
          visibility: item.MDG_MAT_VISIBILITY,
          jsonName: item.MDG_MAT_JSON_FIELD_NAME,
        }));

      const requestHeaderObj = { "Header Data": formattedData };
      dispatch(setRequestHeaderDTIO(requestHeaderObj));
    }
  }, [dtData]);
  useEffect(() => {
    if (attachmentDtData) {
      let responseData = attachmentDtData?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE;
      const attachmentNames = responseData || [];

      // If no attachment types are configured for Internal Order, provide a default
      if (attachmentNames.length === 0) {
        const defaultAttachmentType = [
          {
            MDG_ATTACHMENTS_NAME: "Document",
            MDG_ATTACH_CHNG_ENT_FIELDS: "",
          },
        ];
        setAttachmentsData(defaultAttachmentType);
      } else {
        setAttachmentsData(attachmentNames);
      }
    }
  }, [attachmentDtData]);

  useEffect(() => {
    fetchHeaderFieldsFromDt();
    fetchAttachmentFieldsFromDt();
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE, MODULE_MAP.IO);
  }, [requestId, reqBench]);

  useEffect(() => {
    dispatch(resetInternalOrderState());
  }, []);
  useEffect(() => {
    fetchAllDropdownIOData();
  }, []);

  useEffect(() => {
    const loadInternalOrderData = async () => {
      if (requestId) {
        await getDisplayInternalOrderData(requestId, null, reqBench, null, { isBifurcated: false });

        const requestStatus = IOpayloadData?.requestHeaderData?.RequestStatus;

        const isDraftOrFailed = requestStatus === REQUEST_STATUS.DRAFT || requestStatus === REQUEST_STATUS.UPLOAD_FAILED;

        const isChangeWithUpload = RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD;
        const isCreateWithUpload = RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD;

        const hasEmptyBody = !IOpayloadData?.rowsBodyData || Object.keys(IOpayloadData?.rowsBodyData || {}).length === 0;

        if ((isChangeWithUpload && hasEmptyBody) || isCreateWithUpload) {
          if (isDraftOrFailed) {
            dispatch(setTabValue(0));
            setIsSecondTabEnabled(false);
            setIsAttachmentTabEnabled(false);
            return;
          }
        }

        dispatch(setTabValue(1));
        setIsSecondTabEnabled(true);
        setIsAttachmentTabEnabled(true);
      } else {
        dispatch(setTabValue(0));
      }
    };

    loadInternalOrderData();
  }, [requestId]);

  return (
    <>
      <Box sx={{ padding: 2 }}>
        <Grid sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Typography variant="h6" sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <PermIdentityOutlinedIcon sx={{ fontSize: "1.5rem" }} />
            {t("Request Header ID")}: <span>{savedRequestData?.RequestId || IOpayloadData?.requestHeaderData?.RequestId}</span>
          </Typography>
        </Grid>

        <IconButton onClick={() => setisDialogVisible(true)} color="primary" title={t("Back")} sx={{ left: "-10px" }}>
          <ArrowCircleLeftOutlined sx={{ fontSize: "25px", color: "#000000" }} />
        </IconButton>

        <Stepper nonLinear activeStep={tabValue} sx={{ justifyContent: "center", margin: "25px 14%", marginTop: "-35px" }}>
          {steps.map((label, index) => (
            <Step key={label} completed={completed[index]}>
              <StepButton onClick={() => handleTabChange(index)}>
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>{label}</span>
              </StepButton>
            </Step>
          ))}
        </Stepper>
        <Box sx={{ padding: "20px", borderRadius: "8px" }}>
          {tabValue === 0 && <RequestHeaderIO setIsSecondTabEnabled={setIsSecondTabEnabled} setIsAttachmentTabEnabled={setIsAttachmentTabEnabled} />}
          {tabValue === 1 && <RequestDetailsIO />}
          {tabValue === 2 && <AttachmentsCommentsTab requestStatus={REQUEST_STATUS.ENABLE_FOR_FIRST_TIME} attachmentsData={attachmentsData} requestIdHeader={savedRequestData?.RequestId} pcNumber={savedRequestData?.RequestId} module={MODULE_MAP?.IO} artifactName={ARTIFACTNAMES.IO} />}
          {tabValue === 3 && (
            <Box
              sx={{
                width: "100%",
                overflow: "auto",
              }}
            >
              <PreviewPage requestStatus={REQUEST_STATUS.ENABLE_FOR_FIRST_TIME} module={MODULE_MAP?.IO} payloadData={IOpayloadData} payloadForDownloadExcel={IOpayloadData} />
            </Box>
          )}
        </Box>
      </Box>

      {isDialogVisible && (
        <CustomDialog isOpen={isDialogVisible} titleIcon={<WarningOutlined sx={{ color: colors.secondary.amber, fontSize: "20px" }} />} Title={t("Warning")} handleClose={handleCancel}>
          <DialogContent sx={{ mt: 2 }}>{t("Are you sure you want to leave this page?")}</DialogContent>
          <DialogActions>
            <Button variant="outlined" size="small" sx={button_Outlined} onClick={handleCancel}>
              {t("No")}
            </Button>
            <Button variant="contained" size="small" sx={button_Primary} onClick={handleYes}>
              {t("Yes")}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </>
  );
};

export default CreateRequestforIO;
