import { documentApi } from './DocumentApiService';
import { END_POINTS } from '@constant/apiEndPoints';

export const attachmentsApi = documentApi.injectEndpoints({
  endpoints: (builder) => ({
    getChildAttachments: builder.query({
      query: (arg) => {
        const { requestId, hasAnyChildRequestId } = arg || {};
       
        const url = hasAnyChildRequestId
          ? `/${END_POINTS.TASK_ACTION_DETAIL.GET_CHILD_DOCS}/${requestId}`
          : `/${END_POINTS.TASK_ACTION_DETAIL.GET_DOCS}/${requestId}`;
        return url;
      },
    }),
    uploadAttachment: builder.mutation({
      query: (body) => ({
        url: END_POINTS.DMS_API.UPLOAD_DOCUMENT,
        method: 'POST',
        body,
      }),
    }),
  }),
});

export const { useGetChildAttachmentsQuery, useUploadAttachmentMutation } = attachmentsApi;
