import React, { useEffect, useState } from "react";
import {
  Autocomplete,
  Button,
  Checkbox,
  FormControl,
  Grid,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import ReusableTable from "../../Common/ReusableTable";
import { container_Padding, button_Outlined } from "../../Common/commonStyles";
import { DataGrid } from "@mui/x-data-grid";
import { destination_MaterialMgmt } from "../../../destinationVariables";
import { doAjax } from "../../Common/fetchService";
import { useSelector } from "react-redux";

const DisplayUnitsOfMeasureTab = (props) => {
  const [isLoading, setIsLoading] = useState(false);
  const [rmDataRows, setRmDataRows] = useState([]);
  const [textValue, setTextValue] = useState("");
  const [additionalData, setAdditionalData] = useState({});
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  console.log("drop", dropDownData);
  const payloadData = useSelector((state) => state.payload.payloadData);
  console.log("payl", payloadData);
  const valueFromPayload = useSelector((state) => state.payload.payloadData);
  const [uomRows, setuomRows] = useState([
    {
      id: 1,
      xValue: "1",
      aUnit: payloadData?.BaseUnit?.code,
      measureUnitText: payloadData?.BaseUnit?.desc,
      resemble: "<=>",
      yValue: "1",
      bUnit: payloadData?.BaseUnit?.code,
      measurementUnitText: payloadData?.BaseUnit?.desc,
      eanUpc: "",
      eanCategory: "",
      autoCheckDigit: "",
      addEans: "",
      length: "",
      width: "",
      height: "",
      unitsOfDimension: "",
      volume: "",
      volumeUnit: "",
      grossWeight: "",
      netWeight: "",
      weightUnit: "",
      noLowerLvlUnits: "",
      lowerLvlUnits: "",
      remVolAfterNesting: "",
      maxStackFactor: "",
      maxTopLoadFullPkg: "",
      UomToploadFullPkg: "",
      capacityUsage: "",
      UomCategory: "",
    },
    {
      id: 2,
      xValue: "",
      aUnit: "",
      measureUnitText: "",
      resemble: "<=>",
      yValue: "1",
      bUnit: payloadData?.BaseUnit?.code,
      measurementUnitText: payloadData?.BaseUnit?.desc,
      eanUpc: "",
      eanCategory: "",
      autoCheckDigit: "",
      addEans: "",
      length: "",
      width: "",
      height: "",
      unitsOfDimension: "",
      volume: "",
      volumeUnit: "",
      grossWeight: "",
      netWeight: "",
      weightUnit: "",
      noLowerLvlUnits: "",
      lowerLvlUnits: "",
      remVolAfterNesting: "",
      maxStackFactor: "",
      maxTopLoadFullPkg: "",
      UomToploadFullPkg: "",
      capacityUsage: "",
      UomCategory: "",
    },
  ]);
  const columns = [
    {
      field: "xValue",
      headerName: "X",
      type: "text",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            x.xValue = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "aUnit",
      headerName: "AUn",
      editable: true,
      type: "singleSelect",
      valueOptions: dropDownData?.BaseUnit?.map((item) => item.code),
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            return { ...x, language: params?.props?.value };
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "measureUnitText",
      headerName: "Measurement Unit Text",
      type: "text",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            x.measureUnitText = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "resemble",
      headerName: "<=>",
      editable: true,
    },
    {
      field: "yValue",
      headerName: "Y",
      type: "text",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            x.yValue = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "bUnit",
      headerName: "BUn",
      editable: true,
      type: "singleSelect",
      valueOptions: dropDownData?.BaseUnit?.map((item) => item.code),
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            return { ...x, language: params?.props?.value };
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "measurementUnitText",
      headerName: "Measurement Unit Text",
      type: "text",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            x.measurementUnitText = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "eanUpc",
      headerName: "EAN/UPC",

      type: "text",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            x.eanUpc = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "eanCategory",
      headerName: "EAN Category ",

      type: "singleSelect",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            x.eanCategory = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    }, //dropdown
    {
      field: "autoCheckDigit",
      headerName: "Auto Check Digit",

      renderCell: (params) => <Checkbox sx={{ padding: 0 }} />,
    },
    {
      field: "addEans",
      headerName: "Additional EANs",

      renderCell: (params) => <Checkbox sx={{ padding: 0 }} />,
    },
    {
      field: "length",
      headerName: "Length",

      type: "text",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            x.length = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "width",
      headerName: "Width",

      type: "text",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            x.width = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "height",
      headerName: "Height",

      type: "text",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            x.height = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "unitsOfDimension",
      headerName: "Unit of Dimension",
      editable: true,
      type: "singleSelect",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            x.unitsOfDimension = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "volume",
      headerName: "Volume",
      type: "text",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            x.volume = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "volumeUnit",
      headerName: "Volume Unit",
      editable: true,
      type: "singleSelect",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            x.volumeUnit = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "grossWeight",
      headerName: "Gross Weight",
      type: "text",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            x.grossWeight = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "netWeight",
      headerName: "Net Weight",

      type: "text",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            x.netWeight = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "weightUnit",
      headerName: "Weight Unit",
      editable: true,
      type: "singleSelect",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            x.weightUnit = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "noLowerLvlUnits",
      headerName: "No. Lower- Level Units",

      type: "text",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = uomRows?.map((x) => {
          if (x.id === params.id) {
            x.noLowerLvlUnits = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "lowerLvlUnits",
      headerName: "Lower Level Units",
      editable: true,
      type: "singleSelect",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = initialRows?.map((x) => {
          if (x.id === params.id) {
            x.lowerLvlUnits = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "remVolAfterNesting",
      headerName: "Remainder Volume after Nesting",

      type: "text",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = initialRows?.map((x) => {
          if (x.id === params.id) {
            x.remVolAfterNesting = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "maxStackFactor",
      headerName: "Max. Stacking Factor",

      type: "text",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = initialRows?.map((x) => {
          if (x.id === params.id) {
            x.maxStackFactor = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "maxTopLoadFullPkg",
      headerName: "Max. Top Load on full Package",

      type: "text",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = initialRows?.map((x) => {
          if (x.id === params.id) {
            x.maxTopLoadFullPkg = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "UomToploadFullPkg",
      headerName: "UOM of Max. Topload on Full Package",
      editable: true,
      type: "singleSelect",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = initialRows?.map((x) => {
          if (x.id === params.id) {
            x.UomToploadFullPkg = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "capacityUsage",
      headerName: "Capacity Usage",

      type: "text",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = initialRows?.map((x) => {
          if (x.id === params.id) {
            x.capacityUsage = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
    {
      field: "UomCategory",
      headerName: "UOM Category",
      editable: true,
      type: "singleSelect",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = initialRows?.map((x) => {
          if (x.id === params.id) {
            x.UomCategory = params?.props?.value;
          }
          return x;
        });
        setuomRows(rowsData);
      },
    },
  ];

  const handleAddNewRow = () => {
    setuomRows((prev) => {
      return [
        ...prev,
        {
          id: prev.length + 1,
          xValue: "",
          aUnit: "",
          measureUnitText: "",
          resemble: "<=>",
          yValue: "1",
          bUnit: payloadData?.BaseUnit?.code,
          measurementUnitText: payloadData?.BaseUnit?.desc,
          eanUpc: "",
          eanCategory: "",
          autoCheckDigit: "",
          addEans: "",
          length: "",
          width: "",
          height: "",
          unitsOfDimension: "",
          volume: "",
          volumeUnit: "",
          grossWeight: "",
          netWeight: "",
          weightUnit: "",
          noLowerLvlUnits: "",
          lowerLvlUnits: "",
          remVolAfterNesting: "",
          maxStackFactor: "",
          maxTopLoadFullPkg: "",
          UomToploadFullPkg: "",
          capacityUsage: "",
          UomCategory: "",
        },
      ];
    });
  };
  useEffect(() => {
    console.log(uomRows);
  }, [uomRows]);

  return (
    <div>
      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          mt: 0.25,
          ...container_Padding,
          // ...container_columnGap,
        }}
      >
        <Grid container display="block">
          <Grid item sx={{ paddingTop: "2px !important", mb: 1 }}>
            <Stack flexDirection="row" alignItems="center">
              <div style={{ width: "5%" }}>
                <Typography variant="body2" color="#777">
                  Material
                </Typography>
              </div>
              <Typography variant="body2" fontWeight="bold">
                :
              </Typography>
              <TextField
                size="small"
                sx={{ ml: 2 }}
                value={"textValue"}
                onChange={(e) => {
                  setTextValue(e.target.value);
                }}
              />
            </Stack>
          </Grid>
          <Grid item sx={{ paddingTop: "2px !important", mb: 1 }}>
            <Stack flexDirection="row" alignItems="center">
              <div style={{ width: "5%" }}>
                <Typography variant="body2" color="#777">
                  Description
                </Typography>
              </div>
              <Typography variant="body2" fontWeight="bold">
                :
              </Typography>
              <TextField
                size="small"
                sx={{ ml: 2 }}
                value={"textValue"}
                onChange={(e) => {
                  setTextValue(e.target.value);
                }}
              />
            </Stack>
          </Grid>

          <Grid container display="block" sx={{ padding: 2 }}>
            <Grid item sx={{ paddingTop: "2px !important", mb: 1 }}>
              <Stack flexDirection="row" alignItems="center">
                <div style={{ width: "7%" }}>
                  <Typography variant="body2" color="#777">
                    Units Of Measure Group
                  </Typography>
                </div>
                <Typography variant="body2" fontWeight="bold">
                  :
                </Typography>
                <Grid item md={1.5}>
                  <Autocomplete
                    sx={{ height: "31px", ml: 2 }}
                    size="small"
                    value={additionalData?.BaseUnit}
                    onChange={(e, value) => {
                      setAdditionalData({
                        ...additionalData,
                        BaseUnit: value,
                      });
                    }}
                    options={dropDownData?.BaseUnit ?? []}
                    getOptionLabel={(option) =>
                      `${option?.code} - ${option?.desc}`
                    }
                    renderOption={(props, option) => (
                      <li {...props}>
                        <Typography style={{ fontSize: 12 }}>
                          {option?.code} - {option?.desc}
                        </Typography>
                      </li>
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder="Select Unit Of Measure Group"
                      />
                    )}
                  />
                </Grid>
              </Stack>
            </Grid>
          </Grid>

          <div className="confirmOrder-lineItem">
            <ReusableTable
              scrollbarSize
              title={"Units of Measure/ EANs/ Dimensions  "}
              width="100%"
              rows={uomRows ?? []}
              columns={columns}
              getRowIdValue={"id"}
              hideFooter={true}
              checkboxSelection={false}
              experimentalFeatures={{ newEditingApi: true }}
            />
          </div>
        </Grid>
      </Grid>
    </div>
  );
};

export default DisplayUnitsOfMeasureTab;
