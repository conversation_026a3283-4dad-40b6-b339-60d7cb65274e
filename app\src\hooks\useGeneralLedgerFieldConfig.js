

import { setProfitCenterTabs,setProfitCenterConfig,setProfitCenterData } from "@app/profitCenterTabsSlice";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { groupBy, removeHiddenAndEmptyObjects, transformStructureForAllTabsData } from "@helper/helper";
import { destination_IDM } from "../destinationVariables";
import { doAjax } from "@components/Common/fetchService";
import { API_CODE, TASK_NAME, VISIBILITY_TYPE } from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "./useLogger";
import { setGeneralLedgerConfig, setGeneralLedgerData, setGeneralLedgerTabs } from "@app/generalLedgerTabSlice";

const transformGeneralLedgerFieldConfigData = (responseData) => {
 
  let mandatoryFields = {};
  let sortedData = responseData?.sort((a, b) => a.MDG_GL_VIEW_SEQUENCE - b.MDG_GL_VIEW_SEQUENCE);
  const groupedFields = groupBy(sortedData, "MDG_GL_VIEW_NAME");
  let view_data_array = [];
  Object.entries(groupedFields).forEach(([viewName, fields]) => {
    let groupedFieldsDataCardNameWise = groupBy(fields, "MDG_GL_CARD_NAME");
    let cards = [];

    Object.entries(groupedFieldsDataCardNameWise).forEach(([cardName, cardFields]) => {
      cardFields.sort((a, b) => a.MDG_GL_SEQUENCE_NO - b.MDG_GL_SEQUENCE_NO);

      let seenJsonNames = new Set(); // ❗️ Track jsonNames
      let cardDetails = [];

      for (let item of cardFields) {
        if (!seenJsonNames.has(item.MDG_GL_JSON_FIELD_NAME)) {
          seenJsonNames.add(item.MDG_GL_JSON_FIELD_NAME);
          cardDetails.push({
            fieldName: item.MDG_GL_UI_FIELD_NAME,
            sequenceNo: item.MDG_GL_SEQUENCE_NO,
            fieldType: item.MDG_GL_FIELD_TYPE,
            maxLength: item.MDG_GL_MAX_LENGTH,
            dataType: item.MDG_GL_DATA_TYPE,
            viewName: item.MDG_GL_VIEW_NAME,
            cardName: item.MDG_GL_CARD_NAME,
            cardSeq: item.MDG_GL_CARD_SEQUENCE,
            viewSeq: item.MDG_GL_VIEW_SEQUENCE,
            value: item.MDG_GL_DEFAULT_VALUE,
            visibility: item.MDG_GL_VISIBILITY,
            jsonName: item.MDG_GL_JSON_FIELD_NAME,
          });
        }
      }

      cards.push({
        cardName,
        cardSeq: cardFields[0].MDG_GL_CARD_SEQUENCE,
        viewSeq: cardFields[0].MDG_GL_VIEW_SEQUENCE,
        cardDetails,
      });
    });

    cards.sort((a, b) => a.cardSeq - b.cardSeq);
    view_data_array.push({ viewName, cards });
  });

  let filteredData = removeHiddenAndEmptyObjects(view_data_array);
  let transformedData = {};
  filteredData.forEach((view) => {
    let cardData = {};
    view.cards.forEach((card) => {
      cardData[card.cardName] = card.cardDetails;
      if (view.viewName !== "Request Header") {
        card.cardDetails.forEach((detail) => {
          if (detail.visibility === VISIBILITY_TYPE.MANDATORY) {
            if (!mandatoryFields[detail.viewName]) {
              mandatoryFields[detail.viewName] = [];
            }

            // Avoid pushing duplicate mandatory fields
            const alreadyExists = mandatoryFields[detail.viewName].some(
              (f) => f.jsonName === detail.jsonName
            );
            if (!alreadyExists) {
              mandatoryFields[detail.viewName].push({
                jsonName: detail?.jsonName,
                fieldName: detail?.fieldName,
              });
            }
          }
        });
      }
    });
    transformedData[view.viewName] = cardData;
  });

  return { transformedData, mandatoryFields };
};

  
  const useGeneralLedgerFieldConfig = () => {
    const dispatch = useDispatch();
    const { customError } = useLogger();
    const initialPayload = useSelector((state) => state.payload?.payloadData);
    const applicationConfig = useSelector((state) => state.applicationConfig);
     const valueFromPayloadGL = useSelector((state) => state.generalLedger.payload || {});

    const userData = useSelector((state) => state.userManagement.userData);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
  
    const getTemplateData = async () => {
      setLoading(true); 
      const payload = {
        decisionTableId: null,
        decisionTableName: "MDG_GL_FIELD_CONFIG",
        version: "v3",
        rulePolicy: null,
        validityDate: null,
        conditions: [
          {
            "MDG_CONDITIONS.MDG_GL_SCENARIO": initialPayload?.RequestType || "Create",
            "MDG_CONDITIONS.MDG_MAT_ROLE": TASK_NAME?.REQ_INITIATE_FIN,
            "MDG_CONDITIONS.MDG_GL_ACCOUNT_TYPE": "X",
          },
        ],
        systemFilters: null,
        systemOrders: null,
        filterString: null,
      };
  
      const hSuccess = (data) => {
        if (data.statusCode === API_CODE.STATUS_200) {
          if (Array.isArray(data?.data?.result) && data?.data?.result.every(item => Object.keys(item).length !== 0)) {
            let responseData = data?.data?.result[0]?.MDG_GL_FIELD_DETAILS_ACTION_TYPE;
            const { transformedData, mandatoryFields } = transformGeneralLedgerFieldConfigData(responseData);
            let glTabsData = Object.keys(transformedData);
  
            const allTabsData = glTabsData.map((tab) => ({
                tab,
                data: transformedData[tab],
              }));
              
              dispatch(setGeneralLedgerTabs(allTabsData));
  
            dispatch(setGeneralLedgerConfig({ GeneralLedger: { allfields: transformStructureForAllTabsData(glTabsData), mandatoryFields } }));
          } else {
            dispatch(setGeneralLedgerData({ GeneralLedger: {} }));
          }
          setLoading(false);
        }
      };
  
      const hError = (error) => {
        customError(error);
        setError(error);
        setLoading(false);
      };
  
      const url =
        applicationConfig.environment === "localhost"
          ? `/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}`
          : `/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`;
  
      doAjax(url, "post", hSuccess, hError, payload);
    };
  
    const fetchGeneralLedgerFieldConfig = () => {
      try {
        getTemplateData();
      } catch (err) {
        setError(err);
        setLoading(false);
      }
    };
  
    return { loading, error, fetchGeneralLedgerFieldConfig };
  };
  
  export default useGeneralLedgerFieldConfig;
  


