import {colors} from './colors';
export const reportStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottom: '1px solid #eee',
    padding: '15px 0',
    marginBottom: '10px',
    backgroundColor: '#f9f9f9',
    borderRadius: '8px',
    paddingLeft: '15px',
    paddingRight: '15px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    transition: 'transform 0.2s, box-shadow 0.2s',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
    },
  };
  
export const dialogStyle = {
    paper: {
      minWidth: '400px',
      borderRadius: '12px',
      padding: '20px',
      transition: 'opacity 0.3s ease-in-out',
    },
  };

export const dialogTitleStyle = {
  display:"flex",justifyContent:"center",alignItems:"center",gap:1, fontWeight: '500', color: '#555' 
};

export const tabsStyle = {
  textTransform: "none",
  backgroundColor: "transparent",  
  marginTop:"10px",
  "&:hover": {
    color: "#000 !important",  
    ".MuiTab-labelIcon": {
      color: colors.primary.white, 
    },
  },
  "&.Mui-selected": {
    backgroundColor: colors.tab.background,
    "& .MuiTypography-root": {
    fontWeight: "bold",
  },  
  },
  borderRadius: "10px 10px 0px 0px",
 };

export const tabLabelStyle = {fontSize: "16px",fontWeight: "bold"};

export const downloadButtonStyle = {
    marginLeft: "10px",
    transition: "all 0.3s ease-in-out",
    "&:hover": {
      backgroundColor: "#1976d2",
      color: "#fff",
      transform: "scale(1.1)",
    },
  };

export const closeButtonStyle = {
    backgroundColor: "#FFEEEE",
    color: "#D32F2F",
    padding: "8px 16px",
    borderRadius: "4px",
    border: "1px solid #D32F2F",
    transition: "all 0.3s ease-in-out",
    boxShadow: "0px 4px 6px rgba(211, 47, 47, 0.1)", // Subtle red shadow
    "&:hover": {
      backgroundColor: "#FFDADA",
      transform: "scale(1.05)",
      boxShadow: "0px 6px 10px rgba(211, 47, 47, 0.15)",
    },
    "&:active": {
      transform: "scale(0.98)",
      boxShadow: "0px 2px 4px rgba(211, 47, 47, 0.2)",
    },
  };