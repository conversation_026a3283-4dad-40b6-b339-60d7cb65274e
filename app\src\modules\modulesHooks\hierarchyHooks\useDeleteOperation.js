import { useCallback } from "react";
import { Modal, message } from "antd";
import { useDispatch } from "react-redux";
import { setTreeData, updateTreeChanges } from "@app/hierarchyDataSlice";

const { confirm } = Modal;

// Reassign IDs while maintaining the hierarchical structure
const reassignIds = (nodes, parentId = "") => {
  return nodes.map((node, index) => {
    const newId = parentId === "" ? `${index}` : `${parentId}-${index}`;
    return {
      ...node,
      id: newId,
      child: node.child ? reassignIds(node.child, newId) : [],
    };
  });
};

const removeNodeForDelete = (nodes, nodeId) => {
  return nodes
    .map((node) => {
      const updatedNode = {
        ...node,
        child: node.child ? removeNodeForDelete(node.child, nodeId) : [],
      };
      return updatedNode;
    })
    .filter((node) => node.id !== nodeId);
};

const useDeleteOperation = ({ rawTreeData, treeChanges, addToChangeLog }) => {
  const dispatch = useDispatch();

  const handleDeleteNode = useCallback(
    (node) => {
      confirm({
        title: `Delete ${node.label}?`,
        content:
          "Deleting node will remove all Profit Centers attached to the nodes & Sub-Nodes from this Hierarchy",
        okText: "Delete",
        okType: "danger",
        cancelText: "Cancel",
        onOk() {
          let updatedTree = removeNodeForDelete([...rawTreeData], node.id);
          updatedTree = reassignIds(updatedTree);

          addToChangeLog("DELETE NODE", `${node?.label} Node deleted`);

          dispatch(
            updateTreeChanges({
              nodeLabel: node.label,
              changes: {
                ...((treeChanges && treeChanges[node.label]) || {}),
                isDeleted: true,
              },
            })
          );

          dispatch(setTreeData(updatedTree));
          message.success(`Deleted ${node.label} and Updated Hierarchy`);
        },
      });
    },
    [rawTreeData, treeChanges, dispatch, addToChangeLog]
  );

  return { handleDeleteNode };
};

export default useDeleteOperation;
