import Paper from '@mui/material/Paper';
import InputBase from '@mui/material/InputBase';
import IconButton from '@mui/material/IconButton';
import SearchIcon from '@mui/icons-material/Search';
import CloseIcon from '@mui/icons-material/Close';
import { Box, Tooltip } from '@mui/material';
import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { commonSearchBarClear, commonSearchBarUpdate } from '../../app/commonSearchBarSlice';
import { colors } from "@constant/colors";
 
export default function SearchBar({ handleSearchAction = () => { },
  elasticSearch,
  onChange = () => { },
  module = '',
  title = '',
  message = '',
  keyName = '',
  query = '',
}) {
  const filterController = useSelector(
    (state) => state.commonSearchBar[module]
  );
  let dispatch = useDispatch()
  const [bdr, setbdr] = useState("")
  const [focused, setFocused] = useState(false);
  const handleKeyPress = (e) => {
 
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearchAction(filterController[keyName]);
    }
  }
  const handleFocus = () => {
    setbdr('2px solid #3b30c8')
  }
  const handleBlur = () => {
    setbdr("")
  }
 
  let handleOnChange = (e) => {
    if (elasticSearch) {
      dispatch(commonSearchBarUpdate({
        module: module, filterData: {
          [e.target.name]: e.target.value
        }
      }))
      onChange(e)
    }
    else {
      dispatch(commonSearchBarUpdate({
        module: module, filterData: {
          [e.target.name]: e.target.value
        }
      }))
      // setQuery(e.target.value)
    }
  }
  let clearSearchBar = () => {
    dispatch(commonSearchBarClear({ module: module }));
    handleSearchAction("");
  }
  return (
    <Tooltip title={title} arrow>
      <Paper
        component="form"
        sx={{
          display: 'flex',
          alignItems: 'center',
          width:'240px',
          height: '36px',
          backgroundColor: 'white',
          border: focused ? `2px solid ${colors.primary.main}` : `1px solid ${colors.primary.border}`,
          borderRadius: '8px',
          transition: 'all 0.2s ease',
          '&:hover': {
            border: `1px solid ${colors.primary.main}`,
            boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
          }
        }}
        elevation={0}
        onFocus={handleFocus}
        onBlur={handleBlur}
      >
        <InputBase
          sx={{
            ml: 1,
            flex: 1,
            fontSize: '14px',
            '& input': {
              padding: '4px 8px',
              '&::placeholder': {
                color: colors.secondary.grey,
                opacity: 1
              }
            }
          }}
          placeholder={message}
          value={filterController?.[keyName] ?? ""}
          name={keyName}
          onChange={handleOnChange}
          onKeyDown={handleKeyPress}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
        />

          {filterController?.[keyName] && <CloseIcon onClick={clearSearchBar} sx={{ fontSize: '16px', marginRight: '10px', '&:hover': { color: colors.error.red } }} />}
          <IconButton  sx={{ 
            p: '6px',
            ml: '2px',
            color: focused ? colors.primary.main : colors.secondary.grey
          }} onClick={() => handleSearchAction(filterController[keyName])}  >  <SearchIcon sx={{ fontSize: '20px' }} /></IconButton>
        
      </Paper>
    </Tooltip>
 
  );
}
 
