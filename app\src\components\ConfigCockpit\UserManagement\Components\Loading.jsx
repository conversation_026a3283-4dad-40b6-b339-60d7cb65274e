import React from "react";
import { makeStyles } from "@mui/styles";
import { CircularProgress } from "@mui/material";
// import { BeatLoader } from "react-spinners";

const useStyle = makeStyles((theme) => ({
  loadingContainer: {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%,-50%)",
    zIndex: "9999",
  },
}));

function Loading({ load }) {
  const classes = useStyle();

  return load ? (
    <div className={classes.loadingContainer}>
      <CircularProgress />
    </div>
  ) : null;
}

export default Loading;
