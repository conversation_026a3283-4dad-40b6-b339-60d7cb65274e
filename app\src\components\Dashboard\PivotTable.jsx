import React, { useState } from 'react'
import ReusableTable from '../common/ReusableTable'
import { Box,Grid, FormControl, InputLabel, MenuItem, Select, Stack, Paper, Card, Typography } from '@mui/material'
import { container_filter, font_Small } from '../common/commonStyles'



const PivotTable = () => {
    const columns=[
        {
            field: "id",
            headerName: "Id",
            hide: true,
            flex: 1,
          },
          {
            field: "poNum",
            headerName: "PO Number",
            flex: 1,
          },
          {
            field: "issued",
            headerName: "Issued Quantity",
            flex: 1,
            align:'right'
          },
          {
            field: "consumed",
            headerName: "Consumed Quantity",
            flex: 1,
            align:'right'
          },
    ]
    const columnsASN=[
        {
            field: "id",
            headerName: "Id",
            hide: true,
            flex: 1,
          },
          {
            field: "asnid",
            headerName: "ASN ID",
            flex: 1,
          },
          {
            field: "issued",
            headerName: "Issued Quantity",
            flex: 1,
            align:'right'
          },
          {
            field: "consumed",
            headerName: "Consumed Quantity",
            flex: 1,
            align:'right'
          },
    ]
    const columnsSubMat=[
      {
          field: "id",
          headerName: "Id",
          hide: true,
          flex: 1,
        },
        {
          field: "subMat",
          headerName: "Sub Material ID",
          flex: 1,
        },
        {
          field: "issued",
          headerName: "Issued Quantity",
          flex: 1,
          align:'right'
        },
        {
          field: "consumed",
          headerName: "Consumed Quantity",
          flex: 1,
          align:'right'
        },
  ]
    const poRows=[{id:1,poNum:45000231,issued:500,consumed:200},{id:2,poNum:45000251,issued:600,consumed:400}]
    const asnRows=[{id:1,asnid:'ASN20230521124532123',issued:100,consumed:100},{id:2,asnid:'ASN20230521124812359',issued:200,consumed:150}]
    const subMatRows=[{id:1,subMat:'00010',issued:174,consumed:40},{id:2,subMat:'00020',issued:890,consumed:300}]

    const [subMat, setSubMat] =useState('');
    const [viewType, setViewType] =useState('');
    const [viewTypePO, setViewTypePO] =useState('');

    const [primaryView, setPrimaryView] =useState('');
const [asn, setasn] = useState('')
const [po, setpo] = useState('')
    const handleChange = (event) => {
      setSubMat(event.target.value);
    };
    const handleChange1 = (event) => {
        setViewType(event.target.value);
        setViewTypePO('')
      };
      const handleChange5 = (event) => {
        setViewTypePO(event.target.value);
        setViewType('')
      };
      const handleChange2 = (event) => {
        setPrimaryView(event.target.value);
      };
      const handleChange3 = (event) => {
        setasn(event.target.value);
      };
      const handleChange4 = (event) => {
        setpo(event.target.value);
      };
  return (
    <>
    <Stack> 
        <Card p={3} >
            <Grid container columnSpacing={2} p={1}>
            <Grid item xs={3}>
                <Typography sx={font_Small}>Primary View Type</Typography>
    <FormControl fullWidth size="small">
 
  <Select
   
    value={primaryView}
    
    onChange={handleChange2}
        placeholder={"Select Sub Material"}
          select
          sx={font_Small}
          size="small"

          displayEmpty={true}
  >
    <MenuItem value={"PO View"}>PO View</MenuItem>
    <MenuItem value={"Sub Material View"}>Sub Material View</MenuItem>
    <MenuItem value={"ASN View"}>ASN View</MenuItem>
  </Select>
</FormControl>
</Grid>
              {primaryView=='Sub Material View' && <><Grid item xs={3}>
                  <Typography sx={font_Small}>Sub Material</Typography>
      <FormControl fullWidth size="small">
   
    <Select
     
      value={subMat}
      
          placeholder={"Select Sub Material"}
            select
            sx={font_Small}
            size="small"
  
            onChange={handleChange}
            displayEmpty={true}
    >
      <MenuItem value={"SM1"}>SM1</MenuItem>
      <MenuItem value={"SM2"}>SM2</MenuItem>
      <MenuItem value={"SM3"}>SM3</MenuItem>
    </Select>
  </FormControl>
  </Grid>
  <Grid item xs={3}>
  <Typography sx={font_Small}>View Type</Typography>
  
  <FormControl fullWidth size="small">
    
    <Select
      value={viewType}
      onChange={handleChange1}
          placeholder={"Select View Type"}
            select
            sx={font_Small}
            size="small"
            value={viewType}
            displayEmpty={true}
    >
      <MenuItem value={"PO"}>PO</MenuItem>
      <MenuItem value={"ASN"}>ASN</MenuItem>
     
    </Select>
  </FormControl>
  </Grid></>
 }  
 {primaryView=='ASN View' && <><Grid item xs={3}>
 <Typography sx={font_Small}>ASN ID</Typography>
      <FormControl fullWidth size="small">
   
    <Select
     
      value={asn}
      
          placeholder={"Select Sub Material"}
            select
            sx={font_Small}
            size="small"
  
            onChange={handleChange3}
            displayEmpty={true}
    >
      <MenuItem value={"ASN1"}>ASN1</MenuItem>
      <MenuItem value={"ASN2"}>ASN2</MenuItem>
      <MenuItem value={"ASN3"}>ASN3</MenuItem>
    </Select>
  </FormControl>
  </Grid><Grid item xs={3}>
    
    </Grid></>}
    {primaryView=='PO View' && <><Grid item xs={3}>
 <Typography sx={font_Small}>PO Number</Typography>
      <FormControl fullWidth size="small">
   
    <Select
     
      value={po}
      
          placeholder={"Select Sub Material"}
            select
            sx={font_Small}
            size="small"
  
            onChange={handleChange4}
            displayEmpty={true}
    >
      <MenuItem value={"PO1"}>PO1</MenuItem>
      <MenuItem value={"PO2"}>PO2</MenuItem>
      <MenuItem value={"PO3"}>PO3</MenuItem>
    </Select>
  </FormControl>
  </Grid><Grid item xs={3}>
  <Typography sx={font_Small}>View Type</Typography>
  
  <FormControl fullWidth size="small">
    
    <Select
      value={viewTypePO}
      onChange={handleChange5}
          placeholder={"Select View Type"}
            select
            sx={font_Small}
            size="small"
            // value={viewTypePO}
            displayEmpty={true}
    >
      <MenuItem value={"SubMat"}>Sub Material</MenuItem>
      <MenuItem value={"ASN"}>ASN</MenuItem>
     
    </Select>
  </FormControl>
    </Grid>
    {viewTypePO=='ASN' && <Grid item xs={3}>
  <Typography sx={font_Small}>ASN ID</Typography>
  
  <FormControl fullWidth size="small">
    
    <Select
      value={asn}
      onChange={handleChange3}
          placeholder={"Select View Type"}
            select
            sx={font_Small}
            size="small"
            // value={viewTypePO}
            displayEmpty={true}
    >
      <MenuItem value={"ASN202017417475"}>ASN202017417475</MenuItem>
      <MenuItem value={"ASN202017477237"}>ASN202017477237</MenuItem>
     
    </Select>
  </FormControl>
    </Grid>
    }</>}
</Grid>
</Card>
    <ReusableTable
    //  url_onRowClick={"/purchaseOrder/ASN/details/"}
    //  status_onRowDoubleClick={true}
     width="100%"
    //  title={`List of Shipment IDs (${poDataRows?.length})`}
     rows={primaryView=='ASN View' ? subMatRows:viewType=='PO' ? poRows : viewTypePO=='SubMat' ? subMatRows : viewTypePO=='ASN' ? subMatRows : asnRows}
     columns={primaryView=='ASN View' ?  columnsSubMat: viewType=='PO' ? columns : viewTypePO=='SubMat' ? columnsSubMat:  viewTypePO=='ASN' ? columnsSubMat: columnsASN}
     hideFooter={false}
     getRowIdValue={"id"}
    //  module={"asn"}
    //  isLoading={isLoading}
     disableSelectionOnClick={true}
    />
    </Stack>

    </>
  )
}

export default PivotTable