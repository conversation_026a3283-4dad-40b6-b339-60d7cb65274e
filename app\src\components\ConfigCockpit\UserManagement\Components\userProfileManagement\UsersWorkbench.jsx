import xlsx from "json-as-xlsx";
import React, { useEffect, useState } from "react";
import ReusableTable from "../../../../common/ReusableTable";
import CheckCircleOutlineOutlinedIcon from "@mui/icons-material/CheckCircleOutlineOutlined";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { ViewDetailsIcon } from "../../../../common/icons";
import { useSelector } from "react-redux";
import { destination_ManageAccount, destination_Po } from "../../../../../destinationVariables";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Chip,
  Grid,
  IconButton,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import moment from "moment";
import {
  button_Marginleft,
  button_Outlined,
  button_Primary,
  container_filter,
  iconButton_SpacingSmall,
  primary_Color,
} from "../../../../common/commonStyles";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { setHistoryPath } from "../../../../../app/utilitySlice";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import FilterField from "../../../../common/ReusableFilterBox/FilterField";
import {
  commonFilterClear,
  commonFilterUpdate,
} from "../../../../../app/commonFilterSlice";
import { getColor_Status, saveExcel } from "../../../../../functions";
import axios from "axios";
import { IosShare, Refresh } from "@mui/icons-material";
import SearchBar from "../../../../common/SearchBar";
import { doAjax } from "../../../../common/fetchService";
import ReusableDialog from "../../../../common/ReusableDialog";
import ReusableSnackBar from "../../../../common/ReusableSnackBar";
import NoDataDialog from "../../../../Common/NoDataDialog";
// import NoDataDialog from "../../../../common/NoDataDialog";

const UsersWorkbench = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [wbData, setwbData] = useState([]);
  const [vendorDetailsSet, setVendorDetailsSet] = useState({});
  let masterData = useSelector((state) => state.masterData);
  const [odataCompanieslist, setodataCompanieslist] = useState([])
  const formcontroller_FilterWB = useSelector(
    (state) => state.commonFilter["Userswb"]
  );
  const [selectedTask, setSelectedTask] = useState(null)
  let userData = useSelector((state) => state.userManagement.userData);
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 8);
  let dispatch = useDispatch();
  let navigate = useNavigate();
  const rejectUser = () => {};
  const approveUser = () => {};
  const moduleFilterData = [
    {
      type: "singleSelect",
      filterName: "purchGrp",
      filterData: masterData?.purchasingGroups,
      filterTitle: "Purchasing Group",
      hideFilter: false
    },
    {
      type: "singleSelect",
      filterName: "company",
      filterData: masterData?.companyCode,
      filterTitle: "Company",
    },
    {
      type: "singleSelect",
      filterName: "supplier",
      filterData: vendorDetailsSet,
      filterTitle: "Supplier",
    },
    {
      type: "multiSelect",
      filterName: "taskStatus",
      filterData: [
        'Approved',
        'Rejected',
        'Requires Approval'
      ],
      filterTitle: "Task Status",
    },
    {
      type: "multiSelect",
      filterName: "type",
      filterData: [
        'Bank Information',
        'Tax Information'
      ],
      filterTitle: "Type",
    },
    {
      type: "text",
      filterName: "assignedTo",
      filterTitle: "Assigned To",
    },
    {
      type: "text",
      filterName: "createdBy",
      filterTitle: "Created By",
    },
    {
      type: "dateRange",
      filterName: "date",
      filterTitle: "Created Between",
    },
  ];

  //<-- functions and variables for reusable dialogbox -->
  const [reusableDialog_Ref, setreusableDialog_Ref] = useState('')

  const [warning_Notification, setwarning_Notification] = useState({
    currentNotification: "",
    success: true,
    open: false,
    title: "",
    severity: "",
  });
  const [Success_Notification, setSuccess_Notification] = useState({
    currentNotification: "",
    success: true,
    open: false,
    title: "",
    severity: "",
  }); 
  const functions_ReusableDialogBox = {
    MessageDialogCancel: () => {
      setwarning_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
    },
    MessageDialogClickOpen: () => {
      setwarning_Notification((prev) => ({ ...prev, open: true }));
      // setOpenMessageDialog(true);
    },

    MessageDialogClose: () => {
      setwarning_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));

      setSuccess_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
    },
    messageDialogCloseAndRedirect: () => {
      setwarning_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
      setSuccess_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
      // navigate('/manageAccount')
    },
    getHandleOkFunction:()=>{
      switch(reusableDialog_Ref){
        case 'CONFIRMSUBMIT':
          setwarning_Notification((prev) => ({
            open: false,
            currentNotification: "",
            success: "",
            title: "",
            severity: "",
          }));
          handleApproveReject('Approve')
          break;
        case 'ERROR':
          functions_ReusableDialogBox.MessageDialogClose()
          break;
        case 'CONFIRMREJECT':
          setwarning_Notification((prev) => ({
            open: false,
            currentNotification: "",
            success: "",
            title: "",
            severity: "",
          }));
          handleApproveReject('Reject')
        default :
        functions_ReusableDialogBox.MessageDialogClose()
      }
    },
    viewOkButton:()=>{
      // console.log(reusableDialog_Ref,'ref')
      switch(reusableDialog_Ref){
        case 'CONFIRMSUBMIT':
          return true
          break;
        case 'ERROR':
          return false
          break;
        case 'CONFIRMREJECT':
          return true
        default :
       return false
      }
    },
    viewCancelButton: ()=>{
      switch(reusableDialog_Ref){
        case 'CONFIRMSUBMIT':
          return true
          break;
        case 'ERROR':
          return false
          break;
          case 'CONFIRMREJECT':
         return true
        default :
       return false
      }
    },
    getOkButtonText:()=>{
      switch(reusableDialog_Ref){
        case 'CONFIRMSUBMIT':
          return 'OK'
          break;
        case 'ERROR':
          return 'OK'
        case 'CONFIRMREJECT':
           return 'OK'
        default :
       return ''
      }
    },
    getHandleCancleFunction:()=>{
      switch(reusableDialog_Ref){
        case 'CONFIRMSUBMIT':
          return functions_ReusableDialogBox.MessageDialogClose()
        case 'ERROR':
          return ()=>{}
        case 'CONFIRMREJECT':
           return functions_ReusableDialogBox.MessageDialogClose()
        default :
       return ()=>{}
      }
    }
  };


  //<-- Function for approve and reject -->
const handleApproveReject = async(status)=>{
    let hSuccess = (res)=>{
      if(res){
        setSuccess_Notification((prev) => ({
          open: true,
          currentNotification: status === "Approve" ? `Approved Successfully`: `Rejected Successfully`,
          success: "",
          title: "Success",
          severity: "",
        }));
      }
    }
    let hError = (err)=>{

    }
    doAjax(`/${destination_ManageAccount}/userManagement/postUserInformationToSAP?taskId=${selectedTask}&actionStatus=${status}`,'get',hSuccess)
  }
  const confirmApproveReject = (status, taskId)=>{
    // debugger
    setSelectedTask(taskId)
    if(status==='Approve'){
      setreusableDialog_Ref('CONFIRMSUBMIT')

      setwarning_Notification((prev) => ({
        ...prev,
        currentNotification: `Proceed with approval of ${taskId}`,
        success: false,
        open: true,
        title: "Account confirmation",
        severity: "success",
      }));
    }
    if(status === 'Reject'){
      setreusableDialog_Ref('CONFIRMREJECT')

      setwarning_Notification((prev) => ({
        ...prev,
        currentNotification: `Proceed with the rejection of ${taskId}`,
        success: false,
        open: true,
        title: "Account confirmation",
        severity: "success",
      }));
    }
  }

  const odataFetchCompaniesList = async()=>{
    let hSuccess = (data) => {
      setodataCompanieslist(data)
      // console.log(data,'compdata')
    }
     doAjax(`/${destination_Po}/Odata/populateCompanyCodeDetails`,'get',hSuccess)

  }
 const [singleTaskId, setSingleTaskId] = useState([])
  let functions_FilterWB = {
    submit: (receivedPaylaod = null) => {
      setIsLoading(true);

      let payload = {...formcontroller_FilterWB, taskName: formcontroller_FilterWB.taskName.toString(), taskStatus: formcontroller_FilterWB.taskStatus.toString(), type: formcontroller_FilterWB.type.toString()  }
      payload.toDate = `${moment(formcontroller_FilterWB.date[1], "YYYY-MM-DD")
          .format()
          .split("T")[0]
      }`
      payload.fromDate = `${
          moment(formcontroller_FilterWB.date[0], "YYYY-MM-DD")
            .format()
            .split("T")[0]
        }`
      
        payload.supplierCode = payload.supplier
        payload.companyCode = payload.company
      delete payload.date
      delete payload.company
      delete payload.supplier
      delete payload.taskId


      // payload.toDate = `${
      //   moment(formcontroller_FilterWB.date[1], "YYYY-MM-DD")
      //     .format()
      //     .split("T")[0]
      // }T00:00:00`;
      // payload.fromDate = `${
      //   moment(formcontroller_FilterWB.date[0], "YYYY-MM-DD")
      //     .format()
      //     .split("T")[0]
      // }T00:00:00`;
      // delete payload.createBetween
      // console.log(payload,'payload')
      let hSuccess = (res)=>{
        setwbData(res.userWorkbenchDtoList)
        setIsLoading(false)
      }
      let hError = ()=>{
        setwbData([])
        setIsLoading(false)

      }
      doAjax(`/${destination_ManageAccount}/userManagement/filterTasks`,'post',hSuccess,hError,payload)
      
    },
    handleClear: () => {
      dispatch(commonFilterClear({ module: "Userswb" }));
    },
    singleSearch: () => {
      setIsLoading(true)
      let id = formcontroller_FilterWB.taskId
      let payload = { 
      taskId: id }
      let hSuccess = (res)=>{
        setwbData(res.data.userWorkbenchDtoList)
        setIsLoading(false)
      }
      let hError = (err) => {
        setIsLoading(false)

        console.log(err)
      }
      doAjax(`/${destination_ManageAccount}/userManagement/filterTasks`,'post',hSuccess,hError, payload)


    },
 
  };

  // Fetch Vendor Details from Odata
  const fetchVendorDetails = () => {
    if (userData?.supplierId) {
      setVendorDetailsSet({
        [userData?.supplierId]:
          userData?.supplierName,
      });
    } else {
      const formData = new FormData();

      if (formcontroller_FilterWB?.companyCode !== "") {
        formData.append("compCode", formcontroller_FilterWB?.companyCode);
        let hSuccess = (data) => {
          setVendorDetailsSet(data);
        }
        doAjax(`/${destination_Po}/Odata/getVendorId`, "postformdata",hSuccess)
      }
    }
  };
  const fetchUserWBData = async () => {
    let hSuccess = (data)=>{
      setwbData(data.userWorkbenchDtoList);
      setIsLoading(false);
    }
   doAjax(
      `/${destination_ManageAccount}/userManagement/getAllTasks`,'get', hSuccess
    );
    
  };

  useEffect(() => {
    fetchVendorDetails();
  }, [formcontroller_FilterWB?.companyCode]);
  useEffect(() => {
    // fetchUserWBData();
    functions_FilterWB.submit()
    odataFetchCompaniesList()
  }, []);

  // <-- Functions and varibles for Data list || Datagrid -->
  const appSettings = useSelector((state) => state.appSettings["Format"])
  let colorConfList_TaskStatus = {
    approved: "#CDEFD6",
    rejected: "#FFCDD2",
    pendingaction: "#FAFFC0",
    pendingapproval:'#FAFFC0'
  };
  const columns = [
    {
      field: "taskId",
      headerName: "Change ID",
      hide: false,
      width: "200",
    },

    // {
    //   field: "taskName",
    //   headerName: "Task Name",
    //   editable: false,
    //   width: "200",
    // },
    {
      field: "supplierName",
      headerName: "Supplier",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        return (
          <Stack>
            <Typography variant="body2">{params.row.supplierName}</Typography>
            <Typography variant="body2" sx={{ color: "#7E7E7E" }}>
              {params.row.supplierCode}
            </Typography>
          </Stack>
        );
      },
    },
    {
      field: "company",
      headerName: "Company",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        return (
          <Stack>
            <Typography variant="body2">{odataCompanieslist?.[params.row.companyName?.split('-')[0]]}</Typography>
            <Typography variant="body2" sx={{ color: "#7E7E7E" }}>
              {params.row.companyName?.split('-')[0]}
            </Typography>
          </Stack>
        );
      },
    },

    {
      field: "createdOn",
      headerName: "Created Date",
      editable: false,
      flex: 1,
      type: "date",
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return moment(params.row.createdOn).format(appSettings.date);
      },
    },
    {
      field: "createdBy",
      headerName: "Created By",
      sortable: false,
      editable: false,
      hide: true,
      flex: 1,
      align: "left",
      headerAlign: "left",
    },
    {
      field: "assignedTo",
      headerName: "Assigned To",
      sortable: false,
      editable: false,
      type: "number",
      flex: 1,
      align: "left",
      headerAlign: "left",
    },
    {
      field: "type",
      headerName: "Type",
      sortable: false,
      editable: false,
      type: "number",
      flex: 1,
      align: "left",
      headerAlign: "left",
    },
    {
      field: "taskStatus",
      headerName: "Status",
      sortable: false,
      editable: false,
      type: "number",
      flex: 1,
      align: "left",
      headerAlign: "left",
      renderCell: (cellValues) => {
        return (
          <Box
            sx={{
              minWidth: "inherit",
            }}
          >
            <Chip
              sx={{
                justifyContent: "flex-start",
                borderRadius: "4px",
                minWidth: "100%",
                color: "#000",
                fontSize: "12px",
                backgroundColor:
                  cellValues?.row?.taskStatus &&
                  getColor_Status(
                    cellValues.row.taskStatus,
                    colorConfList_TaskStatus
                  ),
              }}
              label={cellValues?.row?.taskStatus}
            />
          </Box>
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <>
            <Tooltip title="View">
              <IconButton
                sx={iconButton_SpacingSmall}
                onClick={
                  () => {
                    navigate(
                      `/userManagement/confirmUser/${params.row.taskId}`
                    );
                  }
                  //   handleOnClick(
                  //     cellValues.row.confirmid,
                  //     cellValues.row.poStatus
                  //   )
                }
              >
                {ViewDetailsIcon}
              </IconButton>
            </Tooltip>
            {/* {params.row?.taskStatus?.toLowerCase() === "pending action"  && (
              <>
                <Tooltip title="Approve">
                  <IconButton onClick={() => confirmApproveReject('Approve',params.row.taskId)}>
                    <CheckCircleOutlineOutlinedIcon className="wbActionIcon" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Reject">
                  <IconButton onClick={() => confirmApproveReject("Reject",params.row.taskId)}>
                    <CancelOutlinedIcon className="wbActionIcon" />
                  </IconButton>
                </Tooltip>
              </>
            )} */}
          </>
        );
      },
    },
  ];
  let refresh=()=>{
    functions_FilterWB.submit()
  }
  // <-- Functions AND variables for exporting as excel -->


const functions_ExportAsExcel ={

  
   convertJsonToExcel :  () => {
    let excelColumns = []
columns.forEach((item)=>{
  if(item.headerName.toLowerCase() !== 'action' && !item.hide){
    excelColumns.push({ header: item.headerName, key: item.field })
  }
})
    saveExcel({
      fileName: `USERSWBDatasheet-${moment(presentDate).format("DD-MMM-YYYY")}`,
      columns: excelColumns,
      rows: wbData,
    })
  },
  button:()=>{
    return (
      <Button sx={{textTransform:'capitalize', position:'absolute', right:0, top:0}} onClick={()=> functions_ExportAsExcel.convertJsonToExcel()}>Download</Button>
    )
  }
}


  return (
    <>
      <NoDataDialog DataRows={wbData}/>
       <ReusableDialog
        dialogState={warning_Notification.open}
        openReusableDialog={functions_ReusableDialogBox.MessageDialogClickOpen}
        closeReusableDialog={functions_ReusableDialogBox.MessageDialogCancel}
        dialogTitle={warning_Notification.title}
        dialogMessage={warning_Notification.currentNotification}
        handleOk={functions_ReusableDialogBox.getHandleOkFunction }
        dialogOkText={functions_ReusableDialogBox.getOkButtonText()}
        showOkButton={functions_ReusableDialogBox.viewOkButton() }
        showCancelButton= {functions_ReusableDialogBox.viewCancelButton()}
        dialogSeverity={warning_Notification.severity}
        handleDialogReject={functions_ReusableDialogBox.getHandleCancleFunction}
      />
      <ReusableSnackBar
        openSnackBar={Success_Notification.open}
        alertMsg={Success_Notification.currentNotification}
        handleSnackBarClose={
          functions_ReusableDialogBox.messageDialogCloseAndRedirect
        }
      />
     <Grid
                container
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                spacing={0}
                sx={{
                  marginBottom:'.5rem'
                }}
              >
                <Grid item>
                <Typography variant="body2" color="#777">
                  This view displays all the user profile confirmation tasks 
                </Typography>
                </Grid>
                <Grid item sx={{
                  display:'flex',
                  flexDirection:'row',
                    marginBottom:'.5rem'
            
                }}>

                <Tooltip title="Search">
                  <SearchBar
                    title="Search for multiple Tasks separated by comma"
                    module='Userswb'
                    keyName='taskId'
                    clearSearchBar={()=> setSingleTaskId('')}
                    message={"Search Task"}
                    handleSearchAction={functions_FilterWB.singleSearch}
                  />
                </Tooltip>
                <Tooltip title="Reload">
                  <IconButton sx={iconButton_SpacingSmall}>
                    <Refresh
                      onClick={refresh} 
                    
                    />
                  </IconButton>
                </Tooltip>
                <Tooltip  title="Export Table">
                  <IconButton sx={iconButton_SpacingSmall}>
                    <IosShare onClick={functions_ExportAsExcel.convertJsonToExcel} />
                  </IconButton>
                </Tooltip>
                </Grid>

                
              </Grid>
      <Grid container sx={container_filter}>
        <Grid item md={12}>
      
          <Accordion className="filter-accordian">
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls="panel1a-content"
              id="panel1a-header"
              sx={{
                minHeight: "2rem !important",
                margin: "0px !important",
              }}
            >
              <Typography
                sx={{
                  fontWeight: "700",
                  margin: "0px !important",
                }}
              >
                Filter Change Tracker
              </Typography>
            </AccordionSummary>
            <AccordionDetails sx={{ padding: "0rem 1rem 0.5rem" }}>
              <Grid
                container
                rowSpacing={1}
                sx={{ marginBottom: "0.5rem" }}
                spacing={2}
                alignItems="center"
              >
                {moduleFilterData?.map((filter) => filter?.hideFilter ? (<></>) : (
                  <Grid item md={3} key={filter.filterTitle}>
                    <FilterField
                      type={filter.type}
                      filterName={filter.filterName}
                      filterData={filter.filterData}
                      moduleName={"Userswb"}
                      onChangeFilter={filter.onChangeFilter}
                      filterTitle={filter.filterTitle}
                    />
                  </Grid>
                ))}
              </Grid>
              <Grid
                container
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                }}
              >
                <Grid
                  item
                  style={{
                    display: "flex",
                    justifyContent: "space-around",
                  }}
                >
                  <Button
                    variant="outlined"
                    sx={button_Outlined}
                    onClick={functions_FilterWB.handleClear}
                  >
                    Clear
                  </Button>
                  {/* <ReusablePreset
                        anchorEl={anchorEl_Preset}
                        setAnchorEl={setAnchorEl}
                        open={open}
                        handleClose={handleClose_Preset}
                        presets={presets}
                        setPresets={setPresets}
                        presetName={presetName}
                        setPresetName={setPresetName}
                        deletePreset={functions_PresetFilter.deletePreset}
                        saveFilterPreset={
                          functions_PresetFilter.saveFilterPreset
                        }
                        setPresetFilter={functions_PresetFilter.setPresetFilter}
                        setFilterDefault={
                          functions_PresetFilter.setFilterDefault
                        }
                        handleSearch={functions_FilterWB.submit}
                      /> */}
                  <Button
                    variant="contained"
                    sx={{ ...button_Primary, ...button_Marginleft }}
                    onClick={functions_FilterWB.submit}
                  >
                    Search
                  </Button>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        </Grid>
      </Grid>
      <ReusableTable
        isLoading={isLoading}
        module={"UserManagement workbench"}
        width="100%"
        title={"List of Changes (" + wbData.length + ")"}
        rows={wbData}
        columns={columns}
        getRowIdValue={"taskId"}
        hideFooter={false}
        checkboxSelection={false}
        disableSelectionOnClick={true}
        url_onRowClick={"/userManagement/confirmUser/"}
        stopPropagation_Column={"action"}
        status_onRowDoubleClick={true}
      />
    </>
  );
};

export default UsersWorkbench;
