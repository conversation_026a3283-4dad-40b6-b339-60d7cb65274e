import { Checkbox, Grid, Typography } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import {
  setMultipleMaterialPayloadKey,
  setRequiredFields,
  updateMaterialData,
} from "../../../app/payloadslice";
import { useEffect, useState } from "react";
import { useChangeLogUpdate } from "@hooks/useChangeLogUpdate";
import { useLocation } from "react-router-dom";
import { colors } from "@constant/colors";
import { CHANGE_LOG_STATUSES } from "@constant/enum";
import useLang from "@hooks/useLang";

/* Props Definition */
//moduleFilter - Redux filter state for the module - useSelector((state) => state.commonFilter["module"])
//filterData - Data to load in a select filter - {key1:value1, key2:value2, key3:value3, ...}
//onChangeFilter -  Function to call on Filter update
//filterTitle - Title of Field
//filterName - Name property of filter

export default function RadioType(props) {
  const dispatch = useDispatch();
  const valueFromPayload = useSelector((state) => state.payload);
  const { updateChangeLog } = useChangeLogUpdate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get("RequestId");
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const SAPview = location.pathname.includes("DisplayMaterialSAPView");
  const { t } = useLang();

  useEffect(() => {
    if (props.details.visibility === "Required") {
      dispatch(setRequiredFields(props.keyName));
    }
  });

  const valueToCheck = valueFromPayload?.[props?.materialID]?.payloadData?.[props?.viewName]?.[props?.plantData]?.[props?.keyName] ?? props?.details?.value ?? false;
  const initialFieldValue = valueToCheck === "X" || valueToCheck === true || valueToCheck === "TRUE" ? true : false;
  const [localValue, setLocalValue] = useState(initialFieldValue);
  useEffect(() => {
    setLocalValue(initialFieldValue);
    if(initialFieldValue) {
      dispatch(
        updateMaterialData({
          materialID: props?.materialID || "",
          keyName: props?.keyName || "",
          data: initialFieldValue,
          viewID: props?.viewName,
          itemID: props?.plantData,
        })
      );
    }
  }, [initialFieldValue]);

  const handleChange = (e) => {
    const updatedValue = e.target.checked;
    setLocalValue(updatedValue);
    // dispatch(
    //   setMultipleMaterialPayloadKey({
    //     materialID: props?.materialID,
    //     keyName: props.keyName,
    //     data: e.target.checked,
    //     viewID: props?.viewName,
    //     itemID: props?.plantData,
    //   })
    // );

    dispatch(
      updateMaterialData({
        materialID: props?.materialID || "",
        keyName: props.keyName || "",
        data: updatedValue,
        viewID: props?.viewName,
        itemID: props?.plantData,
      })
    );

    {
      requestId && !CHANGE_LOG_STATUSES.includes(initialPayload?.RequestStatus) &&
        updateChangeLog({
          materialID: props?.selectedMaterialNumber,
          viewName: props?.viewName,
          plantData: props?.plantData,
          fieldName: props?.details?.fieldName,
          jsonName: props?.details?.jsonName,
          currentValue: updatedValue,
          requestId: initialPayload?.RequestId,
          childRequestId:requestId
        });
    }
  };

  return (
    <Grid item md={2}>
      {SAPview ? (
        <div
          style={{
            padding: "16px",
            backgroundColor: colors.primary.white,
            borderRadius: "8px",
            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
            margin: "16px 0",
            transition: "all 0.3s ease",
          }}
        >
          <Typography
            variant="body1"
            style={{
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              maxWidth: "100%",
              fontWeight: 600,
              fontSize: "12px",
              marginBottom: "4px",
              display: "flex",
              alignItems: "center",
            }}
            title={props?.details?.fieldName}
          >
            {t(props?.details?.fieldName) || "Field Name"}
            {(props?.details?.visibility === "Required" ||
              props?.details?.visibility === "MANDATORY") && (
              <span style={{ color: colors.error.darkRed, marginLeft: "2px" }}>*</span>
            )}
          </Typography>

          <div
            style={{
              fontSize: "0.8rem",
              color: colors.black.dark,
              marginTop: "4px",
            }}
          >
            <span
              style={{
                fontWeight: 500,
                color: colors.secondary.grey,
                letterSpacing: "0.5px", 
                wordSpacing: "1px",
              }}
            >
              {localValue ? "Yes" : "No"}
            </span>
          </div>
        </div>
      ) : (
        <>
          <Typography
            variant="body2"
            color={colors.secondary.grey}
            sx={{
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              maxWidth: "100%",
            }}
            title={props.details.fieldName}
          >
            {t(props.details.fieldName)}
            {props.details.visibility === "Required" ||
            props.details.visibility === "0" ? (
              <span style={{ color: colors.error.darkRed }}>*</span>
            ) : (
              ""
            )}
          </Typography>
          <Checkbox
            sx={{
              padding: 0,
              "&.Mui-disabled": {
                color: colors.hover.light,
              },
              "&.Mui-disabled.Mui-checked": {
                color: colors.hover.light,
              },
            }}
            disabled={props?.disabled}
            checked={localValue}
            onChange={handleChange}
          />
        </>
      )}
    </Grid>
  );
}
