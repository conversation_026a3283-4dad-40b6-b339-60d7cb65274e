import React, { useEffect, useState } from "react";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import {
  Autocomplete,
  Box,
  Card,
  Chip,
  FormControl,
  Grid,
  MenuItem,
  TextField,
} from "@mui/material";
import { useDispatch } from "react-redux";
import DateRange from "../Common/DateRangePicker";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { doAjax } from "../Common/fetchService";
import { destination_MaterialMgmt } from "../../destinationVariables";
import { useSelector } from "react-redux";
import { font_Small } from "../Common/commonStyles";
import {
  commonFilterClear,
  commonFilterUpdate,
} from "../../app/commonFilterSlice";
import { descriptionHeader } from "../../app/editPayloadSlice";
import { Loader } from "rsuite";

const DuplicateDesc = ({ open, onClose }) => {
  const dispatch = useDispatch();
  const ddSearchForm = useSelector(
    (state) => state.commonFilter["DuplicateDesc"]
  );
  const headerDesc = useSelector(
    (state) => state.commonFilter["descriptionHeader"]
  );
  const nmSearchForm = useSelector(
    (state) => state.commonFilter["NewMaterial"]
  );
  console.log("headerDesc",headerDesc);
  // Use the 'open' prop to control the dialog visibility
  const [isListOpen, setIsListOpen] = useState(false);
  const [matGroup, setMatGroup] = useState([]);
  const [baseUnit, setBaseUnit] = useState([]);
  const [packagingMaterials, setPackagingMaterials] = useState([]);
  const [duplicateCheck, setDuplicateCheck] = useState([]);
  const [isMatch, setIsMatch] = useState(false);
  const [responseList, setResponseList] = useState([]);
  const [isProceedEnabled, setIsProceedEnabled] = useState(false);
  const [checkButtonClicked, setCheckButtonClicked] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isAnyFieldEmpty, setIsAnyFieldEmpty] = useState(false);
  const [isDirectMatch, setIsDirectMatch] = useState(false);

  const handleDialogClose = () => {
    // dispatch(commonFilterClear({ module: "DulpicateDesc" }));
    onClose(); // Close the dialog by calling the 'onClose' prop
  };
  const handlePrefix = (value) => {
    var tempPrefix = value;

    let tempFilterData = {
      ...ddSearchForm,
      prefix: tempPrefix,
    };
    dispatch(
      commonFilterUpdate({
        module: "DuplicateDesc",
        filterData: tempFilterData,
      })
    );
  };
  const handleGroup = (e, value) => {
    if (true) {
      var tempGroup = value;

      let tempFilterData = {
        ...ddSearchForm,
        group: tempGroup,
      };
      dispatch(
        commonFilterUpdate({
          module: "DuplicateDesc",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleBaseUnit = (e, value) => {
    if (true) {
      var tempBaseUnit = value;

      let tempFilterData = {
        ...ddSearchForm,
        baseUnit: tempBaseUnit,
      };
      dispatch(
        commonFilterUpdate({
          module: "DuplicateDesc",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handlePackagingMaterials = (e, value) => {
    if (true) {
      var tempPackagingMaterials = value;

      let tempFilterData = {
        ...ddSearchForm,
        packagingMaterials: tempPackagingMaterials,
      };
      dispatch(
        commonFilterUpdate({
          module: "DuplicateDesc",
          filterData: tempFilterData,
        })
      );
    }
  };
  const getMaterialGroup = () => {
    const hSuccess = (data) => {
      setMatGroup(data.body);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getMatlGroup`,
      "get",
      hSuccess,
      hError
    );
  };
  const getBaseUnit = () => {
    const hSuccess = (data) => {
      setBaseUnit(data.body);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getBaseUom`,
      "get",
      hSuccess,
      hError
    );
  };
  const getMaterialGroupForPckMaterial = () => {
    const hSuccess = (data) => {
      setPackagingMaterials(data.body);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getMatGrpPack`,
      "get",
      hSuccess,
      hError
    );
  };
  const handleText = (e) => {
    if (e.target.value !== null) {
      var tempText = e.target.value.toUpperCase();

      let tempFilterData = {
        ...ddSearchForm,
        text: tempText,
      };
      dispatch(
        commonFilterUpdate({
          module: "DuplicateDesc",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleDialogProceed = () => {
    const concatenatedValues = [
      ddSearchForm?.prefix,
      ddSearchForm?.group?.code,
      ddSearchForm?.baseUnit?.code,
      ddSearchForm?.packagingMaterials?.code,
      ddSearchForm?.text,
    ].join("-");

    let tempFilterData = {
      ...nmSearchForm,
      description: concatenatedValues,
    };
    dispatch(
      commonFilterUpdate({
        module: "NewMaterial",
        filterData: tempFilterData,
      })
    );
    // Close the dialog
    onClose();
  };

 
  const handleDuplicateCheck = () => {
    if( ddSearchForm?.prefix==="" ||
      ddSearchForm?.group?.code==="" ||
      ddSearchForm?.baseUnit?.code ==="" ||
      ddSearchForm?.packagingMaterials?.code ==="" ||
      ddSearchForm?.text===""){
        setIsAnyFieldEmpty(true)
      }
      else{
        setIsAnyFieldEmpty(false)
      }
      const concatenatedValuesProbables = [
        // ddSearchForm?.prefix,
        ddSearchForm?.group?.code,
        ddSearchForm?.baseUnit?.code,
        ddSearchForm?.packagingMaterials?.code,
        // ddSearchForm?.text,
      ].join("-");
    const concatenatedValues = [
      ddSearchForm?.prefix,
      ddSearchForm?.group?.code,
      ddSearchForm?.baseUnit?.code,
      ddSearchForm?.packagingMaterials?.code,
      ddSearchForm?.text.toLowerCase(),
    ].join("-");
    console.log("concatenatedValues", concatenatedValues);
    setIsLoading(true);
    setCheckButtonClicked(true);
    const hSuccess = (data) => {
      setDuplicateCheck(data.body);
      setResponseList(data.body);
      console.log("concatenatedValuesProbables",concatenatedValuesProbables)
      // Check if the response includes the concatenated value
      const isMatch = data.body.some((item) => {
        const itemParts = item.split("-");
        const itemText = itemParts.pop().toLowerCase(); // Get the text part and convert to lowercase
        const restOfItem = itemParts.join("-");
        return restOfItem === concatenatedValues.split("-").slice(0, -1).join("-") &&
               itemText.includes(ddSearchForm?.text.toLowerCase());
      });   
      const isDirectMatch = data.body.some((item) => {
        const itemParts = item.split("-");
        const itemText = itemParts.pop().toLowerCase(); // Get the text part and convert to lowercase
        const restOfItem = itemParts.join("-");
        return restOfItem === concatenatedValues.split("-").slice(0, -1).join("-") &&
               itemText === ddSearchForm?.text.toLowerCase();
      });   
     if(setIsListOpen)
      setIsProceedEnabled(true);
else{
  setIsProceedEnabled(false)
}
      // Toggle the isListOpen state based on whether there is a match
      setIsListOpen(isMatch);
      setIsDirectMatch(isDirectMatch);
      setIsLoading(false);
      };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_MaterialMgmt}/alter/fetchMatDescDupliChk?descriptionToCheck=${concatenatedValues}`,
      "get",
      hSuccess,
      hError
    );
  };
  const itemMatchesInput = (item) => {
    const input = [
      ddSearchForm?.prefix,
      ddSearchForm?.group?.code,
      ddSearchForm?.baseUnit?.code,
      ddSearchForm?.packagingMaterials?.code,
      ddSearchForm?.text,
    ].join("-");
    return input === `${item}`;
  };

  const isProbableMatch = (item) => {
    const itemParts = item.split("-");
    const itemText = itemParts.pop().toLowerCase(); // Get the text part and convert to lowercase
    return itemText === ddSearchForm?.text.toLowerCase();
  };

  useEffect(() => {
    getMaterialGroup();
    getBaseUnit();
    getMaterialGroupForPckMaterial();
  }, []);
  return (
    <>
      <Dialog
        open={open}
        onClose={handleDialogClose}
        sx={{
          "&::webkit-scrollbar": {
            width: "1px",
          },
        }}
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF",
            // borderBottom: "1px solid grey",
            display: "flex",
          }}
        >
          <Typography variant="h6">Inputs</Typography>
          <IconButton
            sx={{ width: "max-content" }}
            onClick={handleDialogClose}
            children={<CloseIcon />}
          ></IconButton>
        </DialogTitle>
        <DialogContent sx={{ padding: ".5rem 1rem" }} >
          <Grid container spacing={1}>
            <Grid
              item
              md={6}
              sx={{
                width: "100%",
                marginTop: ".5rem",
              }}
            >
              <Typography sx={font_Small}>
                Prefix
                <span style={{ color: "red" }}>*</span>
              </Typography>
              <FormControl fullWidth>
                <Autocomplete
                  options={["INC"]}
                  sx={{ height: "42px" }}
                  required={true}
                  size="small"
                  value={ddSearchForm?.prefix}
                  onChange={(e, value) => handlePrefix(value)}
                  getOptionLabel={(option) => option}
                  renderOption={(props, option) => (
                    <li {...props}>
                      <Typography style={{ fontSize: 12 }}>{option}</Typography>
                    </li>
                  )}
                  renderInput={(params) => (
                    <TextField
                      sx={{ fontSize: "12px !important" }}
                      {...params}
                      variant="outlined"
                      placeholder={"Select Prefix"}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item md={6} sx={{ width: "100%", marginTop: ".5rem" }}>
              <Typography sx={font_Small}>
                Material Group
                <span style={{ color: "red" }}>*</span>
              </Typography>
              <FormControl fullWidth size="small">
                <Autocomplete
                  // sx={{ height: "31px" }}
                  fullWidth
                  size="small"
                  value={ddSearchForm?.group}
                  onChange={handleGroup}
                  // onChange={(e) => handleMatTypeChange(e)}
                  options={matGroup ?? []}
                  getOptionLabel={(option) =>
                    option?.code === "" || option?.desc === ""
                      ? ""
                      : `${option?.code} - ${option?.desc}`
                  }
                  renderOption={(props, option) => (
                    <li {...props}>
                      <Typography style={{ fontSize: 12 }}>
                        {`${option?.code} - ${option?.desc}`}
                      </Typography>
                    </li>
                  )}
                  renderInput={(params) => (
                    <TextField
                      sx={{ fontSize: "12px !important" }}
                      {...params}
                      variant="outlined"
                      placeholder={"Select Material Group"}
                    />
                  )}
                />
                {/* <Select
                            placeholder={"Select Material Group"}
                            select
                            sx={{font_Small, height:"31px", fontSize:"12px", width:"100%"}}
                            size="small"
                            value={matGroup}
                            name={matGroup}
                            onChange={(e) => handleMatGroupChange(e)}
                            displayEmpty={true}
                          // disabled={Object.keys(props?.filterData ?? {})?.length < 2}
                          >
                            <MenuItem sx={font_Small} value={""}>
                              <div style={{ color: "#C1C1C1" }}>Select Material Group { } </div>
                            </MenuItem>
                            {Object.keys(matGroupData).map((key) => (
                              <MenuItem value={key} key={key}>
                                {key + " - " + matGroupData[key]}
                              </MenuItem>
                            ))}
                          </Select> */}
              </FormControl>
            </Grid>
            <Grid item md={6} sx={{ width: "100%", marginTop: ".5rem" }}>
              <Typography sx={font_Small}>
                Base Unit
                <span style={{ color: "red" }}>*</span>
              </Typography>
              <FormControl fullWidth size="small">
                <Autocomplete
                  // sx={{ height: "31px" }}
                  fullWidth
                  size="small"
                  value={ddSearchForm?.baseUnit}
                  onChange={handleBaseUnit}
                  // onChange={(e) => handleMatTypeChange(e)}
                  options={baseUnit ?? []}
                  getOptionLabel={(option) =>
                    option?.code === "" || option?.desc === ""
                      ? ""
                      : `${option?.code} - ${option?.desc}`
                  }
                  renderOption={(props, option) => (
                    <li {...props}>
                      <Typography style={{ fontSize: 12 }}>
                        {`${option?.code} - ${option?.desc}`}
                      </Typography>
                    </li>
                  )}
                  renderInput={(params) => (
                    <TextField
                      sx={{ fontSize: "12px !important" }}
                      {...params}
                      variant="outlined"
                      placeholder={"Select Base Unit"}
                    />
                  )}
                />
                {/* <Select
                            placeholder={"Select Material Group"}
                            select
                            sx={{font_Small, height:"31px", fontSize:"12px", width:"100%"}}
                            size="small"
                            value={matGroup}
                            name={matGroup}
                            onChange={(e) => handleMatGroupChange(e)}
                            displayEmpty={true}
                          // disabled={Object.keys(props?.filterData ?? {})?.length < 2}
                          >
                            <MenuItem sx={font_Small} value={""}>
                              <div style={{ color: "#C1C1C1" }}>Select Material Group { } </div>
                            </MenuItem>
                            {Object.keys(matGroupData).map((key) => (
                              <MenuItem value={key} key={key}>
                                {key + " - " + matGroupData[key]}
                              </MenuItem>
                            ))}
                          </Select> */}
              </FormControl>
            </Grid>
            <Grid item md={6} sx={{ width: "100%", marginTop: ".5rem" }}>
              <Typography sx={font_Small}>
                Material Group:Packaging Materials
                <span style={{ color: "red" }}>*</span>
              </Typography>
              <FormControl fullWidth size="small">
                <Autocomplete
                  // sx={{ height: "31px" }}
                  fullWidth
                  size="small"
                  value={ddSearchForm?.packagingMaterials}
                  onChange={handlePackagingMaterials}
                  // onChange={(e) => handleMatTypeChange(e)}
                  options={packagingMaterials ?? []}
                  getOptionLabel={(option) =>
                    option?.code === "" || option?.desc === ""
                      ? ""
                      : `${option?.code} - ${option?.desc}`
                  }
                  renderOption={(props, option) => (
                    <li {...props}>
                      <Typography style={{ fontSize: 12 }}>
                        {`${option?.code} - ${option?.desc}`}
                      </Typography>
                    </li>
                  )}
                  renderInput={(params) => (
                    <TextField
                      sx={{ fontSize: "12px !important" }}
                      {...params}
                      variant="outlined"
                      placeholder={"Select Packaging Materials"}
                    />
                  )}
                />
                {/* <Select
                            placeholder={"Select Material Group"}
                            select
                            sx={{font_Small, height:"31px", fontSize:"12px", width:"100%"}}
                            size="small"
                            value={matGroup}
                            name={matGroup}
                            onChange={(e) => handleMatGroupChange(e)}
                            displayEmpty={true}
                          // disabled={Object.keys(props?.filterData ?? {})?.length < 2}
                          >
                            <MenuItem sx={font_Small} value={""}>
                              <div style={{ color: "#C1C1C1" }}>Select Material Group { } </div>
                            </MenuItem>
                            {Object.keys(matGroupData).map((key) => (
                              <MenuItem value={key} key={key}>
                                {key + " - " + matGroupData[key]}
                              </MenuItem>
                            ))}
                          </Select> */}
              </FormControl>
            </Grid>
            <Grid item md={6}>
              <Typography sx={font_Small}>
                Text
                <span style={{ color: "red" }}>*</span>
              </Typography>
              <TextField
                sx={{ fontSize: "12px !important" }}
                size="small"
                fullWidth
                onChange={handleText}
                placeholder="Enter Text"
                value={ddSearchForm?.text}
                inputProps={{ maxLength: 22}}
              />
            </Grid>
            <Grid
              item
              md={6}
              sx={{
                display: "flex",
                justifyContent: "flex-start",
                alignItems:"center",
                marginTop:"17px"
              }}
            >
              <Button
                onClick={handleDuplicateCheck}
                variant="contained"
                // disabled={isAnyFieldEmpty || !isProceedEnabled}
              >
                Check
              </Button>
            </Grid>
          </Grid>
        
            {checkButtonClicked && responseList?.length === 0 ? (
              <Chip
                sx={{
                  justifyContent: "flex-start",
                  borderRadius: "4px",
                  color: "#000",
                  minWidth: "5rem",
                  fontSize: "12px",
                  margin: "5px",
                  backgroundColor: "#d2f5b3",
                }}
                label={
                  <Typography sx={font_Small}>
                    No probables found. You can proceed.
                  </Typography>
                }
              />
            ) : (
              // Render the probables list only if isListOpen is true and there are probables
              isListOpen &&
              responseList?.length > 0 && (
                <Grid container sx={{ paddingTop: "12px" }}>
                  <Grid item>
                    <Card>
                      <Typography sx={font_Small}>Probables</Typography>

                      {responseList.map((item, index) => (
                        <Chip
                          key={index}
                          sx={{
                            justifyContent: "flex-start",
                            borderRadius: "4px",
                            color: "#000",
                            minWidth: "5rem",
                            fontSize: "12px",
                            margin: "5px",
                            backgroundColor: isProbableMatch(item) 
                              ? "#ea9999"
                              : "#f6f181",
                          }}
                          label={
                            <Typography sx={font_Small}>
                              {/* Display the actual probable value from the backend */}
                              {item}
                            </Typography>
                          }
                        />
                      ))}
                    </Card>
                  </Grid>
                </Grid>
              )
            )}
 {isAnyFieldEmpty && (
                      <Grid>
                        <Typography style={{ color: "red" }}>
                        Please fill in all required fields marked with *.
                        </Typography>
                      </Grid>
                    )}
        </DialogContent>

        <DialogActions>
          <Button onClick={handleDialogClose}>Cancel</Button>
          <Button
            onClick={handleDialogProceed}
            disabled={!isProceedEnabled || isDirectMatch}
            variant="contained"
          >
            Proceed
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default DuplicateDesc;
