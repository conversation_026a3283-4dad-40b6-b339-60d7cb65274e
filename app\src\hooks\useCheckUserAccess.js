import { doAjax } from '@components/Common/fetchService';
import React, { useState } from 'react'
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';
import { destination_MaterialMgmt } from '../destinationVariables';
import { END_POINTS } from '@constant/apiEndPoints';
import { API_CODE, ERROR_MESSAGES, LOADING_MESSAGE, SUCCESS_MESSAGES } from '@constant/enum';
import useLogger from './useLogger';
import { setPayload } from '@app/payloadSlice';
import { showToast } from '../functions';
import { toast } from 'react-toastify';

const useCheckUserAccess = () => {
    const { customError } = useLogger();
    const userData = useSelector((state) => state.userManagement.userData);
    const [ isError, setIsError ] = useState(false);
    const dispatch = useDispatch();
    const getUserAccessData = () => {
      const loadingToastId = toast.loading(LOADING_MESSAGE.LOADING_USER_ACCESS, {
        position: "bottom-left",
        theme: "dark",
      });
        let payload = {
          userMailId: userData?.emailId || "",
          tCode:"MM02"
        };
        const hSuccess = (data) => {
            toast.dismiss(loadingToastId);
            if (data.statusCode === API_CODE?.STATUS_200) {
              let responseData = data?.body[0];
              dispatch(setPayload({ keyName: "DirectAllowed", data: responseData?.DirectAllowed }));
              showToast(SUCCESS_MESSAGES.USER_ACCESS_SUCCESS, "success");
            } else {
            customError("Failed to fetch data");
            setIsError(true)
          }
        };
        
        const hError = (error) => {
          setIsError(true);
          customError(error);
        };

        doAjax(`/${destination_MaterialMgmt}${END_POINTS?.USER_ACCESS?.CHECK_ACCESS}`, "post", hSuccess, hError, payload);

      };
    
    return { getUserAccessData, isError };
}

export default useCheckUserAccess