import { MODULE_MAP } from "./enum";

export const CREATE_CHANGE_LOG_TEMPS = {
      "AdditionalData": {
        fieldName: ["Material", "AltUnit", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Alternative Unit of Measure", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "BasicData": {
        fieldName: ["Material",  "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material",  "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "MRPData": {
        fieldName: ["Material", "Plant", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Plant", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "PurchasingData":{
        fieldName: ["Material", "Plant", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Plant", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "SalesData": {
        fieldName: ["Material", "SalesOrg", "DistrChan", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Sales Org", "Distribution Channel", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "SalesPlantData":{
        fieldName: ["Material", "Plant", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Plant", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "SalesGeneralData":{
        fieldName: ["Material", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "PurchasingGeneralData":{
        fieldName: ["Material", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "WarehouseData": {
        fieldName: ["Material", "WhseNo",   "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Warehouse",  "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "DescriptionData": {
        fieldName: ["Material", "Langu", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Language", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "AdditionalEANData": {
        fieldName: ["Material", "AltUnit", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Alternative Unit of Measure", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "CostingData": {
        fieldName: ["Material","Plant", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material","Plant", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "AccountingData": {
        fieldName: ["Material", "Plant", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Plant", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "WorkSchedulingData": {
        fieldName: ["Material", "Plant", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Plant", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "TaxClassificationData": {
        fieldName: ["Material", "Country", "TaxType", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Country", "Tax Type", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
  };

export const MODULE_SPECIFIC_CHANGE_LOG_TEMPS = {
  [MODULE_MAP?.BK]: {
    "Control Data": {
      fieldName: ["BankKey", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
      headerName: ["Bank Key",  "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
    },
    "Address Data": {
      fieldName: ["BankKey", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
      headerName: ["Bank Key", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
    },
  },
}

export const FINANCE_CHANGE_LOG_TEMPS = {
  "FinanceCostData": {
        fieldName: ["Material", "Plant", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Plant", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
}
  