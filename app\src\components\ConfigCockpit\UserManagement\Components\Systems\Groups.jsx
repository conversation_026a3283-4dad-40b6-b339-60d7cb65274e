import React, { useState, useEffect } from "react";
import {
  Paper,
  IconButton,
  Tooltip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  TextField,
  Typography,
  Checkbox,
  BottomNavigation,
} from "@mui/material";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import { makeStyles } from "@mui/styles";
import {
  Add,
  Refresh,
  CheckBoxOutlineBlank,
  CheckBox,
} from "@mui/icons-material";
import { Autocomplete } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import Loading from "../Loading";
import { findApplicationById } from "../../Utility/basic";
import {
  setGroups,
  setResponseMessage,
} from "../../../../../app/userManagementSlice";
import GroupDetail from "./GroupDetail";
import UploadFile from "../UploadFile";
import {
  appHeaderHeight,
  groupPageHeaderHeight,
  sidebarWidth,
} from "../../Data/cssConstant";
import {
  destination_IWA,
  destination_IWA_NPI,
  // destination_IWA_SCP,
  destination_Po,
} from "../../../../../destinationVariables";
// import { doAjax } from "../../../../common/fetchService";
import {
  button_Primary,
  font_Small,
  iconButton_SpacingSmall,
} from "../../../../common/commonStyles";
import ReusableTable from "../../../../common/ReusableTable";
import ReusablePromptBox from "../../../../common/ReusablePromptBox/ReusablePromptBox";
import applicationConfigReducer from "../../../../../app/applicationConfigReducer";
import localConfigServer from '../../../../../data/localConfigServer.json'
import CloseIcon from "@mui/icons-material/Close";
import { doAjax } from "../../../../Common/fetchService";

const useStyle = makeStyles((theme) => ({
  groupsHeaderContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end",
    paddingBottom: 10,
    position: "sticky",
    top: 0,
    zIndex: 99,
    height: groupPageHeaderHeight,
  },
  groupHeadeTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: theme.palette.text.primary,
  },
  groupsHeaderDropdown: {
    width: 150,
    marginRight: 6,
  },
  groupsHeaderAddButton: {
    marginLeft: 10,
    textTransform: "capitalize",
  },
  groupsTableContainer: {
    height: "100%",
    width: "100%",
  },
  groupsTableHead: {
    position: "sticky",
    top: 0,
    zIndex: 99,
    backgroundColor: "#F1F5FE",
  },
  groupsTableHeadCell: {
    whiteSpace: "nowrap",
    fontSize: 9,
    fontWeight: "bold",
    borderLeft: "1px solid #E5E5E5",
    borderRight: "1px solid #E5E5E5",
  },
  groupsTableBody: {
    height: "100%",
  },
  groupsTableBodyRow: {
    cursor: "pointer",
    "&:hover": {
      backgroundColor: theme.palette.action.hover,
    },
  },
  groupsTableBodyRowSelected: {
    backgroundColor: theme.palette.action.selected,
  },
  groupsTableBodyCell: {
    whiteSpace: "nowrap",
    fontSize: 10,
    backgroundColor: "white",
    borderLeft: "1px solid #E5E5E5",
    borderRight: "1px solid #E5E5E5",
  },
  groupsTableBodyTextHide: {
    overflow: "hidden",
    maxWidth: 180,
    textOverflow: "ellipsis",
  },
}));

const NewGroup = ({
  open,
  onClose,
  filteredGroups,
  setFilteredGroups,
  selectAplication,
  setSelectApplication,
  handleRefresh,
  handleOpenPromptBox,
}) => {
  let masterData = useSelector((state) => state.masterData);
  const [suppliersData, setsuppliersData] = useState({
    codeList: [],
    dataMap: {},
  });
  const classes = useStyle();
  const basicReducerState = useSelector((state) => state.userManagement);
  const [load, setLoad] = useState(false);
  const [purchasingGroups, setPurchasingGroups] = useState({});
  const applicationConfig =  useSelector(state=> state.applicationConfig)
  const getPurchGroupDetails = () => {
    // if (userData?.supplierId) {
    //   setVendorDetailsSet({
    //     [userData?.supplierId]: userData?.supplierName,
    //   });
    // } else {

    // if (moduleFilter?.companyCode !== "") {
    const hSuccess = (data) => {
      setPurchasingGroups(data.data);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_Po}/Odata/purchasingGroup`, "get", hSuccess, hError);
    // }
    // }
  };
  const initialGroup = {
    name: "",
    applicationId: "",
    users: [],
    roles: [],
    status: "Draft",
    companyCode: "",
    supplierId: "",
    purchasingGroup: "",
  };
  const [newGroup, setNewGroup] = useState(initialGroup);
  const dispatch = useDispatch();

  const getApplicationNameById = (applicationId) => {
    const application = findApplicationById(
      Number(applicationId),
      basicReducerState.applications
    );
    return application?.name || "-";
  };
  const insertNewGroup = () => {
    try {
      setLoad(true);
      const createGroupUrl = `/${destination_IWA_NPI}/api/v1/groupsMDG/createGroupMDG`;
      const createGroupPayload = {
        name: newGroup?.name,
        applicationId: newGroup?.applicationId,
        userIdList:
          newGroup?.users?.map((user) => user?.emailId).join(",") || "",
        companyCode: newGroup.companyCode
          ? `${newGroup.companyCode} - ${
              masterData.companyCode[newGroup.companyCode]
            }`
          : "",
        supplierId: newGroup.supplierId
          ? `${newGroup.supplierId} - ${
              suppliersData.dataMap[newGroup.supplierId]
            }`
          : "",
        roleIdList:
          newGroup?.roles?.map((role) => Number(role?.id)).join(",") || "",

          purchasingGroup: newGroup?.purchasingGroup ? `${newGroup?.purchasingGroup} - ${purchasingGroups[newGroup?.purchasingGroup]}`:""
      };
      const createGroupRequestParam = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(createGroupPayload),
      };
      // console.log(createGroupPayload)
      fetch(createGroupUrl, createGroupRequestParam)
        .then((res) => res.json())
        .then((group_data) => {
          setLoad(false);

          setNewGroup(initialGroup);
          // getAllGroups(
          //   () => {
          //     // setLoad(true);
          //   },
          //   (data) => {
          //     dispatch(setGroups(data?.data || []));
          //     setSelectApplication(-1);
          //     setFilteredGroups(
          //       data?.data?.map((group) => ({
          //         ...group,
          //         applicationId: group?.applicationId?.split(",") || [],
          //       })) || []
          //     );
          //     setLoad(false);
          //     onClose();

          //     dispatch(
          //       setResponseMessage({
          //         open: true,
          //         status: group_data?.status ? "success" : "error",
          //         message: group_data?.status
          //           ? "Group created successfully"
          //           : "Something went wrong",
          //       })
          //     );
          //   },
          //   (err) => {
          //     setLoad(false);

          //   }
          // );
          handleOpenPromptBox("SUCCESS", {
            message: `Group Created Successfully`,
            redirectOnClose: false,
          });
          onClose();
          handleRefresh();
        })
        .catch((err) => {
          setLoad(false);
        });
    } catch (e) {
      console.log("error");
    }
  };

  useEffect(() => {
    getPurchGroupDetails()
    doAjax(`/${destination_Po}/Odata/getAllSuppliers`, "get", (res) => {
      setsuppliersData({
        codeList: Object.keys(res),
        dataMap: res,
      });
    });
  }, []);
  useEffect(() => {
    console.log(newGroup, "rerr");
  }, [newGroup]);
  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose}>
      <DialogTitle sx={{
          height: "3rem",
          display: "flex",
          margin: 0,
          justifyContent: "space-between",
          alignItems: "center",
          padding: ".5rem",
          paddingLeft: "1rem",
          backgroundColor: "#EAE9FF40",
        }}>
        <Typography variant="h6">New Group</Typography>
        <IconButton
            sx={{ width: "max-content" }}
            onClick={onClose}
            children={<CloseIcon />}
          />
      </DialogTitle>

      <DialogContent sx={{ padding: "1rem 1rem" }}>
        <Loading load={load} />

        <Grid
          container
          sx={{
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Grid item xs sx={{
            marginTop:'.5rem'
          }}>
            <Typography variant="body1">Group Name<span style={{ color: "red" }}>*</span></Typography>
            <TextField
              variant="outlined"
              placeholder="Enter Group Name"
              fullWidth
              required
              size="small"
              value={newGroup?.name}
              onChange={(e) => {
                setNewGroup({ ...newGroup, name: e.target.value });
              }}
              error={basicReducerState?.groups?.find(
                (group) => group?.name === newGroup?.name
              )}
              helperText={
                basicReducerState?.groups?.find(
                  (group) => group?.name === newGroup?.name
                ) && "Group name already exists."
              }
            />
          </Grid>

          <Grid item xs sx={{
            marginTop:'.5rem'
          }}>
            <Typography variant="body1">Users</Typography>
            <Autocomplete
              multiple
              size="small"
              disableCloseOnSelect
              filterSelectedOptions
              style={{ fontSize: 12 }}
              value={newGroup?.users}
              onChange={(e, users) => {
                setNewGroup({
                  ...newGroup,
                  users: users,
                });
              }}
              options={basicReducerState?.users}
              getOptionLabel={(option) => option?.displayName}
              renderOption={(props, option, { selected }) => (
                <li {...props}>
                  <Checkbox
                    icon={<CheckBoxOutlineBlank fontSize="small" />}
                    checkedIcon={<CheckBox color="primary" fontSize="small" />}
                    checked={selected}
                  />

                  <Typography style={{ fontSize: 12 }}>
                    {option?.displayName}
                  </Typography>
                </li>
              )}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  style={{ fontSize: 12 }}
                  placeholder="Select Users"
                />
              )}
            />
          </Grid>

          {/* {!{
            CHWSCP: true,
          }[localConfigServer.system] && ( */}
            {/* <Grid
              item
              xs
              sx={{
                marginTop: ".5rem",
              }}
            >
              <Typography variant="body1">Purchasing Group</Typography>
              <Autocomplete
                size="small"
                // disableCloseOnSelect
                filterSelectedOptions
                style={{ fontSize: 12 }}
                value={newGroup?.purchasingGroup}
                onChange={(e, data) => {
                  setNewGroup({
                    ...newGroup,
                    purchasingGroup: data,
                  });
                }}
                options={Object.keys(purchasingGroups)}
                getOptionLabel={(option) => {
                  if (option) {
                    return `${option} - ${purchasingGroups?.[option]}`;
                  } else {
                    return "";
                  }
                }}
                renderOption={(props, option, { selected }) => (
                  <li {...props}>
                    <Checkbox
                      icon={<CheckBoxOutlineBlank fontSize="small" />}
                      checkedIcon={
                        <CheckBox color="primary" fontSize="small" />
                      }
                      checked={selected}
                    />

                    <Typography style={{ fontSize: 12 }}>
                      {`${option} - ${purchasingGroups?.[option]}`}
                    </Typography>
                  </li>
                )}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    style={{ fontSize: 12 }}
                    placeholder="Select Purchasing Group"
                  />
                )}
              />
            </Grid> */}
          {/* )} */}

         {/* {{
          CHWSCP:true
         }[localConfigServer.system]&& <Grid item xs sx={{
            marginTop:'.5rem'
          }}>
            <Typography variant="body1">Company</Typography>
            <Autocomplete
              size="small"
              // disableCloseOnSelect
              filterSelectedOptions
              style={{ fontSize: 12 }}
              value={newGroup?.company}
              onChange={(e, data) => {
                console.log(data, "rerrr");
                setNewGroup({
                  ...newGroup,
                  companyCode: data,
                });
              }}
              options={Object.keys(masterData?.companyCode)}
              getOptionLabel={(option) => {
                if (option) {
                  return `${option} - ${masterData?.companyCode[option]}`;
                } else {
                  return "";
                }
              }}
              renderOption={(props, option, { selected }) => (
                <li {...props}>
                  <Checkbox
                    icon={<CheckBoxOutlineBlank fontSize="small" />}
                    checkedIcon={<CheckBox color="primary" fontSize="small" />}
                    checked={selected}
                  />

                  <Typography style={{ fontSize: 12 }}>
                    {`${option} - ${masterData?.companyCode[option]}`}
                  </Typography>
                </li>
              )}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  style={{ fontSize: 12 }}
                  placeholder="Select Company"
                />
              )}
            />
          </Grid>}
          <Grid item xs sx={{
            marginTop:'.5rem'
          }}>
            <Typography variant="body1">Supplier</Typography>
            <Autocomplete
              // multiple
              size="small"
              // disableCloseOnSelect
              filterSelectedOptions
              style={{ fontSize: 12 }}
              value={newGroup?.supplierId}
              onChange={(e, data) => {
                setNewGroup({
                  ...newGroup,
                  supplierId: data,
                });
              }}
              options={suppliersData?.codeList}
              getOptionLabel={(option) => {
                if (option) {
                  return `${option} - ${suppliersData.dataMap[option]}`;
                } else {
                  return "";
                }
              }}
              renderOption={(props, option, { selected }) => (
                <li {...props}>
                  <Checkbox
                    icon={<CheckBoxOutlineBlank fontSize="small" />}
                    checkedIcon={<CheckBox color="primary" fontSize="small" />}
                    checked={selected}
                  />

                  <Typography style={{ fontSize: 12 }}>
                    {`${option} - ${suppliersData.dataMap[option]}`}
                  </Typography>
                </li>
              )}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  style={{ fontSize: 12 }}
                  placeholder="Select Supplier"
                />
              )}
            />
          </Grid> */}
        </Grid>
      </DialogContent>

      <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
        <Button
          key={"CANCEL"}
          size="small"
          variant="outlined"
          onClick={() => {
            onClose();
            setNewGroup(initialGroup);
          }}
        >
          Cancel
        </Button>

        <Button
          key={"ADD"}
          size="small"
          variant={
            (load ||
            newGroup?.name?.length === 0 ||
            basicReducerState?.groups?.find(
              (group) => group?.name === newGroup?.name
            )) ? "outlined" : "contained"
          }
          className="btn-ml"
          onClick={insertNewGroup}
          style={{ textTransform: "capitalize" }}
          disabled={
            load ||
            newGroup?.name?.length === 0 ||
            basicReducerState?.groups?.find(
              (group) => group?.name === newGroup?.name
            )
          }
        >
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
};

function Groups() {
  const classes = useStyle();
  const basicReducerState = useSelector((state) => state.userManagement);
  const [load, setLoad] = useState(true);
  const [selectAplication, setSelectApplication] = useState(-1);
  const [groups, setgroups] = useState([]);
  const [filteredGroups, setFilteredGroups] = useState([]);
  const [openNewGroupDialog, setOpenNewGroupDialog] = useState(false);
  const [file, setFile] = useState(null);
  const [openGroupFileDialog, setOpenGroupFileDialog] = useState(false);
  const [deletingGroup, setDeletingGroup] = useState(null);
  const [params, setParams] = useState({});
  const dispatch = useDispatch();
  const [openDeletionDialog, setOpenDeletionDialog] = useState(false);
  const [promptType, setPromptType] = useState("dialog");
  const [promptMessage, setPromptMessage] = useState("");

  const groupsTableColumns = [
    {
      field: "name",
      headerName: "Group Name",
      flex: 1,
    },
    {
      field: "supplierId",
      headerName: "Supplier",
      flex: 1,
      hide:localConfigServer.system !=='CHWSCP',
      renderCell: (params) => {
        return params.row.supplierId !== "" ? (
          <Typography variant="body2">{params.row.supplierId}</Typography>
        ) : (
          <Typography variant="body2">Not Applicable</Typography>
        );
      },
    },
    {
      field: "purchasingGroup",
      headerName: "Purchasing Group",
      flex: 1,
      // hide:localConfigServer.system ==='CHWSCP',
      renderCell: (params) => {
        return params.row.purchasingGroup !== "" ? (
          <Typography variant="body2">{params.row.purchasingGroup}</Typography>
        ) : (
          <Typography variant="body2">Not Applicable</Typography>
        );
      },
    },
    {
      field: "roleName",
      headerName: "Role",
      flex: 1,
    },
    {
      field: "getUsersCountPerGroup",
      headerName: "User Count",
      headerAlign: "right",
      align: "right",
      flex: 1,
      type: "number",
    },
    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (cellValues) => {
        let user = cellValues.row;
        return (
          <Tooltip title="Delete">
            <IconButton
              aria-label="Delete"
              disabled={load || cellValues.row?.getUsersCountPerGroup !== 0}
              onClick={(e) => {
                e.stopPropagation();
                setDeletingGroup(user?.id);
                promptAction_Functions.handleOpenPromptBox("DELETE", {
                  title: "Confirm Delete",
                  message: `Do you want to delete the group?`,
                  severity: "warning",
                  cancelButton: true,
                  okButton: true,
                  okButtonText: "Delete",
                });
                // setOpenDeletionDialog(true);
                // setPromptType("dialog");
                // setPromptMessage("");
              }}
            >
              <DeleteOutlinedIcon color={cellValues.row?.getUsersCountPerGroup === 0 ? "danger" : "gray"} />
            </IconButton>
          </Tooltip>
        );
      },
    },
  ];

  const getApplicationNameById = (applicationId) => {
    const application = findApplicationById(
      Number(applicationId),
      basicReducerState.applications
    );
    return application?.name || "-";
  };

  const editGroup = (groupId) => {
    setParams({ groupId: groupId });
  };
  const getGroups = () => {
    const getGroupsUrl = `/${destination_IWA_NPI}/api/v1/groupsMDG/getAllGroupsMDG`;
    const getGroupsRequestParam = {
      headers: {
        "Content-Type": "application/json",
      },
    };
    fetch(getGroupsUrl, getGroupsRequestParam)
      .then((res) => res.json())
      .then((data) => {
        dispatch(setGroups(data?.data || []));
        setLoad(false);
      })
      .catch((err) => {
        setLoad(false);
      });
  };
  const refresh = () => {
    getGroups();
  };
  const deleteGroup = (groupId) => {
    // setLoad(true);
    const disableGroupUrl = `/${destination_IWA_NPI}/api/v1/groupsMDG/deactivateGroupMDG?id=${deletingGroup}`;
    const deleteGroupPayload = {
      id: deletingGroup,
    };
    const disableGroupRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(deleteGroupPayload),
    };
    fetch(disableGroupUrl)
      .then((res) => res.json())
      .then((data) => {
        refresh();
        // setLoad(false);
        if (data.statusCode === 200) {
          promptAction_Functions.handleOpenPromptBox("SUCCESS", {
            message: `Group deleted successfully`,
            redirectOnClose: false,
          });
          // setPromptType("snackbar");
          // setPromptMessage(`Group deleted successfully`);
          // setOpenDeletionDialog(true);
        } else {
          promptAction_Functions.handleOpenPromptBox("ERROR", {
            title: "Failed",
            message: `Group deletion failed`,
            severity: "danger",
            cancelButton: false,
          });
          // setPromptType("snackbar");
          // setPromptMessage(`Group deletion failed`);
          // setOpenDeletionDialog(true);
        }
        getGroups();
        // setPromptType("");
        // setPromptMessage("");
        // setOpenDeletionDialog(false);
        // dispatch(
        //   setGroups(
        //     basicReducerState?.groups?.filter(
        //       (group) => Number(group?.id) !== Number(groupId)
        //     ) || []
        //   )
        // );
        // setgroups(
        //   groups?.filter((group) => group?.id !== Number(groupId)) || []
        // );
        // setFilteredGroups(
        //   filteredGroups?.filter((group) => group?.id !== Number(groupId)) || []
        // );
        setDeletingGroup(null);

        // dispatch(
        //   setResponseMessage({
        //     open: true,
        //     status: data?.status ? "success" : "error",
        //     message: data?.status
        //       ? "Group deleted successfully"
        //       : "Something went wrong",
        //   })
        // );
      })
      .catch((err) => {
        setLoad(false);
      });
  };
  //<-- Functions and variables for ReusablePromptBox *promptAction_Functions -->
  const [promptBoxState, setPromptBoxState] = useState({
    open: false,
    type: "",
    redirectOnClose: true,
    message: "",
    title: "",
    severity: "",
  });
  const [promptBoxScenario, setPromptBoxScenario] = useState("");

  const promptAction_Functions = {
    handleClosePromptBox: () => {
      setPromptBoxState((prev) => ({
        open: false,
        type: "",
        redirectOnClose: false,
        message: "",
        title: "",
        severity: "",
      }));
      getGroups();
      setPromptBoxScenario("");
    },
    handleOpenPromptBox: (ref, data = {}) => {
      // SUCCESS,FAILURE
      let initialData = {
        open: true,
        title: "",
        message: "",
        okButton: true,
        cancelButton: true,
        okButtonText: "Ok",
        cancelText: "Cancel",
        type: "dialog",
      };
      if (ref === "SUCCESS") {
        initialData.type = "snackbar";
      }
      setPromptBoxScenario(ref);
      setPromptBoxState({
        ...initialData,
        ...data,
      });
    },
    handleCloseAndRedirect: () => {
      promptAction_Functions.handleClosePromptBox();
      // navigate("/purchaseOrder/management");
    },
    getCancelFunction: () => {
      switch (promptBoxScenario) {
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getCloseFunction: () => {
      switch (promptBoxScenario) {
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getOkFunction: () => {
      switch (promptBoxScenario) {
        case "DELETE":
          return deleteGroup;
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getCloseAndRedirectFunction: () => {
      if (!promptBoxState.redirectOnClose) {
        return promptAction_Functions.handleClosePromptBox;
      }
      return promptAction_Functions.handleCloseAndRedirect;
    },
  };

  useEffect(() => {
    setgroups(
      basicReducerState?.groups?.map((group) => ({
        ...group,
        applicationId: group?.applicationId?.split(",") || [],
      })) || []
    );
    if (selectAplication === -1) {
      setFilteredGroups(
        basicReducerState?.groups?.map((group) => ({
          ...group,
          applicationId: group?.applicationId?.split(",") || [],
        })) || []
      );
    } else {
      setFilteredGroups(
        groups
          ?.filter((group) => group?.applicationId === selectAplication)
          ?.map((group) => ({
            ...group,
            applicationId: group?.applicationId?.split(",") || [],
          })) || []
      );
    }
    setLoad(false);
  }, [basicReducerState?.groups]);

  useEffect(() => {
    getGroups();
  }, []);

  return (
    <div>
      <Loading load={load} />
      <Paper
        sx={{ position: "fixed", bottom: 0, left: 0, right: 0, zIndex: 1 }}
        elevation={2}
      >
        <BottomNavigation
          showLabels
          className="container_BottomNav"
          sx={{
            display: "flex",
            justifyContent: "flex-end",
          }}
        >
          <Button
            size="small"
            variant="contained"
            color="primary"
            sx={{
              marginLeft: "auto",
            }}
            onClick={() => setOpenNewGroupDialog(true)}
            startIcon={<Add />}
            disabled={load}
          >
            Create Group
          </Button>
        </BottomNavigation>
      </Paper>
      <>
        <NewGroup
          open={openNewGroupDialog}
          onClose={() => setOpenNewGroupDialog(false)}
          filteredGroups={filteredGroups}
          setFilteredGroups={setFilteredGroups}
          selectAplication={selectAplication}
          handleOpenPromptBox={promptAction_Functions.handleOpenPromptBox}
          setSelectApplication={setSelectApplication}
          handleRefresh={refresh}
        />

        <UploadFile
          open={openGroupFileDialog}
          onClose={() => {
            setOpenGroupFileDialog(false);
            setFile(null);
          }}
          onUpload={() => {}}
          file={file}
          setFile={setFile}
          disableCondition={!file}
        >
          <Autocomplete
            multiple
            size="small"
            disableCloseOnSelect
            filterSelectedOptions
            style={{ fontSize: 12 }}
            options={basicReducerState?.applications}
            getOptionLabel={(option) => option?.name}
            renderOption={(props, option, { selected }) => (
              <li {...props}>
                <Checkbox
                  icon={<CheckBoxOutlineBlank fontSize="small" />}
                  checkedIcon={<CheckBox color="primary" fontSize="small" />}
                  checked={selected}
                />

                <Typography style={{ fontSize: 12 }}>{option?.name}</Typography>
              </li>
            )}
            renderInput={(params) => (
              <TextField
                {...params}
                variant="standard"
                label="Applications"
                style={{ fontSize: 12 }}
              />
            )}
          />
        </UploadFile>

        {/* <DeletionMessageBox
          open={deletingGroup ? true : false}
          onClose={() => setDeletingGroup(null)}
          onDelete={() => {
            deleteGroup(deletingGroup);
          }}
          load={load}
        /> */}
        <ReusablePromptBox
          type={promptBoxState.type}
          promptState={promptBoxState.open}
          setPromptState={promptAction_Functions.handleClosePromptBox}
          onCloseAction={promptAction_Functions.getCloseFunction()}
          promptMessage={promptBoxState.message}
          dialogSeverity={promptBoxState.severity}
          dialogTitleText={promptBoxState.title}
          handleCancelButtonAction={promptAction_Functions.getCancelFunction()}
          cancelButtonText={promptBoxState.cancelText} //Cancel button display text
          showCancelButton={promptBoxState.cancelButton} //Enable Cancel button
          handleSnackBarPromptClose={promptAction_Functions.getCloseAndRedirectFunction()}
          handleOkButtonAction={promptAction_Functions.getOkFunction()}
          okButtonText={promptBoxState.okButtonText}
          showOkButton={promptBoxState.okButton}
        />
        {/* <ReusablePromptBox
          type={promptType}
          promptState={openDeletionDialog}
          setPromptState={setOpenDeletionDialog}
          dialogSeverity={"danger"}
          dialogTitleText={"Group Deletion"}
          cancelButtonText={"Cancel"}
          showCancelButton={true}
          okButtonText={"Delete"}
          showOkButton={true}
          promptMessage={promptMessage}
          handleOkButtonAction={deleteGroup}
          handleSnackBarPromptClose={() => {
            getGroups();
            setOpenDeletionDialog(false);
          }}
        /> */}

        <div style={{ height: "38px", marginTop: ".5rem" }}>
          {/* <Typography className={classes.groupHeadeTitle}>Groups</Typography>  */}

          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "end",
            }}
          >
            {/* <FormControl
              variant="standard"
              size="small"
              required
              className={classes.groupsHeaderDropdown}
            >
              <InputLabel>Application name</InputLabel>
              <Select
                size="small"
                style={{ fontSize: 12 }}
                value={selectAplication}
                onChange={(e) => {
                  setSelectApplication(e.target.value);
                  if (e.target.value !== -1) {
                    setFilteredGroups(
                      groups?.filter(
                        (group) =>
                          Number(group?.applicationId[0]) === e.target.value
                      ) || []
                    );
                  } else {
                    setFilteredGroups(groups);
                  }
                }}
              >
                <MenuItem value={-1} style={{ fontSize: 12 }}>
                  All
                </MenuItem>
                {basicReducerState?.applications?.map((application, index) => (
                  <MenuItem
                    key={`${application?.id}-${index}`}
                    value={application?.id}
                    style={{ fontSize: 12 }}
                  >
                    {application?.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl> 

            <Tooltip title="Download template">
            <IconButton
              size="small"
              // color="primary"
              style={{ marginLeft: 4 }}
              disabled={load}
              onClick={(e) => {
                downloadFile({
                  data: groupFileHeading,
                  fileName: "cw_groups.csv",
                  fileType: "text/csv",
                });
              }}
            >
              <GetApp  />
            </IconButton>
          </Tooltip>

          <Tooltip title="Upload file">
            <IconButton
              size="small"
              // color="primary"
              style={{ marginLeft: 4 }}
              disabled={load}
              onClick={() => {
                setOpenGroupFileDialog(true);
              }}
            >
              <Publish  />
            </IconButton>
          </Tooltip>  */}

            <Tooltip title="Refresh">
              <IconButton
                sx={{ ...iconButton_SpacingSmall }}
                disabled={load}
                onClick={getGroups}
              >
                <Refresh />
              </IconButton>
            </Tooltip>
          </div>
        </div>

        <Grid
          container
          spacing={2}
          style={{
            height: `calc(100vh - ${appHeaderHeight} - ${groupPageHeaderHeight})`,
          }}
        >
          <Grid item xs={params?.groupId ? 6 : 12} style={{ height: "100%" }}>
            <ReusableTable
              width="100%"
              stopPropagation_Column={["action", "isDefault"]}
              status_onRowSingleClick={true}
              title={`List of Groups (${filteredGroups?.length ?? 0})`}
              rows={
                filteredGroups
                //   [
                //   {
                //     id: 1,
                //     name: "Buyer",
                //     applicationId: "1",
                //     userIdList:
                //       "<EMAIL>,<EMAIL>,<EMAIL>",
                //     roleIdList: "3",
                //     getUsersCountPerGroup: "10",
                //     updatedBy: "null",
                //     updatedOn: "2023-05-31 09:07:24.000000000",
                //   }
                // ]
              }
              columns={groupsTableColumns}
              getRowIdValue={"id"}
              hideFooter={false}
              checkboxSelection={false}
              noOfColumns={5}
              rowsPerPageOptions={[5, 10, 15]}
              disableSelectionOnClick={true}
              callback_onRowSingleClick={(params) => {
                editGroup(params.row.id || null);
              }}
            />
          </Grid>

          {params?.groupId && (
            <Grid
              item
              xs={params?.groupId ? 6 : false}
              sx={{ marginTop: "30px" }}
            >
              <GroupDetail
                params={params}
                setParams={setParams}
                openPromptBox={promptAction_Functions.handleOpenPromptBox}
              />
            </Grid>
          )}
        </Grid>
      </>
    </div>
  );
}

export default Groups;
