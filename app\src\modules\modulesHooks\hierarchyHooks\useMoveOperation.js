import { useDispatch } from "react-redux";
import { message } from "antd";
import { useCallback } from "react";

import { setTreeData, updateTreeChanges } from "@app/hierarchyDataSlice";

/**
 * Utility to recursively find the parent node of a given node ID
 */
const findParentNode = (nodes, childId) => {
  for (const node of nodes) {
    if (node.child?.some((child) => child.id === childId)) {
      return node;
    }
    if (node.child) {
      const found = findParentNode(node.child, childId);
      if (found) return found;
    }
  }
  return null;
};

/**
 * Utility to remove a node from the tree by ID
 */
const removeNode = (nodes, nodeId) => {
  return nodes
    .map((node) => {
      if (node.child) {
        return {
          ...node,
          child: removeNode(node.child, nodeId),
        };
      }
      return node;
    })
    .filter((node) => node.id !== nodeId);
};

// Helper function to add child node while maintaining rawTreeData structure
const addChildNode = (nodes, parentId, newNode) => {
  return nodes.map((node) => {
    if (node.id === parentId) {
      return {
        ...node,
        child: [...(node.child || []), newNode],
      };
    }
    if (node.child) {
      return {
        ...node,
        child: addChildNode(node.child, parentId, newNode),
      };
    }
    return node;
  });
};

const reindexTree = (nodes, prefix = "") => {
  return nodes.map((node, index) => {
    const newId = prefix === "" ? `${index}` : `${prefix}-${index}`;
    const updatedChild = node.child ? reindexTree(node.child, newId) : [];
    return {
      ...node,
      id: newId,
      child: updatedChild,
    };
  });
};

  // Helper to remove tag from node
  const removeTagFromNodeForMovement = (nodes, nodeId, tag) => {
    return nodes.map((node) => {
      if (node.id === nodeId) {
        return {
          ...node,
          tags: node.tags?.filter((t) => t !== tag) || [],
        };
      }
      if (node.child) {
        return {
          ...node,
          child: removeTagFromNodeForMovement(node.child, nodeId, tag),
        };
      }
      return node;
    });
  };

    // Helper to add tag to node
  const addTagToNode = (nodes, nodeId, newTag) => {
    return nodes.map((node) => {
      if (node.id === nodeId) {
        return {
          ...node,
          tags: [...(node.tags || []), newTag],
        };
      }
      if (node.child) {
        return { ...node, child: addTagToNode(node.child, nodeId, newTag) };
      }
      return node;
    });
  };
  

const useMoveOperation = ({
  rawTreeData,
  treeChanges,
  setSelectedNode,
  setOriginalParent,
  selectedNode,
  addToChangeLog,
  setSelectedTag,
  selectedTag,
  object,
  originalParent,

}) => {
  const dispatch = useDispatch();

  const handleSelectNode = useCallback(
    (node) => {
      const parent = findParentNode(rawTreeData, node.id);
      setOriginalParent(parent);

      const updatedTree = removeNode([...rawTreeData], node.id);

      const existingNode = treeChanges?.[node?.label];

      if (!existingNode?.oldParentNode) {
        dispatch(
          updateTreeChanges({
            nodeLabel: node?.label,
            changes: {
              ...existingNode,
              isMoved: true,
              oldParentNode: parent?.label,
            },
          })
        );
      }

      dispatch(setTreeData(updatedTree));
      setSelectedNode(node);
      message.info(`Moving ${node.label} - select destination`);
    },
    [rawTreeData, treeChanges, dispatch, setSelectedNode, setOriginalParent]
  );

  /**
   * Triggered when user selects a destination node to drop the selected node under
   */
  const handlePlaceNode = useCallback(
    (parentNode) => {
      if (!selectedNode) return;

      const cleanedTree = removeNode([...rawTreeData], selectedNode.id);
      const treeWithNodeAdded = addChildNode(
        cleanedTree,
        parentNode.id,
        selectedNode
      );
      const reindexedTree = reindexTree(treeWithNodeAdded);

      addToChangeLog(
        "MOVE NODE",
        `${selectedNode?.label} Node moved from ${selectedNode?.label} to ${parentNode?.label}`
      );

      const existingNode = treeChanges[selectedNode?.label];
      if (!existingNode) return;

      const newParent = parentNode?.label;
      const oldParent = existingNode.oldParentNode;
      const isNewNode = existingNode.isNewNode;

      if (isNewNode) {
        dispatch(
          updateTreeChanges({
            nodeLabel: selectedNode.label,
            changes: {
              ...existingNode,
              oldParentNode: newParent,
              newParentNode: null,
              isMoved: true,
            },
          })
        );
      } else if (oldParent === newParent) {
        dispatch(
          updateTreeChanges({
            nodeLabel: selectedNode.label,
            changes: {
              ...existingNode,
              oldParentNode: null,
              newParentNode: null,
              isMoved: false,
            },
          })
        );
      } else {
        dispatch(
          updateTreeChanges({
            nodeLabel: selectedNode.label,
            changes: {
              ...existingNode,
              newParentNode: newParent,
              isMoved: true,
            },
          })
        );
      }

      dispatch(setTreeData(reindexedTree));
      setSelectedNode(null);
      setOriginalParent(null);
      message.success(`Moved ${selectedNode.label} to ${parentNode.label}`);
    },
    [
      selectedNode,
      rawTreeData,
      treeChanges,
      dispatch,
      setSelectedNode,
      setOriginalParent,
      addToChangeLog,
    ]
  );


  const handleSelectTag = useCallback(
  (tag, parentNode) => {
    const updatedTree = removeTagFromNodeForMovement(
      [...rawTreeData],
      parentNode.id,
      tag
    );
    setOriginalParent(parentNode);

    const existingNode = treeChanges?.[parentNode.label] || {};
    const currentTags = existingNode?.tags || [];
    const currentReplaceTags = existingNode?.replacedTags || [];

    const isTagInTagList = currentTags?.includes(tag);

    const updatedTags = isTagInTagList
      ? currentTags.filter((t) => t !== tag)
      : currentTags;

    const updatedReplaceTags = isTagInTagList
      ? currentReplaceTags
      : [...new Set([...currentReplaceTags, tag])];

    dispatch(
      updateTreeChanges({
        nodeLabel: parentNode.label,
        changes: {
          tags: updatedTags,
          replacedTags: updatedReplaceTags,
        },
      })
    );

    dispatch(setTreeData(updatedTree));
    setSelectedTag({ tag, sourceNodeId: parentNode.id });
    message.info(`Moving tag ${tag} - select destination node`);
  },
  [rawTreeData, treeChanges, dispatch, setOriginalParent, setSelectedTag]
);

const handlePlaceTag = useCallback(
  (targetNode) => {
    if (!selectedTag) return;

    const updatedTree = addTagToNode(
      [...rawTreeData],
      targetNode.id,
      selectedTag.tag
    );

    addToChangeLog(
      `MOVE ${object}`,
      `${selectedTag?.tag} ${object} moved to ${targetNode.label}`
    );

    const existingTargetNode = treeChanges[targetNode.label] || {};
    const currentTags = existingTargetNode.tags || [];

    dispatch(
      updateTreeChanges({
        nodeLabel: targetNode.label,
        changes: {
          ...existingTargetNode,
          tags: [...new Set([...currentTags, selectedTag.tag])],
        },
      })
    );

    dispatch(setTreeData(updatedTree));
    setOriginalParent(null);
    setSelectedTag(null);

    message.success(
      `Moved ${object} ${selectedTag.tag} to ${targetNode.label}`
    );
  },
  [
    selectedTag,
    rawTreeData,
    object,
    addToChangeLog,
    treeChanges,
    dispatch,
    setOriginalParent,
    setSelectedTag,
  ]
);

 const handleCancelMoveNode = useCallback(() => {
    if (!selectedNode || !originalParent) return;

    // 1. Deep clone the current tree
    const updatedTree = JSON.parse(JSON.stringify(rawTreeData));

    // 2. Recursive restore function
    const restoreToExactPosition = (nodes) => {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].id === originalParent.id) {
          if (!nodes[i].child) nodes[i].child = [];

          let insertPosition = 0;
          while (
            insertPosition < nodes[i].child.length &&
            nodes[i].child[insertPosition].id.localeCompare(selectedNode.id) < 0
          ) {
            insertPosition++;
          }

          nodes[i].child.splice(insertPosition, 0, selectedNode);
          return true;
        }

        if (nodes[i].child && restoreToExactPosition(nodes[i].child)) {
          return true;
        }
      }
      return false;
    };

    const restorationComplete = restoreToExactPosition(updatedTree);

    if (restorationComplete) {
      dispatch(setTreeData(updatedTree));
      message.success(`${selectedNode.label} restored to exact original position`);
    } else {
      message.error("Could not restore node - parent structure changed");
    }

    setSelectedNode(null);
    setOriginalParent(null);
  }, [
    rawTreeData,
    selectedNode,
    originalParent,
    dispatch,
    setSelectedNode,
    setOriginalParent,
  ]);

    // Cancel tag move
  const handleCancelMoveTag = useCallback(() => {
    if (!selectedTag) return;

    const updatedTree = addTagToNode(
      [...rawTreeData],
      selectedTag.sourceNodeId,
      selectedTag.tag
    );

    dispatch(setTreeData(updatedTree));
    setSelectedTag(null);
    setOriginalParent(null);
  }, [rawTreeData, selectedTag, dispatch, setSelectedTag, setOriginalParent]);





  return {
    handleSelectNode,
    handlePlaceNode,
    handleSelectTag,
    handlePlaceTag,
    handleCancelMoveNode,
    handleCancelMoveTag
  };
};

export default useMoveOperation;
