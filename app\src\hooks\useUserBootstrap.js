import { useState } from "react";
import { useDispatch } from "react-redux";
import { setUserDetails, setRoles } from "../app/userManagementSlice";
import { doAjax } from "../components/Common/fetchService";
import { ERROR_MESSAGES, LOADING_MESSAGE } from "../constant/enum";
import { destination_MaterialMgmt, destination_IWA_NEW } from "../destinationVariables";
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "@hooks/useLogger";

const combineUserData = (userRes, rolesRes) => {
  const u = userRes?.data;
  const user = {
    id: u.userDetails.masterUserId,
    user_id: u.userDetails.businessEmailId,
    firstName: u.userDetails.firstName,
    lastName: u.userDetails.lastName,
    emailId: u.userDetails.businessEmailId,
    displayName: `${u.userDetails.firstName} ${u.userDetails.lastName}`,
    userName: u.userDetails.userName,
  };

  const roles = rolesRes?.data?.map((r) => r.roleName);

  return {
    ...user,
    roles,
  };
};

const useUserBootstrap = ({ fallbackUser, fallbackEntities, isLocalEnv }) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [moduleAccessStatus, setModuleAccessStatus] = useState("loading");
  const [finalEmailId, setFinalEmailId] = useState("");
  const { customError, log } = useLogger();

  const fetchAndDispatchUser = async () => {
    setLoading(true);
    setLoaderMessage(LOADING_MESSAGE.LOADING_USER);

    if (isLocalEnv) {
      dispatch(setUserDetails(fallbackUser));
      dispatch(setRoles(fallbackUser.roles || []));
      setFinalEmailId(fallbackUser.emailId);
      setModuleAccessStatus(true);
      setLoaderMessage("");
      setLoading(false);
    }

    let userRes = null;
    let rolesRes = null;
  let url_user=  isLocalEnv? `/${destination_MaterialMgmt}${END_POINTS.API.USER_DETAILS}`: `/${destination_IWA_NEW}${END_POINTS.API.USER_DETAILS_PROD}`;

    try {
      await new Promise((resolve, reject) => {
        doAjax(
          url_user,
          "get",
          (res) => {
            userRes = res;
            resolve();
          },
          isLocalEnv
            ? () => {
                resolve(); 
              }
            : reject
        );
      });

      const email = userRes?.data?.userDetails?.businessEmailId;
      const version = userRes?.data?.version ?? 1;
      let url_roles =isLocalEnv? `/${destination_MaterialMgmt}${END_POINTS.API.ROLES}?businessEmailId=${encodeURIComponent(email)}&userVersionNo=${version}&includeRoleDetails=false&iwaAppIds=MDG`: `/${destination_IWA_NEW}${END_POINTS.API.ROLES_PROD}?businessEmailId=${encodeURIComponent(email)}&userVersionNo=${version}&includeRoleDetails=false&iwaAppIds=MDG`;
      await new Promise((resolve, reject) => {
        doAjax(
          url_roles,
          "get",
          (res) => {
            rolesRes = res;
            resolve();
          },
          isLocalEnv
            ? () => {
                resolve();
              }
            : reject
        );
      });

      const finalUserData = combineUserData(userRes, rolesRes);
      dispatch(setUserDetails(finalUserData));
      dispatch(setRoles(finalUserData.roles));

      setFinalEmailId(finalUserData.emailId);
      setModuleAccessStatus(true);
    } catch (error) {
      customError(ERROR_MESSAGES?.ERROR_FETCHING_USER, error);
      if (isLocalEnv) {
        // In local env, fallback data is already set, just log the erro
      } else {
        setModuleAccessStatus(false);
        setTimeout(() => (window.location.href = "/do/logout"), 8000);
      }
    } finally {
      if (!isLocalEnv) {
        setLoaderMessage("");
        setLoading(false);
      }
    }
  };

  return {
    fetchAndDispatchUser,
    loading,
    loaderMessage,
    moduleAccessStatus,
    finalEmailId,
  };
};

export default useUserBootstrap;
