import {
  Autocomplete,
  Backdrop,
  BottomNavigation,
  Box,
  Button,
  CircularProgress,
  <PERSON>lapse,
  Dialog,
  Tooltip,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  MenuList,
  MenuItem,
  Popper,
  FormControlLabel,
  FormLabel,
  Grid,
  IconButton,
  ButtonGroup,
  Paper,
  Radio,
  RadioGroup,
  Stack,
  Step,
  StepLabel,
  StepButton,
  Stepper,
  Accordion,
  AccordionSummary,
  ClickAwayListener,
  AccordionDetails,
  SvgIcon,
  Tab,
  Tabs,
  TextField,
  Typography,
  tooltipClasses,
} from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { TreeView } from "@mui/x-tree-view/TreeView";
import { alpha, styled } from "@mui/material/styles";
import { TreeItem, treeItemClasses } from "@mui/x-tree-view/TreeItem";
import { useSpring, animated } from "@react-spring/web";
import React, { useEffect, useRef, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  iconButton_SpacingSmall,
  font_Small,
  button_Primary,
  button_Outlined,
  container_Padding,
  button_Marginleft,
  outermostContainer,
  container_filter,
  outermostContainer_Information,
  outerContainer_Information,
} from "../../../common/commonStyles";
import { useNavigate } from "react-router-dom";
import CloseIcon from "@mui/icons-material/Close";

import AddIcon from "@mui/icons-material/Add";
import GeneralInformationTabForProfitCenterET from "../../ProfitCenterTabs/GeneralInformationTabForProfitCenterET";
import ReusableAttachementAndComments from "../../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import dropDownDataSlice, {
  setDropDown,
} from "../../../../app/dropDownDataSlice";
import LoadingComponent from "../../../Common/LoadingComponent";
import { NavigateBefore, Refresh } from "@mui/icons-material";
import ReusableIcon from "../../../Common/ReusableIcon";
import { doAjax } from "../../../Common/fetchService";
import {
  destination_CostCenter,
  destination_GeneralLedger,
  destination_GeneralLedger_Mass,
  destination_IDM,
  destination_ProfitCenter,
} from "../../../../destinationVariables";
import { profitCenterReducer } from "../../../../app/profitCenterTabsSlice";
import { clearGeneralLedger, clearSingleGLPayloadGI, setHandleMassMode } from "../../../../app/generalLedgerTabSlice";
import AttachmentUploadDialog from "../../../Common/AttachmentUploadDialog";
import ReusableHierarchyTree from "../../../Common/ReusableHierarchyTree";
import { commonFilterUpdate, commonFilterClear, clearHierarchyGroup } from "../../../../app/commonFilterSlice";
import ReusableDialog from "../../../Common/ReusableDialog";
import { clearArtifactId, setAttachmentType } from "../../../../app/initialDataSlice";
import ReusablePreset from "../../../Common/ReusablePresetFilter";
import * as XLSX from 'xlsx';
import ReusableTable from "../../../Common/ReusableTable";
import ReusableBackDrop from "../../../Common/ReusableBackDrop";

const HierarchyNodeGeneralLedger = () => {
  let ref_elementForExport = useRef(null);
  const [openInitialDialog, setOpenInitialDialog] = useState(false);
  const anchorRefCreate = useRef(null);
  const anchorRefChange = useRef(null);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [openButtonCreate, setOpenButtonCreate] = useState(false);
  const [openButtonChange, setOpenButtonChange] = useState(false);
  const [scenarioState, setScenarioState] = useState("create");
  const [selectedIndexCreate, setSelectedIndexCreate] = useState(0);
  const [anchorEl, setAnchorEl] = useState(null);
  const [controllingArea, setControllingArea] = useState({code:"ETCA", desc:"ET FAMILY CO AREA"});
  const [costCenterGroup, setCostCenterGroup] = useState(null);
  const [chartOfAccount, setChartOfAccount] = useState({code:"ETCN", desc:"ET NATURAL / OPERATIONAL CHART OF ACCOUNTS"});
  const [openDownloadExcelDialog, setOpenDownloadExcelDialog] = useState(false);
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [costCenterGroupDescription, setCostCenterGroupDescription] = useState(null);
  const [isValidationErrorNode, setIsValidationErrorNode] = useState(false);
  const [isValidationErrorDesc, setIsValidationErrorDesc] = useState(false);
  const [isDuplicateDBDesc, setIsDuplicateDBDesc] = useState(false);
  const [isDuplicateNodeRequest, setIsDuplicateNodeRequest] = useState(false);
  const [bigTreeData,setBigTreeData] = useState([])
  const [isProceedDisabled, setIsProceedDisabled] = useState(true);
  const [isProceedCreateDisabled, setIsProceedCreateDisabled] = useState(true);

  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [parentNodeForObject, setParentNodeForObject] = useState("");
  console.log("scenarioState", scenarioState);
  const [newNodeList, setNewNodeList] = useState([]);
  const [newTagList, setNewTagList] = useState([]);
  const [newDescList, setNewDescList] = useState([]);
  const [newReplaceNodeList, setNewReplaceNodeList] = useState([]);
  const [newReplaceTagList, setNewReplaceTagList] = useState([]);
  const[newDescriptionAdded, setNewDescriptionAdded] = useState([])
  const[newAddedNode, setNewAddedNode] = useState([])
  const [moveNodeList,setMoveNodeList] = useState([])
  const [restoreMoveNode, setRestoreMoveNode] = useState([])
  const [removePCData,setRemovePCData] = useState([])
  const [removeNodeData, setRemoveNodeData] = useState([])
  const [storeMoveTagList,setStoreMoveTagList] = useState([])
  const [restoreMoveTagList,setRestoreMoveTagList] = useState([])
  const [changeDescription, setChangeDescription] = useState([]);

  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [downloadType, setDownloadType] = useState("systemGenerated");

  const [openDownloadChangeDialog, setOpenDownloadChangeDialog] = useState(false);

  const [initialNodeData, setInitialNodeData] = useState([
    {
      id: "1",
      title: "",
      child: [],
      tags: [],
      description: "Test",
      personResonsible:"test"
    },
]);
  const [isAccordionExpanded, setIsAccordionExpanded] = useState(
    initialNodeData.length === 0 || !initialNodeData[0]?.title // condition to check if no data
  );
  console.log("innnnnnn", initialNodeData)
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const ceSearchForm = useSelector(
    (state) => state.commonFilter["HierarchyNodeGeneralLedger"]
  );
  console.log("controlllign", controllingArea);
  const handleToggleCreate = () => {
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };
  const handleToggleChange = () => {
    setOpenButtonChange((prevOpen) => !prevOpen);
  };
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const dropDownData = useSelector((state) => state?.AllDropDown?.dropDown);

  const [openGlDialog, setOpenGlDialog] = useState(false);
  const [viewGlList, setViewGlList] = useState([]);
  console.log("viewGlList", viewGlList);
  const rowsForGlList = viewGlList.map((gl, index) => ({
    Id: index + 1, 
    glName: gl?.code,   
    glDescription: gl?.desc     
  }));
  const [range, setRange] = useState("");
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);



  const NoMaxWidthTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))({
    [`& .${tooltipClasses.tooltip}`]: {
      maxWidth: "none",
    },
  });


  const handleDownloadDialogClose = () => {
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
  }
  const handleDownloadChangeDialogClose = () => {
    setOpenDownloadChangeDialog(false);
    setDownloadType("systemGenerated");
  };
const handleDownloadTypeChange = (event) => {
  setDownloadType(event?.target?.value);
}
const handleMultipleDownloadTypeChange = (event) => {
  setDownloadType(event?.target?.value);
};
const onDownloadTypeChange = () => {
if(downloadType==="systemGenerated"){
  handleDownloadCreate();
  handleDownloadDialogClose()
}
if(downloadType==="mailGenerated"){
handleEmailDownload();
handleDownloadDialogClose();
}
}
const onMultipleDownloadTypeChange = () => {
  console.log("changedownloadtype", downloadType);
  if (downloadType === "systemGenerated") {
    handleChangeDownload();
    handleDownloadChangeDialogClose();
  }
  if (downloadType === "mailGenerated") {
    handleChangeDownloadEmail();
    handleDownloadChangeDialogClose();
  }
};



  const handleCloseGLDialog = () => {
    setOpenGlDialog(false);
    setPage(0);
    setPageSize(10);
  }
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleParentNodeSearch = () => {
    if (searchValue.length === 6) {
      getParentNode(searchValue);
      setErrorMessage("")
    } else {
      setErrorMessage("General Ledger needs to be of length 6");
    }
  }

  const getParentNode = (value) => {
          const secondPayload = {
            "chartOfAccount":  ceSearchForm?.chartOfAccount?.code,
            "hierarchy":  ceSearchForm?.costElementGroup?.code,
            "glList": [value]
          };
          const secondApiSuccess = (secondData) => {
            if (secondData?.statusCode == "200" && secondData?.body && secondData?.body.length > 0) {
              console.log("herrr")
              setParentNodeForObject(secondData?.body[0]?.Node);
              setErrorMessage("");
            } else {
              setParentNodeForObject('');
              setErrorMessage("No parent node found.");
            }
          }
          const secondApiError = (error) => {
            // setNodeData('');
            setErrorMessage('Error fetching parent node.');
          }
          doAjax(
            `${destination_GeneralLedger}/node/fetchParentNodeForObject`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondPayload
          )
      
  };

  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
  };

  const getChartOfAccount = (value) => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "COA",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getChartOfAccounts`,
      "get",
      hSuccess,
      hError
    );
  };
  const getControllingArea = (value) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "COAREA",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getControllingArea`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCostCenterGroup = (value) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "costElementGroup",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/node/getZeroLevelNodes?coa=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleMassModeGL = useSelector(
    (state) => state.generalLedger.handleMassMode
  );

  const getHeirarchyNodeTreeStructure = () => {
    setIsLoading(true);
    var payload = {
      coa: ceSearchForm?.chartOfAccount?.code === "" ? "ETCN" : ceSearchForm?.chartOfAccount?.code,
      node: ceSearchForm?.costElementGroup?.code === "" ? "AL-ALL" : ceSearchForm?.costElementGroup?.code,
      controllingArea: ceSearchForm?.controllingArea?.code === "" ? "ETCA" : ceSearchForm?.controllingArea?.code,
      classValue: "0102",
      id: "",
      screenName: "Display",
    };
    const hSuccess = (data) => {
      console.log(data.body, "data");
      let innerData = [];
      if (data.statusCode === 200) {
        innerData.push(data.body.hierarchyTree);
        setInitialNodeData(innerData);
      }
      setInitialNodeData(innerData);
      setIsLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/displayHierarchyTreeNodeStructure`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  
  const getFilter = () => {
    setIsAccordionExpanded(false);
    setIsLoading(true);
    var payload = {
      coa: ceSearchForm?.chartOfAccount?.code === "" ? "" : ceSearchForm?.chartOfAccount?.code,
      node: ceSearchForm?.costElementGroup?.code === "" ? "" : ceSearchForm?.costElementGroup?.code,
      controllingArea: ceSearchForm?.controllingArea?.code === "" ? "" : ceSearchForm?.controllingArea?.code,
      classValue: "0102",
      id: "",
      screenName: "Display",
    };
    const hSuccess = (data) => {
      console.log(data.body, "data");
      let innerData = [];
      if (data.statusCode === 200) {
        innerData.push(data.body.hierarchyTree);
        setInitialNodeData(innerData);
      }
      setInitialNodeData(innerData);
      setIsLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/displayHierarchyTreeNodeStructure`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const refreshPage = () => {
    dispatch(commonFilterClear({ module: "HierarchyNodeGeneralLedger" }));
    // reloadDisplay();
    setInitialNodeData([{}]);
    setIsAccordionExpanded(true);
  }
  const reloadDisplay = () => {
    setIsLoading(true);
    var payload = {
      coa: "ETCN",
      node: "AL-ALL",
      controllingArea: "ETCA",
      classValue: "0102",
      id: "",
      screenName: "Display",
    };
    const hSuccess = (data) => {
      console.log(data.body, "data");
      let innerData = [];
      if (data.statusCode === 200) {
        innerData.push(data.body.hierarchyTree);
        setInitialNodeData(innerData);
      }
      setInitialNodeData(innerData);
      setIsLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/displayHierarchyTreeNodeStructure`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  useEffect(() => {
    getChartOfAccount();
    getControllingArea();
    // getHeirarchyNodeTreeStructure();
    getCostElementGroup("ETCN");
    getCostElementGroupFilter("ETCN");
    getNewControllingArea();
    getNewChartOfAccounts();
    dispatch(clearArtifactId());
    dispatch(clearGeneralLedger());
    dispatch(clearSingleGLPayloadGI());
    dispatch(clearHierarchyGroup({module: 'HierarchyNodeGeneralLedger', groupName: 'costElementGroup'}));
    // dispatch(commonFilterClear({ module: "HierarchyNodeGeneralLedger" }));
  }, []);

  const getBigTreeData = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_BIG_HIERARCHY_NODES_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [{ "MDG_CONDITIONS.MDG_MODULE":  "CE Hierarchy Group"  }],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    

    const hSuccess = (data) => {
      // debugger
      if (data.statusCode === 200) {
        var tempArr = []
        data?.data?.result?.[0]?.MDG_BIG_HIERARCHY_NODES_ACTION_TYPE?.map((item)=>{
          tempArr.push(item?.MDG_HIERARCHY_BIG_NODES)
        })
        setBigTreeData(tempArr)
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  useEffect(()=>{
    getBigTreeData()
  },[])
  const handleDownloadCreate = async () => {
    setLoaderMessage("Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience.")
    setBlurLoading(true);
    let hSuccess = (response) => {
      if(response?.Blob?.size!==0 || response?.Blob?.type!==""){
      setBlurLoading(false);
      setLoaderMessage("");
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `CEG Mass Create.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
      setMessageDialogMessage(
        `General Ledger Hierarchy_Mass Create.xlsx has been downloaded successfully`
      );
      setMessageDialogSeverity("success");
    }
    else{
      setBlurLoading(false);
        setLoaderMessage(false);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          "Please try again."
        );
        //setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
    }
    };
    let hError = (error) => {
      setBlurLoading(false);
      setLoaderMessage("");
      if (error) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>
 `);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/excel/downloadHierarchyNodeTemplate`,
      "getblobfile",
      hSuccess,
      hError
      // downloadPayload
    );
  };
  const handleEmailDownload = async () => {
    // setLoaderMessage("Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience.")
    setBlurLoading(true);
    let hSuccess = (response) => {
      setBlurLoading(false);
      setLoaderMessage("");
      // const href = URL.createObjectURL(response);
      // const link = document.createElement("a");
      // link.href = href;
      // link.setAttribute("download", `CEG Mass Create.xlsx`);
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);
      // URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
     setMessageDialogMessage(`Download has been started. You will get the Excel file via email`);
      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      setBlurLoading(false);
      setLoaderMessage("");
      if (error) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>
 `);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/excel/downloadHierarchyNodeTemplateInMail`,
      "get",
      hSuccess,
      hError
      // downloadPayload
    );
  };

  const getNewControllingArea = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_CUSTOM_DROPDOWN_LIST",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MODULE": "CEG",
          "MDG_CONDITIONS.MDG_FIELD_NAME": "Controlling Area"
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        const lookupData =
          data?.data?.result[0]?.MDG_CUSTOM_LOOKUP_ACTION_TYPE || [];
        console.log("questionData", lookupData);

        let lookupDataArr = []
        lookupData?.map((itemData) => {
          let lookupDataHash = {}
          lookupDataHash["code"] = itemData?.MDG_LOOKUP_CODE
          lookupDataHash["desc"] = itemData?.MDG_LOOKUP_DESC
          lookupDataArr.push(lookupDataHash)
        })
        console.log(lookupDataArr, "lookupDataArr")

        dispatch(setDropDown({ keyName: "NewControllingArea", data: lookupDataArr }));
        // setQuestions(questionsData);
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  }

  const getNewChartOfAccounts = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_CUSTOM_DROPDOWN_LIST",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MODULE": "CEG",
          "MDG_CONDITIONS.MDG_FIELD_NAME": "Chart Of Accounts"
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        const lookupData =
          data?.data?.result[0]?.MDG_CUSTOM_LOOKUP_ACTION_TYPE || [];
        console.log("questionData", lookupData);

        let lookupDataArr = []
        lookupData?.map((itemData) => {
          let lookupDataHash = {}
          lookupDataHash["code"] = itemData?.MDG_LOOKUP_CODE
          lookupDataHash["desc"] = itemData?.MDG_LOOKUP_DESC
          lookupDataArr.push(lookupDataHash)
        })
        console.log(lookupDataArr, "lookupDataArr")
        lookupDataArr.sort((a, b) => a.code.localeCompare(b.code));

        console.log(lookupDataArr, "lookupDataArr2");

        dispatch(setDropDown({ keyName: "NewChartOfAccounts", data: lookupDataArr }));
        // setQuestions(questionsData);
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  }

  const handleChartOfAccountSearch = (e, value) => {
    if(true) {
      var tempCoa = value;

      let tempFilterData = {
        ...ceSearchForm,
        chartOfAccount: tempCoa,
      };
      dispatch(
        commonFilterUpdate({
          module: "HierarchyNodeGeneralLedger",
          filterData: tempFilterData,
        })
      );
      console.log("tttt", tempFilterData)
    }
    // getCostElementGroup(value.code);
    getCostElementGroupFilter(value.code);
  }

  const handleControllingAreaSearch = (e, value) => {
    if (true) {
      var tempControllingArea = value;
      console.log("ccc", value)

      let tempFilterData = {
        ...ceSearchForm,
        controllingArea: tempControllingArea,
      };
      dispatch(
        commonFilterUpdate({
          module: "HierarchyNodeGeneralLedger",
          filterData: tempFilterData,
        })
      );
      console.log("tttt", tempFilterData)
    }
    // getCostCenterGroup(value.code);
  };
  const handleCostElementGroupSearch = (e, value) => {
    if (true) {
      var tempCeg = value;
      console.log("ccc", value)

      let tempFilterData = {
        ...ceSearchForm,
        costElementGroup: tempCeg,
      };
      dispatch(
        commonFilterUpdate({
          module: "HierarchyNodeGeneralLedger",
          filterData: tempFilterData,
        })
      );
      console.log("tttt", tempFilterData)
    }
    // getCostCenterGroup(value.code);
  };

  const handleAccordionToggle = () => {
    setIsAccordionExpanded(!isAccordionExpanded);
  };

  const handleClear = () => {
    dispatch(commonFilterClear({ module: "HierarchyNodeGeneralLedger" }));
  };
  
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const uploadExcel = (file) => {
    console.log(file);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    if (handleMassModeGL === "Change") {
      var uploadUrl = `/${destination_GeneralLedger_Mass}/massAction/getAllHierarchyNodeFromExcelForChange`;
      setEnableDocumentUpload(false);
      
      setBlurLoading(true);
      // setTimeout(() => {
      //   setBlurLoading(false);
      //   const content = (
      //     <Typography component="div">
      //       <ul>
      //         <li>Mass Upload Process has Started in the background. As soon as the request ID is generated, you will receive a notification and mail for it containing the new request ID number.</li>
      //         <li>Then you can visit the Request Bench Tab and search for that request ID and do further actions on it.</li>
      //         <li>Note - All request IDs generated in the background would initially have the status Draft.</li>
      //       </ul>
      //     </Typography>
      //   );
      //   handleMessageDialogClickOpen();
      //   setMessageDialogTitle("Header - Information");
      //   setMessageDialogMessage(content);
      //   setMessageDialogSeverity("success");
      // }, 4000);
      const hSuccess = (data) => {
        // console.log(data, "example");
        console.log(data?.body,"data?.body")
        setIsLoading();
        if (data.statusCode === 200) {
          // setEnableDocumentUpload(false);
          setBlurLoading(false);
          setLoaderMessage("");
          console.log("success200create");
    //       // dispatch(setControllingArea(data?.body?.controllingArea));
    //       dispatch(setMultipleProfitCenterData(data?.body));
    //       setMessageDialogTitle("Change");
    //       setMessageDialogMessage(`${file.name} has been Uploaded Succesfully`);
    //       setMessageDialogSeverity("success");
    //       setMessageDialogOK(false);
    //       setsuccessMsg(true);
    //       handleSnackBarOpen();
    //       setMessageDialogExtra(true);
    //       setIsLoading(false);
          const content = (
            <Typography component="div">
              <ul>
                <li>Mass Upload Process has Started in the background. As soon as the request ID is generated, you will receive a notification and mail for it containing the new request ID number.</li>
                <li>Then you can visit the Request Bench Tab and search for that request ID and do further actions on it.</li>
                <li>Note - All request IDs generated in the background would initially have the status Draft.</li>
              </ul>
            </Typography>
          );
          setMessageDialogTitle("Header - Information");
          setMessageDialogMessage(content);
          setMessageDialogSeverity("success");
          handleMessageDialogClickOpen();
    //       navigate(`/masterDataCockpit/profitCenter/changeMultiplePC`);
        }
        else if (data.statusCode === 429) {
          // Handling status code 429 (Too Many Requests)
          setBlurLoading(false);
          
          setMessageDialogTitle("Error");
          setLoaderMessage("");
          // setsuccessMsg(false);
          setEnableDocumentUpload(false);
          setMessageDialogMessage(data?.message || "Too many requests. Please try again later.");
          setMessageDialogSeverity("danger");
          // setMessageDialogOK(false);
          // setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
        }
         else {
          setEnableDocumentUpload(false);
          setBlurLoading(false);
          setLoaderMessage("");
          console.log("success200failcreate")
          setMessageDialogTitle("Error");    
          setMessageDialogMessage("Upload failed. Incorrect template tab name, please recheck upload file");
          setMessageDialogSeverity("danger");
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
    //     handleClose();
      };
      const hError = (error) => {
        console.log(error);
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>`);
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
      };
      doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
    } else {
      var uploadUrl = `/${destination_GeneralLedger_Mass}/massAction/getAllHierarchyNodeFromExcel`;
      setEnableDocumentUpload(false);
      setBlurLoading(true);
      const hSuccess = (data) => {
        if (data.statusCode === 200) {
          setBlurLoading(false);
          setLoaderMessage("");
          console.log("success200create");
          const content = (
            <Typography component="div">
              <ul>
                <li>Mass Upload Process has Started in the background. As soon as the request ID is generated, you will receive a notification and mail for it containing the new request ID number.</li>
                <li>Then you can visit the Request Bench Tab and search for that request ID and do further actions on it.</li>
                <li>Note - All request IDs generated in the background would initially have the status Draft.</li>
              </ul>
            </Typography>
          );
          setMessageDialogTitle("Header - Information");
          setMessageDialogMessage(content);
          setMessageDialogSeverity("success");
          handleMessageDialogClickOpen();
        }
        else if (data.statusCode === 429) {
          setBlurLoading(false);
          setMessageDialogTitle("Error");
          setLoaderMessage("");
          // setsuccessMsg(false);
          setEnableDocumentUpload(false);
          setMessageDialogMessage(data?.message || "Too many requests. Please try again later.");
          setMessageDialogSeverity("danger");
          handleMessageDialogClickOpen();
        }
         else {
          setEnableDocumentUpload(false);
          setBlurLoading(false);
          setLoaderMessage("");
          console.log("success200failcreate")
          setMessageDialogTitle("Error");
          // setsuccessMsg(false);
          setMessageDialogMessage("Upload failed. Incorrect template tab name, please recheck upload file");
          setMessageDialogSeverity("danger");
          // setMessageDialogOK(false);
          // setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
    //     handleClose();
      };
      const hError = (error) => {
        console.log(error);
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`);
        setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // setDialogOkText("OK");
        handleMessageDialogClickOpen();
      };
      doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
    }

    // navigate(
    //   "/masterDataCockpit/et/hierarchyNodeProfitCenter/createMassHierarchyNode"
    // );
  };

  const handleCreateMultiple = () => {
    setEnableDocumentUpload(true);
    dispatch(setHandleMassMode("Create"));
  };

  const handleClickCreate = (option, index) => {
    if (index !== 0) {
      setSelectedIndexCreate(index);
      setOpenButtonCreate(false);
      if (index === 1) {
        // setEnableDocumentUpload(true);
        handleCreateMultiple();
      } else if (index === 2) {
        // handleDownloadCreate();
        setOpenDownloadDialog(true);
      }
    }
  };
  const handleChangeMultiple = () => {
    setEnableDocumentUpload(true);
    dispatch(setHandleMassMode("Change"));
  };
  const handleChangeDownload = () => {
    // setIsLoading(true);
    setLoaderMessage("Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience.")
    setBlurLoading(true);
    let hSuccess = (response) => {
      if(response?.Blob?.size!==0 || response?.Blob?.type!==""){
      // setIsLoading(false);
      setBlurLoading(false);
      setLoaderMessage("");
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
  
      link.href = href;
      link.setAttribute("download", `CEG Mass Change.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
  
      setMessageDialogMessage(
        `General Ledger_Hierarchy_Mass Change.xlsx has been downloaded successfully`
      );
  
      setMessageDialogSeverity("success");
    }
    else{
      setBlurLoading(false);
        setLoaderMessage(false);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          "Please try again."
        );
        //setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
    }
    };
  
    let hError = (error) => {
      // setIsLoading(false);
      setBlurLoading(false);
      setLoaderMessage("");
      if (error) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>
 `);
        setMessageDialogSeverity("danger");
      }
    };
  
    doAjax(
      `/${destination_GeneralLedger_Mass}/excel/downloadHierarchyNodeTemplateForChange`,
      "getblobfile",
      hSuccess,
      hError
    );
  };
  const handleChangeDownloadEmail = () => {
    // setIsLoading(true);
    // setLoaderMessage("Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience.")
    setBlurLoading(true);
    let hSuccess = (response) => {
      // setIsLoading(false);
      setBlurLoading(false);
      setLoaderMessage("");
      // const href = URL.createObjectURL(response);
      // const link = document.createElement("a");
  
      // link.href = href;
      // link.setAttribute("download", `CEG Mass Change.xlsx`);
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);
      // URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
  
     setMessageDialogMessage(`Download has been started. You will get the Excel file via email`);
  
      setMessageDialogSeverity("success");
    };
  
    let hError = (error) => {
      // setIsLoading(false);
      setBlurLoading(false);
      setLoaderMessage("");
      if (error) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>
 `);
        setMessageDialogSeverity("danger");
      }
    };
  
    doAjax(
      `/${destination_GeneralLedger_Mass}/excel/downloadHierarchyNodeTemplateForChangeInMail`,
      "get",
      hSuccess,
      hError
    );
  };
  const handleClickChange = (option, index) => {
    if (index !== 0) {
      setSelectedIndexCreate(index);
      setOpenButtonCreate(false);
      if (index === 1) {
        // handleCreateSingleWithCopy();
        handleChangeMultiple();
      } else if (index === 2) {
        // handleCreateSingleWithoutCopy();
        // handleChangeDownload();
        setOpenDownloadChangeDialog(true)
      }
    }
  };

  const handleCloseButtonCreate = (event) => {
    if (
      anchorRefCreate.current &&
      anchorRefCreate.current.contains(event.target)
    ) {
      return;
    }
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };
  const handleCloseButtonChange = (event) => {
    if (
      anchorRefChange.current &&
      anchorRefChange.current.contains(event.target)
    ) {
      return;
    }
    setOpenButtonChange((prevOpen) => !prevOpen);
  };
  const optionsCreateMultiple = [
    "Create Multiple",
    "Upload Template ",
    "Download Template ",
  ];
  const optionsChangeMultiple = [
    "Change Multiple",
    "Upload Template ",
    "Download Template ",
  ];
  const steps = [
    "GENERAL INFORMATION",
    "HIERARCHY NODE",
    "ATTACHMENTS & COMMENTS",
  ];

  const handleCloseInitialDialog = () => {
    setOpenInitialDialog(false);
    setScenarioState("create");
    // setChartOfAccount(null);
    // setControllingArea(null);
    setCostCenterGroup(null);
    setCostCenterGroupDescription(null);
    setIsValidationErrorNode(false);
    setIsValidationErrorDesc(false);
    setIsDuplicateDBDesc(false)
    setIsDuplicateNodeRequest(false);
  };
  const handleDownloadExcelDialogClose = () => {
    setOpenDownloadExcelDialog(false);
  };

  const navigateData = {
    controllingArea,
    costCenterGroup,
    chartOfAccount,
    costCenterGroupDescription,
    scenarioState,
  };

  console.log("nvd", navigateData)

  // const handleInitialDialogSubmit = () => {
  //   navigate(
  //     "/masterDataCockpit/groupNode/hierarchyNodeGeneralLedger/newHierarchyGroup",
  //     {
  //       state: navigateData,
  //     }
  //   );
  // };

  useEffect(() => {
    setChartOfAccount({code:"ETCN", desc:"ET NATURAL / OPERATIONAL CHART OF ACCOUNTS"});
    setControllingArea({code:"ETCA", desc:"ET FAMILY CO AREA"});
    setCostCenterGroup(null);
    setCostCenterGroupDescription(null);
    setIsValidationErrorNode(false);
    setIsValidationErrorDesc(false);
    setIsDuplicateDBDesc(false)
    setIsDuplicateNodeRequest(false);
  }, [scenarioState]);
  
  useEffect(() => {
    if (navigateData?.scenarioState === 'create') {
      const isDisabled = 
        // !navigateData?.chartOfAccount?.code || 
        // !navigateData?.controllingArea?.code || 
        !navigateData?.costCenterGroup?.code || 
        !navigateData?.costCenterGroupDescription?.desc;
      setIsProceedCreateDisabled(isDisabled);
    } else {
      const isDisabled = 
        // !navigateData?.chartOfAccount?.code || 
        // !navigateData?.controllingArea?.code || 
        !navigateData?.costCenterGroup?.code;
      setIsProceedDisabled(isDisabled);
    }
  }, [navigateData?.costCenterGroup?.code, navigateData?.costCenterGroupDescription?.desc, navigateData?.scenarioState]);

  const handleInitialDialogSubmit = () => {
    if(navigateData?.scenarioState ==='create'){
    var payload = {
      requestId: "",
      chartOfAccount: navigateData?.chartOfAccount?.code,
      hierarchyGrp: "",
      node: navigateData?.costCenterGroup?.code,
    };
    const hSuccess = (data) => {
      setBlurLoading(false);
      console.log("dupli", data);

      if (
        data?.body?.PresentInHier === "X" ||
        data?.body?.PresentInCOA === "X" || data?.body?.isDbDuplicate){
          setIsValidationErrorNode(true)
          setIsValidationErrorDesc(false)
          setIsDuplicateDBDesc(false)
          console.log('CHECKERROR')
      } else {

        var payloadForDesc = {
          requestId: "",
          classValue: "0102",
          chartOfAccount: navigateData?.chartOfAccount?.code,
          desc: navigateData?.costCenterGroupDescription?.desc,
        };
        const hSuccessDesc = (data) => {
          setBlurLoading(false);
          console.log("dupli", data);
    
          if(Object.keys(data.body).length != 0 && data?.body?.isDbDuplicate === true){
            setIsValidationErrorNode(false);
            setIsValidationErrorDesc(false);
            setIsDuplicateDBDesc(true)
            console.log("CHECKERROR");
          }
          else if(Object.keys(data.body).length != 0){
            setIsValidationErrorNode(false);
            setIsValidationErrorDesc(true);
            setIsDuplicateDBDesc(false)
            console.log("CHECKERROR");
          } else {
            navigate(
              "/masterDataCockpit/groupNode/hierarchyNodeGeneralLedger/newHierarchyGroup",
              {
                state: navigateData,
              }
            )
          }
        }
        const hErrorDesc = () =>{

        }

        doAjax(
          `/${destination_GeneralLedger}/node/descDuplicacyCheckForCEG`,
          "post",
          hSuccessDesc,
          hErrorDesc, 
          payloadForDesc
        );

      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/node/nodeDuplicacyCheckForCEG`,
      "post",
      hSuccess,
      hError, 
      payload
    );
  }
  else {
      var payload = {
      requestId: "",
      chartOfAccount: navigateData?.chartOfAccount?.code,
      hierarchyGrp: "",
      node: navigateData?.costCenterGroup?.code,
      };
    const hSuccess = (data) => {
      setBlurLoading(false);
      console.log("dupli", data);

      if (data?.body?.isDbDuplicate){
        setIsDuplicateNodeRequest(true);
        console.log('CHECKERROR')
      } else {
        navigate(
          "/masterDataCockpit/groupNode/hierarchyNodeGeneralLedger/newHierarchyGroup",
          {
            state: navigateData,
          }
        );
      }
    }
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_GeneralLedger}/node/nodeDuplicacyCheckForCEG`,
        "post",
        hSuccess,
        hError, 
        payload
      );
  }
  };

  const handleDownloadExcelDialogSubmit = () => {};

  const handleControllingArea = () => {};

  const [showInputT, setShowInput] = useState(false);
  const updateTreeData = (updatedState) => {
    setData(updatedState);
  };

  const updateNodeList = (updatedState) => {
    setNewNodeList(updatedState);
  };
  const updatePCList = (updatedState) => {
    setNewTagList(updatedState);
  };
  const updateDescList = (updatedState) => {
    setNewDescList(updatedState);
  };
  const updateReplaceNodeList = (updatedState) => {
    setNewReplaceNodeList(updatedState);
  };

  const updateReplaceTagList = (updatedState) => {
    setNewReplaceTagList(updatedState);
  };



  const updateChangeDescList = (updatedState) => {
    setNewDescriptionAdded(updatedState);
  };

  const updatedNewNodeList = (updatedState) => {
    setNewAddedNode(updatedState);
  };

  const updateMoveNodeList = (updatedState) => {
    setMoveNodeList(updatedState);
  };
  const updateRestoreMoveNode = (updatedState) => {
    setRestoreMoveNode(updatedState);
  };

  const updateRemovePCData = (updatedState) => {
    setRemovePCData(updatedState);
  };
  const updateRemoveNodeData = (updatedState) => {
    setRemoveNodeData(updatedState);
  };

  const updateStoreMoveTagList = (updatedState) => {
    setStoreMoveTagList(updatedState);
  };
  const updateRestoreMoveTag = (updatedState) => {
    setRestoreMoveTagList(updatedState);
  };
  const updateChangedDescription = (updatedState) => {
    setChangeDescription(updatedState);
  };

  const handleUpdate = (ref, data) => {
    switch (ref) {
      case "UPDATEMOVENODE":
        console.log("insidemodenode");
        updateMoveNodeList(data);
        break;

      case "RESTOREMOVENODE":
        updateRestoreMoveNode(data);
        break;

      case "REMOVEPCDATA":
        updateRemovePCData(data);
        break;

      case "REMOVENODEDATA":
        updateRemoveNodeData(data);
        break;

      case "UPDATEMOVENODE":
        updateMoveNodeList(data);
        break;

      case "STOREMOVETAGLIST":
        updateStoreMoveTagList(data);
        break;

      case "RESTOREMOVETAGLIST":
        updateRestoreMoveTag(data);
        break;

      case "CHANGEDESCRIPTION":
        updateChangedDescription(data);
        break;

      default:
        return null;
    }
  };

  const columnsForGlList = [
    { field: "glName", headerName: "GL Account", flex: 1 },
    { field: "glDescription", headerName: "GL Description", flex: 1 },
    // Add more columns if needed later
  ];
  
  const getCostElementGroup = (value) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "CostElementGroup",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/node/getZeroLevelNodes?chartOfAccount=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCostElementGroupFilter = (value) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "CostElementGroupFilter",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_GeneralLedger}/data/getCostEleGroup?coa=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleDownloadGLRange = () => {
    const workbook = XLSX.utils.book_new();
    
    const worksheetData = rowsForGlList.map(row => {
      const formattedRow = {};
      columnsForGlList.forEach(column => {
        const field = column.field;
        const headerName = column.headerName || field; // Default to field name if headerName is not defined
        formattedRow[headerName] = row[field];
      });
      return formattedRow;
    });
  
    const worksheet = XLSX.utils.json_to_sheet(worksheetData);
  
    const columnWidths = columnsForGlList.map(column => ({
      wch: Math.max(column.headerName.length, 10) 
    }));

    worksheet['!cols'] = columnWidths;

    XLSX.utils.book_append_sheet(workbook, worksheet, 'General Ledgers');
  
    XLSX.writeFile(workbook, 'List of General Ledgers.xlsx');
  };

  return (
    <>
      {isLoading === true ? (
          <LoadingComponent />
        ) : (
      <div ref={ref_elementForExport}>
        <ReusableDialog
          dialogState={openMessageDialog}
          openReusableDialog={handleMessageDialogClickOpen}
          closeReusableDialog={handleMessageDialogClose}
          dialogTitle={messageDialogTitle}
          dialogMessage={messageDialogMessage}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          // handleExtraButton={handleMessageDialogNavigate}
          dialogSeverity={messageDialogSeverity}
        />
        <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
          <Stack spacing={1}>
            {enableDocumentUpload && (
              <AttachmentUploadDialog
                artifactId=""
                artifactName=""
                setOpen={setEnableDocumentUpload}
                handleUpload={uploadExcel}
              />
            )}

            <Dialog
              hideBackdrop={false}
              elevation={2}
              PaperProps={{
                sx: { boxShadow: "none" },
              }}
              open={openInitialDialog}
              onClose={handleCloseInitialDialog}
            >
              <DialogTitle
                sx={{
                  justifyContent: "space-between",
                  alignItems: "center",
                  height: "max-content",
                  padding: ".5rem",
                  paddingLeft: "1rem",
                  backgroundColor: "#EAE9FF40",
                  display: "flex",
                }}
              >
                <Typography variant="h6">Please Select</Typography>
                <IconButton
                  sx={{ width: "max-content" }}
                  onClick={handleCloseInitialDialog}
                  children={<CloseIcon />}
                />
              </DialogTitle>
              <DialogContent sx={{ padding: ".5rem 1rem" }}>
                <Stack>
                  <Box sx={{ minWidth: 400 }}>
                    <FormControl>
                      <FormLabel
                        id="demo-radio-buttons-group-label"
                        sx={{
                          fontSize: "12px",

                          fontWeight: "700",
                        }}
                      >
                        {" "}
                      </FormLabel>

                      <RadioGroup
                        aria-labelledby="demo-radio-buttons-group-label"
                        defaultValue={scenarioState}
                        name="radio-buttons-group"
                        row
                        onChange={(e) => setScenarioState(e.target.value)}
                      >
                        <FormControlLabel
                          value="create"
                          control={<Radio />}
                          label="Create"
                        />
                        <FormControlLabel
                          value="change"
                          control={<Radio />}
                          label="Change"
                        />
                      </RadioGroup>
                    </FormControl>

                    <Grid container spacing={1}>
                      {scenarioState === "create" ? (
                        <>
                          <Grid
                            item
                            md={6}
                            sx={{ width: "100%", marginTop: ".5rem" }}
                          >
                            <Typography>
                              Chart Of Account
                              <span style={{ color: "red" }}>*</span>
                            </Typography>

                            <FormControl
                              fullWidth
                              sx={{ margin: ".5em 0px", minWidth: "250px" }}
                            >
                              <Autocomplete
                                sx={{ height: "31px" }}
                                fullWidth
                                size="small"
                                value={chartOfAccount ?? {code:"ETCN", desc:"ET NATURAL / OPERATIONAL CHART OF ACCOUNTS"}}
                                options={dropDownData?.NewChartOfAccounts ?? []}
                                disableClearable
                                onChange={(e, value) => {
                                  setChartOfAccount(value);
                                  getCostElementGroup(value?.code);
                                }}
                                getOptionLabel={(option) => {
                                  if (option?.code)
                                    return (
                                      `${option?.code}-${option?.desc}` ?? ""
                                    );
                                  else return "";
                                }}
                                renderOption={(props, option) => (
                                  <li {...props}>
                                    <Typography style={{ fontSize: 12 }}>
                                    {option?.desc ? (
                                    <>
                                      <strong>{option.code}</strong> -{" "}
                                      {option.desc}
                                    </>
                                  ) : (
                                    <strong>{option.code}</strong>
                                  )}
                                    </Typography>
                                  </li>
                                )}
                                renderInput={(params) => (
                                  <TextField
                                    sx={{ fontSize: "12px !important" }}
                                    {...params}
                                    variant="outlined"
                                    placeholder="SELECT CHART OF ACCOUNT"

                                    // error={controllingAreaValid}
                                  />
                                )}
                              />
                            </FormControl>
                          </Grid>
                          <Grid
                            item
                            md={6}
                            sx={{ width: "100%", marginTop: ".5rem" }}
                          >
                            <Typography>
                              Controlling Area
                              <span style={{ color: "red" }}>*</span>
                            </Typography>

                            <FormControl
                              fullWidth
                              sx={{ margin: ".5em 0px", minWidth: "250px" }}
                            >
                              <Autocomplete
                                sx={{ height: "31px" }}
                                fullWidth
                                size="small"
                                value={controllingArea ?? {code:"ETCA", desc:"ET FAMILY CO AREA"}}
                                options={dropDownData?.NewControllingArea ?? []}
                                disableClearable
                                onChange={(e, value) => {
                                  setIsValidationErrorNode(false)
                                  setIsValidationErrorDesc(false)
                                  setControllingArea(value);
                                  // getCostCenterGroup(value.code);
                                }}
                                getOptionLabel={(option) => {
                                  if (option?.code)
                                    return (
                                      `${option?.code}-${option?.desc}` ?? ""
                                    );
                                  else return "";
                                }}
                                renderOption={(props, option) => (
                                  <li {...props}>
                                    <Typography style={{ fontSize: 12 }}>
                                    {option?.desc ? (
                                      <>
                                        <strong>{option.code}</strong> -{" "}
                                        {option.desc}
                                      </>
                                    ) : (
                                      <strong>{option.code}</strong>
                                    )}
                                    </Typography>
                                  </li>
                                )}
                                renderInput={(params) => (
                                  <TextField
                                    sx={{ fontSize: "12px !important" }}
                                    {...params}
                                    variant="outlined"
                                    placeholder="SELECT CONTROLLING AREA"

                                    // error={controllingAreaValid}
                                  />
                                )}
                              />
                            </FormControl>
                          </Grid>

                          <Grid
                            item
                            md={6}
                            sx={{ width: "100%", marginTop: ".5rem" }}
                          >
                            <Typography>
                              Cost Element Group
                              <span style={{ color: "red" }}>*</span>
                            </Typography>

                            <FormControl
                              fullWidth
                              sx={{ margin: ".5em 0px", minWidth: "250px" }}
                            >
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                fullWidth
                                size="small"
                                value={costCenterGroup?.code ?? ""}
                                inputProps={{maxLength: 10}}
                                onChange={(e, value) => {
                                  setIsValidationErrorNode(false)
                                  setIsValidationErrorDesc(false)
                                  console.log("pctrgroup", e.target.value);
                                  setCostCenterGroup({
                                    code: e.target.value?.replace(/[^a-zA-Z0-9_\/-]/g, "")?.toUpperCase(),
                                    desc: "",
                                  });
                                }}
                                placeholder="ENTER COST ELEMENT GROUP"
                              />
                            </FormControl>
                          </Grid>

                          <Grid
                            item
                            md={6}
                            sx={{ width: "100%", marginTop: ".5rem" }}
                          >
                            <Typography>
                              Cost Element Group Description
                              <span style={{ color: "red" }}>*</span>
                            </Typography>

                            <FormControl
                              fullWidth
                              sx={{ margin: ".5em 0px", minWidth: "250px" }}
                            >
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                fullWidth
                                size="small"
                                value={costCenterGroupDescription?.desc ?? ""}
                                inputProps={{maxLength: 40}}
                                onChange={(e, value) => {
                                  setIsValidationErrorNode(false)
                                  setIsValidationErrorDesc(false)
                                  let inputValue = e.target.value;
                                  if (inputValue.startsWith(" ")) {
                                    inputValue = inputValue.trimStart();
                                  }
                                  const capitalizedValue = inputValue
                                  .replace(/[^a-zA-Z0-9-&()#.'/$%, ]/g, "") // Allow only specific characters
                                  .replace(/\s{2,}/g, " ") // Replace multiple spaces with a single space
                                  .replace(/\s*([-&()#.'/$%,])\s*/g, "$1") // Remove spaces before and after special characters
                                  .replace(/([-&()#.'/$%,])\s+/g, "$1") // Remove spaces **after** special characters
                                  .trimStart()?.toUpperCase();
                                  console.log("pctrgroup", e.target.value.toUpperCase());
                                  setCostCenterGroupDescription({
                                    code: "",
                                    // desc: e.target.value.toUpperCase(),
                                    desc: capitalizedValue,
                                  });
                                }}
                                placeholder="ENTER COST ELEMENT GROUP DESCRIPTION"
                              />
                            </FormControl>
                          </Grid>
                        </>
                      ) : (
                        <>
                          <Grid
                            item
                            md={6}
                            sx={{ width: "100%", marginTop: ".5rem" }}
                          >
                            <Typography>
                              Chart Of Account
                              <span style={{ color: "red" }}>*</span>
                            </Typography>

                            <FormControl
                              fullWidth
                              sx={{ margin: ".5em 0px", minWidth: "250px" }}
                            >
                              <Autocomplete
                                sx={{ height: "31px" }}
                                fullWidth
                                size="small"
                                value={chartOfAccount ?? {code:"ETCN", desc:"ET NATURAL / OPERATIONAL CHART OF ACCOUNTS"}}
                                options={dropDownData?.NewChartOfAccounts ?? []}
                                disableClearable
                                onChange={(e, value) => {
                                  setChartOfAccount(value);
                                  getCostElementGroup(value?.code);
                                }}
                                getOptionLabel={(option) => {
                                  if (option?.code)
                                    return (
                                      `${option?.code}-${option?.desc}` ?? ""
                                    );
                                  else return "";
                                }}
                                renderOption={(props, option) => (
                                  <li {...props}>
                                    <Typography style={{ fontSize: 12 }}>
                                    {option?.desc ? (
                                    <>
                                      <strong>{option.code}</strong> -{" "}
                                      {option.desc}
                                    </>
                                  ) : (
                                    <strong>{option?.code}</strong>
                                  )}
                                    </Typography>
                                  </li>
                                )}
                                renderInput={(params) => (
                                  <TextField
                                    sx={{ fontSize: "12px !important" }}
                                    {...params}
                                    variant="outlined"
                                    placeholder="SELECT CHART OF ACCOUNT"

                                    // error={controllingAreaValid}
                                  />
                                )}
                              />
                            </FormControl>
                          </Grid>

                          <Grid
                            item
                            md={6}
                            sx={{ width: "100%", marginTop: ".5rem" }}
                          >
                            <Typography>
                              Controlling Area
                              <span style={{ color: "red" }}>*</span>
                            </Typography>

                            <FormControl
                              fullWidth
                              sx={{ margin: ".5em 0px", minWidth: "250px" }}
                            >
                              <Autocomplete
                                sx={{ height: "31px" }}
                                fullWidth
                                size="small"
                                value={controllingArea ?? {code:"ETCA", desc:"ET FAMILY CO AREA"}}
                                options={dropDownData?.NewControllingArea ?? []}
                                disableClearable
                                onChange={(e, value) => {
                                  setControllingArea(value);
                                  // getCostCenterGroup(value.code);
                                }}
                                getOptionLabel={(option) => {
                                  if (option?.code)
                                    return (
                                      `${option?.code}-${option?.desc}` ?? ""
                                    );
                                  else return "";
                                }}
                                renderOption={(props, option) => (
                                  <li {...props}>
                                    <Typography style={{ fontSize: 12 }}>
                                    {option?.desc ? (
                                    <>
                                      <strong>{option.code}</strong> -{" "}
                                      {option.desc}
                                    </>
                                  ) : (
                                    <strong>{option?.code}</strong>
                                  )}
                                    </Typography>
                                  </li>
                                )}
                                renderInput={(params) => (
                                  <TextField
                                    sx={{ fontSize: "12px !important" }}
                                    {...params}
                                    variant="outlined"
                                    placeholder="SELECT CONTROLLING AREA"

                                    // error={controllingAreaValid}
                                  />
                                )}
                              />
                            </FormControl>
                          </Grid>

                          <Grid
                            item
                            md={6}
                            sx={{ width: "100%", marginTop: ".5rem" }}
                          >
                            <Typography>
                              Cost Element Group
                              <span style={{ color: "red" }}>*</span>
                            </Typography>

                            <FormControl
                              fullWidth
                              sx={{ margin: ".5em 0px", minWidth: "250px" }}
                            >
                              <Autocomplete
                                sx={{ height: "31px" }}
                                fullWidth
                                size="small"
                                options={dropDownData?.CostElementGroup ?? []}
                                onChange={(e, value,reason) => {
                                  if (reason === 'clear') {
                                    setCostCenterGroup(null);
                                    setCostCenterGroupDescription(null);
                                    return;
                                  }
                                  setIsDuplicateNodeRequest(false);
                                  setCostCenterGroup({
                                    code: value.code,
                                    desc: "",
                                  });
                                  setCostCenterGroupDescription({
                                    code: "",
                                    desc: value.desc,
                                  });
                                }}
                                getOptionLabel={(option) => {
                                  if (option?.code)
                                    return (
                                      `${option?.code}-${option?.desc}` ?? ""
                                    );
                                  else return "";
                                }}
                                renderOption={(props, option) => (
                                  <li {...props}>
                                    <Typography style={{ fontSize: 12 }}>
                                    {option?.desc ? (
                                    <>
                                      <strong>{option.code}</strong> -{" "}
                                      {option.desc}
                                    </>
                                  ) : (
                                    <strong>{option?.code}</strong>
                                  )}
                                    </Typography>
                                  </li>
                                )}
                                renderInput={(params) => (
                                  <TextField
                                    sx={{ fontSize: "12px !important" }}
                                    {...params}
                                    variant="outlined"
                                    placeholder="SELECT  COST ELEMENT GROUP"

                                    // error={controllingAreaValid}
                                  />
                                )}
                              />
                            </FormControl>
                          </Grid>
                        </>
                      )}
                    </Grid>
                  </Box>
                </Stack>
                {isValidationErrorNode ? (
                  <Grid>
                    <Typography style={{ color: "red" }}>
                      *This Node already exists in the selected Controlling Area
                    </Typography>
                  </Grid>
                ) : ""}
                {isDuplicateDBDesc && scenarioState === "create" ? (
                                    <Grid>
                                      <Typography style={{ color: "red" }}>
                                        *This Description already exists in some ongoing request in selected Controlling Area.
                                      </Typography>
                                    </Grid>
                                  ) : (
                                    ""
                                  )}

                {isValidationErrorDesc ? (
                  <Grid>
                    <Typography style={{ color: "red" }}>
                      *This Description already exists for some node in selected Controlling Area
                    </Typography>
                  </Grid>
                ) : ""}
                    {isDuplicateNodeRequest ? (
                      <Grid>
                        <Typography style={{ color: "red" }}>
                          *There is an ongoing request for this Cost Element Group
                        </Typography>
                      </Grid>
                    ) : ""}
              </DialogContent>

              <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
                {scenarioState === "create" ? (
                  <>
                    <Button
                      sx={{ width: "max-content", textTransform: "capitalize" }}
                      onClick={handleCloseInitialDialog}
                    >
                      Cancel
                    </Button>

                    <Button
                      className="button_primary--normal"
                      type="save"
                      onClick={handleInitialDialogSubmit}
                      variant="contained"
                      disabled={isProceedCreateDisabled}
                    >
                      Proceed
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      sx={{ width: "max-content", textTransform: "capitalize" }}
                      onClick={handleCloseInitialDialog}
                    >
                      Cancel
                    </Button>

                    <Button
                      className="button_primary--normal"
                      type="save"
                      onClick={handleInitialDialogSubmit}
                      variant="contained"
                      disabled={isProceedDisabled}
                    >
                      Proceed
                    </Button>
                  </>
                )}
              </DialogActions>
            </Dialog>
            <Dialog
              hideBackdrop={false}
              elevation={2}
              PaperProps={{
                sx: { boxShadow: "none" },
              }}
              open={openDownloadExcelDialog}
              onClose={handleDownloadExcelDialogClose}
            >
              <DialogTitle
                sx={{
                  justifyContent: "space-between",

                  alignItems: "center",

                  height: "max-content",

                  padding: ".5rem",

                  paddingLeft: "1rem",

                  backgroundColor: "#EAE9FF40",

                  // borderBottom: "1px solid grey",

                  display: "flex",
                }}
              >
                <Typography variant="h6">Please Select</Typography>

                <IconButton
                  sx={{ width: "max-content" }}
                  onClick={handleDownloadExcelDialogClose}
                  children={<CloseIcon />}
                />
              </DialogTitle>

              <DialogContent sx={{ padding: ".5rem 1rem" }}>
                <Stack>
                  <Box sx={{ minWidth: 400 }}>
                    <Grid container spacing={1}>
                      <Grid
                        item
                        md={6}
                        sx={{ width: "100%", marginTop: ".5rem" }}
                      >
                        <Typography>
                          Controlling Area
                          <span style={{ color: "red" }}>*</span>
                        </Typography>

                        <FormControl
                          fullWidth
                          sx={{ margin: ".5em 0px", minWidth: "250px" }}
                        >
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            options={dropDownData?.NewControllingArea ?? []}
                            onChange={(e, value) => {
                              setControllingArea(value);
                              getProfitCenterGroup(value.code);
                            }}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return `${option?.code}-${option?.desc}` ?? "";
                              else return "";
                            }}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                {option?.desc ? (
                                    <>
                                      <strong>{option.code}</strong> -{" "}
                                      {option.desc}
                                    </>
                                  ) : (
                                    <strong>{option?.code}</strong>
                                  )}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="SELECT CONTROLLING AREA"

                                // error={controllingAreaValid}
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>

                      <Grid
                        item
                        md={6}
                        sx={{ width: "100%", marginTop: ".5rem" }}
                      >
                        <Typography>
                          Cost Element Group
                          <span style={{ color: "red" }}>*</span>
                        </Typography>

                        <FormControl
                          fullWidth
                          sx={{ margin: ".5em 0px", minWidth: "250px" }}
                        >
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            onChange={(e, value) => {
                              setCostCenterGroup(value);
                            }}
                            options={dropDownData?.costCTRGroup ?? []}
                            getOptionLabel={(option) => {
                              if (option?.code)
                                return `${option?.code}-${option?.desc}` ?? "";
                              else return "";
                            }}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                {option?.desc ? (
                                    <>
                                      <strong>{option.code}</strong> -{" "}
                                      {option.desc}
                                    </>
                                  ) : (
                                    <strong>{option?.code}</strong>
                                  )}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="SELECT COST ELEMENT GROUP"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                    </Grid>
                  </Box>
                </Stack>
              </DialogContent>

              <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
                <Button
                  sx={{ width: "max-content", textTransform: "capitalize" }}
                  onClick={handleDownloadExcelDialogClose}
                >
                  Cancel
                </Button>

                <Button
                  className="button_primary--normal"
                  type="save"
                  onClick={handleDownloadExcelDialogSubmit}
                  variant="contained"
                >
                  Proceed
                </Button>
              </DialogActions>
            </Dialog>

            <Dialog
              // fullWidth
              maxWidth={"md"}
              open={openGlDialog}
              onClose={handleCloseGLDialog}
              sx={{
                "&::webkit-scrollbar": {
                  width: "1px",
                },
              }}
            >
              <DialogTitle
                sx={{
                  justifyContent: "space-between",
                  alignItems: "center",
                  height: "max-content",
                  padding: ".5rem",
                  paddingLeft: "1rem",
                  backgroundColor: "#EAE9FF40",
                  // borderBottom: "1px solid grey",
                  display: "flex",
                }}
              >
                <Typography variant="h6">{`List of General Ledgers in range [${range}]  `}</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Tooltip title="Export Table">
                    <IconButton
                      sx={{
                        padding: " 0.25rem",
                        height: "max-content",
                        ml: 1
                      }}
                      onClick={handleDownloadGLRange}
                    >
                      <ReusableIcon iconName={"IosShare"} />
                    </IconButton>
                  </Tooltip>
                <IconButton
                  sx={{ width: "max-content", ml: 1  }}
                  onClick={handleCloseGLDialog}
                  children={<CloseIcon />}
                />
                </Box>
              </DialogTitle>
              <DialogContent>
                <ReusableTable
                  width="100%"
                  rows={rowsForGlList ?? []}
                  columns={columnsForGlList}
                  page={page}
                  pageSize={pageSize}
                  rowCount={rowsForGlList?.length ?? 0}
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  // title={"This table shows the List of General Ledgers between"}
                  getRowIdValue={"Id"}
                  hideFooter={true}
                  checkboxSelection={false}
                  callback_onRowDoubleClick={(params) => {
                    console.log("params", params.row);
                  }}
                  stopPropagation_Column={"action"}
                  status_onRowDoubleClick={true}
                  showCustomNavigation={true}
                />
              </DialogContent>
            </Dialog>


            <Dialog open={openDownloadDialog}
         onClose={handleDownloadDialogClose} >
        <DialogTitle  sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              // borderBottom: "1px solid grey",
              display: "flex",
            }}>
        <Typography variant="h6" gutterBottom sx={{ 
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between", 
                }}>
                Select Download Option       
          </Typography>
          
        </DialogTitle>
          <DialogContent>
          <FormControl>
      <RadioGroup
        row
        aria-labelledby="demo-row-radio-buttons-group-label"
        name="row-radio-buttons-group"
        value={downloadType}
        onChange={handleDownloadTypeChange}
      >
          <NoMaxWidthTooltip
                   arrow
                   placement="bottom"
                    title={
                      <span  
                      style={{
                      whiteSpace: "nowrap", // Prevents line break
                      fontSize: "12px",
                      // maxWidth: "400px", // Optional width constraint
                      overflow: "hidden",
                      textOverflow: "ellipsis", // Adds ellipsis if overflow
                    }}>
                      Here Excel will be downloaded
                       </span>
                    }
                    // placement="right"
                  >
                <FormControlLabel value="systemGenerated" control={<Radio />} label="System-Generated" />
                  </NoMaxWidthTooltip>
        
                  <NoMaxWidthTooltip
                   arrow
                   placement="bottom"
                    title={
                      <span  
                      style={{
                      whiteSpace: "nowrap", // Prevents line break
                      fontSize: "12px",
                      // maxWidth: "400px", // Optional width constraint
                      overflow: "hidden",
                      textOverflow: "ellipsis", // Adds ellipsis if overflow
                    }}>
                      Here Excel will be sent to your email
                       </span>
                    }
                    // placement="right"
                  >
                  <FormControlLabel value="mailGenerated" control={<Radio />} label="Mail-Generated" />
                  </NoMaxWidthTooltip>
        
      </RadioGroup>
    </FormControl>
          </DialogContent>
          <DialogActions>
            <Button variant="contained" 
             onClick={onDownloadTypeChange}
            >
              OK
            </Button>
          </DialogActions>
        </Dialog>
        {/* change multiple */}
        <Dialog open={openDownloadChangeDialog}
                onClose={handleDownloadChangeDialogClose} >
        <DialogTitle  sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              // borderBottom: "1px solid grey",
              display: "flex",
            }}>
        <Typography variant="h6" gutterBottom sx={{ 
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between", 
                }}>
                Select Download Option       
          </Typography>
          
        </DialogTitle>
          <DialogContent>
          <FormControl>
      <RadioGroup
        row
        aria-labelledby="demo-row-radio-buttons-group-label"
        name="row-radio-buttons-group"
        value={downloadType}
        onChange={handleMultipleDownloadTypeChange}
      >
          <NoMaxWidthTooltip
                   arrow
                   placement="bottom"
                    title={
                      <span  
                      style={{
                      whiteSpace: "nowrap", // Prevents line break
                      fontSize: "12px",
                      // maxWidth: "400px", // Optional width constraint
                      overflow: "hidden",
                      textOverflow: "ellipsis", // Adds ellipsis if overflow
                    }}>
                      Here Excel will be downloaded
                       </span>
                    }
                    // placement="right"
                  >
                <FormControlLabel value="systemGenerated" control={<Radio />} label="System-Generated" />
                  </NoMaxWidthTooltip>
        
                  <NoMaxWidthTooltip
                   arrow
                   placement="bottom"
                    title={
                      <span  
                      style={{
                      whiteSpace: "nowrap", // Prevents line break
                      fontSize: "12px",
                      // maxWidth: "400px", // Optional width constraint
                      overflow: "hidden",
                      textOverflow: "ellipsis", // Adds ellipsis if overflow
                    }}>
                      Here Excel will be sent to your email
                       </span>
                    }
                    // placement="right"
                  >
                  <FormControlLabel value="mailGenerated" control={<Radio />} label="Mail-Generated" />
                  </NoMaxWidthTooltip>
        
      </RadioGroup>
    </FormControl>
          </DialogContent>
          <DialogActions>
            <Button variant="contained" 
             onClick={onMultipleDownloadTypeChange}
            >
              OK
            </Button>
          </DialogActions>
        </Dialog>
        
            {/* <Backdrop
              sx={{
                color: "#fff",
                zIndex: (theme) => theme.zIndex.drawer + 1,
              }}
              open={blurLoading}
            >
              <CircularProgress color="inherit" />
            </Backdrop> */}
            <ReusableBackDrop
          blurLoading={blurLoading}
          loaderMessage={loaderMessage}
        />

            <Grid container sx={outermostContainer_Information}>
              <Grid item md={5} sx={outerContainer_Information}>
                <Typography variant="h3">
                  <strong>General Ledger Hierarchy</strong>
                </Typography>

                <Typography variant="body2" color="#777">
                  This view displays the selected General Ledger Hierarchy
                </Typography>
              </Grid>
              <Grid item md={7} sx={{ display: "flex" }}>
                <Grid
                  container
                  direction="row"
                  justifyContent="flex-end"
                  alignItems="center"
                  spacing={0}
                >
                  <Tooltip title="Reload">
                    <IconButton sx={iconButton_SpacingSmall}>
                      <Refresh
                        sx={{
                          "&:hover": {
                            transform: "rotate(360deg)",
                            transition: "0.9s",
                          },
                        }}
                        onClick={refreshPage}
                      />
                    </IconButton>
                  </Tooltip>
                  {/* <Tooltip title="Export">
                    <IconButton sx={iconButton_SpacingSmall}>
                      <IosShare onClick={exportAsPicture} />
                    </IconButton>
                  </Tooltip> */}
                </Grid>
              </Grid>
            </Grid>

            <Grid container sx={container_filter}>
              <Grid item md={12}>
                <Accordion className="filter-accordian"
                expanded={isAccordionExpanded}
                onChange={handleAccordionToggle}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1a-content"
                    id="panel1a-header"
                    sx={{
                      minHeight: "2rem !important",
                      margin: "0px !important",
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: "700",
                      }}
                    >
                      Search Hierarchy Node
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails sx={{ padding: "0.5rem 1rem 0.5rem" }}>
                    <Grid
                      container
                      rowSpacing={1}
                      spacing={2}
                      justifyContent="space-between"
                      alignItems="center"
                      // sx={{ marginBottom: "0.5rem" }}
                    >
                      <Grid
                        container
                        spacing={1}
                        sx={{ padding: "0rem 1rem 0.5rem" }}
                      >
                        <Grid item md={2}>
                          <Typography sx={font_Small}>
                            Chart Of Account
                          </Typography>

                          <FormControl size="small" fullWidth>
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              size="small"
                              options={dropDownData?.NewChartOfAccounts ?? []}
                              value={ceSearchForm?.chartOfAccount ?? {code:"ETCN", desc:"ET NATURAL / OPERATIONAL CHART OF ACCOUNTS"}}
                              onChange={handleChartOfAccountSearch}
                              disableClearable
                              getOptionLabel={(option) => {
                                if (option?.code)
                                  return (
                                    `${option?.code}-${option?.desc}` ?? ""
                                  );
                                else return "";
                              }}
                              renderOption={(props, option) => (
                                <li {...props}>
                                  <Typography style={{ fontSize: 12 }}>
                                    {/* {`${option?.code}-${option?.desc}`} */}
                                    {option?.desc ? (
                                      <>
                                        <strong>{option.code}</strong> - {option.desc}
                                      </>
                                    ) : (
                                      <strong>{option.code}</strong>
                                    )}
                                  </Typography>
                                </li>
                              )}
                              renderInput={(params) => (
                                <TextField
                                  sx={{ fontSize: "12px !important" }}
                                  {...params}
                                  variant="outlined"
                                  placeholder="Select Controlling Area"

                                  // error={controllingAreaValid}
                                />
                              )}
                            />
                          </FormControl>
                        </Grid>
                        <Grid item md={2}>
                          <Typography sx={font_Small}>
                            Controlling Area
                          </Typography>

                          <FormControl size="small" fullWidth>
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              size="small"
                              options={dropDownData?.NewControllingArea ?? []}
                              value={ceSearchForm?.controllingArea ?? {code:"ETCA", desc:"ET FAMILY CO AREA"}}
                              onChange={handleControllingAreaSearch}
                              disableClearable
                              getOptionLabel={(option) => {
                                if (option?.code)
                                  return (
                                    `${option?.code}-${option?.desc}` ?? ""
                                  );
                                else return "";
                              }}
                              renderOption={(props, option) => (
                                <li {...props}>
                                  <Typography style={{ fontSize: 12 }}>
                                    {/* {`${option?.code}-${option?.desc}`} */}
                                    {option?.desc ? (
                                      <>
                                        <strong>{option.code}</strong> - {option.desc}
                                      </>
                                    ) : (
                                      <strong>{option.code}</strong>
                                    )}
                                  </Typography>
                                </li>
                              )}
                              renderInput={(params) => (
                                <TextField
                                  sx={{ fontSize: "12px !important" }}
                                  {...params}
                                  variant="outlined"
                                  placeholder="Select Controlling Area"

                                  // error={controllingAreaValid}
                                />
                              )}
                            />
                          </FormControl>
                        </Grid>
                        <Grid item md={2}>
                          <Typography sx={font_Small}>
                            Cost Element Group
                          </Typography>
                          <FormControl size="small" fullWidth>
                            <Autocomplete
                              sx={{ height: "31px" }}
                              fullWidth
                              size="small"
                              // value={ccSearchForm?.controllingArea}
                              value={ceSearchForm?.costElementGroup}
                              onChange={handleCostElementGroupSearch}
                              options={dropDownData?.CostElementGroupFilter ?? []}
                              getOptionLabel={(option) => {
                                if (option?.code)
                                  return (
                                    `${option?.code}-${option?.desc}` ?? ""
                                  );
                                else return "";
                              }}
                              renderOption={(props, option) => (
                                <li {...props}>
                                  <Typography style={{ fontSize: 12 }}>
                                    {/* {`${option?.code}-${option?.desc}`} */}
                                    {option?.desc ? (
                                      <>
                                        <strong>{option.code}</strong> - {option.desc}
                                      </>
                                    ) : (
                                      <strong>{option.code}</strong>
                                    )}
                                  </Typography>
                                </li>
                              )}
                              renderInput={(params) => (
                                <TextField
                                  sx={{ fontSize: "12px !important" }}
                                  {...params}
                                  variant="outlined"
                                  placeholder="Select Cost Element Group"
                                />
                              )}
                            />
                          </FormControl>
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid
                      container
                      style={{
                        display: "flex",
                        justifyContent: "flex-end",
                      }}
                    >
                      <Grid
                        item
                        style={{
                          display: "flex",
                          justifyContent: "space-around",
                        }}
                      >
                        <Button
                          variant="outlined"
                          sx={button_Outlined}
                          onClick={handleClear}
                        >
                          Clear
                        </Button>
                        <Grid sx={{ ...button_Marginleft }}>
                          <ReusablePreset
                            moduleName={"HierarchyNodeGeneralLedger"}
                          />
                        </Grid>
                        <Button
                          variant="contained"
                          sx={{ ...button_Primary, ...button_Marginleft }}
                          onClick={() => getFilter()}
                        >
                          Search
                        </Button>
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              </Grid>
            </Grid>

            <>
            {initialNodeData.length > 0 && initialNodeData[0]?.title &&
              <Grid container>
                <Grid
                  item
                  md={12}
                  sx={{
                    backgroundColor: "white",
                    maxHeight: "max-content",
                    height: "max-content",
                    borderRadius: "8px",
                    border: "1px solid #E0E0E0",
                    mt: 0.25,
                    boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                    ...container_Padding,
                    // ...container_columnGap,
                  }}
                >
                  <Grid
                    container
                    display="flex"
                    flexDirection="row"
                    flexWrap="nowrap"
                  >
                    <Grid item md={12} >

                    {initialNodeData[0] ? (
                            <Grid
                              item
                              sx={{
                                display: "flex",
                                justifyContent: "space-between",
                              }}
                            >
                              <Grid>
                                <Typography
                                  variant="body1"
                                  fontWeight="bold"
                                  justifyContent="flex-start"
                                >
                                  Use{" "}
                                  <span
                                    style={{
                                      fontWeight: "bolder",
                                      color: "#000000",
                                    }}
                                  >
                                    Ctrl+F
                                  </span>{" "}
                                  to search the Hierarchy and find the placement
                                  of a Hierarchy Node or Data Object.
                                </Typography>{" "}
                              </Grid>
                              <Grid
                                item
                                // md={5}
                                //  justifyContent="center"
                                // style={{ minHeight: "100vh" }}
                              >
                                <Box
                                  // mt={4}
                                  display="flex"
                                  // flexDirection="column"
                                  // alignItems="center"
                                >
                                  <Box display="flex">
                                    <TextField
                                      label="Search"
                                      variant="outlined"
                                      size="small"
                                      value={searchValue}
                                      inputProps={{ maxLength: 10 }}
                                      onChange={(e) => {
                                        const value = e.target.value;
                                        setSearchValue(value);
                                        setParentNodeForObject("");
                                        setErrorMessage("");
                                      }}
                                      sx={{ marginRight: 1 }}
                                      placeholder="Enter General Ledger"
                                    />
                                    <Button
                                      variant="contained"
                                      color="primary"
                                      onClick={handleParentNodeSearch}
                                    >
                                      Get Node
                                    </Button>
                                    <Tooltip title="Search to find the Parent Node of a Data Object.">
                                      <IconButton
                                        color="primary"
                                        aria-label="upload picture"
                                        component="label"
                                        sx={iconButton_SpacingSmall}
                                      >
                                        <InfoIcon
                                          style={{
                                            height: "0.8em",
                                            width: "0.8em",
                                            color: "grey",
                                          }}
                                          onClick={() => {}}
                                        />
                                      </IconButton>
                                    </Tooltip>
                                  </Box>
                                  <Box mt={2}>
                                    {parentNodeForObject ? (
                                      <Typography variant="body1">
                                        Parent Node: {parentNodeForObject}
                                      </Typography>
                                    ) : errorMessage ? (
                                      <Typography variant="body1" color="error">
                                        {errorMessage}
                                      </Typography>
                                    ) : null}
                                  </Box>
                                </Box>
                              </Grid>{" "}
                            </Grid>
                          ) : (
                            ""
                          )}
                    
                      <ReusableHierarchyTree
                        coa={ceSearchForm?.chartOfAccount?.code === "" ? "" : ceSearchForm?.chartOfAccount?.code}
                        treeData={initialNodeData}
                        bigTreeData={bigTreeData}
                        buttonAdd={false} 
                        buttonRemove={false}
                        buttonIconVisibility={false}
                        handleData={updateTreeData}
                        updatedNodeList={updateNodeList}
                        updatedPCList={updatePCList}
                        updatedDescList={updateDescList}
                        updateReplaceNodeList={updateReplaceNodeList}
                        updateReplaceTagList={updateReplaceTagList}
                        handleUpdates={handleUpdate}
                        updateChangeDescList={updateChangeDescList}
                        updatedNewNodeList = {updatedNewNodeList}
                        updateMoveNodeList={updateMoveNodeList}
                        updateRestoreMoveNode={updateRestoreMoveNode}
                        updateRemovePCData={updateRemovePCData}
                        updateRemoveNodeData={updateRemoveNodeData}
                        updateStoreMoveTagList={updateStoreMoveTagList}
                        updateRestoreMoveTag={updateRestoreMoveTag}

                        // for gl range view list
                        updateGlViewList = {setViewGlList}
                        updateGlViewDialog = {setOpenGlDialog}
                        updateRange = {setRange}
                      />

                      {showInputT && (
                        <div>
                          <input
                            type="text"
                            placeholder="Enter title"
                            onBlur={() => setShowInput(false)}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                handleAddNodeForParent(e.target.value);
                              }
                            }}
                          />
                        </div>
                      )}
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            }
            </>

            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{ display: "flex", justifyContent: "flex-end", gap: 1 }}
              >
                <Button
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary }}
                  onClick={() => setOpenInitialDialog(true)}
                >
                  Single
                </Button>

                <ButtonGroup
                  variant="contained"
                  ref={anchorRefCreate}
                  aria-label="split button"
                >
                  <Button
                    size="small"
                    variant="contained"
                    onClick={() =>
                      handleClickCreate(optionsCreateMultiple[0], 0)
                    }
                    // onClick={handleDialogClickOpen}
                  >
                    {optionsCreateMultiple[0]}
                  </Button>
                  <Button
                    size="small"
                    aria-controls={
                      openButtonCreate ? "split-button-menu" : undefined
                    }
                    aria-expanded={openButtonCreate ? "true" : undefined}
                    aria-label="select action"
                    aria-haspopup="menu"
                    onClick={handleToggleCreate}
                  >
                    <ReusableIcon
                      iconName={"ArrowDropUp"}
                      iconColor={"#FFFFFF"}
                    />
                  </Button>
                </ButtonGroup>
                <Popper
                  sx={{
                    zIndex: 1,
                  }}
                  open={openButtonCreate}
                  anchorEl={anchorRefCreate.current}
                  placement={"top-end"}
                >
                  <Paper
                    style={{ width: anchorRefCreate.current?.clientWidth }}
                  >
                    <ClickAwayListener onClickAway={handleCloseButtonCreate}>
                      <MenuList id="split-button-menu" autoFocusItem>
                        {optionsCreateMultiple
                          .slice(1)
                          ?.map((option, index) => (
                            <MenuItem
                              key={option}
                              selected={index === selectedIndexCreate - 1}
                              onClick={() =>
                                handleClickCreate(option, index + 1)
                              }
                            >
                              {option}
                            </MenuItem>
                          ))}
                      </MenuList>
                    </ClickAwayListener>
                  </Paper>
                </Popper>

                <ButtonGroup
                  variant="contained"
                  ref={anchorRefChange}
                  aria-label="split button"
                >
                  <Button
                    size="small"
                    variant="contained"
                    onClick={() =>
                      handleClickChange(optionsChangeMultiple[0], 0)
                    }
                  >
                    {optionsChangeMultiple[0]}
                  </Button>
                  <Button
                    size="small"
                    aria-controls={
                      openButtonChange ? "split-button-menu" : undefined
                    }
                    aria-expanded={openButtonChange ? "true" : undefined}
                    aria-label="select action"
                    aria-haspopup="menu"
                    onClick={handleToggleChange}
                  >
                    <ReusableIcon
                      iconName={"ArrowDropUp"}
                      iconColor={"#FFFFFF"}
                    />
                  </Button>
                </ButtonGroup>
                <Popper
                  sx={{
                    zIndex: 1,
                  }}
                  open={openButtonChange}
                  anchorEl={anchorRefChange.current}
                  placement={"top-end"}
                >
                  <Paper
                    style={{ width: anchorRefChange.current?.clientWidth }}
                  >
                    <ClickAwayListener onClickAway={handleCloseButtonChange}>
                      <MenuList id="split-button-menu" autoFocusItem>
                        {optionsChangeMultiple
                          .slice(1)
                          ?.map((option, index) => (
                            <MenuItem
                              key={option}
                              selected={index === selectedIndexCreate - 1}
                              onClick={() =>
                                handleClickChange(option, index + 1)
                              }
                            >
                              {option}
                            </MenuItem>
                          ))}
                      </MenuList>
                    </ClickAwayListener>
                  </Paper>
                </Popper>
              </BottomNavigation>
            </Paper>
          </Stack>
        </div>
      </div>
      )}
    </>
  );
};

export default HierarchyNodeGeneralLedger;
