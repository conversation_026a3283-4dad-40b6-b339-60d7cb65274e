import React from "react";
import {
    BottomNavigation,
  Button,
  Grid,
  Paper,
  Typography,
} from "@mui/material";
import { Stack } from "@mui/system";
import { useState, useEffect } from "react";
import {
  outermostContainer,
  outermostContainer_Information,
} from "@components/Common/commonStyles";
import useLang from "@hooks/useLang";
import { useNavigate } from "react-router-dom";
import { setDisplayPayload } from "@app/payloadSlice";
import { useDispatch } from "react-redux";
import { clearHeaderFieldsBOM } from "./bomSlice";
import { APP_END_POINTS } from "@constant/appEndPoints";

const BillOfMaterial = () => {
  const navigate = useNavigate();
  const { t } = useLang();
  const [value, setValue] = useState(null);
  const dispatch = useDispatch();

  return (
    <>
      <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1}>
          <Grid container mt={0} sx={outermostContainer_Information}>
            <Grid item md={5} >
              <Typography variant="h3">
                <strong>{t("Bill of Material")}</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                {t("This view displays the Bill of Materials")}
              </Typography>
            </Grid>
          </Grid>
          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
            >
            <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
                }}
                value={value}
                onChange={(newValue) => {
                setValue(newValue);
                }}
            >
                <Button
                  size="small"
                  variant="contained"
                  onClick={() => {
                    navigate(APP_END_POINTS?.CREATE_BOM);
                    dispatch(setDisplayPayload({}));
                    dispatch(clearHeaderFieldsBOM());
                  }}
                >
                  {t("Create Request")}
                </Button>
            </BottomNavigation>
            </Paper>

        </Stack>
      </div>
    </>
  );
};

export default BillOfMaterial;
