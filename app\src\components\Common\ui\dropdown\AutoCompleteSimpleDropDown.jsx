import { useState, useRef, useCallback, useMemo, useEffect } from "react";
import { 
  Box, 
  Paper, 
  TextField, 
  IconButton,
  Typography,
  Checkbox,
  FormGroup,
  FormControlLabel,
  Chip,
  Popover
} from "@mui/material";
import { FixedSizeList as List } from "react-window";
import ClearIcon from '@mui/icons-material/Clear';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import { colors } from "@constant/colors";

const AutoCompleteSimpleDropDown = ({ 
  options = [], 
  value = [],
  onChange,
  placeholder = "Select Option",
  onInputChange,
  minCharacters = 0,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
  const [isPopoverVisible, setIsPopoverVisible] = useState(false);
  const [popoverContent, setPopoverContent] = useState("");
  const containerRef = useRef(null);
  const listRef = useRef(null);

  const handleInputChange = (e) => {
    const value = e.target.value;
    setInputValue(value);
    setIsOpen(true);
    if (onInputChange) {
      onInputChange(value);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const filteredOptions = useMemo(() => {
    if (inputValue.length < minCharacters) {
      return [];
    }

    if (!inputValue) return ["Select All", ...options];
    
    return ["Select All", ...options.filter(option => 
      option.toLowerCase().includes(inputValue.toLowerCase())
    )];
  }, [options, inputValue, minCharacters]);

  const handleSelect = useCallback((selectedOption) => {
    if (selectedOption === "Select All") {
      onChange(value.length === options.length ? [] : [...options]);
    } else {
      const newValue = value.includes(selectedOption)
        ? value.filter(item => item !== selectedOption)
        : [...value, selectedOption];
      onChange(newValue);
    }
  }, [value, options, onChange]);

  const handlePopoverOpen = (event, content) => {
    setPopoverAnchorEl(event.currentTarget);
    setPopoverContent(content);
    setIsPopoverVisible(true);
  };

  const handlePopoverClose = () => {
    setIsPopoverVisible(false);
  };

  const Row = useCallback(({ index, style }) => {
    const option = filteredOptions[index];
    const isSelected = option === "Select All" 
      ? value.length === options.length 
      : value.includes(option);
    
    return (
      <Box
        component="div"
        sx={{
          ...style,
          padding: "4px 8px",
          cursor: "pointer",
          "&:hover": {
            backgroundColor: "action.hover",
          },
        }}
        onClick={() => handleSelect(option)}
      >
        <FormGroup sx={{ width: '100%', py: 0.5 }}>
          <FormControlLabel
            sx={{
              margin: 0,
              '& .MuiFormControlLabel-label': {
                flex: 1
              }
            }}
            control={
              <Checkbox 
                size="small" 
                checked={isSelected}
                indeterminate={option === "Select All" && value.length > 0 && value.length < options.length}
                sx={{ py: 0.5 }}
              />
            }
            label={
              <Typography sx={{ fontSize: 13 }}>
                {option}
              </Typography>
            }
          />
        </FormGroup>
      </Box>
    );
  }, [filteredOptions, value, options, handleSelect]);

  const handleClear = () => {
    onChange([]);
    setInputValue("");
  };

  const renderSelectedValues = () => {
    if (value?.length === 0) return null;
    
    return value?.length > 1 ? (
      <>
        <Chip
          sx={{
            height: 25,
            fontSize: "0.85rem",
            '.MuiChip-label': { padding: "0 6px" }
          }}
          label={value[0]}
        />
        <Chip
          sx={{
            height: 25,
            fontSize: "0.85rem",
            '.MuiChip-label': { padding: "0 6px" },
            ml: 0.5
          }}
          label={`+${value.length - 1}`}
          onMouseEnter={(event) => {
            const selectedOptionsText = value
              .slice(1)
              .map(option => `${option}`)
              .join("<br />");
            handlePopoverOpen(event, selectedOptionsText);
          }}
          onMouseLeave={handlePopoverClose}
        />
      </>
    ) : (
      <Chip
        sx={{
          height: 25,
          fontSize: "0.85rem",
          '.MuiChip-label': { padding: "0 6px" }
        }}
        label={value[0]}
      />
    );
  };

  return (
    <Box ref={containerRef} sx={{ position: "relative", width: "100%" }}>
      <TextField
        fullWidth
        size="small"
        value={inputValue}
        onChange={handleInputChange}
        onClick={() => setIsOpen(true)}
        placeholder={placeholder}
        InputProps={{
          startAdornment: renderSelectedValues(),
          endAdornment: (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {value?.length > 0 && (
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleClear();
                  }}
                  sx={{ 
                    padding: '4px',
                    mr: 0.5,
                    '&:hover': {
                      backgroundColor: 'rgba(0, 0, 0, 0.04)'
                    }
                  }}
                >
                  <ClearIcon sx={{ fontSize: '16px' }} />
                </IconButton>
              )}
              <IconButton 
                size="small" 
                onClick={(e) => {
                  e.stopPropagation();
                  setIsOpen(!isOpen);
                }}
                sx={{ padding: '4px' }}
              >
                {isOpen ? (
                  <ArrowDropUpIcon sx={{ fontSize: '20px' }} />
                ) : (
                  <ArrowDropDownIcon sx={{ fontSize: '20px' }} />
                )}
              </IconButton>
            </Box>
          ),
          sx: {
            '& .MuiInputBase-input': {
              padding: '4px 8px',
              height: '25px'
            }
          }
        }}
      />

      {isOpen && (
        <Paper
          sx={{
            position: "absolute",
            top: "100%",
            left: 0,
            right: 0,
            mt: 1,
            maxHeight: 300,
            zIndex: 1000,
          }}
        >
          {filteredOptions.length === 0 ? (
            <Box sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                {inputValue.length < minCharacters 
                  ? `Please enter at least ${minCharacters} characters` 
                  : 'No options found'}
              </Typography>
            </Box>
          ) : (
            <List
              height={Math.min(filteredOptions.length * 35, 300)}
              itemCount={filteredOptions.length}
              itemSize={35}
              width="100%"
              ref={listRef}
            >
              {Row}
            </List>
          )}
        </Paper>
      )}

      <Popover
        open={isPopoverVisible}
        anchorEl={popoverAnchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        onMouseEnter={() => setIsPopoverVisible(true)}
        onMouseLeave={handlePopoverClose}
        PaperProps={{
          sx: {
            backgroundColor: colors.primary.whiteSmoke,
            boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
            borderRadius: "8px",
            padding: "10px",
            fontSize: "0.875rem",
            color: colors.blue.main,
            border: "1px solid #ddd",
          }
        }}
      >
        <Box
          sx={{
            maxHeight: "270px",
            overflowY: "auto",
            padding: "5px",
          }}
          dangerouslySetInnerHTML={{ __html: popoverContent }}
        />
      </Popover>
    </Box>
  );
};

export default AutoCompleteSimpleDropDown;
