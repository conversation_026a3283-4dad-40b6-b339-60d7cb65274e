import React, { useState, useRef } from "react";
import { Grid, Stack, Typography } from "@mui/material";
import { outermostContainer, outermostContainer_Information } from "@components/Common/commonStyles";
import useLang from "@hooks/useLang";
import BottomNavigationForMasterData from "./BottomNavigationForMasterData";

const MasterDataLayout = ({
  dialogs,
  snackbar,
  searchParamsSection,
  tableSection,
  bottomNav,
  exportSearch,
  backDrop,
  toastContainer,
  ...props
}) => {

  const { t } = useLang();

  const [filterOpen, setFilterOpen] = useState(true);

  const refExport = useRef(null);

  return (
    <div ref={refExport}>

      <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1}>
          <Grid container mt={0} sx={outermostContainer_Information}>
            <Grid item md={5}>
              <Typography variant="h3">
                <strong>{t(props?.heading)}</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                {t(props?.description)}
              </Typography>
            </Grid>
          </Grid>
          {filterOpen && searchParamsSection}

          {/* Table Section */}
          <Grid item sx={{ position: "relative" }}>
            {tableSection}
          </Grid>

          {/* Bottom Navigation Actions */}
          <Grid item>
          <BottomNavigationForMasterData buttonConfigs={props?.buttonConfigs}/>
          </Grid>
        </Stack>
      </div>

      {/* Export/Search Dialog, Backdrop, Toasts */}
      {exportSearch}
      {backDrop}
      {toastContainer}
    </div>
  );
};

export default MasterDataLayout;
