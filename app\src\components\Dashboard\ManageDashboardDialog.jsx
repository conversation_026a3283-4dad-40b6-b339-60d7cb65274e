import React, { useState, useEffect, useCallback } from "react";
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, List, ListItem, ListItemText, Switch, Divider, Box, CircularProgress, Typography, FormControl, Select, MenuItem, Grid, Tabs, Tab } from "@mui/material";
import { useSelector } from "react-redux";

import { destination_Dashboard } from "../../destinationVariables";
import { CHART_TYPE } from "../../constant/enum";
import { doAjax } from "../Common/fetchService";
import { END_POINTS } from "../../constant/apiEndPoints";
import useLang from "@hooks/useLang";

// Color palette options
const COLOR_PALETTES = [
  { value: "Pallet 1", label: "Pallet 1" },
  { value: "Pastel", label: "Pastel" },
  { value: "Vibrant", label: "Vibrant" },
  { value: "Dark", label: "Dark" },
  { value: "Default", label: "Default" }
];
const LINEAR_CHART_TYPES = [
  { value: CHART_TYPE.BAR, label: "Bar" },
  { value: CHART_TYPE.COLUMN, label: "Column" },
  { value: CHART_TYPE.LINE, label: "Line" },
  { value: CHART_TYPE.AREA, label: "Area" },
  { value: CHART_TYPE.STACK_COLUMN, label: "Stacked Column" },
];

const CIRCULAR_CHART_TYPES = [
  { value: CHART_TYPE.PIE, label: "Pie" },
  { value: CHART_TYPE.DONUT, label: "Donut" },
];
const isCircularChart = (chartType) => {
  return chartType === CHART_TYPE.PIE || chartType === CHART_TYPE.DONUT;
};
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div role="tabpanel" hidden={value !== index} id={`dashboard-tabpanel-${index}`} aria-labelledby={`dashboard-tab-${index}`} {...other}>
      {value === index && <Box sx={{ p: 1 }}>{children}</Box>}
    </div>
  );
}

const ManageDashboardDialog = ({ open, onClose, onSave, decisionTableConfig, userPreferences, reportConfig }) => {
  const [tabValue, setTabValue] = useState(0);
  const [graphSettings, setGraphSettings] = useState([]);
  const [reportSettings, setReportSettings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [savingChanges, setSavingChanges] = useState(false);
  const [changedKpis, setChangedKpis] = useState({});
  const [changedReports, setChangedReports] = useState({});
  const { t } = useLang();

  const userData = useSelector((state) => state.userManagement.userData);
  const userId = userData?.user_id || "";
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const initializeSettings = useCallback(() => {
    if (decisionTableConfig?.length > 0) {
      const settings = decisionTableConfig.map((kpi) => {
        const userPref = userPreferences?.find((p) => p.KpiId === kpi.MDG_KPI_ID);
        return {
          id: kpi.MDG_KPI_ID,
          prefId: userPref?.Id || null,
          name: kpi.MDG_KPI_NAME,
          enabled: String(kpi.MDG_KPI_VISIBILITY).toLowerCase() === "true", // <-- FIXED
          userEnabled: userPref ? userPref.KpiVisibility === true && userPref.IsActive === true : false,
          sequence: kpi.MDG_KPI_GRAPH_SEQUENCE,
          chartType: userPref?.KpiChartType || kpi.MDG_KPI_GRAPH_TYPE,
          column: kpi.MDG_KPI_GRAPH_COLUMN?.toLowerCase(),
          colorPallet:userPref?.KpiColPallet|| kpi.MDG_KPI_COLOR_PALLET,
        };
      });
      const reports = reportConfig.map((report) => {
        const userPref = userPreferences?.find((p) => p.KpiId === report.MDG_KPI_ID);
        return {
          id: report.MDG_KPI_ID,
          prefId: userPref?.Id || null,
          name: report.MDG_KPI_NAME,
          enabled: ["true", "enabled"].includes(String(report.MDG_KPI_VISIBILITY).toLowerCase()),
          userEnabled: userPref ? userPref.KpiVisibility === true && userPref.IsActive === true : true,
        };
      });
      
      setGraphSettings(settings);
      setReportSettings(reports);
      setChangedKpis({});
      setChangedReports({});
      setLoading(false);
    }
  }, [decisionTableConfig, userPreferences]);

 useEffect(() => {
  if (open) {
    setLoading(true);
    initializeSettings();
  } else {
    setTabValue(0); // reset only on close
  }
}, [open, initializeSettings]);

  const handleToggleGraph = (id) => {
    setGraphSettings((prev) => prev.map((graph) => (graph.id === id ? { ...graph, userEnabled: !graph.userEnabled } : graph)));

    setChangedKpis((prev) => ({
      ...prev,
      [id]: true,
    }));
  };
  const handleToggleReport = (id) => {
    setReportSettings((prev) => prev.map((report) => (report.id === id ? { ...report, userEnabled: !report.userEnabled } : report)));

    setChangedReports((prev) => ({
      ...prev,
      [id]: true,
    }));
  };

  const handleChartTypeChange = (id, newChartType) => {
    setGraphSettings((prev) => prev.map((graph) => (graph.id === id ? { ...graph, chartType: newChartType } : graph)));
    setChangedKpis((prev) => ({
      ...prev,
      [id]: true,
    }));
  };

  const handleColorPaletteChange = (id, newColorPalette) => {
    setGraphSettings((prev) => prev.map((graph) => (graph.id === id ? { ...graph, colorPallet: newColorPalette } : graph)));
    setChangedKpis((prev) => ({
      ...prev,
      [id]: true,
    }));
  };

  const handleSaveChanges = async () => {
    setSavingChanges(true);
    
    try {
      const changedKpiIds = Object.keys(changedKpis);
      const changedReportIds = Object.keys(changedReports);

      if (changedKpiIds.length === 0 && changedReportIds.length === 0) {
        onClose();
        return;
      }
      const kpiPrefs = graphSettings
        .filter((graph) => changedKpiIds.includes(graph.id))
        .map((graph) => ({
          Id: graph.prefId,
          UserId: userId,
          KpiId: graph.id,
          KpiChartType: graph.chartType,
          KpiChartName: graph.name,
          KpiColPallet: graph.colorPallet,
          KpiSequence: Number(graph.sequence),
          KpiColumn: graph.column,
          KpiVisibility: graph.userEnabled,
          IsActive: graph.userEnabled,
          KpiType: "KPI Metrics",
        }));

      const reportPrefs = reportSettings
        .filter((report) => changedReportIds.includes(report.id))
        .map((report) => ({
          Id: report.prefId,
          UserId: userId,
          KpiId: report.id,
          KpiChartType: "REPORT",
          KpiChartName: report.name,
          KpiColPallet: "",
          KpiSequence: 0,
          KpiColumn: "",
          KpiVisibility: report.userEnabled,
          IsActive: report.userEnabled,
          KpiType: "KPI Reports",
        }));

      const newPrefs = [...kpiPrefs, ...reportPrefs];

      // Save user preferences
      await new Promise((resolve, reject) => {
        doAjax(`/${destination_Dashboard}${END_POINTS.DASHBOARD_APIS.SAVE_USER_CONFIG}`, "post", resolve, reject, newPrefs);
      });
      
      // Call the onSave callback to refresh the dashboard components
      if (onSave) {
        onSave();
      } else {
        onClose(); // Fallback to just closing if no onSave provided
      }
    } catch (error) {
      onClose();
    } finally {
      setSavingChanges(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
      <DialogTitle>{t("Manage Dashboard")}</DialogTitle>
      <DialogContent dividers>
        {loading ? (
          <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Box sx={{ borderBottom: 1, borderColor: "divider", mb: 2 }}>
              <Tabs value={tabValue} onChange={handleTabChange} aria-label="dashboard management tabs">
                <Tab label={t("KPI Metrics")} id="dashboard-tab-0" aria-controls="dashboard-tabpanel-0" />
                <Tab label={t("KPI Reports")} id="dashboard-tab-1" aria-controls="dashboard-tabpanel-1" />
              </Tabs>
            </Box>

            <TabPanel value={tabValue} index={0}>
              <List>
                {graphSettings.map((graph, index) => (
                  <React.Fragment key={graph.id}>
                    {index > 0 && <Divider />}
                    <ListItem>
                      <Grid container spacing={2} alignItems="center">
                        <Grid item xs={4}>
                          <ListItemText
                            primary={
                              <Typography
                                variant="body1"
                                sx={{
                                  fontWeight: graph.enabled ? "normal" : "light",
                                  color: graph.enabled ? "text.primary" : "text.disabled",
                                }}
                              >
                                {t(graph.name)}
                              </Typography>
                            }
                            secondary={
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{
                                  fontStyle: graph.enabled ? "normal" : "italic",
                                  color: graph.enabled ? "text.secondary" : "text.disabled",
                                }}
                              >
                                {`${t("Column")}: ${graph.column}`}
                              </Typography>
                            }
                          />
                        </Grid>
                        <Grid item xs={3}>
                          <FormControl fullWidth size="small">
                            <Select value={graph.chartType} onChange={(e) => handleChartTypeChange(graph.id, e.target.value)} displayEmpty>
                              {isCircularChart(graph.chartType)
                                ? CIRCULAR_CHART_TYPES.map((option) => (
                                    <MenuItem key={option.value} value={option.value}>
                                      {option.label}
                                    </MenuItem>
                                  ))
                                : LINEAR_CHART_TYPES.map((option) => (
                                    <MenuItem key={option.value} value={option.value}>
                                      {option.label}
                                    </MenuItem>
                                  ))}
                            </Select>
                          </FormControl>
                        </Grid>
                        <Grid item xs={3}>
                          <FormControl fullWidth size="small">
                            <Select 
                              value={graph.colorPallet || "default"} 
                              onChange={(e) => handleColorPaletteChange(graph.id, e.target.value)} 
                               renderValue={(selected) => selected || "Select Palette"}
                            >
                              {COLOR_PALETTES.map((option) => (
                                <MenuItem key={option.value} value={option.value}>
                                  {option.label}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        </Grid>
                        <Grid item xs={2} sx={{ textAlign: "right" }}>
                          <Switch edge="end" checked={graph.userEnabled} onChange={() => handleToggleGraph(graph.id)} disabled={!graph.enabled} />
                        </Grid>
                      </Grid>
                    </ListItem>
                  </React.Fragment>
                ))}
              </List>
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <List>
                {reportSettings.map((report, index) => (
                  <React.Fragment key={report.id}>
                    {index > 0 && <Divider />}
                    <ListItem>
                      <Grid container spacing={2} alignItems="center">
                        <Grid item xs={9}>
                          <ListItemText
                            primary={
                              <Typography
                                variant="body1"
                                sx={{
                                  fontWeight: report.enabled ? "normal" : "light",
                                  color: report.enabled ? "text.primary" : "text.disabled",
                                }}
                              >
                                {t(report.name)}
                              </Typography>
                            }
                          />
                        </Grid>
                        <Grid item xs={3} sx={{ textAlign: "right" }}>
                          <Switch edge="end" checked={report.userEnabled} onChange={() => handleToggleReport(report.id)} disabled={!report.enabled} />
                        </Grid>
                      </Grid>
                    </ListItem>
                  </React.Fragment>
                ))}
              </List>
            </TabPanel>
          </>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={savingChanges}>
          {t("Cancel")}
        </Button>
        <Button onClick={handleSaveChanges} variant="contained" color="primary" disabled={savingChanges || (Object.keys(changedKpis).length === 0 && Object.keys(changedReports).length === 0)}>
          {savingChanges ? t("Saving...") : t("Save Changes")}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ManageDashboardDialog;











