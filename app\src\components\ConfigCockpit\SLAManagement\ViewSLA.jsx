import { useState, useEffect } from "react";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import Modal from "@mui/material/Modal";
import {
  DatePicker,
  DesktopDatePicker,
  LocalizationProvider,
  MobileDateTimePicker,
} from "@mui/x-date-pickers";
import CloseIcon from "@mui/icons-material/Close";
import {
  Card,
  CardContent,
  CardHeader,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  Input,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Tooltip,
} from "@mui/material";
import axios from "axios";
import ReusableDialog from "../../common/ReusableDialog";
import {
  destination_Admin,
  destination_Notification,
  destination_Po,
  destination_SLA_Mgmt,
} from "../../../destinationVariables";
import {
  button_Marginleft,
  button_Outlined,
  button_Primary,
  font_Small,
} from "../../common/commonStyles";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { doAjax } from "../../common/fetchService";
import { useSelector } from "react-redux";
import moment from 'moment/moment'
import ReusableIcon from "../../common/ReusableIcon";

const ViewSLA = ({ id, open, handleClose }) => {
  const [SLAData, setSLAData] = useState({});
  const [emailTemplateList, setemailTemplateList] = useState([]);
  const [ready, setready] = useState(false)
  const [suppliersData, setsuppliersData] = useState({})
  const [companysData, setcompanysData] = useState({})
  const [purchasingGroupsData, setpurchasingGroupsData] = useState({})

  const appSettings = useSelector((state) => state.appSettings)
  let fieldData = [
    { title: "Process Name",
     value: "processName",
      type: "string",
      col:5
     },

    { value: "serviceName",
     title: "Service Name",
      type: "string",
      col:5
     },
    {
      title: "SLA Type",
      value: "slaType",
      type: "string",
      col:5
    },
    {
      title: "SLA Validity",
      value: "endDate",
      type: "date",
      col:5
    },
    {
      title: "SLA",
      value: "sla",
      type: "string",
      col:5
    },
    {
      title: "",
      value: "SLA Reference",
      type: "string",
      variant: "custom",
      component: (params, data) => {
        return (
          <>
            <Stack
              sx={{
                backgroundColor: "#EAE9FF",
                borderRadius: "10px",
                padding: ".5rem",
              }}
            >
              <Typography>{`SLA Reference: ${data[params.value]}}`}</Typography>
            </Stack>
          </>
        );
      },
      col:5
    },
    {
      title: "Master Data Category",
      value: "masterDataCategory",
      type: "string",
      col:5
    },
    {
      title: "Master Data",
      value: "masterData",
      type: "string",
      col:5
    },
    {
      title: "Created Date",
      value: "createdAt",
      type: "date",
      col:5
    },
    {
      title: "Created By",
      value: "createdBy",
      type: "string",
      col:5
    },
    {
      title: "Updated Date",
      value: "updatedAt",
      type: "string",
      col:5
    },
    {
      title: "Updated By",
      value: "updatedBy",
      type: "string",
      col:5
    },
  ];
  let fetchSingleSLA = async () => {
    try {
      let hSuccess = (response) => {
        let res = response.data;
        let tData = {
          ...res,
        };
        Object.keys(emailTemplateList).forEach((item) => {
          if (emailTemplateList[item] === res.emailTemplate) {
            tData.emailTemplate = item;
          }
        });
        setSLAData(tData);
        setready(true)
        // setSLAData(res.data)
      };
      let hError = () => {};
      doAjax(
        `/${destination_SLA_Mgmt}/sla/getSingleSla/${id}`,
        "get",
        hSuccess,
        hError
      );
    } catch (e) {
      console.log(e);
    }
  };
  let createDataFields = (fieldData, data) => {
    //map the keys extracted from data object
    //loop keys
    // extract title text to be displayed for related property

    let returnValue = (item, nData) => {
      if (item.type === "date") {
        return moment(nData[item.value]).format(appSettings.dateFormat);
      } else {
        return nData[item.value];
      }
    };
    return (
      <>
        {fieldData.map((i) => (
          <Grid item xs={i.col} sx={{ display: "flex", direction: "row" }}>
            <Typography variant="body1">{i.title}</Typography>
            {i.variant && i.variant === "custom" ? (
              i.component(i, data)
            ) : (
              <Typography variant="body1">{returnValue(i, data)}</Typography>
            )}
          </Grid>
        ))}
      </>
    );
  };
  let fetchEmailTemplates = async () => {
    try {
      let hSuccess = (response) => {
        let tData = {};
        response.forEach((item) => {
          if (item.templateName) {
            tData[item.templateName] = item.templateId;
          }
        });
        setemailTemplateList(tData);

        console.log(response);
      };
      let hError = () => {};
      doAjax(
        `/${destination_Notification}/emailTemplate/getAllTemplates`,
        "get",
        hSuccess,
        hError
      );
    } catch (e) {
      console.log(e);
    }
  };
  // const fetchCompanySupplierData = ()=>{
  //   doAjax(`/${destination_Po}/Odata/populateCompanyCodeDetails`,'get',(res)=>{
  //     setcompanysData(res.data)
  //   })
  //   doAjax(`/${destination_Po}/Odata/getAllSuppliers`,'get',(res)=>{
  //     setsuppliersData(res.data)
  //   })
  //   doAjax(`/${destination_Po}/Odata/purchasingGroup`, "get", (res)=>{
  //     setpurchasingGroupsData(res.data);

  //   }, (err)=>{});
  // }
  // useEffect(() => {
  //   // fetchEmailTemplates();
  //   fetchCompanySupplierData()

  // }, []);
  useEffect(() => {
    fetchSingleSLA();
  }, [emailTemplateList]);

  console.log();
  let masterDataTitle = (params)=>{
    let value = ''
    switch(params.masterDataCategory?.toLowerCase()){
      case "company":
        value =  companysData[params.masterData];
        break;
        case 'supplier': 
        value = suppliersData[params.masterData];
        break;

        case 'purchasing group':
          value = purchasingGroupsData[params.masterData];
        break;

        }
    console.log(companysData)

      return value
  }
  return (
    <Dialog
        // maxWidth="md"
        open={open}
        onClose={() => handleClose("VIEW")}>
      
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              display: "flex",
            }}
          >
              <Typography variant="h6">{SLAData.processName && `SLA - ${SLAData.processName}`}</Typography>

<IconButton
  sx={{ width: "max-content" }}
  onClick={() => handleClose("VIEW")}
  children={<ReusableIcon iconName={"Close"} />}
/>
            </DialogTitle>
          <DialogContent sx={{ padding: ".5rem 1rem" }}>
            
            {ready && 
            <Grid container col={10}>
            <Grid item xs='5' sx={{ padding: "0px .4em",margin:'.5rem 0' }}>
              <Typography
                variant="body1"
                sx={{ textAlign: "start" }}
              >
                Process Name
              </Typography>
              <Typography
                variant="body1"
                fontWeight="bold"
                sx={{ textAlign: "start" }}
              >
                {SLAData.processName}
               
              </Typography>
            </Grid>
            <Grid item xs='5' sx={{ padding: "0px .4em",margin:'.5rem 0' }}>
              <Typography
                variant="body1"
                sx={{ textAlign: "start" }}
              >
                Service Name
              </Typography>
              <Typography
                variant="body1"
                fontWeight="bold"
                sx={{ textAlign: "start" }}
              >
                {SLAData.serviceName}
               
              </Typography>
            </Grid>
            <Grid item xs='5' sx={{ padding: "0px .4em",margin:'.5rem 0' }}>
              <Typography
                variant="body1"
                sx={{ textAlign: "start" }}
              >
                SLA Type
              </Typography>
              <Typography
                variant="body1"
                fontWeight="bold"
                sx={{ textAlign: "start" }}
              >
                {SLAData.slaType}
               
              </Typography>
            </Grid>
            <Grid item xs='5' sx={{ padding: "0px .4em",margin:'.5rem 0' }}>
            <Stack
            sx={{
              backgroundColor: (theme) => theme.background.secondary ,
              borderRadius: "10px",
              padding: ".5rem",
            }}
          >
            <Typography>{`SLA Reference: ${SLAData.slaReference??""}`}</Typography>
          </Stack>
            </Grid>
            <Grid item xs='5' sx={{ padding: "0px .4em",margin:'.5rem 0' }}>
              <Typography
                variant="body1"
                sx={{ textAlign: "start" }}
              >
               SLA Validity Range
              </Typography>
              <Typography
                variant="body1"
                fontWeight="bold"
                sx={{ textAlign: "start" }}
              >
                {`${moment(SLAData.startDate).format(appSettings.dateFormat)} ~ ${moment(SLAData.endDate).format(appSettings.dateFormat)}`}
               
              </Typography>
            </Grid>
          
            

            <Grid item xs='5' sx={{ padding: "0px .4em",margin:'.5rem 0' }}>
              <Typography
                variant="body1"
                sx={{ textAlign: "start" }}
              >
                Master Data Category
              </Typography>
              <Typography
                variant="body1"
                fontWeight="bold"
                sx={{ textAlign: "start" }}
              >
                {SLAData.masterDataCategory}
               
              </Typography>
            </Grid>
            <Grid item xs='5' sx={{ padding: "0px .4em",margin:'.5rem 0' }}>
              <Typography
                variant="body1"
                sx={{ textAlign: "start" }}
              >
                Master Data
              </Typography>
              <Stack>
          <Typography variant="body1"
                fontWeight="bold"
                sx={{ textAlign: "start" }} >
            
            {masterDataTitle({masterData:SLAData.masterData,masterDataCategory:SLAData.masterDataCategory})}
          </Typography>
          <Typography fontWeight="bold" sx={{ textAlign: "start" }}>{SLAData.masterData}</Typography>
          </Stack>
             
            </Grid>
            <Grid item xs='5' sx={{ padding: "0px .4em",margin:'.5rem 0' }}>
              <Typography
                variant="body1"
                sx={{ textAlign: "start" }}
              >
                Created Date
              </Typography>
              <Typography
                variant="body1"
                fontWeight="bold"
                sx={{ textAlign: "start" }}
              >
                {moment(SLAData.createdAt).format(appSettings.dateFormat)}
               
              </Typography>
            </Grid>
            <Grid item xs='5' sx={{ padding: "0px .4em",margin:'.5rem 0' }}>
              <Typography
                variant="body1"
                sx={{ textAlign: "start" }}
              >
                Created By
              </Typography>
              <Typography
                variant="body1"
                fontWeight="bold"
                sx={{ textAlign: "start" }}
              >
                {SLAData.createdBy}
               
              </Typography>
            </Grid>
            <Grid item xs='5' sx={{ padding: "0px .4em",margin:'.5rem 0' }}>
              <Typography
                variant="body1"
                sx={{ textAlign: "start" }}
              >
                Updated Date
              </Typography>
              <Typography
                variant="body1"
                fontWeight="bold"
                sx={{ textAlign: "start" }}
              >
                {moment(SLAData.updatedAt).format(appSettings.dateFormat)}
               
              </Typography>
            </Grid>
            <Grid item xs='5' sx={{ padding: "0px .4em",margin:'.5rem 0' }}>
              <Typography
                variant="body1"
                sx={{ textAlign: "start" }}
              >
                Updated By
              </Typography>
              <Typography
                variant="body1"
                fontWeight="bold"
                sx={{ textAlign: "start" }}
              >
                {SLAData.updatedBy}
               
              </Typography>
            </Grid>

          
          </Grid>
            }
            </DialogContent>
        </Dialog>

  );
};

export default ViewSLA;
