import { VISIBILITY_TYPE } from "@constant/enum";

export const getFieldsForTemplate = (changeFieldData = [], fieldNameList = []) => {
  const trimmedFieldNameList = fieldNameList.map((f) => f.trim());

  const mandatoryFields = new Set();
  const headerFields = new Set();
  const allFields = new Set();
  const fieldMetaMap = {}; // store metadata here

  changeFieldData.forEach((item) => {
    const name = item?.MDG_MAT_FIELD_NAME?.trim();
    const visibility = item?.MDG_MAT_FIELD_VISIBILITY;
    const jsonFieldName = item?.MDG_MAT_JSON_FIELD_NAME;

    if (!name || !jsonFieldName) return;

    // Add to metadata map
    fieldMetaMap[jsonFieldName?.toLowerCase()] = {
      maxLength: item?.MDG_MAT_MAX_LENGTH,
      fieldType: item?.MDG_MAT_FIELD_TYPE,
      sequence: item?.MDG_MAT_FIELD_SEQUENCE,
      visibility,
      originalFieldName: name,
    };

    if (visibility === VISIBILITY_TYPE.HEADER) {
      headerFields.add(name);
      allFields.add(name);
    }

    if (visibility === VISIBILITY_TYPE.MANDATORY && trimmedFieldNameList.includes(name)) {
      mandatoryFields.add(name);
      allFields.add(name);
    }

    if (trimmedFieldNameList.includes(name)) {
      allFields.add(name);
    }
  });

  return {
    allFields: Array.from(allFields),
    mandatoryFields: Array.from(mandatoryFields),
    headerFields: Array.from(headerFields),
    fieldMetaMap, // return metadata map
  };
};
