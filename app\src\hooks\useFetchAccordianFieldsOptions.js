import { END_POINTS } from '@constant/apiEndPoints';
import { destination_MaterialMgmt } from '../destinationVariables';
import useFetchDropdownAndDispatch from './useFetchDropdownAndDispatch';
import { MATERIAL_VIEWS } from '@constant/enum';

const useFetchAccordianFieldsOptions = () => {
  const { fetchDataAndDispatch } = useFetchDropdownAndDispatch();

  const fetchTabSpecificData = (comb, type) => {
    
    if (type === MATERIAL_VIEWS.SALES && comb && comb.includes('-')) {
      const [salesOrg, distChnl] = comb.split('-');
      if (salesOrg) {
        fetchDataAndDispatch(
          `/${destination_MaterialMgmt}${END_POINTS.DATA.GET_DELIVARING_PLANT_BASED_ON_SALES_ORG_AND_DISTCHNL}`,
          "DelygPlnt",
          "post",
          { salesOrg, distChnl },
          true
        );
      }
    } else if (type === MATERIAL_VIEWS.PLANT && comb) {
      const plant = comb;
      
      fetchDataAndDispatch(
        `/${destination_MaterialMgmt}${END_POINTS.DATA.GET_SPPROC_TYPE}`,
        "Spproctype",
        "post",
        { plant },
        true
      );
      fetchDataAndDispatch(
        `/${destination_MaterialMgmt}${END_POINTS.DATA.GET_MRP_CONTROLLER_BASED_ON_PLANT}`,
        "MrpCtrler",
        "post",
        { plant },
        true
      );
      fetchDataAndDispatch(
        `/${destination_MaterialMgmt}${END_POINTS.DATA.GET_PROD_STORAGE_LOCATION_BASED_ON_PLANT}`,
        "IssStLoc",
        "post",
        { plant },
        true
      );
      fetchDataAndDispatch(
        `/${destination_MaterialMgmt}${END_POINTS.DATA.GET_PROCUREMENT_STORAGE_LOCATION_BASED_ON_PLANT}`,
        "SlocExprc",
        "post",
        { plant },
        true
      );
      fetchDataAndDispatch(
        `/${destination_MaterialMgmt}${END_POINTS.DATA.GET_SCHEDULING_MARGIN_KEY_BASED_ON_PLANT}`,
        "SmKey",
        "post",
        { plant },
        true
      );
      fetchDataAndDispatch(
        `/${destination_MaterialMgmt}${END_POINTS.DATA.GET_PROFIT_CENTER_BASED_ON_PLANT}`,
        "ProfitCtr",
        "post",
        { plant },
        true
      );
      fetchDataAndDispatch(
        `/${destination_MaterialMgmt}${END_POINTS.DATA.GET_PRODUCTION_SCHEDULING_PROFILE_BASED_ON_PLANT}`,
        "ProdProf",
        "post",
        { plant },
        true
      );
    } else if (type === MATERIAL_VIEWS.WAREHOUSE && comb) {
      fetchDataAndDispatch(
        `/${destination_MaterialMgmt}${END_POINTS.DATA.GET_PLACEMENT}?wareHouseNo=${comb}`,
        "Placement",
        "get",
        {plant:comb},
        true
      );
    }
  };

  return { fetchTabSpecificData };
};

export default useFetchAccordianFieldsOptions;
