import React, { useState, useEffect, useRef } from 'react';
import 'react-toastify/dist/ReactToastify.css';
import { 
  IconButton, 
  Tooltip, 
  Box, 
  Typography, 
  Popper, 
  Paper, 
  ClickAwayListener,
  Grow,
  CircularProgress,
  Stack
} from '@mui/material';
import SpeedIcon from '@mui/icons-material/Speed';
import { styled } from '@mui/material/styles';
import { colors } from '@constant/colors';
import { doAjax } from "../fetchService";
import useLogger from '@hooks/useLogger';
import { destination_DocumentManagement } from '../../../../src/destinationVariables';
import { ERROR_MESSAGES } from '@constant/enum';
import useLang from '@hooks/useLang';

const {VITE_DOCUMENT1_FORSPEEDTEST, VITE_DOCUMENT2_FORSPEEDTEST, VITE_DOCUMENT3_FORSPEEDTEST} = import.meta.env;

const SpeedIconButton = styled(IconButton)(({ speedColor }) => ({
  color: speedColor,
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: `rgba(${speedColor === colors.health.critical ? '231, 76, 60' :
      speedColor === colors.health.warning ? '243, 156, 18' :
        '46, 204, 113'}, 0.2)`,
    transform: 'scale(1.05)'
  },
}));

const GlassPopper = styled(Paper)(({ theme }) => ({
  width: 300,
  borderRadius: 20,
  border: '1px solid rgba(255, 255, 255, 0.3)',
  position: 'relative',
  background: 'rgba(255, 255, 255, 0.85)',
  backdropFilter: 'blur(12px)',
  padding: 24,
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1)'
}));

const SpeedIndicator = styled(Box)(({ theme }) => ({
  position: 'relative',
  width: 160,
  height: 160,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  margin: '0 auto 20px',
}));

const GlassCircle = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  borderRadius: '50%',
  background: 'rgba(255, 255, 255, 0.1)',
  backdropFilter: 'blur(5px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  boxShadow: '0 4px 30px rgba(0, 0, 0, 0.1)',
  zIndex: 1
}));
const InternetSpeedChecker = ({ minSpeedMbps = 5 }) => {
  const [speedMbps, setSpeedMbps] = useState(null);
  const [animating, setAnimating] = useState(false);
  const {customError} = useLogger();
  const { t } = useLang();
  const [open, setOpen] = useState(false);
  const anchorRef = useRef(null);
  const imageUrls = [
    `/${destination_DocumentManagement}/documentManagement/download?id=${VITE_DOCUMENT1_FORSPEEDTEST}`,
    `/${destination_DocumentManagement}/documentManagement/download?id=${VITE_DOCUMENT2_FORSPEEDTEST}`,
    `/${destination_DocumentManagement}/documentManagement/download?id=${VITE_DOCUMENT3_FORSPEEDTEST}`
  ];
  const hasRunInitialCheck = useRef(false);
  const [lastChecked, setLastChecked] = useState(null);
  const [isChecking, setIsChecking] = useState(false);

  useEffect(() => {
    if (!hasRunInitialCheck.current) {
      checkInternetSpeed();
      hasRunInitialCheck.current = true;
    }
    const intervalId = setInterval(() => {
      checkInternetSpeed();
    }, 15 * 60 * 1000);
    
    return () => clearInterval(intervalId);
  }, []);

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = (event) => {
    if (anchorRef?.current && anchorRef.current.contains(event?.target)) {
      return;
    }
    setOpen(false);
  };

  const checkInternetSpeed = async () => {
    setAnimating(true);
    setIsChecking(true);
    const startTime = new Date().getTime();
    
    try {
      const connectionTest = await fetch('https://www.google.com/favicon.ico', { 
        mode: 'no-cors',
        cache: 'no-cache',
        timeout: 5000
      }).catch(() => null);
      
      if (!connectionTest) {
        setSpeedMbps(0);
        setIsChecking(false);
        setLastChecked(new Date());
        setAnimating(false);
        return;
      }
      
      let totalBytes = 0;
      let downloadCount = 0;
      const totalImages = imageUrls.length;
      
      const noCacheHeaders = {
        "Cache-Control": "no-store, no-cache, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0"
      };

      const hSuccess = (data) => {
        let imageSizeInBytes = 0;
        if (data && data.size) {
          imageSizeInBytes = data.size;
        } else if (data && typeof data === 'string') {
          imageSizeInBytes = new Blob([data]).size;
        } else if (data) {
          imageSizeInBytes = new Blob([JSON.stringify(data)]).size;
        }
        
        totalBytes += imageSizeInBytes;
        downloadCount++;
        
        if (downloadCount === totalImages) {
          const endTime = new Date().getTime();
          const durationInSeconds = (endTime - startTime) / 1000;
          const bitsLoaded = totalBytes * 8;
          const speedInBitsPerSecond = bitsLoaded / durationInSeconds;
          const calculatedSpeedMbps = (speedInBitsPerSecond / (1024 * 1024)).toFixed(2);
          
          setSpeedMbps(calculatedSpeedMbps);
          setLastChecked(new Date());
          setIsChecking(false);
          if (calculatedSpeedMbps < minSpeedMbps || calculatedSpeedMbps === 0) {
            setOpen(true);
          }
          setTimeout(() => {
            setAnimating(false);
          }, 100);
        }
      };
      
      const hError = (error) => {
        customError(ERROR_MESSAGES.FAILED_TO_CHECK_INTERNET_SPEED, error);
        setSpeedMbps(0);
        setIsChecking(false);
        setLastChecked(new Date());
        setAnimating(false);
        setOpen(true);
      };

      imageUrls.forEach(url => {
        const cacheBustedUrl = `${url}&cache_bust=${Date.now()}`;
        doAjax(
          cacheBustedUrl,
          "getblobfile",
          hSuccess,
          hError,
          null,
          noCacheHeaders
        );
      });
    } catch (error) {
      customError(ERROR_MESSAGES.FAILED_TO_CHECK_INTERNET_SPEED, error);
      setSpeedMbps(0);
      setIsChecking(false);
      setLastChecked(new Date());
      setAnimating(false);
    }
  };

  const getSpeedColor = () => {
    if (speedMbps === 0) return colors.health.critical; 
    if (speedMbps === null) return colors.health.warning; 
    if (speedMbps < minSpeedMbps) return colors.health.critical; 
    return colors.health.healthy;
  };

  const getSpeedStatus = () => {
    if (speedMbps === 0) return "No Connection";
    if (speedMbps === null) return "Unknown";
    if (speedMbps < minSpeedMbps) return "Slow Connection";
    return "Good Connection";
  };

  return (
    <Box>
      <Tooltip title={t("Internet Speed")} placement="top" arrow>
        <SpeedIconButton
          ref={anchorRef}
          onClick={handleToggle}
          aria-controls={open ? 'speed-menu' : undefined}
          aria-haspopup="true"
          speedColor={getSpeedColor()}
          size="small"
          sx={{
            animation: animating ? 'pulse 2s infinite' : 'none',
            '@keyframes pulse': {
              '0%': {
                boxShadow: `0 0 0 0 rgba(${getSpeedColor() === colors.health.critical ? '231, 76, 60' :
                  getSpeedColor() ===  colors.health.warning ? '243, 156, 18' :
                    '46, 204, 113'}, 0.4)`
              },
              '70%': {
                boxShadow: `0 0 0 10px rgba(${getSpeedColor() === colors.health.critical ? '231, 76, 60' :
                  getSpeedColor() ===  colors.health.warning ? '243, 156, 18' :
                    '46, 204, 113'}, 0)`
              },
              '100%': {
                boxShadow: `0 0 0 0 rgba(${getSpeedColor() === colors.health.critical ? '231, 76, 60' :
                  getSpeedColor() ===  colors.health.warning ? '243, 156, 18' :
                    '46, 204, 113'}, 0)`
              },
            }
          }}
        >
          <SpeedIcon fontSize="medium" />
        </SpeedIconButton>
      </Tooltip>

      <Popper
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        placement="bottom-end"
        transition
        disablePortal
        style={{ zIndex: 2 }}
        modifiers={[
          {
            name: 'offset',
            options: {
              offset: [0, 10],
            },
          },
        ]}
      >
        {({ TransitionProps }) => (
          <Grow
            {...TransitionProps}
            style={{ transformOrigin: 'top right' }}
            timeout={300}
          >
            <GlassPopper
              elevation={6}
              sx={{
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: -10,
                  right: 14,
                  width: 0,
                  height: 0,
                  borderLeft: '10px solid transparent',
                  borderRight: '10px solid transparent',
                  borderBottom: `10px solid rgba(${getSpeedColor() === colors.health.critical ? '231, 76, 60' : 
                                                getSpeedColor() === colors.health.warning ? '243, 156, 18' : 
                                                '46, 204, 113'}, 0.3)`,
                  zIndex: 0,
                },
              }}
            >
              <ClickAwayListener onClickAway={handleClose}>
                <Box sx={{ width: "100%" }}>
                  <Box sx={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    mb: 2 
                  }}>
                    <Typography variant="h6" sx={{ 
                      fontWeight: "bold", 
                      background: `linear-gradient(90deg, #333333 0%, ${getSpeedColor()} 100%)`,
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                    }}>
                      {t("Internet Speed")}
                    </Typography>
                    <IconButton 
                      onClick={checkInternetSpeed}
                      disabled={isChecking}
                      size="small"
                      sx={{
                        backgroundColor: 'rgba(0, 0, 0, 0.05)',
                        '&:hover': {
                          backgroundColor: 'rgba(0, 0, 0, 0.1)',
                        }
                      }}
                    >
                      <SpeedIcon fontSize="small" />
                    </IconButton>
                  </Box>
                  
                  <SpeedIndicator>
                    <GlassCircle />
                    <Box sx={{ 
                      zIndex: 2, 
                      display: 'flex', 
                      flexDirection: 'column', 
                      alignItems: 'center', 
                      justifyContent: 'center' 
                    }}>
                      {isChecking ? (
                        <CircularProgress size={50} thickness={4} />
                      ) : (
                        <>
                          <Typography 
                            variant="h4" 
                            sx={{ 
                              fontWeight: 'bold', 
                              color: getSpeedColor(), 
                              transition: 'color 0.5s ease',
                              fontSize: '35px'
                            }}
                          >
                            {speedMbps || '0'}
                          </Typography>
                          <Typography variant="caption" sx={{ opacity: 0.7 }}>
                            Mbps
                          </Typography>
                        </>
                      )}
                    </Box>
                    <Box sx={{ 
                      position: 'absolute', 
                      top: 0, 
                      left: 0, 
                      right: 0, 
                      bottom: 0, 
                      zIndex: 0 
                    }}>
                      <CircularProgress
                        variant="determinate"
                        value={100}
                        size={150}
                        thickness={6}
                        sx={{ 
                          color: 'rgba(0, 0, 0, 0.05)',
                          position: 'absolute',
                        }}
                      />
                      <CircularProgress
                        variant="determinate"
                        value={speedMbps ? Math.min((speedMbps / minSpeedMbps) * 100, 100) : 0}
                        size={150}
                        thickness={6}
                        sx={{ 
                          color: getSpeedColor(), 
                          transition: 'color 0.5s ease',
                          position: 'absolute',
                          strokeLinecap: 'round',
                          filter: `drop-shadow(0 0 6px rgba(${getSpeedColor() === colors.health.critical ? '231, 76, 60' : 
                                                           getSpeedColor() === colors.health.warning ? '243, 156, 18' : 
                                                           '46, 204, 113'}, 0.5))`,
                        }}
                      />
                    </Box>
                  </SpeedIndicator>

                  <Stack spacing={2} sx={{ mt: 2 }}>
                    <Box sx={{ 
                      display: 'flex', 
                      justifyContent: 'space-between',
                      p: 1.5,
                      borderRadius: 2,
                      bgcolor: 'rgba(0, 0, 0, 0.03)'
                    }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>{t("Status")}</Typography>
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          fontWeight: 600, 
                          color: getSpeedColor()
                        }}
                      >
                        {getSpeedStatus()}
                      </Typography>
                    </Box>
                    
                    <Box sx={{ 
                      display: 'flex', 
                      justifyContent: 'space-between',
                      p: 1.5,
                      borderRadius: 2,
                      bgcolor: 'rgba(0, 0, 0, 0.03)'
                    }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>{t("Minimum Required")}</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>{minSpeedMbps} Mbps</Typography>
                    </Box>
                    
                    <Box sx={{ 
                      display: 'flex', 
                      justifyContent: 'space-between',
                      p: 1.5,
                      borderRadius: 2,
                      bgcolor: 'rgba(0, 0, 0, 0.03)'
                    }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>{t("Last Checked")}</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {lastChecked ? lastChecked.toLocaleTimeString() : 'Never'}
                      </Typography>
                    </Box>
                  </Stack>
                </Box>
              </ClickAwayListener>
            </GlassPopper>
          </Grow>
        )}
      </Popper>
    </Box>
  );
};

export default InternetSpeedChecker;
