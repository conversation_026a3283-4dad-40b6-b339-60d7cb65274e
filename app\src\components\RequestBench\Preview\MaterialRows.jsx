import React from 'react';
import {
  Typo<PERSON>,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from "@mui/material";
import { ERROR_MESSAGES } from "@constant/enum";
import { colors } from "@constant/colors";

const MaterialRows = ({ storedRows, selectedMaterialId, handleMaterialClick }) => {
  if (!storedRows || storedRows.length === 0) {
    return (
      <div style={{ padding: "16px", textAlign: "center", color: colors.text.secondary }}>
        <Typography variant="body1">
          {ERROR_MESSAGES.NO_DATA_AVAILABLE}
        </Typography>
      </div>
    );
  }

  // Helper function to safely render values
  const renderValue = (value) => {
    if (value === null || value === undefined) {
      return "--";
    }
    
    if (typeof value === 'object') {
      // If it's an object with code and desc properties
      if (value.code !== undefined) {
        return value.desc ? `${value.code} - ${value.desc}` : value.code;
      }
      
      // For other objects, stringify them
      return JSON.stringify(value);
    }
    
    return String(value);
  };

  return (
    <div style={{ width: "100%", height: "375px", overflow: "auto", padding: "16px" }}>
      <TableContainer
        component={Paper}
        sx={{
          borderRadius: "12px",
          boxShadow: colors.shadow.medium,
          overflowX: "auto",
          border: `1px solid ${colors.border.light}`,
          backgroundColor: colors.background.paper,
          maxWidth: "100%",
        }}
      >
        <Table
          sx={{ minWidth: 650, tableLayout: "auto" }}
          aria-label="material data table"
        >
          <TableHead>
            <TableRow
              sx={{
                backgroundColor: colors.background.subtle,
                "& th": {
                  fontWeight: 600,
                  color: colors.text.primary,
                  borderBottom: `2px solid ${colors.border.light}`,
                  padding: "14px 16px",
                  textAlign: "left",
                  verticalAlign: "middle",
                  whiteSpace: "nowrap",
                },
              }}
            >
              <TableCell>Line Number</TableCell>
              <TableCell>Industry Sector</TableCell>
              <TableCell>Material Type</TableCell>
              <TableCell>BOM</TableCell>
              <TableCell>Category</TableCell>
              <TableCell>Relation</TableCell>
              <TableCell>UOM</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {storedRows.map((row, rowIndex) => (
              <TableRow
                key={row.id || rowIndex}
                onClick={() => handleMaterialClick(row.id)}
                sx={{
                  "&:hover": {
                    backgroundColor: colors.hover.light,
                    transition: "background-color 0.2s ease",
                    cursor: "pointer",
                  },
                  "& td": {
                    borderBottom: `1px solid ${colors.border.light}`,
                    padding: "12px 16px",
                    textAlign: "left",
                    verticalAlign: "middle",
                  },
                  "&:last-child td": {
                    borderBottom: "none",
                  },
                  backgroundColor: row.id === selectedMaterialId ? colors.primary.ultraLight : "inherit",
                }}
              >
                <TableCell>
                  <Typography
                    variant="body2"
                    sx={{ fontSize: "13px", color: colors.text.secondary, fontWeight: 500 }}
                  >
                    {renderValue(row.lineNumber)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography
                    variant="body2"
                    sx={{ fontSize: "13px", color: colors.text.secondary, fontWeight: 500 }}
                  >
                    {renderValue(row.industrySector)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography
                    variant="body2"
                    sx={{ fontSize: "13px", color: colors.text.secondary, fontWeight: 500 }}
                  >
                    {renderValue(row.materialType)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography
                    variant="body2"
                    sx={{ fontSize: "13px", color: colors.text.secondary, fontWeight: 500 }}
                  >
                    {renderValue(row.Bom)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography
                    variant="body2"
                    sx={{ fontSize: "13px", color: colors.text.secondary, fontWeight: 500 }}
                  >
                    {renderValue(row.Category)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography
                    variant="body2"
                    sx={{ fontSize: "13px", color: colors.text.secondary, fontWeight: 500 }}
                  >
                    {renderValue(row.Relation)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography
                    variant="body2"
                    sx={{ fontSize: "13px", color: colors.text.secondary, fontWeight: 500 }}
                  >
                    {renderValue(row.Uom)}
                  </Typography>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};

export default MaterialRows;
