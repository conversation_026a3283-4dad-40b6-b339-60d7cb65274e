import { useRef, useEffect, useState } from 'react';
import Level from '../workFlow/Level';
import SVGConnectors from '../workFlow/SVGConnectors';

const RequestHistoryDiagram = ({ data, onTaskClick }) => {
  const ref = useRef(null);
  const [sourceTargets, setSourceTargets] = useState([]);

  // Extract levels and tasks
  const levels = Object.entries(data)
    .map(([levelName, tasks]) => {
      return {
        label: levelName,
        levelKey: tasks?.[0]?.attributesDtos?.[0]?.currentLevel ?? 0,
        tasks: tasks.map((t, i) => {
          const attr = t.attributesDtos?.[0] || {};
          const created = new Date(t.createdAt);
          const due = new Date(t.dueDate);
          const sla = Math.max(1, Math.round((due - created) / (1000 * 60 * 60 * 24)));
          return {
            id: `${levelName.replace(/\s/g, '')}-${i}`,
            name: t.subject,
            approver: t.recipientUsers?.[0] ?? 'N/A',
            status: t.status,
            sla,
            group: attr.approverGroup,
            level: levelName,
          };
        }),
      };
    })
    .sort((a, b) => a.levelKey - b.levelKey);

  useEffect(() => {
    const connectors = [];
    for (let i = 0; i < levels.length - 1; i++) {
      const from = levels[i].tasks;
      const to = levels[i + 1].tasks;
      from.forEach(f => {
        if (to.length > 0) {
          connectors.push({ fromId: f.id, toId: to[0].id });
        }
      });
    }
    setSourceTargets(connectors);
  }, [data]);

  return (
    <div
      style={{
        display: 'flex',
        position: 'relative',
        gap: 30,
        overflow: 'visible',
        minHeight: 200,
        padding: '40px 0',
      }}
      ref={ref}
    >
      {levels.map((level, idx) => (
        <Level key={idx} label={level.label} tasks={level.tasks} onTaskClick={onTaskClick} />
      ))}
      <SVGConnectors containerRef={ref} sourceTargets={sourceTargets} />
    </div>
  );
};

export default RequestHistoryDiagram;
