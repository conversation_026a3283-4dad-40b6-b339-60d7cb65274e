import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  Dialog,
  DialogContent,
  DialogActions,
  DialogTitle,
  Grid,
  Typography,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

const NewItem = ({
  open,
  onClose,
  title,
  label,
  newItem,
  setNewItem,
  onCreate,
  error,
  errorMessage,
}) => {
  
  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle
        sx={{
          height: "3rem",
          display: "flex",
          margin: 0,
          justifyContent: "space-between",
          alignItems: "center",
          padding: ".5rem",
          paddingLeft: "1rem",
          backgroundColor: "#EAE9FF40",
        }}
      >
        <Typography variant="h6">{title}</Typography>
        <IconButton
            sx={{ width: "max-content" }}
            onClick={onClose}
            children={<CloseIcon />}
          />
      </DialogTitle>

      <DialogContent sx={{ padding: "1rem 1rem" }}>
        <Grid
          container
          sx={{
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Grid
            item
            xs
            sx={{
              marginTop: "0.5rem",
            }}
          >
            <Typography variant="body1">Name<span style={{ color: "red" }}>*</span></Typography>
            <TextField
              variant="outlined"
              placeholder="Enter Name"
              fullWidth
              required
              size="small"
              value={newItem?.name}
              onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
              error={error}
              helperText={error && errorMessage}
            />
          </Grid>

          {label && (
            <Grid
              item
              xs
              sx={{
                marginTop: "0.5rem",
              }}
            >
              <Typography variant="body1">Label<span style={{ color: "red" }}>*</span></Typography>
              <TextField
                variant="outlined"
                placeholder="Enter Label"
                fullWidth
                required
                size="small"
                value={newItem?.label}
                onChange={(e) =>
                  setNewItem({ ...newItem, label: e.target.value })
                }
              />
            </Grid>
          )}

          <Grid
            item
            xs
            sx={{
              marginTop: "0.5rem",
            }}
          >
            <Typography variant="body1">Description<span style={{ color: "red" }}>*</span></Typography>
            <TextField
              variant="outlined"
              fullWidth
              placeholder="Enter Description"
              required
              size="small"
              value={newItem?.description}
              onChange={(e) =>
                setNewItem({
                  ...newItem,
                  description: e.target.value,
                })
              }
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
        <Button size="small" variant="outlined" onClick={onClose}>
          Cancel
        </Button>

        <Button
          size="small"
          variant={
            newItem?.name?.length === 0 ||
            (label && newItem?.label?.length === 0) ||
            newItem?.description?.length === 0 ||
            error
              ? "outlined"
              : "contained"
          }
          className="btn-ml"
          onClick={() => {
            onCreate();
            onClose();
          }}
          disabled={
            newItem?.name?.length === 0 ||
            (label && newItem?.label?.length === 0) ||
            newItem?.description?.length === 0 ||
            error
          }
        >
          Submit
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default NewItem;
