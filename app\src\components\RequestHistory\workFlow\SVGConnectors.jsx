import { useEffect, useRef, useState } from 'react';

const SVGConnectors = ({ containerRef, sourceTargets }) => {
  const svgRef = useRef(null);
  const [svgSize, setSvgSize] = useState({ width: 0, height: 0 });

  // Update SVG size on mount and when container size changes
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setSvgSize({ width: rect.width, height: rect.height });
      }
    };
    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, [containerRef]);

  useEffect(() => {
    if (!containerRef.current || !svgRef.current) return;

    const svg = svgRef.current;
    svg.innerHTML = ''; // Clear old paths

    // Re-add marker defs after clearing
    const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
    const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
    marker.setAttribute('id', 'arrowhead');
    marker.setAttribute('markerWidth', '10');
    marker.setAttribute('markerHeight', '7');
    marker.setAttribute('refX', '9');
    marker.setAttribute('refY', '3.5');
    marker.setAttribute('orient', 'auto');
    const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
    polygon.setAttribute('points', '0 0, 10 3.5, 0 7');
    polygon.setAttribute('fill', '#3498db');
    marker.appendChild(polygon);
    defs.appendChild(marker);
    svg.appendChild(defs);

    sourceTargets.forEach(({ fromId, toId }) => {
      const from = document.getElementById(fromId);
      const to = document.getElementById(toId);
      if (!from || !to) return;

      const fromRect = from.getBoundingClientRect();
      const toRect = to.getBoundingClientRect();
      const containerRect = containerRef.current.getBoundingClientRect();

      // Calculate positions relative to the container
      const startX = fromRect.right - containerRect.left;
      const startY = fromRect.top + fromRect.height / 2 - containerRect.top;

      const endX = toRect.left - containerRect.left;
      const endY = toRect.top + toRect.height / 2 - containerRect.top;

      const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      const offset = Math.max(50, Math.abs(endX - startX) / 2);

      const d = `M ${startX},${startY} C ${startX + offset},${startY} ${endX - offset},${endY} ${endX},${endY}`;
      path.setAttribute('d', d);
      path.setAttribute('stroke', '#bdc3c7');
      path.setAttribute('stroke-width', '2');
      path.setAttribute('fill', 'none');
      path.setAttribute('marker-end', 'url(#arrowhead)');

      svg.appendChild(path);
    });
  }, [containerRef, sourceTargets, svgSize]);

  return (
    <svg
      ref={svgRef}
      width={svgSize.width}
      height={svgSize.height}
      style={{
        position: 'absolute',
        top: 5,
        left: 1.5,
        pointerEvents: 'none',
        zIndex: 0
      }}
    />
  );
};

export default SVGConnectors;
