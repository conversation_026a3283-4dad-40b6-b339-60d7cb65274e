import React, { useState, useEffect } from "react";
import {
  Button,
  Dialog,
  DialogContent,
  DialogActions,
  Typography,
  TextField,
  DialogTitle,
} from "@mui/material";
import { makeStyles } from "@mui/styles";

const useStyle = makeStyles((theme) => ({
  dialogTitle: {
    height: "3rem",
    display: "flex",
    alignItems: "center",
    borderBottom: "1px solid #d9d9d9",
    margin: 0,
    padding: 0,
    paddingLeft: 10,
  },
  dialogActions: {
    height: "3rem",
    borderTop: "1px solid #d9d9d9",
  },
}));

function DeletionMessageBox({ open, onClose, onDelete, load }) {
  const classes = useStyle();
  const [deleteText, setDeleteText] = useState("");

  useEffect(() => {
    setDeleteText("");
  }, [open]);

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose}>
      <DialogTitle className={classes.dialogTitle}>Confirm Delete</DialogTitle>

      <DialogContent sx={{paddingTop: "1.2rem !important"}}>
        <TextField
          variant="outlined"
          size="small"
          id="standard-basic"
          label="Type 'delete' if you wanted to delete it"
          fullWidth
          value={deleteText}
          onChange={(e) => setDeleteText(e.target.value)}
        />
      </DialogContent>

      <DialogActions className={classes.dialogActions}>
        <Button
          size="small"
          variant="outlined"
          sx={{
            textTransform: "none",
            borderColor: "#3B30C8",
            color: "#3B30C8",
          }}
          onClick={() => {
            onClose();
            setDeleteText("");
          }}
          disabled={load}
        >
          Cancel
        </Button>

        <Button
          size="small"
          variant="contained"
          sx={{
            textTransform: "none",
            backgroundColor: "#3B30C8",
          }}
          onClick={() => {
            onDelete();
            onClose();
            setDeleteText("");
          }}
          disabled={load || deleteText?.toLowerCase() !== "delete"}
        >
          Delete
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default DeletionMessageBox;
