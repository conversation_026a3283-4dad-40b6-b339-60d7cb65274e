import React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Typography from "@mui/material/Typography";

const MessageBoxComponentDiscard = (props) => {
  return (
    <Dialog className="styleMessageBox" open={props.open} onClose={() => props.onClose("CANCEL")} aria-labelledby="customized-dialog-title" >
      <DialogTitle id="customized-dialog-title" color="primary" sx={{fontSize : '1rem', marginLeft: '0.5rem'}}>
        Discard
      </DialogTitle>
      <DialogContent dividers>
        <Typography gutterBottom style={{ fontSize: "0.8rem" }}>
          Do you want to discard the record?
        </Typography>
      </DialogContent>
      <DialogActions sx={{ height: "2.5rem", padding: "0 0.2rem" }}>
        <Button
          key={"CANCEL"}
          variant="contained"
          size="small"
          onClick={() => {
            props.onClose("CANCEL");
          }}
          sx={{ height: "1.8rem", width: "3.5rem", textTransform: "none", fontWeight: "600" }}
          color="primary"
          className="styledWTOutlinedButton styleButton"
        >
          Cancel
        </Button>
        <Button key={"DISCARD"} variant="contained" size="small" onClick={() => props.onClose("DISCARD")} color="primary" className="styleWTPrimaryContainedButton styleButton" sx={{ height: "1.8rem" }}>
          Discard
        </Button>
      </DialogActions>
    </Dialog>
  );
};
export default MessageBoxComponentDiscard;
