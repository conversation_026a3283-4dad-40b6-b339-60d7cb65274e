import {
  Autocomplete,
  Box,
  CardContent,
  Checkbox,
  Grid,
  Select,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { container_Padding, container_columnGap } from "../Common/commonStyles";
import { destination_MaterialMgmt } from "../../destinationVariables";

const InputDetails = (props) => {

  return (
    <div>
      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
            }}
          >
            General Data
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            <Grid container spacing={1}>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Base Unit:
                  </Typography>
                  <Autocomplete
                    sx={{ height: "31px" }}
                    fullWidth
                    size="small"
                    value={props.basicData?.matGroup}
                    onChange={(e, value) => {
                      props?.setBasicData({
                        ...props?.basicData,
                        baseUnit: value,
                      });
                    }}
                    options={props?.dropDownData?.baseUnit ?? []}
                    getOptionLabel={(option) =>
                      `${option?.code} - ${option?.desc}`
                    }
                    renderOption={(props, option) => (
                      <li {...props}>
                        <Typography style={{ fontSize: 12 }}>
                          {option?.code} - {option?.desc}
                        </Typography>
                      </li>
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder=""
                      />
                    )}
                  />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Material Group:
                  </Typography>
                  <Autocomplete
                    sx={{ height: "31px" }}
                    fullWidth
                    size="small"
                    disablePortal

                    value={props.basicData?.matGroup}
                    onChange={(e, value) => {
                      props?.setBasicData({
                        ...props?.basicData,
                        matGroup: value,
                      });
                    }}
                    options={props?.dropDownData?.matGroup ?? []}
                    getOptionLabel={(option) =>
                      `${option?.code} - ${option?.desc}`
                    }
                    renderOption={(props, option) => (
                      <li {...props}>
                        <Typography style={{ fontSize: 12 }}>
                          {option?.code} - {option?.desc}
                        </Typography>
                      </li>
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder=""
                      />
                    )}
                  />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Old Material Group :
                  </Typography>
                  <TextField
                    size="small"
                    onChange={(e) => {
                      props.setBasicData((prev) => {
                        return { ...prev, oldMaterialGroup: e.target.value };
                      });
                    }}
                  />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    External Material Group:
                  </Typography>
                  <Autocomplete
                    sx={{ height: "31px" }}
                    fullWidth
                    size="small"

                    value={props.basicData?.extMatGroup}
                    onChange={(e, value) => {
                      props?.setBasicData({
                        ...props?.basicData,
                        extMatGroup: value,
                      });
                    }}
                    // onChange={(e) => handleMatTypeChange(e)}

                    options={props?.dropDownData?.extMatGroup ?? []}
                    getOptionLabel={
                      (option) =>
                        `${option?.code} - ${option?.desc}`
                      // `${option}`
                    }
                    renderOption={(props, option) => (
                      <li {...props}>
                        <Typography style={{ fontSize: 12 }}>
                          {option?.code} - {option?.desc}
                          {/* {option} */}
                        </Typography>
                      </li>
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder=""
                      />
                    )}
                  />
                  {/* <Typography variant="body2" fontWeight="bold">
                          0101
                        </Typography> */}
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Division:
                  </Typography>
                  <Autocomplete
                    sx={{ height: "31px" }}
                    fullWidth
                    size="small"

                    value={props.basicData?.division}
                    onChange={(e, value) => {
                      props?.setBasicData({
                        ...props?.basicData,
                        division: value,
                      });
                    }}
                    // onChange={(e) => handleMatTypeChange(e)}

                    options={props?.dropDownData?.division ?? []}
                    getOptionLabel={
                      (option) => `${option?.code} -  ${option?.desc}`
                      // `${option}`
                    }
                    renderOption={(props, option) => (
                      <li {...props}>
                        <Typography style={{ fontSize: 12 }}>
                          {option?.code} - {option?.desc}
                          {/* {option} */}
                        </Typography>
                      </li>
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder=""
                      />
                    )}
                  />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Lab/Office:
                  </Typography>
                  <Autocomplete
                    sx={{ height: "31px" }}
                    fullWidth
                    size="small"

                    value={props.basicData?.labOffice}
                    onChange={(e, value) => {
                      props?.setBasicData({
                        ...props?.basicData,
                        labOffice: value,
                      });
                    }}
                    // onChange={(e) => handleMatTypeChange(e)}

                    options={props?.dropDownData?.labOffice ?? []}
                    getOptionLabel={
                      (option) =>
                        `${option?.code} - ${option?.desc}`
                      // `${option}`
                    }
                    renderOption={(props, option) => (
                      <li {...props}>
                        <Typography style={{ fontSize: 12 }}>
                          {option?.code} - {option?.desc}
                          {/* {option} */}
                        </Typography>
                      </li>
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder=""
                      />
                    )}
                  />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Product Allocation:
                  </Typography>
                  <Autocomplete
                    sx={{ height: "31px" }}
                    fullWidth
                    size="small"

                    value={props?.basicData?.prodAllocation}
                    onChange={(e, value) => {
                      props?.setBasicData({
                        ...props?.basicData,
                        prodAllocation: value,
                      });
                    }}
                    // onChange={(e) => handleMatTypeChange(e)}
                    options={props?.dropDownData?.prodAllocation ?? []}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder=""
                      />
                    )}
                  />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Product Hierarchy:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    X-Plant Material Status:
                  </Typography>
                  <Autocomplete
                    sx={{ height: "31px" }}
                    fullWidth
                    size="small"

                    value={props.basicData?.xPlantMatlStatus}
                    onChange={(e, value) => {
                      props?.setBasicData({
                        ...props?.basicData,
                        xPlantMatlStatus: value,
                      });
                    }}
                    // onChange={(e) => handleMatTypeChange(e)}

                    options={props?.dropDownData?.xPlantMatlStatus ?? []}
                    getOptionLabel={
                      (option) =>
                        `${option?.code} - ${option.desc}`
                      // `${option}`
                    }
                    renderOption={(props, option) => (
                      <li {...props}>
                        <Typography style={{ fontSize: 12 }}>
                          {option?.code} - {option?.desc}
                          {/* {option} */}
                        </Typography>
                      </li>
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder=""
                      />
                    )}
                  />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Valid From:
                  </Typography>
                  <TextField size="small" />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Gen Item Category Group:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Authorization Group:
                  </Typography>
                  <TextField
                    size="small"
                    onChange={(e) => {
                      props?.setBasicData((prev) => {
                        return { ...prev, authorizationGroup: e.target.value };
                      });
                    }}
                  />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  Assign Effective Vals:
                </Typography>
                <Checkbox sx={{ padding: 0 }} />
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>

      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
            }}
          >
            Dimensions/ EANS
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            <Grid container spacing={1}>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Gross Weight
                  </Typography>
                  <TextField
                    size="small"
                    onChange={(e) => {
                      props.setBasicData((prev) => {
                        return { ...prev, grossWeight: e.target.value };
                      });
                    }}
                  />
                </Stack>
              </Grid>

              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Weight Unit
                  </Typography>
                  <Autocomplete
                    sx={{ height: "31px" }}
                    fullWidth
                    size="small"

                    value={props.basicData?.weightUnit}
                    onChange={(e, value) => {
                      props?.setBasicData({
                        ...props?.basicData,
                        weightUnit: value,
                      });
                    }}
                    options={props?.dropDownData?.weightUnit ?? []}
                    getOptionLabel={
                      (option) => `${option?.code}  ${option?.desc}`
                      // `${option}`
                    }
                    renderOption={(props, option) => (
                      <li {...props}>
                        <Typography style={{ fontSize: 12 }}>
                          {option?.code} - {option?.desc}
                          {/* {option} */}
                        </Typography>
                      </li>
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder=""
                      />
                    )}
                  />
                </Stack>
              </Grid>

              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Net Weight
                  </Typography>
                  <TextField
                    size="small"
                    onChange={(e) => {
                      props.setBasicData((prev) => {
                        return { ...prev, netWeight: e.target.value };
                      });
                    }}
                  />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Volume
                  </Typography>
                  <TextField
                    size="small"
                    onChange={(e) => {
                      props.setBasicData((prev) => {
                        return { ...prev, volume: e.target.value };
                      });
                    }}
                  />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Volume Unit
                  </Typography>
                  <Autocomplete
                    sx={{ height: "31px" }}
                    fullWidth
                    size="small"

                    value={props.basicData?.volumeUnit}
                    onChange={(e, value) => {
                      props?.setBasicData({
                        ...props?.basicData,
                        volumeUnit: value,
                      });
                    }}
                    // onChange={(e) => handleMatTypeChange(e)}

                    options={props?.dropDownData?.volumeUnit ?? []}
                    getOptionLabel={
                      (option) => `${option?.code}  ${option?.desc}`
                      // `${option}`
                    }
                    renderOption={(props, option) => (
                      <li {...props}>
                        <Typography style={{ fontSize: 12 }}>
                          {option?.code} - {option?.desc}
                          {/* {option} */}
                        </Typography>
                      </li>
                    )}
                    renderInput={(params) => (
                      <TextField {...params} variant="outlined" />
                    )}
                  />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Size/ Dimensions
                  </Typography>
                  <TextField
                    size="small"
                    onChange={(e) => {
                      props.setBasicData((prev) => {
                        return { ...prev, sizeOrDimensionText: e.target.value };
                      });
                    }}
                  />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    EAN/ UPC
                  </Typography>
                  <TextField
                    size="small"
                    onChange={(e) => {
                      props.setBasicData((prev) => {
                        return { ...prev, ean_upc: e.target.value };
                      });
                    }}
                  />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    EAN Category
                  </Typography>
                  <Autocomplete
                    sx={{ height: "31px" }}
                    fullWidth
                    size="small"

                    value={props.basicData?.labOffice}
                    onChange={(e, value) => {
                      props?.setBasicData({
                        ...props?.basicData,
                        eanCategory: value,
                      });
                    }}
                    // onChange={(e) => handleMatTypeChange(e)}

                    options={props?.dropDownData?.eanCategory ?? []}
                    getOptionLabel={
                      (option) => `${option?.code} `
                      // `${option}`
                    }
                    renderOption={(props, option) => (
                      <li {...props}>
                        <Typography style={{ fontSize: 12 }}>
                          {option?.code}
                          {/* {option} */}
                        </Typography>
                      </li>
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder=""
                      />
                    )}
                  />
                </Stack>
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>

      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
            }}
          >
            Packaging Material Data
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            {/* <Grid
              container
              rowSpacing={1}
              spacing={2}
              justifyContent="space-between"
            > */}
            <Grid
              container
              //   rowSpacing={1}
              spacing={1}
              //   justifyContent="space-between"
            >
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Matl. Grp: Pack Matls
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>

              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Ref Mat for Pckg
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>

      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
              margin: "0px !important",
            }}
          >
            Basic Data Texts
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            {/* <Grid
              container
              rowSpacing={1}
              spacing={2}
              justifyContent="space-between"
            > */}
            <Grid
              container
              //   rowSpacing={1}
              spacing={1}
              //   justifyContent="space-between"
            >
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Languages Maintained:
                  </Typography>
                  <TextField
                    size="small"
                    onChange={(e) => {
                      props.setBasicData((prev) => {
                        return { ...prev, language: e.target.value };
                      });
                    }}
                  />
                </Stack>
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>

      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
              margin: "0px !important",
            }}
          >
            Other Data
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            <Grid container spacing={1}>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Prod/insp. memo:
                  </Typography>
                  <TextField size="small" />
                </Stack>
              </Grid>

              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Ind Std Desc:
                  </Typography>
                  <TextField
                    size="small"
                    onChange={(e) => {
                      props.setBasicData((prev) => {
                        return {
                          ...prev,
                          industryStandardName: e.target.value,
                        };
                      });
                    }}
                  />
                </Stack>
              </Grid>

              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Basic Material:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>

              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    MS Book Part Number:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>

              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Medium:
                  </Typography>
                  <Autocomplete
                    sx={{ height: "31px" }}
                    fullWidth
                    size="small"

                    value={props.basicData?.medium}
                    onChange={(e, value) => {
                      props?.setBasicData({
                        ...props?.basicData,
                        medium: value,
                      });
                    }}
                    // onChange={(e) => handleMatTypeChange(e)}

                    options={props?.dropDownData?.medium ?? []}
                    getOptionLabel={
                      (option) => `${option?.code}  ${option?.desc}`
                      // `${option}`
                    }
                    renderOption={(props, option) => (
                      <li {...props}>
                        <Typography style={{ fontSize: 12 }}>
                          {option?.VolumeUnit} - {option?.UnitText}
                          {/* {option} */}
                        </Typography>
                      </li>
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder=""
                      />
                    )}
                  />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  CAD Indicator:
                </Typography>
                <Checkbox sx={{ padding: 0 }} />
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>

      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
            }}
          >
            Environment
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            {/* <Grid
              container
              rowSpacing={1}
              spacing={2}
              justifyContent="space-between"
            > */}
            <Grid
              container
              //   rowSpacing={1}
              spacing={1}
              //   justifyContent="space-between"
            >
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    DG Indicator Profile:
                  </Typography>
                  <Autocomplete
                    sx={{ height: "31px" }}
                    fullWidth
                    size="small"

                    value={props.basicData?.dgIndiProfile}
                    onChange={(e, value) => {
                      props?.setBasicData({
                        ...props?.basicData,
                        dgIndiProfile: value,
                      });
                    }}
                    // onChange={(e) => handleMatTypeChange(e)}

                    options={props?.dropDownData?.dgIndiProfile ?? []}
                    getOptionLabel={
                      (option) => `${option?.code}  ${option?.desc}`
                      // `${option}`
                    }
                    renderOption={(props, option) => (
                      <li {...props}>
                        <Typography style={{ fontSize: 12 }}>
                          {option?.VolumeUnit} - {option?.UnitText}
                          {/* {option} */}
                        </Typography>
                      </li>
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder=""
                      />
                    )}
                  />
                </Stack>
              </Grid>

              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  Environmentally Rlvt:
                </Typography>
                <Autocomplete
                  sx={{ height: "31px" }}
                  fullWidth
                  size="small"
                  value={props.basicData?.dgIndiProfile}
                  onChange={(e, value) => {
                    props?.setBasicData({
                      ...props?.basicData,
                      dgIndiProfile: value,
                    });
                  }}
                  // onChange={(e) => handleMatTypeChange(e)}

                  options={props?.dropDownData?.dgIndiProfile ?? []}
                  getOptionLabel={
                    (option) => `${option?.VolumeUnit}  ${option?.UnitText}`
                    // `${option}`
                  }
                  renderOption={(props, option) => (
                    <li {...props}>
                      <Typography style={{ fontSize: 12 }}>
                        {option?.code} - {option?.desc}
                        {/* {option} */}
                      </Typography>
                    </li>
                  )}
                  renderInput={(params) => (
                    <TextField {...params} variant="outlined" placeholder="" />
                  )}
                />
              </Grid>

              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  In Bulk/Liquid:{" "}
                </Typography>
                <Checkbox sx={{ padding: 0 }} />
              </Grid>
              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  Highly Viscous:
                </Typography>
                <Checkbox sx={{ padding: 0 }} />
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>

      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
            }}
          >
            Segmentation Data
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            {/* <Grid
              container
              rowSpacing={1}
              spacing={2}
              justifyContent="space-between"
            > */}
            <Grid
              container
              //   rowSpacing={1}
              spacing={1}
              //   justifyContent="space-between"
            >
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Segmentation Structure:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Segmentation Strategy:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>

      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",                              
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
            }}
          >
            Design Documents Assigned
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            {/* <Grid
              container
              rowSpacing={1}
              spacing={2}
              justifyContent="space-between"
            > */}
            <Grid
              container
              //   rowSpacing={1}
              spacing={1}
              //   justifyContent="space-between"
            >
              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  No Link:
                </Typography>
                <Checkbox sx={{ padding: 0 }} />
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>

      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
              margin: "0px !important",
            }}
          >
            Design Drawing
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            {/* <Grid
              container
              rowSpacing={1}
              spacing={2}
              justifyContent="space-between"
            > */}
            <Grid
              container
              //   rowSpacing={1}
              spacing={1}
              //   justifyContent="space-between"
            >
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Document:
                  </Typography>
                  <TextField size="small" />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Document Type:
                  </Typography>
                  <TextField size="small" />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Document Versions:
                  </Typography>
                  <TextField size="small" />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Page Number:
                  </Typography>
                  <TextField size="small" />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Document Change Number:
                  </Typography>
                  <TextField size="small" />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Page Format:
                  </Typography>
                  <TextField size="small" />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    No. Sheets:
                  </Typography>
                  <TextField size="small" />
                </Stack>
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>

      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
              margin: "0px !important",
            }}
          >
            Client-Specific Configuration
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            {/* <Grid
              container
              rowSpacing={1}
              spacing={2}
              justifyContent="space-between"
            > */}
            <Grid
              container
              //   rowSpacing={1}
              spacing={1}
              //   justifyContent="space-between"
            >
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Cross-Plant CM:
                  </Typography>
                  <Autocomplete
                  sx={{ height: "31px" }}
                  fullWidth
                  size="small"
                  value={props.basicData?.dgIndiProfile}
                  onChange={(e, value) => {
                    props?.setBasicData({
                      ...props?.basicData,
                      dgIndiProfile: value,
                    });
                  }}
                  // onChange={(e) => handleMatTypeChange(e)}

                  options={props?.dropDownData?.dgIndiProfile ?? []}
                  getOptionLabel={
                    (option) => `${option?.VolumeUnit}  ${option?.UnitText}`
                    // `${option}`
                  }
                  renderOption={(props, option) => (
                    <li {...props}>
                      <Typography style={{ fontSize: 12 }}>
                        {option?.code} - {option?.desc}
                        {/* {option} */}
                      </Typography>
                    </li>
                  )}
                  renderInput={(params) => (
                    <TextField {...params} variant="outlined" placeholder="" />
                  )}
                />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  Material is Configurable:
                </Typography>
                <Checkbox
                  sx={{ padding: 0 }}
                  onChange={(e) => {
                    props.setBasicData((prev) => {
                      return { ...prev, productIsConfigurable: e.target.checked };
                    });
                  }}
                />
              </Grid>
              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  Variant:
                </Typography>
                <Checkbox sx={{ padding: 0 }} />
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>

      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
              margin: "0px !important",
            }}
          >
            Brazil Tax Data
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            {/* <Grid
              container
              rowSpacing={1}
              spacing={2}
              justifyContent="space-between"
            > */}
            <Grid
              container
              //   rowSpacing={1}
              spacing={1}
              //   justifyContent="space-between"
            >
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    ANP Code:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>
      {/* <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
              margin: "0px !important",
            }}
          >
            Product Groups
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            <Grid
              container
              spacing={2}
            >
              <ReusableTable/>
            </Grid>
         
          </CardContent>
        </Box>
      </Grid> */}
      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
              margin: "0px !important",
            }}
          >
            Planning Scenario
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            {/* <Grid
              container
              rowSpacing={1}
              spacing={2}
              justifyContent="space-between"
            > */}
            <Grid
              container
              //   rowSpacing={1}
              spacing={1}
              //   justifyContent="space-between"
            >
              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  Product for Kit-to-Order:
                </Typography>
                <Checkbox sx={{ padding: 0 }} />
              </Grid>
              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  Procure-to-Order:
                </Typography>
                <Checkbox sx={{ padding: 0 }} />
              </Grid>
              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  Push Deployment from Supplier:
                </Typography>
                <Checkbox sx={{ padding: 0 }} />
              </Grid>
              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  Inventory Balancing Not Allowed
                </Typography>
                <Checkbox sx={{ padding: 0 }} />
              </Grid>
              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  Global Stock on Entry Location DRP:
                </Typography>
                <Checkbox sx={{ padding: 0 }} />
              </Grid>
              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  Excl. from Express Shipment:
                </Typography>
                <Checkbox sx={{ padding: 0 }} />
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>
      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
              margin: "0px !important",
            }}
          >
            WM Execution Data
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            {/* <Grid
              container
              rowSpacing={1}
              spacing={2}
              justifyContent="space-between"
            > */}
            <Grid
              container
              //   rowSpacing={1}
              spacing={1}
              //   justifyContent="space-between"
            >
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Handling Indicator:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    WH Material Group:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Warehouse Storage Condition:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Standard HU Type:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Serial Number Profile:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Preferred UOM
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  Pilferable
                </Typography>
                <Checkbox
                  id="IsPilferable"
                  sx={{ padding: 0 }}
                  onChange={(e) => {
                    props.setBasicData((prev) => {
                      return { ...prev, isPilferable: e.target.checked };
                    });
                  }}
                />
              </Grid>
              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  Relevant for HS:
                </Typography>
                <Checkbox
                  sx={{ padding: 0 }}
                  onChange={(e) => {
                    props.setBasicData((prev) => {
                      return { ...prev, isRelevantForHS: e.target.checked };
                    });
                  }}
                />
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>
      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
              margin: "0px !important",
            }}
          >
            Quality Management
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            {/* <Grid
              container
              rowSpacing={1}
              spacing={2}
              justifyContent="space-between"
            > */}
            <Grid
              container
              //   rowSpacing={1}
              spacing={1}
              //   justifyContent="space-between"
            >
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Quarantine Period:
                  </Typography>
                  <TextField size="small" />
                </Stack>
                <Select size="small" sx={{ height: "31px" }} />
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Quality Inspection Group:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>
      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
              margin: "0px !important",
            }}
          >
            Catch Weight Management
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            {/* <Grid
              container
              rowSpacing={1}
              spacing={2}
              justifyContent="space-between"
            > */}
            <Grid
              container
              //   rowSpacing={1}
              spacing={1}
              //   justifyContent="space-between"
            >
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Logistics UOM:
                  </Typography>
                  <TextField size="small" />
                </Stack>
                <Select size="small" sx={{ height: "31px" }} />
              </Grid>

              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    CW Profile for CW Quantity:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Catch Wt. Tolerance Group:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  Catch Weight Relevant:
                </Typography>
                <Checkbox sx={{ padding: 0 }} />
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>
      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
              margin: "0px !important",
            }}
          >
            General Packaging
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            {/* <Grid
              container
              rowSpacing={1}
              spacing={2}
              justifyContent="space-between"
            > */}
            <Grid
              container
              //   rowSpacing={1}
              spacing={1}
              //   justifyContent="space-between"
            >
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Handling Unit Type:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Standard HU Type:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Maximum Capacity:
                  </Typography>
                  <TextField size="small" />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Overcapac. Tol.:
                  </Typography>
                  <TextField size="small" />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Typography variant="body2" color="#777">
                  Varb. Tare Weight:
                </Typography>
                <Checkbox
                  sx={{ padding: 0 }}
                  onChange={(e) => {
                    props.setBasicData((prev) => {
                      return { ...prev, hasVarbTareWeight: e.target.checked };
                    });
                  }}
                />
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>
      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
              margin: "0px !important",
            }}
          >
            Maximum Packaging
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            {/* <Grid
              container
              rowSpacing={1}
              spacing={2}
              justifyContent="space-between"
            > */}
            <Grid
              container
              //   rowSpacing={1}
              spacing={1}
              //   justifyContent="space-between"
            >
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Max. Pack. Length:
                  </Typography>
                  <TextField
                    size="small"
                    onChange={(e) => {
                      props.setBasicData((prev) => {
                        return { ...prev, maxPackLength: e.target.value };
                      });
                    }}
                  />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Max. Pack. Width:
                  </Typography>
                  <TextField
                    size="small"
                    onChange={(e) => {
                      props.setBasicData((prev) => {
                        return { ...prev, maxPackWidth: e.target.value };
                      });
                    }}
                  />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Max. Pack. Height:
                  </Typography>
                  <TextField
                    size="small"
                    onChange={(e) => {
                      props.setBasicData((prev) => {
                        return { ...prev, maxPackHeight: e.target.value };
                      });
                    }}
                  />
                </Stack>
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>
      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 2,
          ...container_Padding,
          ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
              margin: "0px !important",
            }}
          >
            Package Building Setting
          </Typography>
        </Grid>
        <Box>
          <CardContent sx={{ padding: "0", paddingBottom: "0px !important" }}>
            {/* <Grid
              container
              rowSpacing={1}
              spacing={2}
              justifyContent="space-between"
            > */}
            <Grid
              container
              //   rowSpacing={1}
              spacing={1}
              //   justifyContent="space-between"
            >
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Ref. Product for Pkg. Building:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Product Shape:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Product Orientation Profile:
                  </Typography>
                  <Select size="small" sx={{ height: "31px" }} />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Overhang Threshold [%]:
                  </Typography>
                  <TextField size="small" />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Bridge Threshold [%]:
                  </Typography>
                  <TextField size="small" />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Maximum SLope for Bridges[]:
                  </Typography>
                  <TextField size="small" />
                </Stack>
              </Grid>
              <Grid item md={2}>
                <Stack>
                  <Typography variant="body2" color="#777">
                    Absolute Height Threshold:
                  </Typography>
                  <TextField size="small" />
                </Stack>
                <Select size="small" sx={{ height: "31px" }} />
              </Grid>
            </Grid>
            {/* </Grid> */}
          </CardContent>
        </Box>
      </Grid>
    </div>
  );
};

export default InputDetails;
