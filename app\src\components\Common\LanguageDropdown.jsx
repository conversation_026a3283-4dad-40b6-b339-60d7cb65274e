import React, { useEffect, useState } from "react";
import { Grid, FormControl, InputLabel, Select, MenuItem } from "@mui/material";
import axios from "axios";
import { doAjax } from "../Common/fetchService";
import { useSelector, useDispatch } from "react-redux";
import {
    destination_IDM,
    destination_MaterialMgmt,
    destination_ProfitCenter_Mass
  } from "../../../src/destinationVariables";



const LanguageDropdown = ({ formData, handleChange }) => {
  const [dropdownDataSegment, setDropdownDataSegment] = useState([]);

  const getBusinessSegment = () => {
    const hSuccess = (data) => {
      console.log("dataaaaa", data);
      setDropdownDataSegment(data.body); // Store API response in state
    
    };

    const hError = (error) => {
      console.log("Error fetching business segments:", error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getLanguageKey`, 
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    getBusinessSegment();
  }, []);

  return (
    <Grid item xs={6} md={12}>
      <FormControl fullWidth>
        <InputLabel>Language</InputLabel>
        <Select
          value={formData.Language}
          onChange={handleChange("Language")}
        >
          {dropdownDataSegment?.length > 0 ? (
            dropdownDataSegment?.map((item) => (
              <MenuItem key={item.code} value={item.code}>
                {item?.code}
              </MenuItem>
            ))
          ) : (
            <MenuItem disabled>Loading...</MenuItem>
          )}
        </Select>
      </FormControl>
    </Grid>
  );
};

export default LanguageDropdown;
