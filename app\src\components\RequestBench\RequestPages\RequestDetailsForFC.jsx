import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import ReusableTable from "../../Common/ReusableTable";
import moment from "moment";
import BottomNav from "@material/BottomNav";
import useMaterialButtonDT from '@hooks/useMaterialButtonDT';
import { updateSelectedRows, updateUnselectedRows } from '../../../app/payloadSlice';
import { updatePage } from "../../../app/paginationSlice";
import { useLocation } from "react-router-dom";
import { ENABLE_STATUSES, REQUEST_TYPE } from "@constant/enum";
import useDisplayCall from "@hooks/useDisplayCall";

const RequestDetailsForFC = (props) => {
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const updatedFcRows = useSelector((state) => state.payload.fcRows);
  const unselectedRowsData = useSelector((state) => state.payload.unselectedRows);
  const paginationData = useSelector((state) => state.paginationData);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons);
  const taskData = useSelector((state) => state.userManagement.taskData);
  const selectedRows = useSelector((state) => state.payload.selectedRows);
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const requestType = initialPayload?.RequestType || "";
  const { getButtonsDisplay } = useMaterialButtonDT();
  const { getNextDisplayDataForCreate } = useDisplayCall();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  let reqBenchData = location.state;

  useEffect(() => {
    if(taskData?.ATTRIBUTE_1) {
      getButtonsDisplay();
    }
  }, [taskData]);

  useEffect(() => {
    if (updatedFcRows?.length > 0 && !(isreqBench && !ENABLE_STATUSES?.includes(reqBenchData?.reqStatus))) {
      const unselectedRowsSet = new Set(unselectedRowsData.map(row => row.id));
      const allRowIds = updatedFcRows
        .filter(row => !unselectedRowsSet.has(row.id))
        .map(row => row.id);
      dispatch(updateSelectedRows(allRowIds));
    }
    return () => {
      dispatch(updateSelectedRows([]));
      dispatch(updateUnselectedRows([]));
    }
  }, [updatedFcRows, dispatch]);

  const handleSelectionChange = (newSelection) => {
    dispatch(updateSelectedRows(newSelection));
    const unselectedRowsData = updatedFcRows?.filter(
      row => !newSelection.includes(row.id)
    );
    dispatch(updateUnselectedRows(unselectedRowsData));
  };

  const columns = [
    {
      field: "FinanceCostingId",
      headerName: "ID",
      flex: 1,
      hide: true,
    },
    {
      field: "RequestId",
      headerName: "Req ID",
      flex: 1.7,
      editable: false,
    },
    {
      field: "RequestType",
      headerName: "Req Type",
      flex: 1.2,
      editable: false,
    },
    {
      field: "Requester",
      headerName: "Requestor",
      flex: 1.6,
      editable: false,
    },
    {
      field: "CreatedOn",
      headerName: "Created On(SAP)",
      flex: 1.3,
      editable: false,
      valueFormatter: (params) => {
        return params.value ? moment(params.value).format("DD MMM YYYY") : "";
      },
    },
    {
      field: "Material",
      headerName: "Material Number",
      flex: 1.3,
      editable: false,
    },
    {
      field: "MatlType",
      headerName: "Material Type",
      flex: 1,
      editable: false,
    },
    {
      field: "Plant",
      headerName: "Plant",
      flex: 1,
      editable: false,
    },
    {
      field: "FStdPrice",
      headerName: "Standard Price",
      flex: 1,
      editable: false,
    },
    {
      field: "IntlPoPrice",
      headerName: "Initial PO Price",
      flex: 1,
      editable: false,
      valueFormatter: (params) => {
        return params.value ? Number(params.value).toFixed(2) : "";
      },
    },
    {
      field: "PryVendor",
      headerName: "Primary Vendor",
      flex: 1.3,
      editable: false,
    },
    {
      field: "FlagForBOM",
      headerName: "Flag For BOM",
      flex: 1,
      editable: false,
    },
    {
      field: "VolInEA",
      headerName: "Volume EA",
      flex: 1,
      editable: false,
      valueFormatter: (params) => {
        return params.value ? Number(params.value).toFixed(2) : "";
      },
    },
    {
      field: "VolInCA",
      headerName: "Volume CA",
      flex: 1,
      editable: false,
      valueFormatter: (params) => {
        return params.value ? Number(params.value).toFixed(2) : "";
      },
    },
    {
      field: "VolInCAR",
      headerName: "Volume Carton",
      flex: 1,
      editable: false,
      valueFormatter: (params) => {
        return params.value ? Number(params.value).toFixed(2) : "";
      },
    },
    {
      field: "NoOfUnitForCA",
      headerName: "Number Of Unit For CA",
      flex: 1,
      editable: false,
      valueFormatter: (params) => {
        return params.value ? Number(params.value).toFixed(0) : "";
      },
    },
    {
      field: "NoOfUnitForCT",
      headerName: "Number Of Unit For CT",
      flex: 1,
      editable: false,
      valueFormatter: (params) => {
        return params.value ? Number(params.value).toFixed(0) : "";
      },
    },
  ];

  const handlePageChange = (newPage) => {
    dispatch(updatePage(newPage));
  };

  useEffect(() => {
    if (paginationData?.page !== 0 && requestType === REQUEST_TYPE?.FINANCE_COSTING) {
      getNextDisplayDataForCreate();
    }
  }, [paginationData?.page]);

  return (
  <>
      <ReusableTable
        isLoading={isLoading}
        module="FinanceCosting"
        width="100%"
        title="Finance Costing Details"
        rows={updatedFcRows}
        columns={columns}
        getRowIdValue="id"
        hideFooter={false}
        checkboxSelection={!(isreqBench && !ENABLE_STATUSES?.includes(reqBenchData?.reqStatus))}
        disableSelectionOnClick={true}
        tempheight={"calc(100vh - 300px)"}
        selectionModel={selectedRows}
        onRowsSelectionHandler={handleSelectionChange}
        rowCount={paginationData?.totalElements || 0}
        pageSize={100}
        onPageChange={(newPage) => handlePageChange(newPage)}
       
      />
      <BottomNav filteredButtons={filteredButtons} setCompleted={props?.setCompleted}/>
    </>
  );
};

export default RequestDetailsForFC;

