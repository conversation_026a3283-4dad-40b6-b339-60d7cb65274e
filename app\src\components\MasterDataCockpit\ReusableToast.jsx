import React, { useEffect, useRef } from 'react';
import { ToastContainer, toast, Zoom, Flip } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

const ReusableToast = ({ isLoadingToast, type, message, isToastPromise }) => {
  const toastId = useRef(null);

  useEffect(() => {
    if(isToastPromise){
      if (isLoadingToast) {
        if (toastId.current === null) {
          toastId.current = toast.loading('Please wait...', {
            autoClose: false,
            hideProgressBar: false,
            closeOnClick: false,
            pauseOnHover: false,
            draggable: false,
            theme: 'dark',
            position: 'top-center',
            transition: Zoom,
            
          });
        } else {
          toast.update(toastId.current, {
            render: 'Please wait...',
            type: 'default',
            isLoading: true,
            autoClose: false,
            hideProgressBar: false,
            closeOnClick: false,
            pauseOnHover: false,
            draggable: false,
            theme: 'dark',
            position: 'top-center',
            transition: Zoom
          });
        }
      } else if (type === 'success' || type === 'error') {
        if (toastId.current !== null) {
          toast.update(toastId.current, {
            render: message,
            type: type,
            isLoading: false,
            autoClose: 3000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
            theme: 'dark',
            position: 'top-center',
            transition: Flip
          });
          toastId.current = null;
        }
      }
    } else {
      if (type === 'success') {
        toast.success(message, {
          autoClose: 2000,
          theme: 'dark',
          position: 'top-center',
          transition: Zoom
        });
      } else if (type === 'error') {
        toast.error(message, {
          autoClose: 2000,
          theme: 'dark',
          position: 'top-center',
          transition: Zoom
        });
      }
    }  
  }, [isLoadingToast, type, message, isToastPromise]);

  return <ToastContainer
    className="custom-toast-container"
    style={{ minWidth: '300px', width: 'auto', maxWidth: '90%', wordBreak: 'break-word' }}
    limit={1}
  />;
};

export default ReusableToast;
