import { requestHeaderTabs, setAllTabsData } from "@app/tabsDetailsSlice";
import { doAjax } from "@components/Common/fetchService";
import React from "react";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { destination_IDM } from "../destinationVariables";
import { END_POINTS } from "@constant/apiEndPoints";

const useGLRequestHeaderConfig = () => {
  const initialPayload = useSelector((state) => state.generalLedger.payload.requestHeaderData);
  console.log(initialPayload,"initialPayload")
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const dispatch = useDispatch();

const module ="General Ledger";

  const getRequestHeaderTemplateGl = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_FMD_REQUEST_HEADER_CONFIG",
      version: "v2",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_SCENARIO": initialPayload?.RequestType || "Create",
          "MDG_CONDITIONS.MDG_MAT_MODULE_NAME": module
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    // setIsLoading(true);
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        let responseData = data?.data?.result[0]?.MDG_MAT_REQUEST_HEADER_CONFIG;
        const formattedData = responseData
          .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO) // Sort by sequence number
          .map((item) => ({
            fieldName: item.MDG_MAT_UI_FIELD_NAME,
            sequenceNo: item.MDG_MAT_SEQUENCE_NO,
            fieldType: item.MDG_MAT_FIELD_TYPE,
            maxLength: item.MDG_MAT_MAX_LENGTH,
            value: item.MDG_MAT_DEFAULT_VALUE,
            visibility: item.MDG_MAT_VISIBILITY,
            jsonName: item.MDG_MAT_JSON_FIELD_NAME,
          }));

        const requestHeaderObj = { "Header Data": formattedData };
        

        dispatch(
          setAllTabsData({ tab: "Request Header", data: requestHeaderObj })
        );
        dispatch(requestHeaderTabs(requestHeaderObj));
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  return { getRequestHeaderTemplateGl };
};

export default useGLRequestHeaderConfig;
