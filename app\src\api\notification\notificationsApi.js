import { notification<PERSON>pi } from "./NotificationApiService";
import { END_POINTS } from "@constant/apiEndPoints";
import { baseUrl_Notification } from "@data/baseUrl";

export const notificationsApi = notificationApi.injectEndpoints({
  endpoints: (builder) => ({
    getIdentifiers: builder.query({
      query: () =>
        `${baseUrl_Notification}${END_POINTS.EMAIL_CONFIG.POPULATE_APP_IDENTIFIERS_HANA}`,
    }),

    getMailList: builder.query({
      query: () => `${baseUrl_Notification}${END_POINTS.API.MAIL_DEFINATION}`,
    }),

    submitMailTemplate: builder.mutation({
      query: (payload) => {
        const { templateData, ...rest } = payload;
        let method, url;
        if (templateData.emailDefinitionId === "") {
          method = "POST";
          url = `${baseUrl_Notification}${END_POINTS.API.MAIL_DEFINATION}`;
        } else {
          method = "PATCH";
          url = `${baseUrl_Notification}${END_POINTS.API.MAIL_DEFINATION}/${templateData.emailDefinitionId}`;
        }

        return {
          url,
          method,
          body: rest,
        };
      },
    }),
  }),
});

export const {
  useGetIdentifiersQuery,
  useGetMailListQuery,
  useSubmitMailTemplateMutation,
} = notificationsApi;
