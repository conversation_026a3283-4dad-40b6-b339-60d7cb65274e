import { Box, CircularProgress, Fab } from "@mui/material";
import { outermostContainer } from "./commonStyles";
import { Check } from "@mui/icons-material";
import { green } from "@mui/material/colors";

export default function LoadingComponent() {

  return (
    <Box
      sx={{
        ...outermostContainer,
        backgroundColor: "#FAFCFF",
        height: "90vh",
        width: "100%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <Box sx={{ m: 1, position: 'relative' }}>
        <Fab size="large" sx={{pointerEvents:"none", cursor:"default", boxShadow:"none", background:"rgba(0,0,0,0)"}}>
          <img src="favicon.ico" width="28px"/>
        </Fab>
        <CircularProgress
          size={68}
          sx={{
            position: 'absolute',
            top: -6,
            left: -6,
            zIndex: 1,
          }}
        />
      </Box>
    </Box>
  );
}