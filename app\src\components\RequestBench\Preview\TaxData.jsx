import React from 'react';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { TAX_DATA } from "@constant/enum";
import { colors } from "@constant/colors";

const TaxData = ({ taxDataPayload }) => {
  if (!taxDataPayload) {
    return null;
  }
  
  // Handle both direct payload and nested payload structures
  const taxDataSet = taxDataPayload[TAX_DATA.DATASET] || 
                    (taxDataPayload.TaxData && taxDataPayload.TaxData[TAX_DATA.DATASET]);
  
  if (!taxDataSet || taxDataSet.length === 0) {
    return null;
  }

  return (
    <Accordion sx={{ boxShadow: 3 }} expanded={true}>
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        sx={{
          backgroundColor: colors.background.subtle,
          padding: "8px 16px",
          "&:hover": { backgroundColor: colors.background.subtle },
          cursor: "default"
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: "bold", color: colors.text.primary }}>{TAX_DATA.ACCORDION_TITLE}</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <TableContainer component={Paper} sx={{ 
          boxShadow: colors.shadow.light,
          border: `1px solid ${colors.border.light}`
        }}>
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: colors.background.subtle }}>
                <TableCell sx={{ fontWeight: "bold", color: colors.text.primary }}>{TAX_DATA.TABLE_HEADERS.COUNTRY}</TableCell>
                <TableCell sx={{ fontWeight: "bold", color: colors.text.primary }}>{TAX_DATA.TABLE_HEADERS.TAX_TYPE}</TableCell>
                <TableCell sx={{ fontWeight: "bold", color: colors.text.primary }}>{TAX_DATA.TABLE_HEADERS.TAX_CLASS}</TableCell>
                <TableCell sx={{ fontWeight: "bold", color: colors.text.primary }}>{TAX_DATA.TABLE_HEADERS.DESCRIPTION}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {taxDataSet.map((taxEntry, index) => (
                <TableRow key={`${taxEntry[TAX_DATA.FIELDS.COUNTRY]}-${taxEntry[TAX_DATA.FIELDS.TAX_TYPE]}-${index}`}>
                  <TableCell sx={{ fontWeight: "bold" }}>{taxEntry[TAX_DATA.FIELDS.COUNTRY]}</TableCell>
                  <TableCell sx={{ fontWeight: "bold" }}>{taxEntry[TAX_DATA.FIELDS.TAX_TYPE]}</TableCell>
                  <TableCell>{taxEntry[TAX_DATA.FIELDS.SELECTED_TAX_CLASS]?.[TAX_DATA.FIELDS.TAX_CLASS] || "--"}</TableCell>
                  <TableCell>{taxEntry[TAX_DATA.FIELDS.SELECTED_TAX_CLASS]?.[TAX_DATA.FIELDS.TAX_CLASS_DESC] || "--"}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </AccordionDetails>
    </Accordion>
  );
};

export default TaxData;

