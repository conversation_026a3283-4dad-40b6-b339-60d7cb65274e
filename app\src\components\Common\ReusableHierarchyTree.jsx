import React, { useEffect, useMemo, useRef, useState } from "react";
import { TreeView } from "@mui/x-tree-view/TreeView";
import { alpha, styled } from "@mui/material/styles";
import { TreeItem, treeItemClasses } from "@mui/x-tree-view/TreeItem";
import { useSpring, animated } from "@react-spring/web";
import {
  Backdrop,
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Collapse,
  Dialog,
  DialogContent,
  DialogTitle,
  Fade,
  Grid,
  IconButton,
  Paper,
  Skeleton,
  Slide,
  Stack,
  SvgIcon,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { Clear, Delete } from "@mui/icons-material";
import AddLinkIcon from "@mui/icons-material/AddLink";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import PersonIcon from "@mui/icons-material/Person";
import {
  iconButton_SpacingSmall,
  font_Small,
  button_Primary,
  button_Outlined,
  container_Padding,
  button_Marginleft,
  outermostContainer,
  container_filter,
  outermostContainer_Information,
  outerContainer_Information,
} from "./commonStyles";
import {
  setPayloadForNewChange,
  setPayloadForValue,
} from "../../app/editPayloadSlice";
import { setSingleProfitCenterPayloadGI } from "../../app/profitCenterTabsSlice";
import ModeEditIcon from "@mui/icons-material/ModeEdit";
import UndoIcon from "@mui/icons-material/Undo";
import RedoIcon from "@mui/icons-material/Redo";
import CloseIcon from "@mui/icons-material/Close";
import ControlPointDuplicateIcon from "@mui/icons-material/ControlPointDuplicate";
import VisibilityIcon from "@mui/icons-material/Visibility";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import RemoveCircleOutlineIcon from "@mui/icons-material/RemoveCircleOutline";
import RestoreIcon from "@mui/icons-material/Restore";
import EditLocationAltIcon from "@mui/icons-material/EditLocationAlt";
import BackspaceIcon from "@mui/icons-material/Backspace";
import OutputIcon from "@mui/icons-material/Output";
import InputIcon from "@mui/icons-material/Input";
import LoginIcon from "@mui/icons-material/Login";
import LogoutIcon from "@mui/icons-material/Logout";
import DisabledByDefaultIcon from "@mui/icons-material/DisabledByDefault";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import ArrowCircleRightIcon from "@mui/icons-material/ArrowCircleRight";
import ArrowCircleLeftIcon from "@mui/icons-material/ArrowCircleLeft";
import {
  destination_CostCenter,
  destination_GeneralLedger,
  destination_IDM,
  destination_ProfitCenter,
} from "../../destinationVariables";
import { doAjax, promiseAjax } from "./fetchService";
import { modules } from "../ConfigCockpit/UserManagement/Data/data";
import { useSelector, useDispatch } from "react-redux";
import ReusableTable from "../Common/ReusableTable";
import ReusableBackDrop from "./ReusableBackDrop";
const ReusableHierarchyTree = ({
  bigTreeData = [],
  module,
  controllingArea,
  coa,
  group,
  searchTerm,
  treeData,
  buttonAdd,
  buttonRemove,
  buttonDelete,
  buttonReplace,
  buttonChangePerResp,
  buttonIconVisibility,
  handleData,
  handleUpdates,
  handleUpdatesCLData,
  reqType,
  updatedNodeList, //FOR JAVA
  updatedPCList, // FOR JAVA
  updatedDescList, //FOR JAVA
  updateReplaceTagList, //FOR JAVA
  updateReplaceNodeList, //FOR JAVA
  updateDeleteNodeList, //FOR JAVA
  updateEditDescList, //FOR JAVA
  updatePRList,
  updateEditPrList,

  newNodeListPL,
  newTagListPL,
  newDescListPL,
  newReplaceNodeListPL,
  newReplaceTagListPL,
  newDeleteNodeListPL,
  newEditDescListPL,
  newPRListPL,
  newEditPrListPL,

  updatedNewNodeList,
  // updateChangeDescList, //FOR CHANGE LOG

  nodeRows = null,
  PCRows = null,
  descRows = null,
  rowsForMovedNodes = null,
  rowsForReplacePC = null,
  rowsForRemoveNode = null,
  rowsForDeleteNode = null,
  removePCrows = null,
  PrRows = null,

  reqId = null,

  updateNodeListDb = null,
  updateDescListDb = null,

  updateGlViewList = null,
  updateGlViewDialog = null,
  updateRange = null,
}) => {
  const [newTreeData, setNewTreeData] = useState(treeData);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  console.log("ntd", newTreeData[0]?.title, newTreeData);
  //   const [newTreeData, setNewTreeData] = useState([
  //     {
  //         "id": "1_1",
  //         "title": "TUK1-DMC",
  //         "description": "",
  //         "personResponsible": "",
  //         "tags": [],
  //         "child": [
  //             {
  //                 "id": "1_1_1",
  //                 "title": "TUK1-DSC2",
  //                 "description": "",
  //                 "personResponsible": "",
  //                 "tags": [],
  //                 "child": [
  //                     {
  //                         "id": "1_1_1_1",
  //                         "title": "TUK1-AMC1",
  //                         "description": "",
  //                         "personResponsible": "",
  //                         "tags": [],
  //                         "child": []
  //                     },
  //                     {
  //                         "id": "1_1_1_2",
  //                         "title": "TUK1-AMC2",
  //                         "description": "",
  //                         "personResponsible": "",
  //                         "tags": [
  //                             "TUK1-PUK3"
  //                         ],
  //                         "child": []
  //                     }
  //                 ]
  //             },
  //             {
  //                 "id": "1_1_2",
  //                 "title": "TUK1-DSC1",
  //                 "description": "",
  //                 "personResponsible": "",
  //                 "tags": [],
  //                 "child": []
  //             },
  //             {
  //                 "id": "1_1_3",
  //                 "title": "TUK1-DMC4",
  //                 "description": "TEST",
  //                 "personResponsible": "",
  //                 "tags": [],
  //                 "child": []
  //             }
  //         ]
  //     },
  //     {
  //         "id": "1_2",
  //         "title": "TUK1-UTILI",
  //         "description": "Utilities for TUK1",
  //         "personResponsible": "",
  //         "tags": [
  //             "OIL EXPENS",
  //             "PC000034",
  //             "PC000195",
  //             "PC0002314",
  //             "TUK1 ENERG",
  //             "TUK1 GAS",
  //             "TUK1 WATER",
  //             "TUK1_ENERG",
  //             "TUK1_FUEL"
  //         ],
  //         "child": [
  //             {
  //                 "id": "1_2_1",
  //                 "title": "QWERTY",
  //                 "description": "Test",
  //                 "personResponsible": "",
  //                 "tags": [],
  //                 "child": []
  //             }
  //         ]
  //     },
  //     {
  //         "id": "1_3",
  //         "title": "TUK1-PRODU",
  //         "description": "Production for TUK1",
  //         "personResponsible": "",
  //         "tags": [
  //             "INC3245",
  //             "MOTOR EXP.",
  //             "PC000015",
  //             "PC000052",
  //             "PTUK144449",
  //             "PTUK144489",
  //             "PTUK185185",
  //             "PTUK1TUK12",
  //             "PTUK454655",
  //             "PTUK454657",
  //             "PTUK454658",
  //             "TUK16677",
  //             "TUK19999",
  //             "TUK1TRADE",
  //             "TUK1_7899"
  //         ],
  //         "child": [
  //             {
  //                 "id": "1_3_1",
  //                 "title": "TUK1TFAIR",
  //                 "description": "Trade air for TUK1",
  //                 "personResponsible": "",
  //                 "tags": [],
  //                 "child": []
  //             },
  //             {
  //                 "id": "1_3_2",
  //                 "title": "PREGUS0004",
  //                 "description": "Profit Center PREGUS0004",
  //                 "personResponsible": "",
  //                 "tags": [
  //                     "P9100000",
  //                     "PC000010",
  //                     "PC003454"
  //                 ],
  //                 "child": []
  //             },
  //             {
  //                 "id": "1_3_3",
  //                 "title": "PREGUS0005",
  //                 "description": "Profit Center PREGUS0005",
  //                 "personResponsible": "",
  //                 "tags": [
  //                     "PC000011",
  //                     "PRC1",
  //                     "PTUK1O7988"
  //                 ],
  //                 "child": []
  //             },
  //             {
  //                 "id": "1_3_4",
  //                 "title": "PREGUS0006",
  //                 "description": "Profit Center PREGUS0006",
  //                 "personResponsible": "",
  //                 "tags": [
  //                     "PC000013",
  //                     "PTUK157757"
  //                 ],
  //                 "child": []
  //             },
  //             {
  //                 "id": "1_3_5",
  //                 "title": "PREGUS0007",
  //                 "description": "Profit Center PREGUS0007",
  //                 "personResponsible": "",
  //                 "tags": [
  //                     "PC000014",
  //                     "PC000016",
  //                     "PC000017",
  //                     "PC000018",
  //                     "PRODUCT-M",
  //                     "PTUK112563"
  //                 ],
  //                 "child": []
  //             },
  //             {
  //                 "id": "1_3_6",
  //                 "title": "PREGUS0008",
  //                 "description": "Profit Center PREGUS0008",
  //                 "personResponsible": "",
  //                 "tags": [
  //                     "PC000012",
  //                     "PTUK1HGSHD",
  //                     "PTUK1TUK19",
  //                     "TUK109804",
  //                     "TUK16678",
  //                     "TUK178553",
  //                     "TUK1_23477",
  //                     "TUK1_4342",
  //                     "TUK1_7855",
  //                     "TUK1_7856",
  //                     "TUK1_7898",
  //                     "TUK1_9890",
  //                     "TUK1_9891"
  //                 ],
  //                 "child": []
  //             }
  //         ]
  //     },
  //     {
  //         "id": "1_4",
  //         "title": "PCSH-TUK89",
  //         "description": "Profit Center Group 2",
  //         "personResponsible": "",
  //         "tags": [],
  //         "child": []
  //     },
  //     {
  //         "id": "1_5",
  //         "title": "PCSH-TUK88",
  //         "description": "Profit Center Group1",
  //         "personResponsible": "",
  //         "tags": [],
  //         "child": []
  //     },
  //     {
  //         "id": "1_6",
  //         "title": "PCSH-TUK90",
  //         "description": "Profit Center Group 3",
  //         "personResponsible": "",
  //         "tags": [],
  //         "child": []
  //     },
  //     {
  //         "id": "1_7",
  //         "title": "PCSH-TUK91",
  //         "description": "Profit Center Group 3",
  //         "personResponsible": "",
  //         "tags": [],
  //         "child": []
  //     },
  //     {
  //         "id": "1_8",
  //         "title": "PCSH-TUK92",
  //         "description": "Profit Center Group 6",
  //         "personResponsible": "",
  //         "tags": [],
  //         "child": []
  //     },
  //     {
  //         "id": "1_9",
  //         "title": "INCTURE",
  //         "description": "INCTURE",
  //         "personResponsible": "",
  //         "tags": [],
  //         "child": [
  //             {
  //                 "id": "1_9_1",
  //                 "title": "INC-BBSR",
  //                 "description": "INCTURE1",
  //                 "personResponsible": "",
  //                 "tags": [
  //                     "PTUK112121"
  //                 ],
  //                 "child": [
  //                     {
  //                         "id": "1_9_1_1",
  //                         "title": "INC-STPI",
  //                         "description": "INCTURE2",
  //                         "personResponsible": "",
  //                         "tags": [],
  //                         "child": []
  //                     },
  //                     {
  //                         "id": "1_9_1_2",
  //                         "title": "INC-FORT",
  //                         "description": "INCTURE3",
  //                         "personResponsible": "",
  //                         "tags": [],
  //                         "child": [
  //                             {
  //                                 "id": "1_9_1_2_1",
  //                                 "title": "INC-TIGER1",
  //                                 "description": "",
  //                                 "personResponsible": "",
  //                                 "tags": [],
  //                                 "child": [
  //                                     {
  //                                         "id": "1_9_1_2_1_1",
  //                                         "title": "INC-TIGER2",
  //                                         "description": "",
  //                                         "personResponsible": "",
  //                                         "tags": [],
  //                                         "child": [
  //                                             {
  //                                                 "id": "1_9_1_2_1_1_1",
  //                                                 "title": "INC-TIGER3",
  //                                                 "description": "",
  //                                                 "personResponsible": "",
  //                                                 "tags": [
  //                                                     "P116094756"
  //                                                 ],
  //                                                 "child": []
  //                                             }
  //                                         ]
  //                                     }
  //                                 ]
  //                             }
  //                         ]
  //                     }
  //                 ]
  //             },
  //             {
  //                 "id": "1_9_2",
  //                 "title": "INC-BBL",
  //                 "description": "INCTURE4",
  //                 "personResponsible": "",
  //                 "tags": [],
  //                 "child": []
  //             },
  //             {
  //                 "id": "1_9_3",
  //                 "title": "INC-PUNE",
  //                 "description": "INCTURE5",
  //                 "personResponsible": "",
  //                 "tags": [],
  //                 "child": []
  //             },
  //             {
  //                 "id": "1_9_4",
  //                 "title": "INC-KOLK",
  //                 "description": "Incture Kolkata",
  //                 "personResponsible": "",
  //                 "tags": [
  //                     "PTUK187656",
  //                     "PTUK1YASH1"
  //                 ],
  //                 "child": []
  //             }
  //         ]
  //     }
  // ]);
  const [showInputFields, setShowInputFields] = useState({});
  const [showInputFieldsForPC, setShowInputFieldsForPC] = useState({});
  const [showInputFieldsForRangeGL, setShowInputFieldsForRangeGL] = useState(
    {}
  );

  console.log("showInputFields", showInputFields, showInputFieldsForPC);
  const [newPcValues, setNewPcValues] = useState({});
  const [editableNodes, setEditableNodes] = useState({});
  const [nodeValues, setNodeValues] = useState({});
  const [personResponsibleField, setPersonResponsibleField] = useState(false);
  const [descriptionField, setDescriptionField] = useState(false);
  const [descriptionValues, setDescriptionValues] = useState({});
  const [editingNodeId, setEditingNodeId] = useState(null);
  const [personResponsibleValues, setPersonResponsibleValues] = useState({});

  const [newNodeList, setNewNodeList] = useState(newNodeListPL);
  const [newTagList, setNewTagList] = useState(newTagListPL);
  const [newDescList, setNewDescList] = useState(newDescListPL);
  // console.log("newDescList", newDescList, newNodeList, newTagList);
  const [newReplaceNodeList, setNewReplaceNodeList] =
    useState(newReplaceNodeListPL);
  const [newDeleteNodeList, setNewDeleteNodeList] =
    useState(newDeleteNodeListPL);
  const [newReplaceTagList, setNewReplaceTagList] =
    useState(newReplaceTagListPL);
  const [changeDescription, setChangeDescription] = useState(newEditDescListPL);
  const [newPRList, setNewPRList] = useState(newPRListPL);
  const [changePR, setChangePR] = useState(newEditPrListPL);
  const [loaderMessage, setLoaderMessage] = useState("");
  // console.log("newPRList", newPRList, changePR);
  // console.log("cccd", changeDescription, newEditDescListPL);
  // console.log("reqType", reqType)

  const [childsOfMovedNodes, setChildsOfMovedNodes] = useState([]);
  const [childTagsOfMovedNodes, setChildTagsOfMovedNodes] = useState([]);

  const dispatch = useDispatch();

  const [timerId, setTimerId] = useState(null);
  const [personResponsibleSearch, setPersonResponsibleSearch] = useState("");
  const [personResponsibleData, setPersonResponsibleData] = useState([]);
  // console.log("personResponsibleSearch", personResponsibleSearch);
  // console.log(personResponsibleData, setPersonResponsibleData);
  const [openPersonResponsibleDialog, setOpenPersonResponsibleDialog] =
    useState(false);

  const [newPrNodeId, setNewPrNodeId] = useState(null);
  console.log("newPrNodeId", newPrNodeId);

  const [count, setCount] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [skip, setSkip] = useState(0);

  const [inputValueNewNode, setInputValueNewNode] = useState("");
  const inputRefNewNode = useRef(null);

  const [glViewList, setGlViewList] = useState([]);

  useEffect(() => {
    if (inputRefNewNode.current) {
      inputRefNewNode.current.focus(); // Set focus on the TextField whenever necessary
    }
  }, [inputValueNewNode]);

  const [requiredLengthError, setRequiredLengthError] = useState(false);
  const [invalidTagError, setInvalidTagError] = useState(false);

  useEffect(() => {
    if (!newReplaceNodeList || reqType === "Create") {
      return;
    }
    const extractedValues = splitAndExtract(newReplaceNodeList);
    // console.log("extractedValues", extractedValues);

    const { collectedChildTitles, collectedChildTags } =
      findNodesAndCollectChildren(newTreeData, extractedValues);
    // console.log("collectedChildTitles", collectedChildTitles);
    // console.log("collectedChildTags", collectedChildTags);

    setChildsOfMovedNodes(collectedChildTitles);
    setChildTagsOfMovedNodes(collectedChildTags);
  }, [newReplaceNodeList, newTreeData]);

  const splitAndExtract = (list) => {
    return list.map((item) => item.split("$$")[1]);
  };

  const collectAllChildTitles = (node) => {
    let titles = [];
    node.child.forEach((child) => {
      titles.push(child.title);
      titles = titles.concat(collectAllChildTitles(child));
    });
    return titles;
  };

  const collectAllChildTags = (node) => {
    let tags = [];
    node.child.forEach((child) => {
      tags = tags.concat(child.tags);
      tags = tags.concat(collectAllChildTags(child));
    });
    return tags;
  };

  const findNodesAndCollectChildren = (treeData, searchValues) => {
    let collectedChildTitles = [];
    let collectedChildTags = [];

    const traverseTree = (node) => {
      if (searchValues.includes(node.title)) {
        collectedChildTitles = collectedChildTitles.concat(
          collectAllChildTitles(node)
        );
        collectedChildTags = collectedChildTags.concat(
          collectAllChildTags(node)
        );
      }
      if (node.child) {
        node.child.forEach((child) => traverseTree(child));
      }
    };

    treeData.forEach((rootNode) => traverseTree(rootNode));

    return { collectedChildTitles, collectedChildTags };
  };

  // console.log("childsOfMovedNodes", childsOfMovedNodes);
  // console.log("childTagsOfMovedNodes", childTagsOfMovedNodes);

  const [movedNodes, setMovedNodes] = useState([]);
  const [tagPickedNodes, setTagPickedNodes] = useState([]);
  // console.log("tagPickedNodes", movedNodes, tagPickedNodes);
  const [checked, setChecked] = useState(false);
  const [storedNodeData, setStoredNodeData] = useState(null);
  const [storedNodeParentData, setStoredNodeParentData] = useState(null);
  // console.log("storedNodeData", storedNodeData, storedNodeParentData)
  const [blurLoading, setBlurLoading] = useState(false);
  const [newAddedNode, setNewAddedNode] = useState([]);
  const [newDescriptionAdded, setNewDescriptionAdded] = useState([]);
  // console.log("ccccd", changeDescription);
  const [moveNodeList, setMoveNodeList] = useState([]);
  const [restoreMoveNode, setRestoreMoveNode] = useState([]);
  const [removePCData, setRemovePCData] = useState([]);
  const [removeNodeData, setRemoveNodeData] = useState([]);
  const [deleteNodeData, setDeleteNodeData] = useState([]);
  const [storeMoveTagList, setStoreMoveTagList] = useState([]);
  const [restoreMoveTagList, setRestoreMoveTagList] = useState([]);
  const [currentNodeAdded, setCurrentNodeAdded] = useState([]);
  const [currentTagsAdded, setCurrentTagsAdded] = useState([]);
  // console.log("descriptionValues", newDescriptionAdded);
  // console.log(
  //   "newTagList",
  //   newTagList,
  //   newDescList,
  //   newNodeList,
  //   newReplaceNodeList,
  //   newReplaceTagList,
  //   newDeleteNodeList
  // );
  const [duplicateNodeError, setDuplicateNodeError] = useState(false);
  const [DBduplicateNodeError, setDBDuplicateNodeError] = useState(false);
  const [DBduplicateDescError, setDBDuplicateDescError] = useState(false);
  const [duplicateTagError, setDuplicateTagError] = useState(false);
  const [duplicateDescError, setDuplicateDescError] = useState(false);

  const [errorMessage, setErrorMessage] = useState("");

  const [objDesc, setObjDesc] = useState("");
  const [pcDescriptions, setPcDescriptions] = useState({});


  console.log("bigTreeData", bigTreeData);
  // console.log("duplicateNodeError", duplicateNodeError);
  const [movedNodeId, setMovedNodeId] = useState(null);
  let userData = useSelector((state) => state.userManagement.userData);

 
  useEffect(() => {
    // setNewTreeData(treeData);
    // PREVIOUSLY FOR CL
    // updateChangeDescList(newDescriptionAdded);
    // updatedNewNodeList(newAddedNode);

    // FOR BACKEND
    updatedNodeList(newNodeList);
    updatedPCList(newTagList);
    updatedDescList(newDescList);
    updateReplaceTagList(newReplaceTagList);
    updateReplaceNodeList(newReplaceNodeList);
    if (updateDeleteNodeList) {
      updateDeleteNodeList(newDeleteNodeList);
    }
    if (updateEditDescList) {
      updateEditDescList(changeDescription);
    }
    if (updatePRList) {
      updatePRList(newPRList);
    }
    if (updateEditPrList) {
      updateEditPrList(changePR);
    }
    // handleUpdates("UPDATEMOVENODE", moveNodeList);
    // handleUpdates("RESTOREMOVENODE", restoreMoveNode);
    // handleUpdates("REMOVEPCDATA", removePCData);
    // handleUpdates("REMOVENODEDATA", removeNodeData);
    // handleUpdates("DELETENODEDATA", deleteNodeData);
    // handleUpdates("STOREMOVETAGLIST", storeMoveTagList);
    // handleUpdates("RESTOREMOVETAGLIST", restoreMoveTagList);
    // handleUpdates("CHANGEDESCRIPTION", changeDescription);
  }, [newTreeData]);

  // useEffect(() => {
  //   handleUpdatesCLData("NEWNODESCLDATA", rowData);
  // }, [treeData])

  // useEffect(() => {
  //   console.log("fetchPc");
  //   const fetchInitialDescriptions = async () => {
  //     const tagsToFetch = [];

  //     // Traverse the tree to gather all tags (PCs) without descriptions
  //     const traverseTreeData = (node) => {
  //       if (node.tags && node.tags.length > 0) {
  //         node.tags.forEach(tag => {
  //           const uniqueTagKey = `${node.id}-${tag}`;
  //           console.log("uniqueTagKey", uniqueTagKey);
  //           if (!pcDescriptions[uniqueTagKey]) {
  //             tagsToFetch.push({ nodeId: node.id, tag });
  //           }
  //         });
  //       }
  //       if (node.child) {
  //         node.child.forEach(chil => traverseTreeData(chil));
  //       }
  //     };

  //     traverseTreeData(newTreeData[0]);
  //     console.log("tagsToFetch", tagsToFetch);

  //     if (tagsToFetch.length > 0) {
  //       // Fetch descriptions for all existing tags
  //       const fetchDescriptions = async ({ nodeId, tag }) => {
  //         let payload = {};
  //         let url = "";
  //         if (module == "Profit Center") {
  //           payload = {
  //             classValue: "0106",
  //             controllingArea: controllingArea,
  //             hierarchyGrp: group,
  //             pc: tag,
  //           };
  //           url = `/${destination_ProfitCenter}/node/pcDuplicacyCheckForPCG`;
  //         } else if (module == "Cost Center") {
  //           payload = {
  //             classValue: "0101",
  //             controllingArea: controllingArea,
  //             hierarchyGrp: group,
  //             cc: tag,
  //           };
  //           url = `/${destination_CostCenter}/node/ccDuplicacyCheckForCCG`;
  //         } else if (module == "General Ledger") {
  //           payload = {
  //             classValue: "0102",
  //             chartOfAccount: coa,
  //             hierarchyGrp: group,
  //             gl: tag,
  //           };
  //           url = `/${destination_GeneralLedger}/node/glDuplicacyCheckForCEG`;
  //         }
  //         // let data = {
  //         //   "statusCode": 200,
  //         //   "message": "Duplicacy check done for PC/s",
  //         //   "body": {
  //         //       "PresentInHier": "",
  //         //       "Description": "********-SUNOCO #2401",
  //         //       "PresentInCA": "X"
  //         //   },
  //         //   "screenName": null
  //         // };
  //         const result = await promiseAjax(url, "post", payload);
  //         const data = await result.json();

  //         return { nodeId, tag, description: data.body.Description };
  //       };

  //       const results = await Promise.all(tagsToFetch.map(fetchDescriptions));

  //       // Store the descriptions in state
  //       const initialPcDescriptions = results.reduce((acc, { nodeId, tag, description }) => {
  //         acc[`${nodeId}-${tag}`] = description;
  //         return acc;
  //       }, {});

  //       setPcDescriptions(initialPcDescriptions);
  //     }
  //   };

  //   fetchInitialDescriptions();
  // }, []);

  // console.log(
  //   "UPDATEMOVENODE",
  //   newNodeList,
  //   newTagList,
  //   newDescList,
  //   newReplaceTagList,
  //   newReplaceNodeList,
  //   newDeleteNodeList
  // );

  const handleClosePersonReponsibleDialog = () => {
    setOpenPersonResponsibleDialog(false);
  };

  const handleOpenTable = () => {
    setOpenPersonResponsibleDialog(true);
  };

  const columnsTable = [
    {
      field: "EmplId",
      headerName: "Emp Id",
      type: "text",
      editable: "false",
      width: 100,
    },
    {
      field: "Status",
      headerName: "Status",
      width: 100,
      editable: "false",
    },
    {
      field: "FirstName",
      headerName: "First Name",
      type: "text",
      editable: "false",
      width: 100,
    },
    {
      field: "MiddleName",
      headerName: "Middle Name",
      width: 100,
      editable: "false",
    },
    {
      field: "LastName",
      headerName: "Last Name",
      width: 100,
      editable: "false",
    },
    {
      field: "OrgorCC",
      headerName: "OrgorCC",
      width: 100,
      editable: "false",
    },
    {
      field: "PositionDesc",
      headerName: "Position",
      width: 100,
      editable: "false",
    },
    {
      field: "NetworkId",
      headerName: "Network ID",
      width: 100,
      editable: "false",
    },
  ];

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };
  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
    setSkip(0);
  };

  const onEdit = (label, newValue, newPrNodeId) => {
    console.log("newlabel", newValue, label, newPrNodeId);
    dispatch(
      setSingleProfitCenterPayloadGI({
        keyname: label
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: newValue,
      })
    );
    dispatch(
      setPayloadForNewChange({
        keyname: label
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: newValue,
      })
    );
    dispatch(
      setPayloadForValue({
        keyname: label
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: newValue,
      })
    );
    handleAddPersonResponsible(newPrNodeId, newValue);
  };

  function MinusSquare(props) {
    return (
      <SvgIcon
        fontSize="inherit"
        style={{ width: 15, height: 50, color: "#f58442" }}
        {...props}
      >
        {/* tslint:disable-next-line: max-line-length */}

        <path d="M22.047 22.074v0 0-20.147 0h-20.12v0 20.147 0h20.12zM22.047 24h-20.12q-.803 0-1.365-.562t-.562-1.365v-20.147q0-.776.562-1.351t1.365-.575h20.147q.776 0 1.351.575t.575 1.351v20.147q0 .803-.575 1.365t-1.378.562v0zM17.873 11.023h-11.826q-.375 0-.669.281t-.294.682v0q0 .401.294 .682t.669.281h11.826q.375 0 .669-.281t.294-.682v0q0-.401-.294-.682t-.669-.281z" />
      </SvgIcon>
    );
  }

  function PlusSquare(props) {
    return (
      <SvgIcon
        fontSize="inherit"
        style={{ width: 15, height: 50, color: "#4aba50" }}
        {...props}
      >
        {/* tslint:disable-next-line: max-line-length */}

        <path d="M22.047 22.074v0 0-20.147 0h-20.12v0 20.147 0h20.12zM22.047 24h-20.12q-.803 0-1.365-.562t-.562-1.365v-20.147q0-.776.562-1.351t1.365-.575h20.147q.776 0 1.351.575t.575 1.351v20.147q0 .803-.575 1.365t-1.378.562v0zM17.873 12.977h-4.923v4.896q0 .401-.281.682t-.682.281v0q-.375 0-.669-.281t-.294-.682v-4.896h-4.923q-.401 0-.682-.294t-.281-.669v0q0-.401.281-.682t.682-.281h4.923v-4.896q0-.401.294-.682t.669-.281v0q.401 0 .682.281t.281.682v4.896h4.923q.401 0 .682.281t.281.682v0q0 .375-.281.669t-.682.294z" />
      </SvgIcon>
    );
  }

  function CloseSquare(props) {
    return (
      <SvgIcon
        className="close"
        fontSize="inherit"
        style={{ width: 14, height: 50 }}
        {...props}
      >
        {/* tslint:disable-next-line: max-line-length */}

        <path d="M17.485 17.512q-.281.281-.682.281t-.696-.268l-4.12-4.147-4.12 4.147q-.294.268-.696.268t-.682-.281-.281-.682.294-.669l4.12-4.147-4.12-4.147q-.294-.268-.294-.669t.281-.682.682-.281.696 .268l4.12 4.147 4.12-4.147q.294-.268.696-.268t.682.281 .281.669-.294.682l-4.12 4.147 4.12 4.147q.294.268 .294.669t-.281.682zM22.047 22.074v0 0-20.147 0h-20.12v0 20.147 0h20.12zM22.047 24h-20.12q-.803 0-1.365-.562t-.562-1.365v-20.147q0-.776.562-1.351t1.365-.575h20.147q.776 0 1.351.575t.575 1.351v20.147q0 .803-.575 1.365t-1.378.562v0z" />
      </SvgIcon>
    );
  }

  function TransitionComponent(props) {
    const style = useSpring({
      to: {
        opacity: props.in ? 1 : 0,

        transform: `translate3d(${props.in ? 0 : 20}px,0,0)`,
      },
    });

    return (
      <animated.div style={style}>
        <Collapse {...props} />
      </animated.div>
    );
  }

  const CustomTreeItem = React.forwardRef((props, ref) => (
    <TreeItem {...props} TransitionComponent={TransitionComponent} ref={ref} />
  ));

  // const StyledTreeItem = styled(CustomTreeItem)(({ theme }) => ({
  //   [`& .${treeItemClasses.iconContainer}`]: {
  //     "& .close": {
  //       opacity: 0.3,
  //     },
  //   },

  //   [`& .${treeItemClasses.group}`]: {
  //     marginLeft: 15,
  //     paddingLeft: 18,
  //     borderLeft: `1px dashed ${alpha(theme.palette.text.primary, 0.4)}`,
  //   },
  // }));

  const StyledTreeItem = styled(TreeItem)(({ theme }) => ({
    "& .MuiTreeItem-content": {
      padding: theme.spacing(0.5),
      position: "relative",
      display: "flex",
      alignItems: "center",
      "&.Mui-selected": {
        backgroundColor: `1px dashed${(theme.palette.action.selected, 0.6)}`,
      },
      "&::before": {
        content: '""',
        position: "absolute",
        top: "50%",
        left: "-20px",
        width: "15px",
        height: "1px",
        backgroundColor: theme.palette.text.primary,
      },
      "&::after": {
        content: '""',
        position: "absolute",
        top: "50%",
        left: "-10px",
        width: "15px",
        height: "1px",
        backgroundColor: theme.palette.text.primary,
        transform: "rotate(90deg)",
      },
    },
    "& .MuiTreeItem-group": {
      marginLeft: theme.spacing(2),
      paddingLeft: theme.spacing(2),
      borderLeft: `1px dashed ${alpha(theme.palette.text.primary, 0.6)}`,
    },
    "& .sticky": {
      position: "sticky",
      top: 0,
      backgroundColor: theme.palette.background.paper,
      zIndex: 1,
    },
  }));

  // const MemoizedTreeItem = React.memo(StyledTreeItem);

  //add node at top level
  const handleAddNodeForParent = (value) => {
    setNewTreeData((prev) => [
      ...prev,

      {
        id: (prev.length + 1).toString(),
        tags: [],
        title: value,
        child: [],
        description: "",
        personResponsible: "",
      },
    ]);
  };

  const addNode = (parentId, newNode, newTreeData) => {
    return newTreeData.map((item) => {
      if (item.title === parentId) {
        item.child.push(newNode);
      } else if (item.child.length > 0) {
        item.child = addNode(parentId, newNode, item.child);
      }

      return item;
    });
  };

  const findTitle = (node, title) => {
    // console.log('checktree', node,title)
    // console.log("pleasecheck", node.title);
    // console.log(`Checking node: ${node.title}`); // Debugging line
    if (node.title === title) {
      return true;
    }
    if (node.child && node.child.length > 0) {
      for (let i = 0; i < node.child.length; i++) {
        if (findTitle(node.child[i], title)) {
          return true;
        }
      }
    }
    return false;
  };

  const [rowData, setRowData] = useState([]);

  const handleAddNode = async (parentId, newNodeTitle, pParentId) => {
    console.log("parentid", parentId, newNodeTitle);
    setBlurLoading(true);
    const checkForNodeDuplicacy = async (newNodeTitle) => {
      let url = "";
      let payload = {};
      if (module == "Profit Center") {
        payload = {
          requestId: reqId,
          controllingArea: controllingArea,
          hierarchyGrp: group,
          node: newNodeTitle,
        };
        url = `/${destination_ProfitCenter}/node/nodeDuplicacyCheckForPCG`;
      } else if (module == "Cost Center") {
        payload = {
          requestId: reqId,
          controllingArea: controllingArea,
          hierarchyGrp: group,
          node: newNodeTitle,
        };
        url = `/${destination_CostCenter}/node/nodeDuplicacyCheckForCCG`;
      } else if (module == "General Ledger") {
        payload = {
          requestId: reqId,
          chartOfAccount: coa,
          hierarchyGrp: group,
          node: newNodeTitle,
        };
        url = `/${destination_GeneralLedger}/node/nodeDuplicacyCheckForCEG`;
      }

      const result = await promiseAjax(url, "post", payload);
      const res = await result.json();

      console.log("Okhi", res);

      return res;
    };

    const findTitleInUITree = findTitle(newTreeData[0], newNodeTitle);
    console.log("moddd", module, coa, group);
    let data = await checkForNodeDuplicacy(newNodeTitle);
    console.log("checkdata", data, findTitleInUITree);
    if (
      data.body.PresentInHier === "X" ||
      data.body.PresentInCA === "X" ||
      data.body?.PresentInCOA === "X" ||
      findTitleInUITree
    ) {
      setDuplicateNodeError(true);
      setBlurLoading(false);
    } else {
      setDuplicateNodeError(false);
      if(data?.body?.isDbDuplicate === true){
        setDBDuplicateNodeError(true);
        setBlurLoading(false);
      }
      else{
        setDBDuplicateNodeError(false);
        if (newNodeTitle.trim() !== "") {
          const newNode = {
            title: newNodeTitle,
            tags: [],
            child: [],
            description: "",
            personResponsible: "",
          };
          let parentIdStart = parentId.split("_")[0];
          let temp = newTreeData.map((element) => {
            if (element.id.split("_")[0] == parentIdStart) {
              let newdata = addData(element, parentId, newNode);
              // console.log("newdata", newdata);
              return newdata;
            }
            return element;
          });
          const parent = findNode(newTreeData, parentId);
          // console.log("parent3", parent);
          // console.log("temp", temp);
          setNewTreeData(temp);
          setShowInputFields({ ...showInputFields, [parentId]: false });
          setNewNodeList((prev) => [...prev, `${parent.title}$$${newNodeTitle}`]);
          setCurrentNodeAdded((prev) => [
            ...prev,
            `${parent.title}$$${newNodeTitle}`,
          ]);
          updateNodeListDb((prev) => [...prev, newNodeTitle]);
  
          // console.log("rrrr", parent.title, newNodeTitle);
  
          // console.log("nddd", nodeRows)
          const newRow = {
            Id: nodeRows?.length?.toString(),
            "Parent Node": parent.title,
            "New Node": newNodeTitle,
            Description: "",
            "Person Responsible": "",
          };
          // console.log("newRow", newRow);
          const updData = Array.isArray(rowData)
            ? [...rowData, newRow]
            : [newRow];
          // console.log("updData", updData);
          setRowData(updData);
          // if(handleClNewNodeData){
          //   handleClNewNodeData(updData);
          // }
  
          if (handleUpdatesCLData) {
            handleUpdatesCLData("NEWNODESCLDATA", updData);
          }
          setNewAddedNode((prev) => [
            ...prev,
            `${parent.title}$$${newNodeTitle}`,
          ]);
          handleData(temp);
        }
        setBlurLoading(false);
      }
    
    }
  };
  // console.log("rowData", rowData);
  const stringCheck = (str1, str2) => {
    // console.log("str1", str1, str2);
    let i = 0,
      j = 0;
    while (i < str1.length && j < str2.length) {
      if (str1[i] != str2[j]) {
        return false;
      }
      i++;
      j++;
    }
    return true;
  };

  const findNode = (list, id) => {
    for (const item of list) {
      if (item.id === id) return item;
      const child = findNode(item.child, id);
      if (child) return child;
    }
    return null;
  };

  const addData = (dataArray, id, newNode) => {
    // console.log("debugger", dataArray.id, dataArray, id);
    if (dataArray.id === id) {
      // console.log("dataadding", newNode);
      dataArray.child.push({
        ...newNode,
        id: `${id}_${dataArray.child.length + 1}`,
      });
    } else if (!stringCheck(dataArray.id, id)) {
    } else {
      dataArray.child = dataArray.child.map((element) => {
        let temp = addData(element, id, newNode);
        return temp;
      });
    }
    return dataArray;
  };

  const findTag = (tree, tag) => {
    if (tree.tags && tree.tags.includes(tag)) {
      return true;
    }
    if (tree.child && tree.child.length > 0) {
      for (let i = 0; i < tree.child.length; i++) {
        if (findTag(tree.child[i], tag)) {
          return true;
        }
      }
    }
    return false;
  };

  const findGl = (node, newTag) => {
    const newTagNum = parseInt(newTag);

    const exactMatch = node?.tags?.some((tag) => {
      if (tag.includes("-")) return false;
      return tag === newTag; // Exact match found
    });

    if (exactMatch) {
      setErrorMessage(`This ${module} already exists in the Hierarchy`);
      return true;
    }

    const hyphenatedTagMatch = node?.tags?.some((tag) => {
      if (tag.includes("-")) {
        const [start, end] = tag.split("-");
        const startNum = parseInt(start);
        const endNum = parseInt(end);

        if (newTagNum >= startNum && newTagNum <= endNum) {
          setErrorMessage(`GL ${newTag} lies within the range ${tag}.`);
          return true;
        }
      }
      return false;
    });

    if (hyphenatedTagMatch) {
      return true;
    }

    if (node?.child && node.child.length > 0) {
      for (let childNode of node.child) {
        if (findGl(childNode, newTag)) {
          return true;
        }
      }
    }

    return false;
  };

  const findTag2 = (node, newValue) => {
    const [start, end] = newValue.split("-");
    const startNum = parseInt(start);
    const endNum = parseInt(end);

    if (isNaN(startNum) || isNaN(endNum)) {
      setErrorMessage("Invalid range format.");
      return true;
    }

    if (startNum >= endNum) {
      console.log("Invalid range: Start value must be less than end value.");
      setErrorMessage("Invalid range: Start value must be less than end value.");
      return true;
    }

    const startTagFound = findGl(node, start);
    const endTagFound = findGl(node, end);

    if (startTagFound || endTagFound) {
      console.log("Either the start or end value already exists in the tree.");
      setErrorMessage("GL Number Range  already exists.");
      return true;
    }

    const rangeOverlapFound = checkRangeOverlap(node, startNum, endNum);

    if (rangeOverlapFound) {
      setErrorMessage(
        "This range overlaps with an existing range or GL number."
      );
      return true;
    }

    return false;
  };

  const checkRangeOverlap = (node, startNum, endNum) => {
    const rangeOverlap = node?.tags?.some((tag) => {
      if (tag.includes("-")) {
        const [existingStart, existingEnd] = tag.split("-").map(Number);
        return (
          startNum <= existingEnd && endNum >= existingStart // Check for range overlap
        );
      } else {
        // If it's a single value, check if it falls within the new range
        const existingValue = parseInt(tag);
        return existingValue >= startNum && existingValue <= endNum;
      }
    });

    if (rangeOverlap) {
      return true;
    }

    // Recursively check child nodes
    if (node?.child && node.child.length > 0) {
      for (let childNode of node.child) {
        if (checkRangeOverlap(childNode, startNum, endNum)) {
          return true;
        }
      }
    }

    return false;
  };

  const [pcRow, setPcRow] = useState([]);
  const handleAddPc = async (nodeId, newValue) => {
    setDuplicateTagError(false)
    setInvalidTagError(false)
    setRequiredLengthError(false)
    setErrorMessage("")
    console.log("nodeId", nodeId);
    setBlurLoading(true);
    const handleCheckForTagDuplicacy = async (newTag) => {
      let url = "";
      let payload = {};
      if (module == "Profit Center") {
        payload = {
          classValue: "0106",
          controllingArea: controllingArea,
          hierarchyGrp: group,
          pc: newTag,
        };
        url = `/${destination_ProfitCenter}/node/pcDuplicacyCheckForPCG`;
      } else if (module == "Cost Center") {
        payload = {
          classValue: "0101",
          controllingArea: controllingArea,
          hierarchyGrp: group,
          cc: newTag,
        };
        url = `/${destination_CostCenter}/node/ccDuplicacyCheckForCCG`;
      } else if (module == "General Ledger") {
        payload = {
          classValue: "0102",
          chartOfAccount: coa,
          hierarchyGrp: group,
          gl: newTag,
        };
        url = `/${destination_GeneralLedger}/node/glDuplicacyCheckForCEG`;
      }

      const result = await promiseAjax(url, "post", payload);
      const res = await result.json();

      // console.log("Okhi", res);

      return res;
    };
    // const findTagInUITree = findTag(newTreeData[0], newValue);
    let findTagInUITree;
    if (module === "General Ledger") {
      console.log("glllitis");
      findTagInUITree = findGl(newTreeData[0], newValue);
    } else {
      console.log("glllnot");
      findTagInUITree = findTag(newTreeData[0], newValue);
    }
    let data = await handleCheckForTagDuplicacy(newValue);
    if (data.body.PresentInCA === "X" || data.body.PresentInCOA === "X") {
      setInvalidTagError(false);
      if (data.body.PresentInHier === "X" || findTagInUITree) {
        if (module !== "General Ledger") {
          setDuplicateTagError(true);
        }
        setBlurLoading(false);
      } else {
        setDuplicateTagError(false);
        // const pcDescription = data.body.Description;
        // setPcDescriptions(prev => ({
        //   ...prev,
        //   [`${nodeId}-${newValue}`]: data.body.Description // Store description with nodeId and pc
        // }));
        if (newValue.trim() !== "") {
          const updatedData = addPcToNode(newTreeData, nodeId, newValue);
          // console.log("updatedData", newTreeData, nodeId, newValue);
          setNewTreeData(updatedData);
          setNewPcValues({ ...newPcValues, [nodeId]: "" });
          setShowInputFieldsForPC({ ...showInputFieldsForPC, [nodeId]: false });
          const parent = findNode(newTreeData, nodeId);
          // console.log("PCPARENT", parent);
          setNewTagList((prev) => [...prev, `${parent?.title}$$${newValue}`]);
          setCurrentTagsAdded((prev) => [
            ...prev,
            `${parent?.title}$$${newValue}`,
          ]);
          handleData(updatedData);

          let newRow;
          if (module == "Profit Center") {
            newRow = {
              Id: PCRows?.length?.toString(),
              Node: parent.title,
              "Profit Center": newValue,
            };
          } else if (module == "Cost Center") {
            newRow = {
              Id: PCRows?.length?.toString(),
              Node: parent.title,
              "Cost Center": newValue,
            };
          } else if (module == "General Ledger") {
            newRow = {
              Id: PCRows?.length?.toString(),
              Node: parent.title,
              "General Ledger": newValue,
            };
          }

          const updData = [...pcRow, newRow];
          setPcRow(updData);
          // if(handleClDescData){
          //   handleClDescData(updData);
          // }
          if (handleUpdatesCLData) {
            handleUpdatesCLData("NEWPCCLDATA", updData);
          }
        }
        setBlurLoading(false);
      }
    } else {
      // console.log("invalid prof");
      setBlurLoading(false);
      setInvalidTagError(true);
    }
  };
  // console.log("pcRow", pcRow);

  const handleAddRangeGL = async (nodeId, newValue) => {
    // debugger
    setErrorMessage("")
    console.log("nodeId", nodeId, newValue);
    const range = newValue.split("-");
    const findTagInUITree = findTag2(newTreeData[0], newValue);
    const handleCheckForGlRangeDuplicacy = async (newTag) => {
      let url = `/${destination_GeneralLedger}/node/glRangeDuplicacyCheckForCEG`;

      var payload = {
        chartOfAccount: coa,
        hierarchyGrp: group,
        glFrom: range[0],
        glTo: range[1],
      };

      const result = await promiseAjax(url, "post", payload);
      const res = await result.json();

      // console.log("Okhi", res);

      return res;
    };

    let data = await handleCheckForGlRangeDuplicacy(newValue);
    console.log("ftttt", findTagInUITree);
     if (findTagInUITree) {
      setDuplicateTagError(true);
      setBlurLoading(false);
    } else if (data?.body?.Status === "Invalid Range") {
      setErrorMessage("Invalid Range");
      setDuplicateTagError(false);
      setBlurLoading(false);
    } else if (data?.body?.Status === "Valid Range") {
      setDuplicateTagError(false);
      if (newValue.trim() !== "") {
        const updatedData = addPcToNode(newTreeData, nodeId, newValue);
        // console.log("updatedData", newTreeData, nodeId, newValue);
        setNewTreeData(updatedData);
        setNewPcValues({ ...newPcValues, [nodeId]: "" });
        setShowInputFieldsForRangeGL({
          ...showInputFieldsForPC,
          [nodeId]: false,
        });
        const parent = findNode(newTreeData, nodeId);
        // console.log("PCPARENT", parent);
        setNewTagList((prev) => [...prev, `${parent?.title}$$${newValue}`]);
        setCurrentTagsAdded((prev) => [
          ...prev,
          `${parent?.title}$$${newValue}`,
        ]);
        handleData(updatedData);

        let newRow = {
          Id: PCRows?.length?.toString(),
          Node: parent.title,
          "General Ledger": newValue,
        };

        const updData = [...pcRow, newRow];
        setPcRow(updData);
        if (handleUpdatesCLData) {
          handleUpdatesCLData("NEWPCCLDATA", updData);
        }
      }
      setBlurLoading(false);
    }
  };

  const addPcToNode = (nodes, nodeId, newValue) => {
    return nodes.map((node) => {
      if (node.id === nodeId) {
        // Update the node's tags array with the new value
        return {
          ...node,
          tags: [...node.tags, newValue],
        };
      } else if (Array.isArray(node.child)) {
        // Recursively update child nodes
        return {
          ...node,
          child: addPcToNode(node.child, nodeId, newValue),
        };
      }
      return node;
    });
  };

  const handleEditButtonClick = (nodeId) => {
    // console.log("nodeid", nodeId)
    setChecked((prev) => !prev);
    const parent = findNode(newTreeData, nodeId);
    // console.log("pareee", parent)
    setPersonResponsibleValues({
      ...personResponsibleValues,
      [nodeId]: parent?.personResponsible,
    });
    setPersonResponsibleField(true);
    setEditingNodeId(nodeId);
  };
  const handleEditDescriptionClick = (nodeId) => {
    // console.log("newNodeId", nodeId);
    const parent = findNode(newTreeData, nodeId);
    // console.log("newPArent", parent);

    setDescriptionValues({
      ...descriptionValues,
      [nodeId]: parent?.description,
    });
    setDescriptionField(true);
    setEditingNodeId(nodeId);
  };
  console.log("descccc", descriptionValues, descriptionField, editingNodeId);

  const getPersonReponsibleData = (nodeId, value) => {
    // console.log("datttt", nodeId, value, data.body.list);

    setNewPrNodeId(nodeId);
    const hSuccess = (data) => {
      if (data?.statusCode === 200) {
        setPersonResponsibleData(data?.body?.list);
        handleOpenTable();
        setCount(data?.body?.count);
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter}/data/getPersonResponsible?personResponsible=${personResponsibleSearch}`,
      "get",
      hSuccess,
      hError
    );
  };

  const handlePersonResponsibleInputChange = (nodeId, value) => {
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    // Set a new timer to execute handleDQM after 1 second
    const newTimerId = setTimeout(() => {
      getPersonReponsibleData(nodeId, value);
    }, 1000);

    // Store the new timer ID
    setTimerId(newTimerId);
  };

  const handleAddPersonResponsible = (nodeId, newValue) => {
    // console.log("hapr", nodeId, newValue)

    if (newValue.trim() !== "") {
      const updatedData = addPersonResponsibleToNode(
        newTreeData,
        nodeId,
        newValue
      );
      // console.log("updatedData", updatedData);
      setNewTreeData(updatedData);
      setPersonResponsibleValues({ ...personResponsibleValues, [nodeId]: "" });
      setPersonResponsibleField(false);
      // setChecked((prev) => !prev);

      const parent = findNode(newTreeData, nodeId);
      // parr = parent;
      // console.log("pppp", parent);

      const checkNodeIndex = newNodeList?.findIndex((item) => {
        const [, existingValue] = item.split("$$");
        // console.log("EXISTNG", existingValue);
        return existingValue === parent?.title;
      });
      // console.log("checkNodeIndex", checkNodeIndex)

      if (checkNodeIndex !== -1) {
        // WHEN NEW NODE IS ADDED, THIS SET PR IS USED
        setNewPRList((prev) => {
          const existingIndex = prev.findIndex((item) =>
            item.startsWith(`${parent?.title}$$`)
          );
          if (existingIndex !== -1) {
            // Update the existing item
            const updatedPrev = [...prev];
            updatedPrev[existingIndex] = `${parent?.title}$$${newValue}`;
            return updatedPrev;
          } else {
            // Add new item
            return [...prev, `${parent?.title}$$${newValue}`];
          }
        });

        const existingIndex = nodeRows?.findIndex(
          (row) => row["New Node"] === parent.title
        );
        // console.log("existingIndex", existingIndex);
        let updatedRowData;
        if (existingIndex !== -1) {
          // If row with same id exists, update it with the new description
          updatedRowData = rowData?.map((row) =>
            row.Id === existingIndex?.toString()
              ? { ...row, "Person Responsible": newValue }
              : row
          );
        }
        // console.log("urda", updatedRowData)
        setRowData(updatedRowData);
        // if(handleClNewNodeData)
        // handleClNewNodeData(updatedRowData);
        if (handleUpdatesCLData) {
          handleUpdatesCLData("NEWNODESCLDATA", updatedRowData);
        }

        const newPRRow = {
          Id: PrRows?.length?.toString(),
          "Parent Node": parent?.title,
          "Old Person Responsible": parent?.personResponsible
            ? parent?.personResponsible
            : "N/A",
          "New Person Responsible": newValue,
        };
        const existingDescIndex = PrRows?.findIndex(
          (row) => row["Parent Node"] === parent?.title
        );
        // console.log("existingDescIndex", existingDescIndex);
        let updatedPr = [];
        if (existingDescIndex !== -1) {
          updatedPr = [...PrRows];
          updatedPr[existingDescIndex] = {
            ...updatedPr[existingDescIndex],
            "New Person Responsible": newValue,
          };
        } else {
          updatedPr = [...PrRows, newPRRow];
        }
        if (handleUpdatesCLData) handleUpdatesCLData("NEWPRCLDATA", updatedPr);
      } else {
        //WHEN EXISTING SAP NODE PR IS CHANGED, THIS STATE IS USED
        setChangePR((prev) => {
          const existingIndex = prev.findIndex((item) =>
            item.startsWith(`${parent?.title}$$`)
          );
          // console.log("existiii", existingIndex)
          if (existingIndex !== -1) {
            // Update the existing item
            const updatedPrev = [...prev];
            updatedPrev[existingIndex] = `${parent?.title}$$${newValue}`;
            return updatedPrev;
          } else {
            // Add new item
            return [...prev, `${parent?.title}$$${newValue}`];
          }
        });

        const newPRRow = {
          Id: PrRows?.length?.toString(),
          "Parent Node": parent?.title,
          "Old Person Responsible": parent?.personResponsible
            ? parent?.personResponsible
            : "N/A",
          "New Person Responsible": newValue,
        };
        const existingDescIndex = PrRows?.findIndex(
          (row) => row["Parent Node"] === parent?.title
        );
        // console.log("existingDescIndex", existingDescIndex);
        let updatedPr = [];
        if (existingDescIndex !== -1) {
          updatedPr = [...PrRows];
          updatedPr[existingDescIndex] = {
            ...updatedPr[existingDescIndex],
            "New Person Responsible": newValue,
          };
        } else {
          updatedPr = [...PrRows, newPRRow];
        }
        if (handleUpdatesCLData) handleUpdatesCLData("NEWPRCLDATA", updatedPr);
      }

      handleData(updatedData);
    }
  };

  const addPersonResponsibleToNode = (nodes, nodeId, newValue) => {
    // console.log("aprtn", nodes, nodeId, newValue)
    return nodes.map((node) => {
      if (node.id === nodeId) {
        return {
          ...node,
          personResponsible: newValue,
        };
      } else if (Array.isArray(node.child)) {
        return {
          ...node,
          child: addPersonResponsibleToNode(node.child, nodeId, newValue),
        };
      }
      // console.log("personnode", node);
      return node;
    });
  };

  const findDescription = (tree, description) => {
    if (tree.description === description) {
      return true;
    }

    if (tree.child && tree.child.length > 0) {
      for (let i = 0; i < tree.child.length; i++) {
        if (findDescription(tree.child[i], description)) {
          return true;
        }
      }
    }

    return false;
  };

  const [nodeDesc, setNodeDesc] = useState([]);
  // console.log("ADED", descRows)

  const handleAddDescription = async (nodeId, newValue) => {
    setBlurLoading(true);
    setDBDuplicateDescError(false)
    setDuplicateDescError(false)
    const handleCheckDuplicateDescription = async (newdesc) => {
      let url = "";
      let payload = {};
      if (module == "Profit Center") {
        payload = {
          requestId: reqId,
          classValue: "0106",
          controllingArea: controllingArea,
          desc: newdesc,
        };
        url = `/${destination_ProfitCenter}/node/descDuplicacyCheckForPCG`;
      } else if (module == "Cost Center") {
        payload = {
          requestId: reqId,
          classValue: "0101",
          controllingArea: controllingArea,
          desc: newdesc,
        };
        url = `/${destination_CostCenter}/node/descDuplicacyCheckForCCG`;
      } else if (module == "General Ledger") {
        payload = {
          requestId: reqId,
          classValue: "0102",
          chartOfAccount: coa,
          desc: newdesc,
        };
        url = `/${destination_GeneralLedger}/node/descDuplicacyCheckForCEG`;
      }

      const result = await promiseAjax(url, "post", payload);
      const res = await result.json();

      // console.log("Okhi", res);

      return res;
    };
    const findDescInUITree = findDescription(newTreeData[0], newValue);
    let data = await handleCheckDuplicateDescription(newValue);
    // console.log("checkdata", Object.keys(data.body));

    let parr;
    if (data.statusCode === 200) {
      if(Object.keys(data.body).length != 0 && data?.body?.isDbDuplicate === true){
        setDBDuplicateDescError(true)
        setDuplicateDescError(false);
        setBlurLoading(false);
      }
      else if (Object.keys(data.body).length != 0 || findDescInUITree) {
        setDBDuplicateDescError(false)
        setDuplicateDescError(true);
        setBlurLoading(false);
      } else {
        setDBDuplicateDescError(false)
        setDuplicateDescError(false);
        // IF NO DUPLICATE AND NEW DESC IS NOT EMPTY
        if (newValue.trim() !== "") {
          const updatedData = addDescriptionToNode(
            newTreeData,
            nodeId,
            newValue
          );
          // console.log("updatedData", updatedData, nodeId, newValue);
          setNewTreeData(updatedData);
          setDescriptionValues({ ...descriptionValues, [nodeId]: "" });
          setDescriptionField(false);
          const parent = findNode(newTreeData, nodeId);
          parr = parent;
          // console.log("pppp", parent, parr);

          const checkNodeIndex = newNodeList?.findIndex((item) => {
            const [, existingValue] = item.split("$$");
            // console.log("EXISTNG", existingValue);
            return existingValue === parent?.title;
          });
          // console.log("checkNodeIndex", checkNodeIndex)

          if (checkNodeIndex !== -1) {
            // WHEN NEW NODE IS ADDED, THIS SET DESCRIPTION IS USED
            // console.log("descnew");
            setNewDescList((prev) => {
              const existingIndex = prev.findIndex((item) =>
                item.startsWith(`${parent?.title}$$`)
              );
              if (existingIndex !== -1) {
                // Update the existing item
                const updatedPrev = [...prev];
                updatedPrev[existingIndex] = `${parent?.title}$$${newValue}`;
                return updatedPrev;
              } else {
                // Add new item
                return [...prev, `${parent?.title}$$${newValue}`];
              }
            });
            // updateDescListDb((prev) => [...prev, newValue]);
            // Update the description list
            updateDescListDb((prev) => {
              const index = prev.findIndex(
                (desc) => desc === parent?.description
              );
              if (index !== -1) {
                // Replace the old description with the new one
                const updatedList = [...prev];
                updatedList[index] = newValue;
                return updatedList;
              } else {
                // Add the new description if it doesn't exist
                return [...prev, newValue];
              }
            });
            setNewDescriptionAdded((prev) => {
              const existingIndex = prev.findIndex((item) =>
                item.startsWith(`${parent?.title}$$`)
              );
              if (existingIndex !== -1) {
                // Update the existing item
                const updatedPrev = [...prev];
                console.log("updatedPrev", updatedPrev);
                updatedPrev[
                  existingIndex
                ] = `${parent?.title}$$N/A$$${newValue}`;
                console.log("updatedPrev", updatedPrev);
                return updatedPrev;
              } else {
                // Add new item
                return [...prev, `${parent?.title}$$"N/A"$$${newValue}`];
              }
            });
            // console.log("newDescriptionAdded", newDescriptionAdded);

            const existingIndex = nodeRows?.findIndex(
              (row) => row["New Node"] === parr.title
            );
            // console.log("existingIndex", existingIndex);
            let updatedRowData;
            if (existingIndex !== -1) {
              // If row with same id exists, update it with the new description
              updatedRowData = rowData?.map((row) =>
                row.Id === existingIndex?.toString()
                  ? { ...row, Description: newValue }
                  : row
              );
            }
            // console.log("urda", updatedRowData)
            setRowData(updatedRowData);
            // if(handleClNewNodeData)
            // handleClNewNodeData(updatedRowData);
            if (handleUpdatesCLData) {
              handleUpdatesCLData("NEWNODESCLDATA", updatedRowData);
            }

            const newDescRow = {
              Id: descRows?.length?.toString(),
              "Parent Node": parr?.title,
              "Old Description": parr?.description ? parr?.description : "N/A",
              "New Description": newValue,
            };
            const existingDescIndex = descRows?.findIndex(
              (row) => row["Parent Node"] === parr?.title
            );
            // console.log("existingDescIndex", existingDescIndex);
            let updatedNodeDesc = [];
            // if(existingDescIndex !== -1){
            //   updatedNodeDesc = [...descRows];
            //   updatedNodeDesc[existingDescIndex] = {
            //     ...updatedNodeDesc[existingDescIndex],
            //     "New Description": newValue,
            //   }
            // } else {
            updatedNodeDesc = [...descRows, newDescRow];
            // }
            // const updData = [...nodeDesc, newDescRow];
            // setNodeDesc(updData);
            // console.log("dddee", updatedNodeDesc);
            // if(handleClDescriptionData)
            // handleClDescriptionData(updData);
            if (handleUpdatesCLData)
              handleUpdatesCLData("NEWDESCCLDATA", updatedNodeDesc);
          } else {
            //WHEN EXISTING SAP NODE DESC IS CHANGED, THIS STATE IS USED
            // console.log("sapppp");
            // console.log("descchange");
            setChangeDescription((prev) => {
              const existingIndex = prev.findIndex((item) =>
                item.startsWith(`${parent?.title}$$`)
              );
              // console.log("existiii", existingIndex)
              if (existingIndex !== -1) {
                // Update the existing item
                const updatedPrev = [...prev];
                updatedPrev[existingIndex] = `${parent?.title}$$${newValue}`;
                return updatedPrev;
              } else {
                // Add new item
                return [...prev, `${parent?.title}$$${newValue}`];
              }
            });
            // updateDescListDb((prev) => [...prev, newValue]);
            updateDescListDb((prev) => {
              const existingIndex = prev.findIndex(
                (desc) => desc === parent?.description
              );
              if (existingIndex !== -1) {
                // Update the existing item
                const updatedPrev = [...prev];
                updatedPrev[existingIndex] = `${newValue}`;
                return updatedPrev;
              } else {
                // Add new item
                return [...prev, `${newValue}`];
              }
            });
            // }
            // setNewDescriptionAdded((prev) => [
            //   ...prev,
            //   `${parent?.title}$$${
            //     parent?.description === "" ? "N/A" : parent?.description
            //   }$$${newValue}`,
            // ]);

            // console.log("newwww", newDescriptionAdded);

            const newDescRow = {
              Id: descRows?.length?.toString(),
              "Parent Node": parr?.title,
              "Old Description": parr?.description ? parr?.description : "N/A",
              "New Description": newValue,
            };
            const existingDescIndex = descRows?.findIndex(
              (row) => row["Parent Node"] === parr?.title
            );
            // console.log("existingDescIndex", existingDescIndex);
            let updatedNodeDesc = [];
            // if(existingDescIndex !== -1){
            //   updatedNodeDesc = [...descRows];
            //   updatedNodeDesc[existingDescIndex] = {
            //     ...updatedNodeDesc[existingDescIndex],
            //     "New Description": newValue,
            //   }
            // } else {
            updatedNodeDesc = [...descRows, newDescRow];
            // }
            // const updData = [...nodeDesc, newDescRow];
            // setNodeDesc(updData);
            // console.log("dddee", updatedNodeDesc);
            // if(handleClDescriptionData)
            // handleClDescriptionData(updData);
            if (handleUpdatesCLData)
              handleUpdatesCLData("NEWDESCCLDATA", updatedNodeDesc);
          }
          // console.log("updatedData",updatedData, newValue);
          handleData(updatedData);
          // setShowInputFieldsForPC({ ...showInputFieldsForPC, [nodeId]: false });
        }
        setBlurLoading(false);
      }
    } else {
      setBlurLoading(false);
    }
  };

  const addDescriptionToNode = (nodes, nodeId, newValue) => {
    return nodes.map((node) => {
      if (node.id === nodeId) {
        return {
          ...node,
          description: newValue,
        };
      } else if (Array.isArray(node.child)) {
        return {
          ...node,
          child: addDescriptionToNode(node.child, nodeId, newValue),
        };
      }
      // console.log("personnode", node);
      return node;
    });
  };

  const getAllNodeIds = (nodes) => {
    // console.log("nodessss", nodes);
    let ids = [];
    nodes?.map((node) => {
      ids.push(node.id);
      if (node.child) {
        ids = ids.concat(getAllNodeIds(node.child));
      }
    });
    return ids;
  };

  // const getAllNodeIds = (nodes, level = 0, maxLevel = 3) => {
  //   if (level >= maxLevel) return [];

  //   return nodes?.reduce((ids, node) => {
  //     ids.push(node.id);
  //     if (node.child && level + 1 < maxLevel) {
  //       ids = ids.concat(getAllNodeIds(node.child, level + 1, maxLevel));
  //     }
  //     return ids;
  //   }, []);
  // };

  // const allNodeIds = getAllNodeIds(newTreeData);
  // console.log("allNodeIds", allNodeIds);
  const getAllExpandedNodeIds = (nodes, level = 0, maxLevel = Infinity) => {
    return nodes?.reduce((expandedIds, node) => {
      // Check if the node is a leaf node (no children) and has tags
      const hasChildren = node.child && node.child.length > 0;
      const hasTags = node.tags && node.tags.length > 0;

      if (hasChildren || (!hasChildren && !hasTags)) {
        // Expand if not a leaf node with tags
        expandedIds.push(node.id);
      }

      // Recursively process child nodes if it's not a leaf node with tags
      if (hasChildren && level + 1 < maxLevel) {
        expandedIds = expandedIds.concat(
          getAllExpandedNodeIds(node.child, level + 1, maxLevel)
        );
      }

      return expandedIds;
    }, []);
  };

  console.log('check node', bigTreeData?.includes(group))
  // Example usage:
  // const allNodeIds = getAllExpandedNodeIds(newTreeData);
  const allNodeIds =
    bigTreeData?.includes(group) || bigTreeData?.includes(newTreeData?.[0]?.title)
      ? getAllExpandedNodeIds(newTreeData)
      : 
      getAllNodeIds(newTreeData);
  console.log("expandedNodeIds", allNodeIds);

  const [removeNodeRow, setRemoveNodeRow] = useState([]);

  const handleDeleteNode = (parentId, nodeId, item) => {
    // console.log("nodeitem", item);
    const gatherAllNodeTitles = (node) => {
      let titles = [node.title];
      if (node.child && node.child.length > 0) {
        node.child.forEach((chi) => {
          titles = [...titles, ...gatherAllNodeTitles(chi)];
        });
      }
      return titles;
    };

    const gatherAllDescriptions = (node) => {
      let descriptions = node.description ? [node.description] : [];
      if (node.child && node.child.length > 0) {
        node.child.forEach((chi) => {
          descriptions = [...descriptions, ...gatherAllDescriptions(chi)];
        });
      }
      return descriptions;
    };
    const nodeToDelete =
      parentId === null || parentId === undefined
        ? newTreeData.find((node) => node.id === nodeId)
        : findNode(newTreeData, nodeId);

    if (!nodeToDelete) {
      return;
    }

    const allTitlesToDelete = gatherAllNodeTitles(nodeToDelete);
    const allDescriptionsToDelete = gatherAllDescriptions(nodeToDelete);
    // console.log("allTitlesToDelete", allTitlesToDelete, nodeToDelete, allDescriptionsToDelete)

    // If parentId is null or undefined, directly filter out the node from data
    if (parentId === null || parentId === undefined) {
      const updatedData = newTreeData.filter((node) => node.id !== nodeId);
      setNewTreeData(updatedData);
      return;
    }

    // Otherwise, perform deletion as usual
    const updatedData = deleteNode(newTreeData, parentId, nodeId);
    setNewTreeData(updatedData);
    const parent = findNode(newTreeData, parentId);
    // console.log("parenttag", parent);
    // setRemoveNodeData((prev) => [...prev, `${parent.title}$$${item.title}`]);
    // setDeleteNodeData((prev) => [...prev, `${parent.title}$$${item.title}`]);
    setNewDeleteNodeList((prev) => [...prev, `${parent.title}$$${item.title}`]);
    handleData(updatedData);

    // updateNodeListDb((prev) => prev.filter(node => node !== item.title));
    updateNodeListDb((prev) =>
      prev.filter((node) => !allTitlesToDelete.includes(node))
    );

    updateDescListDb((prev) =>
      prev.filter((desc) => !allDescriptionsToDelete.includes(desc))
    );
    // updateDescListDb((prev) => {
    //   return prev.filter(entry => entry !== `${item.description}`);
    // });
    // updateDescListDb((prev) => {
    //   return prev.filter(entry => {return !allTitlesToDelete.includes(item.description);});
    // });

    const newRow = {
      Id: rowsForDeleteNode?.length?.toString(),
      "Parent Node": parent.title,
      "Deleted Node": item.title,
    };
    const updData = [...removeNodeRow, newRow];
    // console.log("delle", updData);
    setRemoveNodeRow(updData);
    // if(handleClDeleteNodeData)
    //   handleClDeleteNodeData(updData);
    if (handleUpdatesCLData) {
      handleUpdatesCLData("DELETENODE", updData);
    }
  };

  const deleteNode = (nodes, parentId, nodeId) => {
    return nodes.map((node) => {
      if (node.id === parentId) {
        // Remove the node from the child array
        node.child = node.child.filter((child) => child.id !== nodeId);
      } else if (node.child) {
        // Recursively update child nodes
        node.child = deleteNode(node.child, parentId, nodeId);
      }
      return node;
    });
  };

  const [deletePCRow, setDeletePCRow] = useState([]);
  const handleRemovePc = (nodeId, pcIndex, selectedTag) => {
    // console.log("selectedTag", selectedTag);
    const updatedData = removePcFromNode(newTreeData, nodeId, pcIndex);
    setNewTreeData(updatedData);
    const parent = findNode(newTreeData, nodeId);
    // console.log("parenttag", parent);
    setNewReplaceTagList((prev) => [
      ...prev,
      `${parent.title}$$${selectedTag}`,
    ]);
    // setRemovePCData((prev) => [...prev, `${parent.title}$$${selectedTag}`]);
    handleData(updatedData);

    let newRow;
    if (module == "Profit Center") {
      newRow = {
        Id: removePCrows?.length?.toString(),
        "Parent Node": parent.title,
        "Selected Profit Center": selectedTag,
      };
    } else if (module == "Cost Center") {
      newRow = {
        Id: removePCrows?.length?.toString(),
        "Parent Node": parent.title,
        "Selected Cost Center": selectedTag,
      };
    } else if (module == "General Ledger") {
      newRow = {
        Id: removePCrows?.length?.toString(),
        "Parent Node": parent.title,
        "Selected General Ledger": selectedTag,
      };
    }

    const updData = [...deletePCRow, newRow];
    // console.log("ipppp", updData)
    setDeletePCRow(updData);
    // if(handleClRemovePCData)
    // handleClRemovePCData(updData);
    if (handleUpdatesCLData) handleUpdatesCLData("NEWREMOVEPCDATA", updData);
  };

  // console.log("deletePCRow", deletePCRow);

  const removePcFromNode = (nodes, nodeId, pcIndex) => {
    return nodes.map((node) => {
      if (node.id === nodeId) {
        // Remove the tags from the node's tags array
        node.tags.splice(pcIndex, 1);
      } else if (Array.isArray(node.child)) {
        // Recursively update child nodes
        node.child = removePcFromNode(node.child, nodeId, pcIndex);
      }
      return node;
    });
  };

  // useEffect(() => {
  //   const handleClickOutside = (event) => {
  //     if (inputRef.current && !inputRef.current.contains(event.target)) {
  //       inputRef.current.blur();
  //     }
  //   };

  //   document.addEventListener("mousedown", handleClickOutside);
  //   return () => {
  //     document.removeEventListener("mousedown", handleClickOutside);
  //   };
  // }, []);

  const addNodeToParent = (nodes, parentId, newNode) => {
    return nodes.map((node) => {
      if (node.id === parentId) {
        return {
          ...node,
          child: [...node.child, newNode],
        };
      }
      if (node.child) {
        return {
          ...node,
          child: addNodeToParent(node.child, parentId, newNode),
        };
      }
      return node;
    });
  };

  // const generateNewNodeId = (parentId, originalNodeId) => {
  //   const parentPrefix = parentId.split('_').shift(); // Extract the parent prefix
  //   const newId = `${parentPrefix}_${originalNodeId}`;
  //   return newId;
  // };

  const generateNewNodeId = (parentId) => {
    // Assuming the new node ID should be the next available child number under the new parent
    const nextChildIndex = getNextChildIndex(parentId); // Function to determine the next child index
    const newId = `${parentId}_${nextChildIndex}`;
    return newId;
  };

  const getNextChildIndex = (parentId) => {
    // Find the parent node in the tree data
    const parentNode = findNodeById(treeData, parentId);
    // Calculate the next available child index
    const nextChildIndex = parentNode.child.length + 1;

    return nextChildIndex;
  };

  const updateNodeIds = (node) => {
    // const newId = generateNewNodeId(newParentId, node.i);
    let id = node.id;
    let ids = id.split("_");
    if (node.child.length) {
      // ids.pop();
      ids.push(node.child.length + 1);
    } else {
      ids.push("1");
    }

    id = ids.join("_");
    // console.log('retoredid', id)
    return id;
  };

  const updateChildNodeIds = (node, baseId) => {
    const updateIds = (currentNode, parentId) => {
      // Update the current node's ID
      const childId = `${parentId}_${currentNode.index}`;
      currentNode.id = childId;

      // Recursively update child nodes
      if (currentNode.child && currentNode.child.length > 0) {
        currentNode.child.forEach((child, index) => {
          child.index = index + 1; // Update index for the child node
          updateIds(child, childId); // Recursive call for the child node
        });
      }
    };

    // Start updating IDs with the baseId
    if (node.child && node.child.length > 0) {
      node.child.forEach((child, index) => {
        child.index = index + 1; // Set the index for the first level child
        updateIds(child, baseId); // Recursive call
      });
    }
  };

  const [movedNodeRow, setMovedNodeRow] = useState([]);
  // console.log("mmmnr", movedNodeRow);
  // Handler to store node data
  const handleStoreNodeData = (nodeId, parentId) => {
    // console.log("newnodenode2", nodeId, parentId);
    const nodeToStore = findNodeById(newTreeData, nodeId);
    // console.log("nodeToStore", nodeToStore);
    setStoredNodeData(nodeToStore);
    if (nodeToStore) {
      // const updatedNode = updateNodeIds(nodeToStore, nodeId);
      const updatedTreeData = removeNodeById(newTreeData, nodeId);
      const parent = findNode(newTreeData, parentId);
      // console.log("parent2", parent);
      setStoredNodeParentData(parent);
      setNewReplaceNodeList((prev) => [
        ...prev,
        `${parent.title}$$${nodeToStore?.title}`,
      ]);
      // meant for change log
      // setMoveNodeList((prev) => [
      //   ...prev,
      //   `${parent.title}$$${nodeToStore?.title}`,
      // ]);
      setNewTreeData(updatedTreeData);
      handleData(updatedTreeData);

      const childNodes = getAllChildNodes(nodeToStore);
      // console.log("childNodes", childNodes);
      setMovedNodes((prev) => [...prev, nodeId, ...childNodes]); // FOR BUTTON VISIBILITY
      const tags = getAllTags(nodeToStore);
      // console.log("taaaa", tags);
      if (reqType !== "Create") {
        setTagPickedNodes((prev) => [...prev, ...tags]); // FOR BUTTON VISIBILITY
      }

      const newRow = {
        Id: rowsForMovedNodes?.length?.toString(),
        "Old Parent Node": parent?.title,
        "New Parent Node": "",
        "Selected Node": nodeToStore.title,
      };
      const updData = [...movedNodeRow, newRow];
      // console.log("uuup", updData);
      setMovedNodeRow(updData);
      // if(handleClMovedNodesData)
      // handleClMovedNodesData(updData);
      if (handleUpdatesCLData) {
        handleUpdatesCLData("NEWMOVEDNODEDATA", updData);
      }
    }
  };
  // console.log("movedNodeRow", movedNodeRow)

  const getAllChildNodes = (node) => {
    let childNodes = [];
    // console.log("dddd", node, node.child)
    if (node.child) {
      node.child.forEach((child) => {
        childNodes.push(child.id);
        childNodes = childNodes.concat(getAllChildNodes(child));
      });
    }
    return childNodes;
  };

  const getAllTags = (node) => {
    let tags = [];
    if (node.tags) {
      tags = tags.concat(node.tags);
    }
    if (node.child) {
      node.child.forEach((child) => {
        tags = tags.concat(getAllTags(child));
      });
    }
    return tags;
  };

  // console.log("movedNodes", movedNodes);
  // console.log("newTreeData", newTreeData);
  // console.log("newReplaceNodeList", newReplaceNodeList);

  const removeNode = (nodes, nodeId) => {
    return nodes
      .map((node) => {
        if (node.id === nodeId) {
          return null;
        }
        if (node.child) {
          return {
            ...node,
            child: removeNode(node.child, nodeId),
          };
        }
        return node;
      })
      .filter((node) => node !== null);
  };

  // Handler to restore node treeData
  const handleRestoreNodeData = (targetNodeId, node) => {
    // console.log("newnodenode1", targetNodeId, node);
    if (storedNodeData) {
      const updatedStoredNodeId = updateNodeIds(node);
      // console.log("updatedStoredNodeId", updatedStoredNodeId);
      storedNodeData.id = updatedStoredNodeId;

      const updatedStoredNode = updatedStoredNodeId;

      // Update the IDs of child nodes based on the new base ID
      updateChildNodeIds(storedNodeData, updatedStoredNode);
      // console.log("stnd", storedNodeData)

      const updatedData = addNodeToParent(
        newTreeData,
        targetNodeId,
        storedNodeData
      );
      // console.log("restoredTree", updatedData);
      const parent = findNode(newTreeData, targetNodeId);
      // console.log("parent1", parent);
      setNewNodeList((prev) => [
        ...prev,
        `${parent.title}$$${storedNodeData?.title}`,
      ]);
      setCurrentNodeAdded((prev) => [
        ...prev,
        `${parent.title}$$${storedNodeData?.title}`,
      ]);
      // setRestoreMoveNode((prev) => [
      //   ...prev,
      //   `${parent.title}$$${storedNodeData?.title}`,
      // ]);
      setNewTreeData(updatedData);
      handleData(updatedData); // Update the tree data with the node added to the new location
      setStoredNodeData(null); // Clear the stored node data
      setStoredNodeParentData(null);

      const existingIndex = movedNodeRow.findIndex(
        (row) => row["Selected Node"] === storedNodeData.title
      );
      // console.log("existingIndex",existingIndex)
      let updatedRowData;
      if (existingIndex !== -1) {
        updatedRowData = movedNodeRow.map((row) =>
          row["Selected Node"] === storedNodeData.title
            ? { ...row, "New Parent Node": parent.title }
            : row
        );
      } else {
      }

      // console.log("updatedRowData", updatedRowData);

      setMovedNodeRow(updatedRowData);
      // if(handleClMovedNodesData)
      // handleClMovedNodesData(updatedRowData);
      if (handleUpdatesCLData)
        handleUpdatesCLData("RESTOREMOVEDDATA", updatedRowData);

      setMovedNodeId(updatedStoredNode);
    }
  };

  const findNodeById = (nodes, nodeId) => {
    for (const node of nodes) {
      if (node.id === nodeId) {
        return node;
      }
      if (node.child) {
        const result = findNodeById(node.child, nodeId);
        if (result) {
          return result;
        }
      }
    }
    return null;
  };
  const removeNodeById = (nodes, nodeId) => {
    return nodes
      .map((node) => {
        if (node.id === nodeId) {
          return null;
        }
        if (node.child) {
          return { ...node, child: removeNodeById(node.child, nodeId) };
        }
        return node;
      })
      .filter((node) => node !== null);
  };

  //   const handlePickTag = (item, tags) =>{
  // console.log('tagItem', item, tags)
  //   }

  const [sourceNodeId, setSourceNodeId] = useState(null);
  // console.log("sourceNodeId", sourceNodeId);
  const [pickedTag, setPickedTag] = useState(null);

  const removeTagFromNode = (nodes, nodeId, tag) => {
    return nodes.map((node) => {
      if (node.id === nodeId) {
        return {
          ...node,
          tags: node.tags.filter((t) => t !== tag),
        };
      }
      if (node.child) {
        return { ...node, child: removeTagFromNode(node.child, nodeId, tag) };
      }
      return node;
    });
  };

  const [movePCRow, setMovePCRow] = useState("");
  const handlePickTag = (tag, nodeId) => {
    // console.log("ishshh")
    // console.log("tagnodeId", tag, nodeId);
    setPickedTag(tag);
    setSourceNodeId(nodeId);
    const updatedTreeData = removeTagFromNode(treeData, nodeId, tag);
    const parent = findNode(newTreeData, nodeId);
    console.log("parenttag", parent);
    setNewReplaceTagList((prev) => [...prev, `${parent.title}$$${tag}`]); // for payload
    // setStoreMoveTagList((prev) => [...prev, `${parent.title}$$${tag}`]);
    setNewTreeData(updatedTreeData);
    handleData(updatedTreeData);

    // const childNodes = getAllChildNodes(findNodeById(newTreeData, nodeId));
    // console.log("cccc", childNodes);
    // setTagPickedNodes((prev) => [...prev, nodeId, ...childNodes]);

    let newRow;
    if (module == "Profit Center") {
      newRow = {
        Id: rowsForReplacePC?.length?.toString(),
        "Old Parent Node": parent.title,
        "New Parent Node": "",
        "Selected Profit Center": tag,
      };
    } else if (module == "Cost Center") {
      newRow = {
        Id: rowsForReplacePC?.length?.toString(),
        "Old Parent Node": parent.title,
        "New Parent Node": "",
        "Selected Cost Center": tag,
      };
    } else if (module == "General Ledger") {
      newRow = {
        Id: rowsForReplacePC?.length?.toString(),
        "Old Parent Node": parent.title,
        "New Parent Node": "",
        "Selected General Ledger": tag,
      };
    }

    const updData = [...movePCRow, newRow];
    setMovePCRow(updData);
    // if(handleClPCMovedData)
    // handleClPCMovedData(updData);
    if (handleUpdatesCLData) {
      handleUpdatesCLData("NEWMOVEPCDATA", updData);
    }
  };

  // console.log("movePCRow", movePCRow);

  const handleDropTag = (targetNodeId, parentId) => {
    if (pickedTag && sourceNodeId) {
      const updatedTreeData = addTagToNode(
        newTreeData,
        targetNodeId,
        pickedTag
      );
      const parent = findNode(newTreeData, targetNodeId);
      // console.log("parentdroptag", parent, pickedTag);
      setNewTagList((prev) => [...prev, `${parent.title}$$${pickedTag}`]);
      setCurrentTagsAdded((prev) => [...prev, `${parent.title}$$${pickedTag}`]);
      setNewTreeData(updatedTreeData);
      setRestoreMoveTagList((prev) => [
        ...prev,
        `${parent.title}$$${pickedTag}`,
      ]);
      handleData(updatedTreeData);
      setPickedTag(null);
      setSourceNodeId(null);

      let updatedRowData;
      if (module == "Profit Center") {
        const existingIndex = movePCRow.findIndex(
          (row) => row["Selected Profit Center"] === pickedTag
        );
        console.log("existingIndex", existingIndex);
        if (existingIndex !== -1) {
          updatedRowData = movePCRow.map((row) =>
            row["Selected Profit Center"] === pickedTag.toString()
              ? { ...row, "New Parent Node": parent.title }
              : row
          );
        } else {
        }
      } else if (module == "Cost Center") {
        const existingIndex = movePCRow.findIndex(
          (row) => row["Selected Cost Center"] === pickedTag
        );
        console.log("existingIndex", existingIndex);
        if (existingIndex !== -1) {
          updatedRowData = movePCRow.map((row) =>
            row["Selected Cost Center"] === pickedTag.toString()
              ? { ...row, "New Parent Node": parent.title }
              : row
          );
        } else {
        }
      } else if (module == "General Ledger") {
        const existingIndex = movePCRow.findIndex(
          (row) => row["Selected General Ledger"] === pickedTag
        );
        console.log("existingIndex", existingIndex);
        if (existingIndex !== -1) {
          updatedRowData = movePCRow.map((row) =>
            row["Selected General Ledger"] === pickedTag.toString()
              ? { ...row, "New Parent Node": parent.title }
              : row
          );
        } else {
        }
      }

      // console.log("updatedRowData", updatedRowData);

      setMovePCRow(updatedRowData);
      // if(handleClPCMovedData)
      // handleClPCMovedData(updatedRowData);
      if (handleUpdatesCLData) {
        handleUpdatesCLData("RESTOREMOVEDPCDATA", updatedRowData);
      }
    }
  };

  const addTagToNode = (nodes, nodeId, tag) => {
    return nodes.map((node) => {
      if (node.id === nodeId) {
        return {
          ...node,
          tags: [...node.tags, tag],
        };
      }
      if (node.child) {
        return { ...node, child: addTagToNode(node.child, nodeId, tag) };
      }
      return node;
    });
  };

  const getTagsAfterDollar = (list) => {
    return list.map((item) => item.split("$$")[1]);
  };

  const displayRangeGL = (tag) => {
    const [start, end] = tag.split("-");
    console.log("Tag:", tag, "Start:", start, "End:", end);

    const hSuccess = (data) => {
      if (data?.statusCode === 200) {
        updateGlViewList(data?.body);
      } else {
        console.error("error fetching");
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_GeneralLedger}/node/getGlAccountsForGlRange?chartOfAccount=${coa}&glStart=${start}&glEnd=${end}`,
      "get",
      hSuccess,
      hError
    );
  };

  let getTreeNodes = (newTreeData) => {
    const createChildNodes = (list, parentId) => {
      return list?.map((item) => {
        const nodeId = item.id;

        const currentValue = nodeValues[nodeId] || item.title;
        const showInput = showInputFields[nodeId];
        const showInputForPC = showInputFieldsForPC[nodeId];
        const editable = editableNodes[nodeId] || false;
        const isNodeDisabled =
          reqType === "Create"
            ? false
            : newNodeList?.some(
                (storedNode) => storedNode.split("$$")[1] === item.title
              );
        const tagsAfterDollar =
          reqType === "Create" ? [] : getTagsAfterDollar(newTagList ?? []);
        // console.log("ttttta", list);
        const isTagInNewTagList = (tag) => {
          // console.log("trrrr1", tag);
          return newTagList.some((storedNode) => {
            const storedTag = storedNode.split("$$")[1];
            // console.log("storedTag", storedTag, tag);
            // return tag === storedTag;
            if (tag === storedTag) return true;
            else return false;
          });
        };
        // const moveNodeDisabled = newNodeList.some(storedNode => storedNode.split('$$')[1] === item.title)
        // console.log("hhhhh2", nodeId, parentId)
        return (
          <StyledTreeItem
            key={nodeId}
            nodeId={nodeId}
            // style={{ position: "relative" }}
            label={
              <p>
                {
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      // position: "sticky",
                      // top: 0,
                      // zIndex: 1,
                      // backgroundColor: "white",
                    }}
                  >
                    {editable ? (
                      <TextField
                        defaultValue={item.title}
                        size="small"
                        variant="outlined"
                        onBlur={() => handleSaveNodeValue(nodeId)}
                        onChange={(e) =>
                          handleChangeNodeValue(nodeId, e.target.value)
                        }
                        onKeyDown={(e) => handleTextFieldKeyDown(nodeId, e)}
                      />
                    ) : (
                      <>
                        <p style={{ fontWeight: 600, color: "#00317D" }}>
                          {highlightText(`${item.title}`, searchTerm)}
                        </p>

                        {descriptionField ? (
                          editingNodeId === nodeId ? (
                            <>
                              <TextField
                                size="medium"
                                helperText="Press Enter to Submit Description"
                                label="Change Description"
                                variant="standard"
                                color="info"
                                focused
                                sx={{ width: "40%", ml: 5 }}
                                autoFocus
                                placeholder="Enter Description"
                                value={descriptionValues[nodeId] || ""}
                                inputProps={{ maxLength: 40 }}
                                onChange={(e) => {
                                  let inputValue = e.target.value;
                                  if (inputValue.startsWith(" ")) {
                                    inputValue = inputValue.trimStart();
                                  }
                                  const capitalizedValue = inputValue
                                  .replace(/[^a-zA-Z0-9-&()#, ]/g, "")
                                  .replace(/\s{2,}/g, " ")
                                  .replace(/\s*([-&()#,])\s*/g, "$1") // Remove spaces before and after special characters
                                  .trimStart().toUpperCase();
                                  setDescriptionValues({
                                    ...descriptionValues,
                                    [nodeId]: capitalizedValue,
                                  });
                                }}
                                onBlur={() => {
                                  setDescriptionField(false);
                                  setDuplicateDescError(false);
                                   setDBDuplicateDescError(false)
                                }}
                                onKeyDown={(e) => {
                                  if (e.key === "Enter") {
                                    const inputValue = e.target.value.trim();
                                    if (inputValue) {
                                      handleAddDescription(
                                        nodeId,
                                        e.target.value
                                      );
                                    }
                                  }
                                }}
                              />
                              {duplicateDescError ? (
                                <Typography
                                  variant="caption"
                                  color="error"
                                  sx={{ alignItems: "center" }}
                                >
                                  This Description already exists in the
                                  Hierarchy!{" "}
                                </Typography>
                              ) : (
                                <></>
                              )}
                                {DBduplicateDescError ? (
                  <Typography
                    variant="caption"
                    sx={{ alignItems: "center", pl: 2 }}
                    color="error"
                  >
                    This description is already present in some ongoing request!{" "}
                  </Typography>
                ) : (
                  <></>
                )}
                            </>
                          ) : (
                            <Typography sx={{ ml: 2, color: "#0f07ad" }}>
                              {item?.description === "" ||
                              item?.description === undefined
                                ? ""
                                : `(${item.description})`}
                            </Typography>
                          )
                        ) : (
                          <Typography sx={{ ml: 2, color: "#0f07ad" }}>
                            {item?.description === "" ||
                            item?.description === undefined
                              ? ""
                              : `(${item.description})`}
                          </Typography>
                        )}

                        {personResponsibleField ? (
                          editingNodeId === nodeId ? (
                            <TextField
                              size="small"
                              autoFocus
                              sx={{ mr: 2 }}
                              variant="outlined"
                              placeholder="Enter Person Responsible"
                              value={personResponsibleValues[nodeId] || ""}
                              onChange={(e) => {
                                const capitalizedValue = e.target.value
                                  .replace(/[^a-zA-Z0-9-&()#, ]/g, "")
                                  .toUpperCase();
                                setPersonResponsibleSearch(capitalizedValue);
                                setPersonResponsibleValues({
                                  ...personResponsibleValues,
                                  [nodeId]: capitalizedValue,
                                });
                              }}
                              onBlur={() => {
                                setPersonResponsibleField(false);
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  // const inputValue = e.target.value.trim();
                                  // if(inputValue){
                                  // handleAddPersonResponsible(
                                  //   nodeId,
                                  //   inputValue
                                  // );
                                  // }
                                  handlePersonResponsibleInputChange(
                                    nodeId,
                                    e.target.value
                                  );
                                }
                              }}
                            />
                          ) : (
                            <Typography
                              sx={{
                                fontWeight: 200,
                                color: "steelblue",
                                ml: 2,
                              }}
                            >
                              {highlightText(
                                `${item?.personResponsible}`,
                                searchTerm
                              )}
                            </Typography>
                          )
                        ) : (
                          <Typography
                            sx={{ fontWeight: 200, color: "steelblue", ml: 2 }}
                          >
                            {highlightText(
                              `${item?.personResponsible}`,
                              searchTerm
                            )}
                          </Typography>
                        )}
                      </>
                    )}

                    {buttonIconVisibility ? (
                      <div style={{ display: "flex", marginLeft: 15 }}>
                        <Tooltip title="Description">
                          <IconButton
                            color="primary"
                            aria-label="upload picture"
                            component="label"
                            sx={iconButton_SpacingSmall}
                          >
                            <ModeEditIcon
                              style={{
                                height: "0.8em",
                                width: "0.8em",
                                color: "teal",
                              }}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditDescriptionClick(nodeId);
                              }}
                            />
                          </IconButton>
                        </Tooltip>
                        {buttonChangePerResp && (
                          <Tooltip title="Person Responsible">
                            <IconButton
                              color="primary"
                              aria-label="upload picture"
                              component="label"
                              sx={iconButton_SpacingSmall}
                            >
                              <PersonIcon
                                style={{
                                  height: "0.8em",
                                  width: "0.8em",
                                  color: "darkolivegreen",
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditButtonClick(nodeId);
                                }}
                              />
                            </IconButton>
                          </Tooltip>
                        )}
                        {buttonReplace &&
                          !storedNodeData &&
                          !isNodeDisabled &&
                          !childsOfMovedNodes.includes(item.title) &&
                          item.id !== "1" && (
                            <>
                              {/* {console.log("hnnn1", nodeId, parentId)} */}
                              <Tooltip title={`Move Node ${item?.title} `}>
                                <IconButton
                                  color="primary"
                                  aria-label="upload picture"
                                  component="label"
                                  sx={iconButton_SpacingSmall}
                                >
                                  <ArrowCircleRightIcon
                                    style={{
                                      height: "0.8em",
                                      width: "0.8em",
                                      color: "#d1351d",
                                    }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleStoreNodeData(nodeId, parentId);
                                    }}
                                  />
                                </IconButton>
                              </Tooltip>
                            </>
                          )}

                        {storedNodeData &&
                          item?.tags?.length === 0 &&
                          storedNodeParentData.title !== item.title && (
                            <Tooltip
                              title={`Put Moved Node Back to ${item?.title} `}
                            >
                              <IconButton
                                color="primary"
                                aria-label="upload picture"
                                component="label"
                                sx={iconButton_SpacingSmall}
                              >
                                <ArrowCircleLeftIcon
                                  style={{
                                    height: "0.8em",
                                    width: "0.8em",
                                    color: "#48c91c",
                                  }}
                                  onClick={(e) => {
                                    e.stopPropagation();

                                    handleRestoreNodeData(nodeId, item);
                                  }}
                                />
                              </IconButton>
                            </Tooltip>
                          )}
                        {buttonReplace &&
                          pickedTag &&
                          item?.child?.length === 0 && (
                            //  (sourceNodeId !== item.id) &&
                            <Tooltip
                              title={`Put Moved ${module} Back to ${item?.title}`}
                            >
                              <IconButton
                                color="secondary"
                                aria-label="Drop PC"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDropTag(nodeId, parentId);
                                }}
                              >
                                <UndoIcon
                                  style={{
                                    height: "0.8em",
                                    width: "0.8em",
                                    color: "#48c91c",
                                  }}
                                />
                              </IconButton>
                            </Tooltip>
                          )}
                      </div>
                    ) : (
                      <></>
                    )}

                    <Tooltip title="Delete Node">
                      <>
                        {/* {console.log("item", item)} */}
                        {buttonDelete &&
                          !isNodeDisabled &&
                          !childsOfMovedNodes.includes(item.title) &&
                          item.id !== "1" && (
                            <IconButton
                              color="secondary"
                              aria-label="Delete Node"
                              sx={iconButton_SpacingSmall}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteNode(parentId, nodeId, item);
                              }}
                            >
                              <Delete
                                style={{
                                  height: "0.8em",
                                  width: "0.8em",
                                  color: "#e04848",
                                }}
                              />
                            </IconButton>
                          )}
                      </>
                    </Tooltip>
                  </div>
                }
              </p>
            }
          >
            {item?.child?.[0] ? createChildNodes(item?.child, nodeId) : <></>}
            {item?.tags?.map((tags, index) => {
              // console.log("Tag:", tags, "Index:", index);
              return (
                <StyledTreeItem
                  key={`${nodeId}-tags-${index}`}
                  nodeId={`${nodeId}-tags-${index}`}
                  label={
                    <p>
                      {
                        <div style={{ display: "flex", alignItems: "center" }}>
                          <p> {highlightText(`${tags}`, searchTerm)}</p>
                          {/* {pcDescriptions?.[`${nodeId}-${tags}`] && (
                          <Typography sx={{ ml: 2, color: "#0f07ad" }}>
                            ({pcDescriptions[`${nodeId}-${tags}`]})
                          </Typography>
                        )} */}
                          <Tooltip title={`Remove ${module}`}>
                            <div key={index}>
                              {/* {console.log("hiiiw", tags)} */}
                              {buttonRemove &&
                                !tagsAfterDollar.includes(tags) &&
                                !childTagsOfMovedNodes.includes(tags) && (
                                  <IconButton
                                    color="secondary"
                                    aria-label="Remove tags"
                                    sx={{ iconButton_SpacingSmall }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleRemovePc(nodeId, index, tags);
                                    }} //remove profit center icon
                                  >
                                    <DisabledByDefaultIcon
                                      style={{
                                        height: "0.8em",
                                        width: "0.8em",
                                        color: "#e04848",
                                      }}
                                    />
                                  </IconButton>
                                )}
                            </div>
                          </Tooltip>
                          {/* {console.log("feee", tags, item.id)} */}
                          {buttonReplace &&
                            !storedNodeData &&
                            !pickedTag &&
                            !tagsAfterDollar.includes(tags) &&
                            !tagPickedNodes.includes(tags) && (
                              <Tooltip title={`Move ${module} ${tags}`}>
                                <IconButton
                                  color="secondary"
                                  aria-label="Pick PC"
                                  sx={{ iconButton_SpacingSmall }}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handlePickTag(tags, item.id);
                                  }}
                                >
                                  <RedoIcon
                                    style={{
                                      height: "0.8em",
                                      width: "0.8em",
                                      color: "#d1351d",
                                    }}
                                  />
                                </IconButton>
                              </Tooltip>
                            )}

                          {tags.includes("-") && (
                            <IconButton
                              color="secondary"
                              aria-label="View GL"
                              sx={{ iconButton_SpacingSmall }}
                              onClick={(e) => {
                                e.stopPropagation();
                                updateGlViewDialog(true);
                                displayRangeGL(tags); // Call the function with the tag value
                                updateRange(tags);
                              }}
                            >
                              <VisibilityIcon
                                fontSize="small"
                                style={{
                                  height: "0.8em",
                                  width: "0.8em",
                                  // color: "#d1351d",
                                }}
                              />
                            </IconButton>
                          )}
                        </div>
                      }
                    </p>
                  }
                  sx={{ color: "#9e8a2f" }}
                />
              );
            })}
            {showInputFields[nodeId] && (
              <div style={{ display: "flex" }}>
                <TextField
                  autoFocus
                  size="small"
                  variant="outlined"
                  helperText="Press Enter to Submit Node"
                  placeholder="Enter Node"
                  inputProps={{ maxLength: 10 }}
                  value={inputValueNewNode}
                  inputRef={inputRefNewNode}
                  onChange={(e) => {
                    const capitalizedValue = e.target.value
                      .replace(/[^a-zA-Z0-9_\/-]/g, "")
                      .toUpperCase();
                    setInputValueNewNode(capitalizedValue);
                  }}
                  onBlur={() => {
                    // console.log("ttt1", showInputFields);
                    setShowInputFields({ ...showInputFields, [nodeId]: false });
                    setDuplicateNodeError(false);
                    setDBDuplicateNodeError(false);
                    setInputValueNewNode("");
                  }}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      const inputValue = e.target.value.trim();
                      if (inputValue) {
                        // console.log("ttt2", e.target.value, parentId);
                        handleAddNode(nodeId, inputValueNewNode, parentId);
                        // handleDataFromChild();
                      }
                      setInputValueNewNode("");
                    }
                  }}
                />
                {duplicateNodeError ? (
                  <Typography
                    variant="caption"
                    sx={{ alignItems: "center", pl: 2 }}
                    color="error"
                  >
                    This node already exists in the Hierarchy!{" "}
                  </Typography>
                ) : (
                  <></>
                )}
                  {DBduplicateNodeError ? (
                  <Typography
                    variant="caption"
                    sx={{ alignItems: "center", pl: 2 }}
                    color="error"
                  >
                    This node is already present in some ongoing request!{" "}
                  </Typography>
                ) : (
                  <></>
                )}
              </div>
            )}

            {showInputFieldsForPC[nodeId] && (
              <div>
                <TextField
                  autoFocus
                  size="small"
                  variant="outlined"
                  helperText={`Press Enter to Submit ${module}`}
                  placeholder={`Enter ${module}`}
                  // inputProps={{ maxLength: 10 }}
                  inputProps={{
                    maxLength: module === "General Ledger" ? 6 : 10,
                  }}
                  value={newPcValues[nodeId] || ""}
                  onBlur={() => {
                    setShowInputFieldsForPC({
                      ...showInputFieldsForPC,
                      [nodeId]: false,
                    });
                    setDuplicateTagError(false);
                    setRequiredLengthError(false);
                    setNewPcValues({
                      ...newPcValues,
                      [nodeId]: "", // Clear the input value on blur
                    });
                  }}
                  onChange={(e) => {
                    setNewPcValues({
                      ...newPcValues,
                      [nodeId]: e.target.value,
                    });
                    setRequiredLengthError(false);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      const inputValue = e.target.value.trim();
                      const requiredLength =
                        module === "General Ledger" ? 6 : 10;
                      if (inputValue.length !== requiredLength) {
                        setDuplicateTagError(false)
                        setRequiredLengthError(true);
                        return;
                      }
                      if (inputValue) {
                        setRequiredLengthError(false);
                        handleAddPc(nodeId, e.target.value);
                      }
                    }
                  }}
                />
                {duplicateTagError ? (
                  <Typography
                    variant="caption"
                    sx={{ alignItems: "center", pl: 2 }}
                    color="error"
                  >
                    {`This ${module} already exists in the Hierarchy!`}{" "}
                  </Typography>
                ) : (
                  <></>
                )}
                {requiredLengthError ? (
                  <Typography
                    variant="caption"
                    sx={{ alignItems: "center", pl: 2 }}
                    color="error"
                  >
                    {`${module} needs to be of length ${
                      module === "General Ledger" ? 6 : 10
                    }`}
                  </Typography>
                ) : (
                  <></>
                )}
                {invalidTagError ? (
                  <Typography
                    variant="caption"
                    sx={{ alignItems: "center", pl: 2 }}
                    color="error"
                  >
                    {`Invalid ${module}`}
                  </Typography>
                ) : (
                  <></>
                )}
                {errorMessage ? (
                  <Typography
                    variant="caption"
                    sx={{ alignItems: "center", pl: 2 }}
                    color="error"
                  >
                    {errorMessage}
                  </Typography>
                ) : (
                  <></>
                )}
              </div>
            )}

            {module === "General Ledger" &&
              showInputFieldsForRangeGL[nodeId] && (
                <div>
                  <TextField
                    autoFocus
                    size="small"
                    variant="outlined"
                    helperText="Press Enter to Submit GL Range"
                    placeholder={`Enter GL Range`}
                    inputProps={{ maxLength: 13 }}
                    value={newPcValues[nodeId] || ""}
                    onBlur={() => {
                      setShowInputFieldsForRangeGL({
                        ...showInputFieldsForRangeGL,
                        [nodeId]: false,
                      });
                      setDuplicateTagError(false);
                      setRequiredLengthError(false);
                      setNewPcValues({
                        ...newPcValues,
                        [nodeId]: "", // Clear the input value on blur
                      });
                    }}
                    onChange={(e) => {
                      let inputValue = e.target.value.replace(/-/g, ""); // Remove any existing hyphen
                      if (inputValue.length > 6) {
                        inputValue =
                          inputValue.slice(0, 6) +
                          "-" +
                          inputValue.slice(6, 12); // Add hyphen after 6 characters
                      }
                      setNewPcValues({
                        ...newPcValues,
                        [nodeId]: inputValue,
                      });
                      setRequiredLengthError(false);
                    }}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        const inputValue = e.target.value.trim();
                        const requiredLength = 13;
                        if (inputValue.length !== requiredLength) {
                          console.log(
                            `Input length should be exactly ${requiredLength} characters`
                          );
                          setRequiredLengthError(true);
                          return;
                        }
                        if (inputValue) {
                          setRequiredLengthError(false);
                          handleAddRangeGL(nodeId, e.target.value);
                        }
                      }
                    }}
                  />
                  {errorMessage ? (
                    <Typography
                      variant="caption"
                      sx={{ alignItems: "center", pl: 2, pt: 1 }}
                      color="error"
                    >
                      {errorMessage}
                    </Typography>
                  ) : (
                    <></>
                  )}
                </div>
              )}

            <div style={{ display: "flex", alignItems: "center" }}>
              {!showInputFields[nodeId] && (
                <Tooltip title={`Add a new Node under ${item.title}`}>
                  {buttonAdd && !item?.tags?.length && (
                    <IconButton
                      color="primary"
                      aria-label="upload picture"
                      component="label"
                      sx={iconButton_SpacingSmall}
                    >
                      <AddLinkIcon
                        style={{
                          height: "0.8em",
                          width: "0.8em",
                          color: "green",
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowInputFields({
                            ...showInputFields,
                            [nodeId]: true,
                          });
                        }}
                      />
                    </IconButton>
                  )}
                </Tooltip>
              )}

              {!showInputFieldsForPC[nodeId] && (
                <Tooltip title={`Add a new ${module} under ${item?.title}`}>
                  {buttonAdd && item?.child?.length === 0 && (
                    <IconButton
                      color="primary"
                      aria-label="Add PC"
                      sx={iconButton_SpacingSmall}
                      onClick={(e) => {
                        e.stopPropagation();
                        setRequiredLengthError(false);
                        setInvalidTagError(false);
                        setErrorMessage("");
                        setShowInputFieldsForPC({
                          ...showInputFieldsForPC,
                          [nodeId]: true,
                        });
                      }}
                    >
                      <TrendingUpIcon
                        style={{
                          height: "0.8em",
                          width: "0.8em",
                          color: "green",
                        }}
                      />
                    </IconButton>
                  )}
                </Tooltip>
              )}

              {module === "General Ledger" &&
                !showInputFieldsForRangeGL[nodeId] && (
                  <Tooltip title={`Add GL in range`}>
                    {buttonAdd && item?.child?.length === 0 && (
                      <IconButton
                        color="primary"
                        aria-label="Add GL Range"
                        sx={iconButton_SpacingSmall}
                        onClick={(e) => {
                          e.stopPropagation();
                          setRequiredLengthError(false);
                          setInvalidTagError(false);
                          setErrorMessage("");
                          setShowInputFieldsForRangeGL({
                            ...showInputFieldsForRangeGL,
                            [nodeId]: true,
                          });
                        }}
                      >
                        <ControlPointDuplicateIcon
                          style={{
                            height: "0.8em",
                            width: "0.8em",
                            color: "green",
                          }}
                        />
                      </IconButton>
                    )}
                  </Tooltip>
                )}
            </div>
          </StyledTreeItem>
        );
      });
    };

    return (
      <TreeView
        aria-label="customized"
        defaultExpanded={allNodeIds}
        defaultCollapseIcon={<MinusSquare />}
        defaultExpandIcon={<PlusSquare />}
        defaultEndIcon={<CloseSquare />}
        // sx={{ overflowX: "hidden" }}
      >
        {createChildNodes(newTreeData, null)}
      </TreeView>
    );
  };

  const highlightText = (text, searchTerm) => {
    if (!searchTerm) return text;

    const regex = new RegExp(`(${searchTerm})`, "gi");
    const parts = text.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <span key={index} style={{ backgroundColor: "yellow" }}>
          {part}
        </span>
      ) : (
        part
      )
    );
  };

  // const textToSearch = `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer nec odio. Praesent libero. Sed cursus ante dapibus diam. Sed nisi. Nulla quis sem at nibh elementum imperdiet. Duis sagittis ipsum. Praesent mauris. Fusce nec tellus sed augue semper porta.`;
  
  return (
    <div>
      {/* {blurLoading ? (
        <>
          <Card sx={{ width: "100%" }}>
            <CardContent>
              <Stack
                spacing={1}
                direction={"row"}
                justifyContent={"flex-start"}
                alignItems={"center"}
              >
                <Skeleton animation="pulse" width={600} height={50} />
              </Stack>
              <Stack
                spacing={1}
                direction={"row"}
                justifyContent={"flex-start"}
                alignItems={"center"}
              >
                <Skeleton animation="pulse" width={600} height={50} />
              </Stack>
              <Stack
                spacing={1}
                direction={"row"}
                justifyContent={"flex-start"}
                alignItems={"center"}
              >
                <Skeleton animation="pulse" width={600} height={50} />
              </Stack>
              <Stack
                spacing={1}
                direction={"row"}
                justifyContent={"flex-start"}
                alignItems={"center"}
              >
                <Skeleton animation="pulse" width={600} height={50} />
              </Stack>
              <Stack
                spacing={1}
                direction={"row"}
                justifyContent={"flex-start"}
                alignItems={"center"}
              >
                <Skeleton animation="pulse" width={600} height={50} />
              </Stack>
              <Stack
                spacing={1}
                direction={"row"}
                justifyContent={"flex-start"}
                alignItems={"center"}
              >
                <Skeleton animation="pulse" width={600} height={50} />
              </Stack>
            </CardContent>
          </Card>
        </>
      ) : ( */}
      {getTreeNodes(newTreeData)}
 

      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />

      <Dialog
        fullWidth={true}
        maxWidth={"md"}
        open={openPersonResponsibleDialog}
        onClose={handleClosePersonReponsibleDialog}
        sx={{
          "&::webkit-scrollbar": {
            width: "1px",
          },
        }}
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            // borderBottom: "1px solid grey",
            display: "flex",
          }}
        >
          <Typography variant="h6">Select Person Responsible</Typography>
          <IconButton
            sx={{ width: "max-content" }}
            onClick={handleClosePersonReponsibleDialog}
            children={<CloseIcon />}
          />
        </DialogTitle>
        <DialogContent sx={{ padding: ".5rem 1rem" }}>
          <Grid container spacing={1}>
            <Box sx={{ width: "100%", height: 400 }}>
              <ReusableTable
                width="100%"
                rows={personResponsibleData ?? []}
                columns={columnsTable}
                getRowIdValue={"EmplId"}
                hideFooter={true}
                checkboxSelection={false}
                page={page}
                pageSize={pageSize}
                rowCount={count ?? personResponsibleData?.length ?? 0}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                showCustomNavigation={true}
                callback_onRowDoubleClick={(paramsNew) => {
                  console.log("params", paramsNew.row);
                  // console.log("PCPersonResponsible", paramsNew?.row?.NetworkId.toUpperCase());
                  onEdit(
                    "PCPersonResponsible",
                    `${paramsNew?.row?.EmplId}`,
                    newPrNodeId
                  );
                  setOpenPersonResponsibleDialog(false);
                  setNewPrNodeId(null);
                }}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
              />
            </Box>
          </Grid>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ReusableHierarchyTree;
