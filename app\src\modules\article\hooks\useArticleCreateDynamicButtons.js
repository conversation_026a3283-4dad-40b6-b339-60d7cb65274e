import { useEffect, useState } from "react";
import { INITIAL_PAYLOAD_MAP, LOCAL_STORAGE_KEYS, MODULE_MAP } from "@constant/enum";
import { useSelector } from "react-redux";
import { CreatebuttonPriority } from '@constant/buttonPriority'
import { getLocalStorage } from "@helper/helper";
import { doAjax } from "@components/Common/fetchService";
import useLogger from "@hooks/useLogger";

export const useArticleCreateDynamicButtons = (taskData, applicationConfig, destination_IDM, BUTTON_NAME, module) => {
  const { customError } = useLogger();
  const [buttonsIDM, setButtonsIDM] = useState([]);
  const [filteredButtons, setFilteredButtons] = useState([]);
  const [showWfLevels, setShowWfLevels] = useState(false);
  const queryParams = new URLSearchParams(location.search);
  const selectedModuleInitialPayload = INITIAL_PAYLOAD_MAP[module] || (() => ({}));
  const initialPayload = useSelector(selectedModuleInitialPayload)
  const requestTypeFromTask = useSelector((state) => state?.userManagement?.taskData?.ATTRIBUTE_2);
  const isrequestType = queryParams.get("RequestType");

  useEffect(() => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_MAT_DYN_BUTTON_CONFIG",
      version: "v3",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME": module === MODULE_MAP.BOM ? "BOM" : "Article",
          "MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE": isrequestType || requestTypeFromTask || initialPayload?.RequestType,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        setButtonsIDM(data?.data?.result[0]?.MDG_MAT_DYN_BUTTON_CONFIG);
      }
    };

    const hError = (error) => {
      customError(error);
    };

    const url =
      applicationConfig.environment === "localhost"
        ? `/${destination_IDM}/rest/v1/invoke-rules`
        : `/${destination_IDM}/v1/invoke-rules`;

    doAjax(url, "post", hSuccess, hError, payload);
  }, [taskData]);

  useEffect(() => {
    const savedTask = getLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK, true, {});
    const effectiveTaskDesc = savedTask?.taskDesc || taskData?.taskDesc;

    const filtered = buttonsIDM.filter(
      (button) => button.MDG_MAT_DYN_BTN_TASK_NAME === effectiveTaskDesc
    );

    const sortedButtonArr = filtered.sort((a, b) => {
      const priorityA = CreatebuttonPriority[a.MDG_MAT_DYN_BTN_ACTION_TYPE] ?? 999;
      const priorityB = CreatebuttonPriority[b.MDG_MAT_DYN_BTN_ACTION_TYPE] ?? 999;
      return priorityA - priorityB;
    });

    setFilteredButtons(sortedButtonArr);

    if (
      sortedButtonArr.find((btn) => btn.MDG_MAT_DYN_BTN_BUTTON_NAME === BUTTON_NAME.SEND_BACK) ||
      sortedButtonArr.find((btn) => btn.MDG_MAT_DYN_BTN_BUTTON_NAME === BUTTON_NAME.CORRECTION)
    ) {
      setShowWfLevels(true);
    }
  }, [buttonsIDM]);

  return { filteredButtons, showWfLevels };
};
