import { useDispatch, useSelector } from "react-redux";
import { 
  CURRENT_DATE, 
  getObjectValue, 
  getPreviousValueFromPayload,
  transformChangeLogArray, 
} from "@helper/glhelper";
import { CREATE_CHANGE_LOG_MANDATORY_FIELDS, CREATE_VIEWS_MANIPULATION } from "@constant/changeTemplates";
import { setCreateChangeLogData, setCreateTemplateArray } from "@app/changeLogReducer";
import { setCreateChangeLogDataGL } from "../app/changeLogReducer";

export const useChangeLogUpdateGl = () => {
  const dispatch = useDispatch();
  const userMangmentData = useSelector((state) => state.userManagement.userData);
  const createPayloadCopyForChangeLog = useSelector((state) => state.changeLog.createPayloadCopyForChangeLog || []);


  
  const requestHeaderSlice = useSelector(
      (state) => state.generalLedger.payload?.requestHeaderdata || {}
    );
  const createChangeLogData = useSelector((state) => state.changeLog.createChangeLogDataGL);
  
  
  const createTemplateArray = useSelector((state) => state.changeLog.createTemplateArray);


  const checkDisplaychangeValue =(createPayloadCopyForChangeLog,changeLogPayload)=>{

    let displayPayload=createPayloadCopyForChangeLog?.rowsBodyData?.[Object.keys(changeLogPayload)]
    let newChangeLogPayload=changeLogPayload?.[Object.keys(changeLogPayload)]

    const filteredChangeLog = newChangeLogPayload?.filter(entry => {
      const key = entry.JsonName;
      const currentValuePart = entry.CurrentValue.split("-")[0]?.trim();
      const mainValue = displayPayload[key]?.trim();

      return currentValuePart !== mainValue;
    });

   

  }

 
  const updateChangeLogGl = ({
    uniqueId,
    viewName,
    plantData,
    fieldName,
    jsonName,
    currentValue,
    requestId,
    isDescriptionData = false,
    isUnitOfMeasure = false,
    isAdditionalEAN = false,
    uomId = null,
    eanId = null,
    language,
  }) => {
    let previousValue;
     {
      previousValue = getPreviousValueFromPayload(
        uniqueId,
        viewName,
        plantData,
        jsonName,
        createPayloadCopyForChangeLog
      );
    }
   
    const objectValues = getObjectValue(CREATE_CHANGE_LOG_MANDATORY_FIELDS, viewName);
    let detailsOfObject=createPayloadCopyForChangeLog?.rowsBodyData?.[uniqueId]
    let glAccNumber =createPayloadCopyForChangeLog?.rowsBodyData?.[uniqueId]?.GLAccount
    let AccountType = createPayloadCopyForChangeLog?.rowsBodyData?.[uniqueId]?.Accounttype
   
    const changeFields = {
      ObjectNo: `${uniqueId}`,
      ChangedBy: userMangmentData?.emailId,
      ChangedOn: CURRENT_DATE,
      FieldName: fieldName,
      JsonName:jsonName,
      PreviousValue: previousValue,
      CurrentValue: currentValue,
      SAPValue: previousValue,
      ObjNo:glAccNumber,
      AccType: AccountType
    };

    
  
    dispatch(setCreateTemplateArray(changeFields));
    const updatedCreateTemplateArray = [...createTemplateArray, changeFields];
    const finalChangeLogDataPayload = transformChangeLogArray(updatedCreateTemplateArray);
    
    let changeLogPayload = {
      ...finalChangeLogDataPayload
    };
    
    
    dispatch(setCreateChangeLogDataGL(changeLogPayload));
  };

  return { updateChangeLogGl };
};
