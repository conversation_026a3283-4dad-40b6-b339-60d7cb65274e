import {
  CheckCircleOutline,
  Close,
  CloseFullscreen,
  DangerousOutlined,
  InfoOutlined,
  TrackChangesOutlined,
  WarningAmberOutlined,
} from "@mui/icons-material";
import {
  <PERSON>,
  Button,
  Card,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  Slide,
  TextField,
  Typography,
} from "@mui/material";
import CalendarMonthOutlinedIcon from "@mui/icons-material/CalendarMonthOutlined";
import { Stack, border, width } from "@mui/system";
import React, { useEffect, useRef, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import ReusableSnackBar from "./ReusableSnackBar";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { doAjax } from "./fetchService";
import {
  destination_BankKey,
  destination_CostCenter_Mass,
  destination_GeneralLedger,
  destination_ProfitCenter_Mass,
} from "../../destinationVariables";
import { outerContainer_Information } from "./commonStyles";
import { captureScreenShot } from "../../functions";
import { usePDF } from "react-to-pdf";
import { PDFExport } from "@progress/kendo-react-pdf";
import { v4 as uuidv4 } from "uuid";
import moment from "moment";
import { useSelector } from "react-redux";
import AccessTimeFilledRoundedIcon from "@mui/icons-material/AccessTimeFilledRounded";
import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineSeparator,
  timelineItemClasses,
} from "@mui/lab";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import PersonIcon from "@mui/icons-material/Person";
function ReusableDialogForAllData({
  poId,
  roleName,
  SR_REDIRECT,
  handleOk = null,
  dialogPlaceholder,
  dialogState,
  setDialogState,
  openReusableDialog,
  closeReusableDialog,
  handleCancle,
  dialogTitle,
  dialogMessage,
  showInputText,
  inputText,
  setInputText,
  handleDialogConfirm = () => {},
  handleDialogReject = () => {},
  showCancelButton,
  dialogCancelText,
  showOkButton,
  dialogOkText,
  dialogSeverity,
  requestId,
  commentArtifactType,
  user,
  loadingStatus,
  handleExtraButton,
  handleExtraText,
  showExtraButton,
  onClose,
  alertMsg,
  errorMessage,
  rowsDataForPayload,
}) {
  const [openSnackBar, setOpenSnackBar] = useState(false);
  const [dataOfDisplay, setDataOfDisplay] = useState({});
  const [comments, setComments] = useState([]);
  const [changelogData, setChangelogData] = useState([]);
  const [maxWidth, setMaxWidth] = useState("sm");
  const handleSnackBarClick = () => {
    setOpenSnackBar(true);
  };
  const [tempData, settempData] = useState([
    // {
    //   activityId: "usertask1",
    //   createdAt: "2023-11-02T06:32:16.294Z",
    //   completedAt: "2023-11-02T06:35:26.879Z",
    //   description: null,
    //   id: "90c0c5bb-7949-11ee-b43e-eeee0a8e99ce",
    //   processor: "<EMAIL>",
    //   status: "COMPLETED",
    //   subject: "Supplier Onboarding Process",
    //   workflowInstanceId: "8f10f29d-7949-11ee-b43e-eeee0a8e99ce",
    //   dueDate: "2023-11-06T06:32:12.656Z",
    //   createdBy: "<EMAIL>",
    //   lastChangedAt: "2023-11-02T06:35:26.883Z",
    // },
    // {
    //   activityId: "usertask4",
    //   createdAt: "2023-11-02T06:32:16.938Z",
    //   completedAt: null,
    //   description: null,
    //   id: "9121348b-7949-11ee-8b62-eeee0a8a0233",
    //   processor: null,
    //   status: "READY",
    //   subject: "Supplier Follow Up",
    //   workflowInstanceId: "8f10f29d-7949-11ee-b43e-eeee0a8e99ce",
    //   dueDate: "2023-11-06T06:32:12.656Z",
    //   createdBy: "<EMAIL>",
    //   lastChangedAt: "2023-11-02T06:32:16.938Z",
    // },
    // {
    //   activityId: "usertask12",
    //   createdAt: "2023-11-02T06:35:30.699Z",
    //   completedAt: "2023-11-02T06:40:28.106Z",
    //   description: null,
    //   id: "049ec9f6-794a-11ee-8b62-eeee0a8a0233",
    //   processor: "<EMAIL>",
    //   status: "COMPLETED",
    //   subject: "Finance Task",
    //   workflowInstanceId: "8f10f29d-7949-11ee-b43e-eeee0a8e99ce",
    //   dueDate: "2023-11-06T06:35:25.227Z",
    //   createdBy: "<EMAIL>",
    //   lastChangedAt: "2023-11-02T06:40:28.109Z",
    // },
    // {
    //   activityId: "usertask1",
    //   createdAt: "2023-11-02T06:40:29.361Z",
    //   completedAt: "2023-11-02T06:42:30.612Z",
    //   description: null,
    //   id: "b6a2d75e-794a-11ee-b43e-eeee0a8e99ce",
    //   processor: "<EMAIL>",
    //   status: "COMPLETED",
    //   subject: "Supplier Onboarding Process",
    //   workflowInstanceId: "8f10f29d-7949-11ee-b43e-eeee0a8e99ce",
    //   dueDate: "2023-11-06T06:40:26.808Z",
    //   createdBy: "<EMAIL>",
    //   lastChangedAt: "2023-11-02T06:42:30.615Z",
    // },
    // {
    //   activityId: "usertask6",
    //   createdAt: "2023-11-02T06:42:33.954Z",
    //   completedAt: "2023-11-02T06:49:28.720Z",
    //   description: null,
    //   id: "00e6871c-794b-11ee-8b62-eeee0a8a0233",
    //   processor: "<EMAIL>",
    //   status: "COMPLETED",
    //   subject: "Buyer Task",
    //   workflowInstanceId: "8f10f29d-7949-11ee-b43e-eeee0a8e99ce",
    //   dueDate: "2023-11-06T06:42:29.024Z",
    //   createdBy: "<EMAIL>",
    //   lastChangedAt: "2023-11-02T06:49:28.723Z",
    // },
    // {
    //   activityId: "usertask7",
    //   createdAt: "2023-11-02T06:49:31.316Z",
    //   completedAt: "2023-11-02T06:52:58.180Z",
    //   description: null,
    //   id: "f9aa752d-794b-11ee-b0fd-eeee0a80293f",
    //   processor: "<EMAIL>",
    //   status: "COMPLETED",
    //   subject: "Procurement Lead Task",
    //   workflowInstanceId: "8f10f29d-7949-11ee-b43e-eeee0a8e99ce",
    //   dueDate: "2023-11-06T06:49:27.545Z",
    //   createdBy: "<EMAIL>",
    //   lastChangedAt: "2023-11-02T06:52:58.184Z",
    // },
  ]);
  const appSettings = useSelector((state) => state.appSettings);
  const handleSnackBarClose = () => {
    setOpenSnackBar(false);
  };

  const handleCommentClick = () => {
    if (inputText) {
      const formData = new FormData();
      var comments = JSON.stringify({
        requestId: requestId,
        comment: inputText,
        commentArtifactType: commentArtifactType,
        createdBy: user,
        roleName: roleName,
        poNumber: poId,
      });
      formData.append("comments", comments);
      let hSuccess = (data) => {
        setInputText("");
      };
      let hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_ServiceRequest}/comment/saveCommentWithoutFile`,
        "postformdata",
        hSuccess,
        hError,
        formData
      );
    }
  };

  const getDisplayData = () => {
    console.log("inside the fn");
  };
  const { toPDF, targetRef } = usePDF({ filename: "demo.pdf" });
  useEffect(() => {
    let payload = {};
    let url = "";
    let urForComments = "";
    let changeLogUrl = "";
    if (
      rowsDataForPayload?.requestId?.includes("NCS") ||
      rowsDataForPayload?.requestId?.includes("CCS")
    ) {
      console.log("inside cost");
      payload = {
        id: rowsDataForPayload?.id ?? "",
        costCenter: rowsDataForPayload?.costCenter ?? "",
        controllingArea: rowsDataForPayload?.controllingAreaDataCopy ?? "",
        reqStatus: rowsDataForPayload?.reqStatus ?? "Approved",
        screenName: rowsDataForPayload?.requestType ?? "Change",
      };
      url = `/${destination_CostCenter_Mass}/data/displayCostCenter`;
      urForComments = `/${destination_CostCenter_Mass}/activitylog/fetchTaskDetailsForRequestId?requestId=${rowsDataForPayload?.requestId}`;
      changeLogUrl = `/${destination_CostCenter_Mass}/activitylog/getChangeLogDetails?requestId=${rowsDataForPayload?.requestId?.slice(
        3
      )}&isMass=${false}&coaCostCenter=${
        rowsDataForPayload?.controllingArea + rowsDataForPayload?.costCenter
      }`;
    } else if (
      rowsDataForPayload?.requestId?.includes("NPS") ||
      rowsDataForPayload?.requestId?.includes("CPS")
    ) {
      console.log("inside profit");
      payload = {
        id: rowsDataForPayload?.id ?? "",
        profitCenter: rowsDataForPayload?.profitCenter ?? "",
        controllingArea: rowsDataForPayload?.controllingArea ?? "",
        reqStatus: rowsDataForPayload?.reqStatus ?? "Approved",
        screenName: rowsDataForPayload?.requestType ?? "Change",
      };
      url = `/${destination_ProfitCenter_Mass}/data/displayProfitCenter`;
      urForComments = `/${destination_ProfitCenter_Mass}/activitylog/fetchTaskDetailsForRequestId?requestId=${rowsDataForPayload?.requestId}`;
      changeLogUrl = `/${destination_ProfitCenter_Mass}/activitylog/getChangeLogDetails?requestId=${rowsDataForPayload?.requestId?.slice(
        3
      )}&isMass=${false}&coaProfitCenter=${
        rowsDataForPayload?.controllingArea + rowsDataForPayload?.profitCenter
      }`;
    } else if (
      rowsDataForPayload?.requestId?.includes("NLS") ||
      rowsDataForPayload?.requestId?.includes("CLS")
    ) {
      console.log("inside ledger");
      payload = {
        id: rowsDataForPayload?.id ?? "",
        glAccount: rowsDataForPayload?.glAccount ?? "",
        compCode: rowsDataForPayload?.compCode ?? "",
        reqStatus: rowsDataForPayload?.reqStatus ?? "Approved",
        screenName: rowsDataForPayload?.requestType ?? "Change",
      };
      url = `/${destination_GeneralLedger}/data/displayGeneralLedger`;
      urForComments = `/${destination_GeneralLedger}/activitylog/fetchTaskDetailsForRequestId?requestId=${rowsDataForPayload?.requestId}`;
      changeLogUrl = `/${destination_GeneralLedger}/activitylog/getChangeLogDetails?requestId=${rowsDataForPayload?.requestId?.slice(
        3
      )}&isMass=${false}&compCodeGlAcc=${
        rowsDataForPayloadcompCode + rowsDataForPayload?.glAccount
      }`;
    } else if (
      rowsDataForPayload?.requestId?.includes("NBS") ||
      rowsDataForPayload?.requestId?.includes("CBS")
    ) {
      console.log("inside bankkey");
      payload = {
        id: rowsDataForPayload?.id ?? "",
        bankCtry: rowsDataForPayload?.bankCtryReg ?? "",
        bankKey: rowsDataForPayload?.bankKey ?? "",
        reqStatus: rowsDataForPayload?.reqStatus ?? "Approved",
        screenName: rowsDataForPayload?.requestType ?? "Change",
      };
      url = `/${destination_BankKey}/data/displayBankKey`;
      urForComments = `/${destination_BankKey}/activitylog/fetchTaskDetailsForRequestId?requestId=${rowsDataForPayload?.requestId}`;
      changeLogUrl = `/${destination_BankKey}/activitylog/getChangeLogDetails?requestId=${rowsDataForPayload?.requestId?.slice(
        3
      )}&isMass=${false}&countryBankKey=${
        rowsDataForPayload?.bankCtryReg + rowsDataForPayload?.bankKey
      }`;
    }
    const hSuccess = (data) => {
      // setIsLoading(false);
      console.log("dataaaaaaa", data.body);
      // getRegion(data?.body?.BankCtry);
      const responseBody = data.body.viewData;
      console.log("responsebody", responseBody);
      let viewDataArray = Object.entries(responseBody);
      // console.log(object)
      const toSetArray = {};
      viewDataArray.map((item) => {
        console.log("bottle", item[1]);
        let temp = Object.entries(item[1]);
        console.log("notebook", temp);
        temp.forEach((fieldGroup) => {
          fieldGroup[1].forEach((field) => {
            toSetArray[field.fieldName] = field.value;
          });
        });
        return item;
      });
      setDataOfDisplay(toSetArray);
    };
    const hError = (error) => {
      console.log(error);
    };
    const hCommentSuccess = (data) => {
      var commentRows = [];
      data.body.forEach((cmt) => {
        var tempRow = {
          id: cmt.requestId,
          comment: cmt.comment,
          user: cmt.createdByUser,
          createdAt: cmt.updatedAt,
        };
        commentRows.push(tempRow);
      });
      setComments(commentRows);
    };
    const hCommentError = (error) => {
      console.log(error);
    };

    const hChangeLogSuccess = (data) => {
      var rows = [];
      for (let index = 0; index < data?.body?.length; index++) {
        var tempObj = data?.body[index];
        var tempRow = {
          id: uuidv4(),
          ChangedBy: tempObj.ChangedBy,
          ChangedOn: tempObj.ChangedOn,
          FieldName: tempObj.FieldName,
          PreviousValue: tempObj.PreviousValue,
          CurrentValue: tempObj.CurrentValue,
        };
        rows.push(tempRow);
      }
      console.log(rows, "rows");
      // setloading(false);
      setChangelogData(rows);
    };
    const hChangeLogError = (error) => {
      console.log(error);
    };
    doAjax(url, "post", hSuccess, hError, payload);
    doAjax(urForComments, "get", hCommentSuccess, hCommentError);
    doAjax(changeLogUrl, "get", hChangeLogSuccess, hChangeLogError);
    console.log("payload", payload);
  }, []);
  let pdfExportComponent = useRef();
  // useEffect(()=>{
  //   pdfExportComponent?.current?.save()
  // },[])
  const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction="up" ref={ref} {...props} />;
  });

  // console.log("rmdata", rowsDataForPayload, Object.entries(dataOfDisplay));
  return (
    <>
      <Dialog
        fullScreen
        // maxWidth={maxWidth}
        // sx={{ width: "80%" }}
        // sx={{minHeight:'80vh', maxHeight:'80vh'}}
        hideBackdrop={false}
        elevation={2}
        PaperProps={{
          sx: { boxShadow: "none" },
        }}
        // TransitionComponent={Transition}
        open={true}
        onClose={onClose ? onClose : closeReusableDialog}
      >
        <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        >
          <Grid item></Grid>
          <Grid item>
            {(dialogSeverity !== "danger" || showInputText) && (
              <IconButton
                onClick={(e) => {
                  e.stopPropagation();
                  closeReusableDialog();
                }}
              >
                <CloseIcon />
              </IconButton>
            )}
          </Grid>
        </Grid>

        <DialogContent>
          <Stack>
            {/* <div style="backgroundImage: url('../../../public/favicon.ico')"> */}
            <div style={{ border: "2px Solid" }}>
              <div
                style={{
                  background: "#EAE9FF",
                  display: "flex",
                  justifyContent: "space-between",
                }}
              >
                <div style={{ marginTop: 2, marginLeft: 2 }}>
                  <h6>Request ID: {rowsDataForPayload?.requestId}</h6>
                </div>
                <div style={{ outerContainer_Information }}>
                  <h2>
                    <strong>SUMMARY</strong>
                  </h2>
                </div>

                <div alignItems={"end"}>
                  <Typography>
                    Created By: {rowsDataForPayload?.createdBy}
                  </Typography>
                  <Typography>
                    Created On: {rowsDataForPayload?.createdOn}
                  </Typography>
                </div>
              </div>
              {/* field value */}
              <div style={{ marginLeft: "5%" }}>
                <div style={{ width: "50%" }}>
                  <div style={{ display: "flex" }}>
                    <div style={{ width: "50%" }}>
                      <h5>
                        <u>Field</u>
                      </h5>
                    </div>

                    <div>
                      <h5>
                        <u>Value</u>
                      </h5>
                    </div>
                  </div>
                  {Object.entries(dataOfDisplay).map((item) => {
                    return (
                      <div>
                        <div style={{ display: "flex" }}>
                          <div style={{ width: "50%" }}>
                            <h6>{item[0]}</h6>
                          </div>

                          <div>
                            <p>
                              {item[1] != ""
                                ? item[1]
                                : item[1] === false
                                ? "No"
                                : item[1] === true
                                ? "Yes"
                                : "-"}
                            </p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* comments */}
              <div style={{ marginLeft: "5%", marginTop: "5%" }}>
                <h4>
                  <u>Comments</u>
                </h4>
                <div>
                  <h6>
                    {comments.map((item) => {
                      return (
                        <ul>
                          <li> {`${item?.comment} (${item?.user}) `}</li>
                        </ul>
                      );
                    })}
                  </h6>
                </div>
              </div>
              {/* change log */}
              <div style={{ margin: "5%" }}>
                <h4>
                  <u>Change Log</u>
                </h4>
                <div>
                  <h6>
                    {/* {comments.map((item) => {
                      return ( */}
                    <table style={{ width: "100%", border: "1px solid black" }}>
                      <tr>
                        <th style={{ border: "1px solid black" }}>
                          Field Name
                        </th>
                        <th
                          style={{
                            border: "1px solid black",
                            textAlign: "left",
                            padding: "8px",
                          }}
                        >
                          Old Value
                        </th>
                        <th
                          style={{
                            border: "1px solid black",
                            textAlign: "left",
                            padding: "8px",
                          }}
                        >
                          New Value
                        </th>
                        <th
                          style={{
                            border: "1px solid black",
                            textAlign: "left",
                            padding: "8px",
                          }}
                        >
                          Updated By
                        </th>
                        <th
                          style={{
                            border: "1px solid black",
                            textAlign: "left",
                            padding: "8px",
                          }}
                        >
                          Updated On
                        </th>
                      </tr>

                      {changelogData.map((item) => {
                        return (
                          <tr>
                            <td
                              style={{
                                border: "1px solid black",
                                padding: "8px",
                              }}
                            >
                              {item?.FieldName}
                            </td>
                            <td
                              style={{
                                border: "1px solid black",
                                padding: "8px",
                              }}
                            >
                              {item?.PreviousValue}
                            </td>
                            <td
                              style={{
                                border: "1px solid black",
                                padding: "8px",
                              }}
                            >
                              {item?.CurrentValue}
                            </td>
                            <td
                              style={{
                                border: "1px solid black",
                                padding: "8px",
                              }}
                            >
                              {item?.ChangedBy}
                            </td>
                            <td
                              style={{
                                border: "1px solid black",
                                padding: "8px",
                              }}
                            >
                              {moment(item?.ChangedOn).format(
                                appSettings?.dateFormat
                              )}
                            </td>
                          </tr>
                        );
                      })}
                    </table>
                    {/* );
                    })} */}
                  </h6>
                </div>
              </div>
              {/* taskflow */}
              <div style={{ margin: "5%" }}>
                <h4>
                  <u>Task Flow</u>
                </h4>
                <div>
                  <div
                    style={{
                      border: "1px solid",
                      borderRadius: "10px",
                      padding: "10px",
                      width: "50%",
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      <h6>Soumarya Sarkar</h6>
                      <p>24/02/2024</p>
                    </div>
                    <hr style={{ margin: "0px" }}></hr>
                    <div style={{ marginTop: "5px" }}>
                      <h5>Process Started</h5>
                    </div>
                    <div style={{ marginTop: "4%" }}>
                      {/* <i>Task Completion Time : 30 minutes </i> */}
                    </div>
                  </div>
                  <div style={{width:"50%"}}>
                 <i style={{display:"flex", justifyContent:"center"}} class="material-icons">arrow_downward</i>
                 </div>
                  <div
                    style={{
                      border: "1px solid",
                      borderRadius: "10px",
                      padding: "10px",
                      width: "50%",
                      // marginTop: "3%",
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      <h6>Amaresh Prasad</h6>
                      <p>24/02/2024</p>
                    </div>
                    <hr style={{ margin: "0px" }}></hr>
                    <div style={{ marginTop: "5px" }}>
                      <h5>MDM User Task</h5>
                    </div>
                    <div style={{ marginTop: "4%" }}>
                      <i>Task Completion Time : 5 minutes </i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* </div> */}
          </Stack>
        </DialogContent>

        <DialogActions
          sx={{
            padding: "1rem 1.5rem",
          }}
        >
          {(dialogSeverity === "warning" || showCancelButton) && (
            <Button
              variant="text"
              sx={{
                height: 40,
                minWidth: "4rem",
                textTransform: "none",
                borderColor: "#3B30C8",
                color: "#3B30C8",
              }}
              onClick={(e) => {
                e.stopPropagation();
                handleDialogReject();
              }}
            >
              Cancel
            </Button>
          )}
          {showOkButton && (
            <Button
              variant="contained"
              style={{
                height: 40,
                minWidth: "4rem",
                backgroundColor: "#3B30C8",
                textTransform: "none",
              }}
              onClick={() => {
                handleOk ? handleOk() : closeReusableDialog();
                handleDialogConfirm();
                handleCommentClick();
                handleDialogReject();
                handleSnackBarClick();
              }}
            >
              {dialogOkText}
            </Button>
          )}
          {(dialogSeverity === "warning" || dialogSeverity === "danger") && (
            <Button
              variant="contained"
              style={{
                height: 40,
                minWidth: "4rem",
                backgroundColor: "#3B30C8",
                textTransform: "none",
              }}
              onClick={() => {
                handleOk ? handleOk() : closeReusableDialog();
                handleDialogConfirm();
                handleCommentClick();
                handleDialogReject();
                // handleSnackBarClick();
              }}
            >
              {dialogOkText}
            </Button>
          )}
          {showExtraButton && (
            <Button
              variant="contained"
              style={{
                height: 40,
                minWidth: "4rem",
                backgroundColor: "#3B30C8",
                textTransform: "none",
              }}
              onClick={handleExtraButton}
            >
              {handleExtraText}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
}
function generateHtmlForPdf(data) {
  return <></>;
}
export default ReusableDialogForAllData;
