import { doAjax } from 'your-ajax-utils'; // Update with actual import
import { ERROR_MESSAGES } from 'your-constants';
import { END_POINTS } from 'your-endpoints';
import { customError } from 'your-error-handler';

export const useCheckDuplicateMatNo = ({
  requestDetails,
  singlePayloadData,
  destination_MaterialMgmt,
  setOpenSnackbar,
  setMessageDialogMessage,
  setAlertType,
}) => {
  const checkDuplicateMatNo = (matNo, requestId = "") => {
    return new Promise((resolve) => {
      const payload = [
        {
          materialNo: matNo,
          requestNo: requestId || requestDetails?.requestId,
        },
      ];

      const successHandler = (data) => {
        if (data?.body?.length) {
          setOpenSnackbar(true);
          setMessageDialogMessage(
            `Duplicate material number ${data.body[0].split("$^$")[0]}` +
              ` (${data.body[0].split("$^$")[1]})`
          );
          setAlertType("error");
          resolve(true);
        } else {
          resolve(false);
        }
      };

      const errorHandler = (error) => {
        customError(error);
        resolve(false);
      };

      let count = 0;
      Object.keys(singlePayloadData).forEach((key) => {
        if (key.includes("-") || /\d/.test(key)) {
          if (singlePayloadData[key]?.headerData?.materialNumber === matNo)
            count++;
        }
      });

      if (count > 1) {
        setOpenSnackbar(true);
        setMessageDialogMessage(`${ERROR_MESSAGES.DUPLICATE_MATERIAL}${matNo}`);
        setAlertType("error");
        resolve(true);
      } else {
        doAjax(
          `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION?.MAT_NO_DUPLICATE_CHECK}`,
          "post",
          successHandler,
          errorHandler,
          payload
        );
      }
    });
  };

  return checkDuplicateMatNo;
};
