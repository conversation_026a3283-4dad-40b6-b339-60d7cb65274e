import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  dropDown: {},
  isOdataApiCalled:false,
};

export const profitCenterDropdownSlice = createSlice({
  name: "profitCenterAllDropDown",
  initialState,
  reducers: {
    setDropDown: (state, action) => {
      state.dropDown[action.payload.keyName] = action.payload.data;
    },
    setOdataApiCall:(state,action) => {
      state.isOdataApiCalled = action.payload;
    }
  },
});

export const { setDropDown, setDependentDropdown, setOdataApiCall } = profitCenterDropdownSlice.actions;

export default profitCenterDropdownSlice.reducer;
