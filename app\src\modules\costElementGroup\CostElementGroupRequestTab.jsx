import { useEffect, useState } from "react";
import {
  <PERSON>,
  Button,
  Grid,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Step,
  StepButton,
  IconButton,
  DialogContent,
  DialogActions,
} from "@mui/material";
import { doAjax } from "../../components/Common/fetchService.jsx";
import {
  destination_IDM,
  destination_MaterialMgmt,
  destination_GeneralLedger_Mass,
} from "../../destinationVariables";
import { useDispatch, useSelector } from "react-redux";
import { usePDF } from "react-to-pdf";
import {
  clearTemplateArray,
  clearChangeLogData,
  setDataLoading,
  setChangeFieldRows,
  setChangeFieldRowsDisplay,
  resetPayloadData,
  setMatlNoData,
  setRequestorPayload,
  clearDynamicKeyValue,
  updateSelectedRows,
  setPlantData,
} from "@app/payloadslice";
import { idGenerator } from "../../functions";
import AttachmentsCommentsTab from "@components/RequestBench/RequestPages/AttachmentsCommentsTab.jsx";
import {
  setMaterialRows,
  setRequestHeader,
  setTabValue,
} from "@app/requestDataSlice.jsx";
import { useLocation, useNavigate } from "react-router-dom";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import ArrowCircleLeftOutlined from "@mui/icons-material/ArrowCircleLeftOutlined";
import SummarizeOutlinedIcon from "@mui/icons-material/SummarizeOutlined";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import FileUploadOutlinedIcon from "@mui/icons-material/FileUploadOutlined";
import ReusableDialog from "@components/Common/ReusableDialog";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import {
  appendPrefixByJavaKey,
  clearLocalStorageItem,
  convertKeysName,
  getLocalStorage,
  setLocalStorage
} from "@helper/helper";
import ChangeLogHierarchy from "@components/Changelog/ChangeLogHierarchy";
import useGenericDtCall from "@hooks/useGenericDtCall";
import {
  DECISION_TABLE_NAME,
  DIALOUGE_BOX_MESSAGES,
  ENABLE_STATUSES,
  LOADING_MESSAGE,
  REGION,
  DIVERSION_CONTROL_FLAG,
  REQUEST_STATUS,
  REQUEST_TYPE,
  LOCAL_STORAGE_KEYS,
  API_CODE,
  MODULE_MAP,
  ARTIFACTNAMES,
} from "@constant/enum";
import { setDropDown } from "@app/dropDownDataSlice";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import useLogger from "@hooks/useLogger";
import { ERROR_MESSAGES } from "@constant/enum";
import { colors } from "@constant/colors";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { WarningOutlined } from "@mui/icons-material";
import { button_Outlined, button_Primary } from "@components/Common/commonStyles.jsx";
import { APP_END_POINTS } from "@constant/appEndPoints";
import ExcelOperationsCard from "@components/Common/ExcelOperationsCard";

import { END_POINTS } from "@constant/apiEndPoints";
import RequestHeaderCEG from "./RequestHeaderCEG";
import RequestDetailsCEG from "./RequestDetailsCEG";
import {
  clearHierarchyData,
  setDisplayDataHierarchy,
  setRequestHeaderPayloadDataPCG,
  setTreeData,
  updateToChangeLog,
} from "@app/hierarchyDataSlice";
import ErrorReportDialog from "@components/Common/ErrorReportDialog";
import useLang from "@hooks/useLang";
import PreviewPage from "@components/RequestBench/PreviewPage.jsx";

const CostElementGroupRequestTab = () => {
  const { toPDF, targetRef } = usePDF({ filename: "my-component.pdf" });
  const { t } = useLang();
  const { customError } = useLogger();
  const [isLoading, setIsLoading] = useState(false);
  const [downloadClicked, setDownloadClicked] = useState(false);
  const [ruleData, setRuleData] = useState([]);
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(false);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const [ccNumber, setCcNumber] = useState("");
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [attachmentsData, setAttachmentsData] = useState([]);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [successMsg, setSuccessMsg] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [addHardCodeData, setAddHardCodeData] = useState(false);
  const [isDialogVisible, setisDialogVisible] = useState(false);
  const dispatch = useDispatch();
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const payloadData = useSelector((state) => state.payload.payloadData);
  const [dialogOpen, setDialogOpen] = useState(false);

  const requestIdHeader = useSelector(
    (state) => state.request.requestHeader?.requestId
  );
  const requestType = useSelector(
    (state) => state.request.requestHeader.requestType
  );
  const taskData = useSelector((state) => state.userManagement?.taskData);
  const hierarchyData = useSelector((state) => state.hierarchyData);
  const { getDtCall, dtData } = useGenericDtCall();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const tabValue = useSelector((state) => state.request.tabValue);
  const requestHeaderSlice = useSelector(
    (state) => state.request.requestHeader
  );

  const steps = [
    "Request Header",
    "Hierarchy Tree",
    "Attachments & Remarks",
    "Preview",
  ];
  const [completed, setCompleted] = useState([false]);

  const handleTabChange = (index) => {
    dispatch(setTabValue(index));
  };

  const location = useLocation();
  const rowData = location.state;
  const urlSearchParams = new URLSearchParams(location.search.split("?")[1]);
  const requestId = urlSearchParams.get("RequestId");
  const queryParams = new URLSearchParams(location.search);
  const RequestId = queryParams.get("RequestId");
  const RequestType = queryParams.get("RequestType");
  const reqBench = queryParams.get("reqBench");
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  const openChangeLog = () => {
    setisChangeLogopen(true);
  };

  const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };

  const handleOk = () => {
    if (messageDialogSeverity === "success") {
      navigate(`/requestBench`);
    } else {
      handleMessageDialogClose();
    }
  };

  const handleDownload = () => {
    setDownloadClicked(true);
  };

  const handleUploadMaterial = (file) => {
    let url = "";
    if (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) {
      url = "getAllHierarchyNodeFromExcel";
    } else if (RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) {
      url = "getAllHierarchyNodeFromExcelForChange";
    }
    setLoaderMessage("Initiating Excel Upload");
    setBlurLoading(true);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    formData.append("requestId", requestId ? requestId.slice(4) : "");

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      } else {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }
    };
    const hError = (error) => {
      setBlurLoading(false);
      setLoaderMessage("");
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/${url}`,
      "postformdata",
      hSuccess,
      hError,
      formData
    );
  };

  const getDisplaydata = async (effectiveRequestId = null) => {
    return new Promise((resolve, reject) => {
      dispatch(setDataLoading(true));
      const idToUse = effectiveRequestId ||  RequestId;
      const savedTask = getLocalStorage(
        LOCAL_STORAGE_KEYS.CURRENT_TASK,
        true,
        {}
      );
      const effectiveRequestType =
        RequestType || taskData?.ATTRIBUTE_2 || savedTask?.ATTRIBUTE_2;
      const isChildPresent = rowData?.childRequestIds !== "Not Available";
      let payload = reqBench
        ? {
            parentId: !isChildPresent ? rowData?.requestId: "",
            massChangeId:
              isChildPresent &&
              (effectiveRequestType === REQUEST_TYPE.CHANGE ||
                effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD)
                ? idToUse
                : "",
            massCreationId:
              isChildPresent &&
              (effectiveRequestType === REQUEST_TYPE.CREATE ||
                effectiveRequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD)
                ? idToUse
                : "",
          }
        : {
            parentId: "",
            massChangeId:
              effectiveRequestType === REQUEST_TYPE.CHANGE ||
              effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
                ? idToUse
                : "",
            massCreationId:
              effectiveRequestType === REQUEST_TYPE.CREATE ||
              effectiveRequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD
                ? idToUse
                : "",
          };

      const hSuccess = async (data) => {
        try {
          dispatch(setDataLoading(false));
          const body = data?.body || {};

          const {
            ControllingArea,
            ParentNode,
            ParentDesc,
            RequestType,
            HierarchyTree,
            ChangeLogId,
            ErrorLogId,
            GeneralInformation,
            Torequestheaderdata,
            ...restBody
          } = body;

          const payloadData = {
            ReqCreatedBy: body?.Torequestheaderdata?.ReqCreatedBy,
            RequestStatus: body?.Torequestheaderdata?.RequestStatus,
            Region: body?.Torequestheaderdata?.Region,
            ReqCreatedOn: new Date().toISOString(),
            ReqUpdatedOn: new Date().toISOString(),
            RequestType: body?.Torequestheaderdata?.RequestType,
            RequestDesc: body?.Torequestheaderdata?.RequestDesc,
            RequestPriority: body?.Torequestheaderdata?.RequestPriority,
            LeadingCat: body?.Torequestheaderdata?.LeadingCat,
            RequestId: body?.Torequestheaderdata?.RequestId,
            TemplateName: body?.Torequestheaderdata?.TemplateName,
          };

          let requestorPayload = {};

          if (RequestType === REQUEST_TYPE?.CREATE || RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD) {
            requestorPayload = {
              "Controlling Area": ControllingArea,
              "Cost Element Group": ParentNode,
              "Cost Element Group Description": ParentDesc,
            };
          }

          if (RequestType === REQUEST_TYPE?.CHANGE || RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
            requestorPayload = {
              "Controlling Area": ControllingArea,
              "Cost Element Group": ParentNode,
            };
          }

          dispatch(setRequestorPayload(requestorPayload));
          dispatch(resetPayloadData(payloadData));
          const finalObject = {
            ControllingArea,
            ParentNode,
            ParentDesc,
            ChangeLogId,
            ErrorLogId,
            GeneralInformation,
            treeData: [HierarchyTree],
             requestHeaderData: {
                                  ...Torequestheaderdata,
                                  childRequestId: body?.ToChildHeaderdata?.RequestId,
                                },
            changeLog: [],
            ...restBody,
          };
          dispatch(setDisplayDataHierarchy({ data: finalObject }));

          resolve();
        } catch (error) {
          customError(ERROR_MESSAGES.ERROR_GET_DISPLAY_DATA);
          reject(error);
        }
      };

      const hError = (error) => {
        customError(ERROR_MESSAGES.ERROR_FETCHING_DATA);
        reject(error);
      };
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/displayHierarchyTreeNodeStructureFromDb`,
        "post",
        hSuccess,
        hError,
        payload
      );
    });
  };

  useEffect(() => {
    const loadData = async () => {
      if (RequestId) {
  
        await getDisplaydata(RequestId);
        if (
          ((RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD &&
            !rowData?.parentNode?.length) ||
            (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD &&
              !rowData?.parentNode?.length)) &&
          (rowData?.reqStatus === REQUEST_STATUS.DRAFT ||
            rowData?.reqStatus === REQUEST_STATUS.UPLOAD_FAILED)
        ) {
          dispatch(setTabValue(0));
          setIsSecondTabEnabled(false);
          setIsAttachmentTabEnabled(false);
        } else if (
          (RequestType === REQUEST_TYPE.CREATE ||
            RequestType === REQUEST_TYPE.CHANGE) &&
          !rowData?.parentNode?.length &&
          reqBench
        ) {
          dispatch(setTabValue(0));
          setIsSecondTabEnabled(false);
          setIsAttachmentTabEnabled(true);
        } else {
          dispatch(setTabValue(1));
          setIsSecondTabEnabled(true);
          setIsAttachmentTabEnabled(true);
        }

        setAddHardCodeData(true);
      } else {
        dispatch(setTabValue(0));
      }
    };

    loadData();
    return () => {
      dispatch(setTreeData([]));
      dispatch(setRequestHeaderPayloadDataPCG({}));
      dispatch(clearChangeLogData());
      dispatch(clearTemplateArray());
      dispatch(resetPayloadData({ data: {} }));
      dispatch(setMatlNoData([]));
      dispatch(setPlantData([]));
      dispatch(setRequestorPayload({}));
      dispatch(clearDynamicKeyValue());
      dispatch(updateSelectedRows([]));
      dispatch(setChangeFieldRows([]));
      dispatch(setChangeFieldRowsDisplay({}));
      dispatch(clearHierarchyData());
      clearLocalStorageItem(LOCAL_STORAGE_KEYS.CURRENT_TASK);
      clearLocalStorageItem(LOCAL_STORAGE_KEYS.ROLE);
    };
  }, [requestId, dispatch]);

  useEffect(() => {
    const fetchChangeLogData = async () => {
      if (RequestId) {
        try {
          const result = await changeLogFetch(RequestId, RequestType);
          dispatch(updateToChangeLog(result || []));
        } catch (error) {
          customError("Error fetching changelog data:", error);
        }
      }
    };

    fetchChangeLogData();
  }, [RequestId, dispatch]);

  const changeLogFetch = (requestId) => {
    const url = `/${destination_GeneralLedger_Mass}/node/getTreeModificationHistory?requestId=${requestId?.slice(
      4
    )}`;
    return new Promise((resolve, reject) => {
      const hSuccess = (data) => {
        if (
          data?.statusCode === API_CODE.STATUS_200 &&
          data?.body?.length > 0
        ) {
          resolve(result);
        } else {
          resolve([]);
        }
      };

      const hError = (error) => {
        customError(error);
        reject(error);
      };

      doAjax(url, "get", hSuccess, hError);
    });
  };



  useEffect(() => {
    getAttachmentsIDM();
    dispatch(setMaterialRows([]));
    dispatch(setDropDown({ keyName: "Region", data: REGION }));
    return () => {
      dispatch(setRequestHeader({}));
    };
  }, []);

  useEffect(() => {
    if (isSecondTabEnabled) {
      setCompleted([true]);
    }
  }, [isSecondTabEnabled]);

  useEffect(() => {
    setCcNumber(idGenerator("CEG"));
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE,MODULE_MAP.CEG)
  }, []);

  const getAttachmentsIDM = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "Material",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true);
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData = data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE;
        let templateData = [];
        responseData?.map((element, index) => {
          var tempRow = {
            id: index,
          };
          templateData.push(tempRow);
        });
        setRuleData(templateData);
        const attachmentNames =
          data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE || [];
        setAttachmentsData(attachmentNames);
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  const handleExportTemplateExcel = () => {
    const url = RequestId?.includes("FCA")
      ? END_POINTS.EXCEL.DOWNLOAD_EXCEL_FINANCE
      : END_POINTS.EXCEL.DOWNLOAD_EXCEL_MAT;
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data are exported into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    let financePayload = {
      massSchedulingId: payloadData?.RequestId,
    };
    let payload = {
      dtName:
        payloadData?.RequestType === REQUEST_TYPE?.CHANGE ||
        payloadData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
          ? "MDG_MAT_CHANGE_TEMPLATE"
          : "MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",
      version:
        payloadData?.RequestType === REQUEST_TYPE?.CHANGE ||
        payloadData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
          ? "v4"
          : "v1",
      requestId: payloadData?.RequestId || requestIdHeader || "",
      scenario:
        payloadData?.RequestType === REQUEST_TYPE?.CHANGE ||
        payloadData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
          ? "Change with Upload"
          : "Create with Upload",
      templateName: payloadData?.TemplateName || "",
      region: payloadData?.Region || "",
      matlType: "ALL",
    };

    const hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute(
        "download",
        `${
          payloadData?.TemplateName
            ? payloadData?.TemplateName
            : RequestId?.includes("FCA")
            ? REQUEST_TYPE.FINANCE_COSTING
            : "Mass_Create"
        }_Data Export.xlsx`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");

      setSuccessMsg(true);
      setMessageDialogMessage(
        `${
          payloadData?.TemplateName
            ? payloadData?.TemplateName
            : RequestId?.includes("FCA")
            ? REQUEST_TYPE.FINANCE_COSTING
            : "Mass_Create"
        }_Data Export.xlsx has been exported successfully.`
      );
      setAlertType("success");
      handleSnackBarOpen();
    };
    const hError = () => {};
    doAjax(
      `/${destination_MaterialMgmt}${url}`,
      "postandgetblob",
      hSuccess,
      hError,
      RequestId?.includes("FCA") ? financePayload : payload
    );
  };

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const handleYes = () => {
    if (requestId && !reqBench) {
      navigate(APP_END_POINTS?.MY_TASK);
    } else if (reqBench) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    } else if (!requestId && !reqBench) {
      navigate(APP_END_POINTS?.MASTER_DATA_CEG);
    }
  };

  const handleCancel = () => {
    setisDialogVisible(false);
  };
  return (
    <>
      {loading && (
        <ReusableBackDrop
          blurLoading={blurLoading}
          loaderMessage={loaderMessage}
        />
      )}
      <Box sx={{ padding: 2 }}>
        <Grid
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {requestIdHeader || requestId ? (
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                textAlign: "left",
                display: "flex",
                alignItems: "center",
                gap: 1,
              }}
            >
              <PermIdentityOutlinedIcon sx={{ fontSize: "1.5rem" }} />
              {t("Request Header ID")}:{" "}
              <span>
                {requestIdHeader
                  ? requestHeaderSlice?.requestPrefix +
                    "" +
                    requestHeaderSlice?.requestId
                  : requestId}
              </span>
            </Typography>
          ) : (
            <div style={{ flex: 1 }} />
          )}

          {tabValue === 1 && (
            <Box
              sx={{ display: "flex", justifyContent: "flex-end", gap: "1rem" }}
            >
              <Button
                variant="outlined"
                size="small"
                title="Download Error Report"
                disabled={!RequestId}
                onClick={() => setDialogOpen(true)}
                color="primary"
              >
                <SummarizeOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
              <Button
                variant="outlined"
                disabled={false}
                size="small"
                onClick={openChangeLog}
                title="Change Log"
              >
                <TrackChangesTwoToneIcon sx={{ padding: "2px" }} />
              </Button>
              <Button
                variant="outlined"
                disabled={!RequestId}
                size="small"
                onClick={handleExportTemplateExcel}
                title="Export Excel"
              >
                <FileUploadOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
            </Box>
          )}

          {isChangeLogopen && (
            <ChangeLogHierarchy
              open={true}
              closeModal={handleClosemodalData}
              requestId={requestIdHeader || requestId.slice(4)}
              requestType={payloadData?.RequestType}
            />
          )}
       
          {tabValue === 3 && (
            <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<PictureAsPdfIcon />}
                onClick={toPDF}
              >
                {t("Export Preview")}
              </Button>
            </Box>
          )}
        </Grid>


        <IconButton
          onClick={() => {
            if (
              reqBench &&
              !ENABLE_STATUSES?.includes(payloadData?.RequestStatus)
            ) {
              navigate(APP_END_POINTS?.REQUEST_BENCH);
              return;
            }
            setisDialogVisible(true);
          }}
          color="primary"
          aria-label="upload picture"
          component="label"
          sx={{ left: "-10px" }}
          title="Back"
        >
          <ArrowCircleLeftOutlined
            sx={{ fontSize: "25px", color: "#000000" }}
          />
        </IconButton>

        <Stepper
          nonLinear
          activeStep={tabValue}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            margin: "25px 14%",
            marginTop: "-35px",
          }}
        >
          {steps.map((label, index) => (
            <Step key={label} completed={completed[index]}>
              <StepButton
                color="error"
                disabled={
                  (index === 1 && !isSecondTabEnabled) ||
                  (index === 2 && !isAttachmentTabEnabled) ||
                  (index === 3 &&
                    !isSecondTabEnabled &&
                    !isAttachmentTabEnabled)
                }
                onClick={() => handleTabChange(index)}
                sx={{ fontSize: "50px", fontWeight: "bold" }}
              >
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>
                  {label}
                </span>
              </StepButton>
            </Step>
          ))}
        </Stepper>

        <ReusableDialog
          dialogState={openMessageDialog}
          openReusableDialog={handleMessageDialogClickOpen}
          closeReusableDialog={handleMessageDialogClose}
          dialogTitle={messageDialogTitle}
          dialogMessage={messageDialogMessage}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          handleOk={handleOk}
          // handleExtraButton={handleMessageDialogNavigate}
          dialogSeverity={messageDialogSeverity}
        />
        <ErrorReportDialog
          dialogState={dialogOpen}
          closeReusableDialog={() => setDialogOpen(false)}
          module={MODULE_MAP?.CEG}
          isHierarchyCheck={true}
        />

        <ReusableBackDrop
          blurLoading={blurLoading}
          loaderMessage={loaderMessage}
        />

        {tabValue === 0 && (
          <>
            <RequestHeaderCEG
              setIsSecondTabEnabled={setIsSecondTabEnabled}
              setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
              requestStatus={
                rowData?.reqStatus
                  ? rowData?.reqStatus
                  : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME
              }
              downloadClicked={downloadClicked}
              setDownloadClicked={setDownloadClicked}
            />
            {(RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD ||
              RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) &&
              ((rowData?.reqStatus == REQUEST_STATUS.DRAFT &&
                !rowData?.material?.length) ||
                rowData?.reqStatus == REQUEST_STATUS.UPLOAD_FAILED) && (
                <ExcelOperationsCard
                  handleDownload={handleDownload}
                  setEnableDocumentUpload={setEnableDocumentUpload}
                  enableDocumentUpload={enableDocumentUpload}
                  handleUploadMaterial={handleUploadMaterial}
                />
              )}
            {(payloadData?.RequestType === REQUEST_TYPE?.CHANGE ||
              payloadData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) &&
              !RequestId &&
              payloadData?.DirectAllowed !== "X" &&
              payloadData?.DirectAllowed !== undefined && (
                <Typography
                  sx={{
                    fontSize: "13px",
                    fontWeight: "500",
                    color: colors?.error?.dark,
                    marginTop: "1rem",
                    marginLeft: "0.5rem",
                  }}
                >
                  <Box component="span" sx={{ fontWeight: "bold" }}>
                    {t("Note")}:
                  </Box>{" "}
                  {"You are not authorized to Tcode"}{" "}
                  <Box component="span" sx={{ fontWeight: "bold" }}>
                    {" "}
                    MM02.
                  </Box>
                </Typography>
              )}
          </>
        )}

        {
          tabValue === 1 && (
          
            <RequestDetailsCEG
              setIsAttachmentTabEnabled={true}
              setCompleted={setCompleted}
              downloadClicked={downloadClicked}
              setDownloadClicked={setDownloadClicked}
            />
          )
          // ))
        }
        {tabValue === 2 && (
          <AttachmentsCommentsTab
            requestStatus={
              rowData?.reqStatus
                ? rowData?.reqStatus
                : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME
            }
            attachmentsData={attachmentsData}
            requestIdHeader={
              requestIdHeader
                ? appendPrefixByJavaKey(requestType, requestIdHeader)
                : requestId
            }
            pcNumber={ccNumber}
             module = {MODULE_MAP?.CEG} 
              artifactName={ARTIFACTNAMES.CEG}
            
          />
        )}
        {tabValue === 3 && (
          <Box
            ref={targetRef}
            sx={{
              width: "100%",
              overflow: "auto", // Ensure all content fits inside
            }}
          >
            <PreviewPage requestStatus={rowData?.reqStatus ? rowData?.reqStatus : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME} module = {MODULE_MAP?.CEG} payloadData = {hierarchyData} payloadForDownloadExcel={""}/>
          </Box>
        )}
      </Box>
      {successMsg && (
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={messageDialogMessage}
          alertType={alertType}
          handleSnackBarClose={handleSnackBarClose}
        />
      )}
      {isDialogVisible && (
        <CustomDialog
          isOpen={isDialogVisible}
          titleIcon={
            <WarningOutlined
              size="small"
              sx={{ color: colors?.secondary?.amber, fontSize: "20px" }}
            />
          }
          Title={"Warning"}
          handleClose={handleCancel}
        >
          <DialogContent sx={{ mt: 2 }}>
            {DIALOUGE_BOX_MESSAGES.LEAVE_PAGE_MESSAGE}
          </DialogContent>
          <DialogActions>
            <Button
              variant="outlined"
              size="small"
              sx={{ ...button_Outlined }}
              onClick={handleCancel}
            >
              {t("No")}
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary }}
              onClick={handleYes}
            >
              {t("Yes")}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </>
  );
};

export default CostElementGroupRequestTab;
