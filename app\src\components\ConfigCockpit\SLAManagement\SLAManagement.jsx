import React, { useEffect, useState } from "react";
import EditIcon from "@mui/icons-material/Edit";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  BottomNavigation,
  Box,
  Button,
  Grid,
  IconButton,
  Paper,
  Tab,
  Tabs,
  Tooltip,
  Typography,
} from "@mui/material";
import { Stack } from "@mui/system";
import {
  iconButton_SpacingSmall,
  outermostContainer,
  outermostContainer_Information,
  outerContainer_Information,
  container_table,
  button_Primary,
} from "../../common/commonStyles";
import ReusableTable from "../../common/ReusableTable";
import CreateSLA from "./CreateSLA";
import {
  destination_Admin,
  destination_Po,
  destination_SLA_Mgmt,
} from "../../../destinationVariables";
import { doAjax } from "../../common/fetchService";
import moment from 'moment/moment';
import ReusableDialog from "../../common/ReusableDialog";
import ReusableSnackBar from "../../common/ReusableSnackBar";
import ViewSLA from "./ViewSLA.jsx";
import { pureFinalPropsSelectorFactory } from "react-redux/es/connect/selectorFactory";
import UpdateSLA from "./UpdateSLA";
import ReusableIcon from "../../common/ReusableIcon";
import AccessAlarmsTwoToneIcon from "@mui/icons-material/AccessAlarmsTwoTone";
import NotificationsIcon from "@mui/icons-material/Notifications";
import { useSelector } from "react-redux";
import { ViewDetailsIcon } from "../../Common/icons.jsx";
const SLAManagement = () => {
  const [SLAData, setSLAData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [reusableDialog_Ref, setreusableDialog_Ref] = useState("");
  const [selectedSLAID, setselectedSLAID] = useState("");
  const [viewSLAModal, setViewSLAModal] = useState(false);
  const [pageSize, setPageSize] = useState(10);
  const [createSLAModal, setCreateSLAModal] = useState(false);
  const [updateSLA, setUpdateSLA] = useState(false);
  const [suppliersData, setsuppliersData] = useState({});
  const [companysData, setcompanysData] = useState({});
  const [purchasingGroupsData, setpurchasingGroupsData] = useState({})

  let masterData = useSelector((state) => state.masterData);

  const appSettings = useSelector((state) => state.appSettings)
  const handleClose_SLA = (ref) => {
    fetchSLA();

    switch (ref) {
      case "CREATE":
        setCreateSLAModal(false);
        break;
      case "UPDATE":
        setUpdateSLA(false);
        break;
      case "VIEW":
        setViewSLAModal(false);

        break;
    }
  };

  const [page, setPage] = useState(0);

  const handlePageSizeChange = (event) => {
    
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
    
  };
  const handlePageChange = (event, newPage) => {
    
    setPage(isNaN(newPage) ? 0 : newPage);
    
  };
  const handleOpen_SLACrud = (ref) => {
    switch (ref) {
      case "CREATE":
        setCreateSLAModal(true);
        break;
      case "UPDATE":
        setUpdateSLA(true);
        break;
      case "VIEW":
        setViewSLAModal(true);
        break;
    }
  };

  let handleConfirm = (ref, itemData) => {
    //opens prompt dialog
    //onclick of ok button  #deleteSLA is triggered by #functions_ReusableDialogBox.getHandleOkFunction
    switch (ref) {
      case "DELETE":
        setreusableDialog_Ref("CONFIRMDELETE");
        setwarning_Notification((prev) => ({
          ...prev,
          currentNotification: `Confirm delete SLA ${itemData?.processName}?`,
          success: false,
          open: true,
          title: "Confirm Delete",
          severity: "warning",
        }));
    }
  };
  let fetchSLA = () => {
    setIsLoading(true);
    let hSuccess = (res) => {
      setSLAData(res.data);
      setIsLoading(false);
    };
    let hError = () => { };
    doAjax(`/${destination_SLA_Mgmt}/sla/getAll`, "get", hSuccess, hError);
  };
  // let fetchSingleSLA = (SLAId)=>{
  //   let hSuccess =(res)=>{
  //     setselectedSLAData(res.data)
  //   }
  //   let hError = ()=>{
  //   }
  //   doAjax(`/${destination_Admin}/sla/getSingleSla/${SLAId}`,'get',hSuccess,hError)
  // }

  let deleteSLA = (id) => {
    let hSuccess = (res) => {
      setSuccess_Notification((prev) => ({
        ...prev,
        currentNotification: `SLA ${selectedSLAID} Deleted successfully.`,
        success: true,
        open: true,
        title: "Success",
        severity: "success",
      }));
      fetchSLA();
    };
    let hError = () => {
      setreusableDialog_Ref("ERROR");
      setwarning_Notification((prev) => ({
        ...prev,
        currentNotification: `Failed to Delete SLA ${selectedSLAID}`,
        success: false,
        open: true,
        title: "Error",
        severity: "danger",
      }));
    };
    doAjax(
      `/${destination_SLA_Mgmt}/sla/delete/${selectedSLAID}`,
      "delete",
      hSuccess,
      hError
    );
  };

  let columns = [
    {
      field: "ruleId",
      headerName: "Process Name",
      editable: false,
      hide: true,
    },
    {
      field: "processName",
      headerName: "Process Name",
      editable: false,
      width: 200,
    },
    {
      field: "serviceName",
      headerName: "Service Name",
      editable: false,
      width: 200,
      hide: false,
    },
    {
      field: "slaType",
      headerName: "SLA Type",
      editable: false,
      width: 200,
      hide: false,
      renderCell: (params) => {
        let iconObj = {
          notification: <NotificationsIcon color="primary" />,
          escalation: <AccessAlarmsTwoToneIcon color="danger" />,
        };
        return (
          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            {iconObj[params.row.slaType?.toLowerCase()]}
            <Typography
              variant='body2'
              sx={{
                marginLeft: "6px",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {params.row.slaType}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: "masterData",
      headerName: "Master Data",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        let masterDataTitle = () => {
          let value = ''
          switch (params.row.masterDataCategory?.toLowerCase()) {
            case "company":
              value = companysData[params.row.masterData];
              break;
            case 'supplier':
              value = suppliersData[params.row.masterData];
              break;

            case 'purchasing group':
              value = purchasingGroupsData[params.row.masterData];
              break;

          }
          console.log(value)
          return value
        }
        // console.log(masterDataTitle())
        return (
          <Stack>
            <Typography variant="body2">
              {masterDataTitle()}
            </Typography>
            <Typography variant="body2" 
            // sx={{ color: (theme) => theme.customPalette.textTertiary }}
            >
              {params.row.masterData}
            </Typography>
          </Stack>
        );
      },
    },
    {
      field: "sla",
      headerName: "SLA",
      editable: false,
      flex: 1,
      renderCell: (param) => {
        return (
          <Typography variant='body2'>{`${param.row.sla} ${param.row.slaFormat}`}</Typography>
        );
      },
    },
    {
      field: "endDate",
      headerName: "Validity End Date",
      editable: false,
      flex: 1,
      renderCell: (param) => {
        return moment(param.row.endDate).format(appSettings.dateFormat);
      },
    },
    {
      field: "actionItem",
      headerName: "Action",
      headerAlign: "center",
      align: "center",
      editable: false,
      width: 150,
      clickable: false,
      renderCell: (params) => {
        return (
          <>
            <Tooltip title="View Details">
              <IconButton
                sx={iconButton_SpacingSmall}
                onClick={() => {
                  handleOpen_SLACrud("VIEW");
                  setselectedSLAID(params.row.ruleId);
                }}
              >
                <ViewDetailsIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Delete">
              <IconButton
                sx={iconButton_SpacingSmall}
                onClick={() => {
                  setselectedSLAID(params.row.ruleId);
                  handleConfirm("DELETE", params.row);
                }}
              >
                <DeleteOutlinedIcon color="danger" />

                {/* hidebackdrop=true */}
                {/* <TrackChangesIcon /> */}
              </IconButton>
            </Tooltip>
            {
              <Tooltip title={"Edit"}>
                <IconButton
                  sx={iconButton_SpacingSmall}
                  onClick={() => {
                    setselectedSLAID(params.row.ruleId);
                    handleOpen_SLACrud("UPDATE");
                  }}
                >
                  <ReusableIcon iconName={"Edit"} />
                </IconButton>
              </Tooltip>
            }
          </>
        );
      },
    },
  ];
  //<-- Functions and variables for reusable dialog box -->
  const [warning_Notification, setwarning_Notification] = useState({
    currentNotification: "",
    success: true,
    open: false,
    title: "",
    severity: "",
  });
  const [Success_Notification, setSuccess_Notification] = useState({
    currentNotification: "",
    success: true,
    open: false,
    title: "",
    severity: "",
  });
  const functions_ReusableDialogBox = {
    MessageDialogCancel: () => {
      setwarning_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
    },
    MessageDialogClickOpen: () => {
      setwarning_Notification((prev) => ({ ...prev, open: true }));
      // setOpenMessageDialog(true);
    },

    MessageDialogClose: () => {
      setwarning_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));

      setSuccess_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
    },
    messageDialogCloseAndRedirect: () => {
      // seteditState(true);
      setwarning_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
      setSuccess_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
      // navigate('/manageAccount')
    },
    getHandleOkFunction: () => {
      switch (reusableDialog_Ref) {
        case "CONFIRMDELETE":
          setwarning_Notification((prev) => ({
            open: false,
            currentNotification: "",
            success: "",
            title: "",
            severity: "",
          }));
          deleteSLA();
          break;
        case "ERROR":
          functions_ReusableDialogBox.MessageDialogClose();
          break;
        case "CONFIRMCANCEL":
          functions_ReusableDialogBox.MessageDialogClose();
        // seteditState(true)
        default:
          functions_ReusableDialogBox.MessageDialogClose();
      }
    },
    viewOkButton: () => {
      // console.log(reusableDialog_Ref,'ref')
      switch (reusableDialog_Ref) {
        case "CONFIRMDELETE":
          return false;
          break;
        case "ERROR":
          return false;
          break;
        case "CONFIRMCANCEL":
          return false;
        default:
          return false;
      }
    },
    viewCancelButton: () => {
      switch (reusableDialog_Ref) {
        case "CONFIRMDELETE":
          return true;
          break;
        case "ERROR":
          return false;
          break;
        case "CONFIRMCANCEL":
          return true;
        default:
          return false;
      }
    },
    getOkButtonText: () => {
      switch (reusableDialog_Ref) {
        case "CONFIRMDELETE":
          return "Delete";
          break;
        case "ERROR":
          return "OK";
        case "CONFIRMCANCEL":
          return "OK";
        default:
          return "";
      }
    },
    getHandleCancleFunction: () => {
      switch (reusableDialog_Ref) {
        case "CONFIRMDELETE":
          return functions_ReusableDialogBox.MessageDialogClose();
        case "ERROR":
          return () => { };
        case "CONFIRMCANCEL":
          return functions_ReusableDialogBox.MessageDialogClose();
        default:
          return () => { };
      }
    },
  };
  let controller_UpdateSLA = (status, data) => {
    switch (status) {
      case "SUCCESS":
        setSuccess_Notification((prev) => ({
          ...prev,
          currentNotification: `SLA ${data} updated successfully.`,
          success: true,
          open: true,
          title: "Success",
          severity: "success",
        }));
        break;
    }
  };
  let controller_CreateSLA = (status, data) => {
    switch (status) {
      case "SUCCESS":
        setSuccess_Notification((prev) => ({
          ...prev,
          currentNotification: `SLA ${data} created successfully.`,
          success: true,
          open: true,
          title: "Success",
          severity: "success",
        }));
        break;
    }
  };

  // const fetchCompanySupplierData = () => {
  //   doAjax(
  //     `/${destination_Po}/Odata/populateCompanyCodeDetails`,
  //     "get",
  //     (res) => {
  //       setcompanysData(res.data);
  //     }
  //   );
  //   doAjax(`/${destination_Po}/Odata/getAllSuppliers`, "get", (res) => {
  //     setsuppliersData(res.data);
  //   });

  //   doAjax(`/${destination_Po}/Odata/purchasingGroup`, "get", (res) => {
  //     setpurchasingGroupsData(res.data);

  //   }, (err) => { });
  // };

  useEffect(() => {
    fetchSLA();
    // fetchCompanySupplierData();
    // setpurchasingGroupsData(masterData?.purchasingGroups);  

  }, []);

  return (
    <div id="printScreen" style={outermostContainer}>

      {/* REUSABLE DIALOG */}
      <ReusableDialog
        dialogState={warning_Notification.open}
        openReusableDialog={
          functions_ReusableDialogBox.MessageDialogClickOpen
        }
        closeReusableDialog={functions_ReusableDialogBox.MessageDialogCancel}
        dialogTitle={warning_Notification.title}
        dialogMessage={warning_Notification.currentNotification}
        handleOk={functions_ReusableDialogBox.getHandleOkFunction}
        dialogOkText={functions_ReusableDialogBox.getOkButtonText()}
        showOkButton={functions_ReusableDialogBox.viewOkButton()}
        showCancelButton={functions_ReusableDialogBox.viewCancelButton()}
        dialogSeverity={warning_Notification.severity}
        handleDialogReject={
          functions_ReusableDialogBox.getHandleCancleFunction
        }
      />
      <ReusableSnackBar
        openSnackBar={Success_Notification.open}
        alertMsg={Success_Notification.currentNotification}
        handleSnackBarClose={
          functions_ReusableDialogBox.messageDialogCloseAndRedirect
        }
      />

      <Paper
        sx={{ position: "fixed", bottom: 0, left: 0, right: 0, zIndex: 1 }}
        elevation={2}
      >
        <BottomNavigation
          showLabels
          className="container_BottomNav"
          sx={{
            display: "flex",
            justifyContent: "flex-end",

          }}
        >
          <Button
            variant="contained"
            sx={{ marginLeft: "auto" }}
            onClick={() => handleOpen_SLACrud("CREATE")}
          >
            Create SLA
          </Button>
        </BottomNavigation>
      </Paper>

      {/* CRUD Modal */}
      {createSLAModal && (
        <CreateSLA
          open={createSLAModal}
          controller={controller_CreateSLA}
          handleClose={handleClose_SLA}
        />
      )}
      {updateSLA && (
        <UpdateSLA
          open={updateSLA}
          handleClose={handleClose_SLA}
          id={selectedSLAID}
          controller={controller_UpdateSLA}
        />
      )}
      {viewSLAModal && (
        <ViewSLA
          open={viewSLAModal}
          handleClose={handleClose_SLA}
          id={selectedSLAID}
        />
      )}

      {/* header and search bar */}
      <Stack spacing={1}>
        <Grid container sx={outermostContainer_Information}>
          <Grid item md={5} sx={outerContainer_Information}>
            <Typography variant="h3">
              <strong>SLA Configurations</strong>
            </Typography>
            <Typography variant="body2">
              This view displays the existing SLAs and the configurable SLAs.
            </Typography>
          </Grid>
        </Grid>

        {/* reusable table */}
        {/* INFORMATION */}

        <Stack>
          <Grid>
            {/* Table */}
            <Grid container columns={12} sx={container_table}>
              <Grid item md={12} sx={{ position: "relative" }}>
                <ReusableTable
                  tempheight='calc(100vh - 260px)'
                  width="100%"
                  stopPropagation_Column={["actionItem"]}
                  pageSize={pageSize}
                  page={page}
                  onPageSizeChange={handlePageSizeChange}
                  rowCount={SLAData.length ?? 0}
                  rows={SLAData}
                  columns={columns}
                  getRowIdValue={"ruleId"}
                  hideFooter={true}
                  checkboxSelection={false}
                  onPageChange={handlePageChange}
                  disableSelectionOnClick={true}
                  isLoading={isLoading}
                  showCustomNavigation={true}
                />
              </Grid>
            </Grid>
          </Grid>
        </Stack>
      </Stack>
    </div>
  );
};

export default SLAManagement;
