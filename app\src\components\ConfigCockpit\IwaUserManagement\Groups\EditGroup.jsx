import { useLocation, useNavigate, useParams } from "react-router-dom";
import { EditGroup } from "@cw/editgroup";
import { useSnackbar } from "@hooks/useSnackbar";
import { APP_END_POINTS } from "@constant/appEndPoints";

const EditGroupContainer = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const groupId = searchParams.get("groupId");
  const navigate = useNavigate();
  const { showSnackbar } = useSnackbar();

  const onEditGroupActionClick = (action, groupId, response) => {
    if (action === "groupSummary") {
      navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.GROUPS_SUMMARY);
    }
    if (response && response.data?.status !== "Error") {
      showSnackbar(response.message, "info");
    }
  };

  const dateTimeConfig = {
    dateFormat: "DD-MMM-YYYY",
    timeFormat: "24hr",
  };

  return (
    <>
      <EditGroup groupId={groupId} onEditGroupActionClick={onEditGroupActionClick} app="IWA" dateTimeConfig={dateTimeConfig} />
    </>
  );
};

export default EditGroupContainer;
