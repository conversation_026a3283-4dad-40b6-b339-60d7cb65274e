import { Grid, Stack, TextField, Typography } from "@mui/material";
import React from "react";
import { container_Padding } from "../../Common/commonStyles";

const BasicDataTextsTab = () => {
  return (
    <div>
      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          mt: 0.25,
          ...container_Padding,
          // ...container_columnGap,
        }}
      >
        <Grid container display="block">
          <Grid item sx={{ paddingTop: "2px !important", mb: 1 }}>
            <Stack flexDirection="row" alignItems="center">
              <div style={{ width: "5%" }}>
                <Typography variant="body2" color="#777">
                  Material
                </Typography>
              </div>
              <Typography variant="body2" fontWeight="bold">
                :
              </Typography>
              <TextField size="small" sx={{ ml: 2 }} />
            </Stack>
          </Grid>
          <Grid item sx={{ paddingTop: "2px !important", mb: 1 }}>
            <Stack flexDirection="row" alignItems="center">
              <div style={{ width: "5%" }}>
                <Typography variant="body2" color="#777">
                  Descr
                </Typography>
              </div>
              <Typography variant="body2" fontWeight="bold">
                :
              </Typography>
              <TextField size="small" sx={{ ml: 2 }} />
            </Stack>
          </Grid>
          
        </Grid>
      </Grid>
    </div>
  );
};

export default BasicDataTextsTab;
