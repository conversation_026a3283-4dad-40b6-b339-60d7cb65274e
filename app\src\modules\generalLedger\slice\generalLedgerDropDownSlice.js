import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  dropDown: {},
  isOdataApiCalled:false,
};

export const generalLedgerDropDownSlice = createSlice({
  name: "generalLedgerAllDropDown",
  initialState,
  reducers: {
    setDropDown: (state, action) => {
      state.dropDown[action.payload.keyName] = action.payload.data;
    },
    setDependentDropdown : (state,action) =>{
          if(!state.dropDown[action.payload.keyName]) state.dropDown[action.payload.keyName] = {};
          state.dropDown[action.payload.keyName][action.payload.keyName2] = action.payload.data;
          return state
    },
    setOdataApiCall:(state,action) => {
      state.isOdataApiCalled = action.payload;
    }
  },
});

export const { setDropDown, setDependentDropdown, setOdataApiCall } = generalLedgerDropDownSlice.actions;

export default generalLedgerDropDownSlice.reducer;
