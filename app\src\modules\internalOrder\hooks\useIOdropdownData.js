import useIOFetchDropdownAndDispatch from "./useIOFetchDropdownAndDispatch";
import { destination_InternalOrder } from "../../../destinationVariables";

const useIOdropdownData = () => {
  const { fetchDataAndDispatch } = useIOFetchDropdownAndDispatch();

  const fetchAllDropdownIOData = () => {
    const apiCalls = [
      { url: `/${destination_InternalOrder}/api/v1/lookup/scales`, keyName: "Scale" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/work-end-dates`, keyName: "DateWorkEnds" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/work-start-dates`, keyName: "DateWorkBegins" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/gl-accounts`, keyName: "GlAccount" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/cost-centers`, keyName: "Costcenter" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/overhead-keys`, keyName: "OverheadKey" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/costing-sheets`, keyName: "CstgSheet" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/currencies`, keyName: "Currency" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/locations`, keyName: "LocationPlant" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/sales-orders`, keyName: "SalesOrd" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/requesting-orders`, keyName: "RequestOrder" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/requesting-company-codes`, keyName: "RequCompCode" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/requesting-cost-centers`, keyName: "RequestCctr" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/wbs-elements`, keyName: "WbsElement" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/responsible-cost-centers`, keyName: "Respcctr" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/profit-centers`, keyName: "ProfitCtr" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/object-classes`, keyName: "Objectclass" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/functional-areas`, keyName: "FuncArea" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/plants`, keyName: "Plant" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/business-areas`, keyName: "BusArea" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/responsible-users`, keyName: "ResponsibleUser" },//json 

      // Existing entries
      { url: `/${destination_InternalOrder}/api/v1/lookup/controlling-areas`, keyName: "controllingArea" },
      { url: `/${destination_InternalOrder}/data/getOrderType`, keyName: "orderType" },
      { url: `/${destination_InternalOrder}/api/v1/lookup/company-codes`, keyName: "compCode" },
    ];

    apiCalls.forEach(({ url, keyName }) => {
      fetchDataAndDispatch(url, keyName);
    });
  };

  return { fetchAllDropdownIOData };
};

export default useIOdropdownData;
