import React, { useEffect, useRef, useState } from "react";
import { Box, IconButton, Tooltip, Typography, Slider } from "@mui/material";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import PauseIcon from "@mui/icons-material/Pause";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import VolumeUpIcon from "@mui/icons-material/VolumeUp";

const VideoPlayer = ({ videoUrlProp }) => {
  const videoRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [playing, setPlaying] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(1);

  useEffect(() => {
    if (videoUrlProp) {
      setIsLoading(true);
      setError(null);
      if (videoRef.current) {
        videoRef.current.src = videoUrlProp;
        videoRef.current.load();
      }
    }
  }, [videoUrlProp]);

  const handleLoadedMetadata = () => {
    setDuration(videoRef.current.duration);
    setIsLoading(false);
  };

  const handleTimeUpdate = () => {
    setCurrentTime(videoRef.current.currentTime);
  };

  const handlePlayPause = () => {
    if (playing) {
      videoRef.current.pause();
      setPlaying(false);
    } else {
      videoRef.current.play();
      setPlaying(true);
    }
  };

  const handleSliderChange = (event, newValue) => {
    videoRef.current.currentTime = newValue;
    setCurrentTime(newValue);
  };

  const handleVolumeChange = (event, newValue) => {
    videoRef.current.volume = newValue;
    setVolume(newValue);
  };

  const handleFullscreen = () => {
    if (videoRef.current.requestFullscreen) {
      videoRef.current.requestFullscreen();
    } else if (videoRef.current.webkitRequestFullscreen) {
      videoRef.current.webkitRequestFullscreen();
    } else if (videoRef.current.msRequestFullscreen) {
      videoRef.current.msRequestFullscreen();
    }
  };

  const formatTime = (timeInSeconds) => {
    const minutes = Math.floor(timeInSeconds / 60).toString().padStart(2, "0");
    const seconds = Math.floor(timeInSeconds % 60).toString().padStart(2, "0");
    return `${minutes}:${seconds}`;
  };

  return (
    <Box sx={{ width: "100%", height: "100%", position: "relative", bgcolor: "#000" }}>
      {isLoading && <Typography sx={{ color: "white", textAlign: "center" }}>Loading...</Typography>}
      {error && <Typography sx={{ color: "red", textAlign: "center" }}>{error}</Typography>}

      <video
        ref={videoRef}
        width="100%"
        height="100%"
        onLoadedMetadata={handleLoadedMetadata}
        onTimeUpdate={handleTimeUpdate}
        onError={() => setError("Error loading video")}
        style={{ backgroundColor: "black" }}
        controls={false}
      />

      <Box sx={{
        position: "absolute",
        bottom: 0,
        left: 0,
        right: 0,
        bgcolor: "rgba(0, 0, 0, 0.6)",
        display: "flex",
        flexDirection: "column",
        p: 1,
      }}>
        <Slider
          step={0.001}
          min={0}
          max={duration}
          value={currentTime}
          onChange={handleSliderChange}
          sx={{ color: "primary.main" }}
        />
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Box>
            <IconButton onClick={handlePlayPause} sx={{ color: "white" }}>
              {playing ? <PauseIcon /> : <PlayArrowIcon />}
            </IconButton>
            <Typography component="span" sx={{ color: "white", mr: 2 }}>
              {formatTime(currentTime)} / {formatTime(duration)}
            </Typography>
          </Box>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <VolumeUpIcon sx={{ color: "white", mr: 1 }} />
            <Slider
              min={0}
              max={1}
              step={0.01}
              value={volume}
              onChange={handleVolumeChange}
              sx={{ width: 100, color: "primary.main" }}
            />
            <IconButton onClick={handleFullscreen} sx={{ color: "white", ml: 2 }}>
              <FullscreenIcon />
            </IconButton>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default VideoPlayer;
