import React, { useState, useEffect } from "react";
import { <PERSON>ton, <PERSON>alog, DialogTitle, DialogContent, DialogActions, Stack, Grid, Box, TextField, Backdrop, CircularProgress, BottomNavigation } from "@mui/material";
import Slide from "@mui/material/Slide";

import IconButton from "@mui/material/IconButton";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";

import Typography from "@mui/material/Typography";

import Paper from "@mui/material/Paper";


import { doAjax, doCrudApi } from "../utility/serviceRequest";
import { useDispatch, useSelector } from "react-redux";

import Confirmation from "./ConfirmationDialog";

import CheckIcon from "@mui/icons-material/Check";

import CloseIcon from "@mui/icons-material/Close";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";

import EditIcon from "@mui/icons-material/Edit";
import ReusableTable from "../component/ReusableTable";
import ReusablePromptBox from "../component/PromptBox_Email/ReusablePromptBox";
import { doAjax as doAjaxMDG } from "@components/Common/fetchService";
import { destination_MaterialMgmt } from "../../../destinationVariables";
import { END_POINTS } from "@constant/apiEndPoints";
import {baseUrl_Notification} from '@data/baseUrl'


import { v4 as uuid } from "uuid";

const ManageTemplate = ({ onClose, userList, groupList, setScenario, ...props }) => {


  const userReducer = useSelector((state) => state.userReducer);
  const [userDetails, setUserDetails] = useState(new Map());
  const [groupDetails, setGroupDetails] = useState(new Map());
  const [isLoader, setLoader] = React.useState(false);
  const [filterData, setFilterData] = useState({
    application: null,
    process: null,
    templateName: null,
    participant: null,
    ccParticipant: null,
    groupName: null,
    emailId: null,
  });
  const [isGroupDefinition, setIsGroupDefinition] = useState(false);
  const [identifierData, setIdentifierData] = useState([]);
  const [entityData, setEntityData] = useState([]);
  const [processData, setProcessData] = useState([]);
  const [templateData, setTemplateData] = useState([]);
  const [toparticipant, setToparticipant] = useState([]);
  const [ccToParticipant, setCcToParticipant] = useState([]);
  const [ccToParticipant_Options, setCcToParticipant_Options] = useState([]);

  const participantData = ["GROUP", "USER"];
  const [openalert, setOpenalert] = useState(false);
  const [severity, setSeverity] = useState("success");
  const [data, setData] = useState([]);
  const [showConfirmation, setShowConfirmation] = React.useState(false);
  const [confirmationMessage, setConfirmationMessage] = React.useState("");
  const [buttonAction, setButtonAction] = React.useState("Cancel");
  const [actionItemId, setActionItemId] = useState('');
  const [actionRowData, setActionRowData] = useState([]);



  const newRow = {
    ccParticipant: "",
    ccParticipantType: "",
    createdBy: userReducer.userData.user_id,
    createdOn: new Date().toISOString(),
    updatedBy: userReducer.userData.user_id,
    updatedOn: new Date().toISOString(),
    fromDestination: "",
    id: uuid(),
    application: "",
    name: "",
    process: "",
    regionId: "",
    status: "",
    templateId: null,
    toParticipant: "",
    toParticipantType: "",
    isRowEditable: true,
    creationType: "new",
    applicationName: "",
    processName: "",
    entity: "",
    identifier: "",
  };
  const [rowData, setRowData] = useState(newRow);

  const handleInputChange = (event, option) => {
    const { name, value } = event.target;
    option[name] = value;
  };

  const [emailError, setEmailError] = useState("");
  // const [filtermapping,setFiltermapping]= useState([]);
  // const [filtergroup,setFiltergroup]= useState([]);
  // const [filterData, setFilterData] = useState({
  //   application: null,
  //   process: null,
  //   templateName: null,
  //   participant: null,
  //   ccParticipant: null,
  //   groupName: null,
  //   emailId: null,
  // });

  useEffect(() => {
    if (userReducer.applicationName !== "") {
      getUserList(setToparticipant);
      getUserList(setCcToParticipant);
      
    }
  }, [userReducer]);


  const getUserList = (setter) => {
    let transFormData = userList.map((ele,index) => {
      userDetails.set(ele.emailId, ele.userName);
      return { id: ele.emailId, name: ele.userName };
      //   return ele.emailId;
    });
    setUserDetails(new Map(userDetails));
    setter(transFormData);
  };

  const templateNameListGet = (appval, processval, identifier, entity) => {
    setLoader(true);
    // if (appval === "ITM") {
    doCrudApi(
      "fetchTemplatesOnIdentifiersHana",
      [identifier, entity, processval],

      function (oData) {
        if (oData.statusCode === 401 || oData.statusCode === "401") {
          setShowConfirmation(true);
          setButtonAction("Timeout");
          setConfirmationMessage("Session Timed Out.Kindly Refresh");
        }
        if (oData) setTemplateData(oData);
        setLoader(false);
      },
      function (error) {
        setOpenalert(true);
        setEmailError("error");
        setSeverity("error");
        setLoader(false);
      }
    );
  };

 

  const getFilteredData = () => {
    setLoader(true);
    // const filterMappingLength = props?.filtermapping?.length;
    // const filterGroupLength = props?.filtergroup?.length;

    // for(let i = 0; i < filterMappingLength; i++){
    //   props?.filtermapping.pop();
    // }

    // for(let i = 0; i < filterGroupLength; i++){
    //   props?.filtergroup.pop();
    // }

    let url = isGroupDefinition
      ? "/WorkUtilsServices/v1/mail-group?"
      : "/WorkUtilsServices/v1/mail-mapping?";

    if (isGroupDefinition) {
      url += "emailId=";
      if (filterData.emailId) {
        url += filterData.emailId;
        // props?.filtergroup.push(`${filterData.emailId}`);
      }
      url += "&groupName=";
      if (filterData.groupName) {
        url += filterData.groupName;
        //  props?.filtergroup.push(`${filterData.groupName}`);
      }
    } else {
      url += "application=";
      if (filterData.application) {
        url += filterData.application;
        // props?.filtermapping.push(`${filterData.application}`);
      }
      url += "&process=";
      if (filterData.process) {
        url += filterData.process;
        // props?.filtermapping.push(`${filterData.process}`);
      }
      url += "&emailTemplateId=";
      if (filterData.templateName) {
        url += filterData.templateName;
        // props?.filtermapping.push(`${filterData.templateName}`);
      }
      url += "&toParticipanttype=";
      if (filterData.participant) {
        url += filterData.participant;
        // props?.filtermapping.push(`${filterData.participant}`);
      }
      url += "&ccParticipanttype=";
      if (filterData.ccParticipant) {
        url += filterData.ccParticipant;
        // props?.filtermapping.push(`${filterData.ccParticipant}`);
      }
    }

    doAjax(
      url,
      "get",
      null,
      function (oData) {
        if (oData.statusCode === 401 || oData.statusCode === "401") {
          setShowConfirmation(true);
          setButtonAction("Timeout");
          setConfirmationMessage("Session Timed Out.Kindly Refresh");
        }
        if (!oData.data) {
          oData.data = [];
        }
        if (!oData.data[0]) {
          setShowConfirmation(true);
          setButtonAction("error");
          setConfirmationMessage("No record");
        }
        oData.data.forEach((e) => {
          e["isRowEditable"] = false;
          e["isEdited"] = false;
        });
        setData(oData.data);
        setLoader(false);
      },
      function (error) {
        setLoader(false);
        setOpenalert(true);
        setEmailError("error");
        setSeverity("error");
      }
    );
  };

  const handleDropdownChange = (event, id, option, property) => {
    let index = ''
    
    for(let i = 0; i < data.length; i++){
      if(data[i].id === id){
        index = i;
        break;
      }
    }

    data[index].isEdited = true;
    if (property === "application") {
      data[index].applicationName = option.applicationName;
      data[index].application = option.application;
      setData([...data]);
      getProcessList(option.application);
    }

    if (property === "identifier") {
      data[index].identifierDesc = option.identifierDesc;
      data[index].identifier = option.identifier;
      setData([...data]);
      getEntityList(userReducer.applicationName, option.identifier);
    } else if (property === "entity") {
      data[index].entitydesc = option.entitydesc;
      data[index].entity = option.entity;
      setData([...data]);
      // if (userReducer.applicationName === "ITM")
      getProcessList(
        userReducer.applicationName,
        option.entity,
        data[index].identifier
      );
      // else getProcessList(data[index].application, option.entity);
    } else if (property === "process") {
      data[index].processName = option.processName;
      data[index].process = option.process;
      setData([...data]);
      // if (userReducer.applicationName === "ITM")
      templateNameListGet(
        userReducer.applicationName,
        option.process,
        data[index].identifier,
        data[index].entity
      );
      // else templateNameListGet(data[index].application, option.process);
    } else if (property === "name") {
      data[index].templateName = option.name;
      data[index].templateId = option.emailDefinitionId;
      setData([...data]);
    } else if (property === "toParticipantType") {
      data[index].toParticipantType = option;
      setData([...data]);
      //   if (userReducer.applicationName !== "ITM") {
      if (option === "VARIABLE") {
        setToparticipant([
          { id: "INITIATOR", name: "INITIATOR" },
          { id: "REVIEWER", name: "REVIEWER" },
        ]);
      }
      else {
        getUserList(setToparticipant)
      }
    }
    else if (property === "toParticipant") {
      data[index].toParticipant = option.id;
      setData([...data]);
    } else if (property === "ccParticipantType") {
      data[index].ccParticipantType = option;
      if (option === "") {
          data[index].ccParticipant = ""
          setCcToParticipant([
          ]);
        }
      else if (option === "VARIABLE") {
        setCcToParticipant([
          // { id: "INITIATOR", name: "INITIATOR" },
          // { id: "REVIEWER", name: "REVIEWER" },
        ]);
      }
      else {
        getUserList(setCcToParticipant_Options)
      }
      setData([...data]);
    } else if (property === "ccParticipant") {
      data[index].ccParticipant = option.id;

      setData([...data]);
    }
  };

  const handleCreate = () => {
    let data1 = structuredClone(data);
    data1.unshift(newRow);
    setData(data1);
  };

  const handleDiscardClick = (id) => {
    let index = ''
    // option.isRowEditable = true;
    let data1 = [...data];
    for(let i= 0;i< data1.length;i++){
      if(data1[i].id===id){
        index = i;
        break;
      }
    }
    
    if (data[index].hasOwnProperty("creationType")) {
      const tempRows = [...data];
      tempRows.splice(index, 1);
      setData(tempRows);
    }
  };

  const handleEditClick = (option, id) => {
    // if (userReducer.applicationName === "ITM") {
      let index = ''

    option.isRowEditable = true;
    let data1 = [...data];
    for(let i= 0;i< data1?.length;i++){
      if(data[i].id===id){
        index = i;
        break;
      }
    }
    data1[index] = option;
    setData(data1);
    getEntityList(userReducer.applicationName, option.identifier);
    getProcessList(
      userReducer.applicationName,
      option.entity,
      option.identifier
    );
    templateNameListGet(
      userReducer.applicationName,
      option.process,
      option.identifier,
      option.entity
    );
    //set CCparticipant option for selected row
    switch(option.ccParticipantType){
      case "USER":
        getUserList(setCcToParticipant_Options)
        break;
    }
 

    if (option.toParticipantType === "GROUP") {
      // getParticipantList(
      //   userReducer.applicationName,
      //   option.toParticipantType,
      //   setToparticipant
      // );
    } else if (option.toParticipantType === "USER") {
      getUserList(setToparticipant);
    } else if (option.toParticipantType === "VARIABLE") {
      setToparticipant([
        { id: "INITIATOR", name: "INITIATOR" },
        { id: "REVIEWER", name: "REVIEWER" },
      ]);
    }
    if (option.ccParticipantType === "GROUP") {
      // getParticipantList(
      //   userReducer.applicationName,
      //   option.ccParticipantType,
      //   setCcToParticipant
      // );
    } else if (option.ccParticipantType === "USER") {
      getUserList(setCcToParticipant);
    } else if (option.ccParticipantType === "VARIABLE") {
      setCcToParticipant([
        { id: "INITIATOR", name: "INITIATOR" },
        { id: "REVIEWER", name: "REVIEWER" },
      ]);
    }
  };

  const handleDeleteClick = (id) => {
    setPromptBoxState(prev =>({...prev,open:false}))

    setLoader(true);
    doAjax(
      "/WorkUtilsServices/v1/mail-mapping?Id=" + id,
      "delete",
      null,
      function (oData) {
        if (oData.statusCode === 401 || oData.statusCode === "401") {
          promptAction_Functions.handleOpenPromptBox("ERROR", {
            title: "Error",
            message: "Session Timeout",
            severity: "danger",
            cancelButton: true,
            okButton: true,
            okButtonText: "Ok",
          });
        }
        promptAction_Functions.handleOpenPromptBox("SUCCESS", {
          title: "Success",
          message: "successfully updated the record",
          severity: "success",
        });
        setLoader(false);
      },
      function (error) {
        promptAction_Functions.handleOpenPromptBox("ERROR", {
          title: "Error",
          message: "Failed to update the record",
          severity: "danger",
          cancelButton: true,
          okButton: true,
          okButtonText: "Ok",
        });
        setLoader(false);
      }
    );
  };


    //<-- Functions and variables for ReusablePromptBox *promptAction_Functions -->
    const [promptBoxState, setPromptBoxState] = useState({
      open: false,
      type: "",
      redirectOnClose: true,
      message: "",
      title: "",
      severity: "",
    });
    const [promptBoxScenario, setPromptBoxScenario] = useState("");
  
    const promptAction_Functions = {
      handleClosePromptBox: () => {
        setPromptBoxState((prev) => ({
          open: false,
          type: "",
          redirectOnClose: true,
          message: "",
          title: "",
          severity: "",
        }));
        setPromptBoxScenario("");
      },
      handleOpenPromptBox: (ref, data = {}) => {
        // SUCCESS,FAILURE,WARNING,QUANTITYERROR,
        let initialData = {
          open: true,
          title: "",
          message: "",
          okButton: true,
          cancelButton: true,
          okText: "Ok",
          cancelText: "Cancel",
          type: "dialog",
        };
        if (ref === "SUCCESS") {
          initialData.type = "snackbar";
        }
        setPromptBoxScenario(ref);
        setPromptBoxState({
          ...initialData,
          ...data,
        });
      },
      handleCloseAndRedirect: () => {
        promptAction_Functions.handleClosePromptBox();
        navigate("");
      },
      getCancelFunction: () => {
        switch (promptBoxScenario) {
          default:
            return () => {
              promptAction_Functions.handleClosePromptBox();
            };
        }
      },
      getCloseFunction: () => {
        switch (promptBoxScenario) {
          case "COMMENTERROR":
          default:
            return (value) => {
              promptAction_Functions.handleClosePromptBox();
            };
        }
      },
      getOkFunction: () => {
  
        switch (promptBoxScenario) {
  
          case "DISCARD_CHANGES":
            return () => {
              setPromptBoxState(prev=>({...prev,open:false}))
              handleDiscardClick(actionItemId)}
  
            case "DELETE_MAPPING":
              return () =>{
                handleDeleteClick(actionItemId)}
                case "SUBMIT_CHANGES":
                  return () =>{
                    handleSaveClick(actionRowData,actionItemId)}
        
          default:
            return () => promptAction_Functions.handleClosePromptBox();
  
        }
      },
      getCloseAndRedirectFunction: () => {
        if (!promptBoxState.redirectOnClose) {
          return promptAction_Functions.handleClosePromptBox;
        }
        return promptAction_Functions.handleCloseAndRedirect;
      },
    };

  const handleSaveClick = (option, id) => {
    setLoader(true);
    setPromptBoxState(prev=>({...prev,open:false}))
    let index = ''
    let data1 = [...data];
    for(let i= 0;i< data1.length;i++){
      if(data[i].id===id){
        index = i;
        break;
      }
    }
    
    data[index].isRowEditable = false;
    if (option.isEdited) {
      data[index].isEdited = false;
      let url = "/WorkUtilsServices/v1/mail-mapping";
      let method = "POST";

      if (option.id) {
        method = "PATCH";
      }

      let payload = {
        ccParticipant: option.ccParticipant,
        ccParticipantType: option.ccParticipantType,
        createdBy: option.createdBy,
        createdOn: new Date(option.createdOn).toISOString(),
        fromDestination: null,
        id: option.id,
        // application:"IPM",
        application: userReducer.applicationName,
        // application:
        //   userReducer.applicationName === "ITM" ? "ITM" : option.application,
        name: option.templateName,
        process: option.process,
        regionId: option.regionId,
        status: option.status,
        templateId: option.templateId,
        toParticipant: option.toParticipant,
        toParticipantType: option.toParticipantType,
        updatedBy: userReducer.userData.user_id,
        updatedOn: new Date().toISOString(),
      };
      doAjax(
        url,
        method,
        payload,
        function (oData) {
          if (oData.statusCode === 401 || oData.statusCode === "401") {
            setShowConfirmation(true);
            promptAction_Functions.handleOpenPromptBox("ERROR", {
              title: "Error",
              message: "Session Timeout",
              severity: "danger",
              cancelButton: true,
              okButton: true,
              okButtonText: "Ok",
            });
          }
          setData([...data]);
          promptAction_Functions.handleOpenPromptBox("SUCCESS", {
            title: "Success",
            message: "successfully updated the record",
            severity: "success",
          
          });
          setLoader(false);
        },
        function (error) {
        
          promptAction_Functions.handleOpenPromptBox("ERROR", {
            title: "Error",
            message: "Failed to update the record",
            severity: "danger",
            cancelButton: true,
            okButton: true,
            okButtonText: "Ok",
          });
          setLoader(false);
        }
      );
    }
  };

 

  useEffect(() => {
    if (userReducer.applicationName !== "") {
      getIdentifierList();
    }
  }, [userReducer]);

  const getIdentifierList = () => {
    setLoader(true);
     const onSuccess = (oData) => {
          if(oData){
            setIdentifierData(oData)
            setLoader(false)
          }
        };
        const onError = (error) => {
          setOpenalert(true);
          setEmailError("error");
          setSeverity("error");
          setLoader(false);
        };
        setLoader(false);
        const url = `${baseUrl_Notification}${END_POINTS.EMAIL_CONFIG.POPULATE_APP_IDENTIFIERS_HANA}`;
        doAjax(url, "get", onSuccess, onError);

  };

    const getEntityList = (appName, identifier) => {
      setLoader(true);
    
      const onSuccess = (oData) => {
        if (oData?.statusCode === 401 || oData?.statusCode === "401") {
          promptAction_Functions.handleOpenPromptBox("TIMEOUT", {
            title: "Error",
            message: "Session Timed Out. Kindly Refresh",
            severity: "danger",
            cancelButton: true,
            okButton: true,
            okButtonText: "Refresh",
          });
        } else if (oData) {
          const formattedData = Array.isArray(oData) ? oData : [];
          setEntityData (formattedData);
        }
        setLoader(false);
      };
    
      const onError = (error) => {
        props?.setAlert?.(true);
        props?.setAlertSeverity?.("error");
        props?.setAlertMessage?.(error);
        setLoader(false);
      };
    
      const url = `/${destination_MaterialMgmt}${END_POINTS.EMAIL_CONFIG.FETCH_NOTIFICATION_MODULES}?identifierId=${encodeURIComponent(identifier)}`;    
      doAjaxMDG(url, "get", onSuccess, onError);
    };

  // const getEntityList = (appName, identifier) => {
  //   setLoader(true);

  //   if (appName === "ITM") {
  //     fetchWrapper(
  //       "/CrudServices/crud/api/fetchQuery?converterName=map",
  //       {
  //         method: "POST",
  //         headers: {
  //           Authorization: props?.authorization,
  //           env: props?.destinations["env"],
  //           "Content-Type": "application/json",
  //           mode: "cors",
  //           "Access-Control-Allow-Origin": "*",
  //         },
  //         body: JSON.stringify({
  //           query: "fetchNotificationModulesOnIdentifiersHana",
  //           args: [identifier],
  //         }),
  //       },
  //       props?.destinations
  //     )
  //       .then((res) => res.json())
  //       .then((data) => {
  //         if (data.statusCode === 401 || data.statusCode === "401") {
  //           setShowConfirmation(true);
  //           setButtonAction("Timeout");
  //           setConfirmationMessage("Session Timed Out.Kindly Refresh");
  //         }
  //         if (data) setEntityData(data);
  //         setLoader(false);
  //       })
  //       .catch((error) => {
  //         setLoader(false);
  //       });
  //   } else {
  //     fetchWrapper(
  //       "/WorkUtilsServices/v1/applications/entity?application=" + appName,
  //       {
  //         headers: { Authorization: props?.authorization },
  //       },
  //       props?.destinations
  //     )
  //       .then((res) => res.json())
  //       .then((json) => {
  //         if (json.statusCode === 401 || json.statusCode === "401") {
  //           setShowConfirmation(true);
  //           setButtonAction("Timeout");
  //           setConfirmationMessage("Session Timed Out.Kindly Refresh");
  //         }
  //         if (json.data) setEntityData(json.data);
  //         setLoader(false);
  //       })
  //       .catch((error) => {
  //         props?.setAlert(true);
  //         props?.setAlertSeverity("error");
  //         props?.setAlertMessage(error);
  //         setLoader(false);
  //       });
  //   }
  // };

  const getProcessList = (appName, entityName, identifier) => {
    setLoader(true);
    const onSuccess = (oData) => {
      if (oData?.statusCode === 401 || oData?.statusCode === "401") {
        promptAction_Functions.handleOpenPromptBox("TIMEOUT", {
          title: "Error",
          message: "Session Timed Out. Kindly Refresh",
          severity: "danger",
          cancelButton: true,
          okButton: true,
          okButtonText: "Refresh",
        });
      } else if (oData) {
        const formattedData = Array.isArray(oData) ? oData : [];
        setProcessData(formattedData);
      }
      setLoader(false);
    };

    const onError = (error) => {
      props?.setAlert?.(true);
      props?.setAlertSeverity?.("error");
      props?.setAlertMessage?.(error);
      setLoader(false);
    };

    const url = `/${destination_MaterialMgmt}${END_POINTS.EMAIL_CONFIG.FETCH_NOTIFICATION_EVENTS}?identifierId=${encodeURIComponent(identifier)}&notificationId=${encodeURIComponent(entityName)}`;

    doAjaxMDG(url, "get", onSuccess, onError);
  };


  // const getProcessList = (appName, entityName, identifier) => {
  //   setLoader(true);
  //   if (appName === "ITM") {
  //     fetchWrapper(
  //       "/CrudServices/crud/api/fetchQuery?converterName=map",
  //       {
  //         method: "POST",
  //         headers: {
  //           Authorization: props?.authorization,
  //           env: props?.destinations["env"],
  //           "Content-Type": "application/json",
  //           mode: "cors",
  //           "Access-Control-Allow-Origin": "*",
  //         },
  //         body: JSON.stringify({
  //           query: "fetchNotificationEventsOnIdentifiers",
  //           args: [identifier, entityName],
  //         }),
  //       },
  //       props?.destinations
  //     )
  //       .then((res) => res.json())
  //       .then((data) => {
  //         if (data.statusCode === 401 || data.statusCode === "401") {
  //           setShowConfirmation(true);
  //           setButtonAction("Timeout");
  //           setConfirmationMessage("Session Timed Out.Kindly Refresh");
  //         }
  //         if (data) setProcessData(data);
  //         setLoader(false);
  //       })
  //       .catch((error) => {
  //         props?.setAlert(true);
  //         props?.setAlertSeverity("error");
  //         props?.setAlertMessage(error);
  //         setLoader(false);
  //       });
  //   } else {
  //     fetchWrapper(
  //       "/WorkUtilsServices/v1/applications/process?application=" + appName,
  //       {
  //         headers: { Authorization: props?.authorization },
  //       },
  //       props?.destinations
  //     )
  //       .then((res) => res.json())
  //       .then((json) => {
  //         if (json.statusCode === 401 || json.statusCode === "401") {
  //           setShowConfirmation(true);
  //           setButtonAction("Timeout");
  //           setConfirmationMessage("Session Timed Out.Kindly Refresh");
  //         }
  //         if (json.data) setProcessData(json.data);
  //       });
  //   }
  // };

  const closeConfirmationDialog = (evt) => {
    if (evt === "Timeout") {
      window.location.reload();
    }
    setShowConfirmation(false);
    setConfirmationMessage("");
  };








  const handlemanagedialogClose = () => {
    onClose();
    setScenario('INITIAL');
    // resetDefault();
    // setProcessData([]);
  };

  const columns = [
    { field: "id", headerName: "ID", hide: true },
    {
      field: "masterDataCat",
      headerName: props?.headers[0] ? props?.headers[0] : "Identifier",
      flex: 1,
      headerAlign: "left",
      align:'left',
      renderCell: (params) => {
        if (params.row.isRowEditable) {
          return (
            <Select
              // style={{ padding: '0px 0 0px 0px'}}
              value={params.row.identifier}
              sx={{
                width: "15rem",
                height: "2rem",
                fontWeight: 400,
                fontSize: "12px",
                fontFamily: `"Roboto", sans-serif !important`,
                color: "black !important",
              }}
            >
              {identifierData?.map((option) => (
              <MenuItem
                sx={{
                  width: "15rem",
                  height: "2rem",
                  fontWeight: 400,

                  fontSize: "12px",
                  fontFamily: `"Roboto", sans-serif !important`,
                  color: "black !important",
                }}
                value={option.identifier}
                onClick={(e) =>
                  handleDropdownChange(
                    e,
                    params.row.id,
                    option,
                    "identifier"
                  )
                }
              >
                {option.identifierDesc}
              </MenuItem>
            ))}
            </Select>
          )
        }
        return params.row.identifierDesc

      }
    },
    {
      field: "entity",
      headerName: props?.headers[1] ? props?.headers[1] : "Module",
      flex: 1,
      headerAlign: "left",
      align:'left',
      renderCell: (params) => {
        return (
          <>
            {params.row.isRowEditable ? (
              <Select
                value={params.row.entity}
                sx={{
                  width: "15rem",
                  height: "2rem",
                  fontWeight: 400,

                  fontSize: "12px",
                  fontFamily: `"Roboto", sans-serif !important`,
                  color: "black !important",
                }}
              >
                {entityData?.map((option) => (
                <MenuItem
                  sx={{
                    width: "15rem",
                    height: "2rem",
                    fontWeight: 400,

                    fontSize: "12px",
                    fontFamily: `"Roboto", sans-serif !important`,
                    color: "black !important",
                  }}
                  value={option.entity}
                  onClick={(e) =>
                    handleDropdownChange(e, params.row.id, option, "entity")
                  }
                >
                  {option.entityDesc}
                </MenuItem>
                        ))}
              </Select>
            ) : (
              params.row.entitydesc
            )}
          </>
        )
      }

    },
    {
      field: "process",
      headerName: props?.headers[2] ?? "Event",
      type: "boolean",
      headerAlign: "left",
      align:'left',
      flex: 1,
      renderCell: (params) => {
        return (
          <>
            {params.row.isRowEditable ? (
              <Select
                value={params.row.process}
                sx={{
                  width: "15rem",
                  height: "2rem",
                  fontWeight: 400,

                  fontSize: "12px",
                  fontFamily: `"Roboto", sans-serif !important`,
                  color: "black !important",
                }}
              >
                {processData?.map((option) => (
                <MenuItem
                  sx={{
                    width: "15rem",
                    height: "2rem",
                    fontWeight: 400,

                    fontSize: "12px",
                    fontFamily: `"Roboto", sans-serif !important`,
                    color: "black !important",
                  }}
                  value={option.process}
                  onClick={(e) =>
                    handleDropdownChange(e, params.row.id, option, "process")
                  }
                >
                  {option.processDesc}
                </MenuItem>
                        ))}
              </Select>
            ) : (
              params.row.processName
            )}
          </>
        )
      }
    },
    {
      field: "templateName",
      headerName: props?.headers[3] ? props?.headers[3] : "Template Name",
      
      flex: 1,
      headerAlign: "left",
      align:'left',
      renderCell: (params) => {
        return (
          <>
            {params.row.isRowEditable ? (
              <Select
                value={params.row?.templateName}
                sx={{
                  width: "100%",
                  height: "2rem",
                  fontWeight: 400,

                  fontSize: "12px",
                  fontFamily: `"Roboto", sans-serif !important`,
                  color: "black !important",
                }}
              >
                {templateData?.map((option) => (
                <MenuItem
                  sx={{
                    width: "inherit",
                    height: "2rem",
                    fontWeight: 400,

                    fontSize: "12px",
                    fontFamily: `"Roboto", sans-serif !important`,
                    color: "black !important",
                  }}
                  value={option?.name}
                  onClick={(e) =>
                    handleDropdownChange(e, params.row.id, option, "name")
                  }
                >
                  {option?.name}
                </MenuItem>
                        ))}
              </Select>
            ) : (
              params.row.templateName
            )}
          </>
        )
      }
    },
    {
      field: "toParticipantType",
      headerName: "Recipent Type",
      type: "boolean",
      hide:true,
      flex: 1,
      headerAlign: "left",
      align:'left',
      renderCell: (params) => {
        return (
          <>
            {params.row.isRowEditable ? (
              <Select
                name="toParticipantType"
                value={params.row.toParticipantType}
                sx={{
                  width: "15rem",
                  height: "2rem",
                  fontWeight: 400,

                  fontSize: "12px",
                  fontFamily: `"Roboto", sans-serif !important`,
                  color: "black !important",
                }}
              >
                {participantData?.map((ele) => (
                  <MenuItem
                    sx={{
                      width: "15rem",
                      height: "2rem",
                      fontWeight: 400,

                      fontSize: "12px",
                      fontFamily: `"Roboto", sans-serif !important`,
                      color: "black !important",
                    }}
                    key={ele}
                    value={ele}
                    onClick={(e) =>
                      handleDropdownChange(
                        e,
                        params.row.id,
                        ele,
                        "toParticipantType"
                      )
                    }
                  >
                    {ele}
                  </MenuItem>
                ))}
              </Select>
            ) : (
              params.row.toParticipantType
            )}
          </>
        )
      }
    },
    {
      field: "toParticipant",
      headerName: "Recipent",
      sortable: false,
      filterable: false,
      width: "100",
      headerAlign: "left",
      align:'left',
      hide:true,
      renderCell: (params) => {
        return (
          <>
            {params.row.isRowEditable ? (
              <Select
                value={params.row.toParticipant}
                sx={{
                  width: "15rem",
                  height: "2rem",
                  fontWeight: 400,

                  fontSize: "12px",
                  fontFamily: `"Roboto", sans-serif !important`,
                  color: "black !important",
                }}
              >
                {toparticipant?.map((option) => (
                <MenuItem
                  sx={{
                    width: "15rem",
                    height: "2rem",
                    fontWeight: 400,

                    fontSize: "12px",
                    fontFamily: `"Roboto", sans-serif !important`,
                    color: "black !important",
                  }}
                  value={option.id}
                  onClick={(e) =>
                    handleDropdownChange(
                      e,
                      params.row.id,
                      option,
                      "toParticipant"
                    )
                  }
                >
                  {option.name}
                </MenuItem>
                        ))}
              </Select>
            ) : params.row.toParticipantType === "VARIABLE" ? (
              params.row.toParticipant
            ) : params.row.toParticipantType === "GROUP" ? (
              groupDetails.get(parseInt(params.row.toParticipant))
            ) : (
              userDetails.get(params.row.toParticipant)
            )}
          </>
        )
      }
    },
    {
      field: "ccParticipantType",
      headerName: "CC Type",
      sortable: false,
      filterable: false,
     flex:1,
      hide:false,
      headerAlign: "left",
      align:'left',
      renderCell: (params) => {
        return (
          <>
            {params.row.isRowEditable ? (
              <Select
                value={params.row.ccParticipantType}
                sx={{
                  width: "15rem",
                  height: "2rem",
                  fontWeight: 400,

                  fontSize: "12px",
                  fontFamily: `"Roboto", sans-serif !important`,
                  color: "black !important",
                }}
              >
                     <MenuItem
                    sx={{
                      width: "15rem",
                      height: "2rem",
                      fontWeight: 400,

                      fontSize: "12px",
                      fontFamily: `"Roboto", sans-serif !important`,
                      color: "grey !important",
                    }}
                    key={""}
                    value={""}
                    onClick={(e) =>
                      handleDropdownChange(
                        e,
                        params.row.id,
                        "",
                        "ccParticipantType"
                      )
                    }
                  >
                  Select CC Type
                  </MenuItem>
                {participantData?.map((ele) => (
                  <MenuItem
                    sx={{
                      width: "15rem",
                      height: "2rem",
                      fontWeight: 400,

                      fontSize: "12px",
                      fontFamily: `"Roboto", sans-serif !important`,
                      color: "black !important",
                    }}
                    key={ele}
                    value={ele}
                    onClick={(e) =>
                      handleDropdownChange(
                        e,
                        params.row.id,
                        ele,
                        "ccParticipantType"
                      )
                    }
                  >
                    {ele}
                  </MenuItem>
                ))}
              </Select>
            ) : (
              params.row.ccParticipantType
            )}
          </>
        )
      }
    },
    {
      field: "ccParticipant",
      headerName: "CC",
      sortable: false,
      filterable: false,
      flex:1,
      headerAlign: "left",
      align:'left',
      hide:false,
      renderCell: (params) => {
       
        return (
          <>
            {params.row.isRowEditable ? (
              <Select
                value={params.row.ccParticipant}
                sx={{
                  width: "15rem",
                  height: "2rem",
                  fontWeight: 400,

                  fontSize: "12px",
                  fontFamily: `"Roboto", sans-serif !important`,
                  color: "black !important",
                }}
              >
                {ccToParticipant_Options?.map((option) => (
                          <MenuItem
                            sx={{
                              width: "15rem",
                              height: "2rem",
                              fontWeight: 400,

                              fontSize: "12px",
                              fontFamily: `"Roboto", sans-serif !important`,
                              color: "black !important",
                            }}
                            value={option.id}
                            onClick={(e) =>
                              handleDropdownChange(
                                e,
                                params.row.id,
                                option,
                                "ccParticipant"
                              )
                            }
                          >
                            {option.name}
                          </MenuItem>
                        ))}
              </Select>
            ) : params.row.ccParticipantType === "VARIABLE" ? (
              params.row.ccParticipant
            ) : params.row.ccParticipantType === "GROUP" ? (
              groupDetails.get(parseInt(params.row.ccParticipant))
            ) : (
              userDetails.get(params.row.ccParticipant)
            )}
          </>
        )
      }
    },
    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      width: "100",
      hide:false,
      headerAlign: "center",
      align: "center",
      renderCell: (params) => {
       
        return (
          <>
            {params.row.isRowEditable ? (
              <Stack direction="row">
                <IconButton
                  size="small"
                  aria-label="Edit"
                  color="error"
                  onClick={() => {
                    //                     if (
                    //   params.row.applicationName &&
                    //   params.row.processName &&
                    //   params.row.templateName &&
                    //   params.row.toParticipantType &&
                    //   params.row.toParticipant &&
                    //   params.row.ccParticipantType &&
                    //   params.row.ccParticipant &&
                    //   (userReducer.applicationName !== "ITM" || params.row.identifier)
                    // ){
                      setActionRowData(params.row)
                      setActionItemId(params.row.id)
                      promptAction_Functions.handleOpenPromptBox("SUBMIT_CHANGES", {
                        title: "Confirm Submit",
                        message: "Do you want to save this record?",
                        severity: "success",
                        cancelButton: true,
                        okButton: true,
                        okButtonText: "Submit",
                      });
                    //                   }
                    //                   else{
                    //                     setOpenalert(true);
                    //   setEmailError("Please Enter all fields");
                    //   setSeverity("error");
                    //                   }
                  }}
                >
                  <CheckIcon sx={{ color: "green" }} />
                </IconButton>
                <IconButton
                  size="small"
                  aria-label="Edit"
                  color="error"
                  onClick={() =>{
                    setActionItemId(params.row.id)
                    promptAction_Functions.handleOpenPromptBox("DISCARD_CHANGES", {
                      title: "Confirm Discard",
                      message: "Are you sure you want to proceed with discard? The entered data will be lost",
                      severity: "warning",
                      cancelButton: true,
                      okButton: true,
                      okButtonText: "Discard",
                    });

                    }}
                >
                  <CloseIcon sx={{ color: "red" }} />
                </IconButton>
              </Stack>
            ) : (
              <Stack direction="row">
                <IconButton
                  size="small"
                  aria-label="Edit"
                  color="error"
                  onClick={() => handleEditClick(params.row, params.row.id)}
                >
                  <EditIcon sx={{ color: "blue" }} />
                </IconButton>
                <IconButton
                  size="small"
                  aria-label="Edit"
                  color="error"
                  onClick={() => {
                    setActionItemId(params.row.id)
                    promptAction_Functions.handleOpenPromptBox("DELETE_MAPPING", {
                      title: "Confirm Delete",
                      message: "Do you want to delete this record?",
                      severity: "warning",
                      cancelButton: true,
                      okButton: true,
                      okButtonText: "Ok",
                    });
                  }}
                >
                 <DeleteOutlinedIcon color="danger" />
                </IconButton>
              </Stack>
            )}
          </>
        )
      }
    }
  ];
  return (
    <div p={2} className="cwntSetHeight100 cwntSetWidth100">
      {/* <Dialog
        // fullScreen
        open={props?.open}
        onClose={handlemanagedialogClose}
        // maxWidth="lg"
        PaperProps={{ style: { maxWidth: '100vw', // Set your desired 
        width: '100%', // Set your desired width 
        height: '100%', // Set your desired height 
        margin: '0', // Center the dialog horizontally 
        maxHeight:"100vh",
        boxShadow:"none"
        }, 
        }}
       
        hideBackdrop
        // TransitionComponent={Transition}
      > */}

      <Grid container sx={{ marginBottom: "1.5rem" }}>
        <Grid
          item
          md={9}
          style={{
            display: "flex",
          }}
        >
          <Grid item sx={{ maxWidth: "max-content" }}>
            <IconButton
              onClick={handlemanagedialogClose}
              color="primary"
              component="label"
              className="iconButton-spacing-small"
              sx={{
                padding: "0.25rem",
                height: "max-content",
              }}
            >
              <ArrowCircleLeftOutlinedIcon
                sx={{
                  fontSize: "25px",
                  color: "#000000",
                }}
              />
            </IconButton>
          </Grid>
          <Grid item xs>
            <Typography variant="h3">
              <strong>
                Associated Template
              </strong>
            </Typography>
            <Typography variant="body2" color="#777">
              This view allows user to manage associated Email Templates
            </Typography>
          </Grid>
        </Grid>

      </Grid>
      <Backdrop className="backdrop" sx={{ zIndex: '9' }} open={isLoader}>
        <CircularProgress color="primary" />
      </Backdrop>
      <ReusablePromptBox
        type={promptBoxState.type}
        promptState={promptBoxState.open}
        setPromptState={promptAction_Functions.handleClosePromptBox}
        onCloseAction={promptAction_Functions.getCloseFunction()}
        promptMessage={promptBoxState.message}
        dialogSeverity={promptBoxState.severity}
        dialogTitleText={promptBoxState.title}
        handleCancelButtonAction={promptAction_Functions.getCancelFunction()}
        cancelButtonText={promptBoxState.cancelText} //Cancel button display text
        showCancelButton={promptBoxState.cancelButton} //Enable Cancel button
        handleSnackBarPromptClose={promptAction_Functions.getCloseAndRedirectFunction()}
        handleOkButtonAction={promptAction_Functions.getOkFunction()}
        okButtonText={promptBoxState.okButtonText}
        showOkButton={promptBoxState.okButton}
      />
      {/* <DialogTitle>
         
        </DialogTitle> */}
      {/* <DialogContent> */}
      {/* <div style={{width:"100%"}}> */}

      {/* {userReducer.applicationName !== "ITM" && (
            <Tooltip title="Filter">
              <IconButton
                onClick={handleClickOpen}
                classname="customhover"
                size="small"

                // sx={{
                //   marginRight: "1rem",
                //   textTransform : 'none'
                // }}
              >
                <FilterAltOutlinedIcon sx={{ color: "black" }} />
              </IconButton>
            </Tooltip>
          )} */}
      {/* </div> */}
    

      {/* </DialogContent> */}
      {/* <DialogActions>
            <Button onClick={handleClose}>Cancel</Button>
            <Button onClick={handleClose} variant="contained" color="primary">
              Save
            </Button>
          </DialogActions> */}


      {/* </Dialog> */}
      {/* <Confirmation message={confirmationMessage} creationType={buttonAction} open={showConfirmation} onClose={(evt) => closeConfirmationDialog(evt)} />
      <Snackbar open={openalert} autoHideDuration={6000} onClose={handleToClose} anchorOrigin={{ vertical: "bottom", horizontal: "center" }}>
        <Alert onClose={handleToClose} severity={severity} sx={{ width: "100%" }}>
          {emailError}
        </Alert>
      </Snackbar> */}
       <ReusableTable
              width="100%"
              rows={data ?? []}
              columns={columns}
              hideFooter={false}
              getRowIdValue={"id"}
              disableSelectionOnClick={true}
              isLoading={false}
            />
      <Confirmation
        message={confirmationMessage}
        creationType={buttonAction}
        open={showConfirmation}
        onClose={(evt) => closeConfirmationDialog(evt)}
      />
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0, zIndex:'4' }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
          >
            <Stack direction="row" sx={{ marginLeft: 'auto' }} spacing={2} alignItems="center">
             <Button
                variant="contained"
                sx={{
                  minWidth: "max-content",
                  padding: "6px 12px",
                  texttransform: "capitalize",
                  height: '2rem'
                }}
                onClick={handleCreate}
              >
                Create New Mapping
              </Button>
           
            </Stack>


          </BottomNavigation>
        </Paper>

    </div>
  );
};

export default ManageTemplate;
