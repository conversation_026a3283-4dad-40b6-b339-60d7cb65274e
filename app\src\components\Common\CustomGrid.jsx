import React, { useEffect, useState } from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { useSelector } from 'react-redux';
import moment from 'moment';
import { IconButton, Paper, Tooltip } from "@mui/material";
import AutoDeleteOutlinedIcon from "@mui/icons-material/AutoDeleteOutlined";
import DeleteForeverOutlinedIcon from "@mui/icons-material/DeleteForeverOutlined";
import PauseCircleOutlineIcon from '@mui/icons-material/PauseCircleOutline';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';


const groupRowsByModuleAndRequestType = (rows) => {
  const grouped = {};
  rows.forEach((row) => {
    const groupKey = `${row.Module}-${row.RequestType}`;
    if (!grouped[groupKey]) {
      grouped[groupKey] = [];
    }
    grouped[groupKey].push(row);
  });
  return grouped;
};

const getColorForRow = (module, reqType) => {
  const colorMap = {
    "Cost Center-Mass Create": "#f0f8ff",  // Light Blue
    "Cost Center-Mass Change": "#e6ffe6",  // Light Green
    "Profit Center-Mass Create": "#ffe4e1", // Light Pink
    "Profit Center-Mass Change": "#f5f5dc", // Light Beige
    "CC-PC Combo-Mass Create": "#f5f5f5", // Light Grey
    "CC-PC Combo-Mass Change": "#ffffe0", // Light Yellow
    "General Ledger-Mass Create": "#f7bee375", // Light Green (for Mass Create)
    "General Ledger-Mass Change": "#ffcccb75", // Light Red (for Mass Change)
    "General Ledger-Mass Extend": "#fff2cc", // Light Yellow (for Mass Extend)
  };

  // Default color if no mapping exists
  return colorMap[`${module}-${reqType}`] || "#ffffff"; // white fallback
};
const CustomDataGrid = (props) => {
  const { row, columns, onRowUpdate, isLoading, getRowIdValue, iconClicked, setIconClicked } = props;
  const [rows, setRows] = useState(row || []);
  const appSettings = useSelector((state) => state.appSettings);
  let taskRowDetails = useSelector((state) => state.userManagement.taskData);
  useEffect(() => {
    const sortedData = row.sort((a, b) => {
      if (a.module !== b.module) {
          return a.module.localeCompare(b.module);
      }
      if (a.reqType !== b.reqType) {
          return a.reqType.localeCompare(b.reqType);
      }
      return a.priority - b.priority;
  });

  console.log('sortedData',sortedData)
    setRows(sortedData || []);
  }, [row]);


  const handleDragEnd = (result) => {
    if (!result.destination) return; // If dropped outside, do nothing

    const { source, destination } = result;
    const sourceRow = rows[source.index]; // The row being dragged
    const destinationRow = rows[destination.index]; // The row where it's dropped
    // Create group keys for source and destination rows
    const sourceGroupKey = `${sourceRow.module}-${sourceRow.reqType}`;
    const destinationGroupKey = `${destinationRow.module}-${destinationRow.reqType}`;
    // Copy the rows array to avoid direct mutation
    const updatedRows = Array.from(rows);
    
    // Remove the dragged row from its original position
    const [reorderedRow] = updatedRows.splice(source.index, 1);
    
    // Case 1: If source and destination are in the same group (same module and reqType)
    if (sourceGroupKey === destinationGroupKey) {
      // Insert the reordered row at the destination index in the same group
      updatedRows.splice(destination.index, 0, reorderedRow);
      
      // Update priorities based on the new order
      const sameGroupRows = updatedRows.filter(
        (row) => `${row.module}-${row.reqType}` === sourceGroupKey
      );
      
      // Reassign priorities within the same group
      sameGroupRows.forEach((row, index) => {
        row.priority = index + 1; // Assign priority based on the index
      });
    }
    // Case 2: If source and destination are in different groups
    else {
      // Simply insert the row into the new position at the destination index
      updatedRows.splice(destination.index, 0, reorderedRow);
    }
    
    // Sort the rows to maintain the correct order (module -> reqType -> priority)
    const sortedRows = updatedRows.sort((a, b) => {
      if (a.module !== b.module) {
        return a.module.localeCompare(b.module); // Sort by module
      }
      if (a.reqType !== b.reqType) {
        return a.reqType.localeCompare(b.reqType); // Sort by reqType
      }
      return a.priority - b.priority; // Sort by priority
    });
    
    // Set the updated and sorted rows to the state
    setRows(sortedRows);
    onRowUpdate(sortedRows); // Notify parent component about the update
  
    // Debugging output
    console.log('Before Update:', rows);
    console.log('Moved Row:', reorderedRow);
    console.log('Destination Index:', destination.index);
    console.log('Updated Rows:', updatedRows);
    console.log('Sorted Rows:', sortedRows);
  };

console.log('customrows', rows)


  const handleIconClick = (rowId) => {
    console.log('checkid', rowId)
    setIconClicked((prev) => ({
      ...prev,
      [rowId]: !prev[rowId]  // Toggle the icon state for the specific rowId
    }));
    // setRows((prevRows) =>
    //   prevRows.map((row) =>
    //     row.id === rowId ? { ...row, schedulerStatus: !iconClicked[rowId] ? "Canceled" : row.schedulerStatus } : row
    //   )
    // );
  };
  const groupedRows = groupRowsByModuleAndRequestType(rows);

  return (
    // <div style={{ height: "100%", width: '100%' }}>
      //  <Paper elevation={3} style={{ padding: '16px', marginTop: '16px' }}>
      <DragDropContext onDragEnd={handleDragEnd}>
        <table
          style={{
            width: '100%',
            borderCollapse: 'collapse',
            fontFamily: 'Roboto, sans-serif',
          }}
        >
          <thead>
            <tr style={{ backgroundColor: '#f5f5f5' }}>
            {columns.map((column) => (
      <th key={column.field} style={{ width: column.width || 'auto', padding: '16px', textAlign: column.align || 'left' }}>
        {column.headerName}
        {column.renderHeader}
      </th>
    ))}
            </tr>
          </thead>

          {Object.keys(groupedRows).map((groupKey) => {
          const [module, requestType] = groupKey.split('-');
          const groupRowsData = groupedRows[groupKey];

          return (
          <Droppable droppableId={groupKey} key={groupKey}>
            {(provider) => (
              <tbody
                ref={provider.innerRef}
                {...provider.droppableProps}
                style={{ width: '100%' }}
              >
                {groupRowsData[0] ? groupRowsData?.map((user, index) => (
                  
                  <Draggable key={user.id} draggableId={user.id?.toString()} index={index} isDragDisabled={!props.showDragIcon}>
                    {(provider, snapshot) => {
                      const rowColor = getColorForRow(user.module, user.reqType);
                      return (
                      <tr
                        ref={provider.innerRef}
                        {...provider.draggableProps}
                        style={{
                          backgroundColor: snapshot.isDragging ? '#e0e0e0' : rowColor,
                          transition: 'background-color 0.3s',
                          ...provider.draggableProps.style,
                        }}
                      >
                        {props.showDragIcon ? (
                          <td
                            {...provider.dragHandleProps}
                            style={{
                              cursor: 'grab',
                              textAlign: 'center',
                              fontWeight: 'bold',
                              color: '#616161',
                              padding: '16px',
                              borderBottom: '1px solid #e0e0e0',
                            }}
                          >
                            ≡
                          </td>
                        ):<td
                            
                            style={{
                              cursor: 'grab',
                              textAlign: 'center',
                              fontWeight: 'bold',
                              color: '#616161',
                              padding: '16px',
                              borderBottom: '1px solid #e0e0e0',
                            }}
                          >
                          
                          </td>}
                        {/* <td
                          {...provider.dragHandleProps}
                          style={{
                            cursor: 'grab',
                            textAlign: 'center',
                            fontWeight: 'bold',
                            color: '#616161',
                            padding: '16px',
                            borderBottom: '1px solid #e0e0e0',
                          }}
                        >
                          ≡
                        </td> */}
                        <td
                          style={{
                            padding: '16px',
                            borderBottom: '1px solid #e0e0e0',
                            textAlign: 'left',
                            fontWeight:700
                          }}
                        >
                          {user.reqId}
                        </td>
                        <td
                          style={{
                            padding: '16px',
                            borderBottom: '1px solid #e0e0e0',
                            textAlign: 'left',
                          }}
                        >
                          {user.module}
                        </td>
                        <td
                          style={{
                            padding: '16px',
                            borderBottom: '1px solid #e0e0e0',
                            textAlign: 'left',
                          }}
                        >
                          {user.reqType}
                        </td>
                        <td
                          style={{
                            padding: '16px',
                            borderBottom: '1px solid #e0e0e0',
                            textAlign: 'left',
                          }}
                        >
                          {user.scheduledBy}
                        </td>
                        <td
                          style={{
                            padding: '16px',
                            borderBottom: '1px solid #e0e0e0',
                            textAlign: 'left',
                          }}
                        >
                          {moment(user.scheduledOn).format(
                            appSettings?.dateFormat
                          )}
                        </td>
                        <td
                          style={{
                            padding: '16px',
                            borderBottom: '1px solid #e0e0e0',
                            textAlign: 'left',
                          }}
                        >
                          {user.totalObjects}
                        </td>
                          <td
                          style={{
                            padding: '16px',
                            borderBottom: '1px solid #e0e0e0',
                            textAlign: 'left',
                          }}
                        >
                          {user.pendingObjects}
                        </td>
                        {
                          props?.selectionType==="SAPScheduler" ?
                          <td
                          style={{
                            padding: '16px',
                            borderBottom: '1px solid #e0e0e0',
                            textAlign: 'left',
                          }}
                        >
                          {user.objectSuccessCount}
                        </td>
                        :""
                        }
                        
                        {
                          props?.selectionType==="SAPScheduler" ?
                          <td
                          style={{
                            padding: '16px',
                            borderBottom: '1px solid #e0e0e0',
                            textAlign: 'left',
                          }}
                        >
                          {user.objectFailureCount}
                        </td>
                        :""
                        }
                       
                        {props?.selectionType==="SAPScheduler" ?
                          <td
                          style={{
                            padding: '16px',
                            borderBottom: '1px solid #e0e0e0',
                            textAlign: 'left',
                          }}
                        >
                          {user.retryCount}
                        </td>
                        :""
                        }
                        
                        <td
                          style={{
                            padding: '16px',
                            borderBottom: '1px solid #e0e0e0',
                            textAlign: 'left',
                          }}
                        >
                          {user.schedulerStatus}
                        </td>
                        {/* {
                          props?.selectionType==="SAPScheduler"? */}
                          <td
                          style={{
                            padding: '16px',
                            borderBottom: '1px solid #e0e0e0',
                            textAlign: 'left',
                          }}
                        >
                           {Object?.entries(taskRowDetails).length === 0 
                           && user?.schedulerStatus === "Pending"
                           ?(
                              <div style={{ width: '8%', display: 'flex', justifyContent: 'center' }}>
                                <Tooltip title={iconClicked?.[user?.id] ? "Resume" : "Pause"}>
                                  <IconButton
                                    aria-label="Toggle Delete Icon"
                                    onClick={() => handleIconClick(user?.id)}
                                  >
                                    {iconClicked?.[user?.id] ? (  
                                      <PlayCircleOutlineIcon
                                        sx={{
                                          // color: "#cc3300",
                                          transition: "color 0.3s ease"
                                        }}
                                      />
                                    ) : (
                                      <PauseCircleOutlineIcon
                                        sx={{
                                          // color: "#2cbc34",
                                          transition: "color 0.3s ease"
                                        }}
                                      />
                                    )}
                                  </IconButton>
                                </Tooltip>
                              </div>
                            )
                            :Object?.entries(taskRowDetails).length === 0 
                           && user?.schedulerStatus === "Paused"
                           ?(
                              <div style={{ width: '8%', display: 'flex', justifyContent: 'center' }}>
                                <Tooltip title={iconClicked?.[user?.id] ? "Pause" : "Resume"}>
                                  <IconButton
                                    aria-label="Toggle Delete Icon"
                                    onClick={() => handleIconClick(user?.id)}
                                  >
                                    {iconClicked?.[user?.id] ? (  
                                      <PauseCircleOutlineIcon
                                        sx={{
                                          // color: "#cc3300",
                                          transition: "color 0.3s ease"
                                        }}
                                      />
                                    ) : (
                                      <PlayCircleOutlineIcon
                                        sx={{
                                          // color: "#2cbc34",
                                          transition: "color 0.3s ease"
                                        }}
                                      />
                                    )}
                                  </IconButton>
                                </Tooltip>
                              </div>
                            )
                            : Object?.entries(taskRowDetails).length === 0 
                           && user?.schedulerStatus !== "Canceled" 
                           && user?.schedulerStatus !== "Completed" 
                           ?(
                              <div style={{ width: '8%', display: 'flex', justifyContent: 'center' }}>
                                <Tooltip title={iconClicked?.[user?.id] ? "Canceled" : "Not Canceled"}>
                                  <IconButton
                                    aria-label="Toggle Delete Icon"
                                    onClick={() => handleIconClick(user?.id)}
                                  >
                                    {iconClicked?.[user?.id] ? (  
                                      <DeleteForeverOutlinedIcon
                                        sx={{
                                          color: "#cc3300",
                                          transition: "color 0.3s ease"
                                        }}
                                      />
                                    ) : (
                                      <AutoDeleteOutlinedIcon
                                        sx={{
                                          color: "#2cbc34",
                                          transition: "color 0.3s ease"
                                        }}
                                      />
                                    )}
                                  </IconButton>
                                </Tooltip>
                              </div>
                            )
                            :""
                            }
                        </td>
                        {/* :""
                        } */}
                     
                      </tr>
                      );
                    }}
                  </Draggable>
                )) :(
                  <tr>
                    <td colSpan={columns.length} style={{ textAlign: 'center', padding: '16px', color: '#616161' }}>
                      No Data
                    </td>
                  </tr>
                )}
                {provider.placeholder}
              </tbody>
            )}
          </Droppable>
            )})}
        </table>
      </DragDropContext>
    // </Paper>
    // </div>
  );
};

export default CustomDataGrid;
