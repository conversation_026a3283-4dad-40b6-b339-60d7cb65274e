import React, { useState, useEffect } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import CloseIcon from "@mui/icons-material/Close";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import Switch from "@mui/material/Switch";
import Card from "@mui/material/Card";
import CardActions from "@mui/material/CardActions";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";
import CardHeader from "@mui/material/CardHeader";
import Avatar from "@mui/material/Avatar";
import Toolbar from "@mui/material/Toolbar";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import AppBar from "@mui/material/AppBar";
import { Editor } from "react-draft-wysiwyg";
import { EditorState, ContentState } from "draft-js";
import "react-draft-wysiwyg/dist/react-draft-wysiwyg.css";
import draftToHtml from "draftjs-to-html";
import htmlToDraft from "html-to-draftjs";
import { doAjax, doCrudApi } from "../utility/serviceRequest";
import { useDispatch, useSelector } from "react-redux";
import { Provider } from "react-redux";
import { setAPIBaseUrl, setConfigs, setRefreshTemplate, setToken } from "../redux/reducers/userReducer";
import store from "../redux/store";
import cherrywork from "./Cherrywork.svg";
import logo from "./Logo.png";
import Tooltip from "@mui/material/Tooltip";
import Tab from "@mui/material/Tab";
import TabContext from "@mui/lab/TabContext";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import Confirmation from "./ConfirmationDialog";
import ClickAwayListener from "@mui/material/ClickAwayListener";
import { Backdrop, CircularProgress, Drawer, Grid, IconButton, SwipeableDrawer, Tabs } from "@mui/material";
import ReusablePromptBox from "../component/PromptBox_Email/ReusablePromptBox";
import { colors } from '../../../constant/colors';

const TemplatePreview = ({ ...props }) => {
  // console.log(props);
  //   console.log(props?.groupList);
  // console.log(props?.userList);
  const userReducer = useSelector((state) => state.userReducer);
  const [isTemplateInformation, setIsTemplateInformation] = useState(false);
  const [isLoader, setLoader] = React.useState(false);
  const dispatch = useDispatch();
  const [editorState, setEditorState] = React.useState(() => EditorState.createEmpty());
  const [defaultEditorValue, setDefaultEditorValue] = React.useState(() => EditorState.createEmpty());
  const [value, setValue] = React.useState("1");
  const [userDetails, setUserDetails] = useState(new Map());
  const [groupDetails, setGroupDetails] = useState(new Map());
  const [showConfirmation, setShowConfirmation] = React.useState(false);
  const [confirmationMessage, setConfirmationMessage] = React.useState("");
  const [buttonAction, setButtonAction] = React.useState("Cancel");

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const closeConfirmationDialog = (evt) => {
    if (evt === "delete") {

      onDeleteTemplate(props?.data);
    }
    // setSelectedRow(null);
    // setCreationType("new");
    setShowConfirmation(false);
  };

  const onDeleteTemplate = (row) => {
    setLoader(true);
    setPromptBoxState(prev => ({ ...prev, open: false }))
    doAjax(
      "/WorkUtilsServices/v1/mail-definition/" + row.emailDefinitionId,
      "delete",
      null,
      function (oData) {

        promptAction_Functions.handleOpenPromptBox("SUCCESS", {
          message: `Email template deleted succesfully`,
          redirectOnClose: false,
        });
        setLoader(false)
        // dispatch(setRefreshTemplate());
      },
      function (error) {
        setLoader(false)
        promptAction_Functions.handleOpenPromptBox("ERROR", {
          title: "Error",
          message: "Failed to create template",
          severity: "danger",
          cancelButton: true,
          okButton: true,
          okButtonText: "Ok",
        });
        console.log("error");
      }
    );
  };

  const handleToggleChange = (e) => {
    setIsTemplateInformation(e.target.checked);
  };
  // const onContentStateChange = (editorState) => {
  //   let value = draftToHtml(editorState);
  //   setTemplateData({ ...templateData, body: value });
  // };
  const setEditorValue = (content) => {
    const blocksFromHtml = htmlToDraft(content);
    const { contentBlocks, entityMap } = blocksFromHtml;
    const contentState = ContentState.createFromBlockArray(contentBlocks, entityMap);
    const editorState = EditorState.createWithContent(contentState);

    setDefaultEditorValue(editorState);
    setEditorState(editorState);
  };
  const getFileBase64 = (file, callback) => {
    var reader = new FileReader();
    reader.readAsDataURL(file);
    // Since FileReader is asynchronous,
    // we need to pass data back.
    reader.onload = () => callback(reader.result);
    // TODO: catch an error
    reader.onerror = (error) => { };
  };
  const imageUploadCallback = (file) => new Promise((resolve, reject) => getFileBase64(file, (data) => resolve({ data: { link: data } })));

  React.useEffect(() => {
    if (userReducer.applicationName !== "") {
      setEditorValue(props?.data?.content);
    }
  }, [props?.data, userReducer]);

  useEffect(() => {
    if (userReducer.applicationName !== "") {
      // getGroups();
      let transformUserData = props?.userList.map((ele) => {
        userDetails.set(ele.emailId, ele.userName);
        return { code: ele.userName, description: ele.emailId };
      });
      setUserDetails(new Map(userDetails));
      // setUser(transformUserData);

      let transformGroupData = props?.allGroups.map((ele) => {
        groupDetails.set(ele.id, ele.name);
        return { code: ele.name, description: ele.id };
      });

      setGroupDetails(new Map(groupDetails));
      // setGroup(transformGroupData);

      // console.log(userDetails);
      // console.log(user);
    }
  }, [userReducer,props?.allGroups]);


  //<-- Functions and variables for ReusablePromptBox *promptAction_Functions -->
  const [promptBoxState, setPromptBoxState] = useState({
    open: false,
    type: "",
    redirectOnClose: true,
    message: "",
    title: "",
    severity: "",
  });
  const [promptBoxScenario, setPromptBoxScenario] = useState("");

  const promptAction_Functions = {
    handleClosePromptBox: () => {
      setPromptBoxState((prev) => ({
        open: false,
        type: "",
        redirectOnClose: true,
        message: "",
        title: "",
        severity: "",
      }));
      setPromptBoxScenario("");
    },
    handleOpenPromptBox: (ref, data = {}) => {
      // SUCCESS,FAILURE,WARNING,QUANTITYERROR,
      let initialData = {
        open: true,
        title: "",
        message: "",
        okButton: true,
        cancelButton: true,
        okText: "Ok",
        cancelText: "Cancel",
        type: "dialog",
      };
      if (ref === "SUCCESS") {
        initialData.type = "snackbar";
      }
      setPromptBoxScenario(ref);
      setPromptBoxState({
        ...initialData,
        ...data,
      });
    },
    handleCloseAndRedirect: () => {
      promptAction_Functions.handleClosePromptBox();
      navigate("");
    },
    getCancelFunction: () => {
      switch (promptBoxScenario) {
        default:
          return () => {
            promptAction_Functions.handleClosePromptBox();
          };
      }
    },
    getCloseFunction: () => {
      switch (promptBoxScenario) {
        case "COMMENTERROR":
        default:
          return (value) => {
            promptAction_Functions.handleClosePromptBox();
          };
      }
    },
    getOkFunction: () => {

      switch (promptBoxScenario) {

        case "DELETE_TEMPLATE":
          return () => closeConfirmationDialog("delete")

        default:
          return () => promptAction_Functions.handleClosePromptBox();

      }
    },
    getCloseAndRedirectFunction: () => {
      if (!promptBoxState.redirectOnClose) {
        return promptAction_Functions.handleClosePromptBox;
      }
      return promptAction_Functions.handleCloseAndRedirect;
    },
  };
  
    
  return (
    <div>
      <Backdrop className="backdrop" sx={{ zIndex: '9' }} open={isLoader}>
        <CircularProgress color="primary" />
      </Backdrop>
      
      <ReusablePromptBox
        type={promptBoxState.type}
        promptState={promptBoxState.open}
        setPromptState={promptAction_Functions.handleClosePromptBox}
        onCloseAction={promptAction_Functions.getCloseFunction()}
        promptMessage={promptBoxState.message}
        dialogSeverity={promptBoxState.severity}
        dialogTitleText={promptBoxState.title}
        handleCancelButtonAction={promptAction_Functions.getCancelFunction()}
        cancelButtonText={promptBoxState.cancelText}
        showCancelButton={promptBoxState.cancelButton}
        handleSnackBarPromptClose={promptAction_Functions.getCloseAndRedirectFunction()}
        handleOkButtonAction={promptAction_Functions.getOkFunction()}
        okButtonText={promptBoxState.okButtonText}
        showOkButton={promptBoxState.okButton}
      />

      <SwipeableDrawer 
        anchor="right" 
        onClose={props?.onClose} 
        open={props?.open}
        PaperProps={{
          elevation: 4,
          sx: {
            width: "45vw",
            minWidth: '600px',
            bgcolor: colors.background.default,
            borderRadius: '12px 0 0 12px',
            padding: '24px'
          },
        }}
      >
        {/* Header */}
        <Box sx={{ 
          borderBottom: `1px solid ${colors.border.main}`,
          pb: 2,
          mb: 3,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <Tabs 
            value={value} 
            onChange={handleChange}
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontSize: '14px',
                fontWeight: 500,
                minWidth: 'unset',
                px: 3,
                color: colors.text.secondary,
                '&.Mui-selected': {
                  color: colors.primary.main,
                  fontWeight: 600
                }
              },
              '& .MuiTabs-indicator': {
                backgroundColor: colors.primary.main,
                height: '3px',
                borderRadius: '2px'
              }
            }}
          >
            <Tab label="Template Information" value="1" />
            <Tab label="Preview Information" value="2" />
          </Tabs>

          <Stack direction="row" spacing={1.5}>
            <IconButton 
              onClick={() => {
                props?.setOpenCreateTemplate(true);
                props?.setSelectedRow(props?.data);
                props?.setCreationType("edit");
                props?.setScenario("EDIT");
                props?.setIsEditing(props?.index);
                props?.setOpenTemplateDialog(false);
              }}
              sx={{ 
                color: colors.primary.main,
                '&:hover': {
                  backgroundColor: colors.primary.lighter
                }
              }}
            >
              <EditOutlinedIcon fontSize="small" />
            </IconButton>

            {userReducer.feature.EMAIL_CONFIG_DELETE === "True" && (
              <IconButton
                onClick={() => {
                  props?.setSelectedRow(props?.data);
                  promptAction_Functions.handleOpenPromptBox("DELETE_TEMPLATE", {
                    title: "Confirm Delete",
                    message: "Do you want to delete this record?",
                    severity: "warning",
                    cancelButton: true,
                    okButton: true,
                    okButtonText: "Ok",
                  });
                }}
                sx={{ 
                  color: colors.error.main,
                  '&:hover': {
                    backgroundColor: colors.error.lighter
                  }
                }}
              >
                <DeleteOutlinedIcon fontSize="small" />
              </IconButton>
            )}
          </Stack>
        </Box>

        {/* Template Information Tab */}
        {value === "1" && (
          <Stack 
            spacing={3} 
            sx={{ 
              px: 2,
              height: '100%',
              overflow: 'auto'
            }}
          >
            {[
              { label: props?.headers[3] || "Template Name", value: props?.data?.name },
              { label: "Identifier", value: props?.data.identifierDesc },
              { label: props?.headers[1] || "Module", value: props?.data.entityDesc },
              { label: props?.headers[2] || "Event", value: props?.data.processDesc },
              { 
                label: "Recipients", 
                value: props?.cardData.toParticipantType === "GROUP" 
                  ? groupDetails.get(parseInt(props?.cardData.toParticipant))
                  : props?.cardData.toParticipantType === "USER"
                  ? userDetails.get(props?.cardData.toParticipant)
                  : props?.cardData.toParticipant
              },
              {
                label: "CC Recipients",
                value: props?.cardData.ccParticipantType === "GROUP"
                  ? groupDetails.get(parseInt(props?.cardData.ccParticipant))
                  : props?.cardData.toParticipantType === "USER"
                  ? props?.cardData.ccParticipant
                  : props?.cardData.ccParticipant
              }
            ].map((item, index) => (
              <Paper
                key={index}
                elevation={0}
                sx={{
                  p: 2,
                  borderRadius: '8px',
                  backgroundColor: colors.background.paper,
                  border: `1px solid ${colors.border.light}`
                }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    color: colors.text.primary,
                    fontWeight: 600,
                    display: 'block',
                    mb: 1,
                    fontSize: '0.95rem'
                  }}
                >
                  {item.label}
                </Typography>
                <Tooltip title={item.value || 'Not Available'}>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: 500,
                      color: colors.text.secondary,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {item.value || 'Not Available'}
                  </Typography>
                </Tooltip>
              </Paper>
            ))}
          </Stack>
        )}

        {/* Preview Information Tab */}
        {value === "2" && (
          <Paper 
            elevation={0}
            sx={{
              width: "100%",
              flexGrow: 1,
              bgcolor: colors.background.paper,
              overflow: 'auto',
              border: `1px solid ${colors.border.light}`,
              borderRadius: '8px'
            }}
          >
            <Box sx={{ 
              borderBottom: `1px solid ${colors.border.light}`,
              p: 2,
              bgcolor: colors.background.neutral
            }}>
              <Typography
                variant="subtitle2"
                sx={{
                  color: colors.text.primary,
                  fontWeight: 600
                }}
              >
                Email Preview
              </Typography>
            </Box>
            <Stack 
              sx={{ 
                height: '100%',
                minHeight: '65vh',
                p: 3
              }}
            >
              <Editor
                editorState={editorState}
                wrapperClassName="Editor"
                editorClassName="Editor"
                defaultEditorState={defaultEditorValue}
                onEditorStateChange={setEditorState}
                toolbar={{
                  inline: { inDropdown: false },
                  list: { inDropdown: false },
                  textAlign: { inDropdown: false },
                  link: { inDropdown: false },
                  history: { inDropdown: false },
                  image: {
                    uploadCallback: imageUploadCallback,
                    previewImage: true,
                    alignmentEnabled: true,
                  },
                }}
                toolbarHidden
                readOnly
              />
            </Stack>
          </Paper>
        )}
      </SwipeableDrawer>
      <Confirmation 
        message={confirmationMessage} 
        creationType={buttonAction} 
        open={showConfirmation} 
        onClose={(evt) => closeConfirmationDialog(evt)} 
      />
    </div>
  );
};

export default TemplatePreview;
