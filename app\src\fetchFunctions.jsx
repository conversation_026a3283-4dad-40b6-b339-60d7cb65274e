import axios from "axios";
import { masterDataUpdate } from "./app/masterDataSlice";
import Store from "./app/store";
import { doAjax, promiseAjax } from "./components/common/fetchService";
import {
  destination_SLA_Mgmt,
} from "./destinationVariables";

export const fetchAllDropdownMstrData = () => {
  let hSuccess = (e) => {
    console.log(e,"ddd")
    Store.dispatch(masterDataUpdate({ dropDown: e }));
  };
  doAjax(
    `/${destination_SLA_Mgmt}/sla/getSLADropdownValues`,
    "get",
    hSuccess
  );
};
