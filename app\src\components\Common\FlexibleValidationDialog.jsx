import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';

const FlexibleValidationDialog = ({
  open,
  onClose,
  missingFields = [],
  customMessage = null,
  title = null,
  buttonText = "Close",
  t = (text) => text, // Default translation function
}) => {
  // Determine if we're showing missing fields or custom message
  const showMissingFields = missingFields.length > 0 && !customMessage;
  
  // Default title based on mode
  const dialogTitle = title || (showMissingFields ? t("Missing Mandatory Fields") : t("Validation Error"));

  return (
    <Dialog
      open={open}
      onClose={onClose}
      aria-labelledby="flexible-validation-dialog-title"
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle
        id="flexible-validation-dialog-title"
        sx={{
          backgroundColor: "#fff3e0",
          color: "#e65100",
          display: "flex",
          alignItems: "center",
          gap: 1,
          fontWeight: "bold",
        }}
      >
        <WarningAmberIcon fontSize="medium" />
        {dialogTitle}
      </DialogTitle>

      <DialogContent sx={{ pt: 2 }}>
        {showMissingFields ? (
          // Missing fields mode
          <>
            <Typography variant="body1" gutterBottom>
              {t("Please complete the following mandatory fields:")}
            </Typography>
            <List dense>
              {missingFields.map((field, index) => (
                <ListItem key={index} disablePadding>
                  <ListItemIcon sx={{ minWidth: 30 }}>
                    <WarningAmberIcon fontSize="small" color="warning" />
                  </ListItemIcon>
                  <ListItemText primary={field} />
                </ListItem>
              ))}
            </List>
          </>
        ) : (
          // Custom message mode
          <Typography variant="body1" gutterBottom>
            {customMessage}
          </Typography>
        )}
      </DialogContent>

      <DialogActions sx={{ pr: 3, pb: 2 }}>
        <Button
          onClick={onClose}
          variant="contained"
          color="warning"
          sx={{ textTransform: "none", fontWeight: 500 }}
        >
          {t(buttonText)}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default FlexibleValidationDialog;