import React from 'react';
import { Box, Tooltip, Typography, IconButton } from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
const TruncatedText = ({ 
  text, 
  maxChars = 50, 
  sx = {}, 
  variant = "body2", 
  color = "inherit",
  showIcon = true
}) => {
  if (!text) return null;
  
  const isTruncated = text.length > maxChars;
  const displayText = isTruncated ? `${text.substring(0, maxChars)}...` : text;
  
  return (
    <Box sx={{ display: 'flex', alignItems: 'flex-start', ...sx }}>
      <Typography 
        variant={variant} 
        color={color}
        sx={{ 
          wordBreak: 'break-all',
          overflowWrap: 'break-word'
        }}
      >
        {displayText}
      </Typography>
      
      {isTruncated && showIcon && (
        <Tooltip 
          title={
            <Typography variant="body2">
              {text}
            </Typography>
          }
          arrow
          placement="top"
        >
          <IconButton 
            size="small" 
            sx={{ 
              p: 0, 
              ml: 0.5, 
              color: 'text.secondary',
              '&:hover': { color: 'primary.main' }
            }}
          >
            <InfoOutlinedIcon fontSize="small" sx={{ fontSize: '1rem' }} />
          </IconButton>
        </Tooltip>
      )}
    </Box>
  );
};
export default TruncatedText;