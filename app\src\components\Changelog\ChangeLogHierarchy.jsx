import { useState, useEffect } from "react";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import Modal from "@mui/material/Modal";
import { doAjax } from "../common/fetchService";
import {
  destination_MaterialMgmt,
  destination_ProfitCenter_Mass,
} from "../../destinationVariables";
import moment from "moment";
import ChangeCircleOutlinedIcon from "@mui/icons-material/ChangeCircleOutlined";
import { useDispatch, useSelector } from "react-redux";
import { Grid, Stack, IconButton, Tooltip, Tabs, Tab } from "@mui/material";
import ReusableDataTable from "../Common/ReusableTable";
import CloseIcon from "@mui/icons-material/Close";
import { v4 as uuidv4 } from "uuid";
import ReusableIcon from "../Common/ReusableIcon";
import { iconButton_SpacingSmall } from "../Common/commonStyles";
import { CHANGE_LOG_TEMPS } from "../../constant/changeLogTemplates.js";
import { saveExcel, saveExcelMultiSheets } from "../../functions";
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "@hooks/useLogger";
import { API_CODE, ERROR_MESSAGES, LOADING_MESSAGE } from "@constant/enum";
import {
  convertDate,
  extractDataBaedOnTemplateName,
  formatDateValue,
  getObjectValue,
  getSegregatedPart,
  mergeArrays,
} from "@helper/helper";
import {
  CHANGE_TEMPLATES_FIELD_IDENTIFICATION,
  TEMPLATE_NAME_MANIPULATION,
} from "@constant/changeTemplates";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { updateToChangeLog } from "../../app/hierarchyDataSlice.jsx";
import { useLocation } from "react-router-dom";
import { filterNavigation } from "@helper/helper";

const ChangeLogHierarchy = ({ open, closeModal, requestId, requestType, module }) => {
  const { customError } = useLogger();
    const location = useLocation();
    const dispatch = useDispatch();
  const [loading, setloading] = useState(false);
  const [apiResponse, setApiResponse] = useState(null);
  const payloadData = useSelector((state) => state.payload.payloadData);
  const changeLog = useSelector((state) => state.hierarchyData.changeLog || []);
  const templateKey = payloadData?.TemplateName;
   const queryParams = new URLSearchParams(location.search);
  const RequestId = queryParams.get("RequestId");
    const { destination } = filterNavigation(module);
  const columns = [
    {
      field: "type",
      headerName: "Operation Type",
      flex: 1,
      editable: false,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "description",
      headerName: "Operation Description",
      flex: 1.7,
      editable: false,
      headerAlign: "center",
    },
    {
      field: "updatedBy",
      headerName: "Updated By",
      flex: 1.2,
      editable: false,
      headerAlign: "center",
      align: "center",
    },
    {
      field: "updatedOn",
      headerName: "Updated On",
      flex: 1.6,
      editable: false,
      headerAlign: "center",
      align: "center",
      renderCell: (params) => {
        return formatDateValue(params.value);
      },
    },
  ];

  const [tabData, setTabData] = useState(() => {
    const tabConfig = CHANGE_LOG_TEMPS[templateKey] || {};
    return Object.keys(tabConfig).map((tabName) => ({
      label: tabName,
      columns: tabConfig[tabName],
      rows: [],
    }));
  });

  const style = {
    position: "absolute",
    top: "50%",
    left: "52%",
    transform: "translate(-50%, -50%)",
    width: "80%",
    height: "auto",
    bgcolor: "#fff",
    boxShadow: 4,
    p: 2,
    borderRadius: "20px",
  };

  const onClose = () => {
    closeModal(false);
  };

  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 15);

  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      let excelColumns = [];

      columns?.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      const presentDate = new Date();

      saveExcel({
        fileName: `Change Log Sheet`,
        columns: excelColumns,
        rows: changeLog,
      });

      // const sheetsData = tabData.map((tab) => {
      // 	const columns = tab.columns.fieldName.map((field, idx) => ({
      // 		header: tab.columns.headerName[idx],
      // 		key: field
      // 	}));
      // 	return {
      // 		sheetName: tab.label,
      // 		fileName: `Changelog Data-${moment(presentDate).format("DD-MMM-YYYY")}`,
      // 		columns: columns,
      // 		rows: tab.rows
      // 	};
      // });
      // saveExcelMultiSheets(sheetsData);
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };

    const changeLogFetch = (requestId) => {

      const url = `/${destination}/node/getTreeModificationHistory?requestId=${requestId}`;
      return new Promise((resolve, reject) => {
        const hSuccess = (data) => {
          if (
            data?.statusCode === API_CODE.STATUS_200 &&
            data?.body?.Records?.length > 0
          ) {
            resolve( data?.body?.Records);
          } else {
            resolve([]);
          }
        };
  
        const hError = (error) => {
          customError(error);
          reject(error);
        };
  
        doAjax(url, "get", hSuccess, hError);
      });
    };
  

  useEffect(()=>{
     const fetchChangeLogData = async () => {
      if (RequestId) {
        try {
          const result = await changeLogFetch(RequestId);
          
          dispatch(updateToChangeLog(result || []));
        } catch (error) {
          customError("Error fetching changelog data:", error);
        }
      }
    };
if(!changeLog.length)
    fetchChangeLogData();
  },[RequestId, dispatch])

  return (
    <>
      {loading && (
        <ReusableBackDrop
          blurLoading={loading}
          loaderMessage={LOADING_MESSAGE.CHANGELOG_LOADING}
        />
      )}
      <Modal
        open={open}
        onClose={onClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box sx={style}>
          <Stack>
            <Grid
              item
              md={12}
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <ChangeCircleOutlinedIcon
                  sx={{
                    color: "black",
                    fontSize: "20px",
                    "&:hover": {
                      transform: "rotate(360deg)",
                      transition: "0.9s",
                    },
                    textAlign: "center",
                    marginTop: "4px",
                  }}
                />
                <Typography
                  id="modal-modal-title"
                  variant="subtitle1"
                  fontSize={"16px"}
                  fontWeight={"bold"}
                  sx={{ color: "black" }}
                >
                  Change Log
                </Typography>
              </Box>

              <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <Tooltip title="Export Table">
                  <IconButton
                    sx={iconButton_SpacingSmall}
                    onClick={functions_ExportAsExcel.convertJsonToExcel}
                  >
                    <ReusableIcon iconName={"IosShare"} />
                  </IconButton>
                </Tooltip>

                <IconButton sx={{ padding: "0 0 0 5px" }} onClick={onClose}>
                  <CloseIcon />
                </IconButton>
              </Box>
            </Grid>
          </Stack>

          <Grid item sx={{ position: "relative", mt: 1 }}>
            <Stack>
              <ReusableDataTable
                rows={changeLog}
                columns={columns}
                getRowIdValue={"id"}
                autoHeight
                scrollbarSize={10}
                sx={{
                  "& .MuiDataGrid-row:hover": {
                    backgroundColor: "#EAE9FF40",
                  },
                  backgroundColor: "#fff",
                }}
              />
            </Stack>
          </Grid>
        </Box>
      </Modal>
    </>
  );
};

export default ChangeLogHierarchy;
