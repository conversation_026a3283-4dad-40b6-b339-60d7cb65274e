import { useState, useCallback } from "react";
import { useDispatch } from "react-redux";
import { ERROR_MESSAGES, API_CODE, LOCAL_STORAGE_KEYS, REQUEST_TYPE } from "@constant/enum";
import { doAjax } from "@components/Common/fetchService";
import { destination_BOM } from "../../../destinationVariables";
import useLogger from "@hooks/useLogger";
import { transformBOMDisplayResponseToRedux } from "../../../functions";
import { setBOMpayloadData, setBomRows, setRequestHeaderID } from "../bomSlice";
import { getLocalStorage } from "@helper/helper";
import { END_POINTS } from "@constant/apiEndPoints";

const useDisplayBomData = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { customError } = useLogger();

  const getDisplayBomData = useCallback(
    async (requestId, RequestType, reqBench, taskData, rowData) => {
      return new Promise((resolve, reject) => {
        setLoading(true);
        setError(null);
        const idToUse = requestId;
        const savedTask = getLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK, true, {});
        const effectiveRequestType = RequestType || taskData?.ATTRIBUTE_2 || savedTask?.ATTRIBUTE_2;
        let payload = reqBench
          ? {
              massCreationId: !rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.CREATE || effectiveRequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) ? idToUse : "") : "",
              massChildCreationId: rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.CREATE || effectiveRequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) ? idToUse : "") : "",
              massChangeId: !rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) ? idToUse : "") : "",
              massChildChangeId: rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) ? idToUse : "") : "",
              page: 0,
              size: 10,
              sort: "",
          }
          : {
              massCreationId: "",
              massChangeId: "",
              massChildCreationId: effectiveRequestType === REQUEST_TYPE.CREATE || effectiveRequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ? idToUse : "",
              massChildChangeId: effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD ? idToUse : "",
              page: 0,
              size: 10,
              sort: "",
            };
        const hSuccess = (data) => {
          setLoading(false);
          if (data?.statusCode === API_CODE.STATUS_200) {
            const { bomPayload, bomRows, tabFieldValues } = transformBOMDisplayResponseToRedux(data);
            Object.entries(bomPayload).forEach(([key, value]) => {
              dispatch(setBOMpayloadData({ keyName: key, data: value }));
            });
            dispatch(setBomRows(bomRows));
            Object.entries(tabFieldValues).forEach(([rowId, tabData]) => {
              Object.entries(tabData).forEach(([viewName, rows]) => {
                dispatch({
                  type: "bom/setTabRows",
                  payload: { rowId, viewName, rows },
                });
              });
            });
            resolve(data);
          } else {
            setError(data?.message || ERROR_MESSAGES.ERROR_GET_DISPLAY_DATA);
            reject(data?.message || ERROR_MESSAGES.ERROR_GET_DISPLAY_DATA);
          }
        };
        const hError = (error) => {
          customError(ERROR_MESSAGES.ERROR_FETCHING_DATA);
          setError(error);
          setLoading(false);
          reject(error);
        };
        doAjax(
          `/${destination_BOM}/${END_POINTS.CHG_DISPLAY_REQUESTOR.DISPLAY_DTO}`,
          "post",
          hSuccess,
          hError,
          payload
        );
      });
    },
    [dispatch]
  );

  return {
    getDisplayBomData,
    loading,
    error,
    clearError: () => setError(null),
  };
};

export default useDisplayBomData;
