import {
  Backdrop,
  Button,
  CircularProgress,
  Divider,
  FormControl,
  Grid,
  ListSubheader,
  MenuItem,
  Select,
  Stack,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import { Box } from "@mui/system";
import { useSelector, useDispatch } from "react-redux";
import { destination_Admin, destination_SLA_Mgmt } from "../../../destinationVariables";
import moment from "moment/moment";
import { useNavigate } from "react-router-dom";
import localConfigServer from "../../../data/localConfigServer.json";
import {
  outermostContainer_Information,
  font_Small,
  outermostContainer,
  outerContainer_Information,
} from "../../common/commonStyles";
import { doAjax } from "../../common/fetchService";
import { appSettingsUpdate } from "../../../app/appSettingsSlice";
import ReusableIcon from "../../common/ReusableIcon";
import { formValidator } from "../../../functions";
import ReusablePromptBox from "../../common/ReusablePromptBox/ReusablePromptBox";
import { button_Primary } from "../../common/commonStyles";
const ApplicationConfiguration = () => {
  const [loading, setLoading] = useState(false);
  const [extension, setextension] = useState(true);
  const appSettings = useSelector((state) => state.appSettings);
  let userData = useSelector((state) => state.userManagement.userData);
  const [roleList, setRoleList] = useState([]);
  let masterData = useSelector((state) => state.masterData);
  const dropDownData = masterData?.dropDown?.["Application Settings"];

  const [SettingsObj, setSettingsObj] = useState({
    dateFormat: appSettings.dateFormat,
    range: appSettings.range,
    timeFormat: appSettings.timeFormat,
    defaultModule: appSettings.defaultModule ?? "/",
    roleId: appSettings.roleId ?? "",
    // timeZone: appSettings.timeZone,
  });
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]);
  const dispatch = useDispatch();
  const handleSelect = (e) => {
    setSettingsObj({ ...SettingsObj, [e.target.name]: e.target.value });
  };
  const handleFormat = (e) => {
    setSettingsObj({ ...SettingsObj, dateFormat: e.target.value });
  };
  const handleRole = (e) => {
    setSettingsObj({ ...SettingsObj, roleId: e.target.value });
  };
  const handleRange = (e) => {
    setSettingsObj({ ...SettingsObj, range: e.target.value });
  };
  const handleLandingPage = (e) => {
    setSettingsObj({ ...SettingsObj, defaultModule: e.target.value });
  };
  const handleClear = () => {
    setSettingsObj({
      ...SettingsObj,
      dateFormat: "",
      range: "",
      timeFormat: "",
      // timeZone: "",
    });
    setFormValidationErrorItems([]);
  };
  const fetchAppSettings = () => {
    let hSuccess = (data) => {
      if (!data) {
        dispatch(appSettingsUpdate());
      } else {
        if (
          data.dateFormat ||
          // data.timeZone ||
          data.dateRangeValue ||
          data.timeFormat ||
          data.landingPage
        ) {
          const appSettingsData = {
            dateFormat: data.dateFormat,
            // timeZone: "Asia/Kolkata",
            range: data.dateRangeValue,
            timeFormat: data.timeFormat,
            defaultModule: data.landingPage,
          };
          dispatch(appSettingsUpdate(appSettingsData));
          setSettingsObj({
            ...SettingsObj,
            dateFormat: data.dateFormat ?? "DD MMM YYYY",
            // timeZone: "Asia/Kolkata",
            range: data.dateRangeValue ?? 7,
            timeFormat: data.timeFormat ?? "hh:mm A",
            defaultModule: data.landingPage ?? "/",
            mode: data.mode ?? "light",
          });
        }
      }
    };
    let hError = () => {};
    doAjax(
      `/${destination_SLA_Mgmt}/application/getByEmail/${userData?.emailId}`,
      "get",
      hSuccess,
      hError
    );
  };

  const navigate = useNavigate();

  const fetchUserRoles = () => {
    const hSuccess = (data) => {
      setRoleList(data);
    };
    const hError = (err) => {
      console.error(err);
    };
    doAjax(
      `/${destination_SLA_Mgmt}/application/getRoles`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    fetchAppSettings();
    fetchUserRoles();
  }, []);

  useEffect(() => {
    if (SettingsObj?.roleId) {
      console.log(SettingsObj?.roleId);
      let hSuccess = (data) => {
        if (data) {
          if (
            data.dateFormat ||
            // data.timeZone ||
            data.dateRangeValue ||
            data.timeFormat ||
            data.landingPage
          ) {
            const appSettingsData = {
              dateFormat: data.dateFormat,
              // timeZone: "Asia/Kolkata",
              range: data.dateRangeValue,
              timeFormat: data.timeFormat,
              defaultModule: data.landingPage,
            };
            dispatch(appSettingsUpdate(appSettingsData));
            setSettingsObj({
              ...SettingsObj,
              dateFormat: data.dateFormat ?? "DD MMM YYYY",
              // timeZone: "Asia/Kolkata",
              range: data.dateRangeValue ?? 7,
              timeFormat: data.timeFormat ?? "hh:mm A",
              defaultModule: data.landingPage ?? "/",
              mode: data.mode ?? "light",
            });
          }
        }
      };
      let hError = (err) => {
        console.error(err);
      };
      doAjax(
        `/${destination_SLA_Mgmt}/application/getByRoleId/${SettingsObj?.roleId}`,
        "get",
        hSuccess,
        hError
      );
    }
  }, [SettingsObj?.roleId]);
  const toggleAcordion = () => {
    setextension((prev) => !prev);
  };

  //<-- Functions and variables for ReusablePromptBox *promptAction_Functions -->
  const [promptBoxState, setPromptBoxState] = useState({
    open: false,
    type: "",
    redirectOnClose: true,
    message: "",
    title: "",
    severity: "",
  });
  const [promptBoxScenario, setPromptBoxScenario] = useState("");
  const defaultDateRange = [
    "Last Week",
    "Last Month",
    "Current Month",
    "Current Quarter",
    "Year To Date",
  ];

  const promptAction_Functions = {
    handleClosePromptBox: () => {
      setPromptBoxState((prev) => ({
        open: false,
        type: "",
        redirectOnClose: false,
        message: "",
        title: "",
        severity: "",
      }));
      setPromptBoxScenario("");
      setFormValidationErrorItems([]);
    },
    handleOpenPromptBox: (ref, data = {}) => {
      // SUCCESS,FAILURE
      let initialData = {
        open: true,
        title: "",
        message: "",
        okButton: true,
        cancelButton: true,
        okButtonText: "Ok",
        cancelText: "Cancel",
        type: "dialog",
      };
      if (ref === "SUCCESS") {
        initialData.type = "toast";
        initialData.dialogSeverity = "success";
      }
      setPromptBoxScenario(ref);
      setPromptBoxState({
        ...initialData,
        ...data,
      });
    },
    handleCloseAndRedirect: () => {
      promptAction_Functions.handleClosePromptBox();
      props?.handleClose();
    },
    getCancelFunction: () => {
      switch (promptBoxScenario) {
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getCloseFunction: () => {
      switch (promptBoxScenario) {
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getCloseAndRedirectFunction: () => {
      if (!promptBoxState.redirectOnClose) {
        return promptAction_Functions.handleClosePromptBox;
      }
      return promptAction_Functions.handleCloseAndRedirect;
    },
  };
  const entitiesAndActivities = useSelector(
    (state) => state.userManagement.entitiesAndActivities
  );

  const handleSave = () => {
    let payload = {
      dateRangeValue: SettingsObj.range,
      dateFormat: SettingsObj.dateFormat,
      email: SettingsObj.roleId ? "" : userData?.emailId,
      // timeZone: "Asia/Kolkata",
      timeFormat: SettingsObj.timeFormat,
      landingPage: SettingsObj.defaultModule,
      roleId: SettingsObj.roleId,
    };
    let hSuccess = (data) => {
      const Data = {
        dateFormat: SettingsObj.dateFormat,
        range: SettingsObj.range,
        timeFormat: SettingsObj.timeFormat,
        // timeZone: "Asia/Kolkata",
        landingPage: SettingsObj.defaultModule,
        mode: SettingsObj.mode ?? "light",
      };
      dispatch(appSettingsUpdate(Data));
      if (data.status === "Success") {
        promptAction_Functions.handleOpenPromptBox("SUCCESS", {
          message: `Application Settings Updated successfully`,
          redirectOnClose: true,
        });
      } else {
        promptAction_Functions.handleOpenPromptBox("ERROR", {
          title: "Failed",
          message: `Application Settings Update Failed`,
          severity: "danger",
          cancelButton: false,
        });
      }
    };
    let hError = () => {
      promptAction_Functions.handleOpenPromptBox("ERROR", {
        title: "Failed",
        message: `Application Settings Update Failed`,
        severity: "danger",
        cancelButton: false,
      });
    };
    doAjax(
      `/${destination_SLA_Mgmt}/application/create`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const [pageAccess, setPageAccess] = useState([]);
  useEffect(() => {
    let tempSideConfig = [];
    let index = 1;
    if ((entitiesAndActivities, localConfigServer)) {
      localConfigServer?.accessItems?.map((config) => {
        if (
          config.isAccessible &&
          config.isSideOption &&
          Object.keys(entitiesAndActivities).includes(config.iwaName)
        ) {
          tempSideConfig.push({ ...config, id: index });
          index = index + 1;
        }
      });
      var tempOptions = renderOptions(tempSideConfig);
      setPageAccess(tempOptions);
    }
  }, [entitiesAndActivities]);

  const renderOptions = (options) => {
    var finalOptions = [];
    options.map((option) => {
      if (option.isSideOption && option.isAccessible) {
        const childOptions = option.childItems.filter(
          (item) => item.isAccessible && item.isSideOption
        );
        finalOptions.push({
          name: option.displayName,
          value: option.routePath,
          icon: option.icon,
          isMenu: false,
        });

        if (childOptions.length > 0) {
          childOptions.map((childOption) => {
            finalOptions.push({
              name: childOption.displayName,
              value: childOption.routePath,
              icon: childOption.icon,
              isMenu: true,
            });
          });
        } else {
          finalOptions.push({
            name: option.displayName,
            value: option.routePath,
            icon: option.icon,
            isMenu: true,
          });
        }
      }
    });
    return finalOptions;
  };
  return (
    <div id="printScreen" style={outermostContainer}>
      <ReusablePromptBox
        type={promptBoxState.type}
        promptState={promptBoxState.open}
        setPromptState={promptAction_Functions.handleClosePromptBox}
        onCloseAction={promptAction_Functions.getCloseFunction()}
        promptMessage={promptBoxState.message}
        dialogSeverity={promptBoxState.severity}
        dialogTitleText={promptBoxState.title}
        handleCancelButtonAction={promptAction_Functions.getCancelFunction()}
        cancelButtonText={promptBoxState.cancelText} //Cancel button display text
        showCancelButton={promptBoxState.cancelButton} //Enable Cancel button
        handleSnackBarPromptClose={promptAction_Functions.getCloseAndRedirectFunction()}
        // handleOkButtonAction={promptAction_Functions.getOkFunction()}
        okButtonText={promptBoxState.okButtonText}
        showOkButton={promptBoxState.okButton}
      />
      <Backdrop sx={{ color: "#fff", zIndex: 100 }} open={loading}>
        <CircularProgress color="primary" />
      </Backdrop>
      <Stack spacing={1}>
        {/* INFORMATION */}
        <Grid container sx={outermostContainer_Information}>
          <Grid item md={5} sx={outerContainer_Information}>
            <Typography variant="h3">
              <strong>Application Configurations</strong>
            </Typography>
            <Typography variant="body2">
              This view displays the settings for configuring the application
            </Typography>
          </Grid>
        </Grid>
        {/* <Typography variant="h5">
                <strong>Application Settings</strong>
            </Typography>
            <Divider /> */}
        <Grid container spacing={2} justifyContent={"left"}>
          <Grid item md={3}>
            <Box>
              <Typography sx={font_Small}>User Role</Typography>
              <FormControl fullWidth size="small" sx={{ margin: ".5em 0px" }}>
                <Select
                  placeholder="Select User Role"
                  select
                  sx={font_Small}
                  size="small"
                  value={SettingsObj.roleId}
                  name={"roleId"}
                  onChange={handleRole}
                  displayEmpty={true}
                  error={formValidationErrorItems.includes("roleId")}
                >
                  <MenuItem sx={font_Small} value={""}>
                    <div style={{ color: "#C1C1C1" }}>Select User Role </div>
                  </MenuItem>
                  {roleList &&
                    roleList?.map((role) => (
                      <MenuItem sx={font_Small} value={role.id}>
                        {role.name}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Box>
          </Grid>
          <Grid item md={4}>
            <Grid container rowSpacing={1} spacing={2}>
              <Grid item md={12}>
                <Box>
                  <Typography sx={font_Small}>Date Format</Typography>
                  <FormControl
                    fullWidth
                    size="small"
                    sx={{ margin: ".5em 0px" }}
                  >
                    <Select
                      placeholder="Select Date Format"
                      select
                      sx={font_Small}
                      size="small"
                      value={SettingsObj.dateFormat}
                      name={"dateFormat"}
                      onChange={handleFormat}
                      displayEmpty={true}
                      error={formValidationErrorItems.includes("dateFormat")}
                    >
                      <MenuItem sx={font_Small} value={""}>
                        <div style={{ color: "#C1C1C1" }}>
                          Select Date Format{" "}
                        </div>
                      </MenuItem>

                      <MenuItem value={"DD MMM YYYY"}>
                        DD MMM YYYY (01 Apr 2023)
                      </MenuItem>
                      <MenuItem value={"MMM DD, YYYY"}>
                        MMM DD, YYYY (Apr 01, 2023)
                      </MenuItem>
                      <MenuItem value={"YYYY MMM DD"}>
                        YYYY MMM DD (2023 Apr 01)
                      </MenuItem>

                      <MenuItem value={"DD-MM-YYYY"}>
                        DD-MM-YYYY (01-04-2023)
                      </MenuItem>
                      <MenuItem value={"MM-DD-YYYY"}>
                        MM-DD-YYYY (04-01-2023)
                      </MenuItem>
                      <MenuItem value={"YYYY-MM-DD"}>
                        YYYY-MM-DD (2023-04-01)
                      </MenuItem>

                      <MenuItem value={"DD/MM/YYYY"}>
                        DD/MM/YYYY (01/04/2023)
                      </MenuItem>
                      <MenuItem value={"MM/DD/YYYY"}>
                        MM/DD/YYYY (04/01/2023)
                      </MenuItem>
                      <MenuItem value={"YYYY/MM/DD"}>
                        YYYY/MM/DD (2023/04/01)
                      </MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </Grid>
              <Grid item md={12}>
                <Box>
                  <Typography sx={font_Small}>Time Format</Typography>
                  <FormControl
                    fullWidth
                    size="small"
                    sx={{ margin: ".5em 0px" }}
                  >
                    <Select
                      placeholder="Select Time Format"
                      select
                      size="small"
                      value={SettingsObj.timeFormat}
                      name={"timeFormat"}
                      onChange={handleSelect}
                      displayEmpty={true}
                      sx={font_Small}
                      error={formValidationErrorItems.includes("timeFormat")}
                    >
                      <MenuItem sx={font_Small} value={""}>
                        <div style={{ color: "#C1C1C1" }}>
                          Select Time Format{" "}
                        </div>
                      </MenuItem>

                      <MenuItem value={"hh:mm A"}>12-hour (01:34 AM)</MenuItem>
                      <MenuItem value={"HH:mm"}>24-hour (13:34)</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </Grid>
              {/* <Grid item md={12}>
              <Box>
                <Typography sx={font_Small}>Time Zone</Typography>
                <Autocomplete
                  size="small"
                  placeholder={`Select Time Zone`}
                  // sx={font_Small}
                  value={SettingsObj.timeZone}
                  onChange={(e, newValue) => {
                    const updatedValue = newValue || ``;
                    handleSelect({
                      target: { name: "timeZone", value: updatedValue },
                    });
                  }}
                  options={timezoneOptions}
                  autoHighlight
                  getOptionLabel={(option) => option}
                  renderInput={(params) => (
                    <TextField
                      fullWidth
                      size="small"
                      {...params}
                      placeholder="Select Time Zone"
                    />
                  )}
                  sx={{
                    ...font_Small,
                    backgroundColor: "white",
                    border: formValidationErrorItems.includes("timeZone")
                      ? "1px solid red"
                      : "",
                    borderRadius: "6px",
                  }}
                  error={formValidationErrorItems.includes("timeZone")}
                />
              </Box>
            </Grid> */}
              <Grid item md={12}>
                <Box>
                  <Typography sx={font_Small}>Default Date Range</Typography>
                  <FormControl
                    fullWidth
                    size="small"
                    sx={{ margin: ".5em 0px" }}
                  >
                    <Select
                      placeholder="Select Default Date Range"
                      select
                      size="small"
                      value={SettingsObj.range}
                      name={"range"}
                      onChange={handleRange}
                      displayEmpty={true}
                      sx={font_Small}
                      error={formValidationErrorItems.includes("range")}
                    >
                      <MenuItem sx={font_Small} value={0}>
                        <div style={{ color: "#C1C1C1" }}>
                          Select Default Date Range
                        </div>
                      </MenuItem>

                      {defaultDateRange?.map((rangeName) => (
                        <MenuItem
                          value={
                            rangeName === "Last Week"
                              ? 7
                              : rangeName === "Last Month"
                              ? 50
                              : rangeName === "Current Month"
                              ? 100
                              : rangeName === "Current Quarter"
                              ? 150
                              : rangeName === "Year To Date"
                              ? 200
                              : 0
                          }
                        >
                          {rangeName}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>
              </Grid>
              {pageAccess && (
                <Grid item md={12}>
                  <Box>
                    <Typography sx={font_Small}>
                      Default Landing Page
                    </Typography>
                    <FormControl
                      fullWidth
                      size="small"
                      sx={{ margin: ".5em 0px" }}
                    >
                      <Select
                        placeholder="Select Default Module"
                        select
                        size="small"
                        value={SettingsObj.defaultModule}
                        name={"defaultModule"}
                        onChange={handleLandingPage}
                        displayEmpty={true}
                        sx={font_Small}
                        error={formValidationErrorItems.includes(
                          "defaultModule"
                        )}
                      >
                        {pageAccess?.map((item) => {
                          if (item.isMenu) {
                            return (
                              <MenuItem key={item.value} value={item.value}>
                                <Stack
                                  spacing={1}
                                  direction={"row"}
                                  alignItems={"center"}
                                  justifyContent={"start"}
                                >
                                  <ReusableIcon iconName={item.icon} />
                                  <Typography variant="body1">
                                    {item.name}
                                  </Typography>
                                </Stack>
                              </MenuItem>
                            );
                          } else {
                            return (
                              <ListSubheader>
                                <i>{item.name}</i>
                              </ListSubheader>
                            );
                          }
                        })}
                      </Select>
                    </FormControl>
                  </Box>
                </Grid>
              )}
              <Grid
                item
                md={12}
                sx={{ display: "flex", justifyContent: "right" }}
              >
                <Stack spacing={1} direction={"row"}>
                  {/* <Button
                                    sx={{ width: "max-content", textTransform: "capitalize" }}
                                    onClick={handleClear}
                                    variant="outlined"
                                >
                                    Reset
                                </Button> */}
                  <Button
                    // className="button_primary--normal"
                    type="save"
                    variant="contained"
                    sx={{ ...button_Primary }}
                    onClick={() => {
                      if (
                        formValidator(
                          SettingsObj,
                          ["dateFormat", "range", "timeFormat"],
                          setFormValidationErrorItems
                        )
                      ) {
                        handleSave();
                      }
                    }}
                  >
                    Save
                  </Button>
                </Stack>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Stack>
    </div>
  );
};

export default ApplicationConfiguration;
