import { Grid, Typography } from "@mui/material";
import React from "react";
import RectangleIcon from "@mui/icons-material/Rectangle";
import { useNavigate } from "react-router-dom";
const controlLegend = (id, type) => {
  if (type == "po") {
    return `/purchaseOrder/management/singlePurchaseOrder/${id}`;
  } else if (type == "inv") {
    return `/invoices/invoiceTracker/singleInvoice/${id}`;
  }
};
const LegendBoxPie = ({ content, margin, type }) => {
  const boxColors = ["#a7eff3", "#6F4840", "#b1aade", "#59e0e9"];
  const navigate = useNavigate();
  return (
    <>
      <Grid container ml={margin}>
        {content?.slice(1)?.map((item) => (
          <Grid item xs={4}>
            <Typography
              fontSize="12px"
              sx={{ cursor: "pointer" }}
              onClick={() => navigate(controlLegend(item?.name, type))}
            >
              <RectangleIcon
                style={{ color: `${item?.col}`, marginRight: "10px" }}
                fontSize="16px"
              />{" "}
              {item?.name}
            </Typography>
          </Grid>
        ))}
      </Grid>
    </>
  );
};

export default LegendBoxPie;
