
import React, { useState } from 'react'
import { useSelector } from "react-redux";
import { baseUrl_CrudService, baseUrl_Messaging } from "../../../data/baseUrl";
import EmailTemplate from '../../EmailConfiguration/Email Config/EmailTemplate';

const EmailConfiguration = () => {
    let userData = useSelector((state) => state.userManagement.userData);
    const appConfigDetails = useSelector((state) => state.applicationConfig);

    const [userRawData, setUserRawData] = useState([]);
    const [userGroupRawData, setUserGroupRawData] = useState([]);
    const destinations = appConfigDetails.environment === 'localhost' ? [
        { Description: '', Name: 'CW_Worktext', URL: `${baseUrl_Messaging}` },
        { Description: '', Name: 'WorkUtilsServices', URL: `${baseUrl_Messaging}` },
        { Description: '', Name: 'WorkUtilsServicesHana', URL: `${baseUrl_Messaging}` },
        { Description: '', Name: 'CrudApiServices', URL: `${baseUrl_CrudService}` },
    ] : [];
    let authorizeToken = appConfigDetails.environment === 'localhost' ? appConfigDetails.iwaToken : "Bearer "

    const  feature={

        EMAIL_CONFIG_SUMMARY:"True",
      
      EMAIL_CONFIG_SUMMARY_ACTIVE:"True",
      
      EMAIL_CONFIG_SUMMARY_DRAFT:"True",
      
      EMAIL_CONFIG_SUMMARY_SORT:"True",
      
      EMAIL_CONFIG_SUMMARY_SEARCH:"True",
      
      EMAIL_CONFIG_CREATE:"True",
      
      EMAIL_CONFIG_SAVE:"True",
      
      EMAIL_CONFIG_DISCARD:"True",
      
      EMAIL_CONFIG_DELETE:"True",
      
      EMAIL_CONFIG_SUBMIT:"True",
      
      EMAIL_CONFIG_MANAGE_GROUPS:"True",
      
      EMAIL_CONFIG_MANAGE_GROUPS_CREATE:"True",
      
      EMAIL_CONFIG_MANAGE_GROUPS_ADD_USER:"True",
      
      EMAIL_CONFIG_MANAGE_GROUPS_MEMBER_INFO:"True",
      
      EMAIL_CONFIG_MANAGE_GROUPS_TEMPLATE_INFO:"false",
      
      EMAIL_CONFIG_GROUP_MAPPING:"True",
      
      EMAIL_CONFIG_GROUP_MAPPING_ADD:"True",
      
      EMAIL_CONFIG_GROUP_MAPPING_DELETE:"True",
      
      EMAIL_CONFIG_GROUP_MAPPING_FILTER:"True",
      
      }
    return (
        <div>
            <EmailTemplate
                applicationName="ITM"
                token={authorizeToken}
                destinations={destinations}
                headers={[]}
                useCrud={true}
                environment="itm"
                useWorkAccess={false}
                useConfigServerDestination={false}
                userId={userData?.emailId}
                applicationId="1"
                groupList={userGroupRawData}
                userList={userRawData}
                contentHTML={""}
                needHeading={false}
                isAssociatedTemplate={true}
                isRecepientData={false}
                showManageGroups={true}
                pathName={"/configCockpit/userManagement?component=groups"}
                feature={feature}
            />
        </div>
    )
}

export default EmailConfiguration