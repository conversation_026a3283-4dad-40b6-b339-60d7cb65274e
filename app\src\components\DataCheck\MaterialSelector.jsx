import React, { useState, useMemo, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Button,
  Box,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Fade,
  Tooltip,
  Paper,
  Stack,
  Grid,
  Drawer,
  Divider,
  Avatar,
  LinearProgress
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Assessment as AssessmentIcon,
  Description as DescriptionIcon,
} from '@mui/icons-material';
import { colors } from '@constant/colors';
import RuleIcon from '@mui/icons-material/Rule';
import PersonIcon from '@mui/icons-material/Person';
import ViewModuleIcon from '@mui/icons-material/ViewModuleOutlined';
import { DATA_CLEANSE_CONSTANTS } from '@constant/enum';

const MaterialSelector = ({ apiData }) => {
  const [drawerOpen, setDrawerOpen] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchFocused, setSearchFocused] = useState(false);


  const materials = Object.keys(apiData).map((materialNumber, index) => ({
    id: index + 1,
    materialNumber: materialNumber,
    score: Math.round(apiData[materialNumber][0].MAT_MATERIAL_FIELD_CONFIG.totalScore),
    configCount: apiData[materialNumber].length
  }));

  const [selectedId, setSelectedId] = useState(materials[0]?.id || null);

  // Enhanced filtering with multiple search criteria
  const filteredMaterials = useMemo(() => {
    if (!searchTerm.trim()) return materials;

    const search = searchTerm.toLowerCase();
    return materials.filter((material) => {
      const materialMatch = material.materialNumber.toLowerCase().includes(search);
      const scoreMatch = material.score.toString().includes(search);
      return materialMatch || scoreMatch;
    });
  }, [materials, searchTerm]);

  // Auto-select first result when searching
  useEffect(() => {
    if (searchTerm && filteredMaterials.length > 0 && !selectedId) {
      setSelectedId(filteredMaterials[0].id);
    }
  }, [searchTerm, filteredMaterials, selectedId]);

  const getScoreColor = (score) => {
    if (score >= 80) return 'success';
    if (score >= 50) return 'warning';
    return 'error';
  };

  const getScoreColorHex = (score) => {
    if (score >= 80) return '#2e7d32';
    if (score >= 50) return '#f57c00';
    return '#d32f2f';
  };

  const clearSearch = () => {
    setSearchTerm('');
  };

  const selectedMaterial = materials.find(m => m.id === selectedId);
  const selectedMaterialConfigs = selectedMaterial ? apiData[selectedMaterial.materialNumber] : [];

  const calculateOverallScore = (configs) => {
    if (!configs || configs.length === 0) return 0;
    const totalScore = configs.reduce((sum, config) => {
      const configKey = Object.keys(config)[0];
      return sum + config[configKey].totalScore;
    }, 0);
    return Math.round(totalScore / configs.length);
  };

  const getPassFailCounts = (fieldDetails) => {
    const passCount = fieldDetails.filter(field => field.value.pass === true).length;
    const failCount = fieldDetails.filter(field => field.value.pass === false).length;
    return { passCount, failCount };
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh', bgcolor: '#f5f5f5' }}>
      {/* Header Cards */}
      <Box sx={{ p: 3, bgcolor: 'white', borderBottom: 1, borderColor: 'divider' }}>
        <Grid container spacing={3} justifyContent="center">
          {[
            {
              label: DATA_CLEANSE_CONSTANTS.MODULE,
              value: 'Material',
              color: '#FF6B6B',
              icon: <ViewModuleIcon />,
            },
            {
              label: DATA_CLEANSE_CONSTANTS.NO_OBJECTS,
              value: Object.keys(apiData).length,
              color: '#FF9800',
              icon: <DescriptionIcon />,
            },
            {
              label: DATA_CLEANSE_CONSTANTS.BUSINESS_RULES,
              value: apiData?.[Object.keys(apiData)?.[0]]?.length,
              color: '#9C27B0',
              icon: <RuleIcon />
            },
            {
              label: DATA_CLEANSE_CONSTANTS.AGGREGATE_SCORE,
              value: `${Math.round(materials.reduce((sum, m) => sum + m.score, 0) / materials.length)}%`,
              color: '#2196F3',
              icon: <CheckCircleIcon />,
              // icon: <TrendingUpIcon/>
            },
            {
              label: DATA_CLEANSE_CONSTANTS.CREATEDBY,
              value: '<EMAIL>',
              color: '#4CAF50',
              icon: <PersonIcon />
            },
          ].map((card, idx) => (
            <Grid item xs={12} sm={6} md={2.4} key={idx}>
              <Card
                sx={{
                  background: `linear-gradient(135deg, ${card.color}15 0%, ${card.color}08 100%)`,
                  border: `1px solid ${card.color}30`,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    boxShadow: `0 8px 25px ${card.color}20`,
                  },
                  borderRadius: "10px",
                }}
              >
                <CardContent sx={{ textAlign: 'center', py: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
                    <Avatar sx={{ bgcolor: card.color, width: 40, height: 40 }}>
                      {card.icon}
                    </Avatar>
                  </Box>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {card.label}
                  </Typography>
                  <Typography variant="h5" fontWeight="bold" color={card.color}>
                    {card.value}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Main Content */}
      <Box sx={{ display: 'flex', flex: 1, overflow: 'hidden', zIndex: '1' }}>
        {/* Drawer - Left Sidebar */}
        <Drawer
          variant="persistent"
          open={drawerOpen}
          sx={{
            width: 320,
            flexShrink: 0,
            '& .MuiDrawer-paper': {
              width: 320,
              boxSizing: 'border-box',
              position: 'relative',
              height: 'auto',
              bgcolor: 'white',
              borderRight: 1,
              borderColor: 'divider',
            },
          }}
        >
          <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            {/* Search Container */}
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <TextField
                fullWidth
                variant="outlined"
                placeholder={DATA_CLEANSE_CONSTANTS.SEARCH}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onFocus={() => setSearchFocused(true)}
                onBlur={() => setSearchFocused(false)}
                size="small"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: searchTerm && (
                    <InputAdornment position="end">
                      <IconButton onClick={clearSearch} size="small">
                        <ClearIcon />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    bgcolor: 'grey.50',
                    '&:hover': {
                      bgcolor: 'grey.100',
                    },
                    '&.Mui-focused': {
                      bgcolor: 'white',
                    },
                  },
                }}
              />
            </Box>

            {/* Search Statistics */}
            {searchTerm && (
              <Box sx={{ px: 2, py: 1, bgcolor: 'grey.50', borderBottom: 1, borderColor: 'divider' }}>
                <Typography variant="caption" color="text.secondary">
                  {filteredMaterials.length} of {materials.length} materials
                </Typography>
              </Box>
            )}

            {/* Materials List */}
            <Box sx={{ flex: 1, overflow: 'auto', p: 1, maxHeight: '500px' }}>
              <Stack spacing={1}>
                {filteredMaterials.map((material) => (
                  <Paper
                    key={material.id}
                    elevation={selectedId === material.id ? 8 : 1}
                    sx={{
                      p: 2,
                      cursor: 'pointer',
                      border: selectedId === material.id ? 2 : 1,
                      borderColor: selectedId === material.id ? 'primary.main' : 'divider',
                      bgcolor: selectedId === material.id ? 'primary.50' : 'white',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        bgcolor: selectedId === material.id ? 'primary.100' : 'grey.50',
                      },
                      borderRadius: "10px",
                    }}
                    onClick={() => setSelectedId(material.id)}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="caption" color="text.secondary">
                        {DATA_CLEANSE_CONSTANTS.MATERIAL}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" paddingRight="5px">
                        {DATA_CLEANSE_CONSTANTS.SCORE}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography
                        variant="subtitle2"
                        fontWeight={selectedId === material.id ? 600 : 500}
                        color={selectedId === material.id ? 'primary.main' : 'text.primary'}
                      >
                        {material.materialNumber}
                      </Typography>
                      <Chip
                        label={`${material.score}%`}
                        size="small"
                        color={getScoreColor(material.score)}
                        variant="outlined"
                      />
                    </Box>
                  </Paper>
                ))}

                {filteredMaterials.length === 0 && (
                  <Box sx={{ textAlign: 'center', py: 6 }}>
                    <SearchIcon sx={{ fontSize: 48, color: 'grey.300', mb: 2 }} />
                    <Typography variant="body2" color="text.secondary">
                      {DATA_CLEANSE_CONSTANTS.NO_MATERIAL_FOUND}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {DATA_CLEANSE_CONSTANTS.ADJUST}
                    </Typography>
                  </Box>
                )}
              </Stack>
            </Box>
          </Box>
        </Drawer>

        {/* Main Content Area */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
          {selectedMaterial && selectedMaterialConfigs.length > 0 ? (
            <Stack spacing={3}>
              {/* Header */}
              <Paper
                elevation={2}
                sx={{
                  p: 3,
                  background: 'linear-gradient(135deg,rgb(137, 155, 233) 0%,rgb(171, 114, 228) 100%)',
                  color: 'white',
                  borderRadius: "10px",
                }}
              >
                <Grid container alignItems="center" justifyContent="space-between">
                  <Grid item>
                    <Typography variant="h3" fontWeight="bold" gutterBottom color={"white"}>
                      {selectedMaterial.materialNumber}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      {DATA_CLEANSE_CONSTANTS.ANALYSIS_DETAILS} - {selectedMaterialConfigs.length} Business Rule{selectedMaterialConfigs.length !== 1 ? 's' : ''}
                    </Typography>
                  </Grid>
                  <Grid item>
                    <Box sx={{ textAlign: 'right' }}>
                      <Typography variant="h4" fontWeight="bold" color={"white"}>
                        {calculateOverallScore(selectedMaterialConfigs)}%
                      </Typography>
                      <Typography variant="body2" sx={{ opacity: 0.9 }}>
                         {DATA_CLEANSE_CONSTANTS.OVERALL_SCORE}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Paper>

              {/* Configuration Cards */}
              <Grid container spacing={3} sx={{ marginLeft: "200px" }}>
                {selectedMaterialConfigs.map((config, configIndex) => {
                  const configKey = Object.keys(config)[0];
                  const configData = config[configKey];
                  const { passCount, failCount } = getPassFailCounts(configData.fieldDetails);

                  return (
                    <Grid item xs={12} lg={6} key={configIndex}>
                      <Card
                        elevation={3}
                        sx={{
                          ml: -3,
                          mr: 3,
                          height: '100%',
                          borderRadius: "10px",
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            boxShadow: 6,
                          },
                        }}
                      >
                        <CardContent sx={{ p: 3 }}>
                          {/* Config Header */}
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                            <Typography variant="h6" fontWeight="bold" color="primary">
                              <Box component="span" sx={{ fontWeight: 'bold' }}>{DATA_CLEANSE_CONSTANTS.BUSINESS_RULE}: </Box>
                              <Box component="span" sx={{ fontWeight: 'medium' }}>{configKey}</Box>
                            </Typography>
                            <Chip
                              label={`${Math.round(configData.totalScore)}%`}
                              color={getScoreColor(configData.totalScore)}
                              sx={{ fontWeight: 'bold' }}
                            />
                          </Box>

                          {/* Progress Bar */}
                          <Box sx={{ mb: 2 }}>
                            <LinearProgress
                              variant="determinate"
                              value={configData.totalScore}
                              color={getScoreColor(configData.totalScore)}
                              sx={{ height: 8, borderRadius: 4 }}
                            />
                          </Box>

                          {/* Summary Stats */}
                          <Grid container spacing={2} sx={{ mb: 2 }}>
                            <Grid item xs={4}>
                              <Box sx={{ textAlign: 'center' }}>
                                <Typography variant="h6" fontWeight="bold">
                                  {configData.fieldDetails.length}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {DATA_CLEANSE_CONSTANTS.FIELDS}
                                </Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={4}>
                              <Box sx={{ textAlign: 'center' }}>
                                <Typography variant="h6" fontWeight="bold" color="success.main">
                                  {passCount}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {DATA_CLEANSE_CONSTANTS.PASS}
                                </Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={4}>
                              <Box sx={{ textAlign: 'center' }}>
                                <Typography variant="h6" fontWeight="bold" color="error.main">
                                  {failCount}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {DATA_CLEANSE_CONSTANTS.FAIL}
                                </Typography>
                              </Box>
                            </Grid>
                          </Grid>

                          <Divider sx={{ my: 2 }} />

                          {/* Field Details */}
                          <Stack spacing={1.5}>
                            {configData.fieldDetails.map((field, fieldIndex) => (
                              <Paper
                                key={fieldIndex}
                                variant="outlined"
                                sx={{
                                  p: 2,
                                  bgcolor: field.value.pass ? 'success.50' : 'error.50',
                                  borderColor: field.value.pass ? 'success.200' : 'error.200',
                                }}
                              >
                                <Grid container alignItems="center" spacing={2} paddingLeft={"-500px"}>
                                  <Grid item>
                                    {field.value.pass ? (
                                      <CheckCircleIcon color="success" />
                                    ) : (
                                      <CancelIcon color="error" />
                                    )}
                                  </Grid>
                                  <Grid item xs>
                                    <Typography variant="h5">
                                      <Box component="span" sx={{ fontWeight: 'bold' }}>Field Name: </Box>
                                      <Box component="span" sx={{ fontWeight: 'normal' }}>{field.fieldName}</Box>
                                    </Typography>
                                    <Typography variant="h6" color="text.secondary">
                                      {DATA_CLEANSE_CONSTANTS.SCORE}: {field.value.givenScore}/{field.value.allotedScore}
                                    </Typography>
                                  </Grid>
                                  <Grid item>
                                    <Box sx={{ textAlign: 'right' }}>
                                      <Typography variant="h6" color="text.secondary">
                                        {DATA_CLEANSE_CONSTANTS.PRESENT_VALUE}: {field.value.dataPresentValue || 'N/A'}
                                      </Typography>
                                      <br />
                                      <Typography variant="h6" color="text.secondary">
                                        {DATA_CLEANSE_CONSTANTS.EXPECTED_VALUE}: {field.value.dataDefaultValue}
                                      </Typography>
                                    </Box>
                                  </Grid>
                                </Grid>
                              </Paper>
                            ))}
                          </Stack>
                        </CardContent>
                      </Card>
                    </Grid>
                  );
                })}
              </Grid>
            </Stack>
          ) : (
            <Paper
              elevation={2}
              sx={{
                p: 6,
                textAlign: 'center',
                bgcolor: 'grey.50',
                minHeight: 400,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <AssessmentIcon sx={{ fontSize: 64, color: 'grey.300', mb: 2 }} />
              <Typography variant="h5" color="text.secondary" gutterBottom>
                {DATA_CLEANSE_CONSTANTS.NO_MATERIAL}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {DATA_CLEANSE_CONSTANTS.VALIDATIONS_DETAILS}
              </Typography>
            </Paper>
          )}
        </Box>
      </Box>
    </Box>
  );
};
export default MaterialSelector;