import { CreateRole } from '@cw/createrole';
import { useLocation, useNavigate } from 'react-router-dom';
import {APP_END_POINTS} from "@constant/appEndPoints";
const IwaCreateRole = () => {
  const navigate = useNavigate();
  const reactRouterLocation = useLocation();
  const { mapGroups, mapRoleCollections } = reactRouterLocation.state || {};
  const sourceSystemDataSync = {
    mapGroups: mapGroups ?? [],
    mapRoleCollections: mapRoleCollections ?? [],
  };
  const onCreateRoleActionClick = action => {
    if (action === 'roleSummary' || action === 'home') {
      navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.ROLES_SUMMARY);
    }
  };
  return (
    <CreateRole
      sourceSystemDataSync={sourceSystemDataSync}
      onCreateRoleActionClick={onCreateRoleActionClick}
    />
  );
};
export default IwaCreateRole;