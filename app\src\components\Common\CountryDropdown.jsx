import React, { useEffect, useState } from "react";
import { Grid, FormControl, InputLabel, Select, MenuItem } from "@mui/material";
import { doAjax } from "../Common/fetchService";
import { destination_ProfitCenter_Mass } from "../../../src/destinationVariables";

const CountryDropdown = ({ formData, handleChange }) => {
  console.log("formData",formData)
  const [dropdownDataSegment, setDropdownDataSegment] = useState([]);
  

  const getBusinessSegment = () => {
    const hSuccess = (data) => {
      setDropdownDataSegment(data.body);
    };

    const hError = (error) => {
      console.log("Error fetching business segments:", error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCountryOrReg`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    getBusinessSegment();
  }, []);

  const countryValue = formData?.jsonName ?? "";  // Use a fallback value if formData.Country is undefined
  console.log("countryValue",countryValue)
  return (
    <Grid item xs={6} md={12}>
      <FormControl fullWidth>
        <InputLabel>Country</InputLabel>
        <Select
          value={countryValue}  // Default to empty string if null or undefined
          onChange={handleChange("Country")}
        >
          {dropdownDataSegment?.length > 0 ? (
            dropdownDataSegment.map((item) => (
              <MenuItem key={item.code} value={item.code}>
                {`${item.code}-${item.desc}`}
              </MenuItem>
            ))
          ) : (
            <MenuItem disabled>Loading...</MenuItem>
          )}
        </Select>
      </FormControl>
    </Grid>
  );
};


export default CountryDropdown;
