import { Box, Button, Checkbox, <PERSON>Field, Typography, IconButton, <PERSON>ltip, Tabs, Tab, Paper, Stack } from "@mui/material";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { useState, useEffect } from "react";
import useLang from "@hooks/useLang";
import { DataGrid } from "@mui/x-data-grid";
import { v4 as uuidv4 } from "uuid";
import { DeleteOutlineOutlined as DeleteOutlineOutlinedIcon, CloseFullscreen as CloseFullscreenIcon, CropFree as CropFreeIcon, FormatColorResetRounded, TaskAlt as TaskAltIcon, CancelOutlined as CancelOutlinedIcon } from "@mui/icons-material";
import { useSelector, useDispatch } from "react-redux";
import { updateModuleFieldDataIO, updateTableColumnDataIO, deleteRowDataIO, setActiveRowIdIO, setSelectedOrderTypeIO, setValidatedRowsIO, setValidatedStatusIO, setOriginalRowDataIO, setOriginalTabDataIO } from "./slice/InternalOrderSlice";
import useInternalOrderFieldConfig from "@hooks/useInternalOrderFieldConfig";
import GenericTabsGlobal from "@components/MasterDataCockpit/GenericTabsGlobal";
import useButtonDTConfig from "@hooks/useButtonDTConfig";
import BottomNavGlobal from "@components/RequestBench/RequestPages/BottomNavGlobal";
import { MODULE, MODULE_MAP, REQUEST_TYPE, SUCCESS_MESSAGES, ERROR_MESSAGES } from "@constant/enum";
import { useNavigate } from "react-router-dom";
import { createPayloadForIO } from "../../functions";
import { destination_InternalOrder } from "../../destinationVariables";
import { END_POINTS } from "../../constant/apiEndPoints";
import { Snackbar, Alert } from "@mui/material";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { doAjax } from "@components/Common/fetchService";
import { useSnackbar } from "@hooks/useSnackbar";
import useLogger from "@hooks/useLogger";

const RequestDetailsIO = () => {
  const { t } = useLang();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { fetchInternalOrderFieldConfig, fieldConfigByOrderType } = useInternalOrderFieldConfig();
  const { getButtonsDisplayGlobal } = useButtonDTConfig();

  // State management
  const [rows, setRows] = useState([]);
  const [isGridZoomed, setIsGridZoomed] = useState(false);
  const [page, setPage] = useState(0);
  const [selectedTab, setSelectedTab] = useState(0);
  const [isTabsZoomed, setIsTabsZoomed] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const { showSnackbar } = useSnackbar();
  const { customError, log } = useLogger()

  // Get persistent state from Redux
  const activeRowId = useSelector((state) => state.internalOrder.activeRowId);
  const selectedOrderType = useSelector((state) => state.internalOrder.selectedOrderType);
  const IODropdownData = useSelector((state) => state.internalOrder.dropDownDataIO);
  const IOpayloadData = useSelector((state) => state.internalOrder.IOpayloadData);
  const orderTypeOptions = [{ code: "TUK1", desc: "TUK1" }]; // Dummy dropdown option
  const taskData = useSelector((state) => state.userManagement.taskData);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons);
  const savedRequestData = useSelector((state) => state.internalOrder.savedReqData);

  // Additional Redux selectors for API calls
  const reduxPayload = useSelector((state) => state.internalOrder);
  const requestHeaderSlice = useSelector((state) => state.internalOrder.requestHeaderDTIO);

  const validatedRows = useSelector((state) => state.internalOrder.validatedRows);
  const validatedRowsStatus = useSelector((state) => state.internalOrder.validatedRowsStatus);
  const originalRowData = useSelector((state) => state.internalOrder.originalRowData);
  const originalTabData = useSelector((state) => state.internalOrder.originalTabData);
  // const requestId = savedRequestData?.RequestId || IOpayloadData?.requestHeaderData?.RequestId || "";
  const requestId = savedRequestData?.RequestId || IOpayloadData?.requestHeaderData?.RequestId || "";

  // Dynamic data for workflow and button configuration
  const dynamicData = {
    // Workflow data
    taskId: taskData?.taskId || null,
    processInstanceId: taskData?.processInstanceId || null,
    assignee: taskData?.assignee || null,

    // Button configuration
    buttonConfig: filteredButtons,

    // Request metadata
    requestType: REQUEST_TYPE?.CREATE,
    moduleType: MODULE_MAP?.IO,

    // User context
    userContext: {
      userId: taskData?.assignee,
      timestamp: new Date().toISOString(),
    },

    // Spread any additional task data
    ...taskData,
  };

  const internalOrderTabs = (fieldConfigByOrderType[selectedOrderType]?.allTabsData || []).filter((tab) => tab.tab.toLowerCase() !== "header" && tab.tab.toLowerCase() !== "request header");

  useEffect(() => {
    if (taskData?.ATTRIBUTE_1) {
      getButtonsDisplayGlobal("Internal Order", "MDG_DYN_BTN_DT", "v3");
    }
  }, [taskData]);

  // Load rows from Redux when component mounts or Redux data changes
  useEffect(() => {
    const rowsBodyData = IOpayloadData?.rowsBodyData || {};
    const reduxRows = Object.keys(rowsBodyData).map((uniqueId) => ({
      id: uniqueId,
      included: rowsBodyData[uniqueId]?.included ?? true,
      internalOrder: rowsBodyData[uniqueId]?.internalOrder ?? "",
      controllingArea: rowsBodyData[uniqueId]?.controllingArea ?? "",
      orderType: rowsBodyData[uniqueId]?.orderType ?? "",
      description: rowsBodyData[uniqueId]?.description ?? "",
      compCode: rowsBodyData[uniqueId]?.compCode ?? "",
    }));

    if (reduxRows.length > 0) {
      setRows(reduxRows);

      // If no activeRowId is set but we have rows, set the first row as active
      if (!activeRowId && reduxRows.length > 0) {
        const firstRow = reduxRows[0];
        const orderTypeValue = firstRow.orderType?.code || firstRow.orderType;
        dispatch(setActiveRowIdIO(firstRow.id));
        if (orderTypeValue) {
          dispatch(setSelectedOrderTypeIO(orderTypeValue));
        }
      }
    }
  }, [IOpayloadData?.rowsBodyData, activeRowId, dispatch]);

  // Auto-select first row and load its field configuration
  useEffect(() => {
    if (rows.length > 0 && !activeRowId) {
      const firstRow = rows[0];
      const orderTypeValue = firstRow.orderType?.code || firstRow.orderType;

      dispatch(setActiveRowIdIO(firstRow.id));

      if (orderTypeValue) {
        dispatch(setSelectedOrderTypeIO(orderTypeValue));
        try {
          fetchInternalOrderFieldConfig(orderTypeValue);
        } catch (error) {
          customError("Failed to fetch field configuration for first row:", error);
        }
      }
    }
  }, [rows, activeRowId, fetchInternalOrderFieldConfig, dispatch]);

  useEffect(() => {
    if (selectedOrderType) {
      fetchInternalOrderFieldConfig(selectedOrderType);
    }
  }, [selectedOrderType]);
  // Button handlers for BottomNavGlobal
  const handleSaveAsDraft = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForIO(reduxPayload, requestHeaderSlice, requestId, taskData, dynamicData);

    const hSuccess = (data) => {
      setBlurLoading(false);
      showSnackbar(SUCCESS_MESSAGES.IO_SAVED_AS_DRAFT);
      navigate("/requestbench");
    };

    const hError = (error) => {
      setBlurLoading(false);
      showSnackbar(ERROR_MESSAGES.IO_SAVE_AS_DRAFT_ERROR);
    };

    doAjax(`/${destination_InternalOrder}${END_POINTS.MASS_ACTION.INTERNAL_ORDERS_SAVE_AS_DRAFT}`, "POST", hSuccess, hError, finalPayload);
  };

  const handleSubmitForReview = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForIO(reduxPayload, requestHeaderSlice, requestId, taskData, dynamicData);

    const hSuccess = (data) => {
      setBlurLoading(false);
      showSnackbar(SUCCESS_MESSAGES.IO_SUBMITTED_FOR_REVIEW);
      navigate("/requestbench");
    };

    const hError = (error) => {
      setBlurLoading(false);
      showSnackbar(ERROR_MESSAGES.IO_SUBMIT_FOR_REVIEW_ERROR);
    };

    doAjax(`/${destination_InternalOrder}${END_POINTS.MASS_ACTION.INTERNAL_ORDERS_SUBMIT_FOR_REVIEW}`, "POST", hSuccess, hError, finalPayload);
  };

  const handleSubmitForApprove = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForIO(reduxPayload, requestHeaderSlice, requestId, taskData, dynamicData);

    const hSuccess = (data) => {
      setBlurLoading(false);
      showSnackbar(SUCCESS_MESSAGES.IO_SUBMITTED_FOR_APPROVAL);
      navigate("/requestbench");
    };

    const hError = (error) => {
      setBlurLoading(false);
      showSnackbar(ERROR_MESSAGES.IO_SUBMIT_FOR_APPROVAL_ERROR);
    };

    doAjax(`/${destination_InternalOrder}${END_POINTS.MASS_ACTION.INTERNAL_ORDERS_SUBMIT_FOR_APPROVAL}`, "POST", hSuccess, hError, finalPayload);
  };

  const handleSendBack = () => {
    console.log("Send Back clicked for Internal Order");
    // TODO: Implement send back logic
  };

  const handleCorrection = () => {
    console.log("Correction clicked for Internal Order");
    // TODO: Implement correction logic
  };

  const handleRejectAndCancel = () => {
    console.log("Reject and Cancel clicked for Internal Order");
    // TODO: Implement reject and cancel logic
  };

  const handleValidateAndSyndicate = (type) => {
    setBlurLoading(true);
    const finalPayload = createPayloadForIO(reduxPayload, requestHeaderSlice, requestId, taskData, dynamicData);

    const hSuccess = (data) => {
      setBlurLoading(false);
      if (type === "VALIDATE") {
        showSnackbar(SUCCESS_MESSAGES.IO_VALIDATION_INITIATED);
      } else if (type === "SYNDICATE") {
        showSnackbar(SUCCESS_MESSAGES.IO_SYNDICATION_INITIATED);
      }
      navigate("/requestbench");
    };

    const hError = (error) => {
      setBlurLoading(false);
      showSnackbar(ERROR_MESSAGES.IO_PROCESSING_ERROR);
    };

    const endpoint = type === "VALIDATE" ? `/${destination_InternalOrder}${END_POINTS.MASS_ACTION.VALIDATE_MASS_INTERNAL_ORDER}` : `/${destination_InternalOrder}${END_POINTS.MASS_ACTION.CREATE_INTERNAL_ORDERS_APPROVED}`;

    doAjax(endpoint, "POST", hSuccess, hError, finalPayload);
  };

  const validateAllRows = () => {
    // First check for duplicate rows within the current data
    if (!checkForDuplicateRows()) {
      return; // Stop validation if duplicates found
    }

    setBlurLoading(true);
    const finalPayload = createPayloadForIO(reduxPayload, requestHeaderSlice, requestId, taskData, dynamicData);

    const hSuccess = (data) => {
      setBlurLoading(false);
      showSnackbar(SUCCESS_MESSAGES.IO_VALIDATION_INITIATED);
      navigate("/requestbench");
    };

    const hError = (error) => {
      setBlurLoading(false);
      showSnackbar(ERROR_MESSAGES.IO_VALIDATION_ERROR);
    };

    doAjax(`/${destination_InternalOrder}${END_POINTS.MASS_ACTION.VALIDATE_MASS_INTERNAL_ORDER}`, "POST", hSuccess, hError, finalPayload);
  };

  // Individual row validation function

  // Get validation status for a row
  const getValidationStatus = (row) => {
    const isValidated = validatedRows[row.id];
    const validationStatus = validatedRowsStatus[row.id];

    if (isValidated === true) return "success";
    if (validationStatus === "error") return "error";
    return "default";
  };

  // Individual row validation functions
  const checkDuplicateInternalOrder = (row) => {
    // UI-level duplicate check within current form data using local rows state
    const duplicateFound = rows.some(
      (otherRow) =>
        otherRow.id !== row.id && // Don't compare with itself
        otherRow.orderType === row.orderType &&
        otherRow.controllingArea === row.controllingArea &&
        otherRow.compCode === row.compCode &&
        otherRow.internalOrder === row.internalOrder &&
        row.internalOrder && // Only check if internal order is not empty
        row.internalOrder.trim() !== "" // And not just whitespace
    );

    return !duplicateFound;
  };

  const checkValidation = (row) => {
    const mandatoryFields = ["orderType", "controllingArea", "description"];
    const headerMap = {
      orderType: "Order Type",
      controllingArea: "Controlling Area",
      description: "Description",
    };

    const missing = [];

    mandatoryFields.forEach((field) => {
      let value = row[field];

      // If value is an object with code property, use the code
      if (typeof value === "object" && value !== null && value.code !== undefined) {
        value = value.code;
      }

      const displayName = headerMap[field] || field;

      if (value === null || value === undefined || (typeof value === "string" && value.trim() === "")) {
        missing.push(displayName);
      }
    });

    return {
      missingFields: missing.length > 0 ? missing : null,
      isValid: missing.length === 0,
    };
  };

  const handleValidateRow = async (row) => {
    const { missingFields } = checkValidation(row);

    if (missingFields) {
      showSnackbar(ERROR_MESSAGES.IO_MANDATORY_FIELDS_ERROR(missingFields));
      dispatch(setValidatedStatusIO({ rowId: row.id, status: "error" }));
      dispatch(setValidatedRowsIO({ rowId: row.id, isValid: false }));
      return;
    }

    // Check for duplicates if mandatory validation passes
    const isDuplicateFound = !checkDuplicateInternalOrder(row);

    if (isDuplicateFound) {
      showSnackbar(ERROR_MESSAGES.IO_DUPLICATE_ERROR);
      dispatch(setValidatedStatusIO({ rowId: row.id, status: "error" }));
      dispatch(setValidatedRowsIO({ rowId: row.id, isValid: false }));
      return;
    }

    // Store original data for dirty checking
    dispatch(
      setOriginalRowDataIO({
        rowId: row.id,
        data: JSON.parse(JSON.stringify(row)),
      })
    );

    // Get tab data for this row
    const tabData = IOpayloadData.rowsBodyData[row.id]?.payload || {};
    dispatch(
      setOriginalTabDataIO({
        rowId: row.id,
        data: JSON.parse(JSON.stringify(tabData)),
      })
    );

    showSnackbar(SUCCESS_MESSAGES.IO_VALIDATION_SUCCESSFUL);
    dispatch(setValidatedStatusIO({ rowId: row.id, status: "success" }));
    dispatch(setValidatedRowsIO({ rowId: row.id, isValid: true }));
  };

  const checkForDuplicateRows = () => {
    const duplicates = [];

    for (let i = 0; i < rows.length; i++) {
      for (let j = i + 1; j < rows.length; j++) {
        const row1 = rows[i];
        const row2 = rows[j];

        // Check if rows have same key fields
        if (
          row1.orderType === row2.orderType &&
          row1.controllingArea === row2.controllingArea &&
          row1.compCode === row2.compCode &&
          row1.internalOrder === row2.internalOrder &&
          row1.internalOrder && // Only check if internal order is not empty
          row1.internalOrder.trim() !== "" // And not just whitespace
        ) {
          duplicates.push({ row1: i + 1, row2: j + 1 });
        }
      }
    }

    if (duplicates.length > 0) {
      const duplicateMessages = duplicates.map((dup) => `Rows ${dup.row1} and ${dup.row2} have identical key fields`);
      showSnackbar(ERROR_MESSAGES.IO_DUPLICATE_ROWS_ERROR(duplicateMessages));
      return false;
    }

    return true;
  };

  const handleAddRow = () => {
    const id = uuidv4();
    const newRow = {
      id,
      included: true,
      internalOrder: "",
      controllingArea: "",
      orderType: "",
      description: "",
      compCode: "",
    };

    // Add to local state
    setRows([...rows, newRow]);

    // Initialize table column data in Redux (not in payload)
    Object.entries(newRow).forEach(([fieldName, fieldValue]) => {
      if (fieldName !== "id") {
        // Don't store the ID as a field
        dispatch(
          updateTableColumnDataIO({
            uniqueId: id,
            keyName: fieldName,
            data: fieldValue,
          })
        );
      }
    });
  };
  const handleCellEdit = ({ id, field, value }) => {
    // 1. Update local state
    const updatedRows = rows.map((row) => (row.id === id ? { ...row, [field]: value } : row));
    setRows(updatedRows);

    // 2. Update Redux (if needed)
    dispatch(
      updateTableColumnDataIO({
        uniqueId: id,
        keyName: field,
        data: value,
      })
    );

    // 3. If field is `orderType`, trigger config fetch
    if (field === "orderType") {
      const selectedCode = typeof value === "object" ? value.code : value;
      dispatch(setActiveRowIdIO(id));
      dispatch(setSelectedOrderTypeIO(selectedCode));
      setSelectedTab(0);
    }
  };

  const handleRowClick = (params) => {
    const clickedRow = params.row;
    const orderTypeValue = typeof clickedRow.orderType === "object" ? clickedRow.orderType?.code : clickedRow.orderType;

    // Set active row
    dispatch(setActiveRowIdIO(clickedRow.id));
    setSelectedTab(0);

    // Normalize orderType into object form
    if (orderTypeValue) {
      dispatch(setSelectedOrderTypeIO(orderTypeValue));
    } else {
      dispatch(setSelectedOrderTypeIO(""));
    }
  };

  const handleDeleteRow = (rowId) => {
    const updatedRows = rows.filter((row) => row.id !== rowId);
    setRows(updatedRows);

    // Use deleteRowDataIO which removes from both header and body data, plus cleanup
    dispatch(deleteRowDataIO({ rowId: rowId }));

    // If the deleted row was the active row, clear the active row
    if (activeRowId === rowId) {
      dispatch(setActiveRowIdIO(null));
      dispatch(setSelectedOrderTypeIO(""));
    }
  };

  const toggleGridZoom = () => {
    setIsGridZoomed(!isGridZoomed);
  };

  const toggleTabsZoom = () => {
    setIsTabsZoomed(!isTabsZoomed);
    if (isGridZoomed); // optional: exit grid zoom when tabs zoom
  };
  // Handle page change
  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  // Simple columns definition
  const columns = [
    {
      field: "included",
      headerName: t("Included"),
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      editable: false,
      renderCell: (params) => (
        <Checkbox
          checked={params.row.included}
          onChange={(e) =>
            handleCellEdit({
              id: params.row.id,
              field: "included",
              value: e.target.checked,
            })
          }
        />
      ),
    },
    {
      field: "lineNumber",
      headerName: t("Line Number"),
      flex: 0.6,
      align: "center",
      headerAlign: "center",
      editable: false,
      renderCell: (params) => {
        const rowIndex = rows?.findIndex((row) => row?.id === params?.row?.id);
        const lineNumber = (rowIndex + 1) * 10;
        return <div>{lineNumber}</div>;
      },
    },
    {
      field: "internalOrder",
      headerName: t("Internal Order"),
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      editable: false,
      renderCell: (params) => {
        return (
          <TextField
            value={params.row.internalOrder || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "internalOrder",
                value: newValue,
              })
            }
            size="small"
            fullWidth
            variant="outlined"
            disabled="true"
            placeholder={t("Enter Internal Order")}
          />
        );
      },
    },
    {
      field: "controllingArea",
      headerName: t("Controlling Area"),
      renderHeader: () => (
        <span>
          {t("Controlling Area")}
          <span style={{ color: "red" }}>*</span>
        </span>
      ),
      flex: 0.7,
      align: "center",
      editable: false,
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={IODropdownData?.controllingArea || []}
            value={params.row.controllingArea || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "controllingArea",
                value: newValue,
              })
            }
            placeholder={t("Select Controlling Area")}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "orderType",
      headerName: t("Order Type"),
      renderHeader: () => (
        <span>
          {t("Order Type")}
          <span style={{ color: "red" }}>*</span>
        </span>
      ),
      flex: 0.7,
      editable: false,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            // options={IODropdownData?.orderType || []}
            options={orderTypeOptions}
            value={params.row.orderType || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "orderType",
                value: newValue,
              })
            }
            placeholder={t("Select Order Type")}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "description",
      headerName: t("Description"),
      renderHeader: () => (
        <span>
          {t("Description")}
          <span style={{ color: "red" }}>*</span>
        </span>
      ),
      flex: 0.7,
      editable: false,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => (
        <TextField
          value={params.row.description || ""}
          onChange={(e) =>
            handleCellEdit({
              id: params.row.id,
              field: "description",
              value: e.target.value,
            })
          }
          size="small"
          fullWidth
          variant="outlined"
          placeholder={t("Enter Description")}
        />
      ),
    },
    {
      field: "compCode",
      headerName: t("Comp Code"),
      renderHeader: () => (
        <span>
          {t("Comp Code")}
          <span style={{ color: "red" }}>*</span>
        </span>
      ),
      flex: 0.7,
      editable: false,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={IODropdownData?.compCode || []}
            value={params.row.compCode || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "compCode",
                value: newValue,
              })
            }
            placeholder={t("Select Company Code")}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "actions",
      headerName: t("Actions"),
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        let validateStatus = getValidationStatus(params?.row);

        const handleValidate = async (e) => {
          e.stopPropagation();
          await handleValidateRow(params.row);
        };

        return (
          <Stack direction="row" alignItems="center" sx={{ marginLeft: "0.5rem", marginRight: "0.5rem" }} spacing={0.5}>
            <Tooltip title={validateStatus === "success" ? "Validated Successfully" : validateStatus === "error" ? t("Validation Failed") : t("Click to Validate")}>
              <IconButton onClick={handleValidate} color={validateStatus === "success" ? "success" : validateStatus === "error" ? "error" : "default"}>
                {validateStatus === "error" ? <CancelOutlinedIcon /> : <TaskAltIcon />}
              </IconButton>
            </Tooltip>

            <Tooltip title={t("Delete Row")}>
              <IconButton onClick={() => handleDeleteRow(params.row.id)} color="error">
                <DeleteOutlineOutlinedIcon />
              </IconButton>
            </Tooltip>
          </Stack>
        );
      },
    },
  ];

  return (
    <div>
      <div style={{ padding: "0", width: "100%", margin: "0", marginTop: "20px" }}>
        <Box
          sx={{
            position: isGridZoomed ? "fixed" : "relative",
            top: isGridZoomed ? 0 : "auto",
            left: isGridZoomed ? 0 : "auto",
            right: isGridZoomed ? 0 : "auto",
            bottom: isGridZoomed ? 0 : "auto",
            width: isGridZoomed ? "100vw" : "100%",
            height: isGridZoomed ? "100vh" : "auto",
            zIndex: isGridZoomed ? 1004 : undefined,
            backgroundColor: isGridZoomed ? "white" : "transparent",
            padding: isGridZoomed ? "20px" : "0",
            display: "flex",
            flexDirection: "column",
            boxShadow: isGridZoomed ? "0px 0px 15px rgba(0, 0, 0, 0.2)" : "none",
            transition: "all 0.3s ease",
            borderRadius: "8px",
            border: "1px solid #e0e0e0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "8px 16px",
              backgroundColor: "#f5f5f5",
              borderRadius: "8px 8px 0 0",
            }}
          >
            <Typography variant="h6">{t("Internal Order List")}</Typography>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Button variant="contained" color="primary" size="small" onClick={handleAddRow}>
                + {t("Add")}
              </Button>
              <Tooltip title={isGridZoomed ? t("Exit Zoom") : t("Zoom In")} sx={{ zIndex: "1009" }}>
                <IconButton
                  onClick={toggleGridZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isGridZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
            <div style={{ height: "100%" }}>
              <DataGrid
                rows={rows}
                columns={columns}
                pageSize={50}
                autoHeight={false}
                page={page}
                rowsPerPageOptions={[50]}
                onPageChange={(newPage) => handlePageChange(newPage)}
                onRowClick={handleRowClick}
                pagination
                disableSelectionOnClick
                getRowClassName={(params) => (params.id === activeRowId ? "selected-row" : "")}
                style={{
                  border: "1px solid #ccc",
                  borderRadius: "8px",
                  width: "100%",
                  height: isGridZoomed ? "calc(100vh - 150px)" : `${Math.min(rows.length * 50 + 130, 300)}px`,
                  overflow: "auto",
                }}
                sx={{
                  "& .MuiDataGrid-cell": {
                    padding: "8px",
                  },
                  "& .MuiDataGrid-columnHeaders": {
                    backgroundColor: "#f5f5f5",
                    fontWeight: "bold",
                  },
                  "& .selected-row": {
                    backgroundColor: "rgb(234 233 255)",
                  },
                }}
              />
            </div>
          </div>
        </Box>
      </div>

      {/* Field Configuration Tabs Section */}
      {selectedOrderType && internalOrderTabs.length > 0 && (
        <Box
          sx={{
            position: isTabsZoomed ? "fixed" : "relative",
            top: isTabsZoomed ? 0 : "auto",
            left: isTabsZoomed ? 0 : "auto",
            right: isTabsZoomed ? 0 : "auto",
            bottom: isTabsZoomed ? 0 : "auto",
            width: isTabsZoomed ? "100vw" : "100%",
            height: isTabsZoomed ? "100vh" : "auto",
            zIndex: isTabsZoomed ? 1300 : "auto", // higher zIndex for full-screen components
            backgroundColor: isTabsZoomed ? "#fff" : "transparent",
            padding: isTabsZoomed ? "24px" : "0", // add padding to prevent top cut-off
            boxSizing: "border-box", // ensure padding is respected
            overflow: "auto", // prevent content from overflowing
            margin: isTabsZoomed ? 0 : "20px 0 0 0", // reset margin if zoomed
            display: "flex",
            flexDirection: "column",
            boxShadow: isTabsZoomed ? "0px 0px 15px rgba(0, 0, 0, 0.2)" : "none",
            borderRadius: "8px",
            border: "1px solid #e0e0e0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "8px 16px",
              backgroundColor: "#f5f5f5",
              borderRadius: "8px 8px 0 0",
            }}
          >
            <Typography variant="h6">{t("View Details")}</Typography>
            <Tooltip title={isTabsZoomed ? t("Exit Zoom") : t("Zoom In")}>
              <IconButton
                onClick={toggleTabsZoom}
                color="primary"
                sx={{
                  backgroundColor: "rgba(0, 0, 0, 0.05)",
                  "&:hover": {
                    backgroundColor: "rgba(0, 0, 0, 0.1)",
                  },
                }}
              >
                {isTabsZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
              </IconButton>
            </Tooltip>
          </Box>
          <Tabs
            value={selectedTab}
            onChange={(_, newValue) => setSelectedTab(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              borderBottom: 1,
              borderColor: "divider",
              "& .MuiTabs-indicator": {
                backgroundColor: "#3026B9",
                height: "3px",
              },
            }}
          >
            {internalOrderTabs.map((tab, index) => (
              <Tab key={index} label={tab.tab} />
            ))}
          </Tabs>
          <Paper elevation={2} sx={{ p: 3, borderRadius: 4, marginTop: 2 }}>
            {internalOrderTabs[selectedTab] && activeRowId && <GenericTabsGlobal disabled={false} basicDataTabDetails={internalOrderTabs[selectedTab].data} dropDownData={IODropdownData} activeViewTab={internalOrderTabs[selectedTab].tab} uniqueId={activeRowId} selectedRow={rows.find((row) => row.id === activeRowId) || {}} module={MODULE.IO} />}
          </Paper>
        </Box>
      )}

      {/* Bottom Navigation for Actions */}
      <BottomNavGlobal handleSaveAsDraft={handleSaveAsDraft} handleSubmitForReview={handleSubmitForReview} handleSubmitForApprove={handleSubmitForApprove} handleSendBack={handleSendBack} handleCorrection={handleCorrection} handleRejectAndCancel={handleRejectAndCancel} handleValidateAndSyndicate={handleValidateAndSyndicate} validateAllRows={validateAllRows} isSaveAsDraftEnabled={true} validateEnabled={true} filteredButtons={filteredButtons} moduleName={MODULE_MAP.IO} />

      {/* Loading and Notification Components */}
      {blurLoading && <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />}
    </div>
  );
};

export default RequestDetailsIO;
