import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  selectedSections: [],
};

const selectedSectionsSlice = createSlice({
  name: 'selectedSections',
  initialState,
  reducers: {
    setSelectedSections: (state, action) => {
      state.selectedSections = action.payload;
    },
  },
});

export const { setSelectedSections } = selectedSectionsSlice.actions;
export default selectedSectionsSlice.reducer;