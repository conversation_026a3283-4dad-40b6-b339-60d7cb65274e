import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  materialRows: [], // Initial empty rows array
  requestHeader:{},
  requestIdHeader: null,
  requestPrefix:"",
  allMaterials:[],
  tabValue:0,
  changeMaterial: {},
  salesOrgDTData : {
    uniqueSalesOrgList: [],
    salesOrgData : [],
  }
};

const requestSlice = createSlice({
  name: "request",
  initialState,
  reducers: {
    setMaterialRows: (state, action) => {
      state.materialRows = action.payload;
    },
    pushMaterialRows: (state, action) => {
      const newRows = action.payload;
      const existingIds = new Set(state.materialRows.map((row) => row.id));
      const uniqueRows = newRows.filter((row) => !existingIds.has(row.id));
      state.materialRows = [...state.materialRows, ...uniqueRows];
    },
    setUniqueSalesOrgData: (state, action) => {
      state.salesOrgDTData[action.payload.keyName] = action.payload.data;
      return state;
    },
    setRequestHeader:(state,action)=>{
      state.requestHeader=action.payload;
    },
    setRequestIdHeader: (state, action) => { // New reducer
      state.requestIdHeader = action.payload;
  },
  setRequestIdPrefix: (state, action) => { // New reducer
    state.requestPrefix = action.payload;
},
    setAllMaterials:(state,action)=>{
      state.allMaterials=action.payload;
    },

    setTabValue:(state,action)=>{
      state.tabValue=action.payload;
    },

    addToChangeMaterial: (state, action) => {
      const { uniqueId, data } = action.payload;

      // Add or update the object with the given uniqueId
      if (!state.changeMaterial[uniqueId]) {
        state.changeMaterial[uniqueId] = data;
      }
    },

    updateChangeMaterial: (state, action) => {
      const { materialID, keyName, data } = action.payload;

      if (materialID) {
        state.changeMaterial[materialID].Toclientdata[keyName] = data?.code ? data?.code : data ?? "";
      }
      // Set data if provided, else set an empty string

      return state;
    },

    updateMaterial: (state, action) => {
      const { materialID, keyName, data } = action.payload;

      if (materialID) {
        state.changeMaterial[materialID][keyName] = data ?? "";
      }
      // Set data if provided, else set an empty string

      return state;
    },

  },
});

export const { setMaterialRows, pushMaterialRows, setUniqueSalesOrgData, setRequestHeader,setRequestIdHeader,setRequestIdPrefix,setAllMaterials,setTabValue,addToChangeMaterial,updateChangeMaterial,updateMaterial } = requestSlice.actions;
// export default requestSlice.reducer;
export const requestSliceReducer = requestSlice.reducer;
