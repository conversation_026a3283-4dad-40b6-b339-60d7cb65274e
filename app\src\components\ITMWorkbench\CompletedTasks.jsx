// import configData from "../../data/configData";
// import destinationData from "../../data/destinationData";
// import { returnUserGroupMap } from "../../data/userGroupData";
// import { returnUserMap } from "../../data/userData";
// import { accessToken, userPermissions } from "../../data/propData";
// import Workspace from "@cw/cherrywork-itm";
import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
// import { destination_IWA } from "../../destinationVariables";
import WorkspaceComponent from "@cw/cherrywork-iwm-workspace/Workspace";
import configDataForCompletedTasks from "../../data/configDataForCompletedTasks";
import destinationData from "../../data/destinationData";
import { returnUserGroupMap } from "../../data/userGroupData";
import { returnUserMap } from "../../data/userData";
import { accessToken, userData, userPermissions } from "../../data/propData";
import { APP_END_POINTS } from "../../constant/appEndPoints";


// Workflow imports
import Workflow from "@cw/cherrywork-iwm-workspace/Workflow";
// import destinationData from "../../data/destinationData";
// import { userRawData, returnUserMap } from "../../data/userData";
// import { accessToken, userData } from "../../data/propData";
// import { task } from "../../data/taskData";

// Activity Logs imports
// import ActivityLogs from "@cw/cherrywork-itm-workspace/ActivityLogs";
// import destinationData from "../../data/destinationData";
// import { userRawData, returnUserMap } from "../../data/userData";
// import { accessToken, userData } from "../../data/propData";
import { task } from "../../data/taskData";
import { useNavigate } from "react-router-dom";
import { setTaskData } from "../../app/userManagementSlice";
import { baseUrl_ITMJava } from "../../data/baseUrl";
import { doAjax } from "../Common/fetchService";
import { destination_IWA_NPI } from "../../destinationVariables";
import configData from "../../data/configData";
import { commonSearchBarUpdate } from "@app/commonSearchBarSlice";

export default function CompletedTasks() {
    let userData = useSelector((state) => state.userManagement?.userData);
    let groupData = useSelector((state) => state.userManagement?.groups);
    const applicationConfig = useSelector((state) => state.applicationConfig);
    let dispatch = useDispatch()
    const navigate = useNavigate();
    const [userRawData, setUserRawData] = useState(null);
    const [userGroupRawData, setUserGroupRawData] = useState(null);

    const DestinationConfig = {
      APPLICATION_NAME: "1784",
      CRUD_API_ENV: "itm",
      DB_TYPE: "hana",
      SERVICE_BASE_URL: [
        {
          Description: "",
          Name: "ITMJavaServices",
          URL: "https://ca-gbd-ca-caf-cw-caf-iwm.cfapps.us10-001.hana.ondemand.com",
        },
        // {
        //   Description: "",
        //   Name: "IWAServices",
        //   URL: "https://cw-mdg-authentication-dev.cfapps.eu10-004.hana.ondemand.com",
        // },
        {
          Description: "",
          Name: "ConfigServer",
          URL: "https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com",
        },
        {
          Description: "",
          Name: "WorkNetServices",
          URL: "https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com",
        },
        {
          Description: "",
          Name: "CrudApiServices",
          URL: "https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com",
        },
        {
          Description: "",
          Name: "WorkFormsServices",
          URL: "https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms",
        },
        {
          Description: "",
          Name: "NotificationServices",
          URL: "https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com",
        },
        {
          Description: "",
          Name: "ITMGraphServices",
          URL: "https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com",
        },
        {
          Description: "Native Workflow Services",
          Name: "NativeWorkflowServices",
          URL: "https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com",
        },
        {
          Description: "Native Workflow UI URL",
          Name: "NativeWorkflowUiUrl",
          URL: "https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui",
        },
        {
          Description: "",
          Name: "OnboardingServices",
          URL: "https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com",
        },
      ],
    };

      const userPrefData = {
        // "Language": {
        //   "value": "English",
        //   "key": "EN"
        // },
        // "AppConfiguration": {
        //   "title": "Intelligent Work Management"
        // },
        // "Theme": {
        //   "key": "blueTheme"
        // },
        DateTimeFormat: {
          dateTimeFormat: "DD MMM YYYY||HH:mm",
          timeZone: "Asia/Calcutta",
        },
      };

    const onTaskClick = (task) => {
      dispatch(
        commonSearchBarUpdate({
          module: "RequestHistory",
          filterData: {
            reqId: task?.ATTRIBUTE_1,
          },
        })
      );
      navigate(APP_END_POINTS.REQUEST_HISTORY);
    };
    const fetchFilterViewList = () => {
        console.log("fetchFilterView")
    }
    const clearFilterView = () => {
        console.log("clearFilterView")
    }
    const fetchUserRawData = () => {
        doAjax(
            `/${destination_IWA_NPI}/api/v1/usersMDG/getUsersMDG`,
            "get",
            (resData) => {
                var tempData = resData.data;
                var tempUserData = tempData?.map((udata) => {
                    return { ...udata, userId: udata?.emailId };
                });
                var finalData = { ...resData, data: tempUserData };
                setUserRawData(finalData);
            }
        );
    };

    const fetchUserGroupRawData = () => {
        doAjax(`/${destination_IWA_NPI}/api/v1/groupsMDG/getAllGroupsMDG`, "get", (resData) => {
            var tempData = resData.data;
            var tempGroupData = tempData?.map((gData) => {
                return { ...gData, groupName: gData?.name };
            });
            var finalData = { ...resData, data: tempGroupData };
            setUserGroupRawData(finalData);
        });
    };

    const onActionComplete = (successFlag, taskPayload) => {
        console.log("Success flag.", successFlag);
        console.log("Task Payload.", taskPayload);
    };


    useEffect(() => {
        fetchUserRawData();
        fetchUserGroupRawData();
    }, []);
    return (
        <div
            style={{ width: "calc(100vw - 105px)", height: "calc(100vh-130px)" }}
            className={"workspaceOverride"}
        >
            {/* {userRawData && userGroupRawData && ( */}
                <>
                    <WorkspaceComponent
                        token={"eyJhbGciOiJSUzI1NiIsImprdSI6Imh0dHBzOi8vY2EtZ2JkLmF1dGhlbnRpY2F0aW9uLnVzMTAuaGFuYS5vbmRlbWFuZC5jb20vdG9rZW5fa2V5cyIsImtpZCI6ImRlZmF1bHQtand0LWtleS1kZjk5ODA5MzZhIiwidHlwIjoiSldUIiwiamlkIjogIlNMVzNNUjQ5UDZTMGJwWVJNOWJJZVQ3Z0Q1aDh2SkVpM1ZrOFVEeUVhY3c9In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UiGLR24hLDpu661TnyEfCOSMqY5SAjZTwuJbPANHuTXVwZEZwU0y6G3kMkRFn-9NgvxNEkTwIY2vjipbsFvahSnQHeze4doZlQDTU4lP2m_pGCBsXNF3R8kgpdTt_zFPbAwpK0xNz7dA79vVL-J0cPrFhqEWxf6wLQf4L--o150tb69-dFCsOfPP_O6Kuaw5DMvtfHm9Toe4RhBSVJj1zKrhGn-M-7rNl1wUDuc0WTvJeB7yMHz3Q7MtigXj8bdTtjuH_dyLqLVUdnNPnz8HY9EjShPCqAuBtfiqs_Tb167DZyeiaqJCDg5ZmLKtrKQZUdBGuAvMq2ZgkFktjMh72g"}
                        configData={configData} 
                        destinationData={DestinationConfig}
                        userData={{ ...userData, user_id: userData?.emailId }}
                        
                        userPermissions={userPermissions}
                        userList={returnUserMap(userRawData)} 
                        groupList={returnUserGroupMap(userGroupRawData)} 
                        useWorkAccess={
                          applicationConfig.environment === "localhost" ? true : false
                        } 
                        useConfigServerDestination={
                          applicationConfig.environment === "localhost" ? true : false
                        }
                        inboxTypeKey={"MY_COMPLETED_TASKS"} 
                        workspaceLabel={"Completed Tasks"}
                        subInboxTypeKey={null}
                        onTaskClick={onTaskClick}
                        onActionComplete={onActionComplete}
                        workspaceFiltersByAPIDriven={false}
                        isFilterView={false}
                        savedFilterViewData={[]}
                        selectedFilterView={null}
                        fetchFilterViewList={fetchFilterViewList}
                        clearFilterView={clearFilterView}
                        cachingBaseUrl={
                            baseUrl_ITMJava
                        }
                        selectedTabId={null}
                        externalSystems={[]}
                    />
                </>
                {/* )} */}
        </div>
    );
}
