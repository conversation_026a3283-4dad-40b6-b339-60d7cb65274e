

import React, { useEffect, useRef, useState } from "react";
import {
  Typography,
  IconButton,
  Grid,
  Box,
  Stack,
  Tabs,
  Tab,
  Paper,
  Card,
  CardContent,
  Divider,
} from "@mui/material";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import {
  iconButton_SpacingSmall,
  outermostContainer_Information,
} from "@components/Common/commonStyles";
import { doAjax } from "@components/Common/fetchService";
import {
  destination_ProfitCenter_Mass,
} from "../../destinationVariables";
import { makeStyles } from "@mui/styles";
import InventoryIcon from "@mui/icons-material/Inventory";
import BusinessIcon from "@mui/icons-material/Business";
import CategoryIcon from "@mui/icons-material/Category";
import DescriptionIcon from "@mui/icons-material/Description";
import { useDispatch, useSelector } from "react-redux";
import {
  pushMaterialDisplayData,
  setAdditionalData,
  setUOmData,
} from "../../app/payloadslice";
import { ERROR_MESSAGES, MODULE_MAP } from "@constant/enum";
import { colors } from "@constant/colors";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import useProfitCenterFieldConfig from "@hooks/useProfitCenterFieldConfig";
import GenericTabsGlobal from "@components/MasterDataCockpit/GenericTabsGlobal";
import { convertSAPDateForCalendar } from "@helper/helper";
import useLogger from "@hooks/useLogger";
import useLang from "@hooks/useLang";

const RenderRow = ({ label, value, icon }) => (
  <Grid item xs={6}>
    <Stack flexDirection="row" alignItems="center" spacing={1}>
      {icon && <Box>{icon}</Box>}
      <Typography variant="body2" color={colors.secondary.grey}>
        {label}
      </Typography>
      <Typography variant="body2" fontWeight="bold">
        : {value || ""}
      </Typography>
    </Stack>
  </Grid>
);

const DisplayProfitCenter = () => {
  const useStyles = makeStyles(() => ({
    customTabs: {
      "& .MuiTabs-scroller": {
        overflowX: "auto !important",
        overflowY: "hidden !important",
      },
    },
  }));

  const profitCenterTabs = useSelector((state) => {
    const tabs = state.profitCenter.profitCenterTabs || [];
    return tabs.filter((tab) => tab.tab !== "Initial Screen" && tab.tab !== "History");
  });

  const { loading, error, fetchProfitCenterFieldConfig } = useProfitCenterFieldConfig();
  const navigate = useNavigate();
  const classes = useStyles();
  const location = useLocation();
  const dispatch = useDispatch();
  const structureData = location.state;
  const { customError } = useLogger();
  const { t } = useLang();

  const [tabNames, setTabNames] = useState([]);
  const [tabContentData, setTabContentData] = useState([]);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedRow, setSelectedRow] = useState(null);
  const [selectedRowId, setSelectedRowId] = useState("");
  const hasFetchedData = useRef(false); // NEW: For fetching only once
const hasMappedTabs = useRef(false); 

  useEffect(() => {
    if (!profitCenterTabs?.length) {
      fetchProfitCenterFieldConfig();
      
    }
  }, []);

useEffect(() => {
  if (!hasFetchedData.current && structureData?.controllingArea && structureData?.profitCenter) {
    hasFetchedData.current = true; // prevent re-fetching
    fetchProfitCenterData();
  }
}, [structureData?.controllingArea, structureData?.profitCenter]);
 
  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const mapTabValuesToConfig = (tabs, tabContentData) => {
    const dtoMap = {
      "Basic Data": tabContentData?.basicDataTabDto,
      Indicators: tabContentData?.indicatorsTabDto,
      "Comp Codes": tabContentData?.compCodesTabDto,
      Address: tabContentData?.addressTabDto,
      Communication: tabContentData?.communicationTabDto,
      History: tabContentData?.historyTabDto,
    };

    return tabs.map((tab) => {
      const tabLabel = tab.tab;
      const dto = dtoMap[tabLabel];
      if (!dto) return tab;

      const updatedData = {};
      for (const cardKey in tab.data) {
        updatedData[cardKey] = tab.data[cardKey].map((field) => {
          const jsonKey = field.jsonName;
          let value = dto?.[jsonKey];

          if (field.dataType === "Date" && typeof value === "string" && value.includes("/Date")) {
            value = convertSAPDateForCalendar(value);
          }

          if (typeof value === "boolean") {
            value = value ? "TRUE" : "FALSE";
          }

          return {
            ...field,
            value: value ?? field.value,
          };
        });
      }

      return {
        ...tab,
        data: updatedData,
      };
    });
  };

   // Ref to avoid re-execution
  
  useEffect(() => {
  const tabsReady = profitCenterTabs?.length > 0;
  const dataReady = tabContentData && Object.keys(tabContentData).length > 0;

  if (tabsReady && dataReady && !hasMappedTabs.current) {
    hasMappedTabs.current = true;

    const updatedTabs = mapTabValuesToConfig(profitCenterTabs, tabContentData);
    setTabNames(updatedTabs);
  }
}, [profitCenterTabs, tabContentData]);


  const fetchProfitCenterData = () => {
    setBlurLoading(true);

    const payload = {
      coAreaPCs: [
        {
          controllingArea: structureData?.controllingArea,
          profitCenter: structureData?.profitCenter,
        },
      ],
    };

    const hSuccess = (data) => {
      setBlurLoading(false);
      const rawData = data?.body?.[0] || {};
      setTabContentData(rawData);
    };

    const hError = (error) => {
      console.error("Error fetching profit center data", error);
      setBlurLoading(false);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCentersData`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  

  return (
  <div style={{ backgroundColor: "#FAFCFF" }}>
    {/* Header Section */}
    <Grid container sx={outermostContainer_Information}>
      <Grid item md={12} sx={{ padding: "16px", display: "flex" }}>
        <Grid md={9} sx={{ display: "flex" }}>
          <IconButton color="primary" sx={iconButton_SpacingSmall} onClick={() => navigate(-1)}>
            <ArrowCircleLeftOutlinedIcon sx={{ fontSize: "25px", color: "#000000" }} />
          </IconButton>
          <Grid item md={12}>
            <Typography variant="h3">
              <strong>{t("Display Profit Center")}</strong>
            </Typography>
            <Typography variant="body2" color="#777">
              {t("This view displays the details of the Profit Centers")}
            </Typography>
          </Grid>
        </Grid>
      </Grid>
    </Grid>

    {/* Top Info Card (mimicking the two-column layout) */}
    <Grid
      container
      display="flex"
      flexDirection="row"
      flexWrap="nowrap"
      sx={{
        justifyContent: "space-between",
        alignItems: "center",
        paddingLeft: "29px",
        backgroundColor: colors.basic.lighterGrey,
        borderRadius: "10px",
        boxShadow: "0 2px 8px rgba(0,0,0,0.06)",
      }}
    >
      {/* Left Side */}
      <Stack
        width="48%"
        spacing={1}
        sx={{
          padding: "10px 15px",
          borderRight: "1px solid #eaedf0",
        }}
      >
        <Grid item>
          <RenderRow
            label={t("Controlling Area")}
            value={structureData?.controllingArea || ""}
            labelWidth="35%"
            icon={<InventoryIcon sx={{ color: colors.blue.indigo, fontSize: "20px" }} />}
          />
        </Grid>
        <Grid item>
          <RenderRow
            label={t("ProfitCenter Number")}
            value={structureData?.profitCenter || ""}
            labelWidth="35%"
            icon={<BusinessIcon sx={{ color: colors.blue.indigo, fontSize: "20px" }} />}
          />
        </Grid>
      </Stack>

      {/* Right Side */}
      <Stack
        width="48%"
        spacing={1}
        marginRight={"-10%"}
        sx={{
          padding: "10px 15px",
        }}
      >
        <Grid item>
          <RenderRow
            label={t("Short Description")}
            value={structureData?.ProfitCenterName || ""}
            labelWidth="35%"
            icon={<CategoryIcon sx={{ color: colors.blue.indigo, fontSize: "20px" }} />}
          />
        </Grid>
        <Grid item>
          <RenderRow
            label={t("Long Description")}
            value={structureData?.longText || ""}
            labelWidth="35%"
            icon={<DescriptionIcon sx={{ color: colors.blue.indigo, fontSize: "20px" }} />}
          />
        </Grid>
      </Stack>
    </Grid>

    {/* Tabs Section */}
    <Grid>
      {tabNames.length > 0 ? (
        <Box sx={{ mt: 3 }}>
          <Tabs
            value={selectedTab}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
            sx={{ borderBottom: 1, borderColor: "divider", mb: 2 }}
          >
            {tabNames.map((tab, index) => (
              <Tab key={index} label={tab.tab} />
            ))}
          </Tabs>
          
          <Paper elevation={2} sx={{ p: 3, borderRadius: 8 }}>
            {tabNames[selectedTab] && (
              <GenericTabsGlobal
                disabled={false}
                basicDataTabDetails={tabNames[selectedTab].data}
                dropDownData={""}
                activeViewTab={tabNames[selectedTab].tab}
                uniqueId={selectedRowId}
                selectedRow={selectedRow || {}}
                module={MODULE_MAP?.PC}
              />
            )}
          </Paper>
        </Box>
      ) : (
        <Box
          sx={{
            marginTop: "30px",
            border: `1px solid ${colors.secondary.grey}`,
            padding: "16px",
            background: `${colors.primary.white}`,
            textAlign: "center",
          }}
        >
          <span>{ERROR_MESSAGES.NO_DATA_AVAILABLE}</span>
        </Box>
      )}
    </Grid>

    {/* Loader */}
    <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
  </div>
);

};

export default DisplayProfitCenter;
