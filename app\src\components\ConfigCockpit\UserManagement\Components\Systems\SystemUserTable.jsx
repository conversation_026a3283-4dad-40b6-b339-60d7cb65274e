import React from "react";
import { Typography } from "@mui/material";
import Loading from "../Loading";
import ReusableTable from "../../../../common/ReusableTable";

const userColumns = [
  {
    field: "displayName",
    headerName: "Name",
    width: 200,
  },
  {
    field: "userId",
    headerName: "User ID",
    width: 80,
  },
  {
    field: "emailId",
    headerName: "Email ID",
    editable: false,
    flex: 1,
  },
  {
    field: "roleName",
    headerName: "Roles",
    editable: false,
    flex: 1,
    renderCell: (cellValues) => {
      return cellValues.row.roleName.toString();
    },
  },
  {
    field: "requestDetails",
    headerName: "Request Details",
    sortable: false,
    flex: 1,
    renderCell: (cellValues) => {
      return (
        <div>
          <div>
            <Typography variant="subtitle">
              Created On: &nbsp;
              {cellValues.row.reqCreatedOn === null
                ? "-"
                : cellValues.row.reqCreatedOn}
            </Typography>&nbsp;&nbsp;&nbsp;&nbsp;
            <Typography variant="subtitle">
              Updated On: &nbsp;
              {cellValues.row.reqApprovedOn === null
                ? "-"
                : cellValues.row.reqApprovedOn}
            </Typography>
          </div>
          <div>
            <Typography variant="subtitle">
            Raised By: &nbsp;
              {cellValues.row.reqCreatedBy === null
                ? "-"
                : cellValues.row.reqCreatedBy}
            </Typography>&nbsp;&nbsp;&nbsp;&nbsp;
            <Typography variant="subtitle">
              Approvd By: &nbsp;
              {cellValues.row.reqApprovedBy === null
                ? "-"
                : cellValues.row.reqApprovedBy}
            </Typography>
          </div>
        </div>
      );
    },
  },
];

function SystemUserTable({ filteredUsers, load, height }) {
  return (
    <>
      <Loading load={load} />
      {filteredUsers && (
        <ReusableTable
          // isLoading={isLoading}
          module={"userManagement"}
          width="100%"
          rows={filteredUsers}
          columns={userColumns}
          hideFooter={false}
          getRowIdValue={"userId"}
        />
      )}
    </>
  );
}

export default SystemUserTable;
