import { useEffect } from "react";
import { useDispatch } from "react-redux";
import useDropdownMaterialData from "@material/hooks/useDropdownMaterialData";
import useDropdownFMDData from "@modules/modulesHooks/useDropdownFMDData.js";
import { setOdataApiCall as setMaterialOdataApiCall } from "@material/slice/materialDropdownSlice";
import { setOdataApiCall as setProfitCenterOdataApiCall, setDropDown as profitCenterDropDownAction } from "@profitCenter/slice/profitCenterDropdownSlice";
import { setDropDown as costCenterDropDownAction, setOdataApiCall as setCostCenterOdataApiCall } from "@costCenter/slice/costCenterDropDownSlice";
import { setDropDown as generalLedgerDropDownAction, setOdataApiCall as setGeneralLedgerOdataApiCall } from "@generalLedger/slice/generalLedgerDropDownSlice";
import { getLocalStorage } from "@helper/helper";
import { LOCAL_STORAGE_KEYS, MODULE_MAP } from "@constant/enum";
import { destination_ProfitCenter_Mass, destination_CostCenter_Mass, destination_GeneralLedger_Mass } from "../../destinationVariables";

const useModuleOdataHandler = () => {
  const dispatch = useDispatch();
  const { fetchAllDropdownMasterData } = useDropdownMaterialData();

  const profitCenterHook = useDropdownFMDData(destination_ProfitCenter_Mass, profitCenterDropDownAction);
  const costCenterHook = useDropdownFMDData(destination_CostCenter_Mass, costCenterDropDownAction);
  const generalLedgerHook = useDropdownFMDData(destination_GeneralLedger_Mass, generalLedgerDropDownAction);

  const selectedModule = getLocalStorage(LOCAL_STORAGE_KEYS.MODULE);

  const moduleMap = {
    [MODULE_MAP.MAT]: {
      fetchData: () => {
        fetchAllDropdownMasterData();
        dispatch(setMaterialOdataApiCall(true));
      }
    },
    [MODULE_MAP.PC]: {
      fetchData: () => {
        profitCenterHook.fetchAllDropdownFMD("profitCenter");
        dispatch(setProfitCenterOdataApiCall(true));
      }
    },
    [MODULE_MAP.CC]: {
      fetchData: () => {
        costCenterHook.fetchAllDropdownFMD("costCenter");
        dispatch(setCostCenterOdataApiCall(true));
      }
    },
    [MODULE_MAP.GL]: {
      fetchData: () => {
        generalLedgerHook.fetchAllDropdownFMD("generalLedger");
        dispatch(setGeneralLedgerOdataApiCall(true));
      }
    }
  };

  useEffect(() => {
    const moduleHandler = moduleMap[selectedModule];
    if (moduleHandler) {
      moduleHandler.fetchData();
    }
  }, []);
};

export default useModuleOdataHandler;
