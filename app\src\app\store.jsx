import {documentApi} from "@api/document/DocumentApiService";
import { configureStore } from "@reduxjs/toolkit";
import rootReducer from "./rootReducer";
import {adminApi} from "@api/admin/AdminApiService";

const Store = configureStore({
  reducer: rootReducer,
   middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware()
   .concat(documentApi.middleware)
   .concat(adminApi.middleware)
});

export default Store;
