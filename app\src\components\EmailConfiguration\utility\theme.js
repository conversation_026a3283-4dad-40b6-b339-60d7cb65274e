// import { createTheme } from "@mui/material";
// import { makeStyles } from "@mui/styles";

// export const customTheme = createTheme({
//   // palette: {
//   //   primary: {
     
//   //     main: "rgba(48, 38, 185, 1)", //application primary theme colour
//   //     dark: "rgba(234, 233, 255, 1)", //lighter version of the application theme color
//   //     light: "#F1F5FE", //lighter version of the application theme color
//   //     secondary: "#E5E5E5", //Background color of the page
//   //   },
//   //   background: {
//   //     default: "#FF0000",
//   //     paper: "#FFFFFF",
//   //     navbar: "#FFFFFF",
//   //     accordionLight: "#F9FBFE",
//   //     chip1:"#E4F1FF !important",
//   //     chip2: "#FBEEFA !important",
//   //   },
//   //   common: {
//   //     white: "#FFFFFF",
//   //     black: "#1D1D11",
//   //   },
//   //   secondary: {
//   //     main: "#F1F5FE", //secondary brand colour ---- Cherrywork - red
//   //     light: "#ea9493", //lighter brand colour ---- Cherrywork - red
//   //     complementary: "#649b45", //lighter brand colour ---- Cherrywork - green
//   //   },
//   //   action: {
//   //     main: "#DBE9FF",
//   //   },
//   //   button: {
//   //     createTask: "#DBE9FF", //Create Task Button background colour
      
//   //   },
//   //   text: {
//   //     primary: "#1D1D11",
//   //     secondary: "#999A96", //For Pinned Task toggle - Grey colour
//   //     black: "#3F3F3F",
//   //     darkGrey: "#5E5E5E",
//   //     lightGrey: "#F2F2F2",
//   //     midGrey: "#757575",
//   //   },
//   // },
//   palette: {
//     primary: {
//       main: "#3b30c8",
//     },
//     secondary: {
//       main: "#2cbc34",
//     },
//     danger: {
//       main: "#DA2C2C",
//     },
//     neutral: {
//       main: "#eae9ff",
//     },
//     success: {
//       main: "#2cbc34",
//     }
//   },
//   background: {
//     default: "#FAFCFF"
//   },
//   components:{
//     MuiButton:{
//       // minWidth: "max-content",
     
//     },
//     MuiButtonBase:{
//       minWidth:'10rem',
//       padding: "6px 12px",
//       textTransform: "capitalize",
//       height: ".1rem",
//     }
//   },
//   typography: {
//     h3: {
//       fontSize: "20px",
//       fontWeight: "600",
//       color: "#1d1d1d",
//     },
//     h4: {
//       fontSize: "18px",
//       fontWeight: "600",
//       color: "#1d1d1d",
//     },
//     h5: {
//       fontSize: "16px",
//       fontWeight: "600",
//       color: "#1d1d1d",
//     },
//     h6: {
//       fontSize: "14px",
//       fontWeight: "600",
//       color: "#1d1d1d",
//     },
//     body1: {
//       fontSize: "14px",
//     },
//     body2: {
//       fontSize: "12px",
//     },
//     caption: {
//       fontSize: "10px",
//     },
//     button: {
//       textTransform: "none",
//     },
//   },
//   props: {
//     // Name of the component
//     MuiButtonBase: {
//       // The properties to apply
//       disableRipple: true, // No more ripple, on the whole application!
//     },

//     MuiChip: {
//       styleOverrides: {
//         colorPrimary: {
//           backgroundColor: 'red',
//         },
//         colorSecondary: {
//           backgroundColor: 'brown',
//         },
//       },
//     },

//   },
//   shape: {
//     borderRadius: 4,
//   },
//   // typography: {
//   //   button: {
//   //     textTransform: "none",
//   //   },
//   //   caption: {
//   //     lineHeight: "14px",
//   //   },
//   //   caption2: {
//   //     fontSize: "0.625rem !important", //10
//   //     fontWeight: "normal",
//   //     lineHeight: "14px",
//   //   },
//   //   h0:{
//   //   fontWeight:600,
//   //   fontSize:"1.25rem",
//   //   },

//   //   h1: {
//   //     fontWeight: 500,
//   //     fontSize: "1.2rem",
//   //   },
//   //   h2: {
//   //     fontWeight: 550,
//   //     fontSize: "1rem",
//   //   },
//   //   h3: {
//   //     fontSize: "0.9rem",
//   //     fontWeight: 500,
//   //   },
//   //   h4: {
//   //     fontSize: "0.8rem",
//   //     fontWeight: 500,
//   //   },
//   //   h5: {
//   //     fontSize: "0.6rem",
//   //     fontWeight: 450,
//   //   },
//   //   h6: {
//   //     fontSize: "0.6rem",
//   //     fontWeight: 400,
//   //   },

//   // },
// });

// export const useCustomStyles = makeStyles((theme) => {
//   return {
//     customButton: {
//       fontWeight: "400 !important",
//       boxShadow: "none !important",
//       // "& .MuiButton-sizeSmall": {
//       //   height: `${theme.spacing(4)} !important`,
//       //   fontSize: "14 !important",
//       // },
//       // "& .MuiButton-sizeMedium": {
//       //   height: theme.spacing(4.5),
//       //   fontSize: 14,
//       // },
//       // "& .MuiButton-sizeLarge": {
//       //   height: theme.spacing(5),
//       //   fontSize: 16,
//       // },
//       // "&.MuiButton-containedSecondary": {
//       //   color: theme.palette.primary.main,
//       //   backgroundColor: theme.palette.primary.dark,
//       //   // "&:hover": {
//       //   //   backgroundColor: theme.palette.primary.dark,
//       //   //   boxShadow: "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
//       //   // },
//       // },
//       // "&.MuiButton-containedPrimary": {
//       //   backgroundColor: theme.palette.primary.main,

//       //   // "&:hover": {
//       //   //   backgroundColor: theme.palette.primary.main,
//       //   //   boxShadow: "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
//       //   // },
//       // },
//       // "&.MuiButton-containedAction": {
//       //   color: theme.palette.primary.main,
//       //   backgroundColor: theme.palette.action.main,
//       //   // "&:hover": {
//       //   //   backgroundColor: theme.palette.action.main,
//       //   //   boxShadow: "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
//       //   // },
//       // },
//     },
//     customAutoComplete: {
//       "& .MuiAutocomplete-option, .MuiAutocomplete-popper": {
//         zIndex: 4,
//       },
//       "& .MuiOutlinedInput-input": {
//         zIndex: 2,
//         padding: `${theme.spacing(0, 0, 0, 1)} !important`,
//       },
//       "& .MuiInputBase-sizeSmall": {
//         height: theme.spacing(4),
//         fontSize: 14,
//       },
//       "& .MuiInputBase-sizeMedium": {
//         height: theme.spacing(5),
//         fontSize: 14,
//       },
//       "& .MuiInputBase-sizeLarge": {
//         height: theme.spacing(6),
//         fontSize: 16,
//       },
//       "& .MuiAutocomplete-endAdornment": {
//         zIndex: 2,
//         "& .MuiIconButton-root": {
//           background: "transparent",
//           color: "#495057",
//         },
//       },
//       "&. MuiOutlinedInput-notchedOutline": {
//         zIndex: "1",
//         border: "1px solid #ced4da",
//         "& legend": {
//           width: "0%",
//         },
//       },
//     },
//     option: {
//       "& .MuiAutocomplete-option, .MuiAutocomplete-popper": {
//         zIndex: 4,
//       },
//       zIndex: 4,
//     },
//   };
// });