import React, { useEffect, useState, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { destination_BankKey, destination_IDM } from "../../destinationVariables";
import { v4 as uuidv4 } from "uuid";
import { doAjax } from "@components/Common/fetchService";
import useButtonDTConfig from "@hooks/useButtonDTConfig";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import ReusableDataTable from "@components/Common/ReusableTable";
import FlexibleValidationDialog from "@components/Common/FlexibleValidationDialog";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { useLocation, useNavigate } from "react-router-dom";
import {
  TextField,
  IconButton,
  Box,
  Typography,
  Paper,
  Button,
  Tabs,
  Tab,
  FormControl,
  Radio,
  RadioGroup,
  FormControlLabel,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Grid,
  Checkbox,
  Tooltip,
  FormLabel,
} from "@mui/material";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import TaskAltIcon from "@mui/icons-material/TaskAlt";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import GenericTabsGlobal from "@components/MasterDataCockpit/GenericTabsGlobal";
import { colors } from "@constant/colors";
import { setRowsHeaderData, updateModuleFieldDataBK, setOpenDialog, setSelectedRowID, setRowsBodyData, setDropDownDataBNKY, setBankKeyPayload, setBankKeyPayloadIndividual } from "./bnkySlice";
import { createPayloadForBK, transformApiResponseToReduxPayloadBk } from "../../functions";
import BottomNavGlobal from "@components/RequestBench/RequestPages/BottomNavGlobal";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import CropFreeIcon from "@mui/icons-material/CropFree";
import CloseFullscreenIcon from "@mui/icons-material/CloseFullscreen";
import useLang from "@hooks/useLang";
import CustomDialog from "@components/Common/ui/CustomDialog";
import useBankKeyFieldConfig from "./hooks/useBankKeyFieldConfig";
import { BUTTON_NAME, ENABLE_STATUSES, MODULE, MODULE_MAP, REQUEST_STATUS } from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import { checkIncludedAndValidated, getValidationStatus } from "../../helper/helper";
import { useSnackbar } from "../../hooks/useSnackbar";
import { BK_HEADER_MANDATORY, DELETE_MODAL_BUTTONS_NAME, DIALOUGE_BOX_MESSAGES, REGION_CODE, VALIDATION_STATUS } from "../../constant/enum";
import { button_Outlined, button_Primary } from "../../components/common/commonStyles";
import useDynamicWorkflowDT from "@hooks/useDynamicWorkflowDT";

const BankKeyListDetails = ({
  setIsAttachmentTabEnabled,
  setCompleted,
  requestStatus,
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useLang();

  let task = useSelector((state) => state?.userManagement.taskData);
  const { selectedRowID, mandatoryFields } = useSelector((state) => state.bankKey);
  const bankKeyData = useSelector((state => state.bankKey));
  const bankKeyTabs = useSelector((state) => state.bankKey.bankKeyTabs || []);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isrequestId = queryParams.get("RequestId");
  const reqBench = queryParams.get("reqBench");
  const reqBenchData = location?.state || {};
  const openDialog = useSelector((state) => state.bankKey.isOpenDialog);
  const { loading, error, fetchBankKeyFieldConfig } = useBankKeyFieldConfig();
  const rowsHeaderData = useSelector((state) => state.bankKey.payload.rowsHeaderData);
  const rowsBodyData = useSelector((state) => state.bankKey.payload?.rowsBodyData || {});
  const initialPayload = useSelector((state) => state.bankKey.payload?.requestHeaderData || {});
  const reduxPayload = useSelector((state => state.bankKey.payload));
  const requestType = reduxPayload?.requestHeaderData?.RequestType;
  const changeLogBK = useSelector((state) => state.changeLog.createChangeLogDataBK || {});
  const filteredButtons = useSelector((state) => state.payload.filteredButtons);
  const [selectedRow, setSelectedRow] = useState(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [dropdownDataRegion, setDropdownDataRegion] = useState([]);
  const [missingFieldsDialogOpen, setMissingFieldsDialogOpen] = useState(false);
  const [missingFields, setMissingFields] = useState([]);
  const [isAddRowEnabled, setIsAddRowEnabled] = useState(false);
  const [isSaveAsDraftEnabled, setIsSaveAsDraftEnabled] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const { getButtonsDisplayGlobal, showWfLevels } = useButtonDTConfig();
  const [isGridZoomed, setIsGridZoomed] = useState(false);
  const [isTabsZoomed, setIsTabsZoomed] = useState(false);
  const [withReference, setWithReference] = useState("yes");
  const [selectedBankCtry, setSelectedBankCtry] = useState("");
  const [selectedBankKey, setSelectedBankKey] = useState("");
  const [isDeleteDialogVisible, setIsDeleteDialogVisible] = useState({ data: {}, isVisible: false });
  const [newRowId, setNewRowId] = useState();
  const [customMessageDialog, setCustomMessageDialog] = useState({
    open: false,
    message: "",
    title: ""
  });
  const [isAddRowMode, setIsAddRowMode] = useState(false);
  const [isDropDownLoading, setIsDropDownLoading] = useState(false);
  const { showSnackbar } = useSnackbar();
  const isreqBench = queryParams.get("reqBench");
  const disableCheck = !ENABLE_STATUSES.includes(requestStatus) || (isrequestId && !isreqBench);
  let taskData = useSelector((state) => state.userManagement.taskData);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const [selectedLevel, setSelectedLevel] = useState('');
  const [wfLevels,setWfLevels] = useState([]);
  const { getDynamicWorkflowDT } = useDynamicWorkflowDT();
  useEffect(() => {
      const fetchWorkflowLevels = async () => {
        try {
          const workflowLevelsDtData = await getDynamicWorkflowDT(
            requestType,
            initialPayload?.Region,
            '',
            reduxPayload?.Tochildrequestheaderdata?.BankKeyGroupType,
            taskData?.ATTRIBUTE_3,
            "v1",
            "MDG_BNKY_DYNAMIC_WORKFLOW_DT",
            MODULE_MAP?.BK
          );
          setWfLevels(workflowLevelsDtData);
        } catch (err) {
          customError(err);
        }
      };
      if (requestType && initialPayload?.Region  && taskData?.ATTRIBUTE_3) {
        fetchWorkflowLevels();
      }
    }, [requestType, initialPayload?.Region,taskData?.ATTRIBUTE_3]);
  

  useEffect(() => {
    if (rowsHeaderData) {
      const allRowsValidated = checkIncludedAndValidated(rowsHeaderData);
      setIsAddRowEnabled(allRowsValidated);
    }
  }, [rowsHeaderData]);

  const handleBKCountry = (bkCountry) => {
    if (bkCountry) {
      const region = initialPayload?.Region || REGION_CODE.US;
      const keyToCheck = `${region}-${bkCountry}`;
      const bkCountryExists = keyToCheck in bankKeyTabs;

      if (!bkCountryExists) {
        fetchBankKeyFieldConfig(keyToCheck, bkCountry);
      }
    }
  }

  useEffect(() => {
    if (
      (isrequestId && !reqBench) ||
      (reqBench && reqBenchData?.reqStatus !== REQUEST_STATUS?.DRAFT) ||
      (reqBench && reqBenchData?.reqStatus === REQUEST_STATUS?.DRAFT && reqBenchData?.objectNumbers !== "Not Available")) {
      dispatch(setOpenDialog(false));
    }
  }, []);

  useEffect(() => {
    if (rowsHeaderData?.[0]?.id) {
      handleBKCountry(rowsHeaderData?.[0]?.BankCtry)
    }
  }, [rowsHeaderData])

  useEffect(() => {
    if (task?.ATTRIBUTE_1 || isrequestId) {
      getButtonsDisplayGlobal("Bank Key", "MDG_DYN_BTN_DT", "v3");
    }
  }, [task]);

  const handleValidate = async (row) => {
    const missing = [];

    BK_HEADER_MANDATORY?.forEach((key) => {
      let value = row[key.jsonName];
      if (
        value === null ||
        value === undefined ||
        (typeof value === "string" && value.trim() === "")
      ) {
        missing.push(key.fieldName);
      }
    });

    const objectLevelMandatoryFields = mandatoryFields?.[`${initialPayload?.Region}-${row?.BankCtry}`] || {}

    Object.keys(objectLevelMandatoryFields)?.forEach((key) => {
      const fields = objectLevelMandatoryFields[key];
      fields.forEach((field) => {
        let value = rowsBodyData?.[row.id]?.[field.jsonName];
        if (
          value === null ||
          value === undefined ||
          (typeof value === "string" && value.trim() === "")
        ) {
          missing.push(field.fieldName);
        }
      });
    });

    if (missing.length > 0) {
      const updatedRows = rowsHeaderData.map((item) =>
        item.id === row.id ? { ...item, validated: false } : item
      );
      dispatch(setRowsHeaderData(updatedRows));

      setMissingFields(missing);
      setMissingFieldsDialogOpen(true);
      return;
    }

    try {
      // Get BankCtry and BankKey values from the row
      const bankCtry = row.BankCtry
      const bankKey = row.BankKey
      const id = row.id

      if (bankCtry && bankKey) {
        const duplicateResult = await checkDuplicateBankDetails(bankCtry, bankKey);

        if (duplicateResult.isDuplicate) {
          // Update row status to error
          const updatedRows = rowsHeaderData.map((item) =>
            item.id === row.id ? { ...item, validated: false } : item
          );
          dispatch(setRowsHeaderData(updatedRows));

          // Show duplicate error in dialog
          setCustomMessageDialog({
            open: true,
            message: duplicateResult.message,
            title: "Duplicate Bank Details Found"
          });
          return;
        }
      }

      // Step 3: If both validations pass, mark as successful
      const updatedRows = rowsHeaderData.map((item) =>
        item.id === row.id ? { ...item, validated: true } : item
      );
      dispatch(setRowsHeaderData(updatedRows));

      showSnackbar("Validation Successful", "success");
      setCompleted([true, true]);
      setIsAttachmentTabEnabled(true);

      const allRowsValidated = checkIncludedAndValidated(updatedRows);
      setIsAddRowEnabled(allRowsValidated);

    } catch (error) {
      showSnackbar("Validation failed due to an error", "error");
    }
  };

  const checkDuplicateBankDetails = (bankCtry, bankKey) => {
    return new Promise((resolve, reject) => {
      const payload = [
        {
          bankCtry: bankCtry,
          bankKey: bankKey,
          requestNo: bankKeyData?.requestHeaderResponse?.requestId || initialPayload?.RequestId || "",
        },
      ];

      const successHandler = (data) => {
        if (data?.body?.length) {
          const errorMessage = `Duplicate bank details found: ${data.body[0].split("$^$")[0]} (${data.body[0].split("$^$")[1]})`;
          resolve({ isDuplicate: true, message: errorMessage });
        } else {
          resolve({ isDuplicate: false, message: "" });
        }
      };

      const errorHandler = (error) => {
        customError(error);
        resolve({ isDuplicate: false, message: "" });
      };

      let localDuplicateCount = 0;
      let duplicateMessage = "";

      rowsHeaderData?.forEach((key) => {
        if (key?.BankCtry && key?.BankKey) {
          if (key?.BankCtry === bankCtry && key?.BankKey === bankKey) {
            localDuplicateCount++;
          }
        }
      });

      if (localDuplicateCount > 1) {
        duplicateMessage = `Duplicate bank details found locally: ${bankCtry} - ${bankKey}`;
        resolve({ isDuplicate: true, message: duplicateMessage });
      } else {
        doAjax(
          `/${destination_BankKey}${END_POINTS.MASS_ACTION?.BANK_DUPLICATE_CHECK}`,
          "post",
          successHandler,
          errorHandler,
          payload
        );
      }
    });
  };

  const handleDelete = () => {
    const params = isDeleteDialogVisible?.data;
    dispatch(setRowsHeaderData(rowsHeaderData?.filter((row) => row.id !== params?.row?.id)));
    let localRowsBodyData = { ...rowsBodyData }
    delete localRowsBodyData[params?.row?.id]
    dispatch(setRowsBodyData(localRowsBodyData))
    setSelectedRow(rowsHeaderData?.[0])
    setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false });
  };

  const isFieldMandatory = (jsonName) => {
    return BK_HEADER_MANDATORY?.some(field => field.jsonName === jsonName);
  };

  const getBankKey = (value) => {
    setIsDropDownLoading(true)
    const url = `/${destination_BankKey}/data/getBankKeyBasedOnCountry`
    doAjax(
      url,
      "post",
      (data) => {
        setIsDropDownLoading(false)
        dispatch(setDropDownDataBNKY({
          keyName: "BankKey",
          data: data?.body || []
        }))
      },
      () => { setIsDropDownLoading(false) },
      { bankCtry: value }
    )
  }

  const columns = [
    {
      field: "included",
      headerName: "Included",
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => (
        <Checkbox
          checked={params.row.included}
          disabled={disableCheck}
          onChange={(e) =>
            handleRowInputChange(e.target.checked, params.row.id, "included")
          }
        />
      ),
    },
    {
      field: "lineNumber",
      headerName: "Line Number",
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        const rowIndex = rowsHeaderData.findIndex((row) => row.id === params.row.id);
        return <div>{(rowIndex + 1) * 10}</div>;
      },
    },
    {
      field: "BankCtry",
      flex: 1,
      headerAlign: "center",
      renderHeader: () => (
        <span>
          {t("Bank Ctry/Reg.")}
          {isFieldMandatory("BankCtry") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={bankKeyData?.dropDownData?.BankCtry || []}
            value={
              bankKeyData?.dropDownData?.BankCtry?.find(
                (item) => item.code === params.row.BankCtry
              ) || null
            }
            onChange={(newValue) =>
              handleRowInputChange(newValue?.code, params.row.id, "BankCtry", params)
            }
            placeholder={t("Select Bank Country")}
            disabled={disableCheck}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "BankKey",
      flex: 1,
      headerAlign: "center",
      renderHeader: () => (
        <span>
          {t("Bank Key")}
          {isFieldMandatory("BankKey") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        const value = params.row.BankKey || "";
        return (
          <Box sx={{ position: "relative", width: "100%" }}>
            <TextField
              value={value}
              onChange={(e) => {
                handleRowInputChange(
                  e.target.value.toUpperCase(),
                  params.row.id,
                  "BankKey"
                );
              }}
              variant="outlined"
              size="small"
              placeholder="Enter Bank Key"
              disabled={disableCheck}
              fullWidth
              sx={{
                "& .MuiInputBase-input": {
                  padding: "10px 14px",
                },
                "& .MuiInputBase-root.Mui-disabled": {
                  "& > input": {
                    WebkitTextFillColor: colors.black.dark,
                    color: colors.black.dark,
                  },
                },
              }}
            />

          </Box>
        );
      },
    },
    {
      field: "BankName",
      flex: 1,
      headerAlign: "center",
      renderHeader: () => (
        <span>
          {t("Bank Name")}
          {isFieldMandatory("BankName") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        const value = params.row.BankName || "";

        return (
          <TextField
            value={value}
            onChange={(e) => {
              handleRowInputChange(
                e.target.value.toUpperCase(),
                params.row.id,
                "BankName"
              );
            }}
            variant="outlined"
            size="small"
            placeholder="Enter Bank Name"
            disabled={disableCheck}
            fullWidth
            sx={{
              "& .MuiInputBase-input": {
                padding: "10px 14px",
              },
              "& .MuiInputBase-root.Mui-disabled": {
                "& > input": {
                  WebkitTextFillColor: colors.black.dark,
                  color: colors.black.dark,
                },
              },
            }}
          />
        );
      },
    },
    {
      field: "BankBranch",
      flex: 1,
      headerAlign: "center",
      renderHeader: () => (
        <span>
          {t("Bank Branch")}
          {isFieldMandatory("BankBranch") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        const value = params.row.BankBranch || "";

        return (
          <TextField
            value={value}
            onChange={(e) => {
              handleRowInputChange(
                e.target.value.toUpperCase(),
                params.row.id,
                "BankBranch"
              );
            }}
            variant="outlined"
            size="small"
            placeholder="Enter Bank Branch"
            disabled={disableCheck}
            fullWidth
            sx={{
              "& .MuiInputBase-input": {
                padding: "10px 14px",
              },
              "& .MuiInputBase-root.Mui-disabled": {
                "& > input": {
                  WebkitTextFillColor: colors.black.dark,
                  color: colors.black.dark,
                },
              },
            }}
          />
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      flex: 0.5,
      headerAlign: "center",
      align: "center",
      renderHeader: () => (
        <span style={{ fontWeight: "bold" }}>{t("Action")}</span>
      ),
      renderCell: (params) => {
        let validateStatus = getValidationStatus(params?.row);

        return (
          <Box>
            <Tooltip
              title={
                validateStatus === "success"
                  ? "Validated Successfully"
                  : validateStatus === "error"
                    ? "Validation Failed"
                    : "Click to Validate"
              }
            >
              <IconButton
              disabled={disableCheck}
                onClick={(e) => {
                  e.stopPropagation();
                  handleValidate(params.row);
                }}
                color={validateStatus === "success" ? "success" : validateStatus === "error" ? "error" : "default"}>
                {validateStatus === "error" ? <CancelOutlinedIcon /> : <TaskAltIcon />}
              </IconButton>
            </Tooltip>
            <Tooltip 
            title={t("Delete Row")}
            disabled={disableCheck}>
              <IconButton
                onClick={() => {
                  setIsDeleteDialogVisible({ ...isDeleteDialogVisible, data: params, isVisible: true });
                }}
                color="error"
              >
                <DeleteOutlineOutlinedIcon />
              </IconButton>
            </Tooltip>
          </Box>
        );
      },
    },
  ];

  const handleDialogClose = () => {
    dispatch(setOpenDialog(false));
  };

  const toggleGridZoom = () => {
    setIsGridZoomed(!isGridZoomed);
    if (isTabsZoomed) setIsTabsZoomed(false);
  };

  const toggleTabsZoom = () => {
    setIsTabsZoomed(!isTabsZoomed);
    if (isGridZoomed) setIsGridZoomed(false);
  };
  const validateAllRows = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForBK(
      reduxPayload,
      bankKeyData?.requestHeaderResponse,
      isrequestId,
      task,
      "",
      changeLogBK,
      selectedLevel
    );
    const hSuccess = (data) => {
      setBlurLoading(false);
      showSnackbar("Bank Keys Validation initiated", "success");
      setIsSaveAsDraftEnabled(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      showSnackbar("Error occurred while validating the request", "error");
    };

    doAjax(
      `/${destination_BankKey}/massAction/validateBankKey`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const handleCloseDialog = () => {
    setMissingFieldsDialogOpen(false);
  };

  const handleRowInputChange = (value, id, field, rowData) => {
    if (field === "BankCtry") {
      handleBKCountry(value)
    }
    if (field === "BankName") {
      dispatch(
        updateModuleFieldDataBK({
          uniqueId: selectedRowID || selectedRow?.id,
          keyName: "Name",
          data: value,
        })
      );
    }
    else if (field === "BankKey") {
      dispatch(
        updateModuleFieldDataBK({
          uniqueId: selectedRowID || selectedRow?.id,
          keyName: "BankNo",
          data: value,
        })
      );
    }
    const updatedRows = rowsHeaderData.map((row) =>
      row.id === id ? { ...row, [field]: value } : row
    );
    dispatch(setRowsHeaderData(updatedRows));

    if (rowData?.row) {
      let newRow = JSON.parse(JSON.stringify(rowData?.row));
      newRow[field] = value;
      handleRowClick({ row: newRow })
    }
  };

  const handleAddRow = () => {
    const id = uuidv4();
    setNewRowId(id);
    setIsAddRowMode(true);
    setWithReference("yes"); // Reset radio to default

    dispatch(
      setRowsHeaderData([
        ...rowsHeaderData,
        {
          id,
          controllingArea: "",
          profitCenterNumber: "",
          longDescription: "",
          businessSegment: "",
          companyCode: "",
          included: true,
          isNew: true,
          validated: VALIDATION_STATUS?.default,
        },
      ])
    );
    dispatch(setOpenDialog(true));
  };

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  useEffect(() => {
    if (!selectedRow?.BankCtry) {
      setSelectedRow(rowsHeaderData[0]);
      dispatch(setSelectedRowID(rowsHeaderData[0]?.id))
      setNewRowId(rowsHeaderData[0]?.id)
    }
  }, [rowsHeaderData]);

  const [rowRegionData, setRowRegionData] = useState({});

  const getRegionBasedOnCountry = (countryCode, fieldData, rowId) => {
    const hSuccess = (data) => {
      // Store region data for this specific row
      setRowRegionData((prev) => ({
        ...prev,
        [rowId]: data.body,
      }));

      // Also update the general dropdown data for UI rendering
      setDropdownDataRegion(data.body);

      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Region", data: data.body },
      });
    };
    if (!countryCode) {
      const hError = (error) => {
        console.log(error);
      };

      doAjax(
        `/${destination_BankKey}/data/getRegionBasedOnCountry?country=${countryCode}`,
        "get",
        hSuccess,
        hError
      );
    };
  }

  const handleButtonClick = async (type,remarks) => {
    setBlurLoading(true);
    let apiEndpoint =
      END_POINTS?.MASTER_BUTTON_APIS?.[MODULE_MAP?.BK]?.[
      bankKeyData?.payload?.requestHeaderData?.RequestType
      ]?.[type];
    const finalPayload = createPayloadForBK(
      reduxPayload,
      bankKeyData?.requestHeaderResponse,
      isrequestId,
      task,
      remarks,
      changeLogBK,
      selectedLevel

    );

    const hSuccess = (data) => {
      if (data.statusCode >= 200 && data.statusCode < 300) {
        setBlurLoading(false);
        showSnackbar(data?.message || "", "success");
        // setTimeout(() => {
        navigate(
          END_POINTS?.MASTER_BUTTON_APIS?.[MODULE_MAP?.BK]?.[
            bankKeyData?.payload?.requestHeaderData?.RequestType
          ]?.[type]?.NAVIGATE_TO
        );
        // }, 2000);
      } else {
        setBlurLoading(false);
        showSnackbar(data?.error || data?.message || "Error Fetching Data !!", "error");
      }
    };

    const hError = (error) => {
      setBlurLoading(false);
      showSnackbar(error?.error, "error");
    };

    doAjax(apiEndpoint?.URL, "POST", hSuccess, hError, finalPayload);
  };

  const handleAddWithRef = async (bankCtry, bankKey) => {
    setBlurLoading(true)
    const url = `/${destination_BankKey}/data/displayBankKeySAPData`
    var payload = {
      bankCtry,
      bankKey
    }

    const hSuccess = (response) => {
      setBlurLoading(false)
      const apiResponse = response?.body[0] || {};
      let rowsBody = {...rowsBodyData};
      let rowsHeader = [];
      const dynamicKey = newRowId || "";
      if (!dynamicKey) return;

      if (dynamicKey === "first_request_id_requestor") {
        rowsHeader?.push({
          id: newRowId || "",
          BankCtry: apiResponse?.BankCtry || selectedBankCtry,
          BankKey: "",
          BankName: "",
          BankBranch: apiResponse?.Tobankaddress?.BankBranch || "",
          included: true,
          validated: "default"
        })
      } else {
        rowsHeader = [...rowsHeaderData];

        const newRow = {
          id: newRowId || "",
          BankCtry: apiResponse?.BankCtry || selectedBankCtry,
          BankKey: "",
          BankName: "",
          BankBranch: apiResponse?.Tobankaddress?.BankBranch || "",
          included: true,
          validated: "default"
        };

        const existingIndex = rowsHeader.findIndex(row => row.id === newRowId);

        if (existingIndex !== -1) {
          rowsHeader[existingIndex] = newRow;
        } else {
          rowsHeader.push(newRow);
        }
      }

      rowsBody[dynamicKey] = {
        ...apiResponse,
        ...(apiResponse?.Tobankaddress || {}),
        BankNo: "",
        Name: ""
      };

      dispatch(setBankKeyPayloadIndividual({ keyName: "rowsHeaderData", data: rowsHeader }));
      dispatch(setBankKeyPayloadIndividual({ keyName: "rowsBodyData", data: rowsBody }));

      setSelectedBankCtry("")
      setSelectedBankKey("")
    }

    const hError = () => {
      setBlurLoading(false)
    }

    doAjax(url, "post", hSuccess, hError, payload)
  };

  const handleProceed = async () => {
    if (withReference === "no") {
      dispatch(setOpenDialog(false));
      setIsAddRowMode(false);
    } else {
      await handleAddWithRef(selectedBankCtry, selectedBankKey);
      dispatch(setOpenDialog(false));
      setIsAddRowMode(false);
    }
  };

  const handleRowClick = (params) => {
    const clickedRow = params.row;
    handleBKCountry(clickedRow?.BankCtry)
    setSelectedRow(clickedRow);
    dispatch(setSelectedRowID(clickedRow?.id));
  };

  return (
    <div>
      {error && (
        <Typography color="error">{t("Error loading data")}</Typography>
      )}
      <div
        style={{ padding: "0", width: "100%", margin: "0", marginTop: "20px" }}
      >
        <Box
          sx={{
            position: isGridZoomed ? "fixed" : "relative",
            top: isGridZoomed ? 0 : "auto",
            left: isGridZoomed ? 0 : "auto",
            right: isGridZoomed ? 0 : "auto",
            bottom: isGridZoomed ? 0 : "auto",
            width: isGridZoomed ? "100vw" : "100%",
            height: isGridZoomed ? "100vh" : "auto",
            zIndex: isGridZoomed ? 1004 : 0,
            backgroundColor: isGridZoomed ? "white" : "transparent",
            padding: isGridZoomed ? "20px" : "0",
            display: "flex",
            flexDirection: "column",
            boxShadow: isGridZoomed
              ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
              : "none",
            transition: "all 0.3s ease",
            borderRadius: "8px",
            border: "1px solid #e0e0e0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "8px 16px",
              backgroundColor: "#f5f5f5",
              borderRadius: "8px 8px 0 0",
            }}
          >
            <Typography variant="h6">{t("List of Bank Keys")}</Typography>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Button
                variant="contained"
                color="primary"
                size="small"
                onClick={handleAddRow}
                disabled={!isAddRowEnabled || disableCheck}
              >
                {t("+ Add")}
              </Button>
              <Tooltip
                title={isGridZoomed ? "Exit Zoom" : "Zoom In"}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleGridZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isGridZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
            <div style={{ height: "100%" }}>
              <div>
                <ReusableDataTable
                  isLoading={loading}
                  rows={rowsHeaderData}
                  columns={columns}
                  pageSize={10}
                  tempheight={"50vh"}
                  getRowIdValue={"id"}
                  status_onRowSingleClick={true}
                  callback_onRowSingleClick={handleRowClick}
                  getRowClassName={(params) =>
                    selectedRow?.id === params.row.id ? "Mui-selected" : ""
                  }
                />
              </div>
            </div>
          </div>
        </Box>
      </div>

      {/* with and without reference */}

      {openDialog && (
        <Dialog
          fullWidth
          open={openDialog}
          maxWidth="lg"
          onClose={handleDialogClose}
          sx={{
            "&::webkit-scrollbar": {
              width: "1px",
            },
          }}
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF",
              // borderBottom: "1px solid grey",
              display: "flex",
            }}
          >
            <Typography variant="h6">Add New</Typography>
          </DialogTitle>
          <DialogContent
            sx={{
              padding: ".5rem 1rem",
              alignItems: "center",
              justifyContent: "center",
              margin: "0px 25px",
            }}
          >
            <FormControl component="fieldset" sx={{ paddingBottom: "2%" }}>
              <FormLabel
                component="legend"
                sx={{
                  padding: "15px 0px",
                  fontWeight: "600",
                  fontSize: "15px",
                }}
              >
                Do You Want To Continue
              </FormLabel>
              <RadioGroup
                row
                aria-label="profit-center-number"
                name="profit-center-number"
                value={withReference}
                onChange={(event) => setWithReference(event.target.value)}
              >
                <FormControlLabel
                  value="yes"
                  control={<Radio />}
                  label="With Reference"
                />
                <FormControlLabel
                  value="no"
                  control={<Radio />}
                  label="Without Reference"
                />
              </RadioGroup>
            </FormControl>
            <Grid container spacing={2}>
              {/* First row: 4 dropdowns */}
              <Grid item xs={12}>
                <Grid container spacing={2}>
                  <Grid item xs={3}>
                    <SingleSelectDropdown
                      options={bankKeyData?.dropDownData?.BankCtry || []}
                      value={
                        bankKeyData?.dropDownData?.BankCtry?.find(
                          (item) => item.code === selectedBankCtry
                        ) || null
                      }
                      onChange={(newValue) => {
                        setSelectedBankCtry(newValue?.code || "");
                        getBankKey(newValue?.code);
                      }}
                      placeholder="Select Bank Country"
                      minWidth="90%"
                      listWidth={235}
                      disabled={withReference === "no"}
                    />
                  </Grid>
                  <Grid item xs={3}>
                    <SingleSelectDropdown
                      options={bankKeyData?.dropDownData?.BankKey || []}
                      value={
                        bankKeyData?.dropDownData?.BankKey?.find(
                          (item) => item.code === selectedBankKey
                        ) || null
                      }
                      onChange={(newValue) => {
                        setSelectedBankKey(newValue?.code || "");
                      }}
                      isLoading={isDropDownLoading}
                      placeholder="Select Bank Key"
                      disabled={withReference === "no"}
                      minWidth="90%"
                      listWidth={235}
                    />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{
                width: "max-content",
                textTransform: "capitalize",
              }}
              onClick={handleDialogClose}
              variant="outlined"
            >
              Cancel
            </Button>
            <Button
              className="button_primary--normal"
              type="save"
              // disabled={
              //   !(selectedMatLines?.length || selectedMaterials?.code) &&
              //   withReference === "yes"
              // }
              onClick={handleProceed}
              variant="contained"
            >
              Proceed
            </Button>
          </DialogActions>
        </Dialog>
      )}

      <>
        {/* Missing Fields Dialog */}
        <FlexibleValidationDialog
          open={missingFieldsDialogOpen}
          onClose={() => setMissingFieldsDialogOpen(false)}
          missingFields={missingFields}
          t={t}
        />

        {/* Custom Message Dialog for Duplicacy */}
        <FlexibleValidationDialog
          open={customMessageDialog.open}
          onClose={() => setCustomMessageDialog({ open: false, message: "", title: "" })}
          customMessage={customMessageDialog.message}
          title={customMessageDialog.title}
          t={t}
        />
      </>

      {isDeleteDialogVisible?.isVisible && (
        <CustomDialog isOpen={isDeleteDialogVisible?.isVisible} titleIcon={<DeleteOutlineOutlinedIcon size="small" color="error" sx={{ fontSize: "20px" }} />} Title={t("Delete Row") + "!"} handleClose={() => setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false })}>
          <DialogContent sx={{ mt: 2 }}>{t(DIALOUGE_BOX_MESSAGES.DELETE_MESSAGE)}</DialogContent>
          <DialogActions>
            <Button variant="outlined" size="small" sx={{ ...button_Outlined }} onClick={() => setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false })}>
              {t(DELETE_MODAL_BUTTONS_NAME.CANCEL)}
            </Button>
            <Button variant="contained" size="small" sx={{ ...button_Primary }} onClick={handleDelete}>
              {t(DELETE_MODAL_BUTTONS_NAME.DELETE)}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}

      {selectedRow &&
        (
          <Box
            sx={{
              position: isTabsZoomed ? "fixed" : "relative",
              top: isTabsZoomed ? 0 : "auto",
              left: isTabsZoomed ? 0 : "auto",
              right: isTabsZoomed ? 0 : "auto",
              bottom: isTabsZoomed ? 0 : "auto",
              width: isTabsZoomed ? "100vw" : "100%",
              height: isTabsZoomed ? "100vh" : "auto",
              zIndex: isTabsZoomed ? 1004 : 0,
              backgroundColor: isTabsZoomed ? "white" : "transparent",
              padding: isTabsZoomed ? "20px" : "0",
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              boxShadow: isTabsZoomed
                ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
                : "none",
              transition: "all 0.3s ease",
              borderRadius: "8px",
              border: "1px solid #e0e0e0",
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: "8px 16px",
                borderRadius: "8px 8px 0 0",
              }}
            >
              <Typography variant="h6">{t("View Details")}</Typography>
              <Tooltip
                title={isTabsZoomed ? "Exit Zoom" : "Zoom In"}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleTabsZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isTabsZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}>
              <Tabs
                value={selectedTab}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
                aria-label="Request tabs"
                variant="scrollable"
                scrollButtons="auto"
                sx={{
                  top: 0,
                  position: "sticky",
                  zIndex: 1000,
                  backgroundColor: colors.background.container,
                  borderBottom: `1px solid ${colors.border.light}`,
                  "& .MuiTab-root": {
                    minHeight: "48px",
                    textTransform: "none",
                    fontSize: "14px",
                    fontWeight: 600,
                    color: colors.black.graphite,
                    "&.Mui-selected": {
                      color: colors.primary.main,
                      fontWeight: 700,
                    },
                    "&:hover": {
                      color: colors.primary.main,
                      opacity: 0.8,
                    },
                  },
                  "& .MuiTabs-indicator": {
                    backgroundColor: colors.primary.main,
                    height: "3px",
                  },
                }}
              >
                {bankKeyTabs?.[`${initialPayload?.Region}-${selectedRow?.BankCtry}`]?.map((tab, index) => (
                  <Tab key={index} label={tab.tab} />
                ))}
              </Tabs>

              <Paper elevation={2} sx={{ p: 3, borderRadius: 4 }}>
                {bankKeyTabs?.[`${initialPayload?.Region}-${selectedRow?.BankCtry}`]?.[selectedTab] && (
                  <GenericTabsGlobal
                    disabled={disableCheck}
                    basicDataTabDetails={bankKeyTabs?.[`${initialPayload?.Region}-${selectedRow?.BankCtry}`]?.[selectedTab].data}
                    activeViewTab={bankKeyTabs?.[`${initialPayload?.Region}-${selectedRow?.BankCtry}`]?.[selectedTab].tab}
                    uniqueId={selectedRow?.id || selectedRowID || rowsHeaderData[0]?.id}
                    selectedRow={selectedRow || {}}
                    module={MODULE_MAP?.BK}
                  />
                )}
              </Paper>
              <Box
                sx={{
                  borderTop: "1px solid #e0e0e0",
                  padding: "16px",
                }}
              >

              </Box>
            </Box>
          </Box>
        )}

      <BottomNavGlobal
        handleSaveAsDraft={handleButtonClick}
        handleSubmitForReview={handleButtonClick}
        handleSubmitForApprove={handleButtonClick}
        handleSendBack={handleButtonClick}
        handleCorrection={handleButtonClick}
        handleRejectAndCancel={handleButtonClick}
        handleValidateAndSyndicate={handleButtonClick}
        validateAllRows={validateAllRows}
        isSaveAsDraftEnabled={isSaveAsDraftEnabled}
        validateEnabled={isAddRowEnabled}
        filteredButtons={filteredButtons}
        moduleName={MODULE_MAP?.BK}
        showWfLevels = {showWfLevels}
        selectedLevel={selectedLevel}
        workFlowLevels={wfLevels}
        setSelectedLevel={setSelectedLevel}
   
      />
      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />
    </div>
  );
};

export default BankKeyListDetails;
