import { Box, Card, CardContent, FormControl, Grid, MenuItem, Select, Typography,Stack } from '@mui/material'
import React, { useState } from 'react'
import { container_filter, font_Small } from '../common/commonStyles'
import DashboardGraph from './DashboardGraph'

const DrillDownGraph = ({legendList=[],graphHeader,data,option,xaxis,yaxis,bar=true,line=false,yaxisHeader,grouped=false,type,ToolTip}) => {
    const [graphVal, setgraphVal] = useState(1)
    const handleSelect=(e)=>{
setgraphVal(e.target.value)
    }
  return (
    <>
    <Card
      sx={{
        borderRadius:"10px",
        boxShadow: "4" ,
        minHeight: grouped ? "365px" : "320px",
        maxHeight:  grouped ? "365px" : "320px"
      }}
    >
      <CardContent>
        <Stack alignItems="center">
    <Grid container sx={{alignItems:'center'}} mt={0}>
    <Grid item xs={9}>
                       
    <Typography variant="subtitle2" color="#1d1d1d">
            <strong>{graphHeader}</strong>
          </Typography>
      </Grid>
      <Grid item xs={3}>
      <FormControl  size="small">
        <Select
          placeholder="Select Language"
          select
          sx={font_Small}
          size="small"
          value={graphVal}
          name={'language'}
          onChange={handleSelect}
          displayEmpty={true}
        >
          {/* <MenuItem sx={font_Small} value={0}>
            <div style={{ color: "#C1C1C1" }}>Select Graph </div>
          </MenuItem> */}
          
              <MenuItem  value={1}>
                {/* {isConsumption ? "Purchase Order" : "By Quantity"} */}
                {option[0]}
              </MenuItem>
              <MenuItem  value={2}>
              {/* {isConsumption ? "Shipment" : "By Amount"} */}
              {option[1]}

              </MenuItem>
            {option[2] && <MenuItem  value={3}>
                {/* Material */}
                {option[2]}

              </MenuItem>}
        </Select>
      </FormControl>
    
                       </Grid>
                       <Grid item xs={12}>
{graphVal==1 && <DashboardGraph graphDataSet={data[0]}
isDrillDown={true}
//  header={"H1"}
//  xaxisData={isConsumption ? "poNumber": "material"}
//  yaxisData={isConsumption ? "requiredQuantity":"poQuantity"}
 xaxisData={xaxis[0]}
yaxisData={yaxis[0]}
legendList={legendList}

//  bar={true}
//  line={false}
 bar={bar}
 line={line}
 multi={true}
//  yaxisHeader={"Unit"}
 yaxisHeader={yaxisHeader[0]}
 grouped={grouped}

//  type={isConsumption ?"consumption" :"ops"}
type={type[0]}
//  grouped={isConsumption? true : false}
//  tooltip_Content={isConsumption ? "consumption" : "tooltipmaterialbypurchase"}
tooltip_Content={ToolTip[0]}

 />
 
 }
{graphVal==2 &&<DashboardGraph 
graphDataSet={data[1]}
isDrillDown={true}

// header={"H2"}
// xaxisData={isConsumption ? "shipmentId":"material"}
// yaxisData={isConsumption ? "requiredQuantity":"poValue"}
xaxisData={xaxis[1]}
yaxisData={yaxis[1]}
// bar={true}
// line={false}
legendList={legendList}
bar={bar}
 line={line}
multi={true}
// yaxisHeader={"EUR"}
yaxisHeader={yaxisHeader[1]}

// tooltip_Content={isConsumption ? "consumption" :"common"}
tooltip_Content={ToolTip[1]}

// grouped={isConsumption? true : false}
grouped={grouped}

// type={isConsumption ?"consumption" :"ops"}
type={type[1]}

/>}
{graphVal==3 && <DashboardGraph 
graphDataSet={data[2]}
isDrillDown={true}

// header={"H2"}
// xaxisData={isConsumption ? "subMaterial":"material"}
// yaxisData={isConsumption ? "requiredQuantity":"poValue"}
xaxisData={xaxis[2]}
yaxisData={yaxis[2]}
yaxisHeader={yaxisHeader[2]}
legendList={legendList}

// bar={true}
// line={false}
bar={bar}
 line={line}
multi={true}
// yaxisHeader={"EUR"}
// tooltip_Content={isConsumption ? "consumption" :"common"}
tooltip_Content={ToolTip[2]}

// grouped={isConsumption? true : false}
grouped={grouped}


// type={isConsumption ?"consumption" :"ops"}
type={type[2]}

/>}

                       </Grid>
    </Grid>
    </Stack>
    </CardContent></Card>
        </>
  )
}

export default DrillDownGraph