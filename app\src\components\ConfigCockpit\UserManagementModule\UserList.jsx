import React from "react";
import {
  <PERSON>, Card, Typography, Avatar, Chip, Tooltip
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { colors } from "@constant/colors";
import { getAvatarColor } from "@helper/helper";
import useLang from "@hooks/useLang";

const UserList = ({ users, selectedUser, onUserSelect, loading }) => {
  const { t } = useLang();

  const renderWithHover = (params) => {
    const items = params.value || [];
    if (items.length === 0) return "No data";

    return (
      <Box sx={{ display: "flex", gap: 0.5, alignItems: "center" }}>
        <Chip label={items[0]} size="small" sx={{ bgcolor: colors.primary.light, color: colors.primary.dark }} />
        {items.length > 1 && (
          <Tooltip title={items.slice(1).join(", ")} arrow placement="top">
            <Chip label={`+${items.length - 1}`} size="small" sx={{ bgcolor: colors.secondary.light, color: colors.secondary.dark }} />
          </Tooltip>
        )}
      </Box>
    );
  };

  const columns = [
    {
      field: "displayName",
      headerName: t("User Name"),
      flex: 1,
      sortable: true,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Avatar sx={{ bgcolor: getAvatarColor(params.value || params.row.email), width: 32, height: 32 }}>
            {(params.value || params.row.email).charAt(0).toUpperCase()}
          </Avatar>
          <Typography variant="body1">{params.value || "-"}</Typography>
        </Box>
      ),
    },
    {
      field: "email",
      headerName: t("Email"),
      flex: 1.5,
      sortable: true,
    },
    {
      field: "userRoles",
      headerName: t("Roles"),
      flex: 1,
      sortable: false,
      renderCell: renderWithHover,
    },
    {
      field: "userGroups",
      headerName: t("User Groups"),
      flex: 1,
      sortable: false,
      renderCell: renderWithHover,
    },
  ];

  return (
    <Card sx={{ width: "100%", height: "100%", display: "flex", flexDirection: "column", overflow: "hidden" }}>
      <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", px: 2.5, py: 1.5, borderBottom: "1px solid #ddd" }}>
        <Typography variant="subtitle1">{t("Users")} ({users.length})</Typography>
      </Box>

      <Box sx={{ flex: 1, overflow: "auto" }}>
        <DataGrid
          rows={users}
          columns={columns}
          getRowId={(row) => row.email}
          autoHeight
          pagination
          disableSelectionOnClick
          loading={loading}
          onRowClick={(params) => onUserSelect(params.row)}
          sx={{
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: colors.primary.light,
              fontWeight: "bold",
            },
            "& .MuiDataGrid-row:hover": {
              backgroundColor: colors.primary.veryLight,
            },
            "& .MuiDataGrid-row.Mui-selected": {
              backgroundColor: colors.primary.veryLight,
            },
          }}
        />
      </Box>
    </Card>
  );
};

export default UserList;
