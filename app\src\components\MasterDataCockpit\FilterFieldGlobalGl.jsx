import {
  <PERSON>rid,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { useSelector, useDispatch } from "react-redux";
import InputTypeGlobal from "./GlobalComponents/InputTypeGlobal";
import AutocompleteTypeGlobal from "./GlobalComponents/AutoCompleteTypeGlobal";
import MultiSelectTypeGlobal from "./GlobalComponents/MultiSelectTypeGlobal";
import RadioTypeGlobal from "./GlobalComponents/RadioTypeGlobal";
import DateTypeGlobal from "./GlobalComponents/DateTypeGlobal";
import { setMultipleMaterialPayloadKey, setPayload } from "../../app/payloadSlice";
import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { colors } from "../../constant/colors";
import { updateModuleFieldData } from "@app/profitCenterTabsSlice";
import { updateModuleFieldDataGL } from "@app/generalLedgerTabSlice";

const FilterFieldGlobalGl = (props) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const payloadState = useSelector((state) => state.payload);
  const payloadStatePC = useSelector((state) => state.profitCenter.payload.requestHeaderData);
  let taskData = useSelector((state) => state.userManagement.taskData);
  const queryParams = new URLSearchParams(location.search);
  const isWorkspace = queryParams.get("RequestId");
  const { field, disabled, dropDownData, uniqueId, viewName, plantData } = props;
  const userData = useSelector((state) => state.userManagement.userData);

  const handleChange = (value) => {
    if (location.pathname.includes("material")) {
      if (plantData) {
        dispatch(
          setMultipleMaterialPayloadKey({
            key: field.fieldName,
            value: value,
            viewName: viewName,
            plantData: plantData,
          })
        );
      } else {
        dispatch(
          setPayload({
            key: field.fieldName,
            value: value,
          })
        );
      }
    }
  };

  useEffect(() =>{
    if(!taskData?.requestId){
    if((props?.field?.fieldName === "Created On") || (props?.field?.fieldName === "Updated On")){
      const currentDate = new Date();
      dispatch(
        updateModuleFieldDataGL({
          keyName: props.field.jsonName,
          data: currentDate,
        })
      );
    }
  }
  },[])

  if (props?.field?.fieldName === "Created By") {
      return (
        <Grid item md={2}>
          <Stack>
            <Typography variant="body2" color="#777" sx={{whiteSpace: 'nowrap',overflow: 'hidden',textOverflow: 'ellipsis',maxWidth: '100%'}} title={props.field.fieldName}>
              {props.field.fieldName}
            </Typography>
            <TextField
              title={!isWorkspace? userData?.emailId : payloadState?.payloadData?.ReqCreatedBy || payloadStatePC?.ReqCreatedBy}
              size="small"
              value={!isWorkspace? userData?.emailId : payloadState?.payloadData?.ReqCreatedBy || payloadStatePC?.ReqCreatedBy}
              disabled={
                userData?.emailId ? true
                  : false
              }
              sx={{
                cursor: "not-allowed", 
                "& .MuiInputBase-root": {
                  height: "34px",
                },
                "& .MuiInputBase-input": {
                  cursor: "not-allowed",
                },
                '& .MuiInputBase-root.Mui-disabled': {
                  '& > input': {
                    WebkitTextFillColor: colors.black.dark,
                    color: colors.black.dark,
                  },
                  backgroundColor: colors.hover.light,
                }
              }}
            
            />
          </Stack>
        </Grid>
      );
    }
    else if(props?.field?.fieldName === "Created On") {
      const currentDate = new Date();
      const day = String(currentDate.getDate()).padStart(2, "0"); 
      const month = String(currentDate.getMonth() + 1).padStart(2, "0"); 
      const year = currentDate.getFullYear();
      
      const formattedDate = `${day}-${month}-${year}`;
      return (
        <Grid item md={2}>
          <Stack>
            <Typography variant="body2" color="#777" sx={{whiteSpace: 'nowrap',overflow: 'hidden',textOverflow: 'ellipsis',maxWidth: '100%'}} title={props.field?.fieldName}>
              {props?.field?.fieldName}
            </Typography>
            <TextField
              size="small"
              value={formattedDate} 
              disabled={true}
              sx={{
                cursor: "not-allowed",
                "& .MuiInputBase-root": {
                  height: "34px",
                },
                "& .MuiInputBase-input": {
                  cursor: "not-allowed",
                },
              '& .MuiInputBase-root.Mui-disabled': {
                  '& > input': {
                    WebkitTextFillColor: colors.black.dark,
                    color: colors.black.dark,
                  },
                  backgroundColor: colors.hover.light,
                }
              }}
            />
          </Stack>
        </Grid>
      );
    }
    else if(props?.field?.fieldName === "Updated On") {
      const currentDate = new Date();
      const day = String(currentDate.getDate()).padStart(2, "0"); 
      const month = String(currentDate.getMonth() + 1).padStart(2, "0"); 
      const year = currentDate.getFullYear();
      const formattedDate = `${day}-${month}-${year}`; 
      return (
        <Grid item md={2}>
          <Stack>
            <Typography variant="body2" color="#777" sx={{whiteSpace: 'nowrap',overflow: 'hidden',textOverflow: 'ellipsis',maxWidth: '100%'}} title={props.field?.fieldName}>
              {props?.field?.fieldName}
            </Typography>
            <TextField
              size="small"
              value={formattedDate} 
              disabled={true}
              sx={{
                cursor: "not-allowed",
                "& .MuiInputBase-root": {
                  height: "34px",
                },
                "& .MuiInputBase-input": {
                  cursor: "not-allowed",
                },
              '& .MuiInputBase-root.Mui-disabled': {
                  '& > input': {
                    WebkitTextFillColor: colors.black.dark,
                    color: colors.black.dark,
                  },
                  backgroundColor: colors.hover.light,
                }
              }}
            />
          </Stack>
        </Grid>
      );
    }

  const renderField = () => {
    switch (field.fieldType) {
      case "Input":
        return (
          <InputTypeGlobal
            uniqueId={uniqueId}
            field={field}
            disabled={field?.fieldName === "Long Text" ? true :field?.fieldName === "Long Text" ? true : disabled}
            handleChange={handleChange}
            selectedRow={props?.selectedRow}
          />
        );
      case "Auto":
        return (
          <InputTypeGlobal
            uniqueId={uniqueId}
            field={field}
            disabled={disabled}
            handleChange={handleChange}
            selectedRow={props?.selectedRow}
          />
        );
      case "Disable Input":
        return (
          <InputTypeGlobal
            uniqueId={uniqueId}
            field={field}
            disabled={true}
            handleChange={handleChange}
            selectedRow={props?.selectedRow}
          />
        );
      case "Drop Down":
        return (
          <AutocompleteTypeGlobal
            uniqueId={uniqueId}
            field={field}
            disabled={field?.fieldName === "Account Type" ? true :field?.fieldName === "Account Group" ? true : disabled}
            dropDownData={dropDownData}
            handleChange={handleChange}
            selectedRow={props?.selectedRow}
          />
        );
      case "Multi Select":
        return (
          <MultiSelectTypeGlobal
            uniqueId={uniqueId}
            field={field}
            disabled={disabled}
            dropDownData={dropDownData}
            handleChange={handleChange}
            selectedRow={props?.selectedRow}
          />
        );
      case "Radio Button":
        return (
          <RadioTypeGlobal
            uniqueId={uniqueId}
            field={field}
            disabled={disabled}
            handleChange={handleChange}
            selectedRow={props?.selectedRow}
          />
        );
      case "Calendar":
        return (
          <DateTypeGlobal
            uniqueId={uniqueId}
            field={field}
            disabled={disabled}
            handleChange={handleChange}
            selectedRow={props?.selectedRow}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
        {renderField()}
    </>
  );
};

export default FilterFieldGlobalGl;