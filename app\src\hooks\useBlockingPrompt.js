import { useEffect,useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { DIALOUGE_BOX_MESSAGES } from '../constant/enum';

export const useBlockingPrompt = (isFormDirty) => {
  const navigate = useNavigate();
  const [isDialogVisible, setDialogVisible] = useState(false);
  const [nextLocation, setNextLocation] = useState('');
  const blockNavigation = (nextLocation) => {
    if (isFormDirty) {
      setNextLocation(nextLocation);
      setDialogVisible(true);
    } else {
      navigate(nextLocation);
    }
  };

  const handleConfirm = () => {
    setDialogVisible(false);
    navigate(nextLocation);
  };

  const handleCancel = () => {
    setDialogVisible(false);
  };

  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (isFormDirty) {
        const message = DIALOUGE_BOX_MESSAGES?.LEAVE_PAGE_MESSAGE;
        e.returnValue = message;
        return message;
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [isFormDirty]);

  return {
    blockNavigation,
    isDialogVisible,
    handleConfirm,
    handleCancel,
  }
};


