import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import {baseUrl_Admin} from '@data/baseUrl';
const {VITE_APP_TOKEN} = import.meta.env;

export const adminApi = createApi({
  reducerPath: 'adminApi',
  baseQuery: fetchBaseQuery({
    baseUrl: baseUrl_Admin,
    prepareHeaders: (headers, { getState }) => {
      const { token, environment } = getState().applicationConfig || {};
      if (token && (environment === 'localhost' || environment === '127.0.0.1')) {
        headers.set('authorization', `Bearer ${VITE_APP_TOKEN}`);
      }
      headers.set("Access-Control-Allow-Origin", "*");
      return headers;
    },
  }),
  endpoints: () => ({}),
});