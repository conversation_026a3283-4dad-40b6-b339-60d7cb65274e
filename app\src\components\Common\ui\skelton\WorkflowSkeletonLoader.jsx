import { Box, 
    Skeleton, 
    Card, 
    Card<PERSON>ontent, 
    Typography,
    Accordion,
    AccordionSummary,
    AccordionDetails,
} from '@mui/material';
import useLang from '@hooks/useLang';

const WorkflowSkeletonLoader = () => {
    const { t } = useLang();
    return (
        <Box sx={{ p: 3, maxWidth: 1400, mx: 'auto' }}>
            {/* Header Title */}
            <Typography variant="h6" sx={{ mb: 3, color: '#666' }}>
                {t("Workflow Details")}
            </Typography>

            {/* Stats Cards */}
            <Box sx={{ 
                display: 'flex', 
                gap: 6, 
                mb: 4,
                justifyContent: 'flex-start',
                maxWidth: 600
            }}>
                {/* Workflow Groups */}
                <Box sx={{ textAlign: 'center' }}>
                    <Skeleton 
                        variant="text" 
                        width={60} 
                        height={60} 
                        sx={{ 
                            fontSize: '2.5rem', 
                            mx: 'auto',
                            bgcolor: '#e3f2fd',
                            borderRadius: 1
                        }} 
                    />
                    <Skeleton 
                        variant="text" 
                        width={100} 
                        height={20} 
                        sx={{ mt: 1, mx: 'auto' }} 
                    />
                </Box>

                {/* Total Tasks */}
                <Box sx={{ textAlign: 'center' }}>
                    <Skeleton 
                        variant="text" 
                        width={60} 
                        height={60} 
                        sx={{ 
                            fontSize: '2.5rem', 
                            mx: 'auto',
                            bgcolor: '#e3f2fd',
                            borderRadius: 1
                        }} 
                    />
                    <Skeleton 
                        variant="text" 
                        width={80} 
                        height={20} 
                        sx={{ mt: 1, mx: 'auto' }} 
                    />
                </Box>

                {/* Avg SLA */}
                <Box sx={{ textAlign: 'center' }}>
                    <Skeleton 
                        variant="text" 
                        width={60} 
                        height={60} 
                        sx={{ 
                            fontSize: '2.5rem', 
                            mx: 'auto',
                            bgcolor: '#e3f2fd',
                            borderRadius: 1
                        }} 
                    />
                    <Skeleton 
                        variant="text" 
                        width={120} 
                        height={20} 
                        sx={{ mt: 1, mx: 'auto' }} 
                    />
                </Box>
            </Box>

            {/* Workflow Group Accordion */}
            <Accordion 
                expanded={true} 
                sx={{ 
                    bgcolor: '#37474f',
                    color: 'white',
                    '&:before': { display: 'none' },
                    borderRadius: 1,
                    overflow: 'hidden'
                }}
            >
                <AccordionSummary
                    // expandIcon={<ExpandMore sx={{ color: 'white' }} />}
                    sx={{ 
                        bgcolor: '#37474f',
                        '& .MuiAccordionSummary-content': { 
                            alignItems: 'center' 
                        }
                    }}
                >
                    <Box>
                        <Skeleton 
                            variant="text" 
                            width={180} 
                            height={24} 
                            sx={{ bgcolor: 'rgba(255,255,255,0.2)' }} 
                        />
                        <Skeleton 
                            variant="text" 
                            width={220} 
                            height={16} 
                            sx={{ bgcolor: 'rgba(255,255,255,0.1)', mt: 0.5 }} 
                        />
                    </Box>
                </AccordionSummary>

                <AccordionDetails sx={{ bgcolor: '#f5f5f5', p: 0 }}>
                    {/* Workflow Steps Header */}
                    <Box sx={{ 
                        display: 'grid',
                        gridTemplateColumns: 'repeat(6, 1fr)',
                        gap: 2,
                        bgcolor: '#fff',
                        borderBottom: '1px solid #e0e0e0',
                        p: 2
                    }}>
                        {['Requestor', 'Level 1: Data Entry', 'Level 2: Additional Master Data', 'Level 3: Cost', 'Level 4: Record Approval', 'Final Creation'].map((title, index) => (
                            <Box 
                                key={index}
                                sx={{ 
                                    textAlign: 'center'
                                }}
                            >
                                <Skeleton 
                                    variant="text" 
                                    width="80%" 
                                    height={20} 
                                    sx={{ mx: 'auto' }} 
                                />
                            </Box>
                        ))}
                    </Box>

                    {/* Workflow Tasks */}
                    <Box sx={{ p: 3, bgcolor: '#fafafa' }}>
                        <Box sx={{ 
                            display: 'grid',
                            gridTemplateColumns: 'repeat(6, 1fr)',
                            gap: 2,
                            alignItems: 'flex-start'
                        }}>
                            
                            {/* Requestor Column */}
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                {[1, 2, 3, 4, 5, 6].map((item, index) => (
                                    <Box key={index} sx={{ position: 'relative' }}>
                                        <Card sx={{ 
                                            bgcolor: '#e3f2fd',
                                            minHeight: 60,
                                            border: '2px solid #2196f3',
                                            borderRadius: 2
                                        }}>
                                            <CardContent sx={{ p: 2 }}>
                                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                    <Skeleton variant="circular" width={24} height={24} />
                                                    <Box sx={{ flex: 1 }}>
                                                        <Skeleton variant="text" width="70%" height={16} />
                                                        <Skeleton variant="text" width="40%" height={12} sx={{ mt: 0.5 }} />
                                                    </Box>
                                                    <Skeleton variant="text" width={30} height={20} />
                                                </Box>
                                            </CardContent>
                                        </Card>
                                        
                                        {/* Connection Line */}
                                        {index < 5 && (
                                            <Box sx={{
                                                position: 'absolute',
                                                right: -8,
                                                top: '50%',
                                                width: 16,
                                                height: 2,
                                                bgcolor: '#2196f3',
                                                zIndex: 1,
                                                transform: 'translateY(-50%)'
                                            }} />
                                        )}
                                    </Box>
                                ))}
                            </Box>

                            {/* Level 1 Column */}
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                <Card sx={{ 
                                    bgcolor: '#e3f2fd',
                                    minHeight: 60,
                                    border: '2px solid #2196f3',
                                    borderRadius: 2
                                }}>
                                    <CardContent sx={{ p: 2 }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                            <Skeleton variant="circular" width={24} height={24} />
                                            <Box sx={{ flex: 1 }}>
                                                <Skeleton variant="text" width="80%" height={16} />
                                                <Skeleton variant="text" width="50%" height={12} sx={{ mt: 0.5 }} />
                                            </Box>
                                            <Skeleton variant="text" width={30} height={20} />
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Box>

                            {/* Level 2 Column */}
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                {[1, 2].map((item, index) => (
                                    <Card key={index} sx={{ 
                                        bgcolor: '#e3f2fd',
                                        minHeight: 60,
                                        border: '2px solid #2196f3',
                                        borderRadius: 2
                                    }}>
                                        <CardContent sx={{ p: 2 }}>
                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                <Skeleton variant="circular" width={24} height={24} />
                                                <Box sx={{ flex: 1 }}>
                                                    <Skeleton variant="text" width="75%" height={16} />
                                                    <Skeleton variant="text" width="45%" height={12} sx={{ mt: 0.5 }} />
                                                </Box>
                                                <Skeleton variant="text" width={30} height={20} />
                                            </Box>
                                        </CardContent>
                                    </Card>
                                ))}
                            </Box>

                            {/* Level 3 Column */}
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                <Card sx={{ 
                                    bgcolor: '#e3f2fd',
                                    minHeight: 60,
                                    border: '2px solid #2196f3',
                                    borderRadius: 2
                                }}>
                                    <CardContent sx={{ p: 2 }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                            <Skeleton variant="circular" width={24} height={24} />
                                            <Box sx={{ flex: 1 }}>
                                                <Skeleton variant="text" width="85%" height={16} />
                                                <Skeleton variant="text" width="35%" height={12} sx={{ mt: 0.5 }} />
                                            </Box>
                                            <Skeleton variant="text" width={30} height={20} />
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Box>

                            {/* Level 4 Column */}
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                <Card sx={{ 
                                    bgcolor: '#e3f2fd',
                                    minHeight: 60,
                                    border: '2px solid #2196f3',
                                    borderRadius: 2
                                }}>
                                    <CardContent sx={{ p: 2 }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                            <Skeleton variant="circular" width={24} height={24} />
                                            <Box sx={{ flex: 1 }}>
                                                <Skeleton variant="text" width="90%" height={16} />
                                                <Skeleton variant="text" width="55%" height={12} sx={{ mt: 0.5 }} />
                                            </Box>
                                            <Skeleton variant="text" width={30} height={20} />
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Box>

                            {/* Final Creation Column */}
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                <Card sx={{ 
                                    bgcolor: '#f5f5f5',
                                    minHeight: 60,
                                    border: '2px solid #ccc',
                                    borderRadius: 2
                                }}>
                                    <CardContent sx={{ p: 2 }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                            <Skeleton variant="circular" width={24} height={24} sx={{ bgcolor: '#bdbdbd' }} />
                                            <Box sx={{ flex: 1 }}>
                                                <Skeleton variant="text" width="75%" height={16} sx={{ bgcolor: '#bdbdbd' }} />
                                                <Skeleton variant="text" width="40%" height={12} sx={{ mt: 0.5, bgcolor: '#bdbdbd' }} />
                                            </Box>
                                            <Skeleton variant="text" width={30} height={20} sx={{ bgcolor: '#bdbdbd' }} />
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Box>

                        </Box>
                    </Box>
                </AccordionDetails>
            </Accordion>
        </Box>
    );
};

export default WorkflowSkeletonLoader;