import "./App.css";
import "./theme-light.css"
import { Route, Routes, useLocation } from "react-router-dom";
import ApplicationRouter from "./screens/ApplicationRouter";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import { Suspense, useEffect, useRef, useState } from "react";
import axios from "axios";
import { useDispatch, useSelector } from "react-redux";
import { setToken, setIdmToken ,setIwaToken, handleLogoutWarningScreen } from "./app/applicationConfigReducer";
import { destination_Admin, destination_IWA, destination_IWA_NPI, destination_MaterialMgmt, destination_IDM, destination_Websocket,destination_IWA_NEW } from "./destinationVariables";
import { doAjax } from "./components/Common/fetchService";
import { setCurrentSAPSystem, setEntitiesAndActivities, setRoles, setUserDetails } from "./app/userManagementSlice";
import ErrorBoundary from "./screens/ErrorBoundary";
import InvalidUser from "./screens/InvalidUser";
import LoadingComponent from "./components/Common/LoadingComponent";
import { setNotification, setNotificationData, setNotificationsDirect, setNotificationPreference } from "./app/notificationSlice";
import { fetchAllDropdownMstrData } from "./fetchFunctions";
import { API_CODE, ERROR_MESSAGES, LOADING_MESSAGE, LOGOUT } from "./constant/enum";
import SockJS from "sockjs-client";
import { Client } from "@stomp/stompjs";
import { baseUrl_Websocket } from "@data/baseUrl";
import ReduxSnackbar from "./components/Common/ReduxSnackbar";
import ReusableDialog from "./components/Common/ReusableDialog";
import useLogger from "@hooks/useLogger";
import { END_POINTS } from "@constant/apiEndPoints";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import FloatingChatbot from "@components/Common/FloatingChatbot";
import { getFilter } from "@components/RequestBench/RequestBench";
import { APP_END_POINTS } from "@constant/appEndPoints";
import NetworkStatus from "./screens/NetworkStatus";
import { ToastContainer } from "react-toastify";
import { showToast } from "./functions";
import { setLangTranslation } from "./app/applicationConfigReducer";
import { fetchGlobalAppSettings } from "./components/Common/ApplicationSettings";
import useUserActivity from '@hooks/useUserActivity';
import useUserBootstrap from "@hooks/useUserBootstrap";
import useModuleAccess from "@hooks/useModuleAccess";
import SessionExpiredScreen from "./screens/SessionExpiredScreen";
import { useSnackbar } from "@hooks/useSnackbar";
import "../node_modules/@cw/quickadduser/dist/assets/style.css"
import "../node_modules/@cw/usersummary/dist/assets/style.css"
import "../node_modules/@cw/adduser/dist/assets/style.css"
import "../node_modules/@cw/viewuser/dist/assets/style.css"
import "../node_modules/@cw/edituser/dist/assets/style.css"
import "../node_modules/@cw/rolesummary/dist/assets/style.css"
import "../node_modules/@cw/createrole/dist/assets/style.css"
import "../node_modules/@cw/viewandeditrole/dist/assets/style.css"
import "../node_modules/@cw/mfviewandedit/dist/assets/style.css"
import "../node_modules/@cw/groupsummary/dist/assets/style.css"
import "../node_modules/@cw/creategroup/dist/assets/style.css"
const {
    VITE_URL_AUTH_TOKEN,
    VITE_URL_AUTH_TOKEN_CAF
} = import.meta.env;
import useModuleOdataHandler from "@modules/modulesHooks/useModuleOdataHandler"

function App() {
  const dispatch = useDispatch();
  const { customError, log } = useLogger()
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const userData = useSelector((state) => state.userManagement.userData);
  const idmToken = useSelector((state) => state.applicationConfig.idmToken);
  const iwaToken = useSelector((state) => state.applicationConfig.iwaToken);
  const langSelected = useSelector((state) => state.appSettings.language);
  useModuleOdataHandler()
  const { showSnackbar } = useSnackbar();
  const {
    timeRemaining,
    extendSession,
    stopMonitoring
  } = useUserActivity({
    enabled: true,
    onWarning: () => {
      dispatch(handleLogoutWarningScreen(true));
    },
    onTimeout: () => {
      stopMonitoring();
      dispatch({ type:  LOGOUT});
      window.location.href = "/do/logout";
    }
  });

  const [isAppReady, setIsAppReady] = useState(false);
  const [emailId, setEmailId] = useState("");
  const [validUser, setValidUser] = useState("loading");
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [isSessionExpired,setSessionExpired] = useState(false)
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");

  let initialEntities = {
    DisplayProfitCenter: ["Approve", "Back", "Change", "Correction", "Fill Details", "Next", "Submit For Approval", "Validate"],
    ChangeProfitCenter: ["Back", "Next", "Submit For Review", "Validate", "Save As Draft"],
    CreateBankKey: ["Back", "Next", "Submit Fopr Review", "Validate", "Save As Draft"],
    DisplayBankKey: ["Approve", "Back", "Change", "Correction", "Fill Details", "Next", "Submit For Approval", "Validate"],
    ChangeBankKey: ["Back", "Next", "Submit For Review", "Validate", "Save As Draft"],
    "Config Cockpit": ["Email Template Configurations", "SLA Configurations", "User Management", "Application Configuration", "Business Rules", "Broadcast Configurations", "Document Configurations"],
    "Cost Center": ["ChangeCC", "Display Bottomnavigation", "Display Top Action Button"],
    "Profit Center": ["ChangePC", "Display Bottomnavigation", "Display Top Action Button"],

    "Display BankKey": ["Fill Details", "Change", "Back", "Next", "Validate", "Approve", "Submit For Approval", "Correction"],
    // "Bank Key": [
    //   "ChangeBK",
    //   "Display Bottomnavigation",
    //   "Display Top Action Button"
    // ],
    "General Ledger": ["CreateGL", "ChangeGL", "Display Bottomnavigation", "Display Top Action Button"],
    "Display Material": ["Change", "Display Bottomnavigation", "Display Top Action Button"],
    "Create Single Material": ["General Information", "Accounting", "Basic Data", "Classification", "Purchasing", "Sales", "Attachments & Comments"],
    Dashboard: ["Dashboard"],
    Workspace: [
      "Workspace",
      "My Tasks",
      "Completed Tasks",
      "Admin Tasks",
      "Admin Completed Tasks",
    ],
    Material: ["Create Multiple", "Create Single", "Search Material"],
    "Master Data": ["Material", "BOM", "Cost Center", "Profit Center", "General Ledger"],
    "User Management": ["Delete User", "Edit Application", "Groups", "Roles", "Systems", "User Workbench", "Users"],
    "Request Bench": ["Request Bench", "Cost Center","Manage Scheduler"],
    "Request History": ["Request History"],
    "Business Rules": ["Business Rules", "Modelling", "Authoring"],
  };

  const fetchToken = async () => {
    try {
      const response = await axios(VITE_URL_AUTH_TOKEN);
      setIsAppReady(true);
      dispatch(setToken({ token: response.data }));
    } catch (e) {
      customError(e)
      if (applicationConfig.environment === "localhost" || applicationConfig.environment === "127.0.0.1") {
        setIsAppReady(true);
      }
    }
  };

  const fetchIDMToken = async () => {
    try {
      const response = await axios(VITE_URL_AUTH_TOKEN_CAF);
      dispatch(
        setIdmToken({
          idmToken:response.data
        })
      );
    } catch (e) {
      customError("Failed to fetch IDM token:", e)
    }
  };

  
const fetchIWAToken = async () => {
  try {
    await doAjax(
      `/${destination_MaterialMgmt}/authenticate/tokenIWA`, // relative URL
      "get",
      (res) => {
        dispatch(setIwaToken({ iwaToken: res.body }));
      },
      (err) => {
        customError("Error fetching IWA token", err);
      }
    );
  } catch (err) {
    customError("Unexpected error in fetchIWAToken", err);
  }
};

  const setupApplication = () => {
    if (applicationConfig.environment === "localhost" || applicationConfig.environment === "127.0.0.1") {
      fetchToken();
      fetchIDMToken();
      fetchIWAToken()
    } else {
      setIsAppReady(true);
    }
  };

  const fetchCurrentSAPSystem = () => {
    const hSuccess = (data) => {
      dispatch(setCurrentSAPSystem(data?.SapSystem));
    };
    const hError = (error) => {
      showToast(ERROR_MESSAGES.FAILED_FETCH_SAP_SYSTEM, "error");
    };
    doAjax(
       `/${destination_MaterialMgmt}${END_POINTS.SYSTEM_CONFIG.GET_CURRENT_SAP_SYSTEM}`,
      "get",
      hSuccess,
      hError
    );
  };

   const { fetchAndDispatchUser, moduleAccessStatus, finalEmailId } = useUserBootstrap({
    fallbackUser: {
      id: "P001344",
      user_id: "<EMAIL>",
      firstName: "Suvendu",
      lastName: "Samantaray",
      emailId: "<EMAIL>",
      displayName: "Suvendu Samantaray",
      userName: "INC02117",
      roles: [""],
    },
    fallbackEntities: initialEntities,
    isLocalEnv: ["localhost", "127.0.0.1"].includes(applicationConfig.environment),
  });

    useEffect(() => {
    if (moduleAccessStatus === true) {
      setValidUser(true);
    } else if (moduleAccessStatus === false) {
      setValidUser(false);
    }
  }, [moduleAccessStatus]);

    useEffect(() => {
    if (finalEmailId) {
      setEmailId(finalEmailId);
    }
  }, [finalEmailId]);
   const { fetchModuleAccess } = useModuleAccess();

  useEffect(() => {
    const getApirefresh = () => {
      const hSuccess=()=>{}
      const hError=()=>{
        setSessionExpired(true)
      }
      doAjax(`/${destination_Admin}${END_POINTS?.DUMMY_API}`,'get',hSuccess,hError)
    }
    const intervalId = setInterval(() => {
      getApirefresh();
    }, 120000); // Call the API every 2 minutes
    return () => clearInterval(intervalId);
  }, []);

  const emailRef = useRef(emailId);
  const location = useLocation();
  const urlRef = useRef(false);

  useEffect(() => {
    const isRequestBenchPage = 
    location?.pathname?.endsWith(APP_END_POINTS?.REQUEST_BENCH) || location?.hash?.endsWith(APP_END_POINTS?.REQUEST_BENCH);
    urlRef.current = isRequestBenchPage;
  }, [location]);

  useEffect(() => {
    emailRef.current = emailId;
    if(emailId) {
      fetchAllNotifications()
    }
  }, [emailId]);



  const fetchAsyncResponse = async () => {
    const client = new Client({
      webSocketFactory: () => new SockJS(`${baseUrl_Websocket}/ws`),
      debug: (msg) => log("[STOMP DEBUG]", msg),
      reconnectDelay: 5000,
      onConnect: () => {
        log("Connected to STOMP WebSocket!");
        client.subscribe("/topic/dbUpdate", (message) => {
          let jSonMsg = JSON.parse(message?.body)
          log(jSonMsg, "jSonMsg");
          if(emailRef.current === jSonMsg?.user){
            showSnackbar(jSonMsg?.message,jSonMsg?.statusCode === API_CODE.STATUS_200 ? "success" : "error")
            dispatch(setNotificationData(jSonMsg));
            if(urlRef.current) {
              getFilter(0, 0, true)
            }
          }
          
        });
      },
      onStompError: (frame) => {
        customError(frame.headers["message"]);
      },
    });

    client.activate();

    return () => {
      client.deactivate();
    };
  };

  const NPITheme = createTheme({
    palette: {
      primary: {
        main: "#3b30c8",
      },
      secondary: {
        main: "#2cbc34",
      },
      danger: {
        main: "#DA2C2C",
      },
      neutral: {
        main: "#eae9ff",
      },
      success: {
        main: "#2cbc34",
      },
    },
    background: {
      default: "#FAFCFF",
    },
    typography: {
      h3: {
        fontSize: "20px",
        fontWeight: "600",
        color: "#1d1d1d",
      },
      h4: {
        fontSize: "18px",
        fontWeight: "600",
        color: "#1d1d1d",
      },
      h5: {
        fontSize: "16px",
        fontWeight: "600",
        color: "#1d1d1d",
      },
      h6: {
        fontSize: "14px",
        fontWeight: "600",
        color: "#1d1d1d",
      },
      body1: {
        fontSize: "14px",
      },
      body2: {
        fontSize: "12px",
      },
      caption: {
        fontSize: "10px",
      },
    },
    components: {
      MuiMenu: {
        styleOverriddes: {
          root: {
            maxHeight: "3rem",
            backgroundColor: "red",
          },
        },
      },
    },
  });

  const fetchAllNotifications = () => {
    doAjax(`/${destination_Websocket}/notifications/fetch/unread/${emailId}`,
      "get",
      (data) => {
        if(data?.length) {
          dispatch(setNotificationsDirect([...data].reverse()));
        }
      },
      () => {}
    )
  }

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  useEffect(() => {
    if (applicationConfig.environment === "localhost" || applicationConfig.environment === "127.0.0.1") {
      fetchAndDispatchUser(); // handles user + roles
      fetchLangTranslation();
    }
  }, [idmToken,iwaToken,langSelected]);

  useEffect(() => {
    fetchLangTranslation();
  }, [langSelected]);

  useEffect(() => {
    setupApplication();
    fetchAndDispatchUser();
    fetchModuleAccess();
    fetchAsyncResponse();
    fetchAllDropdownMstrData();
    fetchLangTranslation();
    fetchCurrentSAPSystem();
  }, []);

  useEffect(() => {
    if (userData?.emailId) {
      fetchGlobalAppSettings(userData, dispatch);
    }
  }, [userData?.emailId]);

  useEffect(() => {
    if(userData?.emailId)
    fetchNotificationPreferences()
  }, [userData?.emailId]);

  const fetchLangTranslation = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_LANGUAGE_CONFIG",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_LANGUAGE_NAME": langSelected
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        dispatch(setLangTranslation(data.data.result[0].MDG_LANGUAGE_CONFIG_ACTION_TYPE));
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(`/${destination_IDM}/rest/v1/invoke-rules`, "post", hSuccess, hError, payload);
    } else {
      doAjax(`/${destination_IDM}/v1/invoke-rules`, "post", hSuccess, hError, payload);
    }
  };

  const fetchNotificationPreferences = () => {
    doAjax(`/${destination_Websocket}/notification-preferences/${userData?.emailId}`,
      "get",
      (data) => {
        dispatch(setNotificationPreference(data?.inAppNotification))
      },
      () => {},
    );
  }
  if (isSessionExpired) {
    return <SessionExpiredScreen />;
  }

  return (
    <ErrorBoundary>
      <>
        {isAppReady && (
          <div className="App">
            {validUser !== "loading" && validUser && (
              <ThemeProvider theme={NPITheme}>

                  <Suspense fallback={<LoadingComponent />}>
                    <Routes>
                      <Route path="*" element={<ApplicationRouter timeRemaining={timeRemaining} extendSession={extendSession}/>} />
                    </Routes>
                  </Suspense>
       
              </ThemeProvider>
            )}
            {validUser !== "loading" && !validUser && (
              <ThemeProvider theme={NPITheme}>

                  <Routes>
                    <Route path="*" element={<InvalidUser validUser={validUser} />} />
                  </Routes>
       
              </ThemeProvider>
            )}
          </div>
        )}
        
        <ReduxSnackbar/>
        <ReusableDialog
          dialogState={openMessageDialog}
          openReusableDialog={handleMessageDialogClickOpen}
          closeReusableDialog={handleMessageDialogClose}
          dialogTitle={messageDialogTitle}
          dialogMessage={messageDialogMessage}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          dialogSeverity={messageDialogSeverity}
        />
        <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
        <NetworkStatus/>
        <FloatingChatbot
          emailId={emailId}
        />
        <ToastContainer/>
      </>
     </ErrorBoundary>
  );
}

export default App;
