import { createSlice } from "@reduxjs/toolkit";

let initialState = {
  page: 0,
  size: 10,
  sort: "",
  isNextButtonDisabled: false,
  totalElements: 0,
  currentElements: 0,
  slNo: 0,
  existingCreatePages: [0],
};
export const paginationSlice = createSlice({
  name: "paginationData",
  initialState: initialState,
  reducers: {
    updatePage: (state, action) => {
       state.page = action.payload;
    },
    updateExistingCreatePages: (state, action) => {
       state.existingCreatePages.push(action.payload);
    },
    updateTotalCount: (state, action) => {
       state.totalElements = action.payload;
    },
    updateCurrentCount: (state, action) => {
       state.currentElements = action.payload;
    },
    updateNextButtonStatus: (state, action) => {
       state.isNextButtonDisabled = action.payload;
    },
    updateSlNo: (state, action) => {
      if (typeof action.payload === "object" && action.payload !== null) {
        state.slNo = { ...action.payload };
      } else {
        state.slNo = action.payload;
      }
    },
    clearPaginationData: (state) => {
       state.page = 0;
       state.currentElements = 0;
       state.totalElements = 0;
       state.isNextButtonDisabled = false;
       state.slNo = 0;
       state.existingCreatePages = [0];
    },
  },
});

export const { updatePage, updateTotalCount, updateExistingCreatePages, updateCurrentCount, clearPaginationData, updateSlNo, updateNextButtonStatus } = paginationSlice.actions;

export const paginationReducer = paginationSlice.reducer;