import { useSnackbar } from './useSnackbar';
import { API_CODE, ERROR_MESSAGES, LOADING_MESSAGE, MODULE, MODULE_MAP, REQUEST_TYPE, SUCCESS_MESSAGES } from '../constant/enum';
import { END_POINTS } from '../constant/apiEndPoints';
import { APP_END_POINTS } from '../constant/appEndPoints';
import { doAjax } from '../components/Common/fetchService';
import { useNavigate } from 'react-router-dom';
import { destination_BankKey } from '../destinationVariables';

const useDownloadExcel = (module) => {
    const { showSnackbar } = useSnackbar();
    const navigate = useNavigate()
    const destination = END_POINTS?.MODULE_DESTINATION_MAP?.[module]

    const handleUploadMaterial = (file,setLoaderMessage, setBlurLoading, payloadFields, module,RequestType,requestId,rowData) => {
        let url = "";
        if (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) {
          url = "getAllBankKeysFromExcel";
        } 
        setLoaderMessage("Initiating Excel Upload");
        setBlurLoading(true);
        const formData = new FormData();
        [...file].forEach((item) => formData.append("files", item));
        formData.append("requestId", requestId ? requestId : "");
        formData.append("dtName", "MDG_BNKY_FIELD_CONFIG");
        formData.append("version","v2");
        formData.append("region", rowData?.Region || "US");
        formData.append("bankCtry", rowData?.bankCtry || "US");
        formData.append("role", "Z_FIN_REQ_DOWNLOAD");
    
        const hSuccess = (data) => {
          if (data.statusCode === API_CODE?.STATUS_200) {
            setEnableDocumentUpload(false);
            setBlurLoading(false);
            setLoaderMessage("");
            navigate(APP_END_POINTS?.REQUEST_BENCH);
          } else {
            setEnableDocumentUpload(false);
            setBlurLoading(false);
            setLoaderMessage("");
            navigate(APP_END_POINTS?.REQUEST_BENCH);
          }
        };
        const hError = (error) => {
          setBlurLoading(false);
          setLoaderMessage("");
          navigate(APP_END_POINTS?.REQUEST_BENCH);
        };
        doAjax(
          `/${destination_BankKey}/massAction/${url}`,
          "postformdata",
          hSuccess,
          hError,
          formData
        );
      };

    const handleDownload = (setLoaderMessage, setBlurLoading, payloadFields, module) => {
        setLoaderMessage(LOADING_MESSAGE?.REPORT_LOADING);
        setBlurLoading(true);

        const hSuccess = (response) => {
            if (response?.size == 0) {
                setBlurLoading(false);
                setLoaderMessage("");
                showSnackbar(ERROR_MESSAGES?.DATA_NOT_FOUND_FOR_SEARCH, "error");
                return;
            }
            const href = URL.createObjectURL(response);
            const link = document.createElement("a");

            link.href = href;
            link.setAttribute(
                "download",
                payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
                    ? `${module}_Mass Change.xlsx`
                    : `${module}_Mass Create.xlsx`
            );
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(href);
            setBlurLoading(false);
            setLoaderMessage("");
            showSnackbar(
                `${payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
                    ? `${module}_Mass Change`
                    : `${module}_Mass Create`
                }.xlsx has been downloaded successfully.`,
                "success"
            );
            setTimeout(() => {
                navigate(`${APP_END_POINTS?.REQUEST_BENCH}`);
            }, 2600);
        };
        const hError = () => {
            setBlurLoading(false);
        };

        if(module=== MODULE_MAP?.BK){
            const payload = {
                requestId: payloadFields?.RequestId || "",
                dtName: "MDG_BNKY_FIELD_CONFIG",
                version: "v2",
                region: payloadFields?.Region || "US",
                bankCtry: payloadFields?.BankCtry || "US",
                rolePrefix: "Z_FIN_REQ_DOWNLOAD",
                scenario: "Create with Upload",
            };
            const downloadUrl = `/${destination_BankKey}${
            payloadFields?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD
                ? END_POINTS.EXCEL.DOWNLOAD_EXCEL
                : END_POINTS.EXCEL.DOWNLOAD_EXCEL_WITH_DATA
            }`;
            doAjax(downloadUrl, "postandgetblob", hSuccess, hError,payload);
        }
        else{
        const downloadUrl = `/${destination}${payloadFields?.RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
            ? END_POINTS.EXCEL.DOWNLOAD_EXCEL_PCG_CHANGE_WITH_UPLOAD
            : END_POINTS.EXCEL.DOWNLOAD_EXCEL_PCG_CREATE_WITH_UPLOAD
            }`;
        doAjax(downloadUrl, "getblobfile", hSuccess, hError);
    }
    };

    const handleEmailDownload = () => {
        setBlurLoading(true);
        const hSuccess = () => {
            setBlurLoading(false);
            setLoaderMessage("");
            showSnackbar(SUCCESS_MESSAGES?.DOWNLOAD_MAIL_INITIATED, "success");
            setTimeout(() => {
                navigate(APP_END_POINTS?.REQUEST_BENCH);
            }, 2600);
        };
        const hError = () => {
            setBlurLoading(false);
            showSnackbar(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL, "error");
            setTimeout(() => {
                navigate(APP_END_POINTS?.REQUEST_BENCH);
            }, 2600);
        };
        if(module=== MODULE_MAP?.BK){
            const payload = {
                requestId: payloadFields?.RequestId || "",
                dtName: "MDG_BNKY_FIELD_CONFIG",
                version: "v2",
                region: payloadFields?.Region || "US",
                bankCtry: payloadFields?.BankCtry || "US",
                rolePrefix: "Z_FIN_REQ_DOWNLOAD",
                scenario: "Create with Upload",
            };
            const downloadUrl = `/${destination_BankKey}${
            payloadFields?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
                ? END_POINTS.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL
                : END_POINTS.EXCEL.DOWNLOAD_EXCEL_MAIL
            }`;
        
            doAjax(downloadUrl, "post", hSuccess, hError,payload);
        }
        else{
        const downloadUrl = `/${destination}${payloadFields?.RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
                ? END_POINTS.EXCEL.DOWNLOAD_EXCEL_PCG_CHANGE_WITH_UPLOAD_MAIL
                : END_POINTS.EXCEL.DOWNLOAD_EXCEL_PCG_CREATE_WITH_UPLOAD_MAIL
            }`;

        doAjax(downloadUrl, "get", hSuccess, hError);}
    };

    return {
        handleDownload,
        handleEmailDownload,
        handleUploadMaterial
    };
};

export default useDownloadExcel;
