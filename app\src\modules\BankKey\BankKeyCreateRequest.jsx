import {
  <PERSON>,
  But<PERSON>,
  <PERSON><PERSON>A<PERSON>,
  <PERSON>alogContent,
  <PERSON>rid,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Stepper,
  Typo<PERSON>,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { ArrowCircleLeftOutlined } from "@mui/icons-material";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import useLang from "@hooks/useLang";
import { APP_END_POINTS } from "@constant/appEndPoints";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { WarningOutlined } from "@mui/icons-material";
import {
  button_Outlined,
  button_Primary,
} from "@components/common/commonStyles";
import { colors } from "@constant/colors";
import {
  API_CODE,
  ARTIFACTNAMES,
  DECISION_TABLE_NAME,
  DIALOUGE_BOX_MESSAGES,
  LOADING_MESSAGE,
  LOCAL_STORAGE_KEYS,
  MODULE_MAP,
  REGION,
  REQUEST_STATUS,
  REQUEST_TYPE,
  TASK_NAME,
} from "@constant/enum";
import {
  setHeaderFieldsBnky,
  setTabValue,
  setDropDownDataBNKY,
  setOdataApiCall,
  setRequestHeaderPayloadData,
  resetPayloadData,
  resetValidationStatus,
  resetBankKeyStateBk,
  setBankKeyPayload,
  setSelectedRowID
} from "./bnkySlice";
import { setCreatePayloadCopyForChangeLog } from "@app/changeLogReducer"
import { setRequestHeader } from "@app/requestDataSlice";
import { setDynamicKeyValue } from "@app/payloadSlice";
import RequestHeaderBankKey from "./RequestHeaderBankKey";
import useGenericDtCall from "@hooks/useGenericDtCall";
import BankKeyListDetails from "./BankKeyListDetails";
import { doAjax } from "@components/Common/fetchService";
import { destination_BankKey, destination_BOM, destination_IDM } from "../../destinationVariables";
import { appendPrefixByJavaKey, clearLocalStorageItem, setLocalStorage } from "@helper/helper.js";
import useDropdownFMDData from "../modulesHooks/useDropdownFMDData";
import AttachmentsCommentsTab from "@components/RequestBench/RequestPages/AttachmentsCommentsTab";
import PreviewPage from "@components/RequestBench/PreviewPage";
import { idGenerator, transformApiResponseToReduxPayloadBk } from "../../functions";
import ExcelOperationsCard from "@components/Common/ExcelOperationsCard";
import useDownloadExcel from "@hooks/useDownloadExcel";
import FileUploadOutlinedIcon from "@mui/icons-material/FileUploadOutlined";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import SummarizeOutlinedIcon from "@mui/icons-material/SummarizeOutlined";
import { END_POINTS } from "@constant/apiEndPoints";
import ErrorReportDialog from "@components/Common/ErrorReportDialog";
import ChangeLogGlobal from "@components/Changelog/ChangeLogGlobal"

const BankKeyCreateRequest = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { getDtCall, dtData } = useGenericDtCall();
  const { t } = useLang();
  const urlSearchParams = new URLSearchParams(location.search.split("?")[1]);
  const requestId = urlSearchParams.get("RequestId");
  const steps = [
    t("Request Header"),
    t("Bank Key List"),
    t("Attachments & Remarks"),
    t("Preview"),
  ];
  const rowData = location.state;
  const requestIdHeader = useSelector((state) => state.bankKey.requestHeaderResponse?.requestId || "");
  const requestType = useSelector((state) => state.bankKey.requestHeaderResponse?.requestType || "");
  const payloadFields = useSelector((state) => state.bankKey.payload);
  const tabValue = useSelector((state) => state.bankKey.tabValue);
  const queryParams = new URLSearchParams(location.search);
  const reqBench = queryParams.get("reqBench");
  const RequestId = queryParams.get("RequestId");
  const RequestType = queryParams.get("RequestType");

  const isBankKeyApiCalled = useSelector((state) => state.bankKey?.isOdataApiCalled)
  const { fetchAllDropdownFMD } = useDropdownFMDData(destination_BankKey, setDropDownDataBNKY);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const reduxPayload = useSelector((state => state.bankKey));

  const [isDialogVisible, setisDialogVisible] = useState(false);
  const [completed, setCompleted] = useState([false]);
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(false);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const [attachmentsData, setAttachmentsData] = useState([]);
  const [apiResponses, setApiResponses] = useState([]);
  const [downloadClicked, setDownloadClicked] = useState(false);
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState(false);
  const { handleUploadMaterial } = useDownloadExcel(MODULE_MAP?.BK);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [pcNumber, setPcNumber] = useState("");

  useEffect(() => {
    if (isSecondTabEnabled) {
      setCompleted([true]);
    }
  }, [isSecondTabEnabled]);

  useEffect(() => {
    if (!requestId) {
      dispatch(setTabValue(0));
    }
    fetchHeaderFieldsFromDt();
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE, MODULE_MAP.BK);
    getAttachmentsIDM();
    setPcNumber(idGenerator("BK"));
  }, []);

  useEffect(() => {
    if (!isBankKeyApiCalled) {
      fetchAllDropdownFMD("bankKey")
      dispatch(setOdataApiCall(true))
    }
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE, MODULE_MAP.BK)
    return () => {
      clearLocalStorageItem(LOCAL_STORAGE_KEYS.MODULE)
    }
  }, []);

  useEffect(() => {
    if (dtData) {
      let responseData = dtData?.result[0]?.MDG_MAT_REQUEST_HEADER_CONFIG
        ;
      const formattedData = responseData
        .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
        .map((item) => ({
          fieldName: item.MDG_MAT_UI_FIELD_NAME,
          sequenceNo: item.MDG_MAT_SEQUENCE_NO,
          fieldType: item.MDG_MAT_FIELD_TYPE,
          maxLength: item.MDG_MAT_MAX_LENGTH,
          value: item.MDG_MAT_DEFAULT_VALUE,
          visibility: item.MDG_MAT_VISIBILITY,
          jsonName: item.MDG_MAT_JSON_FIELD_NAME,
        }));

      const requestHeaderObj = { "Header Data": formattedData };
      dispatch(setHeaderFieldsBnky(requestHeaderObj));
    }
  }, [dtData]);

  const handleTabChange = (index) => {
    dispatch(setTabValue(index));
  };

  const handleYes = () => {
    if (requestId && !reqBench) {
      navigate(APP_END_POINTS?.MY_TASK);
    } else if (reqBench) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    } else if (!requestId && !reqBench) {
      navigate(APP_END_POINTS?.BANKKEY);
    }
  };

  const openChangeLog = () => {
    setisChangeLogopen(true);
  };

  const handleDownload = () => {
    setDownloadClicked(true);
  };

  const handleUploadMaterialBankKey = (file) => {
    handleUploadMaterial(file, setLoaderMessage, setBlurLoading, payloadFields, MODULE_MAP?.BK, RequestType, requestId, rowData);
  }

  const handleCancel = () => {
    setisDialogVisible(false);
  };

  const onlyDigits = (val) => String(val || "").replace(/\D/g, "");

  const getAttachmentsIDM = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "Material",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    const hSuccess = (data) => {
      if (data.statusCode === API_CODE?.STATUS_200) {
        let responseData = data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE;
        const attachmentNames = responseData || [];
        setAttachmentsData(attachmentNames);
      }
    };

    const hError = (error) => {
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(`/${destination_IDM}${END_POINTS?.INVOKE_RULES?.LOCAL}`, "post", hSuccess, hError, payload);
    } else {
      doAjax(`/${destination_IDM}${END_POINTS?.INVOKE_RULES?.PROD}`, "post", hSuccess, hError, payload);
    }
  };

  const handleExportTemplateExcel = () => {
    const url = RequestId
      ? END_POINTS.EXCEL.EXPORT_EXCEL_BK
      : END_POINTS.EXCEL.EXPORT_EXCEL_BK;
    setLoaderMessage(LOADING_MESSAGE?.REPORT_LOADING);
    setBlurLoading(true);
    const isChildPresent = rowData?.childRequestIds !== "Not Available";
    let payload = {
      dtName:
        payloadFields?.RequestType === REQUEST_TYPE?.CHANGE ||
          payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
          ? "MDG_BNKY_FIELD_CONFIG"
          : "MDG_BNKY_FIELD_CONFIG",
      version:
        payloadFields?.RequestType === REQUEST_TYPE?.CHANGE ||
          payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
          ? "v2"
          : "v2",
      parentRequestId: reqBench && !isChildPresent ? requestId : "",
      childRequestId: (!reqBench && requestId) || (reqBench && isChildPresent) ? requestId : "",
      scenario:
        payloadFields?.RequestType === REQUEST_TYPE?.CHANGE ||
          payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD ||
          payloadFields?.RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
          ? "Change with Upload"
          : "Create with Upload",
      templateName: payloadFields?.TemplateName || "",
      region: payloadFields?.Region || "US",
      bankCtry: payloadFields?.BankCtry || "US",
      rolePrefix: TASK_NAME?.REQ_INITIATE_DOWNLOAD,
      templateName: "",
    };

    const hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute(
        "download",
        `Bank Key_Data Export.xlsx`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");

      setSuccessMsg(true);
      setAlertType("success");
      handleSnackBarOpen();
    };
    const hError = () => { };
    doAjax(
      `/${destination_BankKey}${url}`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  const getDisplayDataBK = async (requestId) => {
    const isChildPresent = rowData?.childRequestIds !== "Not Available";

    const payload = reqBench ? {

      sort: "id,asc",
      childRequestId: isChildPresent ? requestId : "",
      parentRequestId: !isChildPresent ? requestId : "",
      page: 0,
      size: 10,

    } : {

      sort: "id,asc",
      childRequestId: isChildPresent ? requestId : "",
      parentRequestId: !isChildPresent ? requestId : "",
      page: 0,
      size: 10,

    }

    const hSuccess = (response) => {
      const apiResponse = response?.body || [];
      let requestHeaderData = response?.body[0]?.Torequestheaderdata;
      let TotalIntermediateTasks = response?.body[0]?.TotalIntermediateTasks;

      dispatch(
        setRequestHeaderPayloadData({
          RequestId: requestHeaderData.RequestId,
          RequestPrefix: requestHeaderData.RequestPrefix,
          ReqCreatedBy: requestHeaderData.ReqCreatedBy,
          ReqCreatedOn: requestHeaderData.ReqCreatedOn,
          ReqUpdatedOn: requestHeaderData.ReqUpdatedOn,
          RequestType: requestHeaderData.RequestType,
          RequestDesc: requestHeaderData.RequestDesc,
          RequestStatus: requestHeaderData.RequestStatus,
          RequestPriority: requestHeaderData.RequestPriority,
          FieldName: requestHeaderData.FieldName,
          TemplateName: requestHeaderData.TemplateName,
          Division: requestHeaderData.Division,
          region: requestHeaderData.region,
          leadingCat: requestHeaderData.leadingCat,
          firstProd: requestHeaderData.firstProd,
          launchDate: requestHeaderData.launchDate,
          isBifurcated: requestHeaderData.isBifurcated,
          screenName: requestHeaderData.screenName,
          TotalIntermediateTasks: TotalIntermediateTasks,
        })
      );

      setApiResponses(apiResponse);
      const reqType = requestHeaderData?.RequestType;
      if (
        reqType === REQUEST_TYPE.CHANGE ||
        reqType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
      ) {
        getChangeTemplate();
      }
      const transformedPayload =
        transformApiResponseToReduxPayloadBk(apiResponse);
      dispatch(setSelectedRowID(transformedPayload?.payload?.rowsHeaderData[0]?.id))
      dispatch(setBankKeyPayload(transformedPayload?.payload));
      dispatch(
          setDynamicKeyValue({
              keyName: "childRequestHeaderData",
              data: transformedPayload?.payload?.childRequestHeaderData
          })
      )
      dispatch(setCreatePayloadCopyForChangeLog(transformedPayload?.payload?.rowsBodyData));

    };

    const hError = (error) => {
    };

    doAjax(
      `/${destination_BankKey}/${END_POINTS?.CHG_DISPLAY_REQUESTOR?.DISPLAY_BK}`,
      "post",
      hSuccess,
      hError,
      payload
    );

  };

  useEffect(() => {
    const loadData = async () => {
      if (RequestId) {
        await getDisplayDataBK(RequestId);
        if (
          ((RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD &&
            !rowData?.length) ||
            RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) &&
          (rowData?.reqStatus === REQUEST_STATUS.DRAFT ||
            rowData?.reqStatus === REQUEST_STATUS.UPLOAD_FAILED)
        ) {
          dispatch(setTabValue(0));
          setIsSecondTabEnabled(false);
          setIsAttachmentTabEnabled(false);
        } else {
          dispatch(setTabValue(1));
          setIsSecondTabEnabled(true);
          setIsAttachmentTabEnabled(true);
        }
      } else {
        dispatch(setTabValue(0));
      }
    };

    loadData();
    return () => {
      dispatch(resetPayloadData());
      dispatch(setRequestHeader({}));
      dispatch(resetValidationStatus());
      dispatch(resetBankKeyStateBk());
    };
  }, [requestId, dispatch]);

  const fetchHeaderFieldsFromDt = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_FMD_REQUEST_HEADER_CONFIG,
      version: "v2",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_SCENARIO": REQUEST_TYPE?.CREATE,
          "MDG_CONDITIONS.MDG_MAT_MODULE_NAME": MODULE_MAP?.BK,
        },
      ],
    };
    getDtCall(payload);
  };
  return (
    <>
      <Box sx={{ padding: 2 }}>
        <Grid
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {requestIdHeader || requestId ? (
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                textAlign: "left",
                display: "flex",
                alignItems: "center",
                gap: 1,
              }}
            >
              <PermIdentityOutlinedIcon sx={{ fontSize: "1.5rem" }} />
              {t("Request Header ID")}:{" "}
              <span>{requestIdHeader ? requestIdHeader : requestId}</span>
            </Typography>
          ) : (
            <div style={{ flex: 1 }} />
          )}
          {tabValue === 1 && (
            <Box
              sx={{ display: "flex", justifyContent: "flex-end", gap: "1rem" }}
            >
              <Button
                variant="outlined"
                size="small"
                title="Download Error Report"
                disabled={!RequestId}
                onClick={() => setDialogOpen(true)}
                color="primary"
              >
                <SummarizeOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
              <Button
                variant="outlined"
                disabled={false}
                size="small"
                onClick={openChangeLog}
                title="Change Log"
              >
                <TrackChangesTwoToneIcon sx={{ padding: "2px" }} />
              </Button>
              <Button
                variant="outlined"
                disabled={!RequestId}
                size="small"
                onClick={handleExportTemplateExcel}
                title="Export Excel"
              >
                <FileUploadOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
            </Box>
          )}
        </Grid>

        {isChangeLogopen &&
          <ChangeLogGlobal
            open={true}
            closeModal={() => setisChangeLogopen(false)}
            requestId={requestIdHeader || requestId}
            requestType={RequestType || requestType}
            module={MODULE_MAP?.BK}
          />
        }

        {payloadFields?.TemplateName && (
          <Typography
            variant="h6"
            sx={{
              mb: 1,
              textAlign: "left",
              display: "flex",
              alignItems: "center",
              gap: 1,
            }}
          >
            <FeedOutlinedIcon sx={{ fontSize: "1.5rem" }} />
            {t("Template Name")}: <span>{payloadFields?.TemplateName}</span>
          </Typography>
        )}
        <IconButton
          onClick={() => {
            if (reqBench) {
              navigate(APP_END_POINTS?.REQUEST_BENCH);
              return;
            }
            setisDialogVisible(true);
          }}
          color="primary"
          aria-label="upload picture"
          component="label"
          sx={{ left: "-10px" }}
          title={t("Back")}
        >
          <ArrowCircleLeftOutlined
            sx={{ fontSize: "25px", color: "#000000" }}
          />
        </IconButton>

        <Stepper
          nonLinear
          activeStep={tabValue}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            margin: "25px 14%",
            marginTop: "-35px",
          }}
        >
          {steps.map((label, index) => (
            <Step key={label} completed={completed[index]}>
              <StepButton
                color="error"
                disabled={
                  (index === 1 && !isSecondTabEnabled) ||
                  (index === 2 && !isAttachmentTabEnabled) ||
                  (index === 3 && !isAttachmentTabEnabled)
                }
                onClick={() => handleTabChange(index)}
                sx={{ fontSize: "50px", fontWeight: "bold" }}
              >
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>
                  {label}
                </span>
              </StepButton>
            </Step>
          ))}
        </Stepper>

        <ErrorReportDialog
          dialogState={dialogOpen}
          closeReusableDialog={() => setDialogOpen(false)}
          module={MODULE_MAP?.BK}
        />

        {tabValue === 0 && (
          <>
            <RequestHeaderBankKey
              setIsSecondTabEnabled={setIsSecondTabEnabled}
              setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
              downloadClicked={downloadClicked}
              setDownloadClicked={setDownloadClicked}
            />
            {(RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD ||
              RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) &&
              ((rowData?.reqStatus == REQUEST_STATUS.DRAFT &&
                !rowData?.objectNumbers !== "Not Available") ||
                rowData?.reqStatus == REQUEST_STATUS.UPLOAD_FAILED) && (
                <ExcelOperationsCard
                  handleDownload={handleDownload}
                  setEnableDocumentUpload={setEnableDocumentUpload}
                  enableDocumentUpload={enableDocumentUpload}
                  handleUploadMaterial={handleUploadMaterialBankKey}
                />
              )}
          </>
        )}
        {tabValue === 1 && (
          <BankKeyListDetails
            setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
            setCompleted={setCompleted}
            downloadClicked={downloadClicked}
            setDownloadClicked={setDownloadClicked}
            requestStatus={rowData?.reqStatus ? rowData?.reqStatus : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME}
          />
        )}
        {tabValue === 2 && (
          <AttachmentsCommentsTab
            requestStatus={
              rowData?.reqStatus
                ? rowData?.reqStatus
                : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME
            }
            attachmentsData={attachmentsData}
            requestIdHeader={
              requestIdHeader
                ? requestIdHeader
                : requestId
            }
            pcNumber={pcNumber}
            childRequestIds={rowData?.childRequestIds ? rowData?.childRequestIds : "Not Available"}
            module={MODULE_MAP?.BK}
            artifactName={ARTIFACTNAMES.BK}
          />
        )}
        {tabValue === 3 && (
          <Box
            sx={{
              width: "100%",
              overflow: "auto",
            }}
          >
            <PreviewPage requestStatus={rowData?.reqStatus ? rowData?.reqStatus : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME} module={MODULE_MAP?.BK} payloadData={reduxPayload} payloadForDownloadExcel={""} />
          </Box>
        )}
      </Box>
      {isDialogVisible && (
        <CustomDialog
          isOpen={isDialogVisible}
          titleIcon={
            <WarningOutlined
              size="small"
              sx={{ color: colors?.secondary?.amber, fontSize: "20px" }}
            />
          }
          Title={t("Warning")}
          handleClose={handleCancel}
        >
          <DialogContent sx={{ mt: 2 }}>
            {t(DIALOUGE_BOX_MESSAGES.LEAVE_PAGE_MESSAGE)}
          </DialogContent>
          <DialogActions>
            <Button
              variant="outlined"
              size="small"
              sx={{ ...button_Outlined }}
              onClick={handleCancel}
            >
              {t("No")}
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary }}
              onClick={handleYes}
            >
              {t("Yes")}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </>
  );
};

export default BankKeyCreateRequest;
