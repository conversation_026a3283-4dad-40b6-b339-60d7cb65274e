import React, { useState, useEffect } from "react";
import {
  Typo<PERSON>,
  Grid,
  Paper,
  Tooltip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Menu,
  MenuItem,
} from "@mui/material";
import { HiStatusOffline, HiStatusOnline } from "react-icons/hi";
import { MdEdit } from "react-icons/md";
import { makeStyles, withStyles } from "@mui/styles";
import { Add, Refresh, MoreVert } from "@mui/icons-material";
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined';
import { useSelector, useDispatch } from "react-redux";
import { getAllApplications } from "../../Action/action";
import {
  setApplications,
  setResponseMessage,
} from "../../../../../app/userManagementSlice";
import Loading from "../Loading";
import NewApplication from "./NewApplication";
import { applicationFileHeading } from "../../Utility/file";
import UploadFile from "../UploadFile";
import ApplicationDetail from "./ApplicationDetail";
import DeletionMessageBox from "../DeletionMessageBox";
import { toastMessage } from "../../Utility/config";
import SystemDetails from "./SystemDetails";
import {
  applicationFooterButton,
  applicationPageHeaderHeight,
  sidebarWidth,
  appHeaderHeight,
} from "../../Data/cssConstant";
import { CSVLink } from "react-csv";
import { destination_IWA } from "../../../../../destinationVariables";
import { checkIwaAccess } from "../../../../../functions";

const useStyle = makeStyles((theme) => ({
  systemContainer: {
    border: `0.5px solid ${theme.palette.divider}`,
    borderRadius: 8,
    padding: 10,
    position: "relative",
    // "&:hover": {
    //   "& $systemOwnerEmailEditButton": {
    //     display: "flex",
    //   },
    // },
  },
  systemInfoContainer: {
    display: "flex",
    alignItems: "center",
    borderBottom: `1px solid ${theme.palette.divider}`,
    paddingBottom: 4,
  },
  systemImage: {
    width: 38,
    height: 32,
    objectFit: "contain",
  },
  systemInfoContent: {
    display: "flex",
    flexDirection: "column",
    flex: 1,
    paddingLeft: 8,
  },
  systemName: {
    fontSize: 14,
    fontWeight: 500,
    cursor: "pointer",
    "&:hover": {
      textDecoration: "underline",
    },
  },
  systemUniqueId: {
    fontSize: 14,
    textTransform: "uppercase",
    textOverflow: "ellipsis",
    overflow: "hidden",
    whiteSpace: "nowrap",
    width: "90%",
  },
  systemBulbIcon: {
    fontSize: 20,
    color: theme.palette.text.secondary,
  },
  systemBulbIconOnline: {
    color: theme.palette.success.main,
  },
  systemBulbIconOffline: {
    color: theme.palette.error.main,
  },
  systemOwnerEmailLabel: {
    color: theme.palette.text.secondary,
    fontSize: 12,
    paddingTop: 6,
  },
  systemOwnerEmailEditButton: {
    display: "none",
    // margin: "-4px",
    padding: 0,
    fontSize: 10,
    color: "#3b30c8"
  },
  systemOwnerEmailContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
  },
  systemOwnerEmailText: {
    fontSize: 12,
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
    width: "80%",
  },
  systemOwnerExtraEmailCount: {
    fontSize: 14,
    cursor: "pointer",
  },

  editSystemDetailsDialogContent: {
    padding: "0px 10px 0px 10px",
    position: "relative",
  },
  editSystemDetailsDialogTitle: {
    padding: 8,
    borderBottom: "1px solid #d9d9d9",
    boxShadow: "0px 2px 10px rgba(192, 192, 192, 0.25)",
  },
  editSystemDetailsDialogHeader: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
  },
  editSystemDetailsDialogActions: {
    height: "3rem",
    borderTop: "1px solid #d9d9d9",
  },
  editSystemDetailsSystemCheckbox: {
    "&::span": {
      fontSize: 6,
    },
  },

  systemsReportDialogTitle: {
    height: "3rem",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    borderBottom: "1px solid #d9d9d9",
    margin: 0,
    padding: 0,
    paddingLeft: 10,
    backgroundColor: "#FAFCFF",
    boxShadow: "0px 2px 10px rgba(192, 192, 192, 0.25)",
    "& h2": {
      fontSize: 18,
      fontWeight: 500,
    },
  },
  systemsReportDialogContent: {
    padding: "16px 32px",
  },
  systemsReportDialogContentLabel: {
    fontSize: 14,
  },
  systemsReportDialogContentValue: {
    fontSize: 16,
    textAlign: "end",
  },
  systemsReportDialogActions: {
    height: "3rem",
    borderTop: "1px solid #d9d9d9",
    boxShadow: "0px -4px 9px #D8D8D8",
    paddingRight: 20,
  },

  systemsContainer: {
    // width: `calc(100vw - 113px)`,
    // height: `calc(100vh - ${appHeaderHeight})`,
    position: "relative",
  },
  systemsHeaderContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
    padding: "0 16px 4px 16px",
    height: applicationPageHeaderHeight,
  },
  systemsHeader: {
    fontSize: 16,
    fontWeight: "bold",
  },
  systemsList: {
    marginTop: 0,
    paddingLeft: 8,
    height: `calc(100vh - ${appHeaderHeight} - ${applicationPageHeaderHeight} - ${applicationFooterButton})`,
    width: "100%",
  },
  bottomContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end",
    boxShadow: "0px -4px 9px #D8D8D8",
    backgroundColor: theme.palette.background.default,
    padding: "6px 10px",
    zIndex: 99,
    marginTop: 14,
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: applicationFooterButton,
  },
}));

export const HtmlTooltip = withStyles((theme) => ({
  tooltip: {
    backgroundColor: "#F1F5FE",
    color: theme.palette.primary.main,
    maxWidth: 285,
    fontSize: theme.typography.pxToRem(12),
    border: `1px solid ${theme.palette.primary.main}`,
    padding: 16,
  },
  arrow: {
    backgroundColor: "#F1F5FE",
  },
}))(Tooltip);


export const System = ({
  system,
  onDelete,
  onClick,
  selected,
  load,
  onUpdate,
  connection,
}) => {
  console.log(system);
  // console.log(system?.name, connection);
  const classes = useStyle();
  let iwaAccessData= useSelector(state => state.userManagement.entitiesAndActivities?.['User Management'])

  return (
    <Grid container>
      <Grid item md={4}>
        <Paper className={classes.systemContainer}>
          <div className={classes.systemInfoContainer}>
            {/* <DragHandle /> */}

            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              {system?.imageType && system?.applicationImage && (
                <img
                  className={classes.systemImage}
                  alt={system?.systemName}
                  src={
                    system?.imageType && system?.applicationImage
                      ? `data:${system?.imageType};base64,${system?.applicationImage}`
                      : ``
                  }
                />
              )}

              <div className={classes.systemInfoContent}>
                <Typography
                  className={classes.systemName}
                  onClick={onClick}
                >{`${system?.name}`}</Typography>

                {/* {system?.uniqueIds?.length > 0 ? (
                  <Tooltip
                    title={system?.uniqueIds?.join(", ") || "-"}
                    placement="bottom-start"
                  >
                    <Typography className={classes.systemUniqueId}>
                      {system?.uniqueIds?.join(", ") || "-"}
                    </Typography>
                  </Tooltip>
                ) : (
                  <Typography className={classes.systemUniqueId}>
                    {system?.uniqueIds?.join(", ") || "-"}
                  </Typography>
                )} */}
              </div>
            </div>
          </div>

          {/* <FaLightbulb
          className={`${classes.systemBulbIcon} ${
            connection === true
              ? classes.systemBulbIconOnline
              : connection === false
              ? classes.systemBulbIconOffline
              : ""
          }`}
        />
      </div> */}

          {/* <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Typography className={classes.systemOwnerEmailLabel}>
              Owner(s) Email Id
            </Typography>

            <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <IconButton
            size="small"
            color="primary"
            onClick={onUpdate}
            disabled={load}
            className={classes.systemOwnerEmailEditButton}
          >
            <MdEdit style={{ fontSize: 18 }} />
          </IconButton>

          {system?.applicationType?.toLowerCase() === "cw" && (
            <IconButton
              size="small"
              color="secondary"
              onClick={onDelete}
              disabled={load}
              className={classes.systemOwnerEmailEditButton}
            >
              <DeleteOutlinedIcon color="danger"/>
            </IconButton>
          )}
        </div>
          </div> */}

          <div className={classes.systemOwnerEmailContainer}>
            {/* <Typography className={classes.systemOwnerEmailText}>
              {system?.applicationOwnerEmails?.[0] || "-"}
            </Typography> */}

            {system?.applicationOwnerEmails?.length - 1 > 0 && (
              <HtmlTooltip
                title={system?.applicationOwnerEmails
                  ?.slice(1)
                  ?.map((email, index) => (
                    <Typography
                      key={index}
                      color="primary"
                      style={{ fontSize: 12 }}
                    >
                      {email}
                    </Typography>
                  ))}
              >
                <Typography className={classes.systemOwnerExtraEmailCount}>
                  +{system?.applicationOwnerEmails?.length - 1}
                </Typography>
              </HtmlTooltip>
            )}
          </div>

          <div
            style={{ display: "flex", alignItems: "center", padding: "0.4rem" }}
          >
            <Typography style={{ fontSize: 12 }}>Total Users: </Typography>

            <Typography style={{ fontSize: 12 }}>
              {system?.userCount || 0}
              {/* {Math.floor(Math.random() * (20 - 10) + 10)} */}
            </Typography>
          </div>


          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-end",
              marginTop: "1%",
            }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Tooltip title="Edit System">
              <IconButton
                size="small"
                onClick={onUpdate}
                disabled={load}
                // className={classes.systemOwnerEmailEditButton}
              >
                <MdEdit color="#3b30c8" style={{ fontSize: 18 }} />
              </IconButton>
              </Tooltip>

              {/* {system?.applicationType?.toLowerCase() === "cw" && (
                <IconButton
                  size="small"
                  onClick={onDelete}
                  disabled={load}
                  className={classes.systemOwnerEmailEditButton}
                >
                  <DeleteOutlinedIcon color="danger"/>
                </IconButton>
              )} */}
            </div>

            {/* {connection?.[system?.name] === true ? (
              <HiStatusOnline
                className={`${classes.systemBulbIcon} ${classes.systemBulbIconOnline}`}
              />
            ) : (
              <HiStatusOffline
                className={`${classes.systemBulbIcon} ${
                  connection?.[system?.name] === false &&
                  classes.systemBulbIconOffline
                }`}
              />
            )} */}
          </div>
        </Paper>
      </Grid>
    </Grid>
  );
};

const SystemsReport = ({ open, onClose, connections }) => {
  const classes = useStyle();

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose}>
      <DialogTitle className={classes.systemsReportDialogTitle}>
        System Check Report
      </DialogTitle>

      <DialogContent className={classes.systemsReportDialogContent}>
        <Grid container>
          <Grid item xs={6}>
            <Typography className={classes.systemsReportDialogContentLabel}>
              Technical User
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography className={classes.systemsReportDialogContentValue}>
              IWA Admin
            </Typography>
          </Grid>
        </Grid>

        <Grid container>
          <Grid item xs={6}>
            <Typography className={classes.systemsReportDialogContentLabel}>
              Total Systems Checked
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography className={classes.systemsReportDialogContentValue}>
              {Object?.keys(connections)?.length || 0}
            </Typography>
          </Grid>
        </Grid>

        <Grid container>
          <Grid item xs={6}>
            <Typography className={classes.systemsReportDialogContentLabel}>
              Systems Online
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography className={classes.systemsReportDialogContentValue}>
              {Object?.values(connections)?.filter(
                (connection) => connection === true
              )?.length || 0}
            </Typography>
          </Grid>
        </Grid>

        <Grid container>
          <Grid item xs={6}>
            <Typography className={classes.systemsReportDialogContentLabel}>
              Systems Offline
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography className={classes.systemsReportDialogContentValue}>
              {Object?.values(connections)?.filter(
                (connection) => connection === false
              )?.length || 0}
            </Typography>
          </Grid>
        </Grid>

        <Grid container>
          <Grid item xs={6}>
            <Typography className={classes.systemsReportDialogContentLabel}>
              Overall System Health
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <Typography className={classes.systemsReportDialogContentValue}>
              {`${(
                (Object?.values(connections)?.filter(
                  (connection) => connection === true
                )?.length /
                  Object.keys(connections)?.length) *
                100
              )?.toFixed(2)}%`}
            </Typography>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions className={classes.systemsReportDialogActions}>
        <Button
          size="small"
          variant="outlined"
          onClick={onClose}
        >
          Ok
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const Systems = () => {
  const classes = useStyle();
  const basicReducerState = useSelector((state) => state.userManagement);
  const dispatch = useDispatch();
  const [params, setParams] = useState({});

  const [load, setLoad] = useState(false);
  const [applications, setapplications] = useState([]);
  const [anchorElMenu, setAnchorElMenu] = useState(null);

  const [systemReportDialog, setSystemReportDialog] = useState(false);

  const [updatingApplication, setUpdatingApplication] = useState(null);

  const [isUpdateItem, setIsUpdateItem] = useState(false);
  const [selectedApplication, setSelectedApplication] = useState(null);

  const [applicationFile, setApplicationFile] = useState(null);
  const [addNewApplicationDialog, setAddNewApplicationDialog] = useState(false);
  const [openApplicationFileDialog, setOpenApplicationFileDialog] =
    useState(false);
  const [deletingApplication, setDeletingApplication] = useState(null);

  const initialConnectionState = basicReducerState?.applications
    ?.filter(
      (application) => application?.applicationType?.toLowerCase() !== "cw"
    )
    ?.map((application) => undefined);
  const [connections, setConnections] = useState(initialConnectionState);

  const getApplicationNameById = (appId) => {
    const applicationName = basicReducerState?.applications?.find(
      (application) => Number(application?.id) === Number(appId)
    );
    return applicationName?.name || null;
  };
  const getApplications = () => {
    getAllApplications(
      () => {
        setLoad(true);
      },
      (data) => {
        dispatch(setApplications(data?.data || []));
        setLoad(false);
      },
      (err) => {
        setLoad(false);
      }
    );
  };
  const handleDeleteApplication = (applicationId) => {
    setLoad(true);
    const disableApplicationUrl = `/${destination_IWA}/api/v1/applications/deactivate`;
    const applicationIdPayload = {
      id: applicationId
    };
    const disableApplicationRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(applicationIdPayload)
    };
    fetch(disableApplicationUrl, disableApplicationRequestParam)
      .then((res) => res.json())
      .then((data) => {
        setLoad(false);
        dispatch(
          setApplications(
            basicReducerState?.applications?.filter(
              (application) => application?.id !== Number(applicationId)
            ) || []
          )
        );
        setDeletingApplication(null);

        dispatch(
          setResponseMessage({
            open: true,
            status: data?.status ? "success" : "error",
            message: data?.status
              ? toastMessage?.APPLICATION_DELETED
              : toastMessage?.SOMETHING_WENT_WRONG,
          })
        );
      })
      .catch((err) => {
        setLoad(false);
      });
  };
  const uploadApplicationsFile = () => {
    if (!applicationFile) {
      console.log("no file found");
      return;
    }
    setLoad(true);
    const url = `/${destination_IWA}/api/v1/applications/addApplicationsUsingCsv`;
    let formData = new FormData();
    formData.append("file", applicationFile);
    formData.append("name", applicationFile.name);
    const requestParam = {
      method: "POST",
      headers: {},
      body: formData,
    };
    fetch(url, requestParam)
      .then((res) => {
        console.log(res);
      })
      .then((data) => {
        setLoad(false);
        setOpenApplicationFileDialog(false);
        setApplicationFile(null);
        getApplications();

        dispatch(
          setResponseMessage({
            open: true,
            status: "success",
            message: "Application file uploaded successfully",
          })
        );
      })
      .catch((err) => {
        setLoad(false);
        dispatch(
          setResponseMessage({
            open: true,
            status: "error",
            message: "Something went wrong",
          })
        );
      });
  };
  const testConnection = () => {
    setLoad(true);
    const applicationsName = basicReducerState?.applications
      ?.filter(
        (application) => application?.applicationType?.toLowerCase() !== "cw"
      )
      ?.map((application) => application?.name);
    const testConUrl = `/${destination_IWA}/api/v1/AccessAndRevoke/testConnection`;
    const testConRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify([...applicationsName]),
    };
    fetch(testConUrl, testConRequestParam)
      .then((res) => res.json())
      .then((data) => {
        setLoad(false);
        setConnections(data?.data);
        setSystemReportDialog(true);
      })
      .catch((err) => {
        setLoad(false);
      });
  };
  const onSortEnd = ({ oldIndex, newIndex }) => {
    var newapplications = [...applications];
    var x = applications[oldIndex];
    newapplications.splice(oldIndex, 1);
    newapplications.splice(newIndex, 0, x);
    const dragAndDropUrl = `/${destination_IWA}/api/v1/applications/systemOrder`;
    const dragAndDropPayload = newapplications?.map((app) => Number(app?.id));
    const dragAndDropRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(dragAndDropPayload),
    };
    fetch(dragAndDropUrl, dragAndDropRequestParam)
      .then((res) => res.json())
      .then((data) => {
        if (data?.status) {
          setapplications([...newapplications]);
          dispatch(setApplications([...newapplications]));
        }

        dispatch(
          setResponseMessage({
            open: true,
            status: data?.status ? "success" : "error",
            message: data?.status
              ? "System Order changed successfully"
              : "Something went wrong",
          })
        );
      })
      .catch((err) => {
        dispatch(
          setResponseMessage({
            open: true,
            status: "error",
            message: "Something went wrong",
          })
        );
      });
  };
  const isCWSystem = (appId) => {
    return (
      basicReducerState?.applications
        ?.find((app) => app?.id === appId)
        ?.applicationType?.toLowerCase() === "cw"
    );
  };

  useEffect(() => {
    setapplications(basicReducerState?.applications);
  }, [basicReducerState?.applications]);

  useEffect(() => {
    getApplications();
  }, []);

  return (
    <div className={`${classes.systemsContainer}`}>
      <Loading load={load} />

      <NewApplication
        open={addNewApplicationDialog}
        onClose={() => {
          setAddNewApplicationDialog(false);
          setIsUpdateItem(false);
        }}
        title={`${isUpdateItem ? "Update" : "New"} Application`}
        update={isUpdateItem}
        updatingApplication={updatingApplication}
      />

      <UploadFile
        open={openApplicationFileDialog}
        onClose={() => {
          setOpenApplicationFileDialog(false);
          setApplicationFile(null);
        }}
        onUpload={() => {
          uploadApplicationsFile();
        }}
        file={applicationFile}
        setFile={setApplicationFile}
        disableCondition={!applicationFile}
        load={load}
      />

      <DeletionMessageBox
        open={deletingApplication ? true : false}
        onClose={() => setDeletingApplication(null)}
        onDelete={() => {
          handleDeleteApplication(deletingApplication);
        }}
        load={load}
      />

      <SystemsReport
        open={systemReportDialog}
        onClose={() => {
          setSystemReportDialog(false);
        }}
        connections={connections}
      />

      {selectedApplication ? (
        isCWSystem(selectedApplication) ? (
          <ApplicationDetail
            selectedApplication={selectedApplication}
            setSelectedApplication={setSelectedApplication}
          />
        ) : (
          <SystemDetails
            selectedApplication={selectedApplication}
            setSelectedApplication={setSelectedApplication}
          />
        )
      ) : (
        <>
          <div>
            {/* <Typography className={classes.systemsHeader}>Systems</Typography> */}

            <Grid container
              sx={{
                display: "flex",
                alignItem: "center",
                justifyContent: "flex-start",
                padding: "0 1rem",
              }}
            >
              <Grid item md={10}>
                {applications?.map((application, index) => (
                  // <Grid key={application?.id} item md={8}>
                    <System
                      key={`${application?.id}-${index}`}
                      system={application}
                      onDelete={(e) => {
                        e.stopPropagation();
                        setDeletingApplication(application?.id);
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        if (application?.id) {
                          setSelectedApplication(application?.id);
                        }
                      }}
                      selected={
                        params?.applicationId &&
                        application?.id === Number(params?.applicationId)
                      }
                      load={load}
                      onUpdate={(e) => {
                        e.stopPropagation();
                        setIsUpdateItem(true);
                        setAddNewApplicationDialog(true);
                        setUpdatingApplication({
                          ...application,
                          applications:
                            application?.applications
                              ?.map((mappedAppId) =>
                                getApplicationNameById(
                                  Number(mappedAppId),
                                  basicReducerState?.applications
                                )
                              )
                              ?.filter((applicationName) => applicationName) ||
                            [],
                        });
                      }}
                      connection={connections}
                    />
                  // </Grid>
                ))}
              </Grid>
              {/* <Grid item md={2} sx={{display:"flex", justifyContent: "flex-end"}}>
                <IconButton
                  size="small"
                  onClick={() => {
                    getApplications();
                  }}
                >
                  <Refresh style={{ fontSize: 20 }} />
                </IconButton>
              </Grid> */}

              {/* <IconButton
                size="small"
                aria-controls="menu"
                onClick={(e) => {
                  setAnchorElMenu(e.currentTarget);
                }}
              >
                <MoreVert style={{ fontSize: 20 }} />
              </IconButton> */}

              {/* <Button
                size="small"
                variant="text"
                color="primary"
                style={{ textTransform: "capitalize" }}
                onClick={() => {
                  setAddNewApplicationDialog(true);
                }}
                startIcon={<Add />}
                disabled={load}
              >
                Add New System
              </Button> */}
            </Grid>

            <Menu
              id="menu"
              anchorEl={anchorElMenu}
              anchorOrigin={{
                vertical: "bottom",
                horizontal: "right",
              }}
              transformOrigin={{
                vertical: "top",
                horizontal: "right",
              }}
              style={{ width: 230 }}
              open={Boolean(anchorElMenu)}
              onClose={() => {
                setAnchorElMenu(null);
              }}
            >
              <MenuItem
                aria-controls="subMenu"
                onClick={(e) => {
                  setAnchorElMenu(null);
                }}
                style={{
                  display: "flex",
                  alignItem: "center",
                  justifyContent: "space-between",
                }}
              >
                <CSVLink data={[applicationFileHeading]}>
                  <Typography style={{ fontSize: 14, marginRight: 20 }}>
                    Download template
                  </Typography>
                </CSVLink>
              </MenuItem>

              <MenuItem
                aria-controls="subMenu"
                onClick={(e) => {
                  setOpenApplicationFileDialog(true);
                  setAnchorElMenu(null);
                }}
                style={{
                  display: "flex",
                  alignItem: "center",
                  justifyContent: "space-between",
                }}
              >
                <Typography style={{ fontSize: 14, marginRight: 20 }}>
                  Upload template
                </Typography>
              </MenuItem>
            </Menu>
          </div>

          {/* <SortableList
            applications={applications}
            onSortEnd={onSortEnd}
            useDragHandle
            axis="xy"
          /> */}
          <Grid container spacing={2} sx={{ padding: "1rem" }}></Grid>
        </>
      )}

      {/* {!selectedApplication && (
        <div className={classes.bottomContainer}>
          <Button
            variant="outlined"
            color="primary"
            style={{
              textTransform: "capitalize",
              fontSize: 16,
              margin: 0,
              padding: 4,
            }}
            onClick={() => {
              testConnection();
            }}
          >
            Test Connection
          </Button>
        </div>
      )} */}
    </div>
  );
};

export default Systems;
