import React, { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { handleLogoutWarningScreen } from '../app/applicationConfigReducer'
import { Button, Dialog, DialogContent, Grid, IconButton, Typography } from '@mui/material'
import { Stack } from '@mui/system'
import CloseIcon from "@mui/icons-material/Close";
import userLogout from "../../src/utilityImages/userLogout.jpg"
import { destination_Admin } from '../destinationVariables'
import { doAjax } from '../components/common/fetchService'
import { END_POINTS } from '@constant/apiEndPoints'

const LogoutWarningScreen = ({ timeRemaining = 60000, onExtendSession }) => {
    const dispatch = useDispatch()
    const [count, setCount] = useState(Math.ceil(timeRemaining / 1000))
    const [intervalId, setIntervalId] = useState(null)
    
    const closeScreen = () => {
        if (intervalId) {
            clearInterval(intervalId)
        }
        dispatch(handleLogoutWarningScreen(false))
    }
    
    const refreshScreen = () => {
        getApirefresh()
        if (intervalId) {
            clearInterval(intervalId)
        }
        if (onExtendSession) {
            onExtendSession()
        }
        dispatch(handleLogoutWarningScreen(false))
    }

    const getApirefresh = () => {
        const hSuccess = () => {
        }
        const hError = () => {
        }
        doAjax(`/${destination_Admin}${END_POINTS?.DUMMY_API}`, 'get', hSuccess, hError)
    }
  
    useEffect(() => {
        setCount(Math.ceil(timeRemaining / 1000))
    }, [timeRemaining])

    useEffect(() => {
        const newIntervalId = setInterval(() => {
            setCount((prev) => {
                const newCount = prev - 1
                if (newCount <= 0) {
                    clearInterval(newIntervalId)
                    // The parent component will handle the actual logout
                    return 0
                }
                return newCount
            })
        }, 1000)
        
        setIntervalId(newIntervalId)
        return () => {
            if (newIntervalId) {
                clearInterval(newIntervalId)
            }
        }
    }, [])

    useEffect(() => {
        if (count === 0) {
            if (intervalId) {
                clearInterval(intervalId)
            }
            dispatch(handleLogoutWarningScreen(false))
        }
    }, [count, intervalId, dispatch])
    
    return (
        <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
                sx: { boxShadow: "none" },
            }}
            open={true}
            onClose={closeScreen}
        >
            <Grid container sx={{ position: 'relative' }}>
                <Grid item sx={{ position: "absolute", top: '1rem', right: '1rem' }}>
                    <IconButton
                        onClick={(e) => {
                            e.stopPropagation();
                            closeScreen();
                        }}
                    >
                        <CloseIcon />
                    </IconButton>
                </Grid>
            </Grid>

            <DialogContent>
                <Stack>
                    <Grid container sx={{ display: 'flex', justifyContent: 'center' }}>
                        <Grid item sx={{ opacity: ".5" }}>
                            <img 
                                src={userLogout} 
                                alt="User logout warning"
                                style={{ width: '16rem' }}
                            />
                        </Grid>
                        <Grid
                            item
                            md={12}
                            sx={{
                                textAlign: "center",
                            }}
                        >
                            <Typography>
                                Due to inactivity you will be logged out in <b>{count}</b> seconds.
                            </Typography>
                        </Grid>
                        <Grid item sx={{ display: 'flex', justifyContent: 'end', width: '100%' }}>
                            <Button variant='contained' onClick={refreshScreen}>
                                Stay Logged In
                            </Button>
                        </Grid>
                    </Grid>
                </Stack>
            </DialogContent>
        </Dialog>
    )
}

export default LogoutWarningScreen