import { getCurrentDate, getPreviousValueGlobal } from "@helper/helper";
import { useDispatch, useSelector } from "react-redux";
import { setCreateChangeLogDataBK } from "@app/changeLogReducer";

export const useChangeLogCreation = () => {
    const dispatch = useDispatch();
    const createPayloadCopyForChangeLog = useSelector((state) => state.changeLog.createPayloadCopyForChangeLog || {});
    const changeLogBK = useSelector((state) => state.changeLog.createChangeLogDataBK || {});
    const userData = useSelector((state) => state.userManagement.userData);

    const updateChangeLogGlobal = ({
        uniqueId,
        viewName,
        fieldName,
        jsonName,
        currentValue,
    }) => {
        const CURRENT_DATE = getCurrentDate().sapFormat;
        const previousValue = getPreviousValueGlobal(uniqueId, jsonName, createPayloadCopyForChangeLog);

        const ObjectNo = createPayloadCopyForChangeLog?.[uniqueId]?.BankNo;

        const newChangeEntry = {
            ObjectNo,
            ChangedBy: userData?.emailId,
            ChangedOn: CURRENT_DATE,
            FieldName: fieldName,
            PreviousValue: previousValue,
            CurrentValue: currentValue,
            SAPValue: previousValue,
            ViewName: viewName,
        };

        const existingLogs = changeLogBK?.[uniqueId]?.changeLog || [];
        const existingIndex = existingLogs.findIndex(
            (entry) => entry.ObjectNo === ObjectNo && entry.JsonName === jsonName
        );

        let updatedLogs;

        if (existingIndex !== -1) {
            updatedLogs = [...existingLogs];
            updatedLogs[existingIndex] = {
                ...updatedLogs[existingIndex],
                CurrentValue: currentValue,
                ChangedOn: CURRENT_DATE,
            };
        } else {
            updatedLogs = [...existingLogs, newChangeEntry];
        }

        dispatch(setCreateChangeLogDataBK({
            uniqueId,
            changeLog: updatedLogs
        }));
    };

    return { updateChangeLogGlobal };
};
