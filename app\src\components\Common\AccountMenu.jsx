import * as React from "react";
import Box from "@mui/material/Box";
import Avatar from "@mui/material/Avatar";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import Tooltip from "@mui/material/Tooltip";
import { Button, Popover, Stack, Chip, Grid, Badge } from "@mui/material";
// import { useDispatch, useSelector } from "react-redux";
import PersonIcon from "@mui/icons-material/Person";
import BadgeIcon from "@mui/icons-material/Badge";
import ManageAccountsIcon from "@mui/icons-material/ManageAccounts";
import BusinessIcon from "@mui/icons-material/Business";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { colors } from "@constant/colors";
import TruncatedText from "./ui/TruncatedText";
import useLang from "@hooks/useLang";

export default function AccountMenu() {
  let userData = useSelector((state) => state.userManagement.userData);
  let roles = useSelector((state) => state.userManagement.roles);
  const rolesString = roles.join(", ");
  const { t } = useLang();

  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const navigate = useNavigate();
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const onLogout = () => {
    window.location.href = "/do/logout";
  };
  return (
    <React.Fragment>
      {userData && (
        <>
          <Box sx={{ display: "flex", alignItems: "center", textAlign: "center" }}>
            <Tooltip title={t("User Details")}>
              <IconButton onClick={handleClick} size="small" sx={{ ml: 2 }} aria-controls={open ? "account-menu" : undefined} aria-haspopup="true" aria-expanded={open ? "true" : undefined}>
                <Avatar
                  sx={{
                    width: 32,
                    height: 32,
                    fontSize: "0.8rem",
                    color: "#3730c7",
                    backgroundColor: "#eae9ff",
                  }}
                >
                  {`${userData.firstName?.charAt(0)}${userData.lastName?.charAt(0)}`}
                </Avatar>
              </IconButton>
            </Tooltip>
          </Box>

          <Popover
            open={open}
            anchorEl={anchorEl}
            onClose={handleClose}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "left",
            }}
          >
            <Stack
              sx={{ width: "340px" }}
              direction="column"
              alignItems="center"
              justifyContent="center"
              spacing={1}
            >
              <Grid container>
                <Grid item md={12} backgroundColor={colors.icon.pink} padding="1rem" sx={{ position: "relative" }}>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "center",
                      paddingBottom: "1rem",
                    }}
                  >
                    <Avatar
                      sx={{
                        width: 50,
                        height: 50,
                        fontSize: "1rem",
                        fontWeight: "bold",
                        color: "#0070E8",
                        backgroundColor: "#E4F1FF",
                      }}
                    >
                      {`${userData.firstName?.charAt(0)}${userData.lastName?.charAt(0)}`}
                    </Avatar>
                  </div>
                  <div style={{ display: "flex", justifyContent: "center" }}>
                    <Typography sx={{ fontWeight: "bold" }}>{userData?.displayName}</Typography>
                  </div>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      flexDirection: "column",
                    }}
                  >
                    <Typography variant="subtitle2" sx={{ color: "#757575" }}>
                      {userData?.emailId}
                    </Typography>
                  </div>
                </Grid>
                <Grid item md={12} padding="1rem">
                  <Grid container spacing={1}>
                    <Grid item md={12}>
                      <Stack
                        direction="row"
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "flex-start",
                        }}
                        spacing={1}
                      >
                        <PersonIcon sx={{ color: colors.icon.orange, fontSize: "16px" }} />
                        <Typography variant="body2" sx={{ fontWeight: "bold", color: colors.black.dark }}>
                          {t("Username")}:
                        </Typography>
                        <Typography variant="body2" sx={{ color: colors.primary.grey }}>
                          {userData?.userName}
                        </Typography>
                      </Stack>
                    </Grid>

                    <Grid item md={12}>
                      <Stack
                        direction="row"
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "flex-start",
                        }}
                        spacing={1}
                      >
                        <BadgeIcon sx={{ color: colors.icon.orange, fontSize: "16px" }} />
                        <Typography variant="body2" sx={{ fontWeight: "bold", color: colors.black.dark }}>
                          {t("User ID")}:
                        </Typography>
                        <Typography variant="body2" sx={{ color: colors.primary.grey }}>
                          {userData?.user_id}
                        </Typography>
                      </Stack>
                    </Grid>

                    <Grid item md={12}>
                      <Stack
                        direction="row"
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "flex-start",
                        }}
                        spacing={1}
                      >
                        <ManageAccountsIcon sx={{ color: colors.icon.orange, fontSize: "16px" }} />
                        <Typography variant="body2" sx={{ fontWeight: "bold", color: colors.black.dark }}>
                          {t("Role")}:
                        </Typography>
                        <TruncatedText
                          text={rolesString}
                          maxChars={rolesString?.split(',')[0]?.trim().length || 0}
                          variant="body2"
                          color={colors.primary.grey}
                          showIcon={rolesString?.includes(',') || false}
                        />
                      </Stack>
                    </Grid>

                  </Grid>

                  <Stack>
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        paddingTop: "1rem",
                      }}
                    >
                      <Button
                        variant="contained"
                        onClick={onLogout}
                        style={{
                          minWidth: "6rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                        }}
                      >
                        {t("Logout")}
                      </Button>
                    </div>
                  </Stack>
                </Grid>
              </Grid>
            </Stack>
          </Popover>
          {/* {accountActivity_OpenStatus && <AccountActivity
            open={accountActivity_OpenStatus}
            setOpen={setaccountActivity_OpenStatus}
            data={accountActivity.data}
            anchorEl={anchorEl}
            handleClose={handleClose_AccountActivity}
          />} */}
        </>
      )}
    </React.Fragment>
  );
}
