import { Box, Stack, Switch, Typography, Icon<PERSON><PERSON>on, <PERSON><PERSON><PERSON>, Button } from "@mui/material";
import { useState, useCallback, useEffect } from "react";
import FilterBar from "./FilterBar";
import AllCharts from "./Graphs/AllCharts";
import ReportsDashboard from "./ReportsDashboard";
import DashboardKpis from "./DashboardKpis";
import SettingsIcon from "@mui/icons-material/Settings";
import ManageDashboardDialog from "./ManageDashboardDialog";
import { useDashboardCall } from "../../hooks/useDashboardCall";
import { DashboardSetting } from "@cw/rds/icons";
import useLang from '../../hooks/useLang';

const Dashboard = () => {
  const [showReports, setShowReports] = useState(false);
  const [openManageDialog, setOpenManageDialog] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  const { cards, loading, decisionTableConfig, userPreferences, reportConfig,kpiReportPrefs,refreshDashboard } = useDashboardCall(refreshKey);

  const { t } = useLang();

  const handleDashboardUpdate = useCallback(() => {
    setRefreshKey((prevKey) => prevKey + 1);
    setOpenManageDialog(false);
  }, []);
  return (
    <Box sx={{ height: "100vh", overflow: "hidden", display: "flex", flexDirection: "column" }}>
      <Box sx={{ position: "sticky", top: 0, bgcolor: "background.default", p: 2 }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h3">
              <strong>{t("Dashboard")}</strong>
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {t("This view displays various metrics related to Master Data")}
            </Typography>
          </Box>

          <Stack direction="row" alignItems="center" spacing={1}>
            <Button variant="outlined" startIcon={<DashboardSetting />} color="primary" onClick={() => setOpenManageDialog(true)} size="small" sx={{ mr:"20px !important"}}>
              {t("Manage Dashboard")}
            </Button>
            <Typography variant="body2">{t("KPI Metrics")}</Typography>
            <Switch checked={showReports} onChange={() => setShowReports((prev) => !prev)} color="primary" />
            <Typography variant="body2">{t("KPI Reports")}</Typography>
          </Stack>
        </Stack>

        {!showReports && (
          <Box mt={2}>
            <FilterBar />
            <DashboardKpis />
          </Box>
        )}
      </Box>

      <Box sx={{ flex: 1, overflowY: "auto", p: 2 }}>{showReports ? <ReportsDashboard key={`reports-${refreshKey}`} reportConfig={reportConfig} kpiReportPrefs={kpiReportPrefs} loading={loading} /> : <AllCharts key={`charts-${refreshKey}`} cards={cards} loading={loading} />}</Box>

      <ManageDashboardDialog open={openManageDialog} onClose={() => setOpenManageDialog(false)} onSave={handleDashboardUpdate} decisionTableConfig={decisionTableConfig} userPreferences={userPreferences} reportConfig={reportConfig} />
    </Box>
  );
};

export default Dashboard;
