import { Box, Grid, Tab, Tabs, Typography } from "@mui/material";
import { useState } from "react";
import {
  outermostContainer_Information,
} from "@components/Common/commonStyles";
import DescriptionTab from "./DescriptionTab";
import UnitsOfMeasureTab from "./UnitsOfMeasureTab";
import AdditionalEANSTab from "./AdditionalEANSTab";
import useLang from "@hooks/useLang";
const AdditionalData = (props) => {
  const { t } = useLang();
  const [activeTab, setActiveTab] = useState(0);
  const isDisplaySAPViewPresent = window.location.href.includes(
    "DisplayMaterialSAPView"
  );
  const additionalDataTabs = [
    t("Description"),
    t("Units of Measure"),
    ...(!isDisplaySAPViewPresent ? [t("Additional EANs")] : []),
  ];
  const tabContents = [
    [
      <>
        <DescriptionTab materialID = {props?.materialID} selectedMaterialNumber={props?.selectedMaterialNumber} disabled={props.disableCheck}/>
      </>,
    ],
    [
      <>
        <UnitsOfMeasureTab materialID = {props?.materialID} selectedMaterialNumber={props?.selectedMaterialNumber}  disabled={props.disableCheck}/>
      </>,
    ],
    ...(!isDisplaySAPViewPresent ? [[
      <>
        <AdditionalEANSTab materialID = {props?.materialID} selectedMaterialNumber={props?.selectedMaterialNumber}  disabled={props.disableCheck}/>
      </>,
    ]] : []),
  ];

  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  return (
    <div>
      <Grid
        container
        style={{
          ...outermostContainer_Information,
          backgroundColor: "#FAFCFF",
        }}
      >
        <Grid sx={{ width: "inherit" }}>
          <Grid container style={{ padding: "0 1rem 0 1rem" }}>
            <Grid container sx={outermostContainer_Information}>
              <Grid
                container
                display="flex"
                flexDirection="row"
                flexWrap="nowrap"
              >
                <Grid container>
                  <Tabs
                    value={activeTab}
                    onChange={handleChange}
                    variant="scrollable"
                    sx={{
                      background: "#FFF",
                      borderBottom: "1px solid #BDBDBD",
                      width: "100%",
                    }}
                    aria-label="mui tabs example"
                  >
                    {additionalDataTabs.map((factor, index) => (
                      <Tab
                        sx={{ fontSize: "12px", fontWeight: "700" }}
                        key={index}
                        label={factor}
                      />
                    ))}
                  </Tabs>
                  {tabContents[activeTab].map((cardContent, index) => (
                    <Box key={index} sx={{ mb: 2, width: "100%" }}>
                      <Typography variant="body2">{cardContent}</Typography>
                    </Box>
                  ))}
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </div>
  );
};

export default AdditionalData;
