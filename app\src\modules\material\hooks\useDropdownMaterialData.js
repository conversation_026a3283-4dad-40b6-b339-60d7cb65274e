import useFetchDropdownAndDispatch from "./useMaterialFetchDropdownAndDispatch";
import { destination_MaterialMgmt } from "../../../destinationVariables";

const useDropdownMaterialData = () => {
  const { fetchDataAndDispatch } = useFetchDropdownAndDispatch();
  const fetchAllDropdownMasterData = () => {
    const apiCalls = [
      // NOTE Not being used now but can be used later.
      // { url: `/${destination_MaterialMgmt}/data/getMatlGrp1`, keyName: "MatlGrp1" },
      // { url: `/${destination_MaterialMgmt}/data/getMatlGrp2`, keyName: "MatlGrp2" },
      // { url: `/${destination_MaterialMgmt}/data/getMatlGrp3`, keyName: "MatlGrp3" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxInd`, keyName: "TaxInd" },
      // { url: `/${destination_MaterialMgmt}/data/getDsnOffice`, keyName: "DsnOffice" },
      // { url: `/${destination_MaterialMgmt}/data/getProdAllocation`, keyName: "ProductAllocation" }, //
      // { url: `/${destination_MaterialMgmt}/data/getMedium`, keyName: "Medium" },
      { url: `/${destination_MaterialMgmt}/data/getEanCat`, keyName: "CategoryOfInternationalArticleNumberEAN" }, 
      // { url: `/${destination_MaterialMgmt}/data/getSegStructure`, keyName: "SegmentationStructure" }, //
      // { url: `/${destination_MaterialMgmt}/data/getSegStrategy`, keyName: "SegmentationStrategy" }, //
      // { url: `/${destination_MaterialMgmt}/data/getANPCode`, keyName: "ANPCode" },
      // { url: `/${destination_MaterialMgmt}/data/getMRPGroup`, keyName: "MRPGroup" }, //
      // { url: `/${destination_MaterialMgmt}/data/getUnitGroup`, keyName: "UnitGroup" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxCategory`, keyName: "TaxCategory" }, //
      // { url: `/${destination_MaterialMgmt}/data/getAbcId`, keyName: "AbcId" },
      // { url: `/${destination_MaterialMgmt}/data/getStandHUType`, keyName: "StandardHandlingUnitType" }, //
      // { url: `/${destination_MaterialMgmt}/data/getQualInspGrp`, keyName: "QualityInspectionGroup" }, //
      // { url: `/${destination_MaterialMgmt}/data/getBaseUom`, keyName: "QuarantinePeriod" }, //
      // { url: `/${destination_MaterialMgmt}/data/getDelyUnit`, keyName: "DelyUnit" },
      // { url: `/${destination_MaterialMgmt}/data/getGenItemCatGroup`, keyName: "ItemCat" },
      // { url: `/${destination_MaterialMgmt}/data/getProductShape`, keyName: "ProductShape" }, //
      // { url: `/${destination_MaterialMgmt}/data/getLogHandlingInd`, keyName: "HandlingIndicator" }, //
      // { url: `/${destination_MaterialMgmt}/data/getWHMaterialGroup`, keyName: "WarehouseProductGroup" }, //
      // { url: `/${destination_MaterialMgmt}/data/getWhseStorCondition`, keyName: "WarehouseStorageCondition" },
      // { url: `/${destination_MaterialMgmt}/data/getCWProfile`, keyName: "CWProfileForCWQty" }, //
      // { url: `/${destination_MaterialMgmt}/data/getCatchWtTolGrp`, keyName: "CatchWTToleranceGroup" }, //
      // { url: `/${destination_MaterialMgmt}/data/getReferenceProduct`, keyName: "RefProductForPackagingBuilding" }, //
      // { url: `/${destination_MaterialMgmt}/data/getMatGrpPack`, keyName: "MaterialGroupPackagingMaterials" }, //
      // { url: `/${destination_MaterialMgmt}/data/getProductOrientationProfile`, keyName: "ProductOrientationProfile" }, //
      // { url: `/${destination_MaterialMgmt}/data/getClassBasedOnClassType`, keyName: "ClassType" }, //
      // { url: `/${destination_MaterialMgmt}/data/getSerializationLevel`, keyName: "SerializationLevel" },
      // { url: `/${destination_MaterialMgmt}/data/getPlRefMat`, keyName: "PlRefMat" },
      // { url: `/${destination_MaterialMgmt}/data/getCommGroup`, keyName: "CommGroup" },
      // { url: `/${destination_MaterialMgmt}/data/getPrRefMat`, keyName: "PrRefMat" },
      // { url: `/${destination_MaterialMgmt}/data/getRoundProf`, keyName: "RoundProf" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxType1`, keyName: "TaxType1" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxType2`, keyName: "TaxType2" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxType3`, keyName: "TaxType3" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxType4`, keyName: "TaxType4" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxType5`, keyName: "TaxType5" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxType6`, keyName: "TaxType6" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxType7`, keyName: "TaxType7" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxType8`, keyName: "TaxType8" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxType9`, keyName: "TaxType9" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxclass3`, keyName: "Taxclass3" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxclass4`, keyName: "Taxclass4" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxclass5`, keyName: "Taxclass5" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxclass6`, keyName: "Taxclass6" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxclass7`, keyName: "Taxclass7" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxclass8`, keyName: "Taxclass8" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxclass9`, keyName: "Taxclass9" },
      // { url: `/${destination_MaterialMgmt}/data/getTaxInd`, keyName: "TaxInd" },
     { url: `/${destination_MaterialMgmt}/data/getOrderUnit`, keyName: "PoUnit" },
      { url: `/${destination_MaterialMgmt}/data/getMatlGrp4`, keyName: "MatlGrp4" },
      { url: `/${destination_MaterialMgmt}/data/getMatlGrp5`, keyName: "MatlGrp5" },
      { url: `/${destination_MaterialMgmt}/data/getMatfrgtgrp`, keyName: "Matfrgtgrp" },
      { url: `/${destination_MaterialMgmt}/data/getBaseUom`, keyName: "BaseUom" },
      { url: `/${destination_MaterialMgmt}/data/getBaseUom`, keyName: "CompUom" },
      { url: `/${destination_MaterialMgmt}/data/getBaseUom`, keyName: "CompUom" },
      { url: `/${destination_MaterialMgmt}/data/getBaseUom`, keyName: "DelyUom" },
      { url: `/${destination_MaterialMgmt}/data/getBaseUom`, keyName: "AltUnit" },
      { url: `/${destination_MaterialMgmt}/data/getBaseUom`, keyName: "UnitDim" },
      { url: `/${destination_MaterialMgmt}/data/getBaseUom`, keyName: "CommCoUn" },
      { url: `/${destination_MaterialMgmt}/data/getBaseUom`, keyName: "LeqUnit1" },
      { url: `/${destination_MaterialMgmt}/data/getUnitType`, keyName: "Unittype1" },
      { url: `/${destination_MaterialMgmt}/data/getSproctype`, keyName: "Sproctype" },
      { url: `/${destination_MaterialMgmt}/data/getVarOrdUnit`, keyName: "VarOrdUn" },
      { url: `/${destination_MaterialMgmt}/data/getMatlGroup`, keyName: "MatlGroup" },
      { url: `/${destination_MaterialMgmt}/data/getMatlGroup`, keyName: "MatlGrp" },
      { url: `/${destination_MaterialMgmt}/data/getExtMatlGrp`, keyName: "Extmatlgrp" },
      { url: `/${destination_MaterialMgmt}/data/getDivision`, keyName: "Division" },
      { url: `/${destination_MaterialMgmt}/data/getProdHier`, keyName: "ProdHier" },
      { url: `/${destination_MaterialMgmt}/data/getUnitOfWt`, keyName: "UnitOfWt" },
      { url: `/${destination_MaterialMgmt}/data/getVolumeUnit`, keyName: "Volumeunit" },
      { url: `/${destination_MaterialMgmt}/data/getHazMatProf`, keyName: "Hazmatprof" },
      { url: `/${destination_MaterialMgmt}/data/getItemCat`, keyName: "ItemCat" },
      { url: `/${destination_MaterialMgmt}/data/getItemCat`, keyName: "GItemCat" },
      { url: `/${destination_MaterialMgmt}/data/getSalesUnit`, keyName: "SalesUnit" },
      { url: `/${destination_MaterialMgmt}/data/getPurValkey`, keyName: "PurValkey" },
      { url: `/${destination_MaterialMgmt}/data/getOrderUnit`, keyName: "OrderUnit" },
      { url: `/${destination_MaterialMgmt}/data/getBasicMatl`, keyName: "BasicMatl" },
      { url: `/${destination_MaterialMgmt}/data/getTransGrp`, keyName: "TransGrp" },
      { url: `/${destination_MaterialMgmt}/data/getXDistChainStatus`, keyName: "SalStatus" },
      { url: `/${destination_MaterialMgmt}/data/getXDistChainStatus`, keyName: "XSalStatus" },
      { url: `/${destination_MaterialMgmt}/data/getCSalStatus`, keyName: "CSalStatus" },
      { url: `/${destination_MaterialMgmt}/data/getEanCat`, keyName: "EanCat" },
      { url: `/${destination_MaterialMgmt}/data/getLangu`, keyName: "Langu" },
      { url: `/${destination_MaterialMgmt}/data/getSalesOrg`, keyName: "SalesOrg" },
      { url: `/${destination_MaterialMgmt}/data/getMatlStats`, keyName: "MatlStats" },
      { url: `/${destination_MaterialMgmt}/data/getMatPrGrp`, keyName: "MatPrGrp" },
      { url: `/${destination_MaterialMgmt}/data/getAcctAssgt`, keyName: "AcctAssgt" },
      { url: `/${destination_MaterialMgmt}/data/getDepcountry`, keyName: "Depcountry" },
      { url: `/${destination_MaterialMgmt}/data/getXPlant`, keyName: "PurStatus" },
      { url: `/${destination_MaterialMgmt}/data/getPurGroup`, keyName: "PurGroup" },
      { url: `/${destination_MaterialMgmt}/data/getPlantSpMatlStatus`, keyName: "PurStstus" },
      { url: `/${destination_MaterialMgmt}/data/getPlantSpMatlStatus`, keyName: "PlantSpMatlStatus" },
      { url: `/${destination_MaterialMgmt}/data/getXPlant`, keyName: "CrossPlantMaterialStatus" }, //
      { url: `/${destination_MaterialMgmt}/data/getExtMatlGrp`, keyName: "Extmatgrp" },
      { url: `/${destination_MaterialMgmt}/data/getMaterialGroupPack`, keyName: "MatGroupPackagingMat" },
      { url: `/${destination_MaterialMgmt}/data/getLoadingGroup`, keyName: "Loadinggrp" },
      { url: `/${destination_MaterialMgmt}/data/getAvailCheck`, keyName: "Availcheck" },
      { url: `/${destination_MaterialMgmt}/data/getCountryOfOrigin`, keyName: "Countryori" },
      { url: `/${destination_MaterialMgmt}/data/getRebateGrp`, keyName: "RebateGrp" },
      { url: `/${destination_MaterialMgmt}/data/getMRPType`, keyName: "MrpType" },
      { url: `/${destination_MaterialMgmt}/data/getLotSizingProcedure`, keyName: "Lotsizekey" },
      { url: `/${destination_MaterialMgmt}/data/getProcurementType`, keyName: "ProcType" },
      { url: `/${destination_MaterialMgmt}/data/getBackflush`, keyName: "Backflush" },
      { url: `/${destination_MaterialMgmt}/data/getPeriodInd`, keyName: "PeriodInd" },
      { url: `/${destination_MaterialMgmt}/data/getPlanningStrategyGroup`, keyName: "PlanStrgp" },
      { url: `/${destination_MaterialMgmt}/data/getConsumptionMode`, keyName: "Consummode" },
      { url: `/${destination_MaterialMgmt}/data/getConsumptionPeriodBkwd`, keyName: "BwdCons" },
      { url: `/${destination_MaterialMgmt}/data/getConsumptionPeriodFwd`, keyName: "FwdCons" },
      { url: `/${destination_MaterialMgmt}/data/getIndividualColl`, keyName: "DepReqId" },
      { url: `/${destination_MaterialMgmt}/data/getSaftyTimeIndicator`, keyName: "SaftyTId" },
      { url: `/${destination_MaterialMgmt}/data/getMixedMRP`, keyName: "MixedMrp" },
      { url: `/${destination_MaterialMgmt}/data/getRequirementGroup`, keyName: "GrpReqmts" },
      { url: `/${destination_MaterialMgmt}/data/getPriceUnit`, keyName: "PriceUnit" },
      { url: `/${destination_MaterialMgmt}/data/getMatlType`, keyName: "MatlType" },
      { url: `/${destination_MaterialMgmt}/data/getIndSector`, keyName: "IndSector" },
      { url: `/${destination_MaterialMgmt}/data/getTransGrp`, keyName: "TransGrp" },
      { url: `/${destination_MaterialMgmt}/data/getProfitCenter`, keyName: "ProfitCtr" },
      { url: `/${destination_MaterialMgmt}/data/getMatlGrp2`, keyName: "MatlGrp2" },
      { url: `/${destination_MaterialMgmt}/data/getProdAllocation`, keyName: "ProdAlloc" },
      { url: `/${destination_MaterialMgmt}/data/getVarianceKey`, keyName: "VarianceKey" },
      { url: `/${destination_MaterialMgmt}/data/getConsumptionPeriodFwd`, keyName: "ConsumptionPeriodFwd" },
      { url: `/${destination_MaterialMgmt}/data/getPrimaryVendor`, keyName: "PryVendor" },
      { url: `/${destination_MaterialMgmt}/data/getVendorDetails`, keyName: "Supplier" },
      { url: `/${destination_MaterialMgmt}/data/getBomUsage`, keyName: "BomUsage" },
      { url: `/${destination_MaterialMgmt}/data/getBomItemCategory`, keyName: "Category" },
      { url: `/${destination_MaterialMgmt}/data/getPlant`, keyName: "ProcurementPlant" },
      { url: `/${destination_MaterialMgmt}/data/getPurchaseOrg`, keyName: "PurchaseOrg" },
      { url: `/${destination_MaterialMgmt}/data/getStorageCondition`, keyName: "StorConds" },
      { url: `/${destination_MaterialMgmt}/data/getTemperatureCondition`, keyName: "TempConds" },
      { url: `/${destination_MaterialMgmt}/data/getContainerRequirements`, keyName: "Container" },
      { url: `/${destination_MaterialMgmt}/data/getLabelType`, keyName: "LabelType" },
      { url: `/${destination_MaterialMgmt}/data/getLabelForm`, keyName: "LabelForm" },
      { url: `/${destination_MaterialMgmt}/data/getPeriodIndicatorSLED`, keyName: "PeriodIndExpirationDate" },
      { url: `/${destination_MaterialMgmt}/data/getRoundingRuleSLED`, keyName: "RoundUpRuleExpirationDate" },
      { url: `/${destination_MaterialMgmt}/data/getExpirationDate`, keyName: "SledBbd" },
      { url: `/${destination_MaterialMgmt}/data/getSerialNumberLevel`, keyName: "SerializationLevel" },
      { url: `/${destination_MaterialMgmt}/data/getUnitOfIssue`, keyName: "IssueUnit" },
      { url: `/${destination_MaterialMgmt}/data/getTimeUnit`, keyName: "StgePdUn" },
      { url: `/${destination_MaterialMgmt}/data/getSerialNumberProfile`, keyName: "SernoProf" },
      { url: `/${destination_MaterialMgmt}/data/getDistributionProfile`, keyName: "DistrProf" },
      { url: `/${destination_MaterialMgmt}/data/getStockDeterminationGroup`, keyName: "DetermGrp" },
      { url: `/${destination_MaterialMgmt}/data/getIUIDType`, keyName: "IuidType" },
      { url: `/${destination_MaterialMgmt}/data/getClassType`, keyName: "Classtype" },
    ];
    apiCalls.forEach(({ url, keyName }) => {
      fetchDataAndDispatch(url, keyName);
    });
  }
  return { fetchAllDropdownMasterData };
};

export default useDropdownMaterialData;
