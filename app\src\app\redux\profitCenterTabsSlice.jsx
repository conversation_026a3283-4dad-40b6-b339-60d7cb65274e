import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  tabsData: [],
  headerData: [], // New: for the table input
  allFields: {},
  mandatoryFields: {},
  
};

const profitCenterTabsSlice = createSlice({
  name: 'profitCenterTabs',
  initialState,
  reducers: {
    setProfitCenterTabs: (state, action) => {
      state.tabsData = action.payload;
    },
    setProfitCenterHeaderData: (state, action) => {
      state.headerData = action.payload;
    },
    setProfitCenterConfig: (state, action) => {
      state.allFields = action.payload.ProfitCenter.allfields;
      state.mandatoryFields = action.payload.ProfitCenter.mandatoryFields;
    },
    resetProfitCenter: () => initialState,
  },
});

export const { setProfitCenterTabs, setProfitCenterHeaderData, setProfitCenterConfig, resetProfitCenter } = profitCenterTabsSlice.actions;
export default profitCenterTabsSlice.reducer;
