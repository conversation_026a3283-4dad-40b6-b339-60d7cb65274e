import { doAjax } from "@components/Common/fetchService";
import Store from "@app/store";
import { setDependentDropdown, setDropDown } from "@material/slice/materialDropdownSlice";
import useLogger from "@hooks/useLogger";
import { setValuationClassData } from "@app/payloadSlice";
import { DT_FIELDS_NAME } from "@constant/enum";
const useFetchDropdownAndDispatch = () => {
  const { customError } = useLogger();
  const fetchDataAndDispatch = (url, keyName, method = "get", payload = {},isDependentDropDown = false) => {
    const hSuccess = (data) => {
      if(!isDependentDropDown){Store.dispatch(
          setDropDown({
            keyName,
            data: data.body,
          })
        );
        }else{
          Store.dispatch(
            setDependentDropdown({
              keyName,
              data: data.body,
              keyName2 : payload?.plant || (`${payload.salesOrg}-${payload.distChnl}` || payload.salesOrg)
            })
          );
        }
        if (keyName === DT_FIELDS_NAME.VAL_CLASS && url.includes("getValuationClass")) {
        const materialTypeMatch = url.match(/matlType=([^&]+)/);
        const materialTypeCode = materialTypeMatch ? materialTypeMatch[1] : null;
          Store.dispatch(
            setValuationClassData({ 
              materialType: materialTypeCode, 
              data: data.body 
            })
          );
        }
      }

    const hError = (error) => {
      customError(error);
    };
    
    doAjax(url, method.toLowerCase(), hSuccess, hError, payload);
  };

  return { fetchDataAndDispatch };
};

export default useFetchDropdownAndDispatch;
