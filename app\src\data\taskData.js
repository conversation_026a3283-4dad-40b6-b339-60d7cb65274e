export const task = {
  systemId: "S4",
  systemName: "S4",
  processName: "**********",
  taskType: "Overall release of requisition",
  taskId: "000000394282",
  processId: "1100000046",
  requestId: "000000394282",
  referenceId: null,
  taskDesc: "Please release purchase requisition 1100000046",
  processDesc: "Please release purchase requisition 1100000046",
  businessStatus: "RESERVED",
  technicalStatus: "RESERVED",
  priority: "0",
  subject: "Please release purchase requisition 1100000046",
  createdBy: "243398",
  updatedBy: "<EMAIL>",
  completedBy: "<EMAIL>",
  formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
  itmStatus: "In Progress",
  isForwarded: 0,
  color: null,
  isPinned: 1,
  compDeadline: 1589068800000,
  criticalDeadline: 1588896000000,
  createdOn: 1588580323000,
  updatedOn: 1683134243000,
  completedAt: null,
  taskNature: "Group",
  actions: [
    {
      systemId: "S4",
      processName: "**********",
      taskType: "Overall release of requisition",
      action: "APPROVE",
      icon: "MaterialIcon.MdDone",
      hoverIcon: "",
      label: "Approve",
      priority: "PRIMARY",
      actionOrder: "POSITIVE",
      status: "RESERVED",
      isActive: 1,
    },
    {
      systemId: "S4",
      processName: "**********",
      taskType: "Overall release of requisition",
      action: "REJECT",
      icon: "IcoMoon5Icon.IoClose",
      hoverIcon: "",
      label: "Reject",
      priority: "PRIMARY",
      actionOrder: "NEGATIVE",
      status: "RESERVED",
      isActive: 1,
    },
  ],
  ownerType: "USER",
  ownerId: "<EMAIL>",
  createdByName: "243398",
  updatedByName: "George Abraham",
  forwardedByName: "",
  isCritical: false,
  isBreached: true,
  timeLeftDisplayString: "BREACHED",
  taskSla: "BREACHED",
};
