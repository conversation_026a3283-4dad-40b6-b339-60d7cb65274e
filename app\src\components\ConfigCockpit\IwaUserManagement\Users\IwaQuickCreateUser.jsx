import { QuickAddUser } from '@cw/quickadduser';
import { Stack } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from "@hooks/useSnackbar";
import {APP_END_POINTS} from "@constant/appEndPoints";

const IwaQuickCreateUser = () => {
   const { showSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const onUserActionClick = (action, response) => {
    if (action === 'home' || action === 'usersummary') {
      navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.USERS_SUMMARY);
    }

    if (response) {
      showSnackbar(response.message, "info");
    }
  };
  return (
    <Stack>
      <QuickAddUser onUserActionClick={onUserActionClick} />
    </Stack>
  );
};

export default IwaQuickCreateUser;
