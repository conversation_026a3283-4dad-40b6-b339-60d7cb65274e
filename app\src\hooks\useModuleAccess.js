import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setEntitiesAndActivities } from "../app/userManagementSlice";
import { doAjax } from "../components/Common/fetchService";
import { ERROR_MESSAGES, LOADING_MESSAGE } from "../constant/enum";
import useLogger from "@hooks/useLogger";
import { END_POINTS } from "@constant/apiEndPoints";
import { destination_MaterialMgmt, destination_IWA_NEW } from "../destinationVariables";

// Fallback entities for localhost environment
const fallbackEntities = {
  "Request Bench": ["Request Bench"],
  "Config Cockpit": ["User Management", "Email Template Configurations", "Business Rules", "Broadcast Configurations"],
  Dashboard: ["Dashboard"],
  Home: ["Home"],
  Material: ["Extend with Upload", "Change with Upload", "Create", "Change", "Extend", "Create with Upload"],
  Article: ["Create", "Change", "Create with Upload"],
  "Master Data": ["Material", "Cost Center", "Profit Center", "Bank Key", "General Ledger", "Cost Center Hierarchy", "Profit Center Hierarchy", "General Ledger Hierarchy", "Article","Internal Order"],
  "Document Management": ["Document Management"],
  Workspace: ["My Tasks", "Completed Tasks", "Admin Completed Tasks", "Admin Tasks"],
  "Data Cleanse": ["Material Master", "Cost Center", "Profit Center", "Bank Key", "General Ledger"],
};

const useModuleAccess = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const { customError, log } = useLogger();

  const fetchModuleAccess = () => {
    setLoading(true);
    setLoaderMessage(LOADING_MESSAGE.LOADING_MODULES);

    const isLocalEnv = ["localhost", "127.0.0.1"].includes(applicationConfig.environment);
    if (isLocalEnv) {
      dispatch(setEntitiesAndActivities(fallbackEntities));
      setLoading(false);
      setLoaderMessage("");
    }
    // return;
    // const url =  `/${destination_MaterialMgmt}${END_POINTS.API.MODULE_FEATURE_ACCESS}`;
    const url = isLocalEnv ? `/${destination_MaterialMgmt}${END_POINTS.API.MODULE_FEATURE_ACCESS}` : `/${destination_IWA_NEW}${END_POINTS.API.MODULE_FEATURE_ACCESS_PROD}`;

    doAjax(
      url,
      "get",
      (res) => {
        const modules = res?.data;
        const cleaned = {};

        for (const moduleName in modules) {
          cleaned[moduleName] = Object.keys(modules[moduleName]).filter((feature) => modules[moduleName][feature] === true);
        }

        dispatch(setEntitiesAndActivities(cleaned));
        if (!isLocalEnv) {
          setLoading(false);
          setLoaderMessage("");
        }
      },
      isLocalEnv
        ? () => {
          log(ERROR_MESSAGES?.ERROR_FETCHING_ROLES);
        }
        : (err) => {
          customError(ERROR_MESSAGES?.ERROR_FETCHING_ROLES, err);
          // Use fallback data even in non-local environments if API fails
          dispatch(setEntitiesAndActivities(fallbackEntities));
          setLoading(false);
          setLoaderMessage("");
        }
    );
  };

  return {
    fetchModuleAccess,
    loading,
    loaderMessage,
  };
};

export default useModuleAccess;
