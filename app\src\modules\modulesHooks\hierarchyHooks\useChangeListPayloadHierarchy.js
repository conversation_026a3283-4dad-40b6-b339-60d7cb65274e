import { useCallback } from "react";
import { useDispatch } from "react-redux";
import {
  setNodeList,
  setReplaceNodesList,
  setTagList,
  setReplaceTagList,
  setDescList,
  setEditDescList,
  setDeleteNodeList,
  setNodesListForDBDuplicateCheck,
  setDescListForDBDuplicateCheck,
} from "@app/hierarchyDataSlice"; // Adjust path if needed

const useChangeListPayloadHierarchy = () => {
  const dispatch = useDispatch();

  const preparePayload = useCallback((treeChanges) => {
    const NodeList = [];
    const ReplaceNodesList = [];
    const TagList = [];
    const ReplaceTagList = [];
    const DescList = [];
    const EditDescList = [];
    const DeleteNodeList = [];

    // New lists for DB duplicate checks
    const nodesListForDBDuplicateCheck = [];
    const descListForDBDuplicateCheck = [];

    if (!treeChanges || typeof treeChanges !== "object") {
      return {success:true};
    }

    for (const [nodeKey, change] of Object.entries(treeChanges)) {
      const {
        isNewNode,
        isMoved,
        oldParentNode,
        newParentNode,
        tags = [],
        replaceTagList = [],
        replacedTags = [],
        description,
        isDeleted,
      } = change;

      // 5. Deleted nodes
      if (isDeleted) {
        DeleteNodeList.push(nodeKey);
        continue;
      }

      // New list: nodesListForDBDuplicateCheck
      if (isNewNode) {
        nodesListForDBDuplicateCheck.push(nodeKey);
      }

      // New list: descListForDBDuplicateCheck
      if (description) {
        descListForDBDuplicateCheck.push(nodeKey);
      }

      // 1 & 6 & 7. Handle node movement logic
      if (isNewNode) {
        NodeList.push(`${oldParentNode}$$${nodeKey}`);
      } else if (isMoved) {
        NodeList.push(`${oldParentNode}$$${nodeKey}`);
        ReplaceNodesList.push(`${newParentNode}$$${nodeKey}`);
      }

      // 2. TagList: active nodes with tags
      if (tags?.length > 0) {
        tags.forEach((tag) => {
          TagList.push(`${nodeKey}$$${tag}`);
        });
      }

      // 3. replaceTagList (per node)
      if (replaceTagList?.length > 0) {
        replaceTagList.forEach((tag) => {
          ReplaceTagList.push(`${nodeKey}$$${tag}`);
        });
      }

      // 4. replacedTags
      if (replacedTags?.length > 0) {
        replacedTags.forEach((tag) => {
          ReplaceTagList.push(`${nodeKey}$$${tag}`);
        });
      }

      // 8. Description Handling
      if (description) {
        const formatted = `${nodeKey}$~$${description}`;
        if (isNewNode) {
          DescList.push(formatted);
        } else {
          EditDescList.push(formatted);
        }
      }
    }

    // Dispatch all lists to Redux
    dispatch(setNodeList(NodeList));
    dispatch(setReplaceNodesList(ReplaceNodesList));
    dispatch(setTagList(TagList));
    dispatch(setReplaceTagList(ReplaceTagList));
    dispatch(setDescList(DescList));
    dispatch(setEditDescList(EditDescList));
    dispatch(setDeleteNodeList(DeleteNodeList));
    dispatch(setNodesListForDBDuplicateCheck(nodesListForDBDuplicateCheck));
    dispatch(setDescListForDBDuplicateCheck(descListForDBDuplicateCheck));

    return {success:true,NodeList,ReplaceNodesList,TagList,ReplaceTagList,DescList,EditDescList,DeleteNodeList,nodesListForDBDuplicateCheck,descListForDBDuplicateCheck };
  }, [dispatch]);

  return { preparePayload };
};

export default useChangeListPayloadHierarchy;
