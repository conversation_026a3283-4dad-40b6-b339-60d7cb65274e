import * as React from "react";
import AppBar from "@mui/material/AppBar";
import Box from "@mui/material/Box";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import Badge from "@mui/material/Badge";
import AccountCircle from "@mui/icons-material/AccountCircle";
import NotificationsNoneIcon from "@mui/icons-material/NotificationsNone";
import NotificationsIcon from "@mui/icons-material/Notifications";
import SettingsOutlinedIcon from "@mui/icons-material/SettingsOutlined";
import "./NavBar.css";
import logo from "../../logo.svg";
import conair_img from "../../conair_logo.png";
import { FormControlLabel, Stack, Switch, Tooltip, Typography } from "@mui/material";
import AccountMenu from "./AccountMenu";
import { styled } from "@mui/material/styles";
import { useSelector } from "react-redux";
import ReusableIcon from "./ReusableIcon";
import ApplicationSettings from "./ApplicationSettings";
import { useDispatch } from "react-redux";
import { pushNotification, resetNotificationCount } from "../../app/notificationSlice";
import AppNotification from "./AppNotification";
import NotificationDrawer from "./NotificationDrawer"
import HealthMonitor from "../../screens/HealthMonitor";
import InternetSpeedChecker from "./ui/InternetSpeedCheck";
import useLang from "../../hooks/useLang";
import ForumIcon from '@mui/icons-material/Forum';
import ModernChatDialog from "../../screens/ModernChatDialog"
// import { resetNotificationCount } from "../../app/notificationsSlice";
// let  imports = import('../../ConfigurationModule')

export function AppHeader() {
  // let userData = {
  //   id: "P000264",
  //   user_id: "P000264",
  //   firstName: "Raunak",
  //   lastName: "Sinha",
  //   emailId: "<EMAIL>",
  //   displayName: "Raunak Sinha",
  //   userName: "Raunak Sinha",
  //   role: "Admin",
  //   companyCode: "INC",
  //   companyName: "Incture",
  //   supplierId: "XYZ",
  //   supplierName: "TechSystems",
  // };
  let userData = useSelector((state) => state.userManagement.userData);
  let roles = useSelector((state) => state.userManagement.roles);
  const selectedRole = roles.includes("CA-MDG-SUPER-USER")
    ? "CA-MDG-SUPER-USER"
    : roles[0]; 
  let notificationData = useSelector((state) => state.notifications.notification);
  const dispatch = useDispatch();
  let importedModules = useSelector((state) => state.applicationConfig.importedModules);
  const { t } = useLang(); // Add this line to use the translation function
  // let AppNotification = importedModules['AppNotification.jsx']
  // let DemoApp = importedModules['Demo.jsx']

  // let dispatch = useDispatch()
  // const [accountActivity_OpenStatus, setaccountActivity_OpenStatus] = React.useState(false);
  // const handleOpen_Notifications = (e) => {
  //   dispatch(resetNotificationCount())
  //   setaccountActivity_OpenStatus(true);
  // };
  // const handleClose_Notifications = () => {
  //   setaccountActivity_OpenStatus(false);
  // };
  // useEffect(()=>{

  // },[])
  const [appSettings_OpenStatus, setAppSettings_OpenStatus] =
    React.useState(false);
  const handleOpen_AppSettings = () => {
    setAppSettings_OpenStatus(true);
  };
  const handleClose_AppSettings = () => {
    setAppSettings_OpenStatus(false);
  };
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [iconColor, setIconColor] = React.useState("#424242");
  const [accountActivity_OpenStatus, setaccountActivity_OpenStatus] = React.useState(false);
  const [currentNotificationCount, setCurrentNotificationCount] = React.useState(notificationData?.count);
  const [isChatOpen, setIsChatOpen] = React.useState(false);
  const open = Boolean(anchorEl);

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleClose_Notifications = () => {
    setaccountActivity_OpenStatus(false);
  };

  return (
    <>
      {appSettings_OpenStatus && <ApplicationSettings status={appSettings_OpenStatus} handleClose={handleClose_AppSettings} />}
      {accountActivity_OpenStatus && AppNotification && <AppNotification status={accountActivity_OpenStatus} data={notificationData?.data} handleClose={handleClose_Notifications} />}
      <AppBar
        style={{
          backgroundColor: "white",
          position: "fixed",
        }}
        sx={{
          zIndex: 1002,
        }}
        position="fixed"
      >
        <Toolbar>
          {/* <IconButton
            size="large"
            edge="start"
            color="inherit"
            sx={{ mr: 2 }}
          ></IconButton> */}
          {/* <div className="logo" style={{ flexGrow: 1 }}> */}
          <img style={{ verticalAlign: "middle" }} src={logo} alt=" " height="42vh" width="auto" />
          {/* </div> */}
          <Box sx={{ flexGrow: 1 }}>
            {userData && (
              <Typography
                sx={{
                  color: "black",
                  display: "flex",
                  fontWeight: "bold",
                  justifyContent: "flex-end",
                }}
              >
                {`${t("Welcome")}, ${userData?.firstName} ${userData?.lastName}`}&nbsp;
              </Typography>
            )}
          </Box>
          <Box sx={{ display: { xs: "flex", md: "flex" } }}>
            <AccountMenu />
          </Box>
          {/* {AppNotification && */}
          <Box paddingLeft={".5rem"}>
            <Tooltip title={t("Application Settings")} sx={{ zIndex: 999 }}>
              <IconButton onClick={handleOpen_AppSettings}>
                <ReusableIcon iconName={"Settings"} iconSize={"23px"} iconColor={"#424242"} />
              </IconButton>
            </Tooltip>
          </Box>
          <Box display="flex" sx={{ marginRight: "-10px" }}>
            <HealthMonitor />
          </Box>
          <Box display="flex" sx={{ marginLeft: "0.6%" }}>
            <InternetSpeedChecker  />
          </Box>
          <Box display="flex" sx={{ marginRight: "-5px" }}>
            <Tooltip title="Open Chat">
              <IconButton
                
                onClick={() => setIsChatOpen(true)}
                sx={{
                  width: 40,
                  height: 40,
                  '&:hover': {
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                <Badge badgeContent={0} color="error">
                  <ForumIcon 
                    sx={{ color: "rgb(173, 127, 204) ", fontSize: "23px" }}
                  />
                </Badge>
              </IconButton>
            </Tooltip>
          </Box>
          <ModernChatDialog
            open={isChatOpen}
            onClose={() => setIsChatOpen(false)}
          />
          <Box display="flex" sx={{ marginRight: "-10px" }}>
            <NotificationDrawer />
          </Box>
        </Toolbar>
      </AppBar>
    </>
  );
}

export default AppHeader;
