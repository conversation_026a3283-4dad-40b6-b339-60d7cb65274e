import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  validatedRowsStatus: {},
  payload: {
    requestHeaderData: {},
    rowsHeaderData: [
      {
        id: "first_request_id_requestor",
        BankCtry: "",
        BankKey: "",
        BankName: "",
        BankBranch: "",
        included: true,
        validated: "default",
      },
    ],
    rowsBodyData: {},
    childRequestHeaderData: {}
  },
  changeLogData: [],
  mandatoryFields: {},
  tabValue: 0,
  headerFieldsBnky: {},
  requestHeaderResponse: {},
  selectedRowID: null,
  dropDownData: {},
  isOdataApiCalled: false,
  isOpenDialog: true,
  bankKeyData: {},
  bankKeyTabs: {},
  bankKeyConfig: {},
};

const bnkySlice = createSlice({
  name: "bankkey",
  initialState,
  reducers: {
    setBankKeyPayload: (state, action) => {
      state.payload = action.payload;
    },
    setBankKeyPayloadIndividual: (state, action) => {
      const { keyName, data } = action.payload;
      state.payload[keyName] = data;
      return state;
    },
    setRowsHeaderData: (state, action) => {
      state.payload.rowsHeaderData = action.payload;
    },
    setRowsBodyData: (state, action) => {
      state.payload.rowsBodyData = action.payload;
    },
    setApiChangeLogData: (state, action) => {
      state.payload.changeLogData = action.payload;
    },
    setOdataApiCall: (state, action) => {
      state.isOdataApiCalled = action.payload;
    },
    clearHeaderFieldsBnky: (state) => {
      state.headerFieldsBnky = {};
      state.requestHeaderID = null;
    },
    setTabValue: (state, action) => {
      state.tabValue = action.payload;
    },
    setHeaderFieldsBnky: (state, action) => {
      state.headerFieldsBnky = action.payload;
    },
    setSelectedRowID: (state, action) => {
      state.selectedRowID = action.payload;
    },
    setDropDownDataBNKY: (state, action) => {
      state.dropDownData[action.payload.keyName] = action.payload.data;
      return state;
    },
    setRequestHeaderPayloadSingleField: (state, action) => {
      state.payload.requestHeaderData[action.payload.keyName] =
        action.payload.data;
      return state;
    },
    setRequestHeaderPayloadData: (state, action) => {
      state.payload.requestHeaderData = action.payload;
      return state;
    },
    setOpenDialog: (state, action) => {
      state.isOpenDialog = action.payload;
      // localStorage.setItem("isOpenDialog", JSON.stringify(action.payload));
    },
    setValidatedStatus: (state, action) => {
      const { rowId, status } = action.payload;
      state.validatedRowsStatus[rowId] = status;
    },
    resetValidationStatus: (state) => {
      state.validatedRowsStatus = {};
    },
    updateModuleFieldDataBK: (state, action) => {
      const { uniqueId, viewID, keyName, data } = action.payload;

      if (uniqueId) {
        if (!state.payload.rowsBodyData[uniqueId]) {
          state.payload.rowsBodyData[uniqueId] = {};
        }

        if (keyName) {
          state.payload.rowsBodyData[uniqueId][keyName] = data?.code
            ? data.code
            : data
              ? data
              : "";
        }
      } else {
        state.payload.requestHeaderData[action.payload.keyName] = action
          ?.payload?.data?.code
          ? action?.payload?.data?.code
          : action?.payload?.data
            ? action?.payload?.data
            : "";
      }

      return state;
    },
    resetPayloadData: (state) => {
      (state.payload = {
        requestHeaderData: {},
        rowsHeaderData: [
          {
            id: "first_request_id_requestor",
            controllingArea: "",
            bankKeyNumber: "",
            longDescription: "",
            businessSegment: "",
            companyCode: "",
            included: true,
            validated: false,
          },
        ],
        rowsBodyData: {},
      }),
        (state.bankKeyTabs = {});
    },
    setBankKeyData: (state, action) => {
      state.bankKeyData = action.payload;
    },
    setBankKeyTabs: (state, action) => {
      action.payload.forEach(({ keyName, tab, data }) => {
        if (!state.bankKeyTabs[keyName]) {
          state.bankKeyTabs[keyName] = [];
        }

        const existingTabIndex = state.bankKeyTabs[keyName].findIndex(
          (t) => t.tab === tab
        );

        if (existingTabIndex !== -1) {
          state.bankKeyTabs[keyName][existingTabIndex].data = data;
        } else {
          state.bankKeyTabs[keyName].push({ tab, data });
        }
      });
    },
    setBankKeyConfig: (state, action) => {
      console.log("Setting bank key in Redux:", action.payload);
      state.bankKeyConfig = action.payload;
    },
    setRequestHeaderResponse: (state, action) => { // New reducer
      state.requestHeaderResponse = action.payload;
    },
    setMandatoryFieldsBK: (state, action) => { // New reducer
      state.mandatoryFields = action.payload;
    },
    resetBankKeyStateBk: () => initialState,
  },
});

export const {
  resetBankKeyStateBk,
  setBankKeyData,
  setBankKeyTabs,
  setBankKeyConfig,
  resetValidationStatus,
  setValidatedStatus,
  setOpenDialog,
  setRequestHeaderPayloadSingleField,
  setRequestHeaderPayloadData,
  updateModuleFieldDataBK,
  resetPayloadData,
  setBankKeyTab,
  setRowsHeaderData,
  setRowsBodyData,
  setBankKeyPayload,
  setBankKeyPayloadIndividual,
  clearHeaderFieldsBnky,
  setError,
  clearError,
  setTabValue,
  setHeaderFieldsBnky,
  setRequestHeaderResponse,
  setDropDownDataBNKY,
  setSelectedRowID,
  clearBankKeyPayloadData,
  setOdataApiCall,
  setMandatoryFieldsBK,
  setApiChangeLogData
} = bnkySlice.actions;

export default bnkySlice.reducer; 