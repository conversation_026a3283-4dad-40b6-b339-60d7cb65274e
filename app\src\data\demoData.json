{"d": {"results": [{"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_Product('220')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_Product('220')", "type": "API_PRODUCT_SRV.A_ProductType"}, "Product": "220", "ProductType": "YROH", "CrossPlantStatus": "01", "CrossPlantStatusValidityDate": "/Date(1690848000000)/", "CreationDate": "/Date(1690848000000)/", "CreatedByUser": "INC02490", "LastChangeDate": "/Date(1692144000000)/", "LastChangedByUser": "INC02490", "LastChangeDateTime": "/Date(1692166156000+0000)/", "IsMarkedForDeletion": false, "ProductOldID": "PA1F-KWP-209", "GrossWeight": "18.000", "PurchaseOrderQuantityUnit": "CAR", "SourceOfSupply": "", "WeightUnit": "KG", "NetWeight": "16.000", "CountryOfOrigin": "", "CompetitorID": "", "ProductGroup": "0001", "BaseUnit": "EA", "ItemCategoryGroup": "NORM", "ProductHierarchy": "000101200030100103", "Division": "10", "VarblPurOrdUnitIsActive": "", "VolumeUnit": "M3", "MaterialVolume": "12.000", "ANPCode": "135", "Brand": "", "ProcurementRule": "", "ValidityStartDate": null, "LowLevelCode": "", "ProdNoInGenProdInPrepackProd": "", "SerialIdentifierAssgmtProfile": "S2P", "SizeOrDimensionText": "3*2*2", "IndustryStandardName": "HARD", "ProductStandardID": "2150000000000", "InternationalArticleNumberCat": "EA", "ProductIsConfigurable": true, "IsBatchManagementRequired": true, "ExternalProductGroup": "RTYP", "CrossPlantConfigurableProduct": "", "SerialNoExplicitnessLevel": "1", "ProductManufacturerNumber": "", "ManufacturerNumber": "", "ManufacturerPartProfile": "", "QltyMgmtInProcmtIsActive": true, "IndustrySector": "M", "ChangeNumber": "", "MaterialRevisionLevel": "", "HandlingIndicator": "HM", "WarehouseProductGroup": "RAW", "WarehouseStorageCondition": "SM", "StandardHandlingUnitType": "SP1", "SerialNumberProfile": "S2P", "AdjustmentProfile": "", "PreferredUnitOfMeasure": "CAR", "IsPilferable": true, "IsRelevantForHzdsSubstances": true, "QuarantinePeriod": "2", "TimeUnitForQuarantinePeriod": "KG", "QualityInspectionGroup": "TQM", "AuthorizationGroup": "21", "HandlingUnitType": "YN00", "HasVariableTareWeight": true, "MaximumPackagingLength": "3.000", "MaximumPackagingWidth": "4.000", "MaximumPackagingHeight": "5.000", "UnitForMaxPackagingDimensions": "M", "to_Description": {"results": [{"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductDescription(Product='220',Language='EN')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductDescription(Product='220',Language='EN')", "type": "API_PRODUCT_SRV.A_ProductDescriptionType"}, "Product": "220", "Language": "EN", "ProductDescription": "MDG Test Material"}]}, "to_Plant": {"results": [{"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPlant(Product='220',Plant='I00X')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPlant(Product='220',Plant='I00X')", "type": "API_PRODUCT_SRV.A_ProductPlantType"}, "Product": "220", "Plant": "I00X", "PurchasingGroup": "", "CountryOfOrigin": "US", "RegionOfOrigin": "AK", "ProductionInvtryManagedLoc": "", "ProfileCode": "", "ProfileValidityStartDate": null, "AvailabilityCheckType": "SR", "FiscalYearVariant": "", "PeriodType": "M", "ProfitCenter": "YB110", "Commodity": "", "GoodsReceiptDuration": "0", "MaintenanceStatusName": "V", "IsMarkedForDeletion": false, "MRPType": "", "MRPResponsible": "", "ABCIndicator": "", "MinimumLotSizeQuantity": "0", "MaximumLotSizeQuantity": "0", "FixedLotSizeQuantity": "0", "ConsumptionTaxCtrlCode": "", "IsCoProduct": false, "ProductIsConfigurable": "", "StockDeterminationGroup": "", "StockInTransferQuantity": "0", "StockInTransitQuantity": "0", "HasPostToInspectionStock": false, "IsBatchManagementRequired": true, "SerialNumberProfile": "", "IsNegativeStockAllowed": true, "GoodsReceiptBlockedStockQty": "0", "HasConsignmentCtrl": "", "FiscalYearCurrentPeriod": "2023", "FiscalMonthCurrentPeriod": "8", "ProcurementType": "", "IsInternalBatchManaged": true, "ProductCFOPCategory": "", "ProductIsExciseTaxRelevant": false, "BaseUnit": "EA", "ConfigurableProduct": "", "GoodsIssueUnit": "", "MaterialFreightGroup": "JT1", "OriginalBatchReferenceMaterial": "", "OriglBatchManagementIsRequired": "", "ProductIsCriticalPrt": false, "ProductLogisticsHandlingGroup": "", "to_PlantMRPArea": {"results": []}, "to_PlantQualityMgmt": {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPlantQualityMgmt(Product='220',Plant='I00X')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPlantQualityMgmt(Product='220',Plant='I00X')", "type": "API_PRODUCT_SRV.A_ProductPlantQualityMgmtType"}, "Product": "220", "Plant": "I00X", "MaximumStoragePeriod": "0", "QualityMgmtCtrlKey": "", "MatlQualityAuthorizationGroup": "", "HasPostToInspectionStock": false, "InspLotDocumentationIsRequired": false, "SuplrQualityManagementSystem": "", "RecrrgInspIntervalTimeInDays": "0", "ProductQualityCertificateType": ""}, "to_PlantSales": {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPlantSales(Product='220',Plant='I00X')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPlantSales(Product='220',Plant='I00X')", "type": "API_PRODUCT_SRV.A_ProductPlantSalesType"}, "Product": "220", "Plant": "I00X", "LoadingGroup": "0002", "ReplacementPartType": "", "CapPlanningQuantityInBaseUoM": "50", "ProductShippingProcessingTime": "42.00", "WrkCentersShipgSetupTimeInDays": "22.00", "AvailabilityCheckType": "SR", "BaseUnit": "EA"}, "to_PlantStorage": {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPlantStorage(Product='220',Plant='I00X')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPlantStorage(Product='220',Plant='I00X')", "type": "API_PRODUCT_SRV.A_ProductPlantStorageType"}, "Product": "220", "Plant": "I00X", "InventoryForCycleCountInd": "", "ProvisioningServiceLevel": "", "CycleCountingIndicatorIsFixed": false, "ProdMaximumStoragePeriodUnit": "", "WrhsMgmtPtwyAndStkRemovalStrgy": ""}, "to_PlantText": null, "to_ProdPlantInternationalTrade": {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPlantIntlTrd(Product='220',Plant='I00X')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPlantIntlTrd(Product='220',Plant='I00X')", "type": "API_PRODUCT_SRV.A_ProductPlantIntlTrdType"}, "Product": "220", "Plant": "I00X", "CountryOfOrigin": "US", "RegionOfOrigin": "AK", "ConsumptionTaxCtrlCode": "", "ProductCASNumber": "", "ProdIntlTradeClassification": "", "ExportAndImportProductGroup": ""}, "to_ProductPlantCosting": {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPlantCosting(Product='220',Plant='I00X')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPlantCosting(Product='220',Plant='I00X')", "type": "API_PRODUCT_SRV.A_ProductPlantCostingType"}, "Product": "220", "Plant": "I00X", "IsCoProduct": false, "CostingLotSize": "0", "VarianceKey": "", "BaseUnit": "EA", "TaskListGroupCounter": "", "TaskListGroup": "", "TaskListType": "", "CostingProductionVersion": "", "IsFixedPriceCoProduct": false, "CostingSpecialProcurementType": "", "SourceBOMAlternative": "", "ProductBOMUsage": "", "ProductIsCostingRelevant": false}, "to_ProductPlantForecast": {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPlantForecasting(Product='220',Plant='I00X')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPlantForecasting(Product='220',Plant='I00X')", "type": "API_PRODUCT_SRV.A_ProductPlantForecastingType"}, "Product": "220", "Plant": "I00X", "ConsumptionRefUsageEndDate": null, "ConsumptionQtyMultiplier": "0.00", "ConsumptionReferenceProduct": "", "ConsumptionReferencePlant": ""}, "to_ProductPlantProcurement": {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPlantProcurement(Product='220',Plant='I00X')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPlantProcurement(Product='220',Plant='I00X')", "type": "API_PRODUCT_SRV.A_ProductPlantProcurementType"}, "Product": "220", "Plant": "I00X", "IsAutoPurOrdCreationAllowed": false, "IsSourceListRequired": false, "SourceOfSupplyCategory": "", "ItmIsRlvtToJITDelivSchedules": ""}, "to_ProductSupplyPlanning": {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSupplyPlanning(Product='220',Plant='I00X')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSupplyPlanning(Product='220',Plant='I00X')", "type": "API_PRODUCT_SRV.A_ProductSupplyPlanningType"}, "Product": "220", "Plant": "I00X", "FixedLotSizeQuantity": "0", "MaximumLotSizeQuantity": "0", "MinimumLotSizeQuantity": "0", "LotSizeRoundingQuantity": "0", "LotSizingProcedure": "", "MRPType": "", "MRPResponsible": "", "SafetyStockQuantity": "0", "MinimumSafetyStockQuantity": "0", "PlanningTimeFence": "0", "ABCIndicator": "", "MaximumStockQuantity": "0", "ReorderThresholdQuantity": "0", "PlannedDeliveryDurationInDays": "0", "SafetyDuration": "0", "PlanningStrategyGroup": "", "TotalReplenishmentLeadTime": "0", "ProcurementType": "", "ProcurementSubType": "", "AssemblyScrapPercent": "0.00", "AvailabilityCheckType": "SR", "GoodsReceiptDuration": "0", "MRPGroup": "", "DfltStorageLocationExtProcmt": "", "ProdRqmtsConsumptionMode": "", "BackwardCnsmpnPeriodInWorkDays": "0", "FwdConsumptionPeriodInWorkDays": "0", "BaseUnit": "EA", "PlanAndOrderDayDetermination": "", "RoundingProfile": "", "LotSizeIndependentCosts": "0.00", "MRPPlanningCalendar": "", "RangeOfCvrgPrflCode": "", "IsSafetyTime": "", "PerdPrflForSftyTme": "", "IsMRPDependentRqmt": "", "InHouseProductionTime": "0", "ProductIsForCrossProject": "", "StorageCostsPercentageCode": "", "SrvcLvl": "0.0", "MRPAvailabilityType": "", "FollowUpProduct": "", "RepetitiveManufacturingIsAllwd": false, "DependentRequirementsType": "", "IsBulkMaterialComponent": false, "RepetitiveManufacturingProfile": "", "RqmtQtyRcptTaktTmeInWrkgDays": "0", "ForecastRequirementsAreSplit": "", "EffectiveOutDate": null, "MRPProfile": "", "ComponentScrapInPercent": "0.00", "ProductIsToBeDiscontinued": "", "ProdRqmtsAreConsolidated": "", "MatlCompIsMarkedForBackflush": "", "ProposedProductSupplyArea": "", "Currency": "USD", "PlannedOrderActionControl": ""}, "to_ProductWorkScheduling": {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductWorkScheduling(Product='220',Plant='I00X')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductWorkScheduling(Product='220',Plant='I00X')", "type": "API_PRODUCT_SRV.A_ProductWorkSchedulingType"}, "Product": "220", "Plant": "I00X", "MaterialBaseQuantity": "0", "UnlimitedOverDelivIsAllowed": false, "OverDelivToleranceLimit": "0.0", "UnderDelivToleranceLimit": "0.0", "ProductionInvtryManagedLoc": "", "BaseUnit": "EA", "ProductProcessingTime": "0.00", "ProductionSupervisor": "", "ProductProductionQuantityUnit": "", "ProdnOrderIsBatchRequired": "", "TransitionMatrixProductsGroup": "", "OrderChangeManagementProfile": "", "MatlCompIsMarkedForBackflush": "", "SetupAndTeardownTime": "0.00", "ProductionSchedulingProfile": "", "TransitionTime": "0.00"}, "to_StorageLocation": {"results": []}}]}, "to_ProductBasicText": {"results": [{"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductBasicText(Product='220',Language='EN')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductBasicText(Product='220',Language='EN')", "type": "API_PRODUCT_SRV.A_ProductBasicTextType"}, "Product": "220", "Language": "EN", "LongText": "Handle With Care"}]}, "to_ProductInspectionText": {"results": [{"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductInspectionText(Product='220',Language='EN')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductInspectionText(Product='220',Language='EN')", "type": "API_PRODUCT_SRV.A_ProductInspectionTextType"}, "Product": "220", "Language": "EN", "LongText": "Do check Tolerance Limit"}]}, "to_ProductProcurement": {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductProcurement('220')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductProcurement('220')", "type": "API_PRODUCT_SRV.A_ProductProcurementType"}, "Product": "220", "PurchaseOrderQuantityUnit": "CAR", "VarblPurOrdUnitStatus": "", "PurchasingAcknProfile": "2"}, "to_ProductPurchaseText": {"results": [{"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPurchaseText(Product='220',Language='EN')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductPurchaseText(Product='220',Language='EN')", "type": "API_PRODUCT_SRV.A_ProductPurchaseTextType"}, "Product": "220", "Language": "EN", "LongText": "Purchasing Material\n\n"}]}, "to_ProductQualityMgmt": {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductQualityMgmt('220')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductQualityMgmt('220')", "type": "API_PRODUCT_SRV.A_ProductQualityMgmtType"}, "Product": "220", "QltyMgmtInProcmtIsActive": true}, "to_ProductSales": {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSales('220')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSales('220')", "type": "API_PRODUCT_SRV.A_ProductSalesType"}, "Product": "220", "SalesStatus": "04", "SalesStatusValidityDate": "/Date(1692144000000)/", "TaxClassification": "", "TransportationGroup": "0001"}, "to_ProductSalesTax": {"results": [{"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSalesTax(Product='220',Country='US',TaxCategory='UTXJ',TaxClassification='')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSalesTax(Product='220',Country='US',TaxCategory='UTXJ',TaxClassification='')", "type": "API_PRODUCT_SRV.A_ProductSalesTaxType"}, "Product": "220", "Country": "US", "TaxCategory": "UTXJ", "TaxClassification": ""}, {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSalesTax(Product='220',Country='IN',TaxCategory='JOCG',TaxClassification='')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSalesTax(Product='220',Country='IN',TaxCategory='JOCG',TaxClassification='')", "type": "API_PRODUCT_SRV.A_ProductSalesTaxType"}, "Product": "220", "Country": "IN", "TaxCategory": "JOCG", "TaxClassification": ""}, {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSalesTax(Product='220',Country='IN',TaxCategory='JOSG',TaxClassification='')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSalesTax(Product='220',Country='IN',TaxCategory='JOSG',TaxClassification='')", "type": "API_PRODUCT_SRV.A_ProductSalesTaxType"}, "Product": "220", "Country": "IN", "TaxCategory": "JOSG", "TaxClassification": ""}, {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSalesTax(Product='220',Country='IN',TaxCategory='JOIG',TaxClassification='')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSalesTax(Product='220',Country='IN',TaxCategory='JOIG',TaxClassification='')", "type": "API_PRODUCT_SRV.A_ProductSalesTaxType"}, "Product": "220", "Country": "IN", "TaxCategory": "JOIG", "TaxClassification": ""}, {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSalesTax(Product='220',Country='IN',TaxCategory='JOUG',TaxClassification='')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSalesTax(Product='220',Country='IN',TaxCategory='JOUG',TaxClassification='')", "type": "API_PRODUCT_SRV.A_ProductSalesTaxType"}, "Product": "220", "Country": "IN", "TaxCategory": "JOUG", "TaxClassification": ""}]}, "to_ProductStorage": {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductStorage('220')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductStorage('220')", "type": "API_PRODUCT_SRV.A_ProductStorageType"}, "Product": "220", "StorageConditions": "20", "TemperatureConditionInd": "4", "HazardousMaterialNumber": "", "NmbrOfGROrGISlipsToPrintQty": "76", "LabelType": "M7", "LabelForm": "E2", "MinRemainingShelfLife": "200", "ExpirationDate": "B", "ShelfLifeExpirationDatePeriod": "", "TotalShelfLife": "400", "BaseUnit": "EA"}, "to_ProductUnitsOfMeasure": {"results": [{"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductUnitsOfMeasure(Product='220',AlternativeUnit='EA')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductUnitsOfMeasure(Product='220',AlternativeUnit='EA')", "type": "API_PRODUCT_SRV.A_ProductUnitsOfMeasureType"}, "Product": "220", "AlternativeUnit": "EA", "QuantityNumerator": "1", "QuantityDenominator": "1", "MaterialVolume": "12.000", "VolumeUnit": "M3", "GrossWeight": "18.000", "WeightUnit": "KG", "GlobalTradeItemNumber": "2150000000000", "GlobalTradeItemNumberCategory": "EA", "UnitSpecificProductLength": "0.000", "UnitSpecificProductWidth": "0.000", "UnitSpecificProductHeight": "0.000", "ProductMeasurementUnit": "", "LowerLevelPackagingUnit": "", "RemainingVolumeAfterNesting": "0", "MaximumStackingFactor": 0, "CapacityUsage": "0.000", "BaseUnit": "EA", "to_InternationalArticleNumber": {"__deferred": {"uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductUnitsOfMeasure(Product='220',AlternativeUnit='EA')/to_InternationalArticleNumber"}}}, {"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductUnitsOfMeasure(Product='220',AlternativeUnit='CAR')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductUnitsOfMeasure(Product='220',AlternativeUnit='CAR')", "type": "API_PRODUCT_SRV.A_ProductUnitsOfMeasureType"}, "Product": "220", "AlternativeUnit": "CAR", "QuantityNumerator": "10", "QuantityDenominator": "1", "MaterialVolume": "0.000", "VolumeUnit": "", "GrossWeight": "0.000", "WeightUnit": "", "GlobalTradeItemNumber": "", "GlobalTradeItemNumberCategory": "", "UnitSpecificProductLength": "0.000", "UnitSpecificProductWidth": "0.000", "UnitSpecificProductHeight": "0.000", "ProductMeasurementUnit": "", "LowerLevelPackagingUnit": "", "RemainingVolumeAfterNesting": "0", "MaximumStackingFactor": 0, "CapacityUsage": "0.000", "BaseUnit": "EA", "to_InternationalArticleNumber": {"__deferred": {"uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductUnitsOfMeasure(Product='220',AlternativeUnit='CAR')/to_InternationalArticleNumber"}}}]}, "to_SalesDelivery": {"results": [{"__metadata": {"id": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSalesDelivery(Product='220',ProductSalesOrg='IN01',ProductDistributionChnl='10')", "uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSalesDelivery(Product='220',ProductSalesOrg='IN01',ProductDistributionChnl='10')", "type": "API_PRODUCT_SRV.A_ProductSalesDeliveryType"}, "Product": "220", "ProductSalesOrg": "IN01", "ProductDistributionChnl": "10", "MinimumOrderQuantity": "0", "SupplyingPlant": "BP01", "PriceSpecificationProductGroup": "M2", "AccountDetnProductGroup": "03", "DeliveryNoteProcMinDelivQty": "0", "ItemCategoryGroup": "NORM", "DeliveryQuantityUnit": "", "DeliveryQuantity": "0.000", "ProductSalesStatus": "03", "ProductSalesStatusValidityDate": "/Date(*************)/", "SalesMeasureUnit": "CAR", "IsMarkedForDeletion": false, "ProductHierarchy": "000101200030100103", "FirstSalesSpecProductGroup": "J1", "SecondSalesSpecProductGroup": "Y1", "ThirdSalesSpecProductGroup": "Y3", "FourthSalesSpecProductGroup": "Y4", "FifthSalesSpecProductGroup": "Y5", "MinimumMakeToOrderOrderQty": "0", "BaseUnit": "EA", "LogisticsStatisticsGroup": "1", "VolumeRebateGroup": "JI", "ProductCommissionGroup": "JC", "CashDiscountIsDeductible": true, "PricingReferenceProduct": "", "RoundingProfile": "", "ProductUnitGroup": "JIND", "VariableSalesUnitIsNotAllowed": false, "ProductHasAttributeID01": true, "ProductHasAttributeID02": true, "ProductHasAttributeID03": true, "ProductHasAttributeID04": true, "ProductHasAttributeID05": true, "ProductHasAttributeID06": true, "ProductHasAttributeID07": true, "ProductHasAttributeID08": true, "ProductHasAttributeID09": true, "ProductHasAttributeID10": true, "to_SalesTax": {"__deferred": {"uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSalesDelivery(Product='220',ProductSalesOrg='IN01',ProductDistributionChnl='10')/to_SalesTax"}}, "to_SalesText": {"__deferred": {"uri": "http://**************:8080/sap/opu/odata/sap/API_PRODUCT_SRV/A_ProductSalesDelivery(Product='220',ProductSalesOrg='IN01',ProductDistributionChnl='10')/to_SalesText"}}}]}, "to_Valuation": {"results": []}}]}}