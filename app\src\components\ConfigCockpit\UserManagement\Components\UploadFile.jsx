import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog,
  DialogTitle,
  DialogActions,
  DialogContent,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { useSelector } from "react-redux";
import Loading from "./Loading";

const useStyle = makeStyles((theme) => ({
  idpUsersDialogTitle: {
    height: "3rem",
    display: "flex",
    alignItems: "center",
    borderBottom: "1px solid #d9d9d9",
    margin: 0,
    padding: 0,
    paddingLeft: 10,
  },
  idpUsersDialogActions: {
    height: "3rem",
    borderTop: "1px solid #d9d9d9",
  },
}));

const UploadFile = ({
  open,
  onClose,
  onUpload,
  file,
  setFile,
  disableCondition,
  children,
  load,
}) => {
  const classes = useStyle();
  const userReducerState = useSelector((state) => state.userReducer);

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={() => {
        onClose();
      }}
    >
      <DialogTitle className={classes.idpUsersDialogTitle}>
        Upload File
      </DialogTitle>

      <DialogContent>
        <Loading load={load} />

        <input
          type="file"
          name="file"
          accept=".csv"
          onChange={(event) => {
            if (event.target.files.length > 0) {
              if (event.target.files[0].type === "text/csv") {
                setFile(event.target.files[0]);
              } else {
                setFile(null);
              }
            } else {
              setFile(null);
            }
          }}
          required
          className={userReducerState?.darkMode && "inputDarkMode"}
        />

        {children}
      </DialogContent>

      <DialogActions className={classes.idpUsersDialogActions}>
        <Button
          size="small"
          variant="outlined"
          sx={{
            textTransform: "none",
            borderColor: "#3B30C8",
            color: "#3B30C8",
          }}
          onClick={() => {
            onClose();
          }}
          disabled={load}
        >
          Cancel
        </Button>

        <Button
          size="small"
          variant="contained"
          sx={{
            textTransform: "none",
            backgroundColor: "#3B30C8",
            marginRight: "1.5rem",
          }}
          onClick={onUpload}
          disabled={disableCondition || load}
        >
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UploadFile;
