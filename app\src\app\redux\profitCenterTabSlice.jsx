import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  rows: [], // For table display
  tabs: {}, // For tab data keyed by rowId
  selectedRowId: null,
  apiData: [], // To track selected row
};

const profitCenterTabSlice = createSlice({
  name: "profitCenterData",
  initialState,
  reducers: {
    setProfitCenterApiData: (state, action) => {
      state.apiData = action.payload;
    },

    setProfitCenterRows: (state, action) => {
      state.rows = action.payload;
      console.log("setProfitCenterRows1", action.payload);
    },
    setProfitCenterTabs: (state, action) => {
      // console.log("state",state,action)
      console.log("action", action.payload);
      state.tabs = action.payload;
      // console.log("setProfitCenterTabspayload", action);
    },
    setSelectedRowId: (state, action) => {
      state.selectedRowId = action.payload; // e.g. "1358"
    },
    clearProfitCenterData: (state) => {
      state.rows = [];
      state.tabs = {};
      state.selectedRowId = null;
    },
  },
});

export const {
  setProfitCenterRows,
  setProfitCenterTabs,
  setSelectedRowId,
  clearProfitCenterData,
  setProfitCenterApiData,
} = profitCenterTabSlice.actions;

export default profitCenterTabSlice.reducer;
