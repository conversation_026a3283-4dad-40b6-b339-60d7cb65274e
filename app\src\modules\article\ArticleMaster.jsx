import React from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import InfoIcon from "@mui/icons-material/Info";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import {
  Button,
  Checkbox,
  Grid,
  Paper,
  IconButton,
  Typography,
  TextField,
  Box,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Popper,
  BottomNavigation,
  InputAdornment,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  ButtonGroup,
  ClickAwayListener,
  MenuList,
} from "@mui/material";
import moment from "moment/moment";
import { Stack } from "@mui/system";
import Select from "@mui/material/Select";
import { FormControl, MenuItem } from "@mui/material";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import ReusableDialog from "../../components/Common/ReusableDialog";
import ReusableSnackBar from "../../components/Common/ReusableSnackBar";
import CircularProgressLoader from "../../components/Common/CircularProgressLoader"
import { useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import styled from "@emotion/styled";
import {
  commonFilterClear,
  commonFilterUpdate,
} from "../../app/commonFilterSlice";
import { v4 as uuidv4 } from "uuid";
import {
  button_Marginleft,
  button_Primary,
  container_filter,
  font_Small,
  outermostContainer,
  outermostContainer_Information,
} from "../../components/Common/commonStyles";
import {
  LocalizationProvider,
} from "@mui/x-date-pickers";
import DateRange from "../../components/Common/DateRangePicker";
import { destination_ArticleMgmt } from "../../destinationVariables";
import { doAjax } from "../../components/Common/fetchService";
import ClearIcon from "@mui/icons-material/Clear";
import { setMultipleMaterial, clearArtifactId, clearAttachmentType } from "../../app/initialDataSlice";
import useLang from "@hooks/useLang";

import {
  changeTemplateDT,
  clearOrgData,
  clearTabStatus,
} from "../../app/tabsDetailsSlice";
import { setDropDown } from "@article/slice/materialDropdownSlice";
import { saveExcel } from "../../functions";
import ReusableTable from "../../components/Common/ReusableTable";
import AttachmentUploadDialog from "../../components/Common/AttachmentUploadDialog";
import CloseIcon from "@mui/icons-material/Close";
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import {
  clearPayload,
  clearRequiredFields,
  setDisplayPayload,
  clearAllExceptInitialState,
  setChangeFieldRows,
  setChangeFieldRowsDisplay,
} from "../../app/payloadslice";
import { clearPaginationData } from "@app/paginationSlice"
import ReusablePreset from "../../components/Common/ReusablePresetFilter";
import { setRequestHeader } from "../../app/requestDataSlice";
import useArticleFieldConfig from "@article/hooks/useArticleFieldConfig";
import { API_CODE, DECISION_TABLE_NAME, ERROR_MESSAGES, PAGESIZE, SEARCH_FIELD_TYPES, VISIBILITY_TYPE } from "@constant/enum";
import LargeDropdown from "@components/Common/ui/dropdown/LargeDropdown";
import MaterialDropdown from "@components/Common/ui/dropdown/MaterialDropdown";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import useGenericDtCall from "@hooks/useGenericDtCall";
import { convertKeysName, filterAndMapPlantData } from "@helper/helper";
import { colors } from "@constant/colors";
import { setTaskData } from "../../app/userManagementSlice";
import { showToast } from "../../functions";
import { ToastContainer } from "react-toastify";
import ExportExcelSearch from "../../components/RequestBench/RequestPages/ExportExcelSearch";
import { commonSearchBarClear } from "@app/commonSearchBarSlice";
import { clearMaterialFieldConfig } from "@app/tabsDetailsSlice";
import useFetchDropdownAndDispatch from "./hooks/useMaterialFetchDropdownAndDispatch";
import useLogger from "@hooks/useLogger";



const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginTop: "0px !important",
  border: `1px solid ${colors.primary.border}`,
  borderRadius: '8px',
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  '&:not(:last-child)': {
    borderBottom: 0,
  },
  '&:before': {
    display: 'none',
  },
}));

const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  minHeight: "2rem !important",
  margin: "0px !important",
  backgroundColor: colors.primary.ultraLight,
  borderRadius: '8px 8px 0 0',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: `${colors.primary.light}20`,
  },
}));

const FilterContainer = styled(Grid)({
  padding: '0.75rem',
  gap: '0.5rem',
});

const ButtonContainer = styled(Grid)({
  display: 'flex',
  justifyContent: 'flex-end',
  paddingRight: '0.75rem',
  paddingBottom: '0.75rem',
  paddingTop: '0rem',
  gap: '0.5rem',

});

const ActionButton = styled(Button)({
  borderRadius: '4px',
  padding: '4px 12px',
  textTransform: 'none',
  fontSize: '0.875rem',
});

const LabelTypography = styled(Typography)({
  fontSize: '0.75rem',
  color: colors.primary.dark,
  marginBottom: '0.25rem',
  fontWeight: 500,
});


const ArticleMaster = () => {
  const { customError } = useLogger();
  const dropDownData = useSelector((state) => state.materialDropDownData.dropDown);
  const regionBasedSalesOrgData = useSelector((state) => state.request.salesOrgDTData);
  const { fetchOrgData } = useArticleFieldConfig();
  const { getDtCall, dtData } = useGenericDtCall();
  const { getDtCall: getMasterDataColumn, dtData: masterDataDtResponse } = useGenericDtCall();
  const { getDtCall: getSearchParams, dtData: dtSearchParamsResponse } = useGenericDtCall();
  const { fetchDataAndDispatch } = useFetchDropdownAndDispatch();

  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["Article"]
  );
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };
  const appSettings = useSelector((state) => state.appSettings);

  const [isLoading, setIsLoading] = useState(true);
  const [displayFlag, setDisplayFlag] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [IsFilterDropDownLoading, setIsFilterDropDownLoading] = useState(false);
  const [clearClicked, setClearClicked] = useState(false);
  const [selectedValues, setSelectedValues] = useState({});
  const [selectedxPlant, setSelectedxPlant] = useState([]);
  const [selectedMaterialType, setSelectedMaterialType] = useState([]);
  const [matInputValue, setMatInputValue] = useState("");
  const [timerId, setTimerId] = useState(null);
  const [materialOptions, setMaterialOptions] = useState([]);
  const [selectedMaterial, setSelectedMaterial] = useState([]);
  const [selectedRegion, setSelectedRegion] = useState("");
  const [selectedMaterialGroup, setSelectedMaterialGroup] = useState([]);
  const [selectedPlant, setSelectedPlant] = useState([]);
  const [selectedDivision, setSelectedDivision] = useState([]);
  const [blurLoading, setBlurLoading] = useState(false);
  const [selectedSalesOrg, setSelectedSalesOrg] = useState([]);
  const [selectedDistributionChannel, setSelectedDistributionChannel] = useState([]);
  const [isDropDownLoading, setIsDropDownLoading] = useState(false);
  const [value, setValue] = useState(null);
  const ariaLabel = { "aria-label": "description" };
  const [rmDataRows, setRmDataRows] = useState([]);
  const [tableData, setTableData] = useState([...rmDataRows]);
  const [pageSize, setPageSize] = useState(PAGESIZE?.TOP_SKIP);
  const [UserName, setUserName] = React.useState("");
  const [openSnackBaraccept, setOpenSnackBaraccept] = useState(false);
  const [materialNumber, setMaterialNumber] = useState("");
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [plantCodeSet, setPlantCodeSet] = useState([]);
  const [salesOrg, setSalesOrg] = useState([]);
  const [distributionChannel, setDistributionChannel] = useState([]);
  const [disableButton, setDisableButton] = useState(true);
  const [selectedRow, setSelectedRow] = useState([]);
  const [selectedDetails, setSelectedDetails] = useState([]);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [displayedFields, setDisplayedFields] = useState([]);
  const [filterFieldData, setFilterFieldData] = useState({});
  const [matType, setMatType] = useState([]);
  const [matGroup, setMatGroup] = useState([]);
  const [dynamicOptions, setDynamicOptions] = useState([]);
  const [dynamicColumns, setDynamicColumns] = useState([]);
  const [searchParameters, setSearchParameters] = useState([]);
  const [alertType, setAlertType] = useState("success");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [handleMassMode, setHandleMassMode] = useState("");
  const [successMsg, setsuccessMsg] = useState(false);
  const [openButton, setOpenButton] = useState(false);
  const anchorRef = React.useRef(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [openButtonChange, setOpenButtonChange] = useState(false);
  const anchorRefChange = React.useRef(null);
  const [selectedIndexChange, setSelectedIndexChange] = useState(0);
  const options = ["Create Multiple", "Upload Template", "Download Template"];
  const optionsChange = ["Change Multiple", "Upload Template", "Download Template"];
  const [selectedMassChangeRowData, setSelectedMassChangeRowData] = useState("");
  const [openExportSearch, setOpenExportSearch] = useState(false);
  const [statusOfSelectAllFirstData, setStatusOfSelectAllFirstData] = useState(false);
  const { t } = useLang();
  const [items, setItem] = useState();

  const handleExportSearchComplete = (results) => {
    setOpenExportSearch(false);
  };

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const rmSearchForm = useSelector(
    (state) => state.commonFilter["ArticleMaster"]
  );
  const handleClose = () => {
    setOpen(false);
  };

  const handleClick = (option, index) => {
    if (index !== 0) {
      setSelectedIndex(index);
      setOpenButton(false);
      if (index === 1) {
        handleCreateMultiple()
      } else if (index === 2) {
        handleDownloadTemplate();
      }

    }
  };

  const handleCloseButton = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpenButton(false);
  };
  const uploadExcel = (file) => {
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    formData.append("dtName", "MDG_MAT_FIELD_CONFIG");
    formData.append("version", "v1");

    var uploadUrl = `/${destination_ArticleMgmt}/massAction/getAllMaterialsFromExcel`;
    const hSuccess = (data) => {
      // setIsLoading();
      dispatch(setMultipleMaterial(data?.body?.tableData));
      navigate("/masterDataCockpit/materialMaster/massMaterialTable", {
        state: handleMassMode,
      });
      if (data.statusCode === 200) {
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${file.name} has been Uploaded Succesfully`);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        setMessageDialogExtra(true);
        navigate("/masterDataCockpit/materialMaster/massMaterialTable", {
          state: handleMassMode,
        });
      }
      handleClose();
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
  };
  const handleClickChange = (option, index) => {
    if (index !== 0) {
      setSelectedIndexChange(index);
      setOpenButtonChange(false);
      if (index === 1) {
        handleChangeMultiple();
      } else if (index === 2) {
        // handleDownloadTemplate();
        handleChangeDownload();
      }
    }
  };

  const handleCloseButtonChange = (event) => {
    if (anchorRefChange.current && anchorRefChange.current.contains(event.target)) {
      return;
    }
    setOpenButtonChange(false);
  };
  const getMaterialSearch = (inputValue = "") => {
    setIsDropDownLoading(true);
    let payload = {
      materialNo: inputValue,
      salesOrg: regionBasedSalesOrgData?.uniqueSalesOrgList
        ?.map(item => item.code)
        .join("$^$") || "",
      top: 200,
      skip: 0
    };
    const hSuccess = (data) => {

      setIsDropDownLoading(false);
      setMaterialOptions(data.body);

    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_ArticleMgmt}/data/getSearchParamsMaterialNo`,
      "post",
      hSuccess,
      hError,
      payload
    );

  }
  const handleMatInputChange = (e) => {
    const inputValue = e.target.value;
    setMatInputValue(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {

      const newTimerId = setTimeout(() => {
        getMaterialSearch(inputValue);
      }, 500);


      setTimerId(newTimerId);
    }
  }
  const handleMaterialDesc = (e) => {
    if (e.target.value !== null) {
      var tempMatDesc = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        description: tempMatDesc,
      };
      dispatch(
        commonFilterUpdate({
          module: "ArticleMaster",
          filterData: tempFilterData,
        })
      );
    }
  };

  const handleCreatedBy = (e, value) => {
    if (e.target.value !== null) {
      var tempCreatedBy = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        createdBy: tempCreatedBy,
      };
      dispatch(
        commonFilterUpdate({
          module: "ArticleMaster",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleOldMatNo = (e, value) => {
    if (e.target.value !== null) {
      var tempCreatedBy = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        oldMaterialNumber: tempCreatedBy,
      };
      dispatch(
        commonFilterUpdate({
          module: "ArticleMaster",
          filterData: tempFilterData,
        })
      );
    }
  };


  const handleChangeDownload = () => {
    const materialNumbers = selectedMassChangeRowData.map((x) => {
      return x.materialNumber ?? ''
    });
    var downloadPayload = {
      materialNos: materialNumbers,
      "dtName": "MDG_MAT_FIELD_CONFIG",
      "version": "v2"
    }

    // var downloadPayload = selectedMassChangeRowData.map((x) => {
    //   return x.materialNumber??''
    // });
    let hSuccess = (response) => {
      setIsLoading(false);
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `Material_Mass Change.xls`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");

      setMessageDialogMessage(
        `Material_Mass Change.xls has been downloaded successfully`
      );

      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_ArticleMgmt}/excel/downloadExcelWithData`,
      "postandgetblob",
      hSuccess,
      hError,
      downloadPayload
    );
  };

  const handleDownloadTemplate = async () => {
    const params = new URLSearchParams({
      dtName: "MDG_MAT_FIELD_CONFIG",
      version: "v2"
    });
    let hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `${name}`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
      setMessageDialogMessage(
        `Material_Mass Create.xls has been downloaded successfully`
      );
      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_ArticleMgmt}/excel/downloadExcel?${params.toString()}`,
      "getblobfile",
      hSuccess,
      hError
    );
  };

  let dynamicDataApis = {
    "Basic Material": `/${destination_ArticleMgmt}/data/getBasicMatl`,
    "Product Hierarchy": `/${destination_ArticleMgmt}/data/getProdHier`,
    "Purchasing Group": `/${destination_ArticleMgmt}/data/getPurGroup`,
    "Lab/Office": `/${destination_ArticleMgmt}/data/getDsnOffice`,
    "Transportation Group": `/${destination_ArticleMgmt}/data/getTransGrp`,
    "Material Group 5": `/${destination_ArticleMgmt}/data/getMatlGrp5`,
    "Profit Center": `/${destination_ArticleMgmt}/data/getProfitCenterBasedOnPlant`,
    "MRP Controller": `/${destination_ArticleMgmt}/data/getMRPController`,
    "Warehouse No.": `/${destination_ArticleMgmt}/data/getWareHouseNo`,
    "MRP Profile": `/${destination_ArticleMgmt}/data/getMRPProfile`,
  };
  const fetchOptionsForDynamicFilter = (apiEndpoint, selectedItem) => {
    let payload = {
      plant: selectedPlant
        ?.map(item => item.code)
        .join("$^$") || "",
    };
    setIsFilterDropDownLoading(true)
    const hSuccess = (data) => {
      setIsFilterDropDownLoading(false)
      const newOptions = data.body;

      setDynamicOptions((prev) => ({ ...prev, [selectedItem]: newOptions }));

    };
    const hError = (error) => {
      setIsFilterDropDownLoading(false)
    };
    if (selectedItem === "Profit Center") { doAjax(apiEndpoint, "post", hSuccess, hError, payload); }
    else { doAjax(apiEndpoint, "get", hSuccess, hError); }
  };

  const handleSelectAllOptions = (option) => {
    if (selectedValues[option]?.length === dynamicOptions[option]?.length) {
      setSelectedValues((prev) => ({
        ...prev,
        [option]: [],
      }));
      // setSelectedPresetValues((prev) => ({
      //   ...prev,
      //   [option]: [],
      // }));
    } else {
      setSelectedValues((prev) => ({
        ...prev,
        [option]: dynamicOptions[option] ?? [],
      }));
    }
  };

  const handleSelection = (event) => {
    const selectedItems = event.target.value;
    setSelectedOptions(selectedItems);
    setDisplayedFields([]);
    selectedItems.forEach(async (selectedItem) => {
      const apiEndpoint = dynamicDataApis[selectedItem];
      fetchOptionsForDynamicFilter(apiEndpoint, selectedItem);
    });
  };

  const getMaterialType = () => {
    setIsDropDownLoading(true)
    const hSuccess = (data) => {
      setIsDropDownLoading(false)
      setMatType(data.body);
    };
    const hError = (error) => {
      setIsDropDownLoading(false)
    };
    doAjax(
      `/${destination_ArticleMgmt}/data/getMatlType`,
      "get",
      hSuccess,
      hError
    );
  };

  const getMaterialGroup = () => {
    setIsDropDownLoading(true)
    const hSuccess = (data) => {
      setMatGroup(data.body);
      setIsDropDownLoading(false)
    };
    const hError = (error) => {
      setIsDropDownLoading(false)
      customError(error);
    };
    doAjax(
      `/${destination_ArticleMgmt}/data/getMatlGroup`,
      "get",
      hSuccess,
      hError
    );
  };

  const getDistributionChannel = (value) => {
    setIsDropDownLoading(true)
    let payload = {
      salesOrg: value ? value.map((item) => item?.code)
        .join("$^$") : ""
    }

    const hSuccess = (data) => {
      setIsDropDownLoading(false)
      setDistributionChannel(data.body);
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_ArticleMgmt}/data/getDistrChan`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleCreateMultiple = () => {
    setEnableDocumentUpload(true);
    setHandleMassMode("Create");
  };
  const handleChangeMultiple = () => {
    setEnableDocumentUpload(true);
    setHandleMassMode("Change");
  };

  useEffect(() => {
    if (clearClicked) {
      getFilter();
      setClearClicked(false);
    }
  }, [clearClicked]);

  useEffect(() => {
    const apiCalls = [{ url: `/${destination_ArticleMgmt}/data/getCSalStatus`, keyName: "CSalStatus" }]
    apiCalls.forEach(({ url, keyName }) => {
      fetchDataAndDispatch(url, keyName);
    });
    dispatch(clearAllExceptInitialState())
  }, []);

  const resetRedux = () => {
    dispatch(clearPayload());
    dispatch(clearOrgData());
    dispatch(clearTabStatus());
    dispatch(clearRequiredFields());
    dispatch(clearPaginationData())
    dispatch(setChangeFieldRows([]));
    dispatch(setChangeFieldRowsDisplay({}));
    dispatch(setTaskData({}));
  };
  useEffect(() => {
    getMaterialGroup();
    getMaterialType();
    resetRedux();
    fetchOptionsForDynamicFilter([dynamicDataApis]);
    return () => {
      dispatch(commonFilterClear({
        module: "ArticleMaster",
        days: 7
      }));
    };
  }, []);

  useEffect(() => setTableData([...rmDataRows]), [rmDataRows]);

  useEffect(() => {
    var tempType = selectedMaterialType
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      type: tempType,
    };
    dispatch(
      commonFilterUpdate({
        module: "ArticleMaster",
        filterData: tempFilterData,
      })
    );
  }, [selectedMaterialType])

  useEffect(() => {
    var tempDistributionChannel = selectedDistributionChannel
      .map((item) => item?.code)
      .join("$^$");
    let tempFilterData = {
      ...rmSearchForm,
      distributionChannel: tempDistributionChannel,
    };
    dispatch(
      commonFilterUpdate({
        module: "ArticleMaster",
        filterData: tempFilterData,
      })
    );
  }, [selectedDistributionChannel])

  useEffect(() => {
    var tempxplant = selectedxPlant
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      PurStatus: tempxplant,
    };
    dispatch(
      commonFilterUpdate({
        module: "ArticleMaster",
        filterData: tempFilterData,
      })
    );
  }, [selectedxPlant])

  useEffect(() => {
    var tempMatNum = selectedMaterial
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      number: tempMatNum,
    };
    dispatch(
      commonFilterUpdate({
        module: "ArticleMaster",
        filterData: tempFilterData,
      })
    );
  }, [selectedMaterial])

  useEffect(() => {
    var tempDivision = selectedDivision
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      division: tempDivision,
    };
    dispatch(
      commonFilterUpdate({
        module: "ArticleMaster",
        filterData: tempFilterData,
      })
    );
  }, [selectedDivision])

  useEffect(() => {
    Object.keys(selectedValues).forEach((option) => {

      const tempSelected = selectedValues[option]
        ?.map((item) => item?.code)
        .join("$^$");
      let tempFilterData = {
        ...rmSearchForm,
        [option]: tempSelected,
      };

      dispatch(
        commonFilterUpdate({
          module: "ArticleMaster",
          filterData: tempFilterData,
        })
      );
    });
  }, [selectedValues]);

  useEffect(() => {
    var tempSalesOrg = selectedSalesOrg
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      salesOrg: tempSalesOrg,
    };
    dispatch(
      commonFilterUpdate({
        module: "ArticleMaster",
        filterData: tempFilterData,
      })
    );
    fetchUniquePlantData()
    getMaterialSearch();
  }, [selectedSalesOrg])

  const fetchUniquePlantData = () => {
    const uniquePlantData = filterAndMapPlantData(selectedSalesOrg, regionBasedSalesOrgData)
    setPlantCodeSet(uniquePlantData)
  }

  useEffect(() => {
    var tempGroup = selectedMaterialGroup
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      group: tempGroup,
    };
    dispatch(
      commonFilterUpdate({
        module: "ArticleMaster",
        filterData: tempFilterData,
      })
    );
  }, [selectedMaterialGroup])

  useEffect(() => {
    var tempPlant = selectedPlant
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      plant: tempPlant,
    };
    dispatch(
      commonFilterUpdate({
        module: "ArticleMaster",
        filterData: tempFilterData,
      })
    );
  }, [selectedPlant])

  const clearSearchBar = () => {
    setMaterialNumber("");
  };


  const handleSearchAction = (value) => {

    if (!value) {
      setroCount(Count);
      setPage(0);
      setTableData([...rmDataRows]);
      return;
    }
    const selected = rmDataRows.filter((row) => {
      let rowMatched = false;
      let keys = Object.keys(row);

      for (let k = 0; k < keys.length; k++) {
        rowMatched = !row[keys[k]]
          ? false
          : row?.[keys?.[k]] &&
          row?.[keys?.[k]]
            .toString()
            .toLowerCase()
            ?.indexOf(value?.toLowerCase()) != -1;

        if (rowMatched) break;
      }
      return rowMatched;
    });

    setTableData([...selected]);
    setroCount(selected?.length);
  };

  /* Setting Default Dates */
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 7);

  const [date, setDate] = useState([backDate, presentDate]);
  const [date1, setDate1] = useState([backDate, presentDate]);

  const handleDate = (e) => {
    var createdOn = e;

    dispatch(
      commonFilterUpdate({
        module: "ArticleMaster",
        filterData: {
          ...rmSearchForm,
          createdOn: createdOn,
        },
      })
    );

  };

  const handleDate1 = (e) => {
    if (e !== null) setDate1(e.reverse());
  };

  const handleSnackBarClickaccept = () => {
    setOpenSnackBaraccept(true);
  };

  const handleSnackBarCloseaccept = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }

    setOpenSnackBaraccept(false);
  };

  const handleUserName = (e) => {
    setUserName(e.target.value);
  };
  // Get Filter Data
  const getFilter = () => {

    setPage(0)
    setTableLoading(true);
    setStatusOfSelectAllFirstData(false)
    let payload = {
      fromDate:
        moment(rmSearchForm?.createdOn[0]).format("YYYYMMDD") ?? "",
      toDate:
        moment(rmSearchForm?.createdOn[1]).format("YYYYMMDD") ?? "",
      createdBy: rmSearchForm?.createdBy ?? "",
      materialDesc: rmSearchForm?.description ?? "",
      plant: rmSearchForm?.plant ?? "",
      materialGroup: rmSearchForm?.group ?? "",
      materialType: rmSearchForm?.type ?? "",
      changedBy: rmSearchForm?.changedBy ?? "",
      taskId: rmSearchForm?.taskId ?? "",
      status: rmSearchForm?.status ?? "",
      salesOrg: rmSearchForm?.salesOrg ?? "",
      division: rmSearchForm?.division ?? "",
      distributionChannel: rmSearchForm?.distributionChannel ?? "",
      storageLocation: rmSearchForm?.storageLocation ?? "",
      ProdHier: rmSearchForm?.["Product Hierarchy"] ?? "",
      BasicMatl: rmSearchForm?.["Basic Material"] ?? "",
      ProfitCtr: rmSearchForm?.["Profit Center"] ?? "",
      PurGroup: rmSearchForm?.["Purchasing Group"] ?? "",
      MatlGrp5: rmSearchForm?.["Material Group 5"] ?? "",
      MrpCtrler: rmSearchForm?.["MRP Controller"] ?? "",
      warehouseNo: rmSearchForm?.["Warehouse No"] ?? "",
      Mrpprofile: rmSearchForm?.["MRP Profile"] ?? "",
      oldMaterialNo: rmSearchForm?.oldMaterialNumber ?? "",
      number: rmSearchForm?.number ?? "",
      PurStatus: rmSearchForm?.PurStatus ?? "",
      top: pageSize,
      skip: 0,

      // labOffice: rmSearchForm?.labOffice ?? "",
      labOffice: rmSearchForm?.["Lab/Office"] ?? "",
      // transportationGroup: rmSearchForm?.transportationGroup ?? "",
      transportationGroup: rmSearchForm?.["Transportation Group"] ?? "",
      batchManagement: rmSearchForm?.batchManagement ?? "",
    };
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        var rows = [];
        for (let index = 0; index < data?.body?.length; index++) {
          var tempObj = data?.body[index];
          // if (tempObj["MaterialNo"]) {
          var tempRow = {
            id: uuidv4(),
            Number: tempObj["Number"],
            materialType:
              tempObj["Materialtype"] !== ""
                ? `${tempObj["Materialtype"]} - ${tempObj["MaterialTypeDesc"]}`
                : "Not Available",
            materialDesc:
              tempObj["MaterialDescrption"] !== ""
                ? `${tempObj["MaterialDescrption"]}`
                : "Not Available",
            materialGroup:
              tempObj["MaterialGroup"] !== ""
                ? `${tempObj["MaterialGroup"]} - ${tempObj["materialGroupDesc"]}`
                : "-",
            XplantMatStatus:
              tempObj["XplantMatStatus"] !== ""
                ? `${tempObj["XplantMatStatus"]} ${tempObj["XplantMatStatusDesc"] ? `-` + tempObj["XplantMatStatusDesc"] : ''}`
                : "-",
            Plant:
              tempObj["Plant"].length > 0
                ? `${tempObj["Plant"]}`
                : "-",
            WarehouseNo:
              tempObj["WarehouseNo"].length > 0
                ? `${tempObj["WarehouseNo"]}`
                : "-",
            createdOn: moment(tempObj.CreatedOn).format(appSettings?.dateFormat),
            changedOn: moment(tempObj.LastChange).format(appSettings?.dateFormat),
            changedBy: tempObj["ChangedBy"],
            createdBy: tempObj.CreatedBy,
            Division:
              tempObj["Division"] !== ""
                ? `${tempObj["Division"]} ${tempObj["DivisionDesc"] ? `-` + tempObj["DivisionDesc"] : ''}`
                : "Not Available",
            StorageLocation:
              tempObj["StorageLocation"].length > 0
                ? `${tempObj["StorageLocation"]} `
                : "-",
            oldMaterialNumber:
              tempObj["OldMaterialNumber"] !== ""
                ? `${tempObj["OldMaterialNumber"]} - ${tempObj["OldMaterialNumberName"]}`
                : "Not Available",
            labOffice:
              tempObj["LabOffice"] !== ""
                ? `${tempObj["LabOffice"]} - ${tempObj["LabOfficeName"]}`
                : "Not Available",
            transportationGroup:
              tempObj["TrnsportGroup"] !== ""
                ? `${tempObj["TrnsportGroup"]} - ${tempObj["TrnsportGroupName"]}`
                : "Not Available",
            SalesOrg:
              tempObj["SalesOrg"].length > 0
                ? `${tempObj["SalesOrg"]}`
                : "-",
            DistChnl:
              tempObj["DistChnl"].length > 0
                ? `${tempObj["DistChnl"]}`
                : "-",
            indSector:
              tempObj["Industrysector"] !== ""
                ? tempObj["Industrysector"]
                : "-",
            PrimaryVendor:
              tempObj["PryVendor"] !== ""
                ? tempObj["PryVendor"]
                : "-",
          };
          rows.push(tempRow);
          // }
        }
        rows.sort(
          (a, b) =>
            moment(a.createdOn, "DD MMM YYYY HH:mm") -
            moment(b.createdOn, "DD MMM YYYY HH:mm")
        );
        setRmDataRows(rows.reverse());
        setTableLoading(false);
        setroCount(data.count);
        setCount(data.count);
        dispatch(commonSearchBarClear({ module: "MaterialMgmt" }));
      } else if (data?.statusCode === API_CODE.STATUS_414) {
        showToast(data?.message, "error");
        setTableLoading(false);
      }
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_ArticleMgmt}/data/getMaterialBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };


  const getFilterBasedOnPagination = () => {
    setTableLoading(true);
    let payload = {
      fromDate:
        moment(rmSearchForm?.createdOn[0]).format("YYYYMMDD") ?? "",
      toDate:
        moment(rmSearchForm?.createdOn[1]).format("YYYYMMDD") ?? "",
      createdBy: rmSearchForm?.createdBy ?? "",
      materialDesc: rmSearchForm?.description ?? "",
      plant: rmSearchForm?.plant ?? "",
      materialGroup: rmSearchForm?.group ?? "",
      materialType: rmSearchForm?.type ?? "",
      changedBy: rmSearchForm?.changedBy ?? "",
      taskId: rmSearchForm?.taskId ?? "",
      status: rmSearchForm?.status ?? "",
      salesOrg: rmSearchForm?.salesOrg ?? "",
      division: rmSearchForm?.division ?? "",
      distributionChannel: rmSearchForm?.distributionChannel ?? "",
      storageLocation: rmSearchForm?.storageLocation ?? "",
      ProdHier: rmSearchForm?.["Product Hierarchy"] ?? "",
      BasicMatl: rmSearchForm?.["Basic Material"] ?? "",
      ProfitCtr: rmSearchForm?.["Profit Center"] ?? "",
      PurGroup: rmSearchForm?.["Purchasing Group"] ?? "",
      MatlGrp5: rmSearchForm?.["Material Group 5"] ?? "",
      MrpCtrler: rmSearchForm?.["MRP Controller"] ?? "",
      warehouseNo: rmSearchForm?.["Warehouse No"] ?? "",
      Mrpprofile: rmSearchForm?.["MRP Profile"] ?? "",
      oldMaterialNo: rmSearchForm?.oldMaterialNumber ?? "",
      number: rmSearchForm?.number ?? "",
      PurStatus: rmSearchForm?.PurStatus ?? "",
      top: pageSize,
      skip: pageSize * (page) ?? 0,
      fetchCount: false,
      // labOffice: rmSearchForm?.labOffice ?? "",
      labOffice: rmSearchForm?.["Lab/Office"] ?? "",
      // transportationGroup: rmSearchForm?.transportationGroup ?? "",
      transportationGroup: rmSearchForm?.["Transportation Group"] ?? "",
      batchManagement: rmSearchForm?.batchManagement ?? "",
    };
    const hSuccess = (data) => {
      setTableLoading(false);
      var rows = [];
      for (let index = 0; index < data?.body?.length; index++) {
        var tempObj = data?.body[index];
        // if (tempObj["MaterialNo"]) {
        var tempRow = {
          id: uuidv4(),
          Number: tempObj["Number"],
          materialType:
            tempObj["Materialtype"] !== ""
              ? `${tempObj["Materialtype"]} - ${tempObj["MaterialTypeDesc"]}`
              : "Not Available",
          materialDesc:
            tempObj["MaterialDescrption"] !== ""
              ? `${tempObj["MaterialDescrption"]}`
              : "Not Available",
          materialGroup:
            tempObj["MaterialGroup"] !== ""
              ? `${tempObj["MaterialGroup"]} - ${tempObj["materialGroupDesc"]}`
              : "-",
          XplantMatStatus:
            tempObj["XplantMatStatus"] !== ""
              ? `${tempObj["XplantMatStatus"]} ${tempObj["XplantMatStatusDesc"] ? `-` + tempObj["XplantMatStatusDesc"] : ''}`
              : "-",
          Plant:
            tempObj["Plant"].length > 0
              ? `${tempObj["Plant"]}`
              : "Not Available",
          WarehouseNo:
            tempObj["WarehouseNo"].length > 0
              ? `${tempObj["WarehouseNo"]}`
              : "-",
          createdOn: moment(tempObj.CreatedOn).format(appSettings?.dateFormat),
          changedOn: moment(tempObj.LastChange).format(appSettings?.dateFormat),
          changedBy: tempObj["ChangedBy"],
          createdBy: tempObj.CreatedBy,
          division:
            tempObj["Division"] !== ""
              ? `${tempObj["Division"]}- ${tempObj["DivisionDesc"]} `
              : "Not Available",
          storageLocation:
            tempObj["StorageLocation"].length > 0
              ? `${tempObj["StorageLocation"]} `
              : "-",
          oldMaterialNumber:
            tempObj["OldMaterialNumber"] !== ""
              ? `${tempObj["OldMaterialNumber"]} - ${tempObj["OldMaterialNumberName"]}`
              : "Not Available",
          labOffice:
            tempObj["LabOffice"] !== ""
              ? `${tempObj["LabOffice"]} - ${tempObj["LabOfficeName"]}`
              : "Not Available",
          transportationGroup:
            tempObj["TrnsportGroup"] !== ""
              ? `${tempObj["TrnsportGroup"]} - ${tempObj["TrnsportGroupName"]}`
              : "Not Available",
          SalesOrg:
            tempObj["SalesOrg"].length > 0
              ? `${tempObj["SalesOrg"]}`
              : "-",
          DistChnl:
            tempObj["DistChnl"].length > 0
              ? `${tempObj["DistChnl"]}`
              : "-",
          indSector:
            tempObj["Industrysector"] !== ""
              ? tempObj["Industrysector"]
              : "-",
          PrimaryVendor:
            tempObj["PryVendor"] !== ""
              ? tempObj["PryVendor"]
              : "-",
        };
        rows.push(tempRow);
        // }
      }
      rows.sort(
        (a, b) =>
          moment(a.createdOn, "DD MMM YYYY HH:mm") -
          moment(b.createdOn, "DD MMM YYYY HH:mm")
      );
      setRmDataRows((prevRows) => [...prevRows, ...rows]);
      setTableLoading(false);
      //setroCount(rows.length);
    };
    const hError = (error) => {
      setTableLoading(false);
      customError(error);
    };
    doAjax(
      `/${destination_ArticleMgmt}/data/getMaterialBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const [poHeader, setPoHeader] = useState(null);
  const [roCount, setroCount] = useState(0);
  const [Count, setCount] = useState(0);
  const [opendialog, setOpendialog] = useState(false);
  const [openforDeletion, setOpenforDeletion] = useState(false);
  const [opendialog2, setOpendialog2] = useState(false);
  const [opendialog3, setOpendialog3] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");


  const handleDialogCloseforDeletion = () => {
    setOpenforDeletion(false);
  };

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
    setOpendialog3(false);
    setOpendialog(false);
    setOpendialog2(false);
  };

  const [anchorEl_Preset, setAnchorEl] = useState(null);
  const openAnchor = Boolean(anchorEl_Preset);
  const [isPresetActive, setIsPresetActive] = useState(false);

  const handleClear = () => {
    setSelectedPlant([])
    setSelectedOptions([])
    setSelectedMaterial([])
    setSelectedRegion("")
    setSelectedxPlant([])
    setSelectedDistributionChannel([])
    setSelectedMaterialGroup([])
    setSelectedSalesOrg([])
    setSelectedValues({})
    setSelectedMaterialType([])
    setSelectedDivision([])
    // setMaterialFilterDetails({
    //   number: null,
    //   type: null,
    //   description: "",
    //   group: null,
    //   plant: null,
    //   createdBy: "",
    //   createdOn: [null, null],
    // });
    dispatch(commonFilterClear({ module: "ArticleMaster" }));
    setFilterFieldData((prevState) => {
      const updatedState = { ...prevState };
      Object.keys(updatedState).forEach((key) => {
        updatedState[key] = { code: "", desc: "" }; // Clear specific fields
      });
      return updatedState;
    });
    setClearClicked(true)
  };
  const onRowsSelectionHandler = (ids) => {
    //Selected Columns stored here
    const selectedRowsData = ids.map((id) =>
      rmDataRows.find((row) => row.id === id)
    );
    var compCodes = selectedRowsData.map((row) => row.company);
    var companySet = new Set(compCodes);
    var vendors = selectedRowsData.map((row) => row.vendor);
    var vendorSet = new Set(vendors);
    var paymentTerms = selectedRowsData.map((row) => row.paymentTerm);
    var paymentTermsSet = new Set(paymentTerms);
    if (selectedRowsData.length > 0) {
      if (companySet.size === 1) {
        if (vendorSet.size === 1) {
          if (paymentTermsSet.size !== 1) {
            setDisableButton(true);
            setMessageDialogTitle("Error");
            setMessageDialogMessage(
              "Invoice cannot be generated for vendors with different payment terms"
            );
            setMessageDialogSeverity("danger");
            handleMessageDialogClickOpen();
          } else setDisableButton(false);
        } else {
          setDisableButton(true);
          setMessageDialogTitle("Error");
          setMessageDialogMessage(
            "Invoice cannot be generated for multiple suppliers"
          );
          setMessageDialogSeverity("danger");
          handleMessageDialogClickOpen();
        }
      } else {
        setDisableButton(true);
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          "Invoice cannot be generated for multiple companies"
        );
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
      }
    } else {
      setDisableButton(true); //Enable the Create E-Invoice button when at least one row is selected and no two companys or vendors are same
    }
    setSelectedRow(ids); //Setting the ids(PO Numbers) of selected rows
    setSelectedDetails(selectedRowsData); //Setting the entire data of a selected row
  };
  const [page, setPage] = useState(0);

  const handlePageSizeChange = (event) => {

    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);

  };
  const handlePageChange = (event, newPage) => {

    setPage(isNaN(newPage) ? 0 : newPage);

  };

  // useEffect(() => {

  //     if(page!==0){
  //     const requiredDataCount = pageSize * (page + 1);
  //     if (requiredDataCount > rmDataRows.length && rmDataRows.length % pageSize === 0) {
  //       getFilterBasedOnPagination();
  //     }
  //     }
  //   }, [page]);
  useEffect(() => {
    if (!statusOfSelectAllFirstData) {
      if (page != 0 && page * pageSize >= rmDataRows?.length) {
        getFilterBasedOnPagination();
        // setSkip((prev) => prev + 500);
      }
    }
    // if (page !== 0) {
    //   if ((parseInt(page) + 1) * parseInt(pageSize) >= parseInt(skip) + 100) {
    //     getFilterBasedOnPagination();
    //     // setSkip((prev) => prev + 500);
    //   }
    // }
  }, [page, pageSize]);

  function refreshPage() {
    getFilter();
  }

  const [open, setOpen] = useState(false);

  const createMultiValueCell = (fieldName, displayName) => ({
    field: fieldName,
    headerName: t(displayName),
    editable: false,
    flex: 1,
    renderCell: (params) => {
      const values = params.value ? params.value.split(",").map(m => m.trim()) : [];
      const displayCount = values.length - 1;

      if (values.length === 0) return "-";

      const formatText = (text) => {
        const [code, ...rest] = text.split('-');
        return (
          <>
            <strong>{code}</strong>{rest.length ? ` - ${rest.join('-')}` : ''}
          </>
        );
      };

      return (
        <Box sx={{
          display: "flex",
          alignItems: "center",
          width: "100%",
          minWidth: 0
        }}>
          <Tooltip
            title={values[0]}
            placement="top"
            arrow
          >
            <Typography
              variant="body2"
              sx={{
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                flex: 1,
                minWidth: 0,
              }}
            >
              {formatText(values[0])}
            </Typography>
          </Tooltip>
          {displayCount > 0 && (
            <Box sx={{
              display: "flex",
              alignItems: "center",
              ml: 1,
              flexShrink: 0
            }}>
              <Tooltip
                arrow
                placement="right"
                title={
                  <Box sx={{ p: 1, maxHeight: 200, overflowY: "auto" }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                      Additional {displayName}s ({displayCount})
                    </Typography>
                    {values.slice(1).map((value, idx) => (
                      <Typography key={idx} variant="body2" sx={{ mb: 0.5 }}>
                        {formatText(value)}
                      </Typography>
                    ))}
                  </Box>
                }
              >
                <Box sx={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer"
                }}>
                  <InfoIcon
                    sx={{
                      fontSize: "1rem",
                      color: "primary.main",
                      "&:hover": { color: "primary.dark" }
                    }}
                  />
                  <Typography
                    variant="caption"
                    sx={{
                      ml: 0.5,
                      color: "primary.main",
                      fontSize: "11px"
                    }}
                  >
                    +{displayCount}
                  </Typography>
                </Box>
              </Tooltip>
            </Box>
          )}
        </Box>
      );
    }
  });
  const displayCell = () => (
    {
      field: "dataValidation",
      headerName: t("Data Validation"),
      editable: false,
      flex: 1,
      renderCell: (params) => {
        // Check if percentage is provided in the data, otherwise use sequence
        const providedPercentage = params.value; // Assuming the percentage comes from the data

        let percentage;
        if (providedPercentage !== undefined && providedPercentage !== null) {
          // Use the provided percentage
          percentage = providedPercentage;
        } else {
          // Fallback to repeating sequence: 100, 75, 50, 25
          const percentages = [100, 75, 50, 25];
          percentage = percentages[params.api.getRowIndexRelativeToVisibleRows(params.id) % 4];
        }

        return <CircularProgressLoader percentage={percentage} id={params.id} />;
      }
    })
  const createSingleValueCell = (fieldName, displayName) => ({
    field: fieldName,
    headerName: t(displayName),
    editable: false,
    flex: 1,
    renderCell: (params) => {
      const [firstPart, ...rest] = params.value?.split(" - ") || [];
      return (
        <span style={{ flex: 1, wordBreak: 'break-word', whiteSpace: 'normal' }}>
          <strong>{firstPart}</strong> {rest.length ? `- ${rest.join(" - ")}` : ""}
        </span>
      );
    },
  });
  const fetchMasterDataColumns = (region) => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_COLUMN,
      version: "v1",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": region?.toUpperCase() || "US",
          "MDG_CONDITIONS.MDG_MODULE": "Article" || "",
          "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE": "Master Data",
        },
      ],
    };
    getMasterDataColumn(payload);
  };
  const fetchSearchParameterFromDt = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_PARAMETER,
      version: "v1",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": "US",
          "MDG_CONDITIONS.MDG_MODULE": "Material",
          "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE": "Master Data",
        },
      ],
    };
    getSearchParams(payload);
  }

  const createMasterDataColums = (data) => {
    const columns = [];
    let sortedData = data?.sort(
      (a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO
    ) || [];
    if (sortedData) {
      sortedData?.forEach((item) => {
        if (item?.MDG_MAT_VISIBILITY === VISIBILITY_TYPE.DISPLAY) {

          if (item?.MDG_MAT_UI_FIELD_NAME) {
            const fieldName = item.MDG_MAT_JSON_FIELD_NAME;
            const headerName = item.MDG_MAT_UI_FIELD_NAME;
            if (fieldName === "DataValidation") {
              columns.push(displayCell());
            }
            else if (item.MDG_MAT_FIELD_TYPE === "Multiple") {
              columns.push(createMultiValueCell(fieldName, headerName));
            }
            else if (item.MDG_MAT_FIELD_TYPE === "Single") {
              columns.push(createSingleValueCell(fieldName, headerName));
            }
          }
        }
      });
    }
    return columns;
  }

  useEffect(() => {
    if (masterDataDtResponse) {
      const columnsGlobal = createMasterDataColums(masterDataDtResponse?.result?.[0]?.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);
      setDynamicColumns(columnsGlobal);
    }
    if (dtSearchParamsResponse) {
      const response = dtSearchParamsResponse?.result?.[0]?.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE;
      const additionalData = response?.filter((item) => {
        return item.MDG_MAT_FILTER_TYPE === "Additional";
      }).map((item) => {
        return { title: t(item.MDG_MAT_UI_FIELD_NAME) };
      });
      setSearchParameters(response);
      setItem(additionalData);
    }
  }, [masterDataDtResponse, dtSearchParamsResponse]);

  useEffect(() => {
    getFilter();
    // functions_PresetFilter.getFilterPresets();
  }, [pageSize]);

  useEffect(() => {
    if (selectedRegion) {
      fetchOrgData(selectedRegion);
      fetchDivisionData(selectedRegion);
      // Handle both US and EUR regions
      const regionCode = selectedRegion?.code?.toUpperCase();
      if (regionCode === "US" || regionCode === "EUR") {
        // fetchMasterDataColumns(regionCode, "Material", "");
        fetchMasterDataColumns(regionCode);
      }
    }
  }, [selectedRegion]);

  useEffect(() => {
    //getFilter();
    // fetchMasterDataColumns("US", "Material", "");
    fetchMasterDataColumns("US");
    fetchSearchParameterFromDt();
    dispatch(commonFilterClear({ module: "DuplicateDesc" }));
    dispatch(clearArtifactId());
    dispatch(clearAttachmentType());
    dispatch(changeTemplateDT({}));
    return () => {
      dispatch(clearMaterialFieldConfig());
    }
    // functions_PresetFilter.getFilterPresets();
  }, []);
  // useEffect(() => {
  //   if ((rmSearchForm?.company).length) {
  //     getVendorDetails();
  //     getPlantCodeSet()
  //   }
  // }, [rmSearchForm?.company]);

  // let serviceRequestForm_Component = new createServiceRequestForm(Status_ServiceReqForm, setStatus_ServiceReqForm)
  // <-- Function for taking screenshot (Export button) -->
  let ref_elementForExport = useRef(null);
  // let exportAsPicture = () => {
  //   setTimeout(() => {
  //     captureScreenShot("Material-Single");
  //   }, 100);
  // };
  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      dynamicColumns?.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `Article Data-${moment(presentDate).format("DD-MMM-YYYY")}`,
        columns: excelColumns,
        rows: rmDataRows,
      })
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };

  const fetchDivisionData = (region) => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_REGION_DIVISION_MAPPING,
      version: "v1",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": region?.code || "",
        },
      ],
    };
    getDtCall(payload);
  }

  useEffect(() => {
    if (dtData) {
      const convertedData = convertKeysName(dtData?.result?.[0]?.MDG_MAT_REGION_DIVISION_MAPPING);
      dispatch(setDropDown({ keyName: "Division", data: convertedData }));
    }
  }, [dtData]);

  useEffect(() => {
    if (selectedRegion) {
      fetchOrgData(selectedRegion);
      fetchDivisionData(selectedRegion);
    }
  }, [selectedRegion]);

  const handleSelectAllData = () => {
    setStatusOfSelectAllFirstData(true);
    getFilterAfterSelectAllOptions();
  }
  const handleFirstPageOptions = () => {
    setStatusOfSelectAllFirstData(true)
    setPage(0)
  }

  const getFilterAfterSelectAllOptions = () => {
    setPage(0)
    setTableLoading(true);
    let payload = {
      fromDate:
        moment(rmSearchForm?.createdOn[0]).format("YYYYMMDD") ?? "",
      toDate:
        moment(rmSearchForm?.createdOn[1]).format("YYYYMMDD") ?? "",
      createdBy: rmSearchForm?.createdBy ?? "",
      materialDesc: rmSearchForm?.description ?? "",
      plant: rmSearchForm?.plant ?? "",
      materialGroup: rmSearchForm?.group ?? "",
      materialType: rmSearchForm?.type ?? "",
      changedBy: rmSearchForm?.changedBy ?? "",
      taskId: rmSearchForm?.taskId ?? "",
      status: rmSearchForm?.status ?? "",
      salesOrg: rmSearchForm?.salesOrg ?? "",
      division: rmSearchForm?.division ?? "",
      distributionChannel: rmSearchForm?.distributionChannel ?? "",
      storageLocation: rmSearchForm?.storageLocation ?? "",
      ProdHier: rmSearchForm?.["Product Hierarchy"] ?? "",
      BasicMatl: rmSearchForm?.["Basic Material"] ?? "",
      ProfitCtr: rmSearchForm?.["Profit Center"] ?? "",
      PurGroup: rmSearchForm?.["Purchasing Group"] ?? "",
      MatlGrp5: rmSearchForm?.["Material Group 5"] ?? "",
      MrpCtrler: rmSearchForm?.["MRP Controller"] ?? "",
      warehouseNo: rmSearchForm?.["Warehouse No"] ?? "",
      Mrpprofile: rmSearchForm?.["MRP Profile"] ?? "",
      oldMaterialNo: rmSearchForm?.oldMaterialNumber ?? "",
      number: rmSearchForm?.number ?? "",
      PurStatus: rmSearchForm?.PurStatus ?? "",
      top: Count,
      skip: 0,

      // labOffice: rmSearchForm?.labOffice ?? "",
      labOffice: rmSearchForm?.["Lab/Office"] ?? "",
      // transportationGroup: rmSearchForm?.transportationGroup ?? "",
      transportationGroup: rmSearchForm?.["Transportation Group"] ?? "",
      batchManagement: rmSearchForm?.batchManagement ?? "",
    };
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        var rows = [];
        for (let index = 0; index < data?.body?.length; index++) {
          var tempObj = data?.body[index];
          // if (tempObj["MaterialNo"]) {
          var tempRow = {
            id: uuidv4(),
            Number: tempObj["Number"],
            materialType:
              tempObj["Materialtype"] !== ""
                ? `${tempObj["Materialtype"]} - ${tempObj["MaterialTypeDesc"]}`
                : "Not Available",
            materialDesc:
              tempObj["MaterialDescrption"] !== ""
                ? `${tempObj["MaterialDescrption"]}`
                : "Not Available",
            materialGroup:
              tempObj["MaterialGroup"] !== ""
                ? `${tempObj["MaterialGroup"]} - ${tempObj["materialGroupDesc"]}`
                : "-",
            XplantMatStatus:
              tempObj["XplantMatStatus"] !== ""
                ? `${tempObj["XplantMatStatus"]} ${tempObj["XplantMatStatusDesc"] ? `-` + tempObj["XplantMatStatusDesc"] : ''}`
                : "-",
            Plant:
              tempObj["Plant"].length > 0
                ? `${tempObj["Plant"]}`
                : "-",
            WarehouseNo:
              tempObj["WarehouseNo"].length > 0
                ? `${tempObj["WarehouseNo"]}`
                : "-",
            createdOn: moment(tempObj.CreatedOn).format(appSettings?.dateFormat),
            changedOn: moment(tempObj.LastChange).format(appSettings?.dateFormat),
            changedBy: tempObj["ChangedBy"],
            createdBy: tempObj.CreatedBy,
            division:
              tempObj["Division"] !== ""
                ? `${tempObj["Division"]}- ${tempObj["DivisionDesc"]} `
                : "Not Available",
            StorageLocation:
              tempObj["StorageLocation"].length > 0
                ? `${tempObj["StorageLocation"]} `
                : "-",
            oldMaterialNumber:
              tempObj["OldMaterialNumber"] !== ""
                ? `${tempObj["OldMaterialNumber"]} - ${tempObj["OldMaterialNumberName"]}`
                : "Not Available",
            labOffice:
              tempObj["LabOffice"] !== ""
                ? `${tempObj["LabOffice"]} - ${tempObj["LabOfficeName"]}`
                : "Not Available",
            transportationGroup:
              tempObj["TrnsportGroup"] !== ""
                ? `${tempObj["TrnsportGroup"]} - ${tempObj["TrnsportGroupName"]}`
                : "Not Available",
            SalesOrg:
              tempObj["SalesOrg"].length > 0
                ? `${tempObj["SalesOrg"]}`
                : "-",
            DistChnl:
              tempObj["DistChnl"].length > 0
                ? `${tempObj["DistChnl"]}`
                : "-",
            indSector:
              tempObj["Industrysector"] !== ""
                ? tempObj["Industrysector"]
                : "-",
            PrimaryVendor:
              tempObj["PryVendor"] !== ""
                ? tempObj["PryVendor"]
                : "-",
          };
          rows.push(tempRow);
          // }
        }
        rows.sort(
          (a, b) =>
            moment(a.createdOn, "DD MMM YYYY HH:mm") -
            moment(b.createdOn, "DD MMM YYYY HH:mm")
        );
        setRmDataRows(rows.reverse());
        setTableLoading(false);
        setPage(Math.floor(rows?.length / pageSize));
        setroCount(data.count);
        setCount(data.count);
        dispatch(commonSearchBarClear({ module: "MaterialMgmt" }));
      } else if (data?.statusCode === API_CODE.STATUS_414) {
        showToast(data?.message, "error");
        setTableLoading(false);
      }
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_ArticleMgmt}/data/getMaterialBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  }

  return (
    <div ref={ref_elementForExport}>
      <ReusableDialog
        dialogState={openMessageDialog}
        openReusableDialog={handleMessageDialogClickOpen}
        closeReusableDialog={handleMessageDialogClose}
        dialogTitle={messageDialogTitle}
        dialogMessage={messageDialogMessage}
        handleDialogConfirm={handleMessageDialogClose}
        dialogOkText={"OK"}
        // handleExtraButton={handleMessageDialogNavigate}
        dialogSeverity={messageDialogSeverity}
      />
      <ReusableSnackBar openSnackBar={openSnackbar} alertMsg={messageDialogMessage} alertType={alertType} handleSnackBarClose={handleSnackBarClose} />
      <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1}>
          <Grid container mt={0} sx={outermostContainer_Information}>
            <Grid item md={5} >
              <Typography variant="h3">
                <strong>{t("Article Management")}</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                {t("This view displays the list of Articles")}
              </Typography>
            </Grid>

          </Grid>

          <Grid container sx={container_filter}>
            <Grid item md={12}>
              <StyledAccordion defaultExpanded={false}>
                <StyledAccordionSummary
                  expandIcon={<ExpandMoreIcon sx={{ fontSize: '1.25rem', color: colors.primary.main }} />}
                  aria-controls="panel1a-content"
                  id="panel1a-header"
                >
                  <FilterListIcon sx={{ fontSize: '1.25rem', marginRight: 1, color: colors.primary.main }} />
                  <Typography
                    sx={{
                      fontSize: '0.875rem',
                      fontWeight: 600,
                      color: colors.primary.dark,
                    }}
                  >
                    {t("Search Article")}
                  </Typography>
                </StyledAccordionSummary>
                <AccordionDetails sx={{ padding: "1rem 1rem 0.5rem" }}>
                  <FilterContainer container>
                    <Grid
                      container
                      rowSpacing={1}
                      spacing={2}
                      alignItems="center"
                      sx={{ padding: "0rem 1rem 0.5rem" }}
                    >
                      {searchParameters?.filter(item => item.MDG_MAT_VISIBILITY !== "Hidden")
                        .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
                        .map((item, index) => {
                          return (
                            <React.Fragment key={index}>
                              {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.REGION &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {t(item?.MDG_MAT_UI_FIELD_NAME)} <span style={{ color: colors?.error?.dark }}>*</span>
                                  </LabelTypography>
                                  <FormControl size="small" fullWidth>
                                    <SingleSelectDropdown
                                      options={[{ code: "US", desc: "USA" }, { code: "EUR", desc: "Europe" }]}
                                      value={selectedRegion}
                                      onChange={(newValue) => {
                                        setSelectedRegion(newValue)
                                        setSelectedSalesOrg([]);
                                        setSelectedDistributionChannel([]);
                                        setSelectedPlant([]);
                                        setSelectedMaterial([]);
                                      }
                                      }
                                      placeholder={t(item?.MDG_MAT_UI_FIELD_NAME)}
                                      disabled={false}
                                      minWidth="90%"
                                      listWidth={210}
                                    />
                                  </FormControl>
                                </Grid>}
                              {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.SALESORG &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}<span style={{ color: colors?.error?.dark }}>*</span></LabelTypography>
                                  <LargeDropdown
                                    matGroup={selectedRegion ? regionBasedSalesOrgData?.uniqueSalesOrgList : [] || []}
                                    selectedMaterialGroup={selectedSalesOrg}
                                    setSelectedMaterialGroup={(value) => {
                                      setSelectedSalesOrg(value);
                                      setSelectedDistributionChannel([]);
                                      if (value.length === 0) {
                                        setDistributionChannel([]);
                                        setSelectedDistributionChannel([]);
                                      } else {
                                        getDistributionChannel(value);
                                      }
                                    }}
                                    placeholder={t(item?.MDG_MAT_UI_FIELD_NAME)}
                                  />
                                </Grid>}
                              {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.PLANT &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}<span style={{ color: colors?.error?.dark }}>*</span></LabelTypography>
                                  <LargeDropdown
                                    matGroup={selectedSalesOrg?.length ? plantCodeSet : []}
                                    selectedMaterialGroup={selectedPlant}
                                    setSelectedMaterialGroup={setSelectedPlant}
                                    placeholder={t(item?.MDG_MAT_UI_FIELD_NAME)}
                                  />
                                </Grid>}
                              {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.NUMBER &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                  </LabelTypography>
                                  <FormControl size="small" fullWidth>
                                    <MaterialDropdown
                                      matGroup={materialOptions}
                                      selectedMaterialGroup={selectedMaterial}
                                      setSelectedMaterialGroup={setSelectedMaterial}
                                      isDropDownLoading={isDropDownLoading}
                                      placeholder={t(item?.MDG_MAT_UI_FIELD_NAME)}
                                      onInputChange={handleMatInputChange}
                                      minCharacters={4}
                                    />
                                  </FormControl>
                                </Grid>}
                              {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.MATERIALTYPE &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                                  <LargeDropdown
                                    matGroup={matType}
                                    selectedMaterialGroup={selectedMaterialType}
                                    setSelectedMaterialGroup={setSelectedMaterialType}
                                    placeholder={t(item?.MDG_MAT_UI_FIELD_NAME)}
                                  />
                                </Grid>}
                              {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.MATERIALGROUP &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                                  <LargeDropdown
                                    matGroup={matGroup}
                                    selectedMaterialGroup={selectedMaterialGroup}
                                    setSelectedMaterialGroup={setSelectedMaterialGroup}
                                    placeholder={t(item?.MDG_MAT_UI_FIELD_NAME)}
                                  />
                                </Grid>}
                              {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.DISTRIBUTIONCHANNEL &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                                  <LargeDropdown
                                    matGroup={selectedSalesOrg?.length ? distributionChannel : []}
                                    selectedMaterialGroup={selectedDistributionChannel}
                                    setSelectedMaterialGroup={(value) => {
                                      if (!value || value.length === 0) {
                                        setSelectedDistributionChannel([]);
                                        return;
                                      }
                                      setSelectedDistributionChannel(value);
                                    }}
                                    isDropDownLoading={isDropDownLoading}
                                    placeholder={t(item?.MDG_MAT_UI_FIELD_NAME)}
                                  />
                                </Grid>}
                              {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.DIVISION &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                                  <LargeDropdown
                                    matGroup={selectedRegion ? dropDownData?.Division : [] ?? []}
                                    selectedMaterialGroup={selectedDivision}
                                    setSelectedMaterialGroup={(value) => {
                                      if (!value || value.length === 0) {
                                        setSelectedDivision([]);
                                        return;
                                      }
                                      setSelectedDivision(value);
                                    }}
                                    placeholder={t(item?.MDG_MAT_UI_FIELD_NAME)}
                                  />
                                </Grid>}
                              {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.PURSTATUS &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                                  <LargeDropdown
                                    matGroup={dropDownData?.CSalStatus ?? []}
                                    selectedMaterialGroup={selectedxPlant}
                                    setSelectedMaterialGroup={(value) => {
                                      if (!value || value.length === 0) {
                                        setSelectedxPlant([]);
                                        return;
                                      }
                                      setSelectedxPlant(value);
                                    }}
                                    placeholder={t(item?.MDG_MAT_UI_FIELD_NAME)}
                                  />
                                </Grid>}
                              {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.CREATEDON &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                                  <FormControl fullWidth sx={{ padding: 0 }}>
                                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                                      <DateRange
                                        handleDate={handleDate}

                                        date={rmSearchForm?.createdOn}
                                      />
                                    </LocalizationProvider>
                                  </FormControl>
                                </Grid>}

                            </React.Fragment>

                          )

                        })
                      }
                      <Grid item md={2}>
                        <LabelTypography sx={font_Small}>{t("Add New Filters")}</LabelTypography>
                        <FormControl sx={{ width: "100%" }}>
                          <Select
                            sx={{
                              font_Small,
                              // height: "31px",
                              fontSize: "12px",
                              width: "100%",
                            }}

                            // fullWidth
                            size="small"
                            multiple
                            limitTags={2}
                            value={selectedOptions}
                            onChange={handleSelection}
                            renderValue={(selected) => selected.join(", ")}
                            MenuProps={{
                              MenuProps,
                            }}
                            endAdornment={
                              selectedOptions.length > 0 && (
                                <InputAdornment position="end" sx={{ marginRight: '10px' }}>
                                  <IconButton
                                    size="small"
                                    onClick={() => setSelectedOptions([])}
                                    aria-label="Clear selections"
                                  >
                                    <ClearIcon />
                                  </IconButton>
                                </InputAdornment>
                              )
                            }
                          >
                            {items?.map((option) => (
                              <MenuItem key={option.title} value={option.title}>
                                <Checkbox
                                  checked={
                                    selectedOptions.indexOf(option.title) > -1
                                  }
                                />
                                {option.title}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>

                      </Grid>
                    </Grid>
                    <Grid
                      container
                      sx={{
                        flexDirection: "row",
                        padding: "0rem 1rem 0.5rem",
                      }}
                      gap={1}
                    >
                      {selectedOptions.map((option, i) => {
                        if (option === "Old Article Number") {
                          return (
                            <>
                              <Grid item md={2} key={i}>
                                <LabelTypography sx={font_Small}>
                                  {t(option)}
                                </LabelTypography>
                                <FormControl size="small" fullWidth>
                                  <TextField
                                    sx={{ fontSize: "12px !important" }}
                                    fullWidth
                                    size="small"
                                    value={rmSearchForm?.oldMaterialNumber}
                                    onChange={handleOldMatNo}
                                    placeholder={t("ENTER OLD ARTICLE NUMBER")}
                                  />
                                </FormControl>
                              </Grid>
                            </>
                          )
                        }
                        else if (option === "Article Description") {
                          return (
                            <>
                              <Grid item md={2} key={i}>
                                <LabelTypography sx={font_Small}>
                                  {t(option)}
                                </LabelTypography>
                                <FormControl size="small" fullWidth>
                                  <TextField
                                    sx={{ fontSize: "12px !important" }}
                                    fullWidth
                                    size="small"
                                    value={rmSearchForm?.materialDescription}
                                    onChange={handleMaterialDesc}
                                    placeholder={t("ENTER ARTICLE DESCRIPTION")}
                                  />
                                </FormControl>
                              </Grid>
                            </>
                          )
                        }
                        else if (option === "Created By") {
                          return (
                            <>
                              <Grid item md={2} key={i}>
                                <LabelTypography sx={font_Small}>{t("Created By")}</LabelTypography>
                                <TextField
                                  sx={{ fontSize: "12px !important" }}
                                  fullWidth
                                  size="small"
                                  value={rmSearchForm?.createdBy}
                                  onChange={handleCreatedBy}
                                  placeholder={t("ENTER CREATED BY")}
                                />
                              </Grid>
                            </>
                          )
                        }
                        else {
                          return (
                            <Grid item md={2} key={i}>
                              <LabelTypography sx={{ fontSize: "12px" }}>
                                {t(option)}
                              </LabelTypography>
                              <LargeDropdown
                                matGroup={
                                  dynamicOptions?.[option] ?? []
                                }
                                selectedMaterialGroup={
                                  selectedValues[option]?.length > 0
                                    ? selectedValues[option]
                                    : []
                                }
                                setSelectedMaterialGroup={(value) => {
                                  if (!value || value.length === 0) {
                                    setSelectedValues((prev) => ({
                                      ...prev,
                                      [option]: [],
                                    }));
                                    return;
                                  }

                                  if (value.length > 0 && value[value.length - 1]?.code === "Select All") {
                                    handleSelectAllOptions(option);
                                  } else {
                                    setSelectedValues((prev) => ({
                                      ...prev,
                                      [option]: value,
                                    }));
                                  }
                                }}

                              // placeholder={`SELECT ${option?.toUpperCase()}`}
                              />

                            </Grid>
                          );
                        }


                      })}

                    </Grid>
                  </FilterContainer>
                  <ButtonContainer>
                    <ActionButton
                      variant="outlined"
                      size="small"
                      startIcon={<ClearIcon sx={{ fontSize: '1rem' }} />}
                      onClick={() => {
                        handleClear();
                      }}
                      disabled={isPresetActive}
                      sx={{ borderColor: colors.primary.main, color: colors.primary.main }}>
                      {t("Clear")}
                    </ActionButton>


                    {/* MIGHT UNCOMMENT LATER */}
                    <Grid sx={{ ...button_Marginleft }}>
                      <ReusablePreset
                        moduleName={"ArticleMaster"}
                        handleSearch={getFilter}
                        disabled={selectedRegion === "" || !selectedSalesOrg?.length || !selectedPlant?.length}
                        onPresetActiveChange={(isActive) => setIsPresetActive(isActive)}
                        onClearPreset={handleClear}
                      />
                    </Grid>

                    <ActionButton
                      variant="contained"
                      size="small"
                      startIcon={<SearchIcon sx={{ fontSize: '1rem' }} />}
                      sx={{ ...button_Primary, ...button_Marginleft }}
                      disabled={isPresetActive}
                      onClick={() => {
                        const missingFields = [];

                        if (selectedRegion == "") missingFields.push("Region");
                        if (!selectedSalesOrg?.length) missingFields.push("SalesOrg");
                        if (!selectedPlant?.length) missingFields.push("Plant");

                        if (missingFields.length > 0) {
                          setMessageDialogMessage(ERROR_MESSAGES.MANDATORY_FILTER_MD(missingFields.join(", ")));
                          setAlertType("error");
                          handleSnackBarOpen();
                          return;
                        }

                        getFilter();
                      }}
                    >
                      {t("Search")}
                    </ActionButton>
                  </ButtonContainer>
                </AccordionDetails>
              </StyledAccordion>
            </Grid>
          </Grid>

          <Grid item sx={{ position: "relative" }}>
            <Stack>
              <ReusableTable
                isLoading={tableLoading}
                paginationLoading={tableLoading}
                module={"ArticleMgmt"}
                width="100%"
                title={t("List of Articles") + " (" + roCount + ")"}
                rows={tableData ?? []}
                columns={dynamicColumns ?? []}
                showSearch={true}
                showRefresh={true}
                showSelectedCount={true}
                showExport={true}
                onSearch={(value) => handleSearchAction(value)}
                onRefresh={refreshPage}
                pageSize={pageSize}
                page={page}
                onPageSizeChange={handlePageSizeChange}
                rowCount={roCount ?? rmDataRows?.length ?? 0}
                onPageChange={handlePageChange}
                getRowIdValue={"id"}
                hideFooter={true}
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                tempheight={'calc(100vh - 320px)'}
                onRowsSelectionHandler={onRowsSelectionHandler}
                callback_onRowSingleClick={(params) => {
                  const articleNumber = params.row.Number; // Adjust this based on your data structure
                  const matlType = params?.row?.materialType?.split(" - ")[0];
                  setDisplayFlag(true);

                  navigate(
                    `/masterDataCockpit/articleMaster/DisplayArticleSAPView/${articleNumber}`,
                    {
                      state: params.row,
                    }
                  );
                }}
                // setShowWork={setShowWork}
                showCustomNavigation={true}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
                showFirstPageoptions={true}
                showSelectAllOptions={true}
                onSelectAllOptions={handleSelectAllData}
                onSelectFirstPageOptions={handleFirstPageOptions}
              />
              {/* {viewDetailpage && <SingleMaterialDetail />} */}
            </Stack>
          </Grid>

          {iwaAccessData?.length > 0 && (
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
                value={value}
                onChange={(newValue) => {
                  setValue(newValue);
                }}
              >
                {/* <Button
        size="small"
        variant="contained"
        onClick={handleDialogOpenforDeletion}
      >
        Flag For Deletion
      </Button> */}

                <Dialog
                  open={openforDeletion}
                  onClose={handleDialogCloseforDeletion}
                  sx={{
                    "&::webkit-scrollbar": {
                      width: "1px",
                    },
                  }}
                >
                  <DialogTitle>
                    <Typography variant="h6">Inputs</Typography>
                    <IconButton onClick={handleDialogCloseforDeletion}>
                      <CloseIcon />
                    </IconButton>
                  </DialogTitle>
                  <DialogContent dividers>
                    <Grid container spacing={1}>
                      {/* Your form fields here */}
                    </Grid>
                  </DialogContent>
                  <DialogActions>
                    <Button onClick={handleDialogCloseforDeletion}>
                      Cancel
                    </Button>
                    <Button variant="contained">Proceed</Button>
                  </DialogActions>
                </Dialog>
                <Button
                  size="small"
                  variant="contained"
                  onClick={() => {
                    navigate("/requestBench/createArticle");
                    dispatch(setDisplayPayload({}))
                    dispatch(setRequestHeader({}))
                  }}
                >
                  {t("Create Request")}
                </Button>
                {/* <Button
                  size="small"
                  variant="contained"
                  onClick={() => {
                    handleFinanceRequest();
                  }}
                >
                  {t("Finance Request")}
                </Button> */}
                <Button
                  size="small"
                  variant="contained"
                  onClick={() => {
                    setOpenExportSearch(true);
                  }}
                >
                  {t("SAP Data Export")}
                </Button>

                <Popper
                  sx={{
                    zIndex: 1,
                  }}
                  open={openButton}
                  anchorEl={anchorRef.current}
                  placement={"top-end"}
                >
                  <Paper style={{ width: anchorRef.current?.clientWidth }}>
                    <ClickAwayListener onClickAway={handleCloseButton}>
                      <MenuList id="split-button-menu" autoFocusItem>
                        {options.slice(1).map((option, index) => (
                          <MenuItem
                            key={option}
                            selected={index === selectedIndex - 1}
                            onClick={() => handleClick(option, index + 1)}
                          >
                            {option}
                          </MenuItem>
                        ))}
                      </MenuList>
                    </ClickAwayListener>
                  </Paper>
                </Popper>
                <Popper
                  sx={{
                    zIndex: 1,
                  }}
                  open={openButtonChange}
                  anchorEl={anchorRefChange.current}
                  placement={"top-end"}
                >
                  <Paper style={{ width: anchorRefChange.current?.clientWidth }}>
                    <ClickAwayListener onClickAway={handleCloseButtonChange}>
                      <MenuList id="split-button-menu" autoFocusItem>
                        {optionsChange.slice(1).map((option, index) => (
                          <MenuItem
                            key={option}
                            selected={index === selectedIndexChange - 1}
                            onClick={() => handleClickChange(option, index + 1)}
                          >
                            {option}
                          </MenuItem>
                        ))}
                      </MenuList>
                    </ClickAwayListener>
                  </Paper>
                </Popper>
                {enableDocumentUpload && (
                  <AttachmentUploadDialog
                    artifactId=""
                    artifactName=""
                    setOpen={setEnableDocumentUpload}
                    handleUpload={uploadExcel}
                  />
                )}

              </BottomNavigation>

            </Paper>
          )}
        </Stack>
      </div>
      <ExportExcelSearch
        openSearch={openExportSearch}
        setOpenSearch={setOpenExportSearch}
        onSearchComplete={handleExportSearchComplete}
      />
      <ReusableBackDrop
        blurLoading={blurLoading}
      />
      <ToastContainer />
    </div>
  );
};

export default ArticleMaster;
