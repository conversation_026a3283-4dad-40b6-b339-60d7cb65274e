import * as React from "react";
import {
  DataGrid,
  GridToolbarColumnsButton,
  GridToolbarContainer,
  GridToolbarExport,
} from "@mui/x-data-grid";
import { LinearProgress, Typography } from "@mui/material";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

import { useDispatch } from "react-redux";

import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";

function CustomToolbar() {
  return (
    <GridToolbarContainer sx={{ display: "flex", justifyContent: "right" }}>
      {/* <GridToolbarColumnsButton/> */}
      <GridToolbarExport />
    </GridToolbarContainer>
  );
}

const StyledGridOverlay = styled("div")(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  height: "100%",
}));

function CustomNoRowsOverlay() {
  return (
    <StyledGridOverlay>
      <Box sx={{ mt: 1 }}>No Data Available</Box>
    </StyledGridOverlay>
  );
}

export default function ReusableTable({
  onCellEditCommit,
  field_name,
  url_onRowClick,
  stopPropagation_Column,
  redirecOnDoubleClick = null,
  status_onRowDoubleClick=false,
  status_onRowSingleClick=false,
  title,
  getRowIdValue,
  getRowHeight,
  rows,
  columns,
  hideFooter,
  checkboxSelection,
  disableSelectionOnClick,
  onRowsSelectionHandler=()=>{},
  showConfig,
  setShowWork,
  fieldName_onCellClick,
  onCellKeyDown=()=>{},
  onEditCellPropsChange=()=>{},
  experimentalFeatures,
  isRowSelectable,
  module,
  isLoading,
  rowsPerPageOptions,
  noOfColumns,
  callback_onRowDoubleClick=null,
  callback_onRowSingleClick=null,
  sortModel,
  onSortModelChange,
  selectedRows,
  columnVisibility=true,
}) {
  let dispatch = useDispatch();
  const [pageSize, setPageSize] = useState(noOfColumns??10);
  let navigate = useNavigate();

  return (
    <div className="reusable-table">
  
      <Typography variant="h6" sx={{marginBottom: ".5rem"}}>
      {title}
      </Typography>
     
      <DataGrid
      onCellKeyDown={onCellKeyDown}
      disableColumnSelector={columnVisibility}
        autoHeight
        
        loading={isLoading}
        getRowId={(param) => (getRowIdValue ? param[getRowIdValue] : "id")}
        rows={rows}
        columns={columns}
        pageSize={pageSize}
        onPageSizeChange={(newPageSize) => setPageSize(newPageSize)}
        rowsPerPageOptions={rowsPerPageOptions??[10, 25, 50]}
        disableExtendRowFullWidth={false}
        hideFooter={hideFooter}
        sortModel={sortModel}
        onSortModelChange={onSortModelChange}
        checkboxSelection={checkboxSelection}
        disableSelectionOnClick={disableSelectionOnClick}
        experimentalFeatures={experimentalFeatures}
        getRowClassName={(params) =>
          `custom-row--${field_name}-${params.row[field_name]}`
        }
        getRowHeight={getRowHeight}
        onRowDoubleClick={
          !callback_onRowDoubleClick && status_onRowDoubleClick //if there is no callback func for onrow double click and status is true
            ? (params, event) => {//default redirection function 
                event.stopPropagation();
                if (redirecOnDoubleClick) {
                  redirecOnDoubleClick();
                }
                // dispatch(setHistoryPath({url: window.location.pathname, module: module }))
                if(params.row.id){
                  navigate(url_onRowClick + `${params.row.id}`);
                }else{
                  navigate(url_onRowClick + `${params.id}`);
                }
                setShowWork && setShowWork(true);
              }
            : callback_onRowDoubleClick? (params) =>callback_onRowDoubleClick(params): null //executes a custom callback if there else 
        }
        onRowClick={status_onRowSingleClick ? (params,event)=>{
          callback_onRowSingleClick(params)
        }:null}
        onCellClick={(param, event) => {
          // console.log([...stopPropagation_Column], param.field)
          if (typeof stopPropagation_Column !== "object") {
            if (stopPropagation_Column === param.field) event.stopPropagation();
          } else {
            if (stopPropagation_Column.includes(param.field))
              event.stopPropagation();
          }
        }}
        onCellEditCommit={onCellEditCommit}
        onEditCellPropsChange={(params, event) =>
          onEditCellPropsChange(params, event)
        }
        onSelectionModelChange={(ids) => onRowsSelectionHandler(ids)}
        sx={{
          "& .MuiDataGrid-row:hover": {
            backgroundColor: "#EAE9FF40",
            cursor: status_onRowSingleClick ? 'pointer' :'default'
          },
          backgroundColor: "#fff",
        }}
        components={{
          LoadingOverlay: LinearProgress,
          NoRowsOverlay: CustomNoRowsOverlay,
        }}
        isRowSelectable={isRowSelectable}
        selectionModel={selectedRows}
      />
    </div>
  );
}
