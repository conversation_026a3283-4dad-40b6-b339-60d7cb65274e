import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';

const useLang = () => {
  const apiTranslations = useSelector(
    (state) => state.applicationConfig?.langTranslation
  );

  const { t: tI18n } = useTranslation();

    const t = (label) => {
    if (Array.isArray(apiTranslations)) {
      const match = apiTranslations.find(
        (item) =>
          item?.MDG_LABEL_NAME?.toLowerCase?.().trim() === label?.toLowerCase?.().trim()
      );
      if (match?.MDG_UI_FIELD_NAME) {
        return match.MDG_UI_FIELD_NAME;
      }
    }

    return tI18n(label);
  };

  return { t };
};

export default useLang;
