import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Typo<PERSON> } from "@mui/material";
import React from "react";
import RectangleIcon from "@mui/icons-material/Rectangle";
import { useNavigate } from "react-router-dom";
// import MaterialDetails from "../PurchaseOrder/Common/MaterialDetails";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { destination_Po } from "../../destinationVariables";
import { setHistoryPath, setRedirectionFilter } from "../../app/utilitySlice";
import { commonFilterUpdate } from "../../app/commonFilterSlice";
import { doAjax } from "../common/fetchService.jsx";

const controlLegend = (id, type) => {
  if (type == "po") {
    return `/purchaseOrder/management/singlePurchaseOrder/${id}`;
  } else if (type == "inv") {
    return `/invoices/invoiceTracker/singleInvoice/${id}`;
  } else if (type == "popie") {
    return `/purchaseOrder`;
  } else if (type == "invpie") {
    return `/invoices/invoiceTracker`;
  } else if (type == "dpr") {
    return `/purchaseOrder/DPR`;
  } else if (type == "sr") {
    return `/serviceRequest`;
  }
};
const LegendBox = ({
  setpoStatus,
  content,
  margin,
  type,
  itemCount = 4,
  mat = false,
  setdashboardSearchForm,
  company,
  vendor,
  setinvstatus,
  date
}) => {
  const dispatch = useDispatch();
  const [matAnchorEl, setMatAnchorEl] = useState(null);
  
  const dashboardSearchForm = useSelector(
    (state) => state.commonFilter["Dashboard"]
  );
  const invTrackerSearchForm = useSelector(
    (state) => state.commonFilter["InvoiceTracker"]
  );
  const matOpen = Boolean(matAnchorEl);

  const popperId = matOpen ? "simple-popper" : undefined;
  const [materialDetails, setMaterialDetails] = useState(null);

  const handleMatCodeClick = (event) => {
    if (materialDetails !== null) setMaterialDetails(null);
    else fetchMaterialDetails(event.target.innerText);

    setMatAnchorEl(matAnchorEl ? null : event.currentTarget);
  };
  const fetchMaterialDetails = (code) => {
    let hSuccess=(data) => {
      if (data.response != "null") setMaterialDetails(data.response[0]);
    };
    let hError=()=>{}
    doAjax(`/${destination_Po}/Odata/materialCode/code/${code}`,'get',hSuccess,hError)
     
  
  };
  const boxColors = ["#ff5555", "#a7eff3", "#fbb708", "#b1aade", "#59e0e9","#6F4840 "];
  const navigate = useNavigate();
  return (
    <>
      <Grid container ml={margin}>
        <Popover
          id={popperId}
          open={matOpen}
          onClose={handleMatCodeClick}
          placement={"right-end"}
          anchorEl={matAnchorEl}
        >
          {/* <MaterialDetails
            handleMatCodeClick={handleMatCodeClick}
            materialDetails={materialDetails}
          /> */}
        </Popover>
        {/* {content?.slice(1)?.map((item) => ( */}
          {content?.map((item) => (
          <Grid item xs={itemCount}>
            {!mat && (
              <Typography
                fontSize="9px"
                sx={{ cursor: "pointer" }}
                onClick={() => {
                  dispatch({
                    type: "REDIRECTED_FROM_FUNC",
                    payload: "dashboard",
                  });
                  dispatch(setHistoryPath({url:window.location.pathname,module:"dashboard"}));

                  dispatch({ type: "REDIRECTED_FROM", payload: "dashboard" });
                  // if (type == "invpie") {
                  //   setinvstatus(item?.invstatus);
                  // }
                 
                  switch(type){
                    case 'dpr':
                      dispatch(setRedirectionFilter({ targetModule: 'DPR' }))

                      dispatch(commonFilterUpdate({
                        filterData:{
                        company:company,
                        supplier:vendor,
                        dprStatus:item?.dprStat,
                       
                        date:dashboardSearchForm?.dashboardDate
                      },
                       module:'DPR'
                      }))

                   
                        break;
                    case 'sr':
                      dispatch(setRedirectionFilter({ targetModule: 'SR' }))
                      
                      // dispatch(setRedirectionFilter({
                      //     filter:{
                      //     srPriority: item?.SRpriority,
                      //     srStatus: item?.SRstatus,
                      //     date:date
                      //   },
                      //    targetModule:'SR'
                      //   }));
                      dispatch(commonFilterUpdate({
                        filterData:{
                          priority:item?.SRpriority,
                       
                          statusCode:item?.SRstatus,
                       
                        date:dashboardSearchForm?.dashboardDate,
                        transactionType:[],
                        transactionId:"",
                        assignedTo:"",
                        createdBy:"",
                        requestId:""
                      },
                       module:'SR'
                      }))
                        break;
                     case 'popie':
                      dispatch(
                        commonFilterUpdate({
                          module: "PurchaseOrder",
                          filterData: {
                            companyCode: dashboardSearchForm?.companyCode,
                            vendorNo: dashboardSearchForm?.vendorNo,
                            poStatus: item?.status,
                            poDate: dashboardSearchForm?.dashboardDate,
                          },
                        })
                      );

                        dispatch(setRedirectionFilter({
                         
                          targetModule:'PurchaseOrder'
                        }));
                      break;
                      case 'invpie':
                   
                          
                          dispatch(
                            commonFilterUpdate({
                              module: "InvoiceTracker",
                              filterData: {
                                companyCode: dashboardSearchForm?.companyCode,
                                vendorNo: dashboardSearchForm?.vendorNo,
                                invoiceStatus: item?.invstatus,
                                creationDate: dashboardSearchForm?.dashboardDate,
                                clearingDate:[],
                                postingDate:[],
                                poNumber: "",
                              },
                            })
                          );
                          dispatch(setRedirectionFilter({ targetModule: "InvoiceTracker" }))
                      break;   
                  }
                  navigate(controlLegend(item?.name, type));
                 
                }}
              >
                <RectangleIcon
                  style={{ color: `${item.col}`, marginRight: "2px" }}
                  fontSize="56px"
                />{" "}
                {item?.name}
              </Typography>
            )}

            {mat && (
              <Typography fontSize="10px" sx={{ cursor: "pointer" }}>
                <RectangleIcon
                  style={{ color: `${item?.col}`, marginRight: "10px" }}
                  fontSize="16px"
                />{" "}
                <span onClick={handleMatCodeClick} aria-describedby={popperId}>
                  {item?.name}
                </span>
              </Typography>
            )}
          </Grid>
        ))}
      </Grid>
    </>
  );
};

export default LegendBox;
