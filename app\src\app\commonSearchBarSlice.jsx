import { createSlice } from "@reduxjs/toolkit";

let initialState_SearchBar = {
    MaterialMaster:{
      number:'',
    },
    DocumentManagement:{
      docName:'',
    },
    Userswb: {
      taskId:'',
    },
    IDPUsers: {

      name: ''
  
    },
    RequestHistory:{
      reqId:''
    },
  Users:{
  
  name:""
  
  }
  };
  const commonSearchBarSlice = createSlice({
    name: "commonSearchBarSlice",
    initialState: initialState_SearchBar,
    reducers: {
      commonSearchBarUpdate(state, action) {
        state[action.payload["module"]] = {...(state[action.payload["module"]]),...(action.payload["filterData"])};//Edited By: bhupendra
        return state;
      },
      commonSearchBarClear(state, action) {
        state[action.payload["module"]] = initialState_SearchBar[action.payload["module"]];
        return state;
      },
    },
  
  });
  export default commonSearchBarSlice.reducer
  export const { commonSearchBarUpdate, commonSearchBarClear } =
  commonSearchBarSlice.actions;