import React from "react";
import { <PERSON>, Card, Typo<PERSON>, Avatar, <PERSON>, Badge, <PERSON>ack, Di<PERSON>r, LinearProgress, IconButton } from "@mui/material";
import { ArrowBack as ArrowBackIcon, Info as InfoIcon } from "@mui/icons-material";
import { colors } from "@constant/colors";
import { ERROR_MESSAGES, LOADING_MESSAGE } from "@constant/enum";
import { getAvatarColor } from "@helper/helper";
import useLang from "@hooks/useLang";

const UserDetails = ({ user, rolesData, onClose }) => {
  if (!user) return null;
  const { t } = useLang();

  const styles = {
    card: {
      width: "100%",
      height: "100%",
      display: "flex",
      flexDirection: "column",
      overflow: "hidden",
      boxShadow: colors.shadow.light,
      border: `1px solid ${colors.border.light}`,
      borderRadius: 1.5,
      bgcolor: colors.background.card,
    },
    header: {
      py: 1.5,
      px: 2.5,
      borderBottom: `1px solid ${colors.border.light}`,
      display: "flex",
      alignItems: "center",
      flexShrink: 0,
      bgcolor: colors.primary.white,
    },
    backButton: {
      mr: 1.5,
      color: colors.primary.grey,
      "&:hover": {
        bgcolor: colors.hover.light,
      },
    },
    headerTitle: {
      fontWeight: 600,
      color: colors.text.primary,
    },
    profileSection: {
      px: 3,
      pt: 3,
      pb: 2.5,
      backgroundImage: `linear-gradient(to right, ${colors.primary.light}, ${colors.primary.veryLight})`,
      borderBottom: `1px solid ${colors.border.light}`,
      flexShrink: 0,
    },
    userInfo: {
      display: "flex",
      alignItems: "center",
      gap: 2.5,
    },
    badgeIndicator: {
      width: 12,
      height: 12,
      borderRadius: "50%",
      bgcolor: colors.success.vibrant,
      border: `2px solid ${colors.primary.white}`,
    },
    avatar: (color) => ({
      width: 64,
      height: 64,
      bgcolor: color,
      boxShadow: colors.shadow.light,
      color: colors.primary.white, // White text for contrast
      fontWeight: 500, // Slightly bolder for better readability
      border: `2px solid ${colors.primary.white}`,
      fontSize: "1.5rem",
    }),
    userName: {
      fontWeight: 600,
      mb: 0.75,
      lineHeight: 1.2,
      color: colors.text.primary,
    },
    userEmail: {
      color: colors.text.secondary,
      fontSize: "0.875rem",
      display: "flex",
      alignItems: "center",
      gap: 0.75,
      flexWrap: "wrap",
    },
    roleChip: (color) => ({
      ml: 0.5,
      height: 20,
      borderRadius: "4px",
      bgcolor: `${color}15`,
      color: color,
      border: `1px solid ${color}30`,
      "& .MuiChip-label": { px: 1, py: 0 },
    }),
    statsContainer: {
      mt: 2.5,
      display: "flex",
      gap: 4,
      justifyContent: "flex-start",
      pl: 1,
    },
    statBox: {
      textAlign: "center",
    },
    statValue: {
      fontWeight: 600,
      fontSize: "1.125rem",
      color: colors.primary.main,
    },
    statLabel: {
      color: colors.text.secondary,
      fontSize: "0.75rem",
    },
    tabsSection: {
      borderBottom: `1px solid ${colors.border.light}`,
      flexShrink: 0,
      bgcolor: colors.background.panel,
    },
    activeTab: {
      py: 1.25,
      px: 2,
      borderBottom: `2px solid ${colors.primary.main}`,
      color: colors.primary.main,
    },
    tabText: {
      fontWeight: 600,
    },
    contentSection: {
      px: 3,
      py: 2.5,
      overflow: "auto",
      flexGrow: 1,
      bgcolor: colors.background.default,
    },
    sectionTitle: {
      mb: 1.5,
      fontWeight: 600,
      color: colors.text.secondary,
      fontSize: "0.75rem",
      textTransform: "uppercase",
      letterSpacing: "0.05em",
    },
    roleChips: {
      direction: "row",
      spacing: 0.75,
      flexWrap: "wrap",
      useFlexGap: true,
    },
    roleChipItem: (color) => ({
      m: 0.25,
      borderRadius: "4px",
      height: 24,
      fontSize: "0.75rem",
      bgcolor: `${color}10`,
      color: color,
      border: `1px solid ${color}30`,
      transition: "all 0.2s ease",
      "&:hover": {
        bgcolor: `${color}15`,
        boxShadow: colors.shadow.light,
      },
      "& .MuiChip-label": { px: 1.5 },
    }),
    noRoles: {
      p: 2,
      textAlign: "center",
      bgcolor: colors.neutral[100],
      borderRadius: 1,
      width: "100%",
      border: `1px dashed ${colors.border.light}`,
    },
    noRolesText: {
      color: colors.text.disabled,
    },
    moduleCard: (color) => ({
      mb: 2,
      borderRadius: 1,
      border: `1px solid ${colors.border.light}`,
      overflow: "hidden",
      transition: "all 0.2s ease-in-out",
      "&:hover": {
        borderColor: color,
        boxShadow: colors.shadow.medium,
      },
    }),
    moduleHeader: (color) => ({
      px: 2,
      py: 1.25,
      bgcolor: `${color}08`,
      borderBottom: `1px solid ${color}20`,
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
    }),
    moduleName: (color) => ({
      color: color,
      fontWeight: 600,
      fontSize: "0.875rem",
    }),
    countChip: (color) => ({
      height: 20,
      fontSize: "0.7rem",
      borderRadius: "4px",
      bgcolor: `${color}15`,
      color: color,
      border: `1px solid ${color}30`,
      "& .MuiChip-label": { px: 1, py: 0 },
    }),
    featureChips: {
      p: 1.75,
    },
    featureChip: (color) => ({
      m: 0.25,
      height: 24,
      borderRadius: "4px",
      fontSize: "0.75rem",
      bgcolor: colors.neutral[100],
      color: colors.text.primary,
      border: `1px solid ${colors.border.light}`,
      transition: "all 0.2s ease",
      "&:hover": {
        bgcolor: `${color}10`,
        borderColor: `${color}30`,
        color: color,
        boxShadow: colors.shadow.light,
      },
      "& .MuiChip-label": { px: 1.5 },
    }),
    loadingContainer: {
      py: 4,
      textAlign: "center",
    },
    loadingProgress: {
      width: "60%",
      mx: "auto",
      mb: 2,
      borderRadius: 1,
      "& .MuiLinearProgress-bar": {
        bgcolor: colors.primary.main,
      },
    },
    loadingText: {
      color: colors.text.secondary,
    },
    noPermissions: {
      p: 4,
      textAlign: "center",
      bgcolor: colors.neutral[100],
      borderRadius: 1,
      border: `1px dashed ${colors.border.light}`,
    },
    infoIconContainer: {
      width: 48,
      height: 48,
      borderRadius: "50%",
      bgcolor: colors.neutral[200],
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      margin: "0 auto",
      mb: 2,
    },
    infoIcon: {
      color: colors.text.disabled,
    },
    noPermissionsText: {
      color: colors.text.secondary,
      fontWeight: 500,
    },
    divider: {
      my: 3,
      bgcolor: colors.border.light,
    },
  };

  return (
    <Card sx={styles.card}>
      {/* Header */}
      <Box sx={styles.header}>
        <IconButton size="small" onClick={onClose} sx={styles.backButton}>
          <ArrowBackIcon fontSize="small" />
        </IconButton>
        <Typography variant="subtitle1" sx={styles.headerTitle}>
          {t("User Details")}
        </Typography>
      </Box>

      {/* User profile */}
      <Box sx={styles.profileSection}>
        <Box sx={styles.userInfo}>
          <Badge overlap="circular" anchorOrigin={{ vertical: "bottom", horizontal: "right" }} badgeContent={<Box sx={styles.badgeIndicator} />}>
            <Avatar sx={styles.avatar(getAvatarColor(user.displayName || user.email))}>{(user.displayName || user.email).charAt(0).toUpperCase()}</Avatar>
          </Badge>

          <Box>
            <Typography variant="h6" sx={styles.userName}>
              {user.displayName || "N/A"}
            </Typography>
            <Typography sx={styles.userEmail}>
              {user.email}
              {user.userRoles.length > 0 && <Chip label={user.userRoles[0]} size="small" sx={styles.roleChip(getAvatarColor(user.userRoles[0]))} />}
            </Typography>
          </Box>
        </Box>

        <Box sx={styles.statsContainer}>
          <Box sx={styles.statBox}>
            <Typography variant="h6" sx={styles.statValue}>
              {user.userRoles.length}
            </Typography>
            <Typography variant="caption" sx={styles.statLabel}>
              {t("Roles")}
            </Typography>
          </Box>
          <Box sx={styles.statBox}>
            <Typography variant="h6" sx={styles.statValue}>
              {user.userGroups.length}
            </Typography>
            <Typography variant="caption" sx={styles.statLabel}>
              {t("Groups")}
            </Typography>
          </Box>
          <Box sx={styles.statBox}>
            <Typography variant="h6" sx={styles.statValue}>
              {rolesData ? Object.keys(rolesData.entitiesAndActivities[0] || {}).length : "-"}
            </Typography>
            <Typography variant="caption" sx={styles.statLabel}>
              {t("Modules")}
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Tabs navigation */}
      <Box sx={styles.tabsSection}>
        <Box sx={{ display: "flex", px: 1.5 }}>
          <Box sx={styles.activeTab}>
            <Typography variant="body2" sx={styles.tabText}>
              {t("Permissions")}
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Content - Scrollable */}
      <Box sx={styles.contentSection}>
        {/* Roles */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={styles.sectionTitle}>
            {t("Assigned Roles")}
          </Typography>

          <Stack direction="row" spacing={0.75} flexWrap="wrap" useFlexGap>
            {user.userRoles.length ? (
              user.userRoles.map((role) => {
                const roleColor = getAvatarColor(role);
                return <Chip key={role} label={role} sx={styles.roleChipItem(roleColor)} />;
              })
            ) : (
              <Box sx={styles.noRoles}>
                <Typography variant="body2" sx={styles.noRolesText}>
                  {t("No roles assigned")}
                </Typography>
              </Box>
            )}
          </Stack>
        </Box>

        <Divider sx={styles.divider} />

        {/* Modules */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" sx={styles.sectionTitle}>
            {t("Modules & Features")}
          </Typography>
        </Box>

        {!rolesData ? (
          <Box sx={styles.loadingContainer}>
            <Box sx={styles.loadingProgress}>
              <LinearProgress color="inherit" />
            </Box>
            <Typography variant="body2" sx={styles.loadingText}>
              {t(LOADING_MESSAGE.LOADING_PERM)}
            </Typography>
          </Box>
        ) : rolesData.entitiesAndActivities.length === 0 ? (
          <Box sx={styles.noPermissions}>
            <Box sx={styles.infoIconContainer}>
              <InfoIcon sx={styles.infoIcon} />
            </Box>
            <Typography variant="body2" sx={styles.noPermissionsText}>
              {t(ERROR_MESSAGES.ERROR_PERM)}
            </Typography>
          </Box>
        ) : (
          <Box>
            {/* Modules with their features */}
            {rolesData.entitiesAndActivities.map((module, index) =>
              Object.keys(module).map((mod) => {
                const moduleColor = getAvatarColor(mod);
                return (
                  <Card key={`${mod}-${index}`} variant="outlined" sx={styles.moduleCard(moduleColor)}>
                    {/* Module header */}
                    <Box sx={styles.moduleHeader(moduleColor)}>
                      <Typography sx={styles.moduleName(moduleColor)}>{mod}</Typography>

                      <Chip label={`${module[mod].length} features`} size="small" sx={styles.countChip(moduleColor)} />
                    </Box>

                    {/* Feature chips */}
                    <Box sx={styles.featureChips}>
                      <Stack direction="row" spacing={0.5} flexWrap="wrap" useFlexGap>
                        {module[mod].map((feature, idx) => (
                          <Chip key={idx} label={feature} size="small" sx={styles.featureChip(moduleColor)} />
                        ))}
                      </Stack>
                    </Box>
                  </Card>
                );
              })
            )}
          </Box>
        )}
      </Box>
    </Card>
  );
};

export default UserDetails;
