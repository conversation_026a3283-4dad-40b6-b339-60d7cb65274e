import React, { useState, useEffect } from "react";
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography, Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useSelector } from "react-redux";
import { ERROR_MESSAGES, LOADING_MESSAGE } from "@constant/enum";
import { colors } from "@constant/colors";

const TaxDataSAP = ({ materialID }) => {
  const uniqueTaxData = useSelector((state) => state.payload[materialID]?.payloadData?.TaxData?.TaxData?.UniqueTaxDataSet);
  const [message, setMessage] = useState(LOADING_MESSAGE?.TAXDATA_LOADING);

  useEffect(() => {
    let timeoutId;
    if (uniqueTaxData?.length === 0) {
      timeoutId = setTimeout(() => {
        setMessage(ERROR_MESSAGES.NO_DATA_AVAILABLE);
      }, 500);
    }
    
    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [uniqueTaxData]);

  if (!uniqueTaxData || uniqueTaxData.length === 0) {
    return <Typography sx={{ textAlign: "center", marginTop: "10px" }}>{message}</Typography>;
  }

  return (
    <Accordion
      sx={{
        marginBottom: "20px",
        boxShadow: 3,
        borderRadius: "10px",
        borderColor: colors?.primary.white,
      }}
      key="Tax_Classification_Static"
      defaultExpanded
    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        sx={{
          backgroundColor: colors.primary.whiteSmoke,
          borderRadius: "10px",
          padding: "8px 16px",
          "&:hover": { backgroundColor: colors.hover.hoverbg },
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: "bold" }}>
          Tax Classification
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        <TableContainer component={Paper} sx={{ maxWidth: "100%" }}>
          <Typography variant="h6" sx={{ p: 1, fontWeight: "bold", textAlign: "center" }}>
            Tax Data
          </Typography>
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: colors.primary.whiteSmoke }}>
                <TableCell sx={{ fontWeight: "bold" }}>Country</TableCell>
                <TableCell sx={{ fontWeight: "bold" }}>Tax Type</TableCell>
                <TableCell sx={{ fontWeight: "bold" }}>Tax Class</TableCell>
                <TableCell sx={{ fontWeight: "bold" }}>Description</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {uniqueTaxData.map(({ Country, TaxType, SelectedTaxClass }, index) => (
                <TableRow key={`${Country}-${TaxType}-${index}`}>
                  <TableCell sx={{ fontWeight: "bold"}}>{Country}</TableCell>
                  <TableCell sx={{ fontWeight: "bold" }}>{TaxType}</TableCell>
                  <TableCell>{SelectedTaxClass?.TaxClass || "N/A"}</TableCell>
                  <TableCell>{SelectedTaxClass?.TaxClassDesc || "N/A"}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </AccordionDetails>
    </Accordion>
  );
};

export default TaxDataSAP;
