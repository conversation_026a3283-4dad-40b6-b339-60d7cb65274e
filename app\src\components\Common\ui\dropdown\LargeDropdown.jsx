import { FixedSizeList as List } from "react-window";
import { useState, useRef, useCallback, useEffect, useMemo } from "react";
import { Box, Paper, Typography, IconButton, TextField, Checkbox, Chip, Popover, FormGroup, FormControlLabel, CircularProgress } from "@mui/material";
import ClearIcon from '@mui/icons-material/Clear';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';

const LargeDropdown = ({ 
  matGroup, 
  selectedMaterialGroup, 
  setSelectedMaterialGroup,
  isDropDownLoading,
  placeholder = "Select Option",
  onInputChange,
  minCharacters = 0
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
  const [popoverContent, setPopoverContent] = useState("");
  const [isPopoverVisible, setIsPopoverVisible] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const listRef = useRef(null);
  const containerRef = useRef(null);

  const filteredOptions = useMemo(() => {
    const options = Array.isArray(matGroup) 
      ? matGroup.map(item => ({
          value: item.code,
          label: item.desc || item.code
        })) 
      : [];

    if (!inputValue) return options;

    return options.filter(option => 
      option.value.toLowerCase().includes(inputValue.toLowerCase()) ||
      option.label.toLowerCase().includes(inputValue.toLowerCase())
    );
  }, [matGroup, inputValue]);

  const handleInputChange = (e) => {
    const value = e.target.value;
    setInputValue(value);
    if (onInputChange) {
      onInputChange(e);
    }
  };

  const handleSelect = useCallback((option) => {
    const isSelected = selectedMaterialGroup.some(item => item.code === option.value);
    
    if (isSelected) {
      setSelectedMaterialGroup(selectedMaterialGroup.filter(item => item.code !== option.value));
    } else {
      setSelectedMaterialGroup([...selectedMaterialGroup, { code: option.value, desc: option.label }]);
    }
  }, [selectedMaterialGroup, setSelectedMaterialGroup]);

  const handleSelectAll = useCallback(() => {
    if (selectedMaterialGroup?.length === matGroup.length) {
      setSelectedMaterialGroup([]);
    } else {
      setSelectedMaterialGroup(matGroup.map(item => ({ code: item.code, desc: item.desc })));
    }
  }, [matGroup, selectedMaterialGroup?.length, setSelectedMaterialGroup]);

  const handleClearAll = useCallback(() => {
    setSelectedMaterialGroup([]);
  }, [setSelectedMaterialGroup]);

  const handlePopoverOpen = (event, content) => {
    setPopoverAnchorEl(event.currentTarget);
    setPopoverContent(content);
    setIsPopoverVisible(true);
  };

  const handlePopoverClose = () => {
    setIsPopoverVisible(false);
  };

  const handleMouseEnterPopover = () => {
    setIsPopoverVisible(true);
  };

  const handleMouseLeavePopover = () => {
    setIsPopoverVisible(false);
  };

  const Row = useCallback(({ index, style }) => {
    if (index === 0) {
      return (
        <Box
          component="div"
          sx={{
            ...style,
            padding: "4px 8px",
            cursor: "pointer",
            "&:hover": {
              backgroundColor: "action.hover",
            },
          }}
          onClick={handleSelectAll}
        >
          <FormGroup sx={{ width: '100%', py: 0.5 }}>
            <FormControlLabel
              sx={{
                margin: 0,
                '& .MuiFormControlLabel-label': {
                  flex: 1
                }
              }}
              control={
                <Checkbox
                  size="small"
                  checked={selectedMaterialGroup?.length === matGroup.length}
                  indeterminate={selectedMaterialGroup?.length > 0 && selectedMaterialGroup?.length < matGroup?.length}
                  sx={{ py: 0.5 }}
                />
              }
              label={
                <Typography sx={{ fontSize: 13 }}>Select All</Typography>
              }
            />
          </FormGroup>
        </Box>
      );
    }

    const option = filteredOptions[index - 1];
    const isSelected = selectedMaterialGroup.some(item => item.code === option.value);
    
    return (
      <Box
        component="div"
        sx={{
          ...style,
          padding: "4px 8px",
          cursor: "pointer",
          "&:hover": {
            backgroundColor: "action.hover",
          },
        }}
        onClick={() => handleSelect(option)}
      >
        <FormGroup sx={{ width: '100%', py: 0.5 }}>
          <FormControlLabel
            sx={{
              margin: 0,
              '& .MuiFormControlLabel-label': {
                flex: 1
              }
            }}
            control={
              <Checkbox 
                size="small" 
                checked={isSelected}
                sx={{ py: 0.5 }}
              />
            }
            label={
              <Typography sx={{ fontSize: 13 }}>
                <strong>{option.value}</strong> - {option.label}
              </Typography>
            }
          />
        </FormGroup>
      </Box>
    );
  }, [filteredOptions, selectedMaterialGroup, handleSelect, handleSelectAll, matGroup?.length]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const renderSelectedValues = () => {
    if (selectedMaterialGroup?.length === 0) return null;
    
    return selectedMaterialGroup?.length > 1 ? (
      <>
        <Chip
          sx={{
            height: 25,
            fontSize: "0.85rem",
            '.MuiChip-label': { padding: "0 6px" }
          }}
          label={selectedMaterialGroup?.[0]?.code}
        />
        <Chip
          sx={{
            height: 25,
            fontSize: "0.85rem",
            '.MuiChip-label': { padding: "0 6px" },
            ml: 0.5
          }}
          label={`+${selectedMaterialGroup?.length - 1}`}
          onMouseEnter={(event) => {
            const selectedOptionsText = selectedMaterialGroup
              .slice(1)
              .map(option => `<strong>${option.code}</strong> - ${option.desc}`)
              .join("<br />");
            handlePopoverOpen(event, selectedOptionsText);
          }}
          onMouseLeave={handlePopoverClose}
        />
      </>
    ) : (
      <Chip
        sx={{
          height: 25,
          fontSize: "0.85rem",
          '.MuiChip-label': { padding: "0 6px" }
        }}
        label={selectedMaterialGroup?.[0]?.code}
      />
    );
  };

  return (
    <Box ref={containerRef} sx={{ position: "relative", width: "100%" }}>
      <TextField
        fullWidth
        size="small"
        placeholder={placeholder?.toUpperCase()}
        value={inputValue}
        onChange={handleInputChange}
        onFocus={() => setIsOpen(true)}
        InputProps={{
          startAdornment: renderSelectedValues(),
          endAdornment: (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {selectedMaterialGroup?.length > 0 && (
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleClearAll();
                    setInputValue("");
                  }}
                  sx={{ 
                    padding: '4px',
                    mr: 0.5,
                    '&:hover': {
                      backgroundColor: 'rgba(0, 0, 0, 0.04)'
                    }
                  }}
                >
                  <ClearIcon sx={{ fontSize: '16px' }} />
                </IconButton>
              )}
              {isDropDownLoading ? (
                <CircularProgress size={20} sx={{ mr: 1 }} />
              ) : (
                <IconButton 
                  size="small" 
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsOpen(!isOpen);
                  }}
                  sx={{ padding: '4px' }}
                >
                  {isOpen ? (
                    <ArrowDropUpIcon sx={{ fontSize: '20px' }} />
                  ) : (
                    <ArrowDropDownIcon sx={{ fontSize: '20px' }} />
                  )}
                </IconButton>
              )}
            </Box>
          ),
        }}
      />

      {isOpen && (
        <Paper
          sx={{
            position: "absolute",
            top: "100%",
            left: 0,
            right: 0,
            mt: 1,
            maxHeight: 300,
            zIndex: 1000,
          }}
        >
          {filteredOptions.length === 0 ? (
            <Box sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                {inputValue.length < minCharacters 
                  ? `Please enter at least ${minCharacters} characters` 
                  : 'No options found'}
              </Typography>
            </Box>
          ) : (
            <List
              height={Math.min(filteredOptions.length * 45 + 45, 300)}
              itemCount={filteredOptions.length + 1}
              itemSize={45}
              width="100%"
              ref={listRef}
            >
              {Row}
            </List>
          )}
        </Paper>
      )}

      <Popover
        open={isPopoverVisible}
        anchorEl={popoverAnchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        onMouseEnter={handleMouseEnterPopover}
        onMouseLeave={handleMouseLeavePopover}
        PaperProps={{
          sx: {
            p: 1,
            maxWidth: 300
          }
        }}
      >
        <Typography 
          variant="body2" 
          dangerouslySetInnerHTML={{ __html: popoverContent }}
        />
      </Popover>
    </Box>
  );
};

export default LargeDropdown;
