import React from "react";
import { Route } from "react-router-dom";
const ApplicationConfiguration = React.lazy(() => import("../../components/ConfigCockpit/ApplicationConfiguration/ApplicationConfigurations"));
const SLAManagement = React.lazy(() => import("../../components/ConfigCockpit/SLAManagement/SLAManagement"));
const EmailConfiguration = React.lazy(() => import("../../components/ConfigCockpit/EmailManagement/EmailConfiguration"));
const UserManagement = React.lazy(() => import("../../components/ConfigCockpit/UserManagementModule/UserManagement"));
const Authoring = React.lazy(() => import("../../components/BusinessRules/Authoring/Authoring"));
const Modelling = React.lazy(() => import("../../components/BusinessRules/Modelling/Modelling"));
const IwaUsersSummary = React.lazy(() => import("../../components/ConfigCockpit/IwaUserManagement/Users/<USER>"));
const IwaCreateUsers = React.lazy(() => import("../../components/ConfigCockpit/IwaUserManagement/Users/<USER>"));
const IwaEditUser = React.lazy(() => import("../../components/ConfigCockpit/IwaUserManagement/Users/<USER>"));
const IwaViewUsers = React.lazy(() => import("../../components/ConfigCockpit/IwaUserManagement/Users/<USER>"));
const IwaQuickCreateUser = React.lazy(() => import("../../components/ConfigCockpit/IwaUserManagement/Users/<USER>"));
const IwaRolesSummary = React.lazy(() => import("../../components/ConfigCockpit/IwaUserManagement/Roles/IwaRolesSummary"));
const IwaCreateRole = React.lazy(() => import("../../components/ConfigCockpit/IwaUserManagement/Roles/IwaCreateRole"));
const IwaViewAndEditRole = React.lazy(() => import("../../components/ConfigCockpit/IwaUserManagement/Roles/IwaViewAndEditRole"));
const IwaGroupsSummary = React.lazy(() => import("../../components/ConfigCockpit/IwaUserManagement/Groups/GroupSummary"));
const CreateGroup = React.lazy(() => import("../../components/ConfigCockpit/IwaUserManagement/Groups/CreateGroup"));
const EditGroup = React.lazy(() => import("../../components/ConfigCockpit/IwaUserManagement/Groups/EditGroup"));
const ViewGroup = React.lazy(() => import("../../components/ConfigCockpit/IwaUserManagement/Groups/ViewGroup"))
export const ConfigCockpitRoutes = [
  <Route path="/configCockpit/applicationConfiguration" element={<ApplicationConfiguration />} />,
  <Route path="/configCockpit/SLAManagement" element={<SLAManagement />} />,
  <Route path="/configCockpit/emailTemplateConfig" element={<EmailConfiguration />} />,
  <Route path="/configCockpit/userManagement" element={<UserManagement />} />,
  <Route path="/configCockpit/businessRules/authoring" element={<Authoring />} />,
  <Route path="/configCockpit/businessRules/modelling" element={<Modelling />} />,
  <Route path="/configCockpit/userManagement/UsersSummary" element={<IwaUsersSummary/>} />,
  <Route path="/configCockpit/userManagement/CreateUsers" element={<IwaCreateUsers/>} />,
  <Route path="/configCockpit/userManagement/EditUser" element={<IwaEditUser/>} />,
  <Route path="/configCockpit/userManagement/ViewUser" element={<IwaViewUsers/>} />,
  <Route path="/configCockpit/userManagement/QuickCreateUser" element={<IwaQuickCreateUser/>} />,
  <Route path="/configCockpit/userManagement/RolesSummary" element={<IwaRolesSummary/>} />,
  <Route path="/configCockpit/userManagement/CreateRole" element={<IwaCreateRole/>} />,
  <Route path="/configCockpit/userManagement/ViewAndEditRole" element={<IwaViewAndEditRole/>} />,
  <Route path="/configCockpit/userManagement/GroupsSummary" element={<IwaGroupsSummary/>} />,
  <Route path="/configCockpit/userManagement/CreateGroup" element={<CreateGroup/>} />,
  <Route path="/configCockpit/userManagement/EditGroup" element={<EditGroup/>} />,
  <Route path="/configCockpit/userManagement/ViewGroup" element={<ViewGroup/>} />,
];