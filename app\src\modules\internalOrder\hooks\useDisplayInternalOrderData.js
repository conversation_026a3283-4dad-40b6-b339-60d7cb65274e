import { useState, useCallback } from "react";
import { useDispatch } from "react-redux";
import { ERROR_MESSAGES, API_CODE, LOCAL_STORAGE_KEYS, REQUEST_TYPE } from "@constant/enum";
import { doAjax } from "@components/Common/fetchService";
import { destination_InternalOrder } from "../../../destinationVariables";
import useLogger from "@hooks/useLogger";
import { transformInternalOrderDisplayResponseToRedux } from "../../../functions";
import { setIOpayloadData, setSavedReqData } from "../slice/InternalOrderSlice";
import { getLocalStorage } from "@helper/helper";
import { END_POINTS } from "@constant/apiEndPoints";
import Store from "@app/store";
const useDisplayInternalOrderData = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const dispatch = useDispatch();
  const { customError } = useLogger();

  const getDisplayInternalOrderData = useCallback(
    (requestId, RequestType, reqBench, taskData, rowData) => {
      setLoading(true);
      setError(null);

      const savedTask = getLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK, true, {});
      const effectiveRequestType = RequestType || taskData?.ATTRIBUTE_2 || savedTask?.ATTRIBUTE_2;
      const isBifurcated = rowData?.isBifurcated;

      const payload = reqBench
        ? {
            parentRequestId: !isBifurcated ? requestId : "",
            childRequestId: isBifurcated ? requestId : "",
            page: "0",
            size: "10",
          }
        : {
            parentRequestId: "",
            childRequestId: requestId || "",
            page: "0",
            size: "10",
          };

      doAjax(
        `/${destination_InternalOrder}${END_POINTS.DISPLAY_INTERNAL_ORDER.DISPLAY_DTO}`,
        "post",
        (data) => {
          setLoading(false);

          if (data?.statusCode !== API_CODE.STATUS_200) {
            setError(data?.message || ERROR_MESSAGES.ERROR_GET_DISPLAY_DATA);
            return;
          }

          const { payload: transformed } = transformInternalOrderDisplayResponseToRedux(data);

          dispatch(setIOpayloadData({ keyName: "requestHeaderData", data: transformed.requestHeaderData }));
          dispatch(setIOpayloadData({ keyName: "rowsBodyData", data: transformed.rowsBodyData }));
          dispatch(setIOpayloadData({ keyName: "childRequestHeaderData", data: transformed.childRequestHeaderData }));
          dispatch(setIOpayloadData({ keyName: "changeLogData", data: transformed.changeLogData }));
          dispatch(setIOpayloadData({ keyName: "ToInternalOrderErrorData", data: transformed.ToInternalOrderErrorData }));
        },
        (error) => {
          setLoading(false);
          setError(error);
          customError(ERROR_MESSAGES.ERROR_FETCHING_DATA);
        },
        payload
      );
    },
    [dispatch, customError]
  );

  return {
    getDisplayInternalOrderData,
    loading,
    error,
    clearError: () => setError(null),
  };
};

export default useDisplayInternalOrderData;

