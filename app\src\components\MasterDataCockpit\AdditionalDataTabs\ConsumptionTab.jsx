import { Grid, Stack, TextField, Typography } from "@mui/material";
import React, { useState } from "react";
import { container_Padding } from "../../Common/commonStyles";
import ReusableTable from "../../common/ReusableTable";

const ConsumptionTab = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [rmDataRows, setRmDataRows] = useState([]);

  const columns = [
    {
      field: "period",
      headerName: "Period",
      editable: true,
      width: 200,
    },
    {
      field: "unplannedConsumption",
      headerName: "Unplanned Consumption",
      editable: true,
      width: 200,
    },
    {
      field: "correctedValue",
      headerName: "Corrected Value",
      editable: true,
      width: 200,
    },
    {
      field: "qutnt",
      headerName: "Qutnt",
      editable: true,
      width: 200,
    },
  ];

  const allColumns = [...columns];
  return (
    <div>
      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          mt: 0.25,
          ...container_Padding,
          // ...container_columnGap,
        }}
      >
        <Grid container display="block">
          <Grid item sx={{ paddingTop: "2px !important", mb: 1 }}>
            <Stack flexDirection="row" alignItems="center">
              <div style={{ width: "5%" }}>
                <Typography variant="body2" color="#777">
                  Material
                </Typography>
              </div>
              <Typography variant="body2" fontWeight="bold">
                :
              </Typography>
              <TextField size="small" sx={{ ml: 2 }} />
            </Stack>
          </Grid>
          <Grid item sx={{ paddingTop: "2px !important", mb: 1 }}>
            <Stack flexDirection="row" alignItems="center">
              <div style={{ width: "5%" }}>
                <Typography variant="body2" color="#777">
                  Descr
                </Typography>
              </div>
              <Typography variant="body2" fontWeight="bold">
                :
              </Typography>
              <TextField size="small" sx={{ ml: 2 }} />
            </Stack>
          </Grid>
          <Grid item sx={{ paddingTop: "2px !important", mb: 1 }}>
            <Stack flexDirection="row" alignItems="center">
              <div style={{ width: "5%" }}>
                <Typography variant="body2" color="#777">
                  Plant
                </Typography>
              </div>
              <Typography variant="body2" fontWeight="bold">
                :
              </Typography>

              <TextField size="small" sx={{ ml: 2 }} />
              <Typography variant="body2" color="#777" sx={{ ml: 2 }}>
                Industry X Template Plant 100X
              </Typography>
            </Stack>
          </Grid>
        </Grid>

        <Grid container display="flex" alignItems="center" gap={3}>
        <Grid item sx={{ paddingTop: "2px !important", mb: 1 }}>
            <Stack flexDirection="row" alignItems="center">
              {/* <div style={{  }}> */}
                <Typography variant="body2" color="#777">
                  Base Unit of Measure
                </Typography>
              {/* </div> */}
              <Typography variant="body2" fontWeight="bold">
                :
              </Typography>
              <TextField size="small" sx={{ ml: 2 }} />
            </Stack>
          </Grid>
          <Grid item sx={{ paddingTop: "2px !important", mb: 1 }}>
            <Stack flexDirection="row" alignItems="center">
              {/* <div style={{  }}> */}
                <Typography variant="body2" color="#777">
                  Period Indicator
                </Typography>
              {/* </div> */}
              <Typography variant="body2" fontWeight="bold">
                :
              </Typography>
              <TextField size="small" sx={{ ml: 2 }} />
            </Stack>
          </Grid>
          <Grid item sx={{ paddingTop: "2px !important", mb: 1 }}>
            <Stack flexDirection="row" alignItems="center">
              {/* <div style={{}}> */}
                <Typography variant="body2" color="#777">
                  Fiscal Year Variant
                </Typography>
              {/* </div> */}
              <Typography variant="body2" fontWeight="bold">
                :
              </Typography>
              <TextField size="small" sx={{ ml: 2 }} />
            </Stack>
          </Grid>
        </Grid>

        <Grid item sx={{ display: "flex", alignItems: "center", mt: 2 }}>
          <ReusableTable
            // module={"MaterialMaster"}
            width="100%"
            title={"Consumption Values"}
            rows={rmDataRows}
            columns={allColumns}
            getRowIdValue={"id"}
            hideFooter={true}
            checkboxSelection={false}
            disableSelectionOnClick={true}
            status_onRowSingleClick={true}
            // onRowsSelectionHandler={onRowsSelectionHandler}
            // callback_onRowSingleClick={(params) => {
            //   const materialNumber = params.row.materialNumber; // Adjust this based on your data structure
            //   navigate(
            //     `/masterDataCockpit/materialMaster/displayMaterialDetail/${materialNumber}`
            //   );
            // }}
            // setShowWork={setShowWork}
            //   stopPropagation_Column={"action"}
            status_onRowDoubleClick={true}
          />
        </Grid>
      </Grid>
    </div>
  );
};

export default ConsumptionTab;
