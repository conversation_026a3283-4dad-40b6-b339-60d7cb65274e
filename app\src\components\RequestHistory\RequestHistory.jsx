import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Box, Button, FormControl, Grid, IconButton, Stack, Tooltip, Typography, Dialog, DialogTitle, DialogContent, Modal,} from "@mui/material";
import { outerContainer_Information, outermostContainer_Information, outermostContainer, iconButton_SpacingSmall } from "../common/commonStyles";
import { VisibilityOutlined} from "@mui/icons-material";
import AttachFileOutlinedIcon from "@mui/icons-material/AttachFileOutlined";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import CloseIcon from "@mui/icons-material/Close";
import ReusableTable from "../Common/ReusableTable";
import moment from "moment";
import { doAjax } from "../Common/fetchService";
import { useSelector } from "react-redux";
import { destination_DocumentManagement, destination_MaterialMgmt } from "../../destinationVariables";
import { idGenerator } from "../../functions";
import { MatDownload } from "../DocumentManagement/UtilDoc";
import EmailIcon from "@mui/icons-material/Email";
import ReusableDialog from "../Common/ReusableDialog";
import { LOCAL_STORAGE_KEYS } from "../../constant/enum";
import { colors } from "../../constant/colors";
import { END_POINTS } from "@constant/apiEndPoints";
import { clearLocalStorageItem, getLocalStorage } from "@helper/helper";
import ActivityTimeline from "@components/Common/ui/ActivityTimeline";
import RequestHistorySkeleton from "@components/Common/ui/ReqestSkeleton";
import { filterNavigation } from "../../helper/helper";

const RequestHistory = () => {
  const [loader, setloader] = useState(true);
  const location = useLocation();
  const parentRequestid = location.state.requestId;
  const module =  location.state.module;
  const [tempData, settempData] = useState([]);
  const [ccNumber, setCcNumber] = useState("");
  const [activityLogData, setactivityLogData] = useState([]);
  const [open, setOpen] = useState(false);
  const [isMailModalOpen,setIsMailModalOpen] = useState(false)
  const [item, setItem] = useState("");
  const [attachments, setAttachments] = useState([]);
  const [isActive, setIsActive] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
      const { destination } = filterNavigation(module);
  const handleOpen = () => {
    setOpenCorrectionDialog(true);
    setIsActive(true);
  };
  const handleClose = () => {
    setOpen(false);
  };

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
    navigate(-1);
  };
  const style = {
    overflow: "scroll",
    position: "absolute",
    top: "50%",
    left: "52%",
    transform: "translate(-50%, -50%)",
    width: "70%",
    height: "70%",
    bgcolor: colors?.primary?.white,
    boxShadow: 4,
    p: 1,
  };
  useEffect(() => {
    setCcNumber(idGenerator("CC"));
  }, []);
  
  
  const requestBenchTaskData = getLocalStorage(LOCAL_STORAGE_KEYS.REQUEST_BENCH_TASK, true, {});
  const requestIDWithPrefix = `${requestBenchTaskData?.childRequestIds}`
  const RequestID = useSelector((state) => state.commonSearchBar?.RequestHistory?.reqId) || requestIDWithPrefix;

  const navigate = useNavigate();
  const handleFlow = () => {
    setloader(false);
    setactivityLogData([]);
    const hSuccess = (data) => {
      settempData(data?.body);
      setloader(false);
    };
    const hError = () => {
      setloader(false);
    };
    doAjax(`/${destination}/${END_POINTS.TASK_ACTION_DETAIL.FETCH_REQUEST_HISTORY}?requestId=${RequestID}`, "get", hSuccess, hError);
  };

  const columns = [
    { field: "id", headerName: "ID", flex: 1, hide: true },
    {
      field: "createdAt",
      headerName: "Notification Date",
      flex: 0.5,
      renderCell: (params) => <Typography sx={{ fontSize: "12px" }}>{moment(params.row.createdAt).format("DD MMM YYYY")}</Typography>,
    },
    { field: "subject", headerName: "Subject", flex: 2 },
    {
      field: "actions",
      align: "center",
      flex: 1,
      headerAlign: "center",
      headerName: "Actions",
      sortable: false,
      renderCell: (params) => (
        <div>
          <Tooltip title="View Mail Body">
            <IconButton
              onClick={() => {
                setItem(mails.find((item) => item.id == params.row.id));
              }}
            >
              <VisibilityOutlined />
            </IconButton>
          </Tooltip>
        </div>
      ),
    },
  ];
  const onClose = () => {
    setOpen(false);
    setIsMailModalOpen(false);
  };
  const attachmentColumns = [
    {
      field: "id",
      headerName: "Document ID",
      flex: 1,
      hide: true,
    },
    {
      field: "docName",
      headerName: "Document Name",
      flex: 1,
    },
    {
      field: "uploadedOn",
      headerName: "Uploaded On",
      flex: 1,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "uploadedBy",
      headerName: "Uploaded By",
      sortable: false,
      flex: 1,
    },
    {
      field: "attachmentType",
      headerName: "Attachment Type",
      sortable: false,
      flex: 1,
    },
    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      width: 75,
      renderCell: (cellValues) => {
        return (
          <>
            <MatDownload index={cellValues.row.id} name={cellValues.row.docName} />
          </>
        );
      },
    },
  ];

  const getAttachments = () => {
    let hSuccess = (data) => {
      var attachmentRows = [];
      data.documentDetailDtoList.forEach((doc) => {
        var tempRow = {
          id: doc.documentId,
          docType: doc.fileType,
          docName: doc.fileName,
          uploadedOn: doc.docCreationDate,
          uploadedBy: doc.createdBy,
          attachmentType: doc.attachmentType,
        };
        attachmentRows.push(tempRow);
      });
      setAttachments(attachmentRows);
    };
    doAjax(`/${destination_DocumentManagement}/${END_POINTS.TASK_ACTION_DETAIL.GET_CHILD_DOCS}/${RequestID}`, "get", hSuccess);
  };

  const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);

  const handleRemarksDialogClose = () => {
    setOpenCorrectionDialog(false);
    setIsActive(false);
  };

  const [mails, setmails] = useState([]);
  const getMail = (id) => {
    setIsMailModalOpen(true)
    let hSuccess = (data) => {
      setmails(data?.body);
    };
    let hError = () => {};
    let url = `/${destination}/${END_POINTS.TASK_ACTION_DETAIL.FETCH_MAILS}?requestId=${RequestID}`;

    
    if (url) {
      doAjax(url, "get", hSuccess, hError);
    }
  };
  useEffect(() => {
    handleFlow();
    getAttachments();
    return () => {
      clearLocalStorageItem(LOCAL_STORAGE_KEYS.REQUEST_BENCH_TASK);
    };
  }, []);

  return (
    <div id={"container_outermost"}>
      <Dialog
        fullWidth
        hideBackdrop={false}
        elevation={2}
        PaperProps={{
          sx: { boxShadow: "none" },
        }}
        open={openCorrectionDialog}
        sx={{
          "& .MuiDialog-container": {
            "& .MuiPaper-root": {
              width: "100%",
              maxWidth: "max-content",
            },
          },
        }}
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: colors?.background?.header,
            display: "flex",
          }}
        >
          <Typography variant="h6">Attachments: </Typography>

          <IconButton sx={{ width: "max-content" }} onClick={handleRemarksDialogClose} children={<CloseIcon />} />
        </DialogTitle>

        <DialogContent sx={{ padding: ".5rem 1rem" }}>
          <Stack>
            <Box sx={{ minWidth: 800 }}>
              <FormControl sx={{ height: "auto" }} fullWidth>
                {Boolean(attachments?.length) && <ReusableTable width="800px" rows={attachments} columns={attachmentColumns} hideFooter={false} getRowIdValue={"id"} disableSelectionOnClick={true} stopPropagation_Column={"action"} />}
                {!Boolean(attachments?.length) && <Typography variant="body2">No Attachments Found</Typography>}
              </FormControl>
            </Box>
          </Stack>
        </DialogContent>
      </Dialog>

      <Dialog
        open={open}
        onClose={handleClose}
        hideBackdrop={false}
        elevation={2}
        PaperProps={{
          sx: { boxShadow: "none" },
        }}
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: colors?.background?.header,
            display: "flex",
          }}
        >
          {" "}
          <Typography variant="h6">Attachments</Typography>
          <IconButton sx={{ width: "max-content" }} onClick={handleClose} children={<CloseIcon />} />
        </DialogTitle>
        <DialogContent sx={{ padding: ".5rem 1rem" }}>
          <Stack>
            <Box sx={{ minWidth: 800 }}>
              <FormControl sx={{ height: "auto" }} fullWidth>
                {Boolean(attachments.length) && <ReusableTable width="70vw" rows={attachments} columns={attachmentColumns} hideFooter={false} getRowIdValue={"id"} disableSelectionOnClick={true} stopPropagation_Column={"action"} />}
                {!Boolean(attachments.length) && <Typography variant="body2">No Attachments Found</Typography>}
              </FormControl>
            </Box>
          </Stack>
        </DialogContent>
      </Dialog>
      <div className="purchaseOrder" style={{ ...outermostContainer, backgroundColor: colors?.background?.container }}>
        <Stack spacing={1}>
          <>
            <Grid container sx={outermostContainer_Information}>
              <Grid item md={6} sx={{ outerContainer_Information, display: "flex" }}>
                <Grid>
                  <IconButton color="primary" aria-label="upload picture" component="label" sx={iconButton_SpacingSmall}>
                    <ArrowCircleLeftOutlinedIcon
                      sx={{
                        fontSize: "25px",
                        color: colors?.basic?.black,
                      }}
                      onClick={() => {
                        navigate(-1);
                      }}
                    />
                  </IconButton>
                </Grid>
                <Grid>
                  <Typography variant="h3">
                    <strong>Request History</strong>
                  </Typography>
                  <Typography variant="body2" color={colors.secondary.grey}>
                    This view displays the history of a Request
                  </Typography>
                </Grid>
              </Grid>
              <Grid item md={6} sx={{ display: "flex" }}>
                <Grid container direction="row" justifyContent="flex-end" alignItems="center" spacing={0}>
                  <Tooltip title="Mail">
                    <IconButton>
                      <EmailIcon onClick={getMail} />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Uploaded Attachments" arrow>
                    <IconButton
                      onClick={handleOpen}
                      sx={{
                        "&:active": {
                          backgroundColor: "rgba(17,52,166,0.3)",
                          color: `${colors?.basic?.black}`,
                        },
                        backgroundColor: isActive ? "rgba(17,52,166,0.3)" : "transparent",
                      }}
                    >
                      <AttachFileOutlinedIcon />
                    </IconButton>
                  </Tooltip>
                </Grid>
              </Grid>
            </Grid>

            <Grid container spacing={1}>
              <Modal open={isMailModalOpen} onClose={onClose} aria-labelledby="modal-modal-title" aria-describedby="modal-modal-description">
                <Box sx={style}>
                  {item != "" ? (
                    <>
                      {mails?.length == 0 ? (
                        <Stack justifyItems={"center"} alignItems={"center"} mt={5}>
                          <Typography variant="h3" color={colors?.basic?.black}>
                            No Mail Found for this Request ID
                          </Typography>
                          <Button
                            size="small"
                            variant="contained"
                            sx={{ marginRight: "1rem" }}
                            onClick={() => {
                              setItem("");
                            }}
                          >
                            Close
                          </Button>
                        </Stack>
                      ) : (
                        <>
                          <Grid container sx={{ height: "100%", p: 2 }}>
                            <Grid item xs={12}>
                              <Box
                                sx={{
                                  border: `1px solid ${colors.basic.black}`,
                                  borderRadius: "8px",
                                  width: "100%",
                                  height: "100%",
                                  boxSizing: "border-box",
                                  display: "flex",
                                  flexDirection: "column",
                                  backgroundColor: colors.basic.white,
                                }}
                                p={3}
                              >
                                {/* Email Header */}
                                <Typography
                                  variant="h6"
                                  sx={{
                                    fontSize: "18px",
                                    fontWeight: "600",
                                    color: colors.text.primary,
                                    mb: 2,
                                  }}
                                >
                                  {item?.subject || "No Subject"}
                                </Typography>

                                {/* Email Meta Information */}
                                <Box sx={{ mb: 2 }}>
                                  <Stack spacing={1.5}>
                                    {/* To Field */}
                                    <Stack direction="row" spacing={1} alignItems="flex-start">
                                      <Typography
                                        sx={{
                                          color: colors.text.secondary,
                                          width: "40px",
                                          flexShrink: 0,
                                          fontSize: "13px",
                                          fontWeight: "500",
                                        }}
                                      >
                                        To:
                                      </Typography>
                                      <Typography
                                        sx={{
                                          flex: 1,
                                          fontSize: "13px",
                                          color: colors.text.charcoal,
                                          wordBreak: "break-word",
                                        }}
                                      >
                                        {item?.toParticipant}
                                      </Typography>
                                    </Stack>

                                    {/* Cc Field */}
                                    <Stack direction="row" spacing={1} alignItems="flex-start">
                                      <Typography
                                        sx={{
                                          color: colors.text.secondary,
                                          width: "40px",
                                          flexShrink: 0,
                                          fontSize: "13px",
                                          fontWeight: "500",
                                        }}
                                      >
                                        Cc:
                                      </Typography>
                                      <Typography
                                        sx={{
                                          flex: 1,
                                          fontSize: "13px",
                                          color: colors.text.charcoal,
                                          wordBreak: "break-word",
                                        }}
                                      >
                                        {item?.ccParticipant}
                                      </Typography>
                                    </Stack>

                                    {/* From Field */}
                                    <Stack direction="row" spacing={1} alignItems="flex-start">
                                      <Typography
                                        sx={{
                                          color: colors.text.secondary,
                                          width: "40px",
                                          flexShrink: 0,
                                          fontSize: "13px",
                                          fontWeight: "500",
                                        }}
                                      >
                                        From:
                                      </Typography>
                                      <Typography
                                        sx={{
                                          flex: 1,
                                          fontSize: "13px",
                                          color: colors.text.charcoal,
                                          wordBreak: "break-word",
                                        }}
                                      >
                                        {item?.fromUser}
                                      </Typography>
                                    </Stack>

                                    {/* Date Field */}
                                    <Typography
                                      sx={{
                                        fontSize: "12px",
                                        color: colors.text.secondary,
                                        borderBottom: `1px solid ${colors.border.light}`,
                                        pb: 2,
                                      }}
                                    >
                                      {moment(item?.createdAt).format("DD MMM YYYY hh:mm:ss a")}
                                    </Typography>
                                  </Stack>
                                </Box>

                                {/* Email Content */}
                                <Box
                                  sx={{
                                    flexGrow: 1,
                                    overflowY: "auto",
                                    backgroundColor: colors.background.default,
                                    borderRadius: "4px",
                                    p: 2,
                                    minHeight: "200px",
                                    "& *": { fontFamily: "inherit" },
                                  }}
                                >
                                  <div
                                    dangerouslySetInnerHTML={{
                                      __html: item?.content,
                                    }}
                                  />
                                </Box>
                              </Box>
                            </Grid>

                            {/* Action Buttons */}
                            <Grid
                              item
                              xs={12}
                              sx={{
                                display: "flex",
                                justifyContent: "flex-end",
                                mt: 2,
                              }}
                            >
                              <Button
                                size="medium"
                                variant="contained"
                                onClick={() => setItem("")}
                                sx={{
                                  minWidth: "100px",
                                  textTransform: "none",
                                  boxShadow: "none",
                                  "&:hover": {
                                    boxShadow: "none",
                                  },
                                }}
                              >
                                Close
                              </Button>
                            </Grid>
                          </Grid>
                        </>
                      )}
                    </>
                  ) : (
                    <>
                      <ReusableTable rows={mails} columns={columns} pageSize={10} getRowIdValue={"id"} hideFooter={false} title={`Email List (${mails?.length})`} />
                    </>
                  )}
                </Box>
              </Modal>
                
            </Grid>
            <Typography variant="h6" sx={{ mt: 2, mb: 2 }}>
              Parent Request ID: {parentRequestid}
              </Typography>
              <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" sx={{ color: '#334155',fontWeight:'bold' }}>
                📌 Task Activity Timeline {RequestID}
              </Typography>
            </Box>
             {tempData?.length > 0 ? (
              <ActivityTimeline data={tempData} childRequestID={RequestID}/>
             ):<RequestHistorySkeleton/>} 
          </>
        </Stack>
      </div>
      <ReusableDialog
        dialogState={openMessageDialog}
        openReusableDialog={handleMessageDialogClickOpen}
        closeReusableDialog={handleMessageDialogClose}
        dialogTitle={messageDialogTitle}
        dialogMessage={messageDialogMessage}
        handleDialogConfirm={handleMessageDialogClose}
        dialogOkText={"OK"}
        // handleExtraButton={handleMessageDialogNavigate}
        dialogSeverity={messageDialogSeverity}
      />
    </div>
  );
};

export default RequestHistory;
