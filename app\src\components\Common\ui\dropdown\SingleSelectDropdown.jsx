import React from 'react';
import { <PERSON>complete, TextField, Typography, CircularProgress } from "@mui/material";
import { FixedSizeList } from 'react-window';
import { DROP_DOWN_ITEM_SIZE } from '@constant/enum';
import { colors } from '@constant/colors';

const LISTBOX_PADDING = 8; // px

const OuterElementContext = React.createContext({});

const OuterElementType = React.forwardRef((props, ref) => {
  const outerProps = React.useContext(OuterElementContext);
  return <div ref={ref} {...props} {...outerProps} />;
});

function ListboxComponent(props) {
  const { children, ...other } = props;
  const itemData = [];
  children.forEach((item) => {
    itemData.push(item);
  });

  const itemCount = itemData.length;
  const itemSize = DROP_DOWN_ITEM_SIZE.HEIGHT

  const getHeight = () => {
    if (itemCount > 8) {
      return 8 * itemSize;
    }
    return itemData.length * itemSize;
  };

  return (
    <div ref={props.ref}>
      <OuterElementContext.Provider value={other}>
        <FixedSizeList
          itemData={itemData}
          height={getHeight() + 2 * LISTBOX_PADDING}
          width="100%"
          outerElementType={OuterElementType}
          innerElementType="ul"
          itemSize={itemSize}
          overscanCount={5}
          itemCount={itemCount}
        >
          {({ data, index, style }) => {
            const dataSet = data[index];
            const inlineStyle = {
              ...style,
              top: style.top + LISTBOX_PADDING,
            };

            return React.cloneElement(dataSet, {
              style: inlineStyle,
            });
          }}
        </FixedSizeList>
      </OuterElementContext.Provider>
    </div>
  );
}



const SingleSelectDropdown = ({ 
  options = [], 
  value = null,
  onChange,
  placeholder = "SELECT OPTION",
  disabled = false,
  minWidth,
  isFieldError,
  handleInputChange,
  isLoading = false,
  isOptionDisabled = () => false,
}) => {

  // const normalizedValue = React.useMemo(() => {
  //   if (!value) return null;
  //   if (typeof value === 'string' || typeof value === 'number') {
  //     return { code: value.toString(), desc: '' };
  //   }
  //   return value;
  // }, [value]);
  const normalizedValue = React.useMemo(() => {
    if (!value) return null;
  
    if (typeof value === 'string' || typeof value === 'number') {
      return options.find(opt => opt.code === value.toString()) || null;
    }
  
    return value;
  }, [value, options]);
  
  return (
    <Autocomplete
      fullWidth
      size="small"
      options={options}
      value={normalizedValue}
      onChange={(_, newValue) => onChange(newValue)}
      getOptionDisabled={isOptionDisabled ? isOptionDisabled : () => false}
      getOptionLabel={(option) => 
        option?.desc ? `${option.code} - ${option.desc}` : option?.code || ""
      }
      isOptionEqualToValue={(option, value) => option.code === value.code}
      ListboxComponent={ListboxComponent}
      disableCloseOnSelect={false}
      renderOption={(props, option) => (
        <Typography 
          {...props} 
          component="li" 
          style={{ 
            fontSize: 12,
            padding: '8px 16px',
            width: '100%',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'start',
          }}
        >
          <span style={{ 
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis'
          }}
          title={`${option?.code}${option?.desc ? ` - ${option?.desc}` : ""}`}
          >
            <strong>{option?.code}</strong>
            {option?.desc ? ` - ${option?.desc}` : ""}
          </span>
        </Typography>
      )}
      renderInput={(params) => (
        <TextField
          {...params}
          placeholder={placeholder?.toUpperCase()}
          title={value?.code ? value?.code : value}
          fullWidth
          onChange={handleInputChange ? handleInputChange : undefined}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {isLoading ? (
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                ) : null}
                {params.InputProps.endAdornment}
              </>
            ),
            sx: {
              "& .MuiOutlinedInput-notchedOutline": {
                borderColor: isFieldError && colors?.error?.dark,
              },
            },
          }}
          sx={{ minWidth: minWidth }}
        />
      )}
      disabled={disabled}
      sx={{
        minWidth: minWidth,
        '& .MuiAutocomplete-popper': {
          width: `${minWidth}px !important`,
        },
        '& .MuiAutocomplete-option': {
          fontSize: '12px',
          color: colors.black.dark,
          '&:hover': {
            backgroundColor: colors.primary.whiteSmoke,
          }
        },
        '& .MuiAutocomplete-listbox': {
          padding: '0px !important'
        },
        '& .MuiInputBase-root.Mui-disabled': {
          '& > input': {
            WebkitTextFillColor: colors.black.dark,
            color: colors.black.dark,
          },
        }
      }}
    />
  );
};

export default SingleSelectDropdown;
