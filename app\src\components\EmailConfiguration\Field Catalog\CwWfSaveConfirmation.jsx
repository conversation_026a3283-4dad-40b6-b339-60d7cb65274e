import React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import MuiDialogContent from "@mui/material/DialogContent";
import MuiDialogActions from "@mui/material/DialogActions";
import Typography from "@mui/material/Typography";

const MessageBoxComponentSubmit = (props) => {
  return (
    <div>
      <Dialog
        onClose={() => 
          props.onClose("CANCEL")
        }
        aria-labelledby="customized-dialog-title"
        open={props.open}
      >
        <DialogTitle
          id="customized-dialog-title"
          color={"primary"}
        >
          Confirmation
        </DialogTitle>
        <MuiDialogContent dividers>
          <Typography gutterBottom sx={{ fontSize: "0.8rem" }}>
            Do you want to save the record?
          </Typography>
        </MuiDialogContent>
        <MuiDialogActions style={{ height: "2.5rem", padding: "0 0.2rem" }}>
          <Button
            key={"CANCEL"}
            sx={{ height: "1.8rem", width: "3.5rem", textTransform: "none", fontWeight: "600" }}
            variant="outlined"
            disableRipple
            autoFocus
            onClick={() => {
              props.onClose("CANCEL");
            }}
            color="primary"
            className="styledOutlinedButton styleButton"
          >
            Cancel
          </Button>
          <Button sx={{ height: "1.8rem" }} variant="contained" key={"SAVE"} disableRipple autoFocus onClick={() => props.onClose("SAVE")} color="primary" className="styleManageButton styleButton">
            OK
          </Button>
        </MuiDialogActions>
      </Dialog>
    </div>
  );
};
export default MessageBoxComponentSubmit;
