import {
    Autocomplete,
    Checkbox,
    Chip,
    FormControlLabel,
    FormGroup,
    Grid,
    <PERSON>over,
    <PERSON>ack,
    TextField,
    Typography,
    Box,
  } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState, useRef } from "react";
import { colors } from "../../../constant/colors"
import { useLocation } from "react-router-dom";
import { updateModuleFieldData } from "@app/profitCenterTabsSlice";
import { updateModuleFieldDataCC } from "@app/costCenterTabsSlice";
import { updateModuleFieldDataGL ,setSelectedOptionsForTemplate} from "@app/generalLedgerTabSlice";
import { updateModuleFieldDataIO } from "@InternalOrder/slice/internalOrderSlice";
import { DROP_DOWN_SELECT_OR_MAP, LOCAL_STORAGE_KEYS, MODULE } from "@constant/enum";
import { getLocalStorage } from "@helper/helper";

const MultiSelectTypeGlobal = (props) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const changeFields = useSelector((state) => state.tabsData.changeFieldsDT);
   const { uniqueId, field, disabled, dropDownData, handleChange, module } = props;
   const selectedModule = getLocalStorage(LOCAL_STORAGE_KEYS.MODULE,true, {})
  const selectedModuleSelector = DROP_DOWN_SELECT_OR_MAP[selectedModule] || (() => ({}));
  const globaldropDownData = useSelector(selectedModuleSelector)

  const initialData =
  module === MODULE?.CC? useSelector((state) => state.profitCenter.payload.requestHeaderData)  :
  module === MODULE?.GL ?
  useSelector((state) => state.generalLedger.payload.requestHeaderData) : useSelector((state) => state.profitCenter.payload.requestHeaderData)
  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues)
  const ccFieldNames = useSelector((state)=> state?.costCenter?.payload)
  const fieldSelectivity = changeFields?.["Field Selectivity"];
  const RequestId = queryParams.get('RequestId');
 
  const [selectedValues, setSelectedValues] = useState([]);
  const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
  const [popoverContent, setPopoverContent] = useState("");
  const [isPopoverVisible, setIsPopoverVisible] = useState(false);
  const popoverRef = useRef(null);

  const options = dropDownData?.[field?.jsonName] || globaldropDownData?.[field?.jsonName] || [];
  let allOptions = options.map((option) => option?.code || "");
  
    useEffect(() => {
      if (fieldSelectivity === "Disabled") {
        setSelectedValues(allOptions);
        dispatchUpdate(allOptions);
      } else {
        if(RequestId) {
          setSelectedValues(dynamicData?.requestHeaderData?.FieldName?.split("$^$") || ccFieldNames?.requestHeaderData?.FieldName?.split(", ") || [])
          return;
        }
        setSelectedValues([]);
      }
      

    }, [fieldSelectivity, initialData?.TemplateName, options]);

     useEffect(() => {
     if(module == MODULE?.GL && initialData?.FieldName){
       setSelectedValues(initialData?.["FieldName"])
     }

    }, []);

  const handlePopoverOpen = (event, content) => {
        setPopoverAnchorEl(event.currentTarget);
        setPopoverContent(content);
        setIsPopoverVisible(true);
      };
     
  const handlePopoverClose = () => {
    setIsPopoverVisible(false);
  };
  
  const handleMouseEnterPopover = () => {
    setIsPopoverVisible(true);
  };
  
  const handleMouseLeavePopover = () => {
    setIsPopoverVisible(false);
  };
  
  const popoverOpen = Boolean(popoverAnchorEl);
  const popoverId = popoverOpen ? "custom-popover" : undefined;

  const handleSelectAllReqType = () => {
      if (selectedValues.length === allOptions.length) {
        setSelectedValues([]);
        dispatchUpdate([]);
      } else {
        setSelectedValues(allOptions);
        dispatchUpdate(allOptions);
      }
  };

  const dispatchUpdate = (values) => {
    if(module === MODULE?.CC) {
      dispatch(
        updateModuleFieldDataCC({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: values || [],
        })
      );
    }
    if(module === MODULE?.GL) {
      dispatch(
        setSelectedOptionsForTemplate({ values})
      );
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: values || [],
        })
      );
    }
    else if(module === MODULE.IO) {
      dispatch(
        updateModuleFieldDataIO({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: values || [],
          viewID: field?.viewName || "",
        })
      );
    }
    else {
      dispatch(
        updateModuleFieldData({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: values || [],
          viewID: field?.viewName || "",
        })
      );
    }
  };



  const isReqTypeSelected = (option) => selectedValues.includes(option);
  const isOptionDisabled = fieldSelectivity === "Disabled";

  return (
      <Grid item md={2} sx={{ marginBottom: "12px !important" }}>
        {field?.visibility === "Hidden" ? null : (
          <Stack>
            <Typography
              variant="body2"
              color={colors.secondary.grey}
              sx={{ whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis", maxWidth: "100%" }}
              title={field?.fieldName}
            >
              {field?.fieldName || "Field Name"}
              {(field?.visibility === "Mandatory" || field?.visibility === "0") && (
                <span style={{ color: "red" }}>*</span>
              )}
            </Typography>
            <Autocomplete
                multiple
                fullWidth
                disableCloseOnSelect
                disabled={disabled}
                size="small"
                value={selectedValues}
                onChange={(e, value, reason) => {
                    if(!isOptionDisabled) {
                        if (reason === "clear" || value?.length === 0) {
                        setSelectedValues([]);
                        dispatchUpdate([]);
                        return;
                    }

                    if (value.length > 0 && value[value.length - 1] === "Select All") {
                        handleSelectAllReqType();
                    } else {
                        setSelectedValues(value);
                        dispatchUpdate(value);
                    }
                  }
                }}
                options={allOptions.length ? ["Select All", ...allOptions] : []}
                getOptionLabel={(option) => `${option}` || ""}
                renderOption={(props, option, { selected }) => (
                    <li {...props} style={{ pointerEvents: isOptionDisabled ? "none" : "auto" }}>
                        <FormGroup>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        disabled={isOptionDisabled}
                                        checked={
                                            isReqTypeSelected(option) ||
                                            (option === "Select All" && selectedValues.length === allOptions.length)
                                        }
                                    />
                                }
                                label={<Typography style={{ fontSize: 12 }}><strong>{option}</strong></Typography>}
                            />
                        </FormGroup>
                    </li>
                )}
                renderTags={(selected, getTagProps) => {
                    const selectedOptionsText = selected?.join("<br />");
                    return selected?.length > 1 ? (
                        <>
                            <Chip
                                sx={{
                                    height: 25,
                                    fontSize: "0.85rem",
                                    ".MuiChip-label": { padding: "0 6px" },
                                    "&.Mui-disabled": {
                                        color: colors?.text?.primary,
                                        opacity: 1
                                    }
                                }}
                                label={`${selected[0]}`}
                                {...getTagProps({ index: 0 })}
                                
                            />
                            <Chip
                                sx={{
                                    height: 25,
                                    fontSize: "0.85rem",
                                    ".MuiChip-label": { padding: "0 6px" },
                                }}
                                label={`+${selected.length - 1}`}
                                onMouseEnter={(event) => handlePopoverOpen(event, selectedOptionsText)}
                                onMouseLeave={handlePopoverClose}
                                
                            />
                            <Popover
                                    id={popoverId}
                                    open={isPopoverVisible}
                                    anchorEl={popoverAnchorEl}
                                    onClose={handlePopoverClose}
                                    anchorOrigin={{
                                      vertical: "bottom",
                                      horizontal: "center",
                                    }}
                                    transformOrigin={{
                                      vertical: "top",
                                      horizontal: "center",
                                    }}
                                    onMouseEnter={handleMouseEnterPopover}
                                    onMouseLeave={handleMouseLeavePopover}
                                    ref={popoverRef}
                                    sx={{
                                      "& .MuiPopover-paper": {
                                        backgroundColor: "#f5f5f5",
                                        boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                                        borderRadius: "8px",
                                        padding: "10px",
                                        fontSize: "0.875rem",
                                        color: "#4791db",
                                        border: "1px solid #ddd",
                                      },
                                    }}
                                  >
                                    <Box
                                      sx={{
                                        maxHeight: "270px",
                                        overflowY: "auto",
                                        padding: "5px",
                                      }}
                                      dangerouslySetInnerHTML={{ __html: popoverContent }}
                                    />
                                  </Popover>
                        </>
                    ) : (
                        selected.map((option, index) => (
                            <Chip
                                sx={{
                                    height: 25,
                                    fontSize: "0.85rem",
                                    ".MuiChip-label": { padding: "0 6px" },
                                }}
                                label={`${option}`}
                                {...getTagProps({ index })}
                                
                            />
                        ))
                    );
                }}
                renderInput={(params) => (
                    <TextField
                        {...params}
                        variant="outlined"
                        placeholder={
                            selectedValues?.length === 0 
                            ? `Select ${field?.fieldName}`
                            : ""
                        }
                        InputProps={{
                            ...params.InputProps,
                            endAdornment: isOptionDisabled
                                ? null
                                : params.InputProps.endAdornment,
                        }}
                        sx={{
                            fontSize: "12px !important",
                            "& .MuiOutlinedInput-root": { height: 35 },
                            "& .MuiInputBase-input": { padding: "10px 14px" },
                        }}
                    />
                )}
            />
          </Stack>
        )}
      </Grid>
    );
};

export default MultiSelectTypeGlobal;