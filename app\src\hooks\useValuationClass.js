import { useSelector, useDispatch } from 'react-redux';
import { setDropDown } from '@app/dropDownDataSlice';
import useFetchDropdownAndDispatch from './useFetchDropdownAndDispatch';
import { END_POINTS } from '@constant/apiEndPoints';
import { DT_FIELDS_NAME } from '@constant/enum';
import { destination_MaterialMgmt } from '../../src/destinationVariables';
/**
 * Custom hook to manage valuation class data fetching and caching
 * @returns {Function} fetchValuationClassData - Function to fetch valuation class data for a material type
 */
const useValuationClass = () => {
  const dispatch = useDispatch();
  const { fetchDataAndDispatch } = useFetchDropdownAndDispatch();
  const valuationClassData = useSelector(state => state.payload.valuationClassData || {});

  /**
   * Fetch valuation class data for a material type, with caching
   * @param {string} materialTypeCode - The material type code to fetch valuation class data for
   */
  const fetchValuationClassData = (materialTypeCode) => {
    if (!materialTypeCode) return;
    
    const valuationClassExists = materialTypeCode in valuationClassData;
    
    if (!valuationClassExists) {
      fetchDataAndDispatch(
        `/${destination_MaterialMgmt}${END_POINTS.DATA.GET_VALUATION_CLASS}?matlType=${materialTypeCode}`,
        DT_FIELDS_NAME.VAL_CLASS
      );
    } else {
      dispatch(setDropDown({ 
        keyName: DT_FIELDS_NAME.VAL_CLASS, 
        data: valuationClassData[materialTypeCode] 
      }));
    }
  };

  return { fetchValuationClassData };
};

export default useValuationClass;