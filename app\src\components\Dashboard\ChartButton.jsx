import { useState } from "react";
import LeaderboardOutlinedIcon from "@mui/icons-material/LeaderboardOutlined";
import ShowChartOutlined from "@mui/icons-material/ShowChartOutlined";
import PieChartOutlineOutlinedIcon from "@mui/icons-material/PieChartOutlineOutlined";
import AlignHorizontalLeftOutlinedIcon from "@mui/icons-material/AlignHorizontalLeftOutlined";
import AssessmentOutlinedIcon from "@mui/icons-material/AssessmentOutlined";
import TableChartOutlinedIcon from "@mui/icons-material/TableChartOutlined";
import StackedBarChartOutlinedIcon from '@mui/icons-material/StackedBarChartOutlined';
const ChartButton = ({
  uiColors,
  setUiColors,
  type,
  bar,
  setBar,
  line,
  setLine,
  isStacked=false,
  isPie = false,
  isHorizontal = false,
  isMulti = false,
  isTable = false,
  isTable2=false,
  isTable3 =false,
  
}) => {
  const handleBar = () => {
    setUiColors({
      ...uiColors,
      [type]: { bar: "#3b30c8", line: "#9c9d9f" },
    });
    setBar({ ...bar, [type]: true });
    setLine({ ...line, [type]: false });
  };

  const handleLine = () => {
    setUiColors({
      ...uiColors,
      [type]: { line: "#3b30c8", bar: "#9c9d9f" },
    });
    setBar({ ...bar, [type]: false });
    setLine({ ...line, [type]: true });
  };

  return (
    <>
      {isPie ? (
        <PieChartOutlineOutlinedIcon sx={{ color: "#3b30c8" }} />
      ) : isMulti ? (
        <AssessmentOutlinedIcon sx={{ color: "#3b30c8" }} />
      ) : isStacked ?
      (
        <StackedBarChartOutlinedIcon sx={{ color: "#3b30c8" }} />

      ) :(isTable || isTable2 || isTable3 )? (
        <TableChartOutlinedIcon sx={{ color: "#3b30c8" }} />
      ) : (
        <>
          {isHorizontal ? (
            <AlignHorizontalLeftOutlinedIcon
              onClick={handleBar}
              sx={{ color: `${bar[type] ? "#3b30c8" : uiColors[type].bar}` }}
            />
          ) : (
            <LeaderboardOutlinedIcon
              onClick={handleBar}
              sx={{ color: `${bar[type] ? "#3b30c8" : uiColors[type].bar}` }}
            />
          )}
          <ShowChartOutlined
            onClick={handleLine}
            sx={{ color: `${line[type] ? "#3b30c8" : uiColors[type].line}` }}
          />
        </>
      )}
    </>
  );
};

export default ChartButton;
