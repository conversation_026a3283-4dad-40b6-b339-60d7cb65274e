import { useEffect, useState, useRef, useCallback } from "react";
import { doAjax } from "../components/Common/fetchService";
import { destination_Dashboard, destination_IDM } from "../destinationVariables";
import { useSelector } from "react-redux";
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "@hooks/useLogger";
import { ERROR_MESSAGES, VISIBILITY_TYPE } from "@constant/enum";

const getColumnByIndex = (index) => {
  const columns = ["First", "Second", "Third"];
  return columns[index % 3];
};

const formatApiData = (body, fallbackId, fallbackIndex) => {
  if (!body || typeof body !== "object" || Array.isArray(body)) return null;
  const rawType = body?.graphDetails?.chartType;
  const chartType = rawType?.toString()?.trim()?.toUpperCase();
  const graphName = body?.graphDetails?.graphName || "Untitled Chart";
  const colorPallete=body?.graphDetails?.color||"Pallet 1"
  const KpiSequence=body?.Sequence||0
  const id = body?.id || fallbackId;
  const column = body?.column || getColumnByIndex(id);
  if (!chartType || !graphName) return null;

  if (["PIE", "DONUT"].includes(chartType)) {
    const label = body?.label;
    const series = body?.series;
    if (!Array.isArray(label) || !Array.isArray(series)) return null;
    return { id, column, graphDetails: { chartType, graphName ,colorPallete,KpiSequence}, label, series };
  }

  if (Array.isArray(body?.data)) {
    return { id, column, graphDetails: { chartType, graphName,colorPallete,KpiSequence }, data: body.data };
  }

  return null;
};

export const useDashboardCall = (refreshTrigger = 0) => {
  const { customError } = useLogger();
  const [cards, setCards] = useState([]);
  const [loading, setLoading] = useState(true);
  const [decisionTableConfig, setDecisionTableConfig] = useState([]);
  const [reportConfig, setReportConfig] = useState([]);
  const [userPreferences, setUserPreferences] = useState([]);
  const [kpiPayloadMap, setKpiPayloadMap] = useState({});
  const [kpiReportPrefs, setKpiReportPrefs] = useState([]);

  const dashboardFilters = useSelector((state) => state.commonFilter?.Dashboard || {});
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const userData = useSelector((state) => state.userManagement.userData);
  const userId = userData?.user_id || "";

  const hasInitializedRef = useRef(false);
  const isFetchingRef = useRef(false);
  const refreshDashboard = useCallback(async () => {
    if (isFetchingRef.current) return;

    isFetchingRef.current = true;
    setLoading(true);
    hasInitializedRef.current = false;

    try {
      const kpiData = await fetchDecisionTable("KPI Metrics");
      const reportData = await fetchDecisionTable("KPI Reports");
      const metricPrefs = await fetchUserPreferences(kpiData, "KPI Metrics");
      const reportPrefs = await fetchUserPreferences(reportData, "KPI Reports");
      const graphData = await fetchGraphData(kpiData, metricPrefs);
      setKpiReportPrefs(reportPrefs);
      setCards(graphData);
      setReportConfig(reportData);
      setUserPreferences([...metricPrefs, ...reportPrefs]);
      hasInitializedRef.current = true;
    } catch (err) {
      customError(ERROR_MESSAGES.DASHBOARD_REFRESH_FAILED, err);
    } finally {
      isFetchingRef.current = false;
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (refreshTrigger > 0) {
      refreshDashboard();
    }
  }, [refreshTrigger, refreshDashboard]);

  const fetchDecisionTable = async (kpiType = "KPI Metrics") => {
    const decisionPayload = {
      decisionTableId: null,
      decisionTableName: "MDG_MAT_DYNAMIC_DASHBOARD_DT",
      version: "v3",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": "US",
          "MDG_CONDITIONS.MDG_MAT_ROLE": "MDM Steward",
          "MDG_CONDITIONS.MDG_KPI_TYPE": kpiType,
        },
      ],
    };

    const dtUrl = applicationConfig.environment === "localhost" ? `/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}` : `/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`;

    try {
      const result = await new Promise((resolve, reject) => {
        doAjax(
          dtUrl,
          "post",
          (res) => {
            const data = res?.data?.result?.[0]?.MDG_DYNAMIC_DASHBOARD_ACTION_TYPE || [];
            resolve(data);
          },
          reject,
          decisionPayload
        );
      });

      if (kpiType === "KPI Metrics") {
        setDecisionTableConfig(result);
      } else {
        setReportConfig(result);
      }
      return result;
    } catch (err) {
      customError(kpiType === "KPI Metrics" ? ERROR_MESSAGES.DECISION_TABLE_FETCH_ERROR : ERROR_MESSAGES.REPORT_CONFIG_FETCH_ERROR, err);
      return [];
    }
  };

  const fetchUserPreferences = async (dtData, kpiType) => {
    try {
      let prefs = await new Promise((resolve) => {
        doAjax(
          `/${destination_Dashboard}${END_POINTS.DASHBOARD_APIS.FETCH_USER_CONFIG}?userId=${userId}&kpiType=${encodeURIComponent(kpiType)}`,
          "get",
          (res) => resolve(res?.body || []),
          () => resolve([])
        );
      });

      if (!prefs.length && dtData.length) {
        const newPrefs = dtData.map((kpi) => ({
          Id: null,
          UserId: userId,
          KpiId: kpi.MDG_KPI_ID,
          KpiChartType: kpiType === "KPI Metrics" ? kpi.MDG_KPI_GRAPH_TYPE : null,
          KpiChartName: kpi.MDG_KPI_NAME,
          KpiColPallet: kpiType === "KPI Metrics" ? kpi.MDG_KPI_COLOR_PALLET : null,
          KpiSequence: Number(kpi.MDG_KPI_GRAPH_SEQUENCE),
          KpiColumn: kpi.MDG_KPI_GRAPH_COLUMN?.toLowerCase(),
          KpiVisibility: true,
          IsActive: true,
          KpiType: kpiType,
        }));

        await new Promise((resolve, reject) => {
          doAjax(`/${destination_Dashboard}${END_POINTS.DASHBOARD_APIS.SAVE_USER_CONFIG}`, "post", resolve, reject, newPrefs);
        });

        prefs = await new Promise((resolve) => {
          doAjax(
            `/${destination_Dashboard}${END_POINTS.DASHBOARD_APIS.FETCH_USER_CONFIG}?userId=${userId}&kpiType=${encodeURIComponent(kpiType)}`,
            "get",
            (res) => resolve(res?.body || []),
            () => resolve(newPrefs) 
          );
        });
      }

      return prefs;
    } catch (err) {
      customError(ERROR_MESSAGES.USER_PREFERENCES_FETCH_ERROR, err);
      return [];
    }
  };

  const fetchGraphData = async (dtData, prefs) => {
    if (!dtData.length) return [];
    try {
      const kpiEndpointMap = {};
      dtData.forEach((kpi) => {
        if (kpi.MDG_KPI_ID && kpi.MDG_KPI_ENDPOINT) {
          kpiEndpointMap[kpi.MDG_KPI_ID] = kpi.MDG_KPI_ENDPOINT.replace(/^\//, "");
        }
      });
      const enabledIds_DT = dtData.filter((d) => ["TRUE", "ENABLED"].includes((d.MDG_KPI_VISIBILITY || "").toString().toUpperCase())).map((d) => d.MDG_KPI_ID);

      const enabledIds_User = prefs.filter((p) => p.KpiVisibility === true && p.IsActive === true).map((p) => p.KpiId);

      const enabledKpiIds = enabledIds_User.length > 0 ? enabledIds_DT.filter((id) => enabledIds_User.includes(id)) : enabledIds_DT;
      const payloadMap = {};
      const results = await Promise.all(
        enabledKpiIds.map((kpiId) => {
          const pref = prefs.find((p) => p.KpiId === kpiId);
          const dt = dtData.find((p) => p.MDG_KPI_ID === kpiId);
          const endpoint = kpiEndpointMap[kpiId];
          if (!endpoint) return Promise.resolve(null);

          const index = parseInt(kpiId.split("_")[1]) - 1;
          const payload = {
            FromDate: "2024-01-01",
            ToDate: "2025-12-31",
            Requestor: "",
            KpiId: kpiId,
            Module: "Material",
            UserId: userId,
            Priority: "",
            Region: dashboardFilters?.selectedRegion || "",
            ReqType: dashboardFilters?.selectedRequestType?.join(",") || "",
            ReqStatus: dashboardFilters?.selectedRequestStatus?.join(",") || "",
            GraphType: pref?.KpiChartType || dt?.MDG_KPI_GRAPH_TYPE || "",
            KpiName: pref?.KpiChartName || dt?.MDG_KPI_NAME || "",
            ColPallet: pref?.KpiColPallet || dt?.MDG_KPI_COLOR_PALLET || "",
            GraphColumn: pref?.KpiColumn || dt?.MDG_KPI_GRAPH_COLUMN?.toLowerCase() || getColumnByIndex(index),
            GraphSequence: pref?.KpiSequence || Number(dt?.MDG_KPI_GRAPH_SEQUENCE) || index + 1,
          };

          payloadMap[kpiId] = payload;

          return new Promise((resolve) => {
            doAjax(
              `/${destination_Dashboard}/counts/${endpoint}`,
              "post",
              (res) => resolve(formatApiData(res.body, index + 1, index)),
              () => resolve(null),
              payload
            );
          });
        })
      );

      setKpiPayloadMap(payloadMap);
      return results.filter(Boolean);
    } catch (err) {
      customError(ERROR_MESSAGES.GRAPH_DATA_FETCH_ERROR, err);
      return [];
    }
  };

  const initializeDashboard = async () => {
    if (isFetchingRef.current || hasInitializedRef.current) return;

    isFetchingRef.current = true;
    setLoading(true);
    try {
      const kpiData = await fetchDecisionTable("KPI Metrics");
      const reportData = await fetchDecisionTable("KPI Reports");
      const metricPrefs = await fetchUserPreferences(kpiData, "KPI Metrics");
      const reportPrefs = await fetchUserPreferences(reportData, "KPI Reports");
      const graphData = await fetchGraphData(kpiData, metricPrefs);
      setCards(graphData);
      setKpiReportPrefs(reportPrefs);
      setReportConfig(reportData);
      setUserPreferences([...metricPrefs, ...reportPrefs]);
      hasInitializedRef.current = true;
    } catch (err) {
      customError(ERROR_MESSAGES.DASHBOARD_INIT_FAILED, err);
    } finally {
      isFetchingRef.current = false;
      setLoading(false);
    }
  };

  useEffect(() => {
    let cancelled = false;
    initializeDashboard();
    return () => {
      cancelled = true;
    };
  }, [userId]);

  useEffect(() => {
    const updateOnFilters = async () => {
      if (!hasInitializedRef.current || isFetchingRef.current) return;

      isFetchingRef.current = true;
      setLoading(true);
      try {
        const graphData = await fetchGraphData(decisionTableConfig, userPreferences);
        setCards(graphData);
      } catch (err) {
        customError(ERROR_MESSAGES.FILTER_CHANGE_UPDATE_FAILED, err);
      } finally {
        isFetchingRef.current = false;
        setLoading(false);
      }
    };
    updateOnFilters();
  }, [dashboardFilters]);

  return {
    cards,
    reportConfig,
    loading,
    decisionTableConfig,
    userPreferences,
    kpiPayloadMap,
    kpiReportPrefs,
    refreshDashboard,
  };
};
