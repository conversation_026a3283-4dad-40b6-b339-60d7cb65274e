import React, { useEffect, useState } from "react";
import { Grid, FormControl, InputLabel, Select, MenuItem } from "@mui/material";
import { doAjax } from "../Common/fetchService";
import {
    destination_IDM,
    destination_MaterialMgmt,
    destination_ProfitCenter_Mass
  } from "../../../src/destinationVariables";


const RegionDropdown = ({ formData, handleChange, selectedCountry }) => {
  const [dropdownDataSegment, setDropdownDataSegment] = useState([]);
  

  useEffect(() => {
    if (!selectedCountry) return; // Do nothing if no country is selected

    const getRegions = () => {
      const hSuccess = (data) => {
        setDropdownDataSegment(data.body); // Store API response
      };

      const hError = (error) => {
        console.error("Error fetching regions:", error);
      };

      doAjax(
        `/${destination_ProfitCenter_Mass}/data/getRegionBasedOnCountry?country=${selectedCountry}`, 
        "get",
        hSuccess,
        hError
      );
    };

    getRegions();
  }, [selectedCountry]); // Fetch when selectedCountry changes

  return (
    <Grid item xs={6} md={12}>
      <FormControl fullWidth disabled={!selectedCountry}> 
        {/* Disable when no country is selected */}
        <InputLabel>Region</InputLabel>
        <Select
          value={formData.Regio || ""}
          onChange={handleChange("Regio")}
        >
          {dropdownDataSegment?.length > 0 ? (
            dropdownDataSegment.map((item) => (
              <MenuItem key={item.code} value={item.code}>
                {`${item?.code}-${item?.desc}`}
              </MenuItem>
            ))
          ) : (
            <MenuItem disabled>{selectedCountry ? "Loading..." : "Select a country first"}</MenuItem>
          )}
        </Select>
      </FormControl>
    </Grid>
  );
};

export default RegionDropdown;
