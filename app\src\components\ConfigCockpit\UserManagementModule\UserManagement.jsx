import React, { useState, useEffect } from "react";
import { Box, Button, Grid, IconButton, LinearProgress, Stack, Tooltip, Typography, alpha } from "@mui/material";
import { Refresh } from "@mui/icons-material";
import { doAjax } from "../../Common/fetchService";
import { destination_MaterialMgmt } from "../../../destinationVariables";
import UserList from "./UserList";
import UserDetails from "./UserDetails";
import { colors } from "@constant/colors";
import { END_POINTS } from "@constant/apiEndPoints";
import { ToastContainer } from "react-toastify";
import { saveExcel, showToast } from "../../../functions";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "../../../constant/enum";
import ReusableIcon from "../../common/ReusableIcon";
import { iconButton_SpacingSmall } from "../../common/commonStyles";
import useLang from "@hooks/useLang";

const getColorFromString = (str, palette = [colors.primary.main, colors.primary.dark, colors.info.dark, colors.secondary.dark, colors.blue.main, colors.icon.oranges]) => {
  const hash = str.split("").reduce((acc, char) => char.charCodeAt(0) + acc, 0);
  return palette[hash % palette.length];
};

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [rolesData, setRolesData] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const { t } = useLang();

  useEffect(() => {
    fetchUsers();
  }, []);
let UserData={
  "<EMAIL>": {
    "userRoles": ["CA-MDG-GNRL-ACCESS"],
    "userGroups": ["General Access Group"],
    "userName": "tripathya",
    "displayName": "Abhishek Tripathy",
    "userEmail": "<EMAIL>"
  },
  "<EMAIL>": {
    "userRoles": [
      "CA-MDG-SUPPLY-EUR",
      "CA-MDG-MRKTNG-SALES-EUR",
      "CA-MDG-MD-TEAM-US",
      "CA-MDG-FINANCE-US",
      "CA-MDG-MRKTNG-FERT-EUR",
      "CA-MDG-MRKTNG-BOM"
    ],
    "userGroups": [
      "Master Data Team US Group",
      "Finance US Group",
      "Supply EUR Group",
      "Marketing BOM Group",
      "Marketing Sales EUR Group",
      "Marketing FERT EUR Group"
    ],
    "userName": "test",
    "displayName": " test",
    "userEmail": "<EMAIL>"
  },
  "<EMAIL>": {
    "userRoles": [
      "CA-MDG-SUPPLY-EUR",
      "CA-MDG-MRKTNG-SALES-EUR",
      "CA-MDG-IMPORT-US",
      "CA-MDG-MD-TEAM-US",
      "CA-MDG-MD-TEAM-EUR",
      "CA-MDG-FINANCE-US",
      "CA-MDG-MRKTNG-US",
      "CA-MDG-MRKTNG-FERT-EUR",
      "CA-MDG-GNRL-ACCESS",
      "CA-MDG-FINANCE-EUR",
      "CA-MDG-MRKTNG-BOM"
    ],
    "userGroups": [
      "Import US Group",
      "Master Data Team US Group",
      "Finance US Group",
      "Marketing US Group",
      "General Access Group",
      "Supply EUR Group",
      "Finance EUR Group",
      "Marketing BOM Group",
      "Marketing Sales EUR Group",
      "Marketing FERT EUR Group",
      "Master Data Team EUR Group"
    ],
    "userName": "kumarburraa",
    "displayName": "Anil KumarBurra",
    "userEmail": "<EMAIL>"
  },
  "<EMAIL>": {
    "userRoles": [
      "CA-MDG-SUPPLY-EUR",
      "CA-MDG-MRKTNG-SALES-EUR",
      "CA-MDG-IMPORT-US",
      "CA-MDG-MD-TEAM-US",
      "CA-MDG-MD-TEAM-EUR",
      "CA-MDG-FINANCE-US",
      "CA-MDG-MRKTNG-US",
      "CA-MDG-MRKTNG-FERT-EUR",
      "CA-MDG-GNRL-ACCESS",
      "CA-MDG-FINANCE-EUR",
      "CA-MDG-MRKTNG-BOM"
    ],
    "userGroups": [
      "Import US Group",
      "Master Data Team US Group",
      "Finance US Group",
      "Marketing US Group",
      "General Access Group",
      "Supply EUR Group",
      "Finance EUR Group",
      "Marketing BOM Group",
      "Marketing Sales EUR Group",
      "Marketing FERT EUR Group",
      "Master Data Team EUR Group"
    ],
    "userName": "manasS",
    "displayName": "Manas Sahoo",
    "userEmail": "<EMAIL>"
  },
  "<EMAIL>": {
    "userRoles": [
      "CA-MDG-MRKTNG-SALES-EUR",
      "CA-MDG-MRKTNG-FERT-EUR"
    ],
    "userGroups": [
      "Marketing Sales EUR Group",
      "Marketing FERT EUR Group"
    ],
    "userName": "prajapatis",
    "displayName": "Sunil Prajapati",
    "userEmail": "<EMAIL>"
  }
}

  const fetchUsers = async () => {
    // setLoading(true);
    // doAjax(
    //   `/${destination_MaterialMgmt}${END_POINTS.WORK_FLOW.FETCH_USER_DATA}`,
    //   "get",
    //   () => {
        setUsers(Object.keys(UserData).map((email) => ({ email, ... UserData[email] })));
        showToast(SUCCESS_MESSAGES.USER_FETCHED, "success");
        setLoading(false);
    //   },
    //   (error) => {
    //     showToast(ERROR_MESSAGES?.ERROR_FETCHING_USER, "error");
    //     setLoading(false);
    //   }
    // );
  };
const RolesDATA={
  "statusCode": 200,
  "message": "fetched successfully",
  "body": {
      "id": 0,
      "roles": [
          "CA-MDG-GNRL-ACCESS"
      ],
      "entitiesAndActivities": [
          {
              "Request Bench": [
                  "Request Bench"
              ],
              "Material": [
                  "Change with Upload",
                  "Extend with Upload",
                  "Extend",
                  "Change"
              ],
              "Master Data": [
                  "Material"
              ],
              "Workspace": [
                  "Completed Tasks",
                  "My Tasks"
              ]
          }
      ],
      "applications": [
          1
      ]
  },
  "count": null
}
  const fetchRoles = (user) => {

    setRolesData(RolesDATA.body);
    // doAjax(
    //   `/${destination_MaterialMgmt}${END_POINTS.WORK_FLOW.FETCH_ROLES_DATA}`,
    //   "post",
    //   (data) => setRolesData(data.body),
    //   (error) => showToast(ERROR_MESSAGES?.ERROR_FETCHING_ROLES, "error"),
    //   user.userRoles
    // );
  };

  const functions_ExportAsExcel = {
    convertJsonToExcel: (users, columns) => {
      if (!users || users.length === 0) {
        showToast(ERROR_MESSAGES?.ERROR_EXPORT, "warning");
        return;
      }

      let excelColumns = [];

      columns.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });

      const rows = users.map((user) => ({
        displayName: user.displayName || "-",
        email: user.email,
        userRoles: user.userRoles.length > 0 ? user.userRoles.join(", ") : "",
        userGroups: user.userGroups.length > 0 ? user.userGroups.join(", ") : "",
      }));
      saveExcel({
        fileName: `User Management Data`,
        columns: excelColumns,
        rows: rows,
      });
    },

    button: (users, columns) => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel(users, columns)}
        >
          Download
        </Button>
      );
    },
  };
  const exportUserData = () => {
    if (users.length === 0) {
      showToast(ERROR_MESSAGES?.ERROR_EXPORT, "warning");
      return;
    }

    const columns = [
      { field: "displayName", headerName: "User Name" },
      { field: "email", headerName: "Email" },
      { field: "userRoles", headerName: "Roles" },
      { field: "userGroups", headerName: "User Groups" },
    ];
    functions_ExportAsExcel.convertJsonToExcel(users, columns);
  };
  const handleUserSelect = (user) => {
    setSelectedUser(user);
    fetchRoles(user);
  };

  const handleCloseDetails = () => {
    setSelectedUser(null);
    setRolesData(null);
  };

  const styles = {
    container: {
      height: "100vh",
      display: "flex",
      flexDirection: "column",
      bgcolor: colors.primary.veryLight,
      overflow: "hidden",
    },
    header: {
      py: 1.5,
      px: 3,
      bgcolor: colors.primary.white,
      borderBottom: `1px solid ${colors.basic.grey}`,
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      flexShrink: 0,
      zIndex: 10,
    },
    headerTitle: {
      fontWeight: 600,
      fontSize: "1.125rem",
      color: colors.primary.grey,
    },
    progressBar: {
      height: 2,
      flexShrink: 0,
      "& .MuiLinearProgress-bar": {
        bgcolor: colors.primary.main,
      },
    },
    contentContainer: {
      display: "flex",
      flex: 1,
      p: 2.5,
      gap: 2.5,
      overflow: "hidden",
      height: "100%",
    },
    listContainer: {
      transition: "width 0.3s ease-in-out",
      height: "100%",
    },
    detailsContainer: {
      width: "42%",
      height: "100%",
      transition: "all 0.3s ease",
    },
  };

  return (
    <>
      <Box sx={styles.container}>
        <Grid sx={styles.header}>
          <Typography variant="h6" sx={styles.headerTitle}>
            {t("User Management")}
          </Typography>
          <Stack direction="row" spacing={1}>
            <Tooltip title="Export Table">
              <IconButton sx={iconButton_SpacingSmall} onClick={exportUserData}>
                <ReusableIcon iconName={"IosShare"} />
              </IconButton>
            </Tooltip>
            <Tooltip title="Reload">
              <IconButton sx={iconButton_SpacingSmall}>
                <Refresh
                  sx={{
                    "&:hover": {
                      transform: "rotate(360deg)",
                      transition: "0.9s",
                    },
                  }}
                  onClick={fetchUsers}
                />
              </IconButton>
            </Tooltip>
          </Stack>
        </Grid>

        {loading && <LinearProgress sx={styles.progressBar} />}

        <Box sx={styles.contentContainer}>
          <Box
            sx={{
              ...styles.listContainer,
              width: selectedUser ? "58%" : "100%",
            }}
          >
            <UserList users={users} selectedUser={selectedUser} onUserSelect={handleUserSelect} loading={loading} colors={colors} getColorFromString={getColorFromString} />
          </Box>

          {selectedUser && (
            <Box sx={styles.detailsContainer}>
              <UserDetails user={selectedUser} rolesData={rolesData} onClose={handleCloseDetails} colors={colors} getColorFromString={getColorFromString} />
            </Box>
          )}
        </Box>
      </Box>
      <ToastContainer />
    </>
  );
};

export default UserManagement;
