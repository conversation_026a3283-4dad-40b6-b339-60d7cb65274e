import React, { useEffect, useState, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { destination_ProfitCenter_Mass } from "../../destinationVariables";
import { v4 as uuidv4 } from "uuid";
import { doAjax } from "../../components/Common/fetchService";
import useProfitCenterFieldConfig from "@hooks/UseProfitCenterFieldConfig";
import useButtonDTConfig from "@hooks/useButtonDTConfig";
import ReusableBackDrop from "../../components/Common/ReusableBackDrop";
import ReusableDataTable from "../../components/Common/ReusableTable";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { useLocation, useNavigate } from "react-router-dom";
import {
  TextField,
  IconButton,
  Box,
  Typography,
  Paper,
  Button,
  Tabs,
  Tab,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Radio,
  RadioGroup,
  FormControlLabel,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Snackbar,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Grid,
  Checkbox,
  Tooltip,
  FormLabel,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  DialogContentText,
  Stack,
} from "@mui/material";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import TaskAltIcon from "@mui/icons-material/TaskAlt";
import { setSelectedRowId } from "@app/redux/profitCenterTabSlice";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import GenericTabsGlobal from "../../components/MasterDataCockpit/GenericTabsGlobal";
import { colors } from "@constant/colors";
import {
  setPCRows,
  updateModuleFieldData,
  setValidatedStatus,
  setOpenDialog,
  setProfitCenterTab,
  setValidatedRows,
  setValidatedRowsFromStorage,
  resetValidatedRows,
} from "@app/profitCenterTabsSlice";
import {
  createPayloadForPC,
  getCompanyCode,
  getCompanyCodeBasedOnControllingArea,
} from "../../functions";
import BottomNavGlobal from "../../components/RequestBench/RequestPages/BottomNavGlobal";
import { setDependentDropdown, setDropDown } from "@app/dropDownDataSlice";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import CropFreeIcon from "@mui/icons-material/CropFree";
import CloseFullscreenIcon from "@mui/icons-material/CloseFullscreen";
import useLang from "@hooks/useLang";
import { useChangeLogUpdateGl } from "@hooks/useChangeLogUpdateGl";
import {
  CHANGE_LOG_STATUSES,
  FAILURE_DIALOG_MESSAGE,
  SUCCESS_DIALOG_MESSAGE,
} from "@constant/enum";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import CloseIcon from "@mui/icons-material/Close";
import SuccessDialog from "@components/Common/SubmitDialog";

const RequestDetailsPC = ({
  reqBench,
  setIsAttachmentTabEnabled,
  setCompleted,
  module,
  isDisabled,
  fieldDisable,
}) => {
  const hasAddedRowRef = useRef(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useLang();

  const selectedLineRef = useRef(null);

  let task = useSelector((state) => state?.userManagement.taskData);
  const validatedRows = useSelector(
    (state) => state.profitCenter.validatedRows
  );

  const { updateChangeLogGl } = useChangeLogUpdateGl();
  const validatedStatus = useSelector(
    (state) => state?.profitCenter?.validatedRowsStatus
  );
  // NOTE:Required for validation
  // const validatedRows = useSelector((state) => state.profitCenter.validatedRows);
  const { selectedRowId, tabs } = useSelector((state) => state.profitCenterTab);

  const profitCenterTabs = useSelector((state) => {
    const tabs = state.profitCenter.profitCenterTabs || [];
    return tabs.filter((tab) => tab.tab !== "Initial Screen");
  });

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isrequestId = queryParams.get("RequestId");

  const requestHeaderSlice = useSelector(
    (state) => state.request.requestHeader
  );

  const openDialog = useSelector((state) => state.profitCenter.isOpenDialog);
  const requestHeaderData = useSelector((state) => state.requestHeader);
  const { loading, error, fetchProfitCenterFieldConfig } =
    useProfitCenterFieldConfig();
  const pcRows = useSelector(
    (state) => state.profitCenter.payload.rowsHeaderData
  );
 
  const reduxPayload = useSelector((state) => state.profitCenter.payload);

  const rowsBodyData = useSelector(
    (state) => state.profitCenter.payload?.rowsBodyData || {}
  );

  let requestStatus =
    rowsBodyData?.[pcRows[0]?.id]?.["Torequestheaderdata"]?.["RequestStatus"];
  const createChangeLogData = useSelector(
    (state) => state.changeLog.createChangeLogDataGL
  );

  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons);

  const [selectedRow, setSelectedRow] = useState(null);

  const [selectedTab, setSelectedTab] = useState(0);
  const [rowTabData, setRowTabData] = useState({});
  const [dropdownDataCompany, setDropdownDataCompany] = useState([]);
  const [dropdownDataBusinessSegment, setDropdownDataBusinessSegment] =
    useState([]);
  const [dropdownDataTaxJur, setDropdownDataTaxJur] = useState([]);
  const [dropdownDataFormPlanning, setDropdownDataFormPlanning] = useState([]);
  const [dropdownDataCOA, setDropdownDataCOA] = useState([]);
  const [dropdownDataCountry, setDropdownDataCountry] = useState([]);
  const [dropdownDataRegion, setDropdownDataRegion] = useState([]);
  const [dropdownDataSegment, setDropdownDataSegment] = useState([]);
  const [dropdownDataLanguage, setDropdownDataLanguage] = useState([]);
  const [missingFieldsDialogOpen, setMissingFieldsDialogOpen] = useState(false);
  const [missingFields, setMissingFields] = useState([]);
  const [isAddRowEnabled, setIsAddRowEnabled] = useState(false);
  const [isSaveAsDraftEnabled, setIsSaveAsDraftEnabled] = useState(false);
  const [validateEnabled, setValidateEnabled] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [openSnackBar, setOpenSnackBar] = useState(false);
  const [alertMsg, setAlertMsg] = useState("");
  const [alertType, setAlertType] = useState("success");
  const [isLoading, setIsLoading] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const { getButtonsDisplayGlobal } = useButtonDTConfig();
  const [dirtyRows, setDirtyRows] = useState({});
  const [originalRowData, setOriginalRowData] = useState({});
  const [originalTabData, setOriginalTabData] = useState({});
  const [isGridZoomed, setIsGridZoomed] = useState(false);
  const [isTabsZoomed, setIsTabsZoomed] = useState(false);
  const [withReference, setWithReference] = useState("yes");
  const [selectedControllingAreaCode, setSelectedControllingAreaCode] =
    useState("");
  const [selectedCompanyCode, setSelectedCompanyCode] = useState("");
  const [profitCenterOptions, setProfitCenterOptions] = useState([]);
  const [selectedProfitCenterCode, setSelectedProfitCenterCode] = useState("");
  const [profitcenterResponse, setProfitcenterResponse] = useState([]);
  const [newRowId, setNewRowId] = useState();
  const [isAddRowMode, setIsAddRowMode] = useState(false);
  const [selectedLineNumberOption, setSelectedLineNumberOption] =
    useState(null);
  const [withRefValues, setWithRefValues] = useState({});
  const [selectedMatLines, setSelectedMatLines] = useState([]);
  const [selectedMaterials, setSelectedMaterials] = useState(null);
  const [errorFieldMap, setErrorFieldMap] = useState({});
  const [duplicateTextDialogOpen, setDuplicateTextDialogOpen] = useState(false);
  const [duplicateTextDetails, setDuplicateTextDetails] = useState([]);
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);
  const [lineNumberCounter, setLineNumberCounter] = useState(0);

  const [dialogData, setDialogData] = useState({
    title: "",
    message: "",
    subText: "",
    buttonText: "",
    redirectTo: "",
  });

  useEffect(() => {
    const storedValidated = localStorage.getItem("validatedRows_PC");
    if (storedValidated) {
      try {
        const parsed = JSON.parse(storedValidated);
        const cleaned = Object.fromEntries(
          Object.entries(parsed).filter(([key]) => key !== "undefined")
        );
        dispatch(setValidatedRowsFromStorage(cleaned)); // ✅ Bulk dispatch
      } catch (e) {
        console.error("Failed to parse validated rows from localStorage", e);
      }
    }
  }, []);
  useEffect(() => {
    if (!profitCenterTabs?.length) {
      fetchProfitCenterFieldConfig();
    }
  }, []);

  useEffect(() => {
    if (
      pcRows.length >= 1 &&
      (pcRows[0]?.profitCenterNumber ||
        pcRows[0]?.controllingArea ||
        pcRows[0]?.companyCode)
    ) {
      dispatch(setOpenDialog(false));
    }
  }, []);

  useEffect(() => {
    if (task?.ATTRIBUTE_1 || isrequestId) {
      getButtonsDisplayGlobal("Profit Center", "MDG_DYN_BTN_DT", "v3");
    }
  }, [task]);

  const isEqual = (a, b) => JSON.stringify(a) === JSON.stringify(b);

  const mandatoryFields = [
    "controllingArea",
    "profitCenterNumber",
    "companyCode",
    "businessSegment",
    "longDescription",
  ];

  const fieldsSyncedFromRow = {
    Description: "longDescription",
    CompanyCode: "companyCode.code",
  };

  const cleanTabForComparison = (tab, row) => {
    const cleanedTab = { ...tab };
    delete cleanedTab.id;
    for (const [tabKey, rowPath] of Object.entries(fieldsSyncedFromRow)) {
      const rowValue = rowPath
        .split(".")
        .reduce((acc, key) => (acc ? acc[key] : undefined), row);

      // If tab value matches what came from row, ignore it
      if (cleanedTab[tabKey] === rowValue) {
        delete cleanedTab[tabKey];
      }
    }
    return cleanedTab;
  };

  const isRowDirty = (rowId) => {
    const originalRow = originalRowData[rowId];
    const originalTab = originalTabData[rowId];
    const currentRow = pcRows.find((r) => r.id === rowId);
    const currentTab = rowsBodyData[rowId];

    if (!originalRow || !originalTab || !currentRow || !currentTab) return true;

    const cleanedCurrentTab = cleanTabForComparison(currentTab, currentRow);
    const cleanedOriginalTab = cleanTabForComparison(originalTab, originalRow);

    return (
      !isEqual(originalRow, currentRow) ||
      !isEqual(cleanedOriginalTab, cleanedCurrentTab)
    );
  };
  const getValidationStatus = (rowId) => {
    const status = validatedStatus[rowId];
    const dirty = isRowDirty(rowId);

    if (!status) return "default";
    return isRowDirty(rowId) ? "error" : status;
  };

  const handleValidate = async (row, tab, config) => {
    const missing = [];
    const lineNumber =
      row?.lineNumber || pcRows.findIndex((r) => r.id === row.id) + 1;

    // 1. Check tab-level mandatory fields
    config.forEach((field) => {
      if (field.visibility === "Mandatory") {
        const value = tab[field.jsonName];
        if (
          value === null ||
          value === undefined ||
          (typeof value === "string" && value.trim() === "")
        ) {
          missing.push(`Line ${lineNumber} - ${field.fieldName}`);
        }
      }
    });

    // 2. Check row-level mandatory fields
    const headerMap = {
      companyCode: "Company Code",
      profitCenterNumber: "Profit Center Number",
      businessSegment: "Business Segment",
      controllingArea: "Controlling Area",
      longDescription: "Long Description",
    };

    mandatoryFields.forEach((field) => {
      const value = row[field];
      const displayName = headerMap[field] || field;

      if (
        value === null ||
        value === undefined ||
        (typeof value === "string" && value.trim() === "")
      ) {
        missing.push(`Line ${lineNumber} - ${displayName}`);
      } else if (
        field === "profitCenterNumber" &&
        (value.length !== 10 || !/^[a-zA-Z0-9]+$/.test(value))
      ) {
        missing.push(`Line ${lineNumber} - ${displayName}`);
      }
    });

    // 3. Check for duplicate (wait for result)

    const status = missing.length === 0 ? "success" : "error";
    dispatch(setValidatedStatus({ rowId: row.id, status }));
    dispatch(setValidatedRows({ rowId: row.id }));
    // 5. If error, highlight fields and show missing
    if (status === "error") {
      const uniqueMissing = [...new Set(missing)];

      setMissingFields(uniqueMissing);
      setMissingFieldsDialogOpen(true);

      const errorFieldsForRow = [];

      config.forEach((field) => {
        if (field.visibility === VISIBILITY_TYPE.MANDATORY) {
          const value = tab[field.jsonName];
          if (
            value === null ||
            value === undefined ||
            (typeof value === "string" && value.trim() === "")
          ) {
            errorFieldsForRow.push(field.jsonName);
          }
        }
      });

      mandatoryFields.forEach((field) => {
        const value = row[field];
        if (
          value === null ||
          value === undefined ||
          (typeof value === "string" && value.trim() === "")
        ) {
          errorFieldsForRow.push(field);
        } else if (
          field === "profitCenterNumber" &&
          (value.length !== 10 || !/^[a-zA-Z0-9]+$/.test(value))
        ) {
          errorFieldsForRow.push(field);
        }
      });

      setErrorFieldMap((prev) => ({
        ...prev,
        [row.id]: errorFieldsForRow,
      }));
    } else {
      // 6. If successful, store original data
      setOriginalRowData((prev) => ({
        ...prev,
        [row.id]: JSON.parse(JSON.stringify(row)),
      }));

      setOriginalTabData((prev) => {
        const { id, ...restTab } = tab;
        return {
          ...prev,
          [row.id]: JSON.parse(JSON.stringify(restTab)),
        };
      });

      // ✅ Show validation success snackbar
      setAlertType("success");
      setAlertMsg("Validation Successful");
      setOpenSnackBar(true);
    }
  };

  useEffect(() => {
    if (pcRows.length && Object.keys(rowsBodyData).length) {
      const newOriginalRows = {};
      const newOriginalTabs = {};

      pcRows.forEach((row) => {
        const rowId = row.id;
        if (!originalRowData[rowId]) {
          newOriginalRows[rowId] = JSON.parse(JSON.stringify(row));
        }
        if (!originalTabData[rowId] && rowsBodyData[rowId]) {
          const { id, ...restTab } = rowsBodyData[rowId];
          newOriginalTabs[rowId] = JSON.parse(JSON.stringify(restTab));
        }
      });

      setOriginalRowData((prev) => ({ ...prev, ...newOriginalRows }));
      setOriginalTabData((prev) => ({ ...prev, ...newOriginalTabs }));
    }
  }, [pcRows, rowsBodyData]);

  const isFieldMandatory = (fieldName) => mandatoryFields.includes(fieldName);

  const columns = [
    {
      field: "included",
      headerName: "",
      flex: 0.3,
      align: "center",
      headerAlign: "center",
      sortable: false,
      disableColumnMenu: true,
      renderHeader: () => {
        const allChecked =
          pcRows.length > 0 && pcRows.every((row) => row.included);
        const someChecked = pcRows.some((row) => row.included);

        return (
          <Checkbox
            indeterminate={!allChecked && someChecked}
            checked={allChecked}
            disabled={isDisabled || fieldDisable}
            onChange={(e) => {
              const checked = e.target.checked;
              const updatedRows = pcRows.map((row) => ({
                ...row,
                included: checked,
              }));
              dispatch(setPCRows(updatedRows));
            }}
          />
        );
      },
      renderCell: (params) => (
        <Checkbox
          checked={params.row.included}
          disabled={isDisabled || fieldDisable}
          onChange={(e) =>
            handleRowInputChange(e.target.checked, params.row.id, "included")
          }
        />
      ),
    },

    {
      field: "lineNumber",
      headerName: "Sl No",
      flex: 0.2,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        const rowIndex = pcRows.findIndex((row) => row.id === params.row.id);
        return <div>{(rowIndex + 1) * 10}</div>;
      },
    },
    {
      field: "controllingArea",
      flex: 0.6,
      headerAlign: "center",
      renderHeader: () => (
        <span>
          {t("Controlling Area")}
          {isFieldMandatory("controllingArea") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={dropdownDataCOA || []}
            value={
              dropdownDataCOA?.find(
                (item) => item.code === params.row.controllingArea
              ) || null
            }
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "controllingArea")
            }
            placeholder={t("Select Controlling Area")}
            disabled={isDisabled || fieldDisable}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "companyCode",
      headerName: "Company Code",
      flex: 0.6,
      headerAlign: "center",
      renderHeader: () => (
        <span>
          {t("Company Code")}
          {isFieldMandatory("companyCode") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={dropdownDataCompany || []}
            value={
              dropdownDataCompany?.find(
                (opt) => opt.code === params.row.companyCode
              ) || null
            }
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "companyCode")
            }
            placeholder={t("Select Company Code")}
            disabled={isDisabled || fieldDisable}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },

    {
      field: "profitCenterNumber",
      flex: 0.7,
      headerAlign: "center",
      renderHeader: () => (
        <span>
          {t("Profit Center Number")}
          {isFieldMandatory("profitCenterNumber") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),

      renderCell: (params) => {
        const rawCompanyCode = params.row.companyCode;
        const companyCode =
          typeof rawCompanyCode === "string"
            ? rawCompanyCode
            : typeof rawCompanyCode === "object" && rawCompanyCode !== null
            ? rawCompanyCode.value ?? ""
            : "";
        const prefix = companyCode ? `P${companyCode}` : "";
        const fullValue = params.row.profitCenterNumber || "";
        const suffix = fullValue.startsWith(prefix)
          ? fullValue.slice(prefix.length)
          : "";

        const isInvalid =
          fullValue.length > 0 &&
          (fullValue.length !== 10 || !/^[a-zA-Z0-9]+$/.test(fullValue));

        const handleKeyDown = (e) => {
          const allowedKeys = [
            "Backspace",
            "Delete",
            "Tab",
            "Escape",
            "Enter",
            "ArrowLeft",
            "ArrowRight",
          ];

          if (allowedKeys.includes(e.key)) return;

          // Only allow numeric characters (0–9)
          if (!/^[0-9]$/.test(e.key)) {
            e.preventDefault();
          }
        };

        const handlePaste = (e) => {
          const paste = e.clipboardData.getData("text");
          if (!/^[0-9]+$/.test(paste)) {
            e.preventDefault();
          }
        };

        return (
          <Box sx={{ position: "relative", width: "100%" }}>
            <TextField
              value={suffix}
              onChange={(e) => {
                const cleanSuffix = e.target.value
                  .replace(/[^0-9]/g, "") // Allow only digits
                  .slice(0, 10 - prefix.length); // Respect total 10-char limit

                handleRowInputChange(
                  prefix + cleanSuffix,
                  params.row.id,
                  "profitCenterNumber"
                );
              }}
              onKeyDown={handleKeyDown}
              onPaste={handlePaste}
              variant="outlined"
              size="small"
              fullWidth
              inputProps={{
                maxLength: 10 - prefix.length,
                style: {
                  paddingLeft: `${prefix.length + 2}ch`, // offset for prefix
                  fontSize: "0.875rem",
                  height: "35px",
                  boxSizing: "border-box",
                  display: "flex",
                  alignItems: "center",
                },
              }}
              disabled={isDisabled || fieldDisable}
              sx={{
                "& .MuiOutlinedInput-root": {
                  height: "35px",
                },
                "& .MuiInputBase-input": {
                  fontFamily: "inherit",
                  fontWeight: 500,
                },
              }}
            />
            {/* Render prefix absolutely over input */}
            <Box
              sx={{
                position: "absolute",
                top: "50%",
                left: "14px",
                transform: "translateY(-50%)",
                pointerEvents: "none",
                fontSize: "0.875rem",
                fontWeight: 500,
                fontFamily: "inherit",
                color: "rgba(0, 0, 0, 0.7)",
              }}
            >
              {prefix}
            </Box>
            {isInvalid && (
              <Box
                sx={{
                  position: "absolute",
                  bottom: -1,
                  left: 14,
                  color: "red",
                  fontSize: "10px",
                  pointerEvents: "none",
                }}
              >
                Must be 10 alphanumeric
              </Box>
            )}
          </Box>
        );
      },
    },

    {
      field: "longDescription",
      flex: 1.3,
      headerAlign: "center",
      renderHeader: () => (
        <span>
          {t("Long Description")}
          {isFieldMandatory("longDescription") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        const value = params.row.longDescription || "";

        return (
          <TextField
            value={value}
            onChange={(e) => {
              handleRowInputChange(
                e.target.value.toUpperCase(),
                params.row.id,
                "longDescription"
              );
            }}
            disabled={isDisabled}
            variant="outlined"
            size="small"
            placeholder="Enter Long Description"
            fullWidth
            sx={{
              "& .MuiInputBase-input": {
                padding: "10px 14px",
              },
              "& .MuiInputBase-root.Mui-disabled": {
                "& > input": {
                  WebkitTextFillColor: colors.black.dark,
                  color: colors.black.dark,
                },
              },
            }}
          />
        );
      },
    },
    {
      field: "businessSegment",
      flex: 0.7,
      headerAlign: "center",
      renderHeader: () => (
        <span>
          {t("Business Segment")}
          {isFieldMandatory("businessSegment") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={dropdownDataBusinessSegment || []}
            value={params.row.businessSegment}
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "businessSegment")
            }
            placeholder={t("Select Business Segment")}
            disabled={isDisabled || fieldDisable}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      flex: 0.3,
      headerAlign: "center",
      renderHeader: () => (
        <span style={{ fontWeight: "bold" }}>{t("Action")}</span>
      ),
      renderCell: (params) => {
        const rowId = params.row.id;
        const rowData = {
          id: rowId,
          ...rowsBodyData[rowId],
        };

        const validateStatus = getValidationStatus(rowId);

        const handleValidateClick = (e) => {
          e.stopPropagation();
          handleValidate(params.row, rowData, mandatoryFieldsConfig);
        };

        return (
          <Box>
            <Tooltip
              title={
                validateStatus === "success"
                  ? "Validated Successfully"
                  : validateStatus === "error"
                  ? "Validation Failed"
                  : "Click to Validate"
              }
            >
              <IconButton
                onClick={handleValidateClick}
                color={validateStatus}
                disabled={isDisabled}
              >
                {validateStatus === "error" ? (
                  <CancelOutlinedIcon />
                ) : (
                  <TaskAltIcon />
                )}
              </IconButton>
            </Tooltip>
          </Box>
        );
      },
    },
  ];

  useEffect(() => {
    if (selectedControllingAreaCode && selectedCompanyCode) {
      fetchProfitCenters();
    }
  }, [selectedControllingAreaCode, selectedCompanyCode]);

  const fetchProfitCenters = () => {
    if (!selectedControllingAreaCode || !selectedCompanyCode) return;

    const payload = {
      controllingArea: selectedControllingAreaCode,
      companyCode: selectedCompanyCode,
      top: "100",
      skip: "0",
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCentersNo`,
      "post",
      (data) => {
        if (Array.isArray(data.body?.list)) {
          const profitCenters = data.body.list
            .map((item) => item.code)
            .filter((code, i, self) => code && self.indexOf(code) === i);

          setProfitCenterOptions(
            profitCenters.map((code) => ({ code, desc: code }))
          );
        }
      },
      (err) => {
        console.error("Profit Center fetch failed", err);
      },
      payload
    );
  };

  const handleSnackBarClose = () => {
    setOpenSnackBar(false);
  };

  const handleDialogClose = () => {
    dispatch(setOpenDialog(false));
  };

  const toggleGridZoom = () => {
    setIsGridZoomed(!isGridZoomed);
    if (isTabsZoomed) setIsTabsZoomed(false);
  };

  const toggleTabsZoom = () => {
    setIsTabsZoomed(!isTabsZoomed);
    if (isGridZoomed) setIsGridZoomed(false);
  };

  useEffect(() => {
    if (validatedRows && Object.keys(validatedRows).length > 0) {
      localStorage.setItem("validatedRows_PC", JSON.stringify(validatedRows));
    }
  }, [validatedRows]);

  useEffect(() => {
    if (!Array.isArray(pcRows) || pcRows.length === 0) {
      setIsAddRowEnabled(true);
      setValidateEnabled(false);
      return;
    }

    const allRowsValidatedAndClean = pcRows.every((row) => {
      const rowId = row?.id;
      if (!rowId) {
        return false;
      }

      const isValidated = validatedRows?.[String(rowId)] === true;

      const isClean = !isRowDirty(rowId);

      return isValidated && isClean;
    });

    setIsAddRowEnabled(allRowsValidatedAndClean);
    setValidateEnabled(allRowsValidatedAndClean);
  }, [pcRows, rowsBodyData, validatedRows, originalRowData, originalTabData]);

  const mandatoryFieldsConfig =
    profitCenterTabs[selectedTab]?.data["General Data"] || [];

  const validateSingleRow = (row, tab, config) => {
    const missing = [];
    const lineNumber =
      row?.lineNumber || pcRows.findIndex((r) => r.id === row.id) + 1;

    config.forEach((field) => {
      if (field.visibility === "Mandatory") {
        const value = tab[field.jsonName];
        if (
          value === null ||
          value === undefined ||
          (typeof value === "string" && value.trim() === "")
        ) {
          missing.push(`Line ${lineNumber} - ${field.fieldName}`);
        }
      }
    });

    const headerMap = {
      companyCode: "Company Code",
      profitCenterNumber: "Profit Center Number",
      businessSegment: "Business Segment",
      controllingArea: "Controlling Area",
      longDescription: "Long Description",
    };

    mandatoryFields.forEach((field) => {
      const value = row[field];
      const displayName = headerMap[field] || field;

      if (
        value === null ||
        value === undefined ||
        (typeof value === "string" && value.trim() === "")
      ) {
        missing.push(`Line ${lineNumber} - ${displayName}`);
      } else if (
        field === "profitCenterNumber" &&
        (value.length !== 10 || !/^[a-zA-Z0-9]+$/.test(value))
      ) {
        missing.push(`Line ${lineNumber} - ${displayName}`);
      }
    });

    return {
      missing,
      status: missing.length > 0 ? "error" : "success",
    };
  };

  const checkDuplicateValidation = (pcRows, rowsBodyData) => {
    const shortTextMap = {};
    const longTextMap = {};
    const profitCenterNumberMap = {};

    pcRows.forEach((row, index) => {
      const tab = rowsBodyData[row.id];

      const lineNumber = row.lineNumber || index + 1;

      const shortText = tab?.ProfitCenterName?.trim();
      const longText = tab?.Description?.trim();
      const profitCenterNumber = row?.profitCenterNumber?.trim();

      if (shortText) {
        if (!shortTextMap[shortText]) {
          shortTextMap[shortText] = [];
        }
        shortTextMap[shortText].push(lineNumber);
      }

      if (longText) {
        if (!longTextMap[longText]) {
          longTextMap[longText] = [];
        }
        longTextMap[longText].push(lineNumber);
      }

      if (profitCenterNumber) {
        if (!profitCenterNumberMap[profitCenterNumber]) {
          profitCenterNumberMap[profitCenterNumber] = [];
        }
        profitCenterNumberMap[profitCenterNumber].push(lineNumber);
      }
    });

    const shortTextDuplicates = Object.entries(shortTextMap)
      .filter(([_, lines]) => lines.length > 1)
      .map(([text, lines]) => ({
        type: "Short Description",
        value: text,
        lines,
      }));

    const longTextDuplicates = Object.entries(longTextMap)
      .filter(([_, lines]) => lines.length > 1)
      .map(([text, lines]) => ({
        type: "Long Description",
        value: text,
        lines,
      }));

    const numberDuplicates = Object.entries(profitCenterNumberMap)
      .filter(([_, lines]) => lines.length > 1)
      .map(([text, lines]) => ({
        type: "Profit Center Number",
        value: text,
        lines,
      }));

    return [...shortTextDuplicates, ...longTextDuplicates, ...numberDuplicates];
  };

  const validateAllRows = () => {
    let allMissing = [];
    let duplicates = "";

    pcRows.forEach((row) => {
      const tab = rowsBodyData[row.id]; // Assuming tabData holds tab info per row
      const { missing, status } = validateSingleRow(
        row,
        tab,
        mandatoryFieldsConfig
      );
      duplicates = checkDuplicateValidation(pcRows, rowsBodyData);

      dispatch(setValidatedStatus({ rowId: row.id, status }));

      if (status === "error") {
        allMissing.push(...missing);
      } else {
        // Optional: store original validated data
        setOriginalRowData((prev) => ({
          ...prev,
          [row.id]: JSON.parse(JSON.stringify(row)),
        }));
        setOriginalTabData((prev) => {
          const { id, ...restTab } = tab;
          return {
            ...prev,
            [row.id]: JSON.parse(JSON.stringify(restTab)),
          };
        });
        dispatch(setValidatedRows({ rowId: row.id }));
      }
    });

    if (allMissing.length > 0) {
      const uniqueMissing = [...new Set(allMissing)];
      setMissingFields(uniqueMissing);
      setMissingFieldsDialogOpen(true);
    } else {
      if (duplicates.length > 0) {
        setDuplicateTextDetails(duplicates);
        setDuplicateTextDialogOpen(true);
        return;
      }
      validateAllRowsafterAllcheck();
      setAlertType("success");
      setAlertMsg("All Rows Validated Successfully");

      setOpenSnackBar(true);
      setCompleted([true, false]);
      setIsAttachmentTabEnabled(true);
    }
  };

  const validateAllRowsafterAllcheck = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForPC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);

      if (data?.statusCode === 200 || data?.statusCode === 201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === 500 || data?.statusCode === 501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
        // fallback - maybe show a snackbar or console log
        setSnackbarOpen(true);
        setAlertMsg("Unexpected response received.");
      }
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while validating the request");
      console.error("Error saving draft:", error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/validateMassProfitCenter`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const handleCloseDialog = () => {
    setMissingFieldsDialogOpen(false);
  };

  const handleRowInputChange = (value, id, field) => {
    if (field === "controllingArea") {
      const controllingAreaValue = value?.code || value;

      // 1. Update Redux row field (persisted)
      dispatch(
        updateModuleFieldData({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "controllingArea",
          data: controllingAreaValue,
          viewID: null,
        })
      );

      // 2. Update screen table (temporary)
      const updatedRows = pcRows.map((row) => {
        if (row.id === id) {
          return {
            ...row,
            controllingArea: controllingAreaValue,
          };
        }
        return row;
      });
      dispatch(setPCRows(updatedRows));

      // 3. Optional
      setDirtyRows((prev) => ({ ...prev, [id]: true }));

      return;
    }

    if (field === "companyCode") {
      const companyCodeValue = value?.code || value;

      // Update Company Code
      dispatch(
        updateModuleFieldData({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "CompanyCode",
          data: companyCodeValue,
          viewID: "Comp Codes",
        })
      );

      // Auto-set Profit Center Number to P<CompanyCode>
      const autoProfitCenter = `P${companyCodeValue}`;
      dispatch(
        updateModuleFieldData({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "profitCenterNumber",
          data: autoProfitCenter,
          viewID: "Comp Codes",
        })
      );

      // Update both fields locally in pcRows
      const updatedRows = pcRows.map((row) => {
        if (row.id === id) {
          return {
            ...row,
            companyCode: companyCodeValue,
            profitCenterNumber: autoProfitCenter,
          };
        }
        return row;
      });

      dispatch(setPCRows(updatedRows));
      setDirtyRows((prev) => ({ ...prev, [id]: true }));
      return; // Exit early to avoid default map below
    }

    if (field === "longDescription") {
      dispatch(
        updateModuleFieldData({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "Description",
          data: value,
          viewID: "Basic Data",
        })
      );
      // Note:changelog logic
      // {
      //   isrequestId &&
      //     !CHANGE_LOG_STATUSES.includes(requestStatus) &&
      //     updateChangeLogGl({
      //       uniqueId: selectedRowId || selectedRow?.id,
      //       viewName: "Basic Data",
      //       plantData: "",
      //       fieldName: "Long Text",
      //       jsonName: "Description",
      //       currentValue: value,
      //       isrequestId: initialPayload?.RequestId,
      //       childRequestId: isrequestId,
      //     });
      // }
    }

    const updatedRows = pcRows.map((row) =>
      row.id === id ? { ...row, [field]: value } : row
    );
    dispatch(setPCRows(updatedRows));
    setDirtyRows((prev) => ({ ...prev, [id]: true }));
  };

  const handleRowClick = (params) => {
    const clickedRow = params.row;

    setSelectedRow(clickedRow);
    dispatch(setSelectedRowId(clickedRow?.ProfitCenterID));
  };

  const handleAddRow = () => {
    const id = uuidv4();
    setNewRowId(id);
    setIsAddRowMode(true);
    setWithReference("yes"); // Reset radio to default

    // ✅ Only reset dropdowns
    setSelectedControllingAreaCode("");
    setSelectedCompanyCode("");
    setSelectedProfitCenterCode("");
    setSelectedMatLines([]);

    // ✅ Open dialog (for reference selection or copy)
    dispatch(setOpenDialog(true));
  };

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  useEffect(() => {
    setSelectedRow(pcRows[0]);
    getCompanyCodePC();
  }, []);

  const getCompanyCodePC = () => {
    const hSuccess = (data) => {
      setDropdownDataCompany(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "CompanyCode", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCompCodeBasedOnControllingArea?controllingArea=ETCA&rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };

  const getBusinessSegment = () => {
    const hSuccess = (data) => {
      const transformed = data.body.map((item) => ({
        code: item,
        desc: item,
      }));

      setDropdownDataBusinessSegment(transformed);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "businessSegment", data: transformed },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getBusinessSegment`,
      "get",
      hSuccess,
      hError
    );
  };

  const getProfitCenterGrp = (coa) => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "PrctrHierGrp",
          data: data?.body || [],
          keyName2: selectedRowId || selectedRow?.id,
        })
      );
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCtrGroup?controllingArea=ETCA`,
      "get",
      hSuccess,
      hError
    );
  };

  const getFormPlanningFrp = () => {
    const hSuccess = (data) => {
      setDropdownDataFormPlanning(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Template", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getFormPlanningTemp`,
      "get",
      hSuccess,
      hError
    );
  };
  const getControllingArea = () => {
    const hSuccess = (data) => {
      const coaData = data?.body || [];

      // Save in local state for dropdown rendering
      setDropdownDataCOA(coaData);

      // Also store in Redux for global availability
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "controllingArea", data: coaData },
      });

      // Optional: if a row is already selected, trigger dependent dropdown
      const controllingAreaCode = selectedRow?.controllingArea;
      if (controllingAreaCode) {
        getProfitCenterGrp();
      }
    };

    const hError = (error) => {
      console.error("Failed to fetch Controlling Area:", error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getControllingArea`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    if (!Array.isArray(dropdownDataCOA) || dropdownDataCOA.length === 0) {
      if (pcRows?.length > 0) {
        getControllingArea();
      }
    }
  }, [dropdownDataCOA, pcRows]);

  const getTaxJurisdiction = () => {
    const hSuccess = (data) => {
      setDropdownDataTaxJur(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "TaxJurisdiction", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getJurisdiction`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    getCountryData();
    // getProfitCenterGrp();
    getFormPlanningFrp();
    getControllingArea();
    getTaxJurisdiction();
    // getBusSeg();
    getBusinessSegment();

    if (pcRows.length > 0) {
      const controllingArea = pcRows[0]?.controllingArea; // or use selectedRow?.controllingArea
      if (controllingArea) {
        getProfitCenterGrp(controllingArea); // ✅ pass the controlling area
      }
    }
  }, [pcRows.length]);

  useEffect(() => {
    if (pcRows.length > 0) {
      const controllingArea = pcRows[0]?.controllingArea;
      if (controllingArea) {
        getProfitCenterGrp(controllingArea);
      }
    }
  }, [pcRows, dropdownDataCOA]);

  useEffect(() => {
    if (selectedRow?.controllingArea) {
      const timeout = setTimeout(() => {
        getProfitCenterGrp(selectedRow.controllingArea);
      }, 500); // Optional: add delay to debounce

      return () => clearTimeout(timeout); // Cleanup on row change
    }
  }, [selectedRow?.controllingArea]);

  const getCountryData = () => {
    const hSuccess = (data) => {
      setDropdownDataCountry(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Country", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCountryOrReg`,
      "get",
      hSuccess,
      hError
    );
  };

  const [rowRegionData, setRowRegionData] = useState({});

  const getRegionBasedOnCountry = (countryCode, fieldData, rowId) => {
    const hSuccess = (data) => {
      // Store region data for this specific row
      setRowRegionData((prev) => ({
        ...prev,
        [rowId]: data.body,
      }));

      // Also update the general dropdown data for UI rendering
      setDropdownDataRegion(data.body);

      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Region", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getRegionBasedOnCountry?country=${countryCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getSegment = () => {
    const hSuccess = (data) => {
      setDropdownDataSegment(data.body);
      dispatch(setDropDown({ keyName: "Segment", data: data.body }));
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getSegment`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getSegment();
  }, []);

  const getLanguage = () => {
    const hSuccess = (data) => {
      setDropdownDataLanguage(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Language", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getLanguage();
  }, []);

  const handleSaveAsDraft = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForPC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("Profit Centers Submission saved as draft.");
      setOpenSnackBar(true);
      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
      console.error("Error saving draft:", error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const handleSubmitForReview = () => {
    setLoaderMessage("");
    setBlurLoading(true);
    const finalPayload = createPayloadForPC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);

      if (data?.statusCode === 200 || data?.statusCode === 201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === 500 || data?.statusCode === 501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
        // fallback - maybe show a snackbar or console log
        setSnackbarOpen(true);
        setAlertMsg("Unexpected response received.");
      }
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while submit for review");
      console.error("Error saving draft:", error);
    };

    // 🔄 API call
    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersSubmitForReview`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const handleValidateAndSyndicate = (type) => {
    setBlurLoading(true);
    const finalPayload = createPayloadForPC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);

      if (data?.statusCode === 200 || data?.statusCode === 201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === 500 || data?.statusCode === 501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
        // fallback - maybe show a snackbar or console log
        setSnackbarOpen(true);
        setAlertMsg("Unexpected response received.");
      }
    };
    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while validating the request");
      console.error("Error saving draft:", error);
    };

    doAjax(
      type === "VALIDATE"
        ? `/${destination_ProfitCenter_Mass}/massAction/validateMassProfitCenter`
        : `/${destination_ProfitCenter_Mass}/massAction/createProfitCentersApproved`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const handleSubmitForApprove = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForPC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);

      if (data?.statusCode === 200 || data?.statusCode === 201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === 500 || data?.statusCode === 501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
        // fallback - maybe show a snackbar or console log
        setSnackbarOpen(true);
        setAlertMsg("Unexpected response received.");
      }
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
      console.error("Error saving draft:", error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersApprovalSubmit`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const handleSendBack = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForPC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);

      if (data?.statusCode === 200 || data?.statusCode === 201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === 500 || data?.statusCode === 501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
        // fallback - maybe show a snackbar or console log
        setSnackbarOpen(true);
        setAlertMsg("Unexpected response received.");
      }
    };
    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
      console.error("Error saving draft:", error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersSendForCorrection`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const handleCorrection = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForPC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);

      if (data?.statusCode === 200 || data?.statusCode === 201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === 500 || data?.statusCode === 501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
        // fallback - maybe show a snackbar or console log
        setSnackbarOpen(true);
        setAlertMsg("Unexpected response received.");
      }
    };
    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while sending for correction");
      console.error("Error saving draft:", error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersSendForReview`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const handleRejectAndCancel = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForPC(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(data?.message ?? "Profit Centers Rejected !");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while rejecting the request");
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersRejected`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const addProfitCenterCopy = (rowId, latestRows) => {
    setBlurLoading(true);

    const payload = {
      coAreaPCs: [
        {
          controllingArea: selectedControllingAreaCode,
          profitCenter: selectedProfitCenterCode,
        },
      ],
    };

    const hSuccess = (data) => {
      const rawData = data?.body || [];
      const response = rawData[0];

      if (!response) {
        setBlurLoading(false);
        handleDialogClose();
        return;
      }

      const country = response?.addressTabDto?.Country || "";
      const region = response?.addressTabDto?.Regio || "";

      // ✅ Use latestRows instead of pcRows
      const updatedRows = latestRows.map((row) =>
        row.id === rowId
          ? {
              ...row,
              controllingArea: response.controllingArea || "",
              businessSegment: response.basicDataTabDto?.Segment || "",
              companyCode: response.compCodesTabDto?.CompanyCode?.[0] || "",
            }
          : row
      );

      dispatch({
        type: "profitCenter/setProfitCenterRows",
        payload: updatedRows,
      });

      const updatedTabs = {
        [rowId]: {
          PersonResponsible: response.basicDataTabDto?.PersonResponsible || "",
          PrctrHierGrp: response.basicDataTabDto?.PrctrHierGrp || "",
          Segment: response.basicDataTabDto?.Segment || "",
          CompanyCode: response.compCodesTabDto?.CompanyCode || [],
          Language: response.communicationTabDto?.Language || "",
          Country: country,
          Street: response.addressTabDto?.Street || "",
          City: response.addressTabDto?.City || "",
          Regio: region,
          LockIndicator: !!response.indicatorsTabDto?.LockIndicator,
          ReqCreatedBy: response.historyTabDto?.ReqCreatedBy || "",
          ReqCreatedOn: response.historyTabDto?.ReqCreatedOn || "",
        },
      };

      dispatch({
        type: "profitCenter/setProfitCenterTab",
        payload: updatedTabs,
      });

      dispatch(
        updateModuleFieldData({
          uniqueId: rowId,
          keyName: "CompanyCode",
          data: response?.compCodesTabDto?.CompanyCode?.[0] || "",
          viewID: "Comp Codes",
        })
      );
      dispatch(
        updateModuleFieldData({
          uniqueId: rowId,
          keyName: "profitCenterNumber",
          data: response?.profitCenter || "",
          viewID: "Comp Codes",
        })
      );
      dispatch(
        updateModuleFieldData({
          uniqueId: rowId,
          keyName: "Country",
          data: country,
          viewID: "Address",
        })
      );
      dispatch(
        updateModuleFieldData({
          uniqueId: rowId,
          keyName: "Regio",
          data: region,
          viewID: "Address",
        })
      );

      if (country) getRegionBasedOnCountry(country, null, rowId);

      setProfitcenterResponse(rawData);
      setBlurLoading(false);
      handleDialogClose();
      getProfitCenterGrp();
    };

    const hError = (error) => {
      console.error("Error fetching profit center data", error);
      setBlurLoading(false);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCentersData`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleProceed = () => {
    dispatch(setOpenDialog(false));
    const id = uuidv4();
    // setLineNumberCounter(lineNumberCounter + 1)
    const lastLineNumber = Math.max(
      0,
      ...pcRows.map((row) => row.lineNumber || 0)
    );
    const newLineNumber = lastLineNumber + 10;

    if (withReference === "yes") {
      if (selectedMatLines?.length) {
        const matlines = selectedMatLines[0];
        
        const selectedIndex = Number(matlines.code / 10 - 1);
       
        const selectedRow = pcRows[selectedIndex];

        if (!selectedRow) {
          return;
        }

        const newRow = {
          id,
          controllingArea: selectedRow?.controllingArea || "",
          profitCenterNumber: "", // Clear for new entry
          longDescription: "",
          businessSegment: selectedRow?.businessSegment || "",
          companyCode: selectedRow?.companyCode || "",
          included: true,
          isNew: true,
          lineNumber: newLineNumber,
        };

        dispatch(setPCRows([...pcRows, newRow]));
        dispatch(setValidatedRows({ rowId: id }));
        setIsAddRowEnabled(false); // ✅ Disable Add Button

        // ✅ COPY TAB DATA from rowBodyData using selectedRow.id
        const selectedTabDataFromRowBody = rowsBodyData?.[selectedRow.id];
        if (selectedTabDataFromRowBody) {
          const mappedTabData = {
            PersonResponsible:
              selectedTabDataFromRowBody?.PersonResponsible || "",
            PrctrHierGrp: selectedTabDataFromRowBody?.PrctrHierGrp || "",
            Segment: selectedTabDataFromRowBody?.Segment || "",
            CompanyCode: selectedTabDataFromRowBody?.CompanyCode || "",
            Language: selectedTabDataFromRowBody?.Language || "",
            Country: selectedTabDataFromRowBody?.Country || "",
            Street: selectedTabDataFromRowBody?.Street || "",
            City: selectedTabDataFromRowBody?.City || "",
            Regio: selectedTabDataFromRowBody?.Regio || "",
            LockIndicator: !!selectedTabDataFromRowBody?.LockIndicator,
            ReqCreatedBy: selectedTabDataFromRowBody?.ReqCreatedBy || "",
            ReqCreatedOn: selectedTabDataFromRowBody?.ReqCreatedOn || "",
          };

          dispatch(setProfitCenterTab({ [id]: mappedTabData }));

          // Also update form fields via updateModuleFieldData
          dispatch(
            updateModuleFieldData({
              uniqueId: id,
              keyName: "CompanyCode",
              data: mappedTabData.CompanyCode,
              viewID: "Comp Codes",
            })
          );
          dispatch(
            updateModuleFieldData({
              uniqueId: id,
              keyName: "Country",
              data: mappedTabData.Country,
              viewID: "Address",
            })
          );
          dispatch(
            updateModuleFieldData({
              uniqueId: id,
              keyName: "Regio",
              data: mappedTabData.Regio,
              viewID: "Address",
            })
          );
          dispatch(
            updateModuleFieldData({
              uniqueId: id,
              keyName: "profitCenterNumber",
              data: "", // Reset
              viewID: "Comp Codes",
            })
          );
        }

        return;
      }

      // ✅ When in "Add Row" mode but no line selected — use blank + API fill
      if (isAddRowMode) {
        const newRow = {
          id,
          controllingArea: "",
          profitCenterNumber: "",
          lineNumber: newLineNumber,
          longDescription: "",
          businessSegment: "",
          companyCode: "",
          included: true,
          isNew: true,
        };

        const newRowList = [...pcRows, newRow];
        dispatch(setPCRows(newRowList));
        dispatch(setValidatedRows({ rowId: id }));
        setIsAddRowEnabled(false); // ✅ Disable Add Button
        addProfitCenterCopy(id, newRowList); // Call API to fill
      } else {
        // ✅ Default fallback: no rows exist, so create one and fill via API
        const newRow = {
          id,
          controllingArea: "",
          profitCenterNumber: "",
          longDescription: "",
          businessSegment: "",
          lineNumber: newLineNumber,
          companyCode: "",
          included: true,
          isNew: true,
        };

        const newRowList = [...pcRows, newRow];
        dispatch(setPCRows(newRowList));
        dispatch(setValidatedRows({ rowId: id }));
        setIsAddRowEnabled(false); // ✅ Disable Add Button
        addProfitCenterCopy(id, newRowList);
      }
    } else {
      // Without Reference — add blank row only if existing rows >= 0
      if (pcRows.length >= 0) {
        const newRow = {
          id,
          controllingArea: "",
          profitCenterNumber: "",
          longDescription: "",
          businessSegment: "",
          companyCode: "",
          lineNumber: newLineNumber,
          included: true,
          isNew: true,
        };

        dispatch(setPCRows([...pcRows, newRow]));
        dispatch(setValidatedRows({ rowId: id }));
        setIsAddRowEnabled(false); // ✅ Disable Add Button
      } else {
        console.warn("No existing valid data in rows. Skipping row addition.");
      }
    }
  };

  return (
    <div>
      <ReusableSnackBar
        openSnackBar={openSnackBar}
        alertMsg={alertMsg}
        handleSnackBarClose={handleSnackBarClose}
        alertType={alertType}
        isLoading={isLoading}
      />

      <SuccessDialog
        open={successDialogOpen}
        onClose={() => setSuccessDialogOpen(false)}
        title={dialogData.title}
        message={dialogData.message}
        subText={dialogData.subText}
        buttonText={dialogData.buttonText}
        redirectTo={dialogData.redirectTo}
      />

      {error && (
        <Typography color="error">{t("Error loading data")}</Typography>
      )}
      <div
        style={{ padding: "0", width: "100%", margin: "0", marginTop: "20px" }}
      >
        <Box
          sx={{
            position: isGridZoomed ? "fixed" : "relative",
            top: isGridZoomed ? 0 : "auto",
            left: isGridZoomed ? 0 : "auto",
            right: isGridZoomed ? 0 : "auto",
            bottom: isGridZoomed ? 0 : "auto",
            width: isGridZoomed ? "100vw" : "100%",
            height: isGridZoomed ? "100vh" : "auto",
            zIndex: isGridZoomed ? 1004 : 1,
            backgroundColor: isGridZoomed ? "white" : "transparent",
            padding: isGridZoomed ? "20px" : "0",
            display: "flex",
            flexDirection: "column",
            boxShadow: isGridZoomed
              ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
              : "none",
            transition: "all 0.3s ease",
            borderRadius: "8px",
            border: "1px solid #e0e0e0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "8px 16px",
              backgroundColor: "#f5f5f5",
              borderRadius: "8px 8px 0 0",
            }}
          >
            <Typography variant="h6">{t("List of Profit Centers")}</Typography>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Button
                variant="contained"
                color="primary"
                size="small"
                onClick={handleAddRow}
                disabled={!isAddRowEnabled || isDisabled || fieldDisable}
              >
                {t("+ Add")}
              </Button>
              <Tooltip
                title={isGridZoomed ? "Exit Zoom" : "Zoom In"}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleGridZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isGridZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
            <div style={{ height: "100%" }}>
              <div>
                <ReusableDataTable
                  isLoading={loading}
                  rows={pcRows}
                  columns={columns}
                  pageSize={10}
                  tempheight={"50vh"}
                  getRowIdValue={"id"}
                  status_onRowSingleClick={true}
                  callback_onRowSingleClick={handleRowClick}
                  getRowClassName={(params) =>
                    selectedRow?.id === params.row.id ? "Mui-selected" : ""
                  }
                />
              </div>
            </div>
          </div>
        </Box>
      </div>

      {/* with and without reference */}

      {openDialog && (
        <Dialog
          fullWidth
          open={openDialog}
          maxWidth="lg"
          onClose={(event, reason) => {
            if (reason !== "backdropClick") {
              handleDialogClose();
            }
          }}
          sx={{
            "&::webkit-scrollbar": {
              width: "1px",
            },
          }}
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF",
              // borderBottom: "1px solid grey",
              display: "flex",
            }}
          >
            <Typography variant="h6">Add New ProfitCenter</Typography>
          </DialogTitle>
          <DialogContent
            sx={{
              padding: ".5rem 1rem",
              alignItems: "center",
              justifyContent: "center",
              margin: "0px 25px",
            }}
          >
            <FormControl component="fieldset" sx={{ paddingBottom: "2%" }}>
              <FormLabel
                component="legend"
                sx={{
                  padding: "15px 0px",
                  fontWeight: "600",
                  fontSize: "15px",
                }}
              >
                How would you like to proceed?
              </FormLabel>
              <RadioGroup
                row
                aria-label="profit-center-number"
                name="profit-center-number"
                value={withReference}
                onChange={(event) => {
                  const value = event.target.value;
                  setWithReference(value);

                  // Clear dependent states when switching
                  if (value === "no") {
                    setSelectedControllingAreaCode("");
                    setSelectedCompanyCode("");
                    setSelectedProfitCenterCode("");
                    setSelectedMatLines([]);
                    setWithRefValues({});
                    setSelectedMaterials(null);
                  }
                }}
              >
                <FormControlLabel
                  value="yes"
                  control={<Radio />}
                  label="With Reference"
                />
                <FormControlLabel
                  value="no"
                  control={<Radio />}
                  label="Without Reference"
                />
              </RadioGroup>
            </FormControl>
            {withReference === "yes" && (
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Grid container spacing={2}>
                    <Grid item xs={3}>
                      <Typography variant="subtitle2" gutterBottom>
                        Controlling Area<span style={{ color: "red" }}>*</span>
                      </Typography>
                      <SingleSelectDropdown
                        options={dropdownDataCOA || []}
                        value={
                          dropdownDataCOA?.find(
                            (item) => item.code === selectedControllingAreaCode
                          ) || null
                        }
                        onChange={(newValue) => {
                          setSelectedMatLines([]);
                          setSelectedControllingAreaCode(newValue?.code || "");
                          getCompanyCode(newValue?.code, "ETP");
                        }}
                        placeholder="Select Controlling Area"
                        minWidth="90%"
                        listWidth={235}
                      />
                    </Grid>

                    <Grid item xs={3}>
                      <Typography variant="subtitle2" gutterBottom>
                        Company Code<span style={{ color: "red" }}>*</span>
                      </Typography>
                      <SingleSelectDropdown
                        options={dropdownDataCompany || []}
                        value={
                          dropdownDataCompany?.find(
                            (item) => item.code === selectedCompanyCode
                          ) || null
                        }
                        onChange={(newValue) => {
                          setSelectedMatLines([]);
                          setSelectedCompanyCode(newValue?.code || "");
                        }}
                        placeholder="Select Company Code"
                        minWidth="90%"
                        listWidth={235}
                      />
                    </Grid>

                    <Grid item xs={3}>
                      <Typography variant="subtitle2" gutterBottom>
                        Profitcenter Number
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <SingleSelectDropdown
                        options={profitCenterOptions}
                        value={
                          profitCenterOptions.find(
                            (item) => item.code === selectedProfitCenterCode
                          ) || null
                        }
                        onChange={(newValue) => {
                          setSelectedMatLines([]);
                          setSelectedProfitCenterCode(newValue?.code || "");
                        }}
                        placeholder="Select Profit Center"
                        minWidth="90%"
                        listWidth={235}
                      />
                    </Grid>

                    {pcRows.length > 0 && (
                      <>
                        <Grid item xs={1} sx={{ textAlign: "center" }}>
                          <Typography
                            variant="body1"
                            sx={{ fontWeight: "bold", color: "gray" }}
                          >
                            OR
                          </Typography>
                        </Grid>

                        <Grid item xs={3}>
                          <Typography variant="subtitle2" gutterBottom>
                            Line Number<span style={{ color: "red" }}>*</span>
                          </Typography>
                          <SingleSelectDropdown
                            options={pcRows.map((row, index) => ({
                              code: row.lineNumber,
                            }))}
                            value={selectedMatLines[0]}
                            onChange={(newValue) => {
                              setSelectedMatLines(newValue ? [newValue] : []);
                              setSelectedControllingAreaCode("");
                              setSelectedCompanyCode("");
                              setSelectedProfitCenterCode("");
                              setWithRefValues({});
                              setSelectedMaterials(null);
                            }}
                            minWidth={180}
                            listWidth={266}
                            placeholder={t("Select Line Number")}
                            disabled={selectedMaterials?.code}
                            getOptionLabel={(option) =>
                              option?.desc
                                ? `${option.code} - ${option.desc}`
                                : option?.code || ""
                            }
                            renderOption={(props, option) => (
                              <li {...props}>
                                <strong>{option?.code}</strong>
                                {option?.desc ? ` - ${option?.desc}` : ""}
                              </li>
                            )}
                            sx={{
                              minWidth: 270,
                              "& .MuiAutocomplete-popper": { minWidth: 306 },
                            }}
                          />
                        </Grid>
                      </>
                    )}
                  </Grid>
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{
                width: "max-content",
                textTransform: "capitalize",
              }}
              onClick={handleDialogClose}
              variant="outlined"
            >
              Cancel
            </Button>
            <Button
              className="button_primary--normal"
              type="save"
              disabled={
                withReference === "yes" &&
                !(
                  (selectedControllingAreaCode &&
                    selectedCompanyCode &&
                    selectedProfitCenterCode) ||
                  selectedMatLines?.length > 0
                )
              }
              onClick={handleProceed}
              variant="contained"
            >
              Proceed
            </Button>
          </DialogActions>
        </Dialog>
      )}

      <Dialog
        open={missingFieldsDialogOpen}
        onClose={handleCloseDialog}
        aria-labelledby="missing-fields-dialog-title"
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          id="missing-fields-dialog-title"
          sx={{
            backgroundColor: "#fff3e0",
            color: "#e65100",
            display: "flex",
            alignItems: "center",
            gap: 1,
            fontWeight: "bold",
          }}
        >
          <WarningAmberIcon fontSize="medium" />
          {t("Missing Mandatory Fields")}
        </DialogTitle>

        <DialogContent sx={{ pt: 2 }}>
          <Typography variant="body1" gutterBottom>
            {t("Please complete the following mandatory fields:")}
          </Typography>
          <List dense>
            {missingFields.map((field, index) => {
              const match = field.match(/^(Line \d+)( - .*)$/);
              return (
                <ListItem key={index} disablePadding>
                  <ListItemIcon sx={{ minWidth: 30 }}>
                    <WarningAmberIcon fontSize="small" color="warning" />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      match ? (
                        <>
                          <strong>{match[1]}</strong>
                          {match[2]}
                        </>
                      ) : (
                        field
                      )
                    }
                  />
                </ListItem>
              );
            })}
          </List>
        </DialogContent>

        <DialogActions sx={{ pr: 3, pb: 2 }}>
          <Button
            onClick={handleCloseDialog}
            variant="contained"
            color="warning"
            sx={{ textTransform: "none", fontWeight: 500 }}
          >
            {t("Close")}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={duplicateTextDialogOpen}
        onClose={() => setDuplicateTextDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          id="missing-fields-dialog-title"
          sx={{
            backgroundColor: "#fff3e0",
            color: "#e65100",
            display: "flex",
            alignItems: "center",
            gap: 1,
            fontWeight: "bold",
          }}
        >
          <WarningAmberIcon fontSize="medium" />
          {t("Duplicate Description")}
        </DialogTitle>

        <DialogContent dividers>
          {duplicateTextDetails.length === 0 ? (
            <Typography>No duplicates found.</Typography>
          ) : (
            <TableContainer component={Paper} elevation={0}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <strong>Type</strong>
                    </TableCell>
                    <TableCell>
                      <strong>Remarks</strong>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {duplicateTextDetails.map((dup, index) => (
                    <TableRow key={index}>
                      <TableCell>{dup.type}</TableCell>
                      <TableCell>
                        <strong>{dup.value}</strong> found in Line(s):{" "}
                        {dup.lines.join(", ")}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setDuplicateTextDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {selectedRow &&
        (reqBench === "true" && selectedRowId ? (
          <Box
            sx={{
              position: isTabsZoomed ? "fixed" : "relative",
              top: isTabsZoomed ? 0 : "auto",
              left: isTabsZoomed ? 0 : "auto",
              right: isTabsZoomed ? 0 : "auto",
              bottom: isTabsZoomed ? 0 : "auto",
              width: isTabsZoomed ? "100vw" : "100%",
              height: isTabsZoomed ? "100vh" : "auto",
              zIndex: isTabsZoomed ? 1004 : 1,
              backgroundColor: isTabsZoomed ? "white" : "transparent",
              padding: isTabsZoomed ? "20px" : "0",
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              boxShadow: isTabsZoomed
                ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
                : "none",
              transition: "all 0.3s ease",
              borderRadius: "8px",
              border: "1px solid #e0e0e0",
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: "8px 16px",
                borderRadius: "8px 8px 0 0",
              }}
            >
              <Typography variant="h6">{t(`${module} Details`)}</Typography>
              <Tooltip
                title={isTabsZoomed ? "Exit Zoom" : "Zoom In"}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleTabsZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isTabsZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ mt: 3 }}>
              <Tabs
                value={selectedTab}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
                aria-label="Request tabs"
                variant="scrollable"
                scrollButtons="auto"
                sx={{
                  top: 0,
                  position: "sticky",
                  zIndex: 1000,
                  backgroundColor: colors.background.container,
                  borderBottom: `1px solid ${colors.border.light}`,
                  "& .MuiTab-root": {
                    minHeight: "48px",
                    textTransform: "none",
                    fontSize: "14px",
                    fontWeight: 600,
                    color: colors.black.graphite,
                    "&.Mui-selected": {
                      color: colors.primary.main,
                      fontWeight: 700,
                    },
                    "&:hover": {
                      color: colors.primary.main,
                      opacity: 0.8,
                    },
                  },
                  "& .MuiTabs-indicator": {
                    backgroundColor: colors.primary.main,
                    height: "3px",
                  },
                }}
              >
                {profitCenterTabs.map((tab, index) => (
                  <Tab key={index} label={tab.tab} />
                ))}
              </Tabs>
              <Paper elevation={2} sx={{ p: 3, borderRadius: 4 }}>
                {profitCenterTabs[selectedTab] && (
                  <GenericTabsGlobal
                    key={
                      (selectedRow?.id || selectedRowId || pcRows[0]?.id) +
                      (errorFieldMap[
                        selectedRow?.id || selectedRowId || pcRows[0]?.id
                      ]?.join(",") || "")
                    }
                    disabled={isDisabled}
                    basicDataTabDetails={profitCenterTabs[selectedTab].data}
                    dropDownData={{
                      CompanyCode: dropdownDataCompany,
                      Country: dropdownDataCountry,
                      // "Segment": dropdownDataSegment,
                      Language: dropdownDataLanguage,
                      Template: dropdownDataFormPlanning,
                      TaxJurisdiction: dropdownDataTaxJur,
                      BusinessSegment: dropdownDataBusinessSegment,
                      controllingArea: dropdownDataCOA,
                    }}
                    activeViewTab={profitCenterTabs[selectedTab].tab}
                    uniqueId={selectedRow?.id || selectedRowId || pcRows[0]?.id}
                    selectedRow={selectedRow || {}}
                    fieldErrors={
                      errorFieldMap[
                        selectedRow?.id || selectedRowId || pcRows[0]?.id
                      ] || []
                    }
                  />
                )}
              </Paper>
            </Box>
          </Box>
        ) : (
          <Box
            sx={{
              position: isTabsZoomed ? "fixed" : "relative",
              top: isTabsZoomed ? 0 : "auto",
              left: isTabsZoomed ? 0 : "auto",
              right: isTabsZoomed ? 0 : "auto",
              bottom: isTabsZoomed ? 0 : "auto",
              width: isTabsZoomed ? "100vw" : "100%",
              height: isTabsZoomed ? "100vh" : "auto",
              zIndex: isTabsZoomed ? 1004 : 1,
              backgroundColor: isTabsZoomed ? "white" : "transparent",
              padding: isTabsZoomed ? "20px" : "0",
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              boxShadow: isTabsZoomed
                ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
                : "none",
              transition: "all 0.3s ease",
              borderRadius: "8px",
              border: "1px solid #e0e0e0",
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: "8px 16px",
                borderRadius: "8px 8px 0 0",
              }}
            >
              <Typography variant="h6">{t("View Details")}</Typography>
              <Tooltip
                title={isTabsZoomed ? "Exit Zoom" : "Zoom In"}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleTabsZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isTabsZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}>
              <Tabs
                value={selectedTab}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
                aria-label="Request tabs"
                variant="scrollable"
                scrollButtons="auto"
                sx={{
                  top: 0,
                  position: "sticky",
                  zIndex: 1000,
                  backgroundColor: colors.background.container,
                  borderBottom: `1px solid ${colors.border.light}`,
                  "& .MuiTab-root": {
                    minHeight: "48px",
                    textTransform: "none",
                    fontSize: "14px",
                    fontWeight: 600,
                    color: colors.black.graphite,
                    "&.Mui-selected": {
                      color: colors.primary.main,
                      fontWeight: 700,
                    },
                    "&:hover": {
                      color: colors.primary.main,
                      opacity: 0.8,
                    },
                  },
                  "& .MuiTabs-indicator": {
                    backgroundColor: colors.primary.main,
                    height: "3px",
                  },
                }}
              >
                {profitCenterTabs.map((tab, index) => (
                  <Tab key={index} label={tab.tab} />
                ))}
              </Tabs>

              <Paper elevation={2} sx={{ p: 3, borderRadius: 4 }}>
                {profitCenterTabs[selectedTab] && (
                  <GenericTabsGlobal
                    key={
                      (selectedRow?.id || selectedRowId || pcRows[0]?.id) +
                      (errorFieldMap[
                        selectedRow?.id || selectedRowId || pcRows[0]?.id
                      ]?.join(",") || "")
                    }
                    disabled={isDisabled}
                    basicDataTabDetails={profitCenterTabs[selectedTab].data}
                    dropDownData={{
                      CompanyCode: dropdownDataCompany,
                      Country: dropdownDataCountry,
                      // "Segment": dropdownDataSegment,
                      Language: dropdownDataLanguage,
                      Template: dropdownDataFormPlanning,
                      TaxJurisdiction: dropdownDataTaxJur,
                      businessSegment: dropdownDataBusinessSegment,
                    }}
                    activeViewTab={profitCenterTabs[selectedTab].tab}
                    uniqueId={selectedRow?.id || selectedRowId || pcRows[0]?.id}
                    selectedRow={selectedRow || {}}
                    fieldErrors={
                      errorFieldMap[
                        selectedRow?.id || selectedRowId || pcRows[0]?.id
                      ] || []
                    }
                  />
                )}
              </Paper>
              <Box
                sx={{
                  borderTop: "1px solid #e0e0e0",
                  padding: "16px",
                }}
              >
                <BottomNavGlobal
                  handleSaveAsDraft={handleSaveAsDraft}
                  handleSubmitForReview={handleSubmitForReview}
                  handleSubmitForApprove={handleSubmitForApprove}
                  handleSendBack={handleSendBack}
                  handleCorrection={handleCorrection}
                  handleRejectAndCancel={handleRejectAndCancel}
                  handleValidateAndSyndicate={handleValidateAndSyndicate}
                  validateAllRows={validateAllRows}
                  isSaveAsDraftEnabled={isSaveAsDraftEnabled}
                  validateEnabled={validateEnabled}
                  filteredButtons={filteredButtons}
                  moduleName={module}
                />
              </Box>
            </Box>
          </Box>
        ))}

      <BottomNavGlobal
        handleSaveAsDraft={handleSaveAsDraft}
        handleSubmitForReview={handleSubmitForReview}
        handleSubmitForApprove={handleSubmitForApprove}
        handleSendBack={handleSendBack}
        handleCorrection={handleCorrection}
        handleRejectAndCancel={handleRejectAndCancel}
        handleValidateAndSyndicate={handleValidateAndSyndicate}
        validateAllRows={validateAllRows}
        isSaveAsDraftEnabled={isSaveAsDraftEnabled}
        validateEnabled={validateEnabled}
        filteredButtons={filteredButtons}
        moduleName={module}
      />
      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />
    </div>
  );
};

export default RequestDetailsPC;
