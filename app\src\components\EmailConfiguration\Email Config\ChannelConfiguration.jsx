import { Stack, Typography } from "@mui/material";
import React from "react";

const ChannelConfiguration = (props) => {
  const Content = () => {
    switch (props?.channel) {
      case "Phone":
        return <div></div>;
      case "SMS":
        return <SMSConfiguration/>;
      case "WhatsApp":
        return <div></div>;
      case "Email":
        return <div></div>;
      case "InApp":
        return <div></div>;
      default:
        return <div></div>;
    }
  };
  return <div>{<Content />}</div>;
};

export default ChannelConfiguration;

const EmailConfiguration = () => {
  return (
    <div>ChannelConfiguration</div>
  )
}
const InAppConfiguration = () => {
  return (
    <div>ChannelConfiguration</div>
  )
}
const SMSConfiguration = () => {
    return (
<Stack diection="column" justifyContent={"start"} alignItems={"center"} >
    <Typography variant={"h2"}>SMS Configuration</Typography>
    <Stack  spacing={1} direction={"row"} justifyItems="flex-start" alignItems={"center"}>
        <Typography variant="body2" noWrap color={"text.primary"}>
                  {"Graph Title"}
                </Typography>
                <Typography variant="body2" noWrap color={"error.main"}>
                  {"*"}
                </Typography>

    </Stack>

</Stack>
    )
}
const PhoneConfiguration = () => {
  return (
    <div>ChannelConfiguration</div>
  )
}
  const WhatsAppConfiguration = () => {
    return (
      <div>ChannelConfiguration</div>
    )
  }