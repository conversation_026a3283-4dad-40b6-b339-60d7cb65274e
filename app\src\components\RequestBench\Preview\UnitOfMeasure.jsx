import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { UOM_DATA } from "@constant/enum";
import { colors } from "@constant/colors";

const UnitOfMeasure = ({ unitOfMeasureDataPayload }) => {
  if (!unitOfMeasureDataPayload || unitOfMeasureDataPayload.length === 0) {
    return null;
  }

  const renderValue = (value) => {
    if (value === null || value === undefined) {
      return "--";
    }
    if (typeof value === 'object') {
      if (value.code !== undefined) {
        return value.desc ? `${value.code} - ${value.desc}` : value.code;
      }
      return JSON.stringify(value);
    }
    return String(value);
  };

  return (
    <Accordion sx={{ boxShadow: 3 }} expanded={true}>
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls="uom-data-content"
        id="uom-data-header"
        sx={{
          backgroundColor: colors.background.subtle,
          padding: "8px 16px",
          "&:hover": { backgroundColor: colors.background.subtle },
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: "bold", color: colors.text.primary }}>
          {UOM_DATA.ACCORDION_TITLE}
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        <TableContainer component={Paper} sx={{ 
          borderRadius: "8px", 
          boxShadow: colors.shadow.light,
          border: `1px solid ${colors.border.light}`
        }}>
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: colors.background.subtle }}>
                <TableCell sx={{ fontWeight: 600, color: colors.text.primary }}>{UOM_DATA.TABLE_HEADERS.ALT_UNIT}</TableCell>
                <TableCell sx={{ fontWeight: 600, color: colors.text.primary }}>{UOM_DATA.TABLE_HEADERS.NUMERATOR}</TableCell>
                <TableCell sx={{ fontWeight: 600, color: colors.text.primary }}>{UOM_DATA.TABLE_HEADERS.DENOMINATOR}</TableCell>
                <TableCell sx={{ fontWeight: 600, color: colors.text.primary }}>{UOM_DATA.TABLE_HEADERS.EAN_UPC}</TableCell>
                <TableCell sx={{ fontWeight: 600, color: colors.text.primary }}>{UOM_DATA.TABLE_HEADERS.DIMENSIONS}</TableCell>
                <TableCell sx={{ fontWeight: 600, color: colors.text.primary }}>{UOM_DATA.TABLE_HEADERS.DIMENSION_UNIT}</TableCell>
                <TableCell sx={{ fontWeight: 600, color: colors.text.primary }}>{UOM_DATA.TABLE_HEADERS.VOLUME}</TableCell>
                <TableCell sx={{ fontWeight: 600, color: colors.text.primary }}>{UOM_DATA.TABLE_HEADERS.VOLUME_UNIT}</TableCell>
                <TableCell sx={{ fontWeight: 600, color: colors.text.primary }}>{UOM_DATA.TABLE_HEADERS.GROSS_WEIGHT}</TableCell>
                <TableCell sx={{ fontWeight: 600, color: colors.text.primary }}>{UOM_DATA.TABLE_HEADERS.NET_WEIGHT}</TableCell>
                <TableCell sx={{ fontWeight: 600, color: colors.text.primary }}>{UOM_DATA.TABLE_HEADERS.WEIGHT_UNIT}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {unitOfMeasureDataPayload?.map((item, index) => {
                // Calculate dimensions string
                const length = renderValue(item[UOM_DATA.FIELDS.LENGTH]);
                const width = renderValue(item[UOM_DATA.FIELDS.WIDTH]);
                const height = renderValue(item[UOM_DATA.FIELDS.HEIGHT]);
                const dimensions = (length === "--" && width === "--" && height === "--") 
                  ? "--" 
                  : `${length}×${width}×${height}`;
                
                return (
                  <TableRow key={item.id || index}>
                    <TableCell sx={{ color: colors.text.secondary }}>{renderValue(item[UOM_DATA.FIELDS.ALT_UNIT])}</TableCell>
                    <TableCell sx={{ color: colors.text.secondary }}>{renderValue(item[UOM_DATA.FIELDS.NUMERATOR])}</TableCell>
                    <TableCell sx={{ color: colors.text.secondary }}>{renderValue(item[UOM_DATA.FIELDS.DENOMINATOR])}</TableCell>
                    <TableCell sx={{ color: colors.text.secondary }}>{renderValue(item[UOM_DATA.FIELDS.EAN_UPC])}</TableCell>
                    <TableCell sx={{ color: colors.text.secondary }}>{dimensions}</TableCell>
                    <TableCell sx={{ color: colors.text.secondary }}>{renderValue(item[UOM_DATA.FIELDS.DIMENSION_UNIT])}</TableCell>
                    <TableCell sx={{ color: colors.text.secondary }}>{renderValue(item[UOM_DATA.FIELDS.VOLUME])}</TableCell>
                    <TableCell sx={{ color: colors.text.secondary }}>{renderValue(item[UOM_DATA.FIELDS.VOLUME_UNIT])}</TableCell>
                    <TableCell sx={{ color: colors.text.secondary }}>{renderValue(item[UOM_DATA.FIELDS.GROSS_WEIGHT])}</TableCell>
                    <TableCell sx={{ color: colors.text.secondary }}>{renderValue(item[UOM_DATA.FIELDS.NET_WEIGHT])}</TableCell>
                    <TableCell sx={{ color: colors.text.secondary }}>{renderValue(item[UOM_DATA.FIELDS.WEIGHT_UNIT])}</TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      </AccordionDetails>
    </Accordion>
  );
};

export default UnitOfMeasure;
