import { useState, useEffect } from "react";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import Modal from "@mui/material/Modal";
import { doAjax } from "../common/fetchService";
import moment from "moment";
import ChangeCircleOutlinedIcon from "@mui/icons-material/ChangeCircleOutlined";
import { useDispatch, useSelector } from "react-redux";
import { Grid, Stack, IconButton, Tooltip, Tabs, Tab } from "@mui/material";
import ReusableDataTable from "../Common/ReusableTable";
import CloseIcon from "@mui/icons-material/Close";
import { v4 as uuidv4 } from "uuid";
import ReusableIcon from "../Common/ReusableIcon";
import { iconButton_SpacingSmall } from "../Common/commonStyles";
import { saveExcel, saveExcelMultiSheets } from "../../functions";
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "@hooks/useLogger";
import { API_CODE, ERROR_MESSAGES, LOADING_MESSAGE, MODULE_MAP } from "@constant/enum";
import { formatDateValue } from "@helper/helper";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { useLocation } from "react-router-dom";
import { filterNavigation } from "@helper/helper";
import { MODULE_SPECIFIC_CHANGE_LOG_TEMPS } from "@constant/createChangeLogTemplate";
import { setApiChangeLogData } from "@modules/BankKey/bnkySlice";

const ChangeLogGlobal = ({ open, closeModal, requestId, requestType, module }) => {
  const { customError } = useLogger();
  const location = useLocation();
  const dispatch = useDispatch();
  const [loading, setloading] = useState(false);
  const payloadData = useSelector((state) => {
    switch (module) {
      case MODULE_MAP?.BK:
        return state?.bankKey?.payload?.requestHeaderData;
      default:
        return null;
    }
  });
  const changeLog = useSelector((state) => {
    switch (module) {
      case MODULE_MAP?.BK:
        return state?.bankKey?.changeLogData;
      default:
        return null;
    }
  });
  const [selectedTab, setSelectedTab] = useState(0);
  const [tabData, setTabData] = useState([]);
  const queryParams = new URLSearchParams(location.search);
  const RequestId = queryParams.get("RequestId");
  const { destination } = filterNavigation(module);
  const columns = [
    {
      field: "type",
      headerName: "Operation Type",
      flex: 1,
      editable: false,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "description",
      headerName: "Operation Description",
      flex: 1.7,
      editable: false,
      headerAlign: "center",
    },
    {
      field: "updatedBy",
      headerName: "Updated By",
      flex: 1.2,
      editable: false,
      headerAlign: "center",
      align: "center",
    },
    {
      field: "updatedOn",
      headerName: "Updated On",
      flex: 1.6,
      editable: false,
      headerAlign: "center",
      align: "center",
      renderCell: (params) => {
        return formatDateValue(params.value);
      },
    },
  ];

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const style = {
    position: "absolute",
    top: "50%",
    left: "52%",
    transform: "translate(-50%, -50%)",
    width: "80%",
    height: "auto",
    bgcolor: "#fff",
    boxShadow: 4,
    p: 2,
    borderRadius: "20px",
  };

  const onClose = () => {
    closeModal(false);
  };

  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 15);

  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      const presentDate = new Date();
      const sheetsData = tabData.map((tab) => {
        return {
          sheetName: tab.label,
          fileName: `Changelog Data-${moment(presentDate).format("DD-MMM-YYYY")}`,
          columns: tab.columns.map((col) => ({
            header: col.headerName,
            key: col.field,
          })),
          rows: tab.rows,
        };
      });

      saveExcelMultiSheets(sheetsData);
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };


  const changeLogFetch = (requestId) => {

    const url = `/${destination}/${END_POINTS?.CHG_DISPLAY_REQUESTOR.FETCH_CHANGELOG_DATA}`;
    let changeLogPayload = {
      ChildRequestId: requestId
    };

    return new Promise((resolve, reject) => {
      const hSuccess = (data) => {
        if (
          data?.statusCode === API_CODE.STATUS_200 &&
          data?.body?.Records?.length > 0
        ) {
          resolve(data?.body?.Records);
        } else {
          resolve([]);
        }
      };

      const hError = (error) => {
        customError(error);
        reject(error);
      };

      doAjax(url, "post", hSuccess, hError, changeLogPayload);
    });
  };


  useEffect(() => {
    const fetchChangeLogData = async () => {
      if (RequestId) {
        try {
          const result = await changeLogFetch(RequestId);
          dispatch(setApiChangeLogData(result || []));

          const template = MODULE_SPECIFIC_CHANGE_LOG_TEMPS?.[module] || {};
          const viewNames = Object?.keys(template);

          const organizedTabs = viewNames?.map((viewName) => {
            const { fieldName, headerName } = template[viewName];

            const rowsForTab = (result || [])?.filter(
              (row) => row?.ViewName === viewName
            );

            return {
              label: viewName,
              columns: fieldName.map((field, idx) => ({
                field,
                headerName: headerName[idx],
                flex: 1,
                align: "center",
                headerAlign: "center",
                renderCell: field.toLowerCase().includes("date")
                  ? (params) => formatDateValue(params.value)
                  : undefined
              })),
              rows: rowsForTab
            };
          });

          setTabData(organizedTabs);

        } catch (error) {
          customError("Error fetching changelog data:", error);
        }
      }
    };

    if (!changeLog.length) fetchChangeLogData();
  }, [RequestId, dispatch]);


  return (
    <>
      {loading && (
        <ReusableBackDrop
          blurLoading={loading}
          loaderMessage={LOADING_MESSAGE.CHANGELOG_LOADING}
        />
      )}
      <Modal
        open={open}
        onClose={onClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box sx={style}>
          <Stack>
            <Grid
              item
              md={12}
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <ChangeCircleOutlinedIcon
                  sx={{
                    color: "black",
                    fontSize: "20px",
                    "&:hover": {
                      transform: "rotate(360deg)",
                      transition: "0.9s",
                    },
                    textAlign: "center",
                    marginTop: "4px",
                  }}
                />
                <Typography
                  id="modal-modal-title"
                  variant="subtitle1"
                  fontSize={"16px"}
                  fontWeight={"bold"}
                  sx={{ color: "black" }}
                >
                  Change Log
                </Typography>
              </Box>

              <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <Tooltip title="Export Table">
                  <IconButton
                    sx={iconButton_SpacingSmall}
                    onClick={functions_ExportAsExcel.convertJsonToExcel}
                  >
                    <ReusableIcon iconName={"IosShare"} />
                  </IconButton>
                </Tooltip>

                <IconButton sx={{ padding: "0 0 0 5px" }} onClick={onClose}>
                  <CloseIcon />
                </IconButton>
              </Box>
            </Grid>
          </Stack>

          <Tabs value={selectedTab} onChange={handleTabChange} sx={{ mt: 2 }}>
            {tabData.map((tab, idx) => (
              <Tab label={tab.label} key={idx} />
            ))}
          </Tabs>

          <Box sx={{ mt: 2 }}>
            {tabData.length > 0 && (
              <ReusableDataTable
                rows={tabData[selectedTab].rows}
                columns={tabData[selectedTab].columns}
                getRowIdValue={"id"} // or fallback to row index if "id" is missing
                autoHeight
                scrollbarSize={10}
                sx={{
                  "& .MuiDataGrid-row:hover": {
                    backgroundColor: "#EAE9FF40",
                  },
                  backgroundColor: "#fff",
                }}
              />
            )}
          </Box>

        </Box>
      </Modal>
    </>
  );
};

export default ChangeLogGlobal;
