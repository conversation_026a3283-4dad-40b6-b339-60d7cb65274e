import React, { useEffect, useRef, useState } from "react";
import { Button, Checkbox, Grid, TextField, IconButton, Typography, Tooltip } from "@mui/material";
import ReusableTable from "@components/common/ReusableTable";
import { container_Padding } from "@components/Common/commonStyles";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import { v4 as uuidv4 } from "uuid";
import { colors } from "@constant/colors";
import { useDispatch, useSelector } from "react-redux";
import { setEanData, setUOmData } from "../../../app/payloadslice";
import { destination_MaterialMgmt } from "../../../destinationVariables";
import { doAjax } from "@components/Common/fetchService";
import { useLocation } from "react-router-dom";
import { showToast } from "../../../functions";
import { API_CODE, ERROR_MESSAGES, MATERIAL_VIEWS, TABLE_FIELDS_UOM, CHANGE_LOG_STATUSES } from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import { useChangeLogUpdate } from "@hooks/useChangeLogUpdate";
import useLang from "@hooks/useLang";

const CustomCheckbox = ({ value, onChange, disabled }) => <Checkbox checked={value || false} onChange={(e) => onChange(e.target.checked)} disabled={disabled} />;

const CustomTextField = ({ value, onChange, disabled, maxLength }) => (
  <TextField
    variant="outlined"
    fullWidth
    size="small"
    value={value || ""}
    onChange={(e) => {
      const newValue = e.target.value;
      if (/^\d*$/.test(newValue)) {
        onChange(newValue);
      }
    }}
    disabled={disabled}
    inputProps={{
      pattern: "[0-9]*", // HTML5 pattern for numbers
      inputMode: "numeric", // Shows numeric keyboard on mobile devices
    }}
    sx={{
      "& .MuiInputBase-input": {
        color: colors.black.dark,
        fontSize: "12px",
      },
      "& .MuiInputBase-input.Mui-disabled": {
        WebkitTextFillColor: colors.black.dark,
        color: colors.black.dark,
      },
      "& .MuiOutlinedInput-root": {
        "&.Mui-disabled": {
          "& > input": {
            WebkitTextFillColor: colors.black.dark,
            color: colors.black.dark,
          },
        },
      },
    }}
  />
);

const CustomDropdown = ({ options = [], value, onChange, disabled, placeholder, isOptionDisabled }) => {
  const selectedOption = options.find((item) => item.code === value);
  const selectedValue = selectedOption ? `${selectedOption.code} - ${selectedOption.desc || ""}` : "";
  return <SingleSelectDropdown options={options} value={selectedValue} onChange={onChange} disabled={disabled} placeholder={placeholder} isOptionDisabled={isOptionDisabled} />;
};

const AdditionalEANSTab = (props) => {
  const dispatch = useDispatch();
  const isLoading = false;
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  const payloadState = useSelector((state) => state.payload);
  const eanData = payloadState[props.materialID]?.eanData || [];
  const uniqueAltUnit = payloadState[props.materialID]?.UniqueAltUnit || [];
  const UOMRows = payloadState[props.materialID]?.unitsOfMeasureData || [];
  const Region = payloadState?.payloadData?.Region || "";
  let basicData = payloadState[props?.materialID]?.payloadData?.["Basic Data"];
  const location = useLocation();
  const { updateChangeLog } = useChangeLogUpdate();
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get("RequestId");
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const updateReduxData = (updatedRows) => {
    dispatch(setEanData({ materialID: props.materialID, data: updatedRows }));
  };
  const { t } = useLang();

  const handleFieldChange = (id, field, value) => {
    if (field === TABLE_FIELDS_UOM?.EAN_UPC) {
      const currentRow = eanData.find((row) => row.id === id);
      const currentAltUnit = currentRow.altunit;
      const existingMainEan = eanData.some((row) => row.id !== id && row.altunit === currentAltUnit && row.MainEan === true);
      const finalRows = eanData.map((row) => {
        if (row.id === id) {
          return {
            ...row,
            [field]: value,
            au: false,
            MainEan: existingMainEan ? false : value ? true : false,
          };
        }
        return row;
      });

      updateReduxData(finalRows);

      if (UOMRows.length > 0 && value && !existingMainEan) {
        const updatedUOMRows = UOMRows.map((row) => {
          if (row.aUnit === currentAltUnit) {
            return {
              ...row,
              eanUpc: value,
              eanCategory: currentRow.eanCategory,
            };
          }
          return row;
        });

        if (JSON.stringify(updatedUOMRows) !== JSON.stringify(UOMRows)) {
          dispatch(
            setUOmData({
              materialID: props.materialID,
              data: updatedUOMRows,
            })
          );
        }
      }

      if (requestId && !CHANGE_LOG_STATUSES.includes(initialPayload?.RequestStatus)) {
        updateChangeLog({
          materialID: props.materialID,
          viewName: MATERIAL_VIEWS.ADDITIONAL_EAN_DATA,
          plantData: "",
          fieldName: "EanUpc",
          jsonName: TABLE_FIELDS_UOM?.EAN_UPC,
          currentValue: value,
          requestId: initialPayload?.RequestId,
          isAdditionalEAN: true,
          eanId: currentRow.EanId || null,
          childRequestId: requestId,
        });
      }
    } else if (field === "MainEan" && value === true) {
      const currentRow = eanData.find((row) => row.id === id);
      const currentAltUnit = currentRow.altunit;
      const updatedRows = eanData.map((row) => {
        if (row.altunit === currentAltUnit) {
          return { ...row, MainEan: false };
        }
        return row;
      });

      const finalRows = updatedRows.map((row) => (row.id === id ? { ...row, MainEan: true } : row));
      updateReduxData(finalRows);

      if (UOMRows.length > 0) {
        const updatedUOMRows = UOMRows.map((row) => {
          if (row.aUnit === currentAltUnit) {
            return {
              ...row,
              eanUpc: currentRow.eanUpc,
              eanCategory: currentRow.eanCategory,
            };
          }
          return row;
        });

        if (JSON.stringify(updatedUOMRows) !== JSON.stringify(UOMRows)) {
          dispatch(
            setUOmData({
              materialID: props.materialID,
              data: updatedUOMRows,
            })
          );
        }
      }
      if (requestId && !CHANGE_LOG_STATUSES.includes(initialPayload?.RequestStatus)) {
        updateChangeLog({
          materialID: props.materialID,
          viewName: MATERIAL_VIEWS.ADDITIONAL_EAN_DATA,
          plantData: "",
          fieldName: "MainEan",
          jsonName: TABLE_FIELDS_UOM?.MAIN_EAN,
          currentValue: value,
          requestId: initialPayload?.RequestId,
          childRequestId: requestId,
          isAdditionalEAN: true,
          eanId: currentRow.EanId || null,
        });
      }
    } else if (field === "eanCategory") {
      const currentRow = eanData.find((row) => row.id === id);
      const updatedRows = eanData.map((row) => {
        if (row.id === id) {
          return {
            ...row,
            [field]: value,
            MainEan: false,
          };
        }
        return row;
      });
      updateReduxData(updatedRows);
      if (requestId && !CHANGE_LOG_STATUSES.includes(initialPayload?.RequestStatus)) {
        updateChangeLog({
          materialID: props.materialID,
          viewName: MATERIAL_VIEWS.ADDITIONAL_EAN_DATA,
          plantData: "",
          fieldName: "EanCat",
          jsonName: TABLE_FIELDS_UOM?.EAN_CATEGORY,
          currentValue: value,
          requestId: initialPayload?.RequestId,
          childRequestId: requestId,
          isAdditionalEAN: true,
          eanId: currentRow.EanId || null,
        });
      }
    } else if (field === "au" && value === true) {
      const currentRow = eanData.find((row) => row.id === id);
      if (currentRow.eanUpc) {
        const hSuccess = (response) => {
          const updatedRows = eanData.map((row) => {
            if (row.id === id) {
              return {
                ...row,
                [field]: value,
                eanUpc: row.eanUpc + response.body,
                MainEan: false,
              };
            }
            return row;
          });
          updateReduxData(updatedRows);

          if (requestId && !CHANGE_LOG_STATUSES.includes(initialPayload?.RequestStatus)) {
            updateChangeLog({
              materialID: props.materialID,
              viewName: MATERIAL_VIEWS.ADDITIONAL_EAN_DATA,
              plantData: "",
              fieldName: "other",
              jsonName: "au",
              currentValue: value,
              requestId: initialPayload?.RequestId,
              childRequestId: requestId,
              isAdditionalEAN: true,
              eanId: currentRow.EanId || null,
            });
          }
        };

        const hError = (error) => {
          showToast(ERROR_MESSAGES?.ERROR_FETCHING_DATA, "error");
        };

        doAjax(`/${destination_MaterialMgmt}${END_POINTS?.DATA?.GET_CHECK_DIGIT}?number=${currentRow.eanUpc}`, "get", hSuccess, hError, { eanUpc: currentRow.eanUpc });
      }
    } else {
      const updatedRows = eanData.map((row) => (row.id === id ? { ...row, [field]: value } : row));
      const currentRow = eanData.find((row) => row.id === id);
      updateReduxData(updatedRows);
      if (requestId && !CHANGE_LOG_STATUSES.includes(initialPayload?.RequestStatus)) {
        updateChangeLog({
          materialID: props.materialID,
          viewName: MATERIAL_VIEWS.ADDITIONAL_EAN_DATA,
          plantData: "",
          fieldName: field,
          jsonName: field,
          currentValue: value,
          requestId: initialPayload?.RequestId,
          childRequestId: requestId,
          isAdditionalEAN: true,
          eanId: currentRow.EanId || null,
        });
      }
    }
  };

  const handleDeleteRow = (id) => {
    const updatedRows = eanData.filter((row) => row.id !== id);
    updateReduxData(updatedRows);
  };

  const handleAddRow = () => {
    const newRow = {
      id: uuidv4(),
      EanId: null,
      altunit: "",
      MainEan: false,
      eanUpc: "",
      eanCategory: "",
      au: false,
      isNew: true, // Add this flag for new rows
    };
    updateReduxData([...eanData, newRow]);
  };

  useEffect(() => {
    if (uniqueAltUnit.length) {
      const rowsToKeep = eanData.filter((row) => uniqueAltUnit.includes(row.altunit) || row.altunit === "");
      const existingAltUnits = rowsToKeep.map((row) => row.altunit);
      const newAltUnits = uniqueAltUnit.filter((unit) => !existingAltUnits.includes(unit));
      const newRows = newAltUnits.map((unit) => ({
        id: uuidv4(),
        EanId: null,
        altunit: unit,
        MainEan: false,
        eanUpc: "",
        eanCategory: "",
        au: false,
      }));

      updateReduxData([...rowsToKeep, ...newRows]);
    }
  }, [uniqueAltUnit]);

  const columns = [
    {
      field: "altunit",
      headerName: t("Alt. Unit"),
      width: 200,
      renderCell: (params) => <CustomDropdown options={dropDownData?.BaseUom || []} value={params.row.altunit} onChange={(value) => handleFieldChange(params.row.id, "altunit", value?.code)} disabled={props?.disabled} placeholder="SELECT Alt. Unit" isOptionDisabled={(option) => !uniqueAltUnit.includes(option.code)} />,
    },
    {
      field: "eanUpc",
      headerName: t("EAN/UPC"),
      width: 180,
      renderCell: (params) => {
        return (
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <Tooltip title={params.row[TABLE_FIELDS_UOM?.EAN_UPC] || ""} arrow>
              <div style={{ width: "100%" }}>
                <CustomTextField value={params.row[TABLE_FIELDS_UOM?.EAN_UPC]} onChange={(value) => handleFieldChange(params.row.id, TABLE_FIELDS_UOM?.EAN_UPC, value)} disabled={props?.disabled} maxLength={13} onClick={(e) => e.stopPropagation()} onDoubleClick={(e) => e.stopPropagation()} />
              </div>
            </Tooltip>
          </div>
        );
      },
    },
    {
      field: "eanCategory",
      headerName: t("EAN Cat."),
      width: 200,
      renderCell: (params) => (
        <CustomDropdown
          options={dropDownData?.EanCat || []}
          value={params.row.eanCategory}
          onChange={(value) => handleFieldChange(params.row.id, "eanCategory", value?.code)}
          disabled={props?.disabled}
          placeholder="SELECT EAN Cat."
          isOptionDisabled={(option) => {
            const rowsWithSameAltUnit = eanData.filter((row) => row.altunit === params.row.altunit && row.id !== params.row.id);
            return rowsWithSameAltUnit.some((row) => row.eanCategory === option.code);
          }}
        />
      ),
    },
    {
      field: "au",
      headerName: t("Au"),
      width: 150,
      renderCell: (params) => <CustomCheckbox value={params.row.au} onChange={(value) => handleFieldChange(params.row.id, "au", value)} disabled={props?.disabled || params.row.au} />,
    },
    {
      field: "MainEan",
      headerName: t("Main EAN"),
      width: 160,
      renderCell: (params) => <CustomCheckbox value={params.row.MainEan} onChange={(value) => handleFieldChange(params.row.id, "MainEan", value)} disabled={props?.disabled} />,
    },
    {
      field: "actions",
      headerName: t("Actions"),
      width: 180,
      renderCell: (params) => (
        <Tooltip title={params.row.isNew ? "Delete row" : "Cannot delete existing row"}>
          <span>
            <IconButton onClick={() => handleDeleteRow(params.row.id)} disabled={props?.disabled || !params.row.isNew} color="error" size="small">
              <DeleteOutlineIcon fontSize="small" />
            </IconButton>
          </span>
        </Tooltip>
      ),
    },
  ];

  return (
    <div>
      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          mt: 0.25,
          ...container_Padding,
        }}
      >
        <Grid container display="block" sx={{ marginLeft: "-10px" }}>
          <Grid item xs={4}></Grid>
          <Grid item xs={10} mt={4}>
            <ReusableTable title="Additional EANs/Units of Measure" isLoading={isLoading} rows={eanData} columns={columns} getRowIdValue={"id"} hideFooter={false} checkboxSelection={false} disableSelectionOnClick={true} status_onRowSingleClick={true} stopPropagation_Column={["eanUpc", "action"]} status_onRowDoubleClick={false} width="100%" />
            {!props?.disabled && (
              <Button variant="outlined" sx={{ mt: 2 }} onClick={handleAddRow} disabled={!UOMRows?.length}>
                {t("Add Row")}
              </Button>
            )}
          </Grid>
        </Grid>
      </Grid>
    </div>
  );
};

export default AdditionalEANSTab;
