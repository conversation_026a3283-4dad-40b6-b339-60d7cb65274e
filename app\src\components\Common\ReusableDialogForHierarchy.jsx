import React, {
  useState,
  forwardRef,
  useRef,
  useEffect,
  useCallback,
} from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tooltip,
  Box,
  Typography,
  Slide,
  FormControl,
  FormControlLabel,
  tooltipClasses,
  RadioGroup,
  IconButton,
  Radio,
  Tabs,
  Tab,
} from "@mui/material";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import styled from "@emotion/styled";
import { doAjax } from "./fetchService";
import { DataGrid } from "@mui/x-data-grid";
import {
  destination_CostCenter_Mass,
  destination_GeneralLedger_Mass,
  destination_ProfitCenter_Mass,
} from "../../destinationVariables";
import { useDispatch, useSelector } from "react-redux";
import { v4 as uuidv4 } from "uuid";
import { setRequestorPayload, setDataLoading } from "../../app/payloadslice";
import useChangeMaterialRowsRequestor from "../../hooks/useChangeMaterialRowsRequestor";
import ReusableBackDrop from "./ReusableBackDrop";
import ReusableSnackBar from "./ReusableSnackBar";
import DownloadDialog from "./DownloadDialog";
import { useNavigate } from "react-router-dom";
import {
  MANDATORY_FILTERS,
  Hierarchy_Templates,
} from "@constant/changeTemplates";
import { saveExcel } from "../../functions";
import { setDropDown } from "../../app/dropDownDataSlice";
import {
  CHANGE_KEYS,
  ERROR_MESSAGES,
  REQUEST_TYPE,
  API_CODE,
  MODULE_KEY_MAP,
  MODULE_MAP,
  MODULE,
} from "@constant/enum";
import { colors } from "@constant/colors";
import FilterChangeDropdown from "./ui/dropdown/FilterChangeDropdown";
import { APP_END_POINTS } from "@constant/appEndPoints";
import { END_POINTS } from "@constant/apiEndPoints";
import FilterTextField from "./ui/dropdown/FilterTextField";
import { setTabValue } from "@app/requestDataSlice";
import { setTreeData, updateToChangeLog } from "@app/hierarchyDataSlice";
import useDuplicacyCheck from "@hooks/useDuplicateCheckHierarchy";
// Slide transition component
const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});

const ReusableDialogForHierarchy = ({
  open,
  onClose,
  parameters,
  mandatoryFields,
  setShowTable,
  allDropDownData,
  setIsSecondTabEnabled,
  module,
}) => {

  const [selectedValues, setSelectedValues] = useState({});
  const [convertedValues, setConvertedValues] = useState({});
  const [errors, setErrors] = useState({});
  const [blurLoading, setBlurLoading] = useState("");
  const [successMsg, setSuccessMsg] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [loaderMessage, setLoaderMessage] = useState("");
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [materialOptions, setMaterialOptions] = useState([]);
  const [errorTextMessage, setErrorTextMessage] = useState("");
  const [errorText, setErrorText] = useState(false);
  const initialPayload = useSelector(
    (state) => state.hierarchyData.requestHeaderData
  );
  const RequestId = useSelector(
    (state) => state.request.requestHeader.requestId
  );
  const requestHeader = useSelector(
    (state) => state.hierarchyData.requestHeaderData
  );
  const loadForFetching = useSelector((state) => state.payload.dataLoading);
  const regionBasedSalesOrgData = useSelector(
    (state) => state.request.salesOrgDTData
  );
  let userData = useSelector((state) => state.userManagement.userData);
  const {
    checkForNodeDuplicacy,
    checkForDescriptionDuplicacy,
    checkForObjectDuplicacy,
  } = useDuplicacyCheck();
  const [dropDownData, setDropDownData] = useState({});
  const [isFilterLoading, setIsFilterLoading] = useState({
    [CHANGE_KEYS.MATERIAL_NUM]: false,
    [CHANGE_KEYS.PLANT]: false,
    [CHANGE_KEYS.SALES_ORG]: false,
    [CHANGE_KEYS.DIVISION]: false,
    [CHANGE_KEYS.DIST_CHNL]: false,
    [CHANGE_KEYS.WAREHOUSE]: false,
    [CHANGE_KEYS.STORAGE_LOC]: false,
    [CHANGE_KEYS.MRP_CTRLER]: false,
  });
  console.log("parameters", parameters, selectedValues, mandatoryFields);
  const [inputState, setInputState] = useState({ code: "", desc: "" }); // Skip value for API call
  const [timerId, setTimerId] = useState(null);
  const dropdownRef = useRef(null); // Ref for the dropdown

  const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
  const [popoverContent, setPopoverContent] = useState("");
  const [isPopoverVisible, setIsPopoverVisible] = useState(false);
  const popoverRef = useRef(null);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { fetchDuplicateCheckHierarchy } = useChangeMaterialRowsRequestor();
  const [activeTab, setActiveTab] = useState(0);
  const [activeHandler, setActiveHandler] = useState(null);
  const [rowsOfMaterialData, setRowsOfMaterialData] = useState([]);
  const [totalMaterialCount, setTotalMaterialCount] = useState(0);
  const columnsOfMaterialData = Hierarchy_Templates[
    initialPayload?.TemplateName
  ]?.map((item) => ({
    field: item.key,
    headerName: item.key,
    editable: true,
    flex: 2,
  }));
  const limit = 200;

  const handlePasteMaterialData = useCallback((event) => {
    event.preventDefault();
    const clipboardData = event.clipboardData || window.clipboardData;
    const pastedData = clipboardData.getData("Text");

    const newRows = pastedData
      .trim()
      .split("\n")
      .map((row, rowIndex) => {
        const values = row.split("\t");
        const rowData = { id: rowIndex + 1 };
        columnsOfMaterialData.forEach((col, colIndex) => {
          rowData[col.field] = values[colIndex] || "";
        });
        return rowData;
      });
    setRowsOfMaterialData(newRows);
  }, []);

  useEffect(() => {
    if (activeTab === 1) {
      document.addEventListener("paste", handlePasteMaterialData);
      return () => {
        document.removeEventListener("paste", handlePasteMaterialData);
      };
    }
  }, [activeTab, handlePasteMaterialData]);

  const handlePopoverOpen = (event, content) => {
    setPopoverAnchorEl(event.currentTarget);
    setPopoverContent(content);
    setIsPopoverVisible(true);
  };

  const handlePopoverClose = () => {
    setIsPopoverVisible(false);
  };

  const handleMouseEnterPopover = () => {
    setIsPopoverVisible(true);
  };

  const handleMouseLeavePopover = () => {
    setIsPopoverVisible(false);
  };

  const popoverOpen = Boolean(popoverAnchorEl);
  const popoverId = popoverOpen ? "custom-popover" : undefined;

  const handleSelectionChange = (key, newValue) => {
    setSelectedValues((prev) => ({
      ...prev,
      [key]: newValue,
    }));
    if (newValue.length > 0) {
      setErrors((prev) => ({
        ...prev,
        [key]: "",
      }));
    }
  };

  useEffect(() => {
    if (rowsOfMaterialData) {
      let result = convertRowDataToSelectedValues(rowsOfMaterialData);
      setSelectedValues(result);
    }
  }, [rowsOfMaterialData]);

  const handleSelectAll = (key, allOptions) => {
    const allSelected = selectedValues[key]?.length === allOptions.length;
    setSelectedValues((prev) => ({
      ...prev,
      [key]: allSelected ? [] : allOptions,
    }));
    if (!allSelected) {
      setErrors((prev) => ({
        ...prev,
        [key]: "",
      }));
    }
  };

  const convertRowDataToSelectedValues = (data) => {
    const result = {};

    data.forEach((row) => {
      Object.keys(row).forEach((key) => {
        if (key !== "id" && row[key].trim() !== "") {
          // Exclude 'id' and empty values
          if (!result[key]) {
            result[key] = [];
          }
          result[key].push({ code: row[key].trim() });
        }
      });
    });

    return result;
  };

  const validateMandatoryFields = (mandatoryFields) => {
    const emptyMandatoryFields = mandatoryFields?.filter((fieldName) =>
      !selectedValues[fieldName] || Array.isArray(selectedValues[fieldName])
        ? !selectedValues[fieldName]?.length
        : selectedValues[fieldName].trim() === ""
    );

    console.log("emptyMandatoryFields", emptyMandatoryFields);
    if (emptyMandatoryFields.length > 0) {
      setErrorText(true);
      setErrorTextMessage(
        ERROR_MESSAGES.MANDATORY_FILTER_MD(emptyMandatoryFields.join(", "))
      );
      return false;
    }

    return true;
  };

  const handleOkClick = async () => {
    if (!validateMandatoryFields(mandatoryFields)) {
      return;
    }

    try {
      dispatch(setDataLoading(true));
      if (requestHeader?.RequestType === REQUEST_TYPE?.CREATE) {
        const nodeCheck = await checkForNodeDuplicacy(
          selectedValues[MODULE_KEY_MAP?.[module]?.CTR_GRP],
          module,
          selectedValues[MODULE_KEY_MAP?.[module]?.CTRL_AREA]?.[0]?.code,
          "",
          selectedValues?.[MODULE_KEY_MAP?.[module]?.COA]?.[0]?.code,
          ""
        );

        if (nodeCheck?.body?.isDbDuplicate) {
           setErrorText(true);
          setErrorTextMessage(
            `This Node already exists in some ongoing request!`
          );
          dispatch(setDataLoading(false));
          return;
        }

        if (
          nodeCheck?.body?.PresentInHier === "X" ||
          nodeCheck.body.PresentInCA === "X" ||
          nodeCheck.body?.PresentInCOA === "X"
        ) {
           setErrorText(true);
          setErrorTextMessage(`This Node already exists in the hierarchy!`);
          dispatch(setDataLoading(false));
          return;
        }

        const descCheck = await checkForDescriptionDuplicacy(
          selectedValues[MODULE_KEY_MAP?.[module]?.CTR_GRP_DESC],
          module,
          selectedValues[MODULE_KEY_MAP?.[module]?.CTRL_AREA][0]?.code,
          "",
          ""
        );

        if (
          Object.keys(descCheck.body).length != 0 &&
          descCheck?.body?.isDbDuplicate === true
        ) {
           setErrorText(true);
          setErrorTextMessage(
            `Description already present in some ongoing request!`
          );
          dispatch(setDataLoading(false));
          return;
        }
      } else {
        const nodeCheck = await checkForNodeDuplicacy(
          selectedValues[MODULE_KEY_MAP?.[module]?.CTR_GRP]?.[0]?.code,
          module,
          selectedValues[MODULE_KEY_MAP?.[module]?.CTRL_AREA][0]?.code,
          "",
          ""
        );

        if (nodeCheck?.body?.isDbDuplicate) {
          setErrorText(true);
          setErrorTextMessage(
            `This Node already exists in some ongoing request!`
          );
          dispatch(setDataLoading(false));
          return;
        }
      }

      setErrorText(false);
      onClose();
      if (initialPayload?.RequestType === REQUEST_TYPE?.CREATE) {
        let treeData = [
          {
            id: "1",
            label: selectedValues[CHANGE_KEYS?.PRCTR_GRP] || selectedValues[CHANGE_KEYS?.CCCTR_GRP]  || selectedValues[CHANGE_KEYS?.CECTR_GRP],
            description: selectedValues[CHANGE_KEYS?.PRCTR_GRP_DESC] || selectedValues[CHANGE_KEYS?.CCCTR_GRP_DESC] || selectedValues[CHANGE_KEYS?.CECTR_GRP_DESC],
            isParent: true,
            tags: [],
            child: [],
          },
        ];
        dispatch(setTreeData(treeData));
        dispatch(
          updateToChangeLog({
            id: "1",
            type: "ADD NODE",
            description: `${treeData?.[0]?.label} created `,
            updatedBy: userData?.emailId || "",
            updatedOn: `/Date(${new Date().getTime()})/` || "",
          })
        );
      } else if (initialPayload?.RequestType === REQUEST_TYPE?.CHANGE) {
        try {
          const result = await getHeirarchyNodeTreeStructure();
          dispatch(setTreeData(result));
        } catch (error) {
          // customError("Error fetching changelog data:", error);
        }
      }
      dispatch(setDataLoading(false));
      dispatch(setRequestorPayload(selectedValues));
      setIsSecondTabEnabled(true);
      dispatch(setTabValue(1));
    } catch (error) {
      setErrorText(true);
      setErrorTextMessage("Error fetching data.");
      dispatch(setDataLoading(false));
    }
  };

  const getHeirarchyNodeTreeStructure = () => {
    setBlurLoading(true);
    dispatch(setDataLoading(true));
    var payload;
    let destination;
    if (module === MODULE?.PCG) {
      destination =destination_ProfitCenter_Mass
      payload = {
        node:
          selectedValues[CHANGE_KEYS?.PRCTR_GRP]?.length === 0
            ? "DSM-1"
            : selectedValues[CHANGE_KEYS?.PRCTR_GRP]?.[0]?.code,
        controllingArea:
          selectedValues[CHANGE_KEYS?.CTRL_AREA_PCG]?.length === 0
            ? "ETCA"
            : selectedValues[CHANGE_KEYS?.CTRL_AREA_PCG]?.[0]?.code,
        classValue: "0106",
        id: "",
        screenName: "Display",
      }
    }
    else if (module === MODULE?.CCG) {
      destination = destination_CostCenter_Mass
      payload = {
        node:
          selectedValues[CHANGE_KEYS?.CCCTR_GRP]?.length === 0
            ? "DSM-1"
            : selectedValues[CHANGE_KEYS?.CCCTR_GRP]?.[0]?.code,
        controllingArea:
          selectedValues[CHANGE_KEYS?.CTRL_AREA_PCG]?.length === 0
            ? "ETCA"
            : selectedValues[CHANGE_KEYS?.CTRL_AREA_PCG]?.[0]?.code,
        classValue: "0101",
        id: "",
        screenName: "Display",
      }
    }
     else if (module === MODULE?.CEG) {
       destination = destination_GeneralLedger_Mass
      payload = {
        node:
          selectedValues[CHANGE_KEYS?.CECTR_GRP]?.length === 0
            ? "DSM-1"
            : selectedValues[CHANGE_KEYS?.CECTR_GRP]?.[0]?.code,
        controllingArea:
          selectedValues[CHANGE_KEYS?.CTRL_AREA_PCG]?.length === 0
            ? "ETCA"
            : selectedValues[CHANGE_KEYS?.CTRL_AREA_PCG]?.[0]?.code,
        classValue: "0102",
        id: "",
        screenName: "Display",
      }
    }
    return new Promise((resolve, reject) => {
      const hSuccess = (data) => {
        let innerData = [];
        if (data.statusCode === API_CODE.STATUS_200) {
          innerData.push(data.body.HierarchyTree);
          setBlurLoading(false);
          dispatch(setDataLoading(false));

          resolve(innerData);
        } else {
          resolve([]);
        }
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination}/data/displayHierarchyTreeNodeStructure`,
        "post",
        hSuccess,
        hError,
        payload
      );
    });
  };


  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };


  const handleDownloadDialogClose = () => {
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
  };

  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };

  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };

  useEffect(() => {
    const { [CHANGE_KEYS?.MATERIAL_NUM]: ignored, ...rest } =
      selectedValues || {};
    if (rest && Object.keys(rest).length > 0) {
      setInputState({ code: "", desc: "" });
    }
  }, [
    JSON.stringify({
      ...selectedValues,
      [CHANGE_KEYS.MATERIAL_NUM]: undefined,
    }),
  ]);

  useEffect(() => {
    parameters?.forEach((param) => {
      if (param.key === CHANGE_KEYS?.CTRL_AREA_PCG || param.key === CHANGE_KEYS?.COA) {
        fetchOrgLookupData(param.key);
      }
    });
  }, [parameters]);

  useEffect(() => {
    if (
      selectedValues[CHANGE_KEYS?.CTRL_AREA_PCG] &&
      selectedValues[CHANGE_KEYS?.CTRL_AREA_PCG].length === 0 &&
      initialPayload?.RequestType === REQUEST_TYPE?.CHANGE
    ) {
      if (module === MODULE?.PCG) {
        selectedValues[CHANGE_KEYS?.PRCTR_GRP] = [];
        setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.PRCTR_GRP]: [] }));
      }
      else if (module === MODULE?.CCG) {
        selectedValues[CHANGE_KEYS?.CCCTR_GRP] = [];
        setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.CCCTR_GRP]: [] }));
      }
      else if (module === MODULE?.CEG) {
        selectedValues[CHANGE_KEYS?.CECTR_GRP] = [];
        setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.CECTR_GRP]: [] }));
      }
    }
    if (
      selectedValues[CHANGE_KEYS?.CTRL_AREA_PCG] &&
      selectedValues[CHANGE_KEYS?.CTRL_AREA_PCG].length > 0 &&
      initialPayload?.RequestType === REQUEST_TYPE?.CHANGE
    ) {
      fetchZeroLevelNode(selectedValues[CHANGE_KEYS?.CTRL_AREA_PCG]?.[0]?.code || "");
    }
  }, [selectedValues[CHANGE_KEYS?.CTRL_AREA_PCG]]);

  const fetchZeroLevelNode = (value) => {
    var firstNode;
    var destination;

    if(module === MODULE?.PCG) {
      firstNode = CHANGE_KEYS?.PRCTR_GRP
      destination = destination_ProfitCenter_Mass
    }
    else if(module === MODULE?.CCG) {
      firstNode = CHANGE_KEYS?.CCCTR_GRP
      destination = destination_CostCenter_Mass
    }
    else if(module === MODULE?.CEG) {
      firstNode = CHANGE_KEYS?.CECTR_GRP
      destination = destination_GeneralLedger_Mass
    }

    setIsFilterLoading((prev) => ({ ...prev, [firstNode]: true }));
    const successHandler = (data) => {
      setDropDownData((prev) => ({
        ...prev,
        [firstNode]: data.body,
      }));
      setIsFilterLoading((prev) => ({
        ...prev,
        [firstNode]: false,
      }));
    };
    const errorHandler = (error) => {
      setIsFilterLoading((prev) => ({
        ...prev,
        [firstNode]: false,
      }));
    };
    doAjax(
      `/${destination}/node/getZeroLevelNodes?controllingArea=${value}`,
      "get",
      successHandler,
      errorHandler
    );
  };

  const fetchOrgLookupData = (field) => {
    setIsFilterLoading((prev) => ({ ...prev, [field]: true }));
    const endpoints = {
      [CHANGE_KEYS?.CTRL_AREA_PCG]: "/data/getControllingArea",
      [CHANGE_KEYS?.COA]: "/data/getChartOfAccounts",
    };

    const destinationMap = {
      [MODULE?.PCG]: {
        [CHANGE_KEYS?.CTRL_AREA_PCG]: destination_ProfitCenter_Mass,
      },
      [MODULE?.CCG]: {
        [CHANGE_KEYS?.CTRL_AREA_PCG]: destination_CostCenter_Mass,
      },
      [MODULE?.CEG]: {
        [CHANGE_KEYS?.CTRL_AREA_PCG]: destination_ProfitCenter_Mass,
        [CHANGE_KEYS?.COA]: destination_GeneralLedger_Mass,
      }
    };

    const destination = destinationMap[module] || {};

    const successHandler = (data) => {
      setDropDownData((prev) => ({ ...prev, [field]: data.body }));
      dispatch(setDropDown({ keyName: field, data: data?.body }));
      setIsFilterLoading((prev) => ({ ...prev, [field]: false }));
    };
    const errorHandler = (error) => {
      setIsFilterLoading((prev) => ({ ...prev, [field]: false }));
    };

    doAjax(
      `/${destination[field]}${endpoints[field]}`,
      "get",
      successHandler,
      errorHandler
    );
  };

  const renderAutocomplete = (param) => {
    const formatOptionLabel = (option) => {
      if (option.code && option.desc) {
        return `${option.code} - ${option.desc}`;
      }
      return option.code || "";
    };

    if (
      param.key === CHANGE_KEYS?.CTRL_AREA_PCG ||
      ((param.key === CHANGE_KEYS?.PRCTR_GRP || param.key === CHANGE_KEYS?.CCCTR_GRP  || param.key === CHANGE_KEYS?.CECTR_GRP || param.key === CHANGE_KEYS?.COA) && param?.type === "Dropdown")
    ) {
      return (
        <FilterChangeDropdown
          param={param}
          mandatory={mandatoryFields?.includes(param?.key)}
          dropDownData={dropDownData}
          allDropDownData={allDropDownData}
          selectedValues={selectedValues}
          handleSelectAll={handleSelectAll}
          handleSelectionChange={handleSelectionChange}
          errors={errors}
          formatOptionLabel={formatOptionLabel}
          handlePopoverOpen={handlePopoverOpen}
          handlePopoverClose={handlePopoverClose}
          handleMouseEnterPopover={handleMouseEnterPopover}
          handleMouseLeavePopover={handleMouseLeavePopover}
          isPopoverVisible={isPopoverVisible}
          popoverId={popoverId}
          popoverAnchorEl={popoverAnchorEl}
          popoverRef={popoverRef}
          popoverContent={popoverContent}
          isMaterialNum={false}
          isLoading={isFilterLoading[param.key]}
          isSelectAll={true}
          singleSelect={true}
        />
      );
    } else if (
      (param.key === CHANGE_KEYS?.PRCTR_GRP_DESC || param.key === CHANGE_KEYS?.CCCTR_GRP_DESC) ||
      ((param.key === CHANGE_KEYS?.PRCTR_GRP || param.key === CHANGE_KEYS?.CCCTR_GRP || param.key === CHANGE_KEYS?.CECTR_GRP  || param.key === CHANGE_KEYS?.CECTR_GRP_DESC) && param?.type === "Input")
    ) {
      return (
        <FilterTextField
          param={param}
          mandatory={mandatoryFields?.includes(param?.key)}
          selectedValues={selectedValues}
          handleSelectionChange={handleSelectionChange}
          errors={errors}
        />
      );
    }
  };

  return (
    <>
      <Dialog
        open={open}
        TransitionComponent={Transition}
        keepMounted
        onClose={() => { }}
        maxWidth={activeTab === 1 ? "md" : "sm"}
        fullWidth
      >
        <Box
          sx={{
            backgroundColor: "#e3f2fd",
            padding: "1rem 1.5rem",
            display: "flex",
            alignItems: "center",
          }}
        >
          <FeedOutlinedIcon color="primary" sx={{ marginRight: "0.5rem" }} />
          <Typography variant="h6" component="div" color="primary">
            Please Select Filter(s)
          </Typography>
        </Box>

        <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
          <>
            {parameters?.map((param) => (
              <Box key={param.key} sx={{ marginBottom: "1rem" }}>
                {renderAutocomplete(param)}
              </Box>
            ))}
          </>

          {errorText && (
            <Typography variant="h6" color={colors?.error?.dark}>
              * {errorTextMessage}
            </Typography>
          )}
          <ReusableBackDrop blurLoading={loadForFetching} />
        </DialogContent>
        <DialogActions
          sx={{
            padding: "0.5rem 1.5rem",
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "end",
          }}
        >
          <Box sx={{ display: "flex", gap: 1 }}>
            <Button
              onClick={() => {
                navigate(APP_END_POINTS?.REQUEST_BENCH);
                onClose();
                return;
              }}
              color="error"
              variant="outlined"
              sx={{
                height: 36,
                minWidth: "3.5rem",
                textTransform: "none",
                borderColor: "#cc3300",
                fontWeight: 500,
              }}
            >
              Cancel
            </Button>
            {initialPayload?.RequestType !==
              REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                <Button
                  onClick={handleOkClick}
                  variant="contained"
                  sx={{
                    height: 36,
                    minWidth: "3.5rem",
                    backgroundColor: "#3B30C8",
                    textTransform: "none",
                    fontWeight: 500,
                    "&:hover": {
                      backgroundColor: "#2c278f",
                    },
                  }}
                >
                  OK
                </Button>
              )}
          </Box>
        </DialogActions>
      </Dialog>

      <DownloadDialog
        onDownloadTypeChange={onDownloadTypeChange}
        open={openDownloadDialog}
        downloadType={downloadType}
        handleDownloadTypeChange={handleDownloadTypeChange}
        onClose={handleDownloadDialogClose}
      />
      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />
      {successMsg && (
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={messageDialogMessage}
          alertType={alertType}
          handleSnackBarClose={handleSnackBarClose}
        />
      )}
    </>
  );
};

export default ReusableDialogForHierarchy;
