import React, { useState, useCallback, useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button, TextField, Tooltip, IconButton } from "@mui/material";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import ReusableTable from "@components/Common/ReusableTable";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { useDispatch, useSelector } from "react-redux";
import { setManufacturerID, setUniqueAltUnit, setUOmData } from "../../../app/payloadslice";
import { useChangeLogUpdate } from "../../../hooks/useChangeLogUpdate";
import { CHANGE_LOG_STATUSES, DT_TABLES, ERROR_MESSAGES, MATERIAL_VIEWS } from "@constant/enum";
import useCustomDtCall from "@hooks/useCustomDtCall";
import { useLocation } from "react-router-dom";
import { showToast } from "../../../functions";
import { colors } from "../../../constant/colors";
import  useLang  from "@hooks/useLang";

const UnitsOfMeasureTab = (props) => {
  const dispatch = useDispatch();
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  const payloadState = useSelector((state) => state.payload);
  const UOMRows = payloadState[props.materialID]?.unitsOfMeasureData || [];
  let basicData = payloadState[props?.materialID]?.payloadData?.["Basic Data"];
  let headerData = payloadState[props.materialID]?.headerData;
  const uniqueAltUnit = payloadState[props.materialID]?.UniqueAltUnit || [];
  const { updateChangeLog } = useChangeLogUpdate();
  const { getDtCall, dtData } = useCustomDtCall();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get("RequestId");
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const { t } = useLang();

  const handleFieldChange = useCallback(
    (id, field, value) => {
      const updatedRow = {
        ...UOMRows?.find((row) => row.id === id),
        [field]: value,
      };

      if (field === "aUnit") {
        const selectedOption = dropDownData?.BaseUom?.find((item) => item.code === value);
        updatedRow.measureUnitText = selectedOption?.desc || "";
      }

      const updatedRows = UOMRows.map((row) => (row.id === id ? updatedRow : row));

      dispatch(setUOmData({ materialID: props.materialID, data: updatedRows }));

      if (requestId && !CHANGE_LOG_STATUSES.includes(initialPayload?.RequestStatus)) {
        updateChangeLog({
          materialID: props.selectedMaterialNumber,
          viewName: MATERIAL_VIEWS.ADDITIONAL_DATA,
          plantData: updatedRow.aUnit || "",
          fieldName: field,
          jsonName: field,
          currentValue: updatedRow[field],
          requestId: initialPayload?.RequestId,
          childRequestId:props.requestId,
          isUnitOfMeasure: true,
          uomId: updatedRow.UomId || null,
        });
      }
    },
    [UOMRows, props.materialID, props.selectedMaterialNumber, dispatch, updateChangeLog]
  );

  const renderDropdown = (params, options) => {
    const selectedValue = params?.value ? options?.find((opt) => opt.code === params.value) : null;

    return (
      <Tooltip title={selectedValue?.desc || t("No value selected")} arrow placement="top">
        <div style={{ width: "100%" }}>
          <SingleSelectDropdown
            options={params.field === "aUnit" ? dropDownData?.BaseUom : params.field === "eanCategory" ? dropDownData?.CategoryOfInternationalArticleNumberEAN : params.field === "unitsOfDimension" ? dropDownData?.BaseUom : params.field === "volumeUnit" ? dropDownData?.Volumeunit : params.field === "weightUnit" ? dropDownData?.UnitOfWt : []}
            value={selectedValue}
            onChange={(newValue) => {
              handleFieldChange(params.row.id, params.field, newValue?.code);
            }}
            disabled={props?.disabled ||  params?.field === "eanCategory"}
            placeholder={t("Select Option")}
            isOptionDisabled={(option) => uniqueAltUnit.includes(option.code)}
          />
        </div>
      </Tooltip>
    );
  };

  const renderTextField = (params) => {
    // Disable X and Y values for baseUomCode row
    const isBaseUomRow = params.row.aUnit === basicData?.basic?.BaseUom;
    const isXorYField = params.field === "xValue" || params.field === "yValue";
    const isDisabled = props?.disabled || (isBaseUomRow && isXorYField) || params?.field === "eanUpc";

    return (
      <TextField 
        fullWidth 
        size="small" 
        value={isBaseUomRow && isXorYField ? "1" : (params.value || "")}
        onChange={(e) => handleFieldChange(params.row.id, params.field, e.target.value)}
        disabled={isDisabled}
        InputProps={{
          sx: {
            "&.Mui-disabled": {
              "& input": {
                WebkitTextFillColor: colors.text.primary,
                color: colors.text.primary,
              }
            }
          },
        }}
      />
    );
  };

  const columns = [
    { field: "id", headerName: t("ID"), width: 80, hide: true },
    {
      field: "xValue",
      headerName: "X",
      width: 150,
      editable: false,
      renderCell: renderTextField,
    },
    {
      field: "aUnit",
      headerName: t("AUn"),
      width: 150,
      editable: false,
      renderCell: (params) => renderDropdown(params, dropDownData?.BaseUom),
    },
    {
      field: "yValue",
      headerName: t("Y"),
      width: 150,
      editable: false,
      renderCell: renderTextField,
    },
    {
      field: "eanUpc",
      headerName: t("EAN/UPC"),
      width: 150,
      editable: false,
      renderCell: renderTextField,
    },
    {
      field: "eanCategory",
      headerName: t("EAN Category"),
      width: 160,
      editable: false,
      renderCell: (params) => renderDropdown(params, dropDownData?.CategoryOfInternationalArticleNumberEAN),
    },
    {
      field: "length",
      headerName: t("Length"),
      width: 120,
      editable: false,
      renderCell: renderTextField,
    },
    {
      field: "width",
      headerName: t("Width"),
      width: 120,
      editable: false,
      renderCell: renderTextField,
    },
    {
      field: "height",
      headerName: t("Height"),
      width: 120,
      editable: false,
      renderCell: renderTextField,
    },
    {
      field: "unitsOfDimension",
      headerName: t("Unit of Dimension"),
      width: 160,
      editable: false,
      renderCell: (params) => renderDropdown(params, dropDownData?.BaseUom),
    },
    {
      field: "volume",
      headerName: t("Volume"),
      width: 120,
      editable: false,
      renderCell: renderTextField,
    },
    {
      field: "volumeUnit",
      headerName: t("Volume Unit"),
      width: 160,
      editable: false,
      renderCell: (params) => renderDropdown(params, dropDownData?.Volumeunit),
    },
    {
      field: "grossWeight",
      headerName: t("Gross Weight"),
      width: 140,
      editable: false,
      renderCell: renderTextField,
    },
    {
      field: "netWeight",
      headerName: t("Net Weight"),
      width: 140,
      editable: false,
      renderCell: renderTextField,
    },
    {
      field: "weightUnit",
      headerName: t("Weight Unit"),
      width: 160,
      editable: false,
      renderCell: (params) => renderDropdown(params, dropDownData?.UnitOfWt),
    },
    {
      field: "actions",
      headerName: t("Actions"),
      width: 100,
      sortable: false,
      renderCell: (params) => (
        <Tooltip title={params.row.isNew ? "Delete row" : "Cannot delete existing row"}>
          <span>
            <IconButton onClick={() => handleDeleteRow(params.row.id)} disabled={!params.row.isNew || props.disabled} size="small" color="error">
              <DeleteOutlineIcon fontSize="small" />
            </IconButton>
          </span>
        </Tooltip>
      ),
    },
  ];
  const getRegionMatTypePlantUOmData = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DT_TABLES.MDG_MAT_REGION_MATTYPE_PLANT_UOM_DT,
      version: "v1",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": payloadState?.payloadData?.Region,
          "MDG_CONDITIONS.MDG_MAT_MATERIAL_TYPE": payloadState?.payloadData?.Region === "EUR" ? "ALL" : headerData?.materialType?.code,
          "MDG_CONDITIONS.MDG_MAT_PLANT": payloadState?.payloadData?.Region === "US" ? "ALL" : headerData?.orgData?.length > 1 ? "1610" : headerData?.orgData[0]?.plant?.value?.code,
        },
      ],
    };
    getDtCall(payload);
  };
  useEffect(() => {
    if (!UOMRows?.length) getRegionMatTypePlantUOmData();
  }, []);

  useEffect(() => {
    if (dtData?.data?.result?.[0]) {
      let data = dtData?.data?.result?.[0].MDG_MAT_REGION_MATTYPE_PLANT_UOM_DT;

      if (!data || !Array.isArray(data)) {
        showToast(ERROR_MESSAGES?.NO_DATA_AVAILABLE, "error");
        return;
      }

      let uomArr = data.map((item, index) => {
        const selectedBaseUom = dropDownData?.BaseUom?.find((uom) => uom.code === item.MDG_MAT_UOM);
        const isBaseUom = item.MDG_MAT_UOM === basicData?.basic?.BaseUom;

        return {
          id: index + 1,
          uomId: null,
          xValue: isBaseUom ? "1" : "1",
          aUnit: item.MDG_MAT_UOM || "",
          measureUnitText: selectedBaseUom?.desc || "", // ✅ Set from dropDownData
          yValue: isBaseUom ? "1" : "1",
          bUnit: item.MDG_MAT_UOM || "",
          measurementUnitText: basicData?.basic?.BaseUom?.desc || "",
          eanUpc: "",
          eanCategory: "",
          autoCheckDigit: "",
          addEans: "",
          length: item.MDG_MAT_LENGTH || "",
          width: item.MDG_MAT_WIDTH || "",
          height: item.MDG_MAT_HEIGHT || "",
          unitsOfDimension: item.MDG_MAT_UNIT_DIMENSIONS || "",
          volume: item.MDG_MAT_VOLUME || "",
          volumeUnit: item.MDG_MAT_VOLUME_UNIT || "",
          grossWeight: item.MDG_MAT_GROSS_NET_WEIGHT || "",
          netWeight: "",
          weightUnit: item.MDG_MAT_WEIGHT_UNIT || "",
          noLowerLvlUnits: "",
          lowerLvlUnits: "",
          remVolAfterNesting: "",
          maxStackFactor: "",
          maxTopLoadFullPkg: "",
          UomToploadFullPkg: "",
          capacityUsage: "",
          UomCategory: "",
        };
      });

      // Sort to ensure baseUomCode row is first
      uomArr = uomArr.sort((a, b) => 
        a.aUnit === basicData?.basic?.BaseUom ? -1 : b.aUnit === basicData?.basic?.BaseUom ? 1 : 0
      );

      if (JSON.stringify(uomArr) !== JSON.stringify(payloadState[props.materialID]?.unitsOfMeasureData)) {
        dispatch(setUOmData({ materialID: props?.materialID, data: uomArr }));
      }
    }
  }, [dtData]);

  useEffect(() => {
    if (UOMRows?.length){
      const uniqueAUnits = [...new Set(UOMRows.map((item) => item.aUnit).filter(Boolean))];
      dispatch(setUniqueAltUnit({ materialID: props?.materialID, data: uniqueAUnits }));
    }
  }, [UOMRows]);

  const handleDeleteRow = (id) => {
    const updatedRows = UOMRows.filter((row) => row.id !== id);
    dispatch(setUOmData({ materialID: props?.materialID, data: updatedRows }));
  };

  return (
    <div>
      <Grid container direction="row" sx={{ backgroundColor: "white", padding: 2 }}>
        <Grid item xs={12} mt={4}>
          <ReusableTable
            rows={UOMRows}
            columns={columns}
            getRowIdValue="id"
            hideFooter={false}
            checkboxSelection={false}
            disableSelectionOnClick={true}
            onCellEditCommit={(params) => {
              handleFieldChange(params.id, params.field, params.value);
            }}
            width="100%"
            title={t("Units of Measure/ EANs/ Dimensions")}
            showSearch={false}
            showRefresh={false}
            showExport={false}
            showFilter={true}
            showColumns={true}
          />

          {!props?.disabled && (
            <Button
              variant="outlined"
              sx={{ mt: 2 }}
              onClick={() => {
                const newId = UOMRows.length > 0 ? Math.max(...UOMRows.map((row) => row.id)) + 1 : 1;
                const newRow = {
                  id: newId,
                  isNew: true,
                  uomId: null,
                  xValue: "1",
                  aUnit: "",
                  measureUnitText: "",
                  yValue: "1",
                  bUnit: "",
                  measurementUnitText: "",
                  eanUpc: "",
                  eanCategory: "",
                  autoCheckDigit: "",
                  addEans: "",
                  length: "",
                  width: "",
                  height: "",
                  unitsOfDimension: "",
                  volume: "",
                  volumeUnit: "",
                  grossWeight: "",
                  netWeight: "",
                  weightUnit: "",
                  noLowerLvlUnits: "",
                  lowerLvlUnits: "",
                  remVolAfterNesting: "",
                  maxStackFactor: "",
                  maxTopLoadFullPkg: "",
                  UomToploadFullPkg: "",
                  capacityUsage: "",
                  UomCategory: "",
                };

                dispatch(
                  setUOmData({
                    materialID: props?.materialID,
                    data: [...UOMRows, newRow],
                  })
                );
              }}
            >
              {t("Add Row")}
            </Button>
          )}
        </Grid>
      </Grid>
    </div>
  );
};

export default UnitsOfMeasureTab;
