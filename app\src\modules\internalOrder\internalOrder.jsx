import React from "react";
import MasterDataLayout from "@components/Common/MasterDataLanding/MasterDataLayout";
import { useNavigate } from "react-router-dom";
import useLang from "@hooks/useLang";

const internalOrder = () => {
  const { t } = useLang();
  const navigate = useNavigate();
  
  const buttonConfigs = [
    {
      key: "createRequest",
      label: t("Create Request"),
      condition: true,
      onClick: () => {
        navigate("/masterDataCockpit/internalOrder/createRequestforInternalOrder");
      },
    },
    {
      key: "sapExport",
      label: t("SAP Data Export"),
      condition: true,
      // onClick: () => setOpenExportSearch(true),
    },
  ];
  return (
    <div>
      <MasterDataLayout heading={"Internal Order"} description={"This view displays the list of Internal Orders"} buttonConfigs={buttonConfigs} />
    </div>
  );
};

export default internalOrder;
