import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  FormControl,
  FormControlLabel,
  RadioGroup,
  Radio,
  Button,
  Tooltip,
  Box,
  Alert,
  IconButton,
} from "@mui/material";
import {Close, FileDownload, Email } from "@mui/icons-material";
import HelpOutlineTwoToneIcon  from '@mui/icons-material/HelpOutlineTwoTone';
import { styled } from "@mui/material/styles";
import { colors } from "@constant/colors";
import { ERROR_MESSAGES } from "@constant/enum";
 
// Custom styled components
const ProDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiPaper-root": {
    borderRadius: "12px",
    boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
    border: `1px solid ${colors?.placeholder?.color}`,
    backgroundColor: colors?.primary?.white,
    maxWidth: "600px",
  },
}));
 
const ProButton = styled(But<PERSON>)(({ theme }) => ({
  borderRadius: "8px",
  padding: "1.2rem 1rem !important",
  backgroundColor: colors?.primary?.lightPlus,
  "&:hover": {
    backgroundColor: colors?.info?.dark,
    boxShadow: "0 2px 8px rgba(25, 118, 210, 0.3)",
  },
  transition: "all 0.2s ease-in-out",
  textTransform: "none",
  fontWeight: 500,
}));
 
const ProAlert = styled(Alert)(({ theme }) => ({
    borderRadius: "6px",
    backgroundColor: colors?.secondary?.lightYellow,
    display: "flex", // Ensures flex alignment
    alignItems: "center", // Centers items vertically
    "& .MuiAlert-icon": {
      display: "flex",
      alignItems: "center", // Centers the icon inside its container
      justifyContent: "center",
    },
    marginTop: "1rem",
  }));
 
 
const NoMaxWidthTooltip = styled(Tooltip)({
  maxWidth: "none",
});
 
const DownloadDialog = ({ onDownloadTypeChange,open, downloadType, handleDownloadTypeChange, onClose }) => {
  // Alert content based on selection
  const alertMessage = downloadType === "systemGenerated"
  ? ERROR_MESSAGES?.SYSTEM_GENERATED_MSG
  : ERROR_MESSAGES?.EMAIL_DELIVERY_MSG;
 
  return (
    <ProDialog open={open} onClose={onClose}>
      <DialogTitle
        sx={{
          backgroundColor: colors?.success?.light,
          padding: "1rem 1.5rem",
          borderBottom: `1px solid ${colors?.primary?.whiteSmoke}`,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb:2,
        }}
      >
        <Typography
          variant="h6"
          sx={{ fontWeight: 600, color: "#333", letterSpacing: "0.2px" }}
        >
          Select Download Option
        </Typography>
        <IconButton size="small" onClick={onClose}>
          <Close fontSize="small" />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ padding: "1.5rem" }}>
        <FormControl component="fieldset" sx={{ width: "100%" }}>
          <RadioGroup
            aria-label="download-option"
            name="download-option"
            value={downloadType}
            onChange={handleDownloadTypeChange}
            sx={{ display: "flex", flexDirection: "row", gap: 2, alignItems: "center" }} // Centered alignment
            >
            <Box
              sx={{
                flex: 1,
                padding: "0.4rem",
                borderRadius: "6px",
                backgroundColor:
                  downloadType === "systemGenerated" ? "#f0f4ff" : "#ffffff",
                border:
                  downloadType === "systemGenerated"
                    ? "1px solid #1976d2"
                    : "1px solid #e0e0e0",
                transition: "all 0.2s ease-in-out",
                "&:hover": {
                  backgroundColor: "#f7f9fc",
                },
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <FormControlLabel
                value="systemGenerated"
                control={<Radio color="primary" size="small" />}
                label={
                    <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                    <FileDownload sx={{ mr: 1, color: "#1976d2" }} />
                    <Typography sx={{ fontWeight: 500, color: "#333", fontSize: "0.85rem" }}>
                      System-Generated
                    </Typography>
                  </Box>
                }
                sx={{ flexGrow: 1, margin: 0 }}
              />
              <NoMaxWidthTooltip
                title={<span style={{ whiteSpace: "nowrap", fontSize: "12px" }}>Download Excel file instantly</span>}
                arrow
                placement="top"
              >
                <HelpOutlineTwoToneIcon
                  sx={{
                    color: "#1976d2",
                    fontSize: "1.1rem",
                    verticalAlign: "middle",
                    mr: 1,
                  }}
                />
              </NoMaxWidthTooltip>
            </Box>
 
            <Box
              sx={{
                flex: 1,
                padding: "0.4rem",
                borderRadius: "8px",
                backgroundColor:
                  downloadType === "mailGenerated" ? "#f0f4ff" : "#ffffff",
                border:
                  downloadType === "mailGenerated"
                    ? "1px solid #1976d2"
                    : "1px solid #e0e0e0",
                transition: "all 0.2s ease-in-out",
                "&:hover": {
                  backgroundColor: "#f7f9fc",
                },
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
               
              }}
            >
              <FormControlLabel
                value="mailGenerated"
                control={<Radio color="primary" size="small" />}
                label={
                    <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                    <Email sx={{ mr: 1, color: "#1976d2" }} />
                    <Typography sx={{ fontWeight: 500, color: "#333", fontSize: "0.85rem" }}>
                      Mail-Generated
                    </Typography>
                  </Box>
                }
                sx={{ flexGrow: 1, margin: 0 }}
              />
              <NoMaxWidthTooltip
                title={<span style={{ whiteSpace: "nowrap", fontSize: "12px" }}>Receive the Excel file via email</span>}
                arrow
                placement="top"
              >
                <HelpOutlineTwoToneIcon
                  sx={{
                    color: "#1976d2",
                    fontSize: "1.1rem",
                    verticalAlign: "middle",
                    mr: 1,
                  }}
                />
              </NoMaxWidthTooltip>
            </Box>
          </RadioGroup>
 
          {/* Dynamic Alert based on selection */}
          <ProAlert severity="info">
            <Typography sx={{ fontSize: "0.9rem", color: "#555" }}>
              {alertMessage[0]}
            </Typography>
          </ProAlert>
          <ProAlert severity="info">
            <Typography sx={{ fontSize: "0.9rem", color: "#555" }}>
              {alertMessage[1]}
            </Typography>
          </ProAlert>
        </FormControl>
      </DialogContent>
      <DialogActions sx={{ padding: "0 1.5rem 1.5rem" }}>
        <ProButton
          variant="contained"
          onClick={onDownloadTypeChange}
          startIcon={downloadType === "systemGenerated" ? <FileDownload /> : <Email />}
        >
          {downloadType === "systemGenerated" ? "Download" : "Send Email"}
        </ProButton>
      </DialogActions>
    </ProDialog>
  );
};
 
export default DownloadDialog;