{"welcomeFile": "/index.html", "authenticationMethod": "route", "logout": {"logoutEndpoint": "/do/logout", "logoutPage": "index.html"}, "routes": [{"source": "^/cw-mdg-materialmanagement-dest/", "target": "/", "destination": "cw-mdg-materialmanagement-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-articlemanagement-dest/", "target": "/", "destination": "cw-mdg-articlemanagement-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-notification-dest/", "target": "/", "destination": "cw-mdg-notification-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-artificialintelligence-dest/", "target": "/", "destination": "cw-mdg-artificialintelligence-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-dashboard-dest/", "target": "/", "destination": "cw-mdg-dashboard-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-slamanagement-dest/", "target": "/", "destination": "cw-mdg-slamanagement-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-costcenter-dest/", "target": "/", "destination": "cw-mdg-costcenter-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-costcenter-mass-dest/", "target": "/", "destination": "cw-mdg-costcenter-mass-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-profitcenter-dest/", "target": "/", "destination": "cw-mdg-profitcenter-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-profitcenter-mass-dest/", "target": "/", "destination": "cw-mdg-profitcenter-mass-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-notification-dest/", "target": "/", "destination": "cw-mdg-notification-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-bankkey-dest/", "target": "/", "destination": "cw-mdg-bankkey-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-generalledger-dest/", "target": "/", "destination": "cw-mdg-general<PERSON><PERSON>-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-billofmaterial-dest/", "target": "/", "destination": "cw-mdg-billofmaterial-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-internalorder-dest/", "target": "/", "destination": "cw-mdg-internalorder-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-generalledger-mass-dest/", "target": "/", "destination": "cw-mdg-general<PERSON>r-mass-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-documentmanagement-dest/", "target": "/", "destination": "cw-mdg-documentmanagement-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-admin-dest/", "target": "/", "destination": "cw-mdg-admin-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-iwm-dev/", "target": "/", "destination": "cw-mdg-iwm-dev", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-mdg-iwa-oauth2-dest/", "target": "/", "destination": "cw-mdg-iwa-oauth2-dest", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/WorkNetServices/", "target": "/", "destination": "WorkNetServices", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/CrudApiServices/", "target": "/", "destination": "CrudServices", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/WorkUtilsServices/", "target": "/", "destination": "cw-mdg-messaging-dev", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/WorkUtilsServicesHana/", "target": "/", "destination": "cw-mdg-messaging-dev", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/ITMJavaServices/", "target": "/", "destination": "ItmCoreServices", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/NotificationServices/", "target": "/", "destination": "MessagingServices", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/cw-caf-idm-services", "target": "/rest", "destination": "cw-caf-idm-services", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/IWAApi/(.*)$", "target": "/$1", "destination": "cw-caf-iwa-services", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^/IDMApi/(.*)$", "target": "/rest/$1", "destination": "cw-caf-idm-services", "authenticationType": "xsuaa", "csrfProtection": false}, {"source": "^(.*)$", "target": "mdgdev/$1", "service": "html5-apps-repo-rt", "authenticationType": "xsuaa"}]}