import { useSelector, useDispatch } from 'react-redux';
import { addSnackbar, removeSnackbar, closeSnackbar } from '../app/snackbarSlice';

export const useSnackbar = () => {
  const dispatch = useDispatch();
  const snackbars = useSelector((state) => state.snackbar?.snackbars);

  const showSnackbar = (message, type = 'info', autoHideDuration = 3000) => {
    dispatch(addSnackbar({ message, type, autoHideDuration }));
  };

  const handleCloseSnackbar = (id) => {
    dispatch(closeSnackbar(id));
    setTimeout(() => {
      dispatch(removeSnackbar(id));
    }, 300);
  };

  const handleRemoveSnackbar = (id) => {
    dispatch(removeSnackbar(id));
  };

  return {
    snackbars,
    showSnackbar,
    handleCloseSnackbar,
    handleRemoveSnackbar,
  };
};