import { Accordion, AccordionDetails, AccordionSummary, Box, Grid, Tooltip, Typography,Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Stack,} from '@mui/material';
import React from 'react'
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useSelector } from 'react-redux';
import moment from 'moment';
import { FIELD_TYPE } from '@constant/enum';

const PreviewChange = () => {

       const changeData = useSelector((state) => state.tabsData.changeFieldsDT);
       const dropDownData = useSelector((state) => state.AllDropDown.dropDown || {})
          const configData = changeData?.["Config Data"];
          const keys = configData && Object.keys(configData);
          const changeFieldRows = useSelector((state) => state.payload.changeFieldRows)

          const renderTable = (key, data) => {
            const rowData = 
            Array.isArray(changeFieldRows)?changeFieldRows||[]:changeFieldRows[key] || [];
          
            if (!rowData.length) {
              return (
                <div style={{ padding: "16px", textAlign: "center", color: "#757575" }}>
                  <Typography variant="body1">No data available</Typography>
                </div>
              );
            }
          
            // Determine which columns have all empty values
            const columnVisibility = data.map((field) => {
              const isColumnEmpty = rowData.every(
                (row) =>
                  row[field.jsonName] === undefined ||
                  row[field.jsonName] === null ||
                  row[field.jsonName] === ""
              );
              return true; // True if column has at least one non-empty value
            });
          
            // Filter data to only include visible columns
            const visibleData = data.filter((_, index) => columnVisibility[index]);
          
            if (!visibleData.length) {
              return (
                <div style={{ padding: "16px", textAlign: "center", color: "#757575" }}>
                  <Typography variant="body1">No visible data available</Typography>
                </div>
              );
            }
          
            return (
              <div style={{ width: "100%", padding: "16px" }}>
                <TableContainer
                  component={Paper}
                  sx={{
                    borderRadius: "12px",
                    boxShadow: "0 4px 20px rgba(0, 0, 0, 0.05)",
                    overflowX: "auto", // Enable horizontal scrolling
                    border: "1px solid #e0e0e0",
                    backgroundColor: "#fff",
                    maxWidth: "100%", // Ensure it fits the container
                  }}
                >
                  <Table sx={{ minWidth: 650, tableLayout: "auto" }} aria-label="beautiful table">
                    {/* Table Header */}
                    <TableHead>
                      <TableRow
                        sx={{
                          backgroundColor: "#f5f7fa",
                          "& th": {
                            fontWeight: 600,
                            color: "#333",
                            borderBottom: "2px solid #e0e0e0",
                            padding: "14px 16px",
                            textAlign: "left",
                            verticalAlign: "middle",
                            whiteSpace: "nowrap",
                          },
                        }}
                      >
                        {visibleData.map((field, index) => (
                          <TableCell
                            key={field.jsonName}
                            sx={{
                              ...(index === 0 && {
                                position: "sticky",
                                left: 0,
                                zIndex: 1,
                                backgroundColor: "#f5f7fa", // Match header background
                                borderRight: "1px solid #e0e0e0", // Separator for sticky column
                                minWidth: "150px", // Ensure readable width for fixed column
                              }),
                            }}
                          >
                            <Tooltip title={field.fieldName}>
                              <Typography
                                variant="body1"
                                sx={{
                                  fontSize: "12px",
                                  fontWeight: 600,
                                  overflow: "hidden",
                                  textOverflow: "ellipsis",
                                  maxWidth: "250px",
                                }}
                              >
                                {field.fieldName}
                              </Typography>
                            </Tooltip>
                          </TableCell>
                        ))}
                      </TableRow>
                    </TableHead>
          
                    {/* Table Body */}
                    <TableBody>
                      {rowData.map((row, rowIndex) => (
                        <TableRow
                          key={row.id || rowIndex}
                          sx={{
                            "&:hover": {
                              backgroundColor: "#fafafa",
                              transition: "background-color 0.2s ease",
                            },
                            "& td": {
                              borderBottom: "1px solid #e0e0e0",
                              padding: "12px 16px",
                              textAlign: "left",
                              verticalAlign: "middle",
                            },
                            "&:last-child td": {
                              borderBottom: "none",
                            },
                          }}
                        >
                          {visibleData.map((field, index) => {
                            const value = row[field.jsonName];
                            let displayValue = value;
          
                            // Handle different field types
                            if (field.fieldType === FIELD_TYPE.DROPDOWN) {
                              const fieldValue = dropDownData?.[field?.jsonName]?.find(
                                (item) => item.code === value
                              );
                              displayValue = fieldValue
                                ? `${fieldValue.code} - ${fieldValue.desc}`
                                : value || "--";
                            } else if (field.fieldType === FIELD_TYPE.DATE_FIELD && value) {
                              const parseDate = () => {
                                if (value.startsWith("/Date(") && value.endsWith(")/")) {
                                  const timestamp = parseInt(value.slice(6, -2));
                                  return new Date(timestamp);
                                }
                                if (typeof value === "string" && value.match(/^\d{4}-\d{2}-\d{2}/)) {
                                  return new Date(value);
                                }
                                return moment(value, [
                                  "YYYY-MM-DD HH:mm:ss.S",
                                  "DD MMM YYYY HH:mm:ss UTC",
                                ]).toDate();
                              };
                              displayValue = parseDate()?.toLocaleDateString() || "--";
                            } else {
                              displayValue = value || "--";
                            }
          
                            return (
                              <TableCell
                                key={`${row.id}-${field.jsonName}`}
                                sx={{
                                  ...(index === 0 && {
                                    position: "sticky",
                                    left: 0,
                                    zIndex: 1,
                                    backgroundColor: "#fff", // Match body background
                                    borderRight: "1px solid #e0e0e0", // Separator for sticky column
                                    minWidth: "150px", // Consistent with header
                                  }),
                                }}
                              >
                                {value !== undefined && value !== null && value !== "" ? (
                                  <Tooltip title={displayValue}>
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        fontSize: "13px",
                                        color: "#555",
                                        fontWeight: 500,
                                        whiteSpace: "nowrap",
                                        overflow: "hidden",
                                        textOverflow: "ellipsis",
                                        maxWidth: "250px",
                                        letterSpacing: "0.5px",
                                      }}
                                    >
                                      {displayValue}
                                    </Typography>
                                  </Tooltip>
                                ) : (
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      fontSize: "13px",
                                      color: "#bdbdbd",
                                      fontStyle: "italic",
                                    }}
                                  >
                                    --
                                  </Typography>
                                )}
                              </TableCell>
                            );
                          })}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </div>
            );
          };
  if (keys?.length === 1) {
    const key = keys[0];
    const data = configData[key];
    return (
      <>
       <Accordion key={key} sx={{ marginBottom: "20px", boxShadow: 3}} expanded>
                  <AccordionSummary
                  
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls={`${key}-content`}
                    id={`${key}-header`}
                    sx={{
                      backgroundColor: "#f5f5f5",
                      // borderRadius: "10px",
                      padding: "8px 16px",
                      "&:hover": { backgroundColor: "#f5f5f5", },
                    }}
                  >
                    <Typography variant="h6" sx={{ fontWeight: "bold",pl:1 }}>{key}</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
           
                    {renderTable(key, data)}
                  </AccordionDetails>
                </Accordion>
            

       
      </>
    )
  }

  // Multiple key-value pairs: Render accordions with tables inside
  return (
    <>
     
   
          {configData && (

            <Stack spacing={2}>
         
   
              {keys?.map((key) => (
                <Accordion key={key} sx={{  boxShadow: 3}} expanded>
                  <AccordionSummary
                  
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls={`${key}-content`}
                    id={`${key}-header`}
                    sx={{
                      backgroundColor: "#f5f5f5",
                      // borderRadius: "10px",
                      padding: "8px 16px",
                      "&:hover": { backgroundColor: "#f5f5f5", },
                    }}
                  >
                    <Typography variant="h6" sx={{ fontWeight: "bold" }}>{key}</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
           
                    {renderTable(key, configData[key])}
                  </AccordionDetails>
                </Accordion>
              ))}
            </Stack>
          )}
    
      
    </>
  );
}

export default PreviewChange