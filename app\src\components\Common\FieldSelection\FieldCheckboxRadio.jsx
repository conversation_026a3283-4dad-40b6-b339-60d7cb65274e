import React from 'react';
import {
  Checkbox,
  FormControl,
  FormControlLabel,
  FormLabel,
  Grid,
  Radio,
  RadioGroup,
} from '@mui/material';
import { font_Small } from '../commonStyles';
const FieldCheckboxRadio = ({
  field,
  checked,
  radioValue,
  onCheckboxChange,
  onRadioChange,
  isDisabled
}) => {
  return (
    <Grid container md={6} alignItems="center">
      <FormControl fullWidth>
        <Grid container alignItems="center">
          <Grid item md={1}>
            <Checkbox
              checked={checked}
              onChange={onCheckboxChange}
              disabled={isDisabled}
            />
          </Grid>
          <Grid item md={4}>
            <FormLabel sx={font_Small}>{field}:</FormLabel>
          </Grid>
          <Grid item md={7}>
            <RadioGroup
            sx={{display:"flex", justifyContent:"center"}}
              row
              value={radioValue==="0"?"Mandatory":radioValue}
              onChange={onRadioChange}
            >
              <FormControlLabel
                value="Mandatory"
                control={<Radio />}
                label="Mandatory"
                disabled={isDisabled?true:!checked}
              />
              <FormControlLabel
                value="Hide"
                control={<Radio />}
                label="Hide"
                disabled={isDisabled?true:!checked}
              />
              <FormControlLabel
                value="Optional"
                control={<Radio />}
                label="Optional"
                disabled={isDisabled?true:!checked}
              />
            </RadioGroup>
          </Grid>
        </Grid>
      </FormControl>
    </Grid>
  );
};

export default FieldCheckboxRadio;

