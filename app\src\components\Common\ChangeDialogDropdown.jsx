import React, { useState, forwardRef, useRef, useEffect, useCallback } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tooltip,
  Box,
  Typography,
  Slide,
  FormControl,
  FormControlLabel,
  tooltipClasses,
  RadioGroup,
  IconButton,
  Radio,
  Tabs,
  Tab,
} from "@mui/material";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import styled from "@emotion/styled";
import { doAjax } from "./fetchService";
import { DataGrid } from "@mui/x-data-grid";
import { destination_MaterialMgmt } from "../../destinationVariables";
import { useDispatch, useSelector } from "react-redux";
import { setRequestorPayload } from "../../app/payloadslice";
import useChangeMaterialRowsRequestor from "../../hooks/useChangeMaterialRowsRequestor";
import ReusableBackDrop from "./ReusableBackDrop";
import ReusableSnackBar from "./ReusableSnackBar";
import DownloadDialog from "./DownloadDialog";
import { useNavigate } from "react-router-dom";
import { MANDATORY_FILTERS, TEMPLATE_KEYS, Templates } from "@constant/changeTemplates";
import { saveExcel, showToast } from "../../functions";
import { ERROR_MESSAGES, REQUEST_TYPE, API_CODE } from "@constant/enum";
import { colors } from "@constant/colors";
import FilterChangeDropdown from './ui/dropdown/FilterChangeDropdown';

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});

const ChangeDialogDropdown = ({ open, onClose, parameters, templateName, setShowTable, allDropDownData }) => {
  const [selectedValues, setSelectedValues] = useState({});
  const [convertedValues, setConvertedValues] = useState({});
  const [errors, setErrors] = useState({});
  const [blurLoading, setBlurLoading] = useState("");
  const [successMsg, setSuccessMsg] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [loaderMessage, setLoaderMessage] = useState("");
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [errorTextMessage, setErrorTextMessage] = useState("");
  const [errorText, setErrorText] = useState(false);
  const initialPayload = useSelector((state) => state.profitCenter.payload.requestHeaderData);
  const RequestId = useSelector((state) => state.request.requestHeader.requestId);
  const loadForFetching = useSelector((state) => state.payload.dataLoading);
  const [activeTab, setActiveTab] = useState(0);
  const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
  const [popoverContent, setPopoverContent] = useState("");
  const [isPopoverVisible, setIsPopoverVisible] = useState(false);
  const popoverRef = useRef(null);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { fetchDisplayDataRequestor } = useChangeMaterialRowsRequestor();
  
  const NoMaxWidthTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))({
    [`& .${tooltipClasses.tooltip}`]: {
      maxWidth: "none",
    },
  });

  const handlePopoverOpen = (event, content) => {
    setPopoverAnchorEl(event.currentTarget);
    setPopoverContent(content);
    setIsPopoverVisible(true);
  };

  const handlePopoverClose = () => {
    setIsPopoverVisible(false);
  };

  const handleMouseEnterPopover = () => {
    setIsPopoverVisible(true);
  };

  const handleMouseLeavePopover = () => {
    setIsPopoverVisible(false);
  };

  const popoverOpen = Boolean(popoverAnchorEl);
  const popoverId = popoverOpen ? "custom-popover" : undefined;

  const handleSelectionChange = (key, newValue) => {
    setSelectedValues((prev) => ({
      ...prev,
      [key]: newValue,
    }));
    if (newValue.length > 0) {
      setErrors((prev) => ({
        ...prev,
        [key]: "",
      }));
    }
  };

  const handleDownload = () => {
    setLoaderMessage("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience.");
    setBlurLoading(true);
    onClose();
    let templateKeys = Templates[initialPayload?.TemplateName]?.map(item => item.key) || [];
    let payload = {};
    if(activeTab===0){
      payload = {
      materialDetails : [templateKeys.reduce((acc, key) => {
        acc[key] = convertedValues?.[key] ? convertedValues?.[key] : "";
        return acc;
      }, {})],
      templateHeaders : initialPayload?.FieldName ? initialPayload.FieldName?.join("$^$"): "",
      requestId: RequestId || initialPayload?.RequestId || "",
      templateName : initialPayload?.TemplateName ? initialPayload.TemplateName : "",
      dtName : "MDG_MAT_CHANGE_TEMPLATE",
      version : "v4",
      rolePrefix : ""
    }
    }
    else{
      payload = {
        materialDetails : [templateKeys.reduce((acc, key) => {
          acc[key] = rowsOfMaterialData.map(row => row[key]?.trim()).filter(value => value !== "")
          .join(",") || "";
          return acc;
        }, {})],
        templateHeaders : initialPayload?.FieldName ? initialPayload.FieldName?.join("$^$"): "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName : initialPayload?.TemplateName ? initialPayload.TemplateName : "",
        dtName : "MDG_MAT_CHANGE_TEMPLATE",
        version : "v4",
        rolePrefix : ""
      }
    }
    const hSuccess = (response) => {

      if(response?.size==0){
        setBlurLoading(false);
        setLoaderMessage("");
        showToast(ERROR_MESSAGES?.NO_DATA_FOUND, "error", {position: "top-center", largeWidth: true,});
        setTimeout(() => {
          navigate(APP_END_POINTS?.REQUEST_BENCH);
        }, 2600);
        return
      }
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute("download", `${initialPayload.TemplateName}_Mass Change.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");

      setSuccessMsg(true);
      setMessageDialogMessage(`${initialPayload.TemplateName}_Mass Change.xlsx has been downloaded successfully.`);
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    }
    const hError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      showToast(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL, "error", {position : "top-center"});
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    }
    doAjax(`/${destination_MaterialMgmt}/excel/downloadExcelWithData`, "postandgetblob", hSuccess, hError, payload)
  };

  const handleEmailDownload = () => {
    setBlurLoading(true);
    onClose();
    let templateKeys = Templates[initialPayload?.TemplateName]?.map(item => item.key) || [];
    let payload = {};
    if(activeTab===0){
      payload = {
      materialDetails : [templateKeys.reduce((acc, key) => {
        acc[key] = convertedValues?.[key] ? convertedValues?.[key] : "";
        return acc;
      }, {})],
      templateHeaders : initialPayload?.FieldName ? initialPayload.FieldName?.join("$^$"): "",
      requestId: RequestId || initialPayload?.RequestId || "",
      templateName : initialPayload?.TemplateName ? initialPayload.TemplateName : "",
      dtName : "MDG_MAT_CHANGE_TEMPLATE",
      version : "v4",
      rolePrefix : ""
    }
    }
    else{
      payload = {
        materialDetails : [templateKeys.reduce((acc, key) => {
          acc[key] = rowsOfMaterialData.map(row => row[key]?.trim()).filter(value => value !== "")
          .join(",") || "";
          return acc;
        }, {})],
        templateHeaders : initialPayload?.FieldName ? initialPayload.FieldName?.join("$^$"): "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName : initialPayload?.TemplateName ? initialPayload.TemplateName : "",
        dtName : "MDG_MAT_CHANGE_TEMPLATE",
        version : "v4",
        rolePrefix : ""
      }
    }
    const hSuccess = () => {

      setBlurLoading(false);
      setLoaderMessage("");
      setSuccessMsg(true);
      setMessageDialogMessage(`Download has been started. You will get the Excel file via email.`);
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    }
    const hError = () => {
      setBlurLoading(false);
      setSuccessMsg(true);
      setMessageDialogMessage(
        "Oops! Something went wrong. Please try again later."
      );
      setAlertType("danger");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    }
    doAjax(`/${destination_MaterialMgmt}/excel/downloadExcelWithDataInMail`, "postandgetblob", hSuccess, hError, payload)

  };

  const handleDownloadDialogOpen = () => {
    setOpenDownloadDialog(true);
  }

  const handleDownloadDialogClose = () => {
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
  };

  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };

  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };

  useEffect(() => {
    setConvertedValues(convertedData(selectedValues));
    dispatch(setRequestorPayload(convertedData(selectedValues)));
  }, [selectedValues]);

  const handleSelectAll = (key, allOptions) => {
    const allSelected = selectedValues[key]?.length === allOptions.length;
    setSelectedValues((prev) => ({
      ...prev,
      [key]: allSelected ? [] : allOptions,
    }));
    if (!allSelected) {
      setErrors((prev) => ({
        ...prev,
        [key]: "",
      }));
    }
  };

  const convertedData = (data) => {
    const result = {};
  
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        // Map the code values and join them with commas
        result[key] = data[key].map((item) => item.code).join(",");
      }
    }
  
    return result;
  };

  

  const handleOkClick = async () => {

    try {
      const result = await fetchDisplayDataRequestor(templateName, convertedValues);

      if (result && result.length > 0) {
        setErrorText(false);
        setShowTable(true);
      } else {
        setErrorText(true);
        setErrorTextMessage('No data found for the selected criteria.');
      }
    } catch (error) {
      setErrorText(true);
      setErrorTextMessage('Error fetching data.');
    }
  };

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const renderAutocomplete = (param) => {
    return (
      <FilterChangeDropdown
        param={param}
        dropDownData={allDropDownData}
        allDropDownData={allDropDownData}
        selectedValues={selectedValues}
        handleSelectAll={handleSelectAll}
        handleSelectionChange={handleSelectionChange}
        errors={errors}
        formatOptionLabel={(option) => `${option.code} - ${option.desc || ''}`}
        handlePopoverOpen={handlePopoverOpen}
        handlePopoverClose={handlePopoverClose}
        handleMouseEnterPopover={handleMouseEnterPopover}
        handleMouseLeavePopover={handleMouseLeavePopover}
        isPopoverVisible={isPopoverVisible}
        popoverId={popoverId}
        popoverAnchorEl={popoverAnchorEl}
        popoverRef={popoverRef}
        popoverContent={popoverContent}
      />
    );
  };

  return (
    <Dialog
      open={open}
      TransitionComponent={Transition}
      onClose={() => {}}
      maxWidth="sm"
      fullWidth
    >
    <Box
    sx={{
        backgroundColor: "#e3f2fd",
        padding: "1rem 1.5rem",
        display: "flex",
        alignItems: "center",
    }}
    >
    <FeedOutlinedIcon color="primary" sx={{ marginRight: "0.5rem" }} />
        <Typography variant="h6" component="div" color="primary">
            {templateName} Search Filter(s)
        </Typography>
    </Box>
      <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
        {parameters?.map((param) => (
          <Box key={param.key} sx={{ marginBottom: "1rem" }}>
            {renderAutocomplete(param)}
          </Box>
        ))}
        {errorText && (
          <Typography variant="h6" color={colors?.error?.dark}>* {errorTextMessage}</Typography>
        )}
        <ReusableBackDrop
          blurLoading={loadForFetching}
        />
      </DialogContent>
      <DialogActions 
        sx={{ 
          padding: "0.5rem 1.5rem", 
          display: "flex", 
          justifyContent: "space-between", 
          alignItems: "center" 
        }}
      >
        <Box sx={{ display: "flex", gap: 1 }}>
          <Button
            onClick={() => {
              if(initialPayload?.RequestType === REQUEST_TYPE?.CHANGE) {
                navigate("/requestbench");
                onClose();
                return;
              }
              onClose();
            }}
            color="error"
            variant="outlined"
            sx={{
              height: 36,
              minWidth: "3.5rem",
              textTransform: "none",
              borderColor: "#cc3300",
              fontWeight: 500,
            }}
          >
            Cancel
          </Button>
          {initialPayload?.RequestType !== REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
            <Button
              onClick={handleOkClick}
              variant="contained"
              sx={{
                height: 36,
                minWidth: "3.5rem",
                backgroundColor: "#3B30C8",
                textTransform: "none",
                fontWeight: 500,
                "&:hover": {
                  backgroundColor: "#2c278f",
                },
              }}
            >
              OK
            </Button>
          )}
            {initialPayload?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
            <Button
              onClick={() => {
                handleDownloadDialogOpen()
              }}
              variant="contained"
              sx={{
                height: 36,
                minWidth: "3.5rem",
                backgroundColor: "#3B30C8",
                textTransform: "none",
                fontWeight: 500,
                "&:hover": {
                  backgroundColor: "#2c278f",
                },
              }}
            >
              Download
            </Button>
          )}
        </Box>
      </DialogActions>
      <DownloadDialog
            onDownloadTypeChange={onDownloadTypeChange}
            open={openDownloadDialog}
            downloadType={downloadType}
            handleDownloadTypeChange={handleDownloadTypeChange}
            onClose={handleDownloadDialogClose}
        />
      <ReusableSnackBar
        openSnackBar={openSnackbar}
        alertMsg={messageDialogMessage}
        alertType={alertType}
        handleSnackBarClose={handleSnackBarClose}
      />
    </Dialog>
  );
};

export default ChangeDialogDropdown;
