import { END_POINTS } from "@constant/apiEndPoints";
import { API_CODE } from "@constant/enum";

const useCountryBasedOnPlant = ({ 
  doAjax, 
  customError, 
  fetchDataAndDispatch, 
  destination_MaterialMgmt 
}) => {

  const getContryBasedOnPlant = (plant) => {
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        const countryOriValue = data?.body[0]?.code;
        if (countryOriValue) {
          fetchDataAndDispatch(
            `/${destination_MaterialMgmt}${END_POINTS.DATA.GET_COMMODITY_CODE_BASED_ON_COUNTRY}?country=${countryOriValue}`,
            "CommCode",
            "get",
            {plant},
            true
          );
          fetchDataAndDispatch(
            `/${destination_MaterialMgmt}${END_POINTS.DATA.GET_HTS_CODE}?country=${countryOriValue}`,
            "HtsCode",
            "get",
            { plant },
            true
          );
        }
      }
    };
    const hError = (error) => {
      customError(error);
    };
    
    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS.DATA.GET_COUNTRY_BASED_ON_PLANT}`, 
      "post", 
      hSuccess, 
      hError, 
      { plant }
    );
  };

  return { getContryBasedOnPlant };
};

export default useCountryBasedOnPlant;