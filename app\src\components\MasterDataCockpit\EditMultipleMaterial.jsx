import {
  Box,
  Grid,
  IconButton,
  Tabs,
  Typography,
  Stack,
  TextField,
  Checkbox,
  Autocomplete,
  Paper,
  BottomNavigation,
  Button,
  CardContent,
} from "@mui/material";
import Tab from "@mui/material/Tab";
import React, { useState, useEffect } from "react";
import {
  iconButton_SpacingSmall,
  outermostContainer,
  container_Padding,
  outerContainer_Information,
  button_Primary,
  outermostContainer_Information,
  button_Outlined,
} from "../common/commonStyles";
import { useSelector, useDispatch } from "react-redux";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { useNavigate, useLocation } from "react-router-dom";
// import EditableField from "./EditFieldForDisplay";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import { setEditMultipleMaterial } from "../../app/initialDataSlice";
import EditableFieldForMassMaterial from "./EditFieldForMassMaterial";

const EditMultipleMaterial = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [dropDownData, setDropDownData] = useState({});
  const [value, setValue] = useState(0);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isDisplayMode, setIsDisplayMode] = useState(true);
  const location = useLocation();
  const [isBackButtonEnable, setIsBackButtonEnable] = useState(false)
  const ChangesInMaterialDetail = useSelector(
    (state) => state.initialData.EditMultipleMaterial
  );
  const MultipleMaterial = useSelector(
    (state) => state.initialData.MultipleMaterial
  );
  const selectedRowData = location.state;
  const PayloadData = useSelector((state) => state.payload);
  const onEdit = () => {
    setIsEditMode(true);
    // setIsExtendMode(false);
    setEditMultipleMaterial();
    setIsDisplayMode(false);
  };
  // console.log("select", MultipleMaterial.filter((item)=>item.Description === selectedRowData.description)[0]);

  let activeRow = {};
  let activeIndex = -1;
  for (let index = 0; index < MultipleMaterial.length; index++) {
    if (MultipleMaterial[index].Description === selectedRowData.description) {
      activeRow = MultipleMaterial[index];
      activeIndex = index;
      break;
    }
  }
  const tabContents = MultipleMaterial.filter(
    (x) => x.Description === selectedRowData.description
  )[0]["Basic Data"];

  console.log(tabContents, "lololol");

  return (
    <div>
      <Grid
        container
        style={{
          ...outermostContainer_Information,
          backgroundColor: "#FAFCFF",
        }}
      >
        <Grid sx={{ width: "inherit" }}>
          <Grid item md={12} style={{ padding: "16px", display: "flex" }}>
            {/* <Grid  sx={{ display: "flex" }}> */}
            <Grid item style={{ display: "flex", justifyContent: "flex-end" }}>
              <IconButton
                // onClick={handleBacktoRO}
                color="primary"
                aria-label="upload picture"
                component="label"
                sx={iconButton_SpacingSmall}
              >
                <ArrowCircleLeftOutlinedIcon
                  style={{ height: "1em", width: "1em", color: "#000000" }}
                  onClick={() => {
                    setTimeout(() => {
                      navigate(-1);
                    }, 1000);
                    dispatch(clearPayload());
                    dispatch(clearOrgData());
                  }}
                />
              </IconButton>
            </Grid>
            <Grid md={8}>
              <Typography variant="h3">
                <strong>
                  Multiple Material : {selectedRowData.description}{" "}
                </strong>
              </Typography>
              <Typography variant="body2" color="#777">
                This view displays details of uploaded material
              </Typography>
            </Grid>
            {!isEditMode ? (
              <Grid md={4} sx={{ display: "flex", justifyContent: "flex-end" }}>
                <Grid item>
                  <Button
                    variant="outlined"
                    size="small"
                    sx={button_Outlined}
                    onClick={onEdit}
                  >
                    Change
                    <EditOutlinedIcon
                      sx={{ padding: "2px" }}
                      fontSize="small"
                    />
                  </Button>
                </Grid>
              </Grid>
            ) : (
              ""
            )}
            {/* </Grid> */}
          </Grid>
          <Grid container display="flex" flexDirection="row" flexWrap="nowrap">
            <Box width="70%" sx={{ marginLeft: "40px" }}>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Material
                    </Typography>
                  </div>
                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    justifyContent="flex-start"
                  >
                    : {selectedRowData.material}
                  </Typography>
                </Stack>
              </Grid>

              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Material Type
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    : {selectedRowData.materialType}
                    {/* {displayData?.materialDescription?.materialDescription} */}
                  </Typography>
                </Stack>
              </Grid>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Description
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    : {selectedRowData.description}
                  </Typography>
                </Stack>
              </Grid>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Industry Sector
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    : {selectedRowData.industrySector}
                  </Typography>
                </Stack>
              </Grid>
            </Box>
            <Box width="30%" sx={{ marginLeft: "40px" }}>
              <Grid item>
                <Stack flexDirection="row">
                  <Typography
                    variant="body2"
                    color="#777"
                    style={{ width: "30%" }}
                  >
                    {/* {item.info ? item.desc : ""} */}
                  </Typography>

                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    sx={{ width: "8%", textAlign: "center" }}
                  >
                    {/* {item.info ? ":" : ""} */}
                  </Typography>

                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    justifyContent="flex-start"
                  >
                    {/* {item?.info?.code}
                              {item?.info?.code && item?.info?.desc
                                ? " - "
                                : null} */}
                    {/* {item?.info?.desc} */}
                  </Typography>
                </Stack>
              </Grid>
            </Box>
          </Grid>

          <Grid container style={{ padding: "16px" }}>
            <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
              <Tabs
                value={value}
                variant="scrollable"
                sx={{
                  background: "#FFF",
                  borderBottom: "1px solid #BDBDBD",
                  width: "100%",
                }}
                aria-label="mui tabs example"
              >
                <Tab
                  sx={{ fontSize: "12px", fontWeight: "700" }}
                  key={0}
                  label={"Basic Data"}
                ></Tab>
              </Tabs>
            </Box>
            <Grid
              key={tabContents}
              container
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                // borderRadius: "8px",
                // border: "1px solid #E0E0E0",
                mt: 1,
                // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                // padding: "10px",
                mb: 1,
              }}
            >
              {Object.keys(tabContents).map((fieldGroup) => (
                <Grid
                  key={fieldGroup}
                  item
                  md={12}
                  sx={{
                    backgroundColor: "white",
                    maxHeight: "max-content",
                    height: "max-content",
                    borderRadius: "8px",
                    border: "1px solid #E0E0E0",
                    mt: 0.25,
                    boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                    ...container_Padding,
                    // ...container_columnGap,
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: "12px",
                      fontWeight: "700",
                      margin: "0px !important",
                    }}
                  >
                    {fieldGroup}
                  </Typography>
                  <Box sx={{ width: "100%" }}>
                    <CardContent
                      sx={{
                        padding: "0",
                        paddingBottom: "0 !important",
                        paddingTop: "10px !important",
                      }}
                    >
                      <Grid
                        container
                        style={{
                          display: "grid",
                          gridTemplateColumns: "repeat(6,1fr)",
                          gap: "15px",
                        }}
                        justifyContent="space-between"
                        alignItems="flex-start"
                        md={12}
                      >
                        {tabContents[fieldGroup].map((field) => {
                          // console.log("fieldDatatttt", field);
                          return (
                            <EditableFieldForMassMaterial
                              // key={field.fieldName}
                              fieldGroup = {fieldGroup}
                              selectedRowData = {selectedRowData.description}
                              label={field.fieldName}
                              value={field.value}
                              onSave={(newValue) =>
                                handleFieldSave(field.fieldName, newValue)
                              }
                              isEditMode={isEditMode}
                              // isExtendMode={isExtendMode}
                              type={field.fieldType}
                              field={field} // Update the type as needed
                            />
                          );
                        })}
                      </Grid>
                    </CardContent>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {isEditMode ? (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
            <Button
              size="small"
              variant="contained"
              onClick={() => {
                navigate("/masterDataCockpit/materialMaster/massMaterialTable");
              }}
            >
              Save
            </Button>
          </BottomNavigation>
        </Paper>
      ) : (
        ""
      )}
    </div>
  );
};

export default EditMultipleMaterial;
