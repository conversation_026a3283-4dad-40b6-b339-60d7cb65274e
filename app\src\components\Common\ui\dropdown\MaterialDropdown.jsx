import { useState, useRef, useMemo, useEffect } from "react";
import { 
  Box, TextField, Chip, Popover, FormGroup, FormControlLabel, 
  Checkbox, Typography, CircularProgress, Tooltip, Paper, IconButton 
} from "@mui/material";
import { FixedSizeList as List } from "react-window";
import ClearIcon from '@mui/icons-material/Clear';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';

const MaterialDropdown = ({ 
  matGroup, 
  selectedMaterialGroup, 
  setSelectedMaterialGroup,
  isDropDownLoading,
  placeholder = "Enter Material Number",
  onInputChange,
  minCharacters = 4
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
  const [popoverContent, setPopoverContent] = useState("");
  const [isPopoverVisible, setIsPopoverVisible] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const listRef = useRef(null);
  const containerRef = useRef(null);

  const filteredOptions = useMemo(() => {
    const options = matGroup?.map(item => ({
      value: item.code,
      label: item.desc || item.code
    })) || [];

    if (!inputValue || inputValue.length < minCharacters) return [];
    
    return options.filter(option => 
      option.value.toLowerCase().includes(inputValue.toLowerCase()) ||
      option.label.toLowerCase().includes(inputValue.toLowerCase())
    );
  }, [matGroup, inputValue, minCharacters]);

  const handleInputChange = (e) => {
    const value = e.target.value;
    setInputValue(value);
    if (onInputChange && value.length >= minCharacters) {
      onInputChange(e);
    }
  };

  const handleSelect = (option) => {
    const isSelected = selectedMaterialGroup.some(item => item.code === option.value);
    
    if (isSelected) {
      setSelectedMaterialGroup(selectedMaterialGroup.filter(item => item.code !== option.value));
    } else {
      setSelectedMaterialGroup([...selectedMaterialGroup, { code: option.value, desc: option.label }]);
    }
  };

  const handlePopoverOpen = (event, content) => {
    setPopoverAnchorEl(event.currentTarget);
    setPopoverContent(content);
    setIsPopoverVisible(true);
  };

  const handlePopoverClose = () => {
    setIsPopoverVisible(false);
  };

  const renderSelectedValues = () => {
    if (selectedMaterialGroup.length === 0) return null;
    
    return selectedMaterialGroup.length > 1 ? (
      <>
        <Chip
          sx={{
            height: 25,
            fontSize: "0.85rem",
            '.MuiChip-label': { padding: "0 6px" }
          }}
          label={selectedMaterialGroup[0].code}
        />
        <Chip
          sx={{
            height: 25,
            fontSize: "0.85rem",
            '.MuiChip-label': { padding: "0 6px" },
            ml: 0.5
          }}
          label={`+${selectedMaterialGroup.length - 1}`}
          onMouseEnter={(event) => {
            const selectedOptionsText = selectedMaterialGroup
              .slice(1)
              .map(option => `<strong>${option.code}</strong> - ${option.desc}`)
              .join("<br />");
            handlePopoverOpen(event, selectedOptionsText);
          }}
          onMouseLeave={handlePopoverClose}
        />
      </>
    ) : (
      <Chip
        sx={{
          height: 25,
          fontSize: "0.85rem",
          '.MuiChip-label': { padding: "0 6px" }
        }}
        label={selectedMaterialGroup[0].code}
      />
    );
  };

  const handleClearAll = () => {
    setSelectedMaterialGroup([]);
    setInputValue("");
    if (onInputChange) {
      // Simulate an input change event
      onInputChange({ target: { value: "" } });
    }
  };

  const toggleDropdown = (e) => {
    e.stopPropagation();
    setIsOpen(!isOpen);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);


  return (
    <Box 
      ref={containerRef} 
      sx={{ position: "relative", width: "100%" }}
    >
      <Tooltip
        title={inputValue.length < minCharacters ? `Enter at least ${minCharacters} characters` : ""}
        arrow
        disableHoverListener={inputValue.length >= minCharacters}
        placement="top"
      >
        <TextField
          fullWidth
          size="small"
          placeholder={placeholder?.toUpperCase()}
          value={inputValue}
          onChange={handleInputChange}
          onFocus={() => setIsOpen(true)}
          sx={{
            fontSize: "12px !important",
            "& .MuiOutlinedInput-root": {
              height: 35,
            },
            "& .MuiInputBase-input": {
              padding: "10px 14px",
            },
          }}
          InputProps={{
            startAdornment: renderSelectedValues(),
            endAdornment: (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {(selectedMaterialGroup.length > 0 || inputValue) && (
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleClearAll();
                    }}
                    sx={{ 
                      padding: '4px',
                      mr: 0.5,
                      '&:hover': {
                        backgroundColor: 'rgba(0, 0, 0, 0.04)'
                      }
                    }}
                  >
                    <ClearIcon sx={{ fontSize: '16px' }} />
                  </IconButton>
                )}
                {isDropDownLoading ? (
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                ) : (
                  <IconButton 
                    size="small" 
                    onClick={toggleDropdown}
                    sx={{ padding: '4px' }}
                  >
                    {isOpen ? (
                      <ArrowDropUpIcon sx={{ fontSize: '20px' }} />
                    ) : (
                      <ArrowDropDownIcon sx={{ fontSize: '20px' }} />
                    )}
                  </IconButton>
                )}
              </Box>
            ),
          }}
        />
      </Tooltip>

      {isOpen && inputValue.length >= minCharacters && (
        <Paper
          sx={{
            position: "absolute",
            top: "100%",
            left: 0,
            right: 0,
            mt: 1,
            maxHeight: 300,
            zIndex: 1000,
          }}
        >
          {filteredOptions.length === 0 ? (
            <Box sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                {isDropDownLoading ? 'Loading...' : 'No options found'}
              </Typography>
            </Box>
          ) : (
            <List
              height={Math.min(filteredOptions.length * 45, 300)}
              itemCount={filteredOptions.length}
              itemSize={45}
              width="100%"
              ref={listRef}
            >
              {({ index, style }) => {
                const option = filteredOptions[index];
                const isSelected = selectedMaterialGroup.some(item => item.code === option.value);
                
                return (
                  <Box
                    component="div"
                    sx={{
                      ...style,
                      padding: "4px 8px",
                      cursor: "pointer",
                      "&:hover": {
                        backgroundColor: "action.hover",
                      },
                    }}
                    onClick={() => handleSelect(option)}
                  >
                    <FormGroup>
                      <FormControlLabel
                        sx={{
                          margin: 0,
                          '& .MuiFormControlLabel-label': {
                            flex: 1
                          }
                        }}
                        control={
                          <Checkbox 
                            size="small" 
                            checked={isSelected}
                          />
                        }
                        label={
                          <Typography sx={{ fontSize: 12 }}>
                            <strong>{option.value}</strong> - {option.label}
                          </Typography>
                        }
                      />
                    </FormGroup>
                  </Box>
                );
              }}
            </List>
          )}
        </Paper>
      )}

      <Popover
        open={isPopoverVisible}
        anchorEl={popoverAnchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        PaperProps={{
          sx: {
            p: 1,
            maxWidth: 300
          }
        }}
      >
        <Typography 
          variant="body2" 
          dangerouslySetInnerHTML={{ __html: popoverContent }}
        />
      </Popover>
    </Box>
  );
};

export default MaterialDropdown;

