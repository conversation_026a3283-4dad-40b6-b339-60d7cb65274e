import { useSelector } from 'react-redux';

const useLogger = () => {
  const applicationConfig = useSelector((state) => state.applicationConfig);

  const log = (message, ...optionalParams) => {
    if (applicationConfig.environment === 'localhost') {
      console.log(message, ...optionalParams);
    }
  };

  const customError = (message, ...optionalParams) => {
    if (applicationConfig.environment === 'localhost') {
      console.error(message, ...optionalParams);
    }
  };

  const warn = (message, ...optionalParams) => {
    if (applicationConfig.environment === 'localhost') {
      console.warn(message, ...optionalParams);
    }
  };

  return {
    log,
    customError,
    warn,
  };
};

export default useLogger;
