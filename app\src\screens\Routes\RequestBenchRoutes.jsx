import React,{lazy} from "react";
import { Route } from "react-router-dom";
const RequestBench = lazy(() => import("../../components/RequestBench/RequestBench"));
const CreateNewRequest = lazy(() => import("@material/CreateNewRequest"));
const CreateNewArticleRequest = lazy(() => import("@article/CreateNewArticleRequest"));
const ErrorHistory = lazy(() => import("../../components/RequestBench/ErrorHistory"));
const RequestHistory = lazy(() => import("../../components/RequestHistory/RequestHistory"));
const Upload = lazy(() => import("../../components/RequestBench/RequestPages/Upload"))
const BomCreateRequest = lazy(() => import("@BillOfMaterial/bomCreateRequest"));
const ProfitCenterRequestTab = lazy(() => import("@profitCenter/ProfitCenterRequestTab"));
const CostCenterRequestTab = lazy(() => import("@costCenter/CostCenterRequestTab"));
const GeneralLedgerRequesttab = lazy(() => import("@generalLedger/GeneralLedgerRequestTab"))
const CostCenterGroupRequestTab = lazy(() => import("@costCenterGroup/CostCenterGroupRequestTab"))
const ProfitCenterGroupRequestTab = lazy(() => import("@profitCenterGroup/ProfitCenterGroupRequestTab"))
const BankKeyCreateRequest = lazy(() => import("@bankKey/BankKeyCreateRequest"));
const CostElementGroupRequestTab = lazy(() => import("@costElementGroup/CostElementGroupRequestTab"))

export const RequestBenchRoutes = [
  <Route path="/requestBench" element={<RequestBench />} />,
  <Route path="/requestBench/createRequest" element={<CreateNewRequest />} />,
  <Route path="/requestBench/createArticle" element={<CreateNewArticleRequest />} />,
  <Route path="/requestBench/RequestHistory" element={<RequestHistory />} />,
  <Route path="/requestBench/errorHistory" element={<ErrorHistory />} />,
  <Route path="/configCockpit/documentConfigurations" element={<Upload />} />,
  <Route path="/requestBench/bomCreateRequest" element={<BomCreateRequest/>} />,
  <Route path="requestBench/ProfitCenterRequestTab" element={<ProfitCenterRequestTab/>} />,
  <Route path="requestBench/CostCenterRequestTab" element={<CostCenterRequestTab/>} />,
  <Route path="requestBench/GeneralLedgerRequestTab" element={<GeneralLedgerRequesttab/>} />,
  <Route path="requestBench/CostCenterGroupRequestTab" element={<CostCenterGroupRequestTab/>} />,
  <Route path="requestBench/ProfitCenterGroupRequestTab" element={<ProfitCenterGroupRequestTab/>} />,
  <Route path="requestBench/BankKeyCreateRequest" element={<BankKeyCreateRequest />} />,
  <Route path="/requestBench/CostElementGroupRequestTab" element={<CostElementGroupRequestTab/>} />,
];