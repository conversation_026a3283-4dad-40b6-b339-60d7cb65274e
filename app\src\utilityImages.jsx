let images = {
    
}
let imageList = [
    {key:'csv',fileName:'csv.png'},
    {key:'doc',fileName:'doc.png'},
    {key:'docx',fileName:'doc.png'},    
    {key:'jpg',fileName:'jpg.png'},
    {key:'ppt',fileName:'ppt.png'},
    {key:'png',fileName:'png.png'},
    {key:'txt',fileName:'txt.png'},
    {key:'xls',fileName:'xls.png'},
    {key:'xlsx',fileName:'xls.png'},
    {key:'applicationIsDown',fileName:'applicationIsDown.jpg'},
    {key:'scp_AppheaderImg', fileName:'scp-logo_appheader.png'},
    {key:'viatris_AppheaderImg', fileName:'viatris_AppHeaderImg.png'},
    {key:'woc_AppheaderImg', fileName:'woc_AppHeaderImg.png'},
    {key:'internalServerError', fileName:'applicationIsDown.jpg'},
    {key:'runtimeError', fileName:'runtimeError.png'},
    {key:'pageNotFound',fileName:'pageNotFound.jpg'}
    ]

imageList.forEach(async(i)=>{
    const imgUrl = new URL(`/src/utilityImages/${i.fileName}`, import.meta.url).href

    images[i.key] = imgUrl
})

export default images   