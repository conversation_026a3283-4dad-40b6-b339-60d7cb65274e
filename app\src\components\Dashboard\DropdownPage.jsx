import React from "react";
import { Select, MenuItem, Box } from "@mui/material";

const DropdownPage = ({ selectedGraph, options, handleGraphChange }) => {
  return (
    <Box display="flex" justifyContent="flex-end">
      <Select
        value={selectedGraph}
        onChange={handleGraphChange}
        style={{ minWidth: "100px" }}
        size="small"
      >
        {options.map((option, index) => (
          <MenuItem key={index} value={option}>
            {option}
          </MenuItem>
        ))}
      </Select>
    </Box>
  );
};

export default DropdownPage;
