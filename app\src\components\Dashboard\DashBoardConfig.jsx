import dashboardKeyWord from "./DashboardKeyword.jsx";

const dashboardConfig = {
  SCP: {
    product: true,

    TabPanel: [
      { uid: 0, name: "Operational Metrics", icon: "Tune", required: true },
      { uid: 1, name: "Purchase Metrics", icon: "ReceiptLong", required: true },
      {
        uid: 2,
        name: "Delivery Metrics",
        icon: "LocalShipping",
        required: true,
      },
      { uid: 3, name: "Invoice Metrics", icon: "Description", required: true },
    ],
    Tiles: [
      {
        uid: 1,
        name: "Open",
        count: dashboardKeyWord.Open,
        required: true,
        width: 2,
        type: "PO",
        status: ["PO Open"],
      },
      {
        uid: 1,
        name: "Pending Confirmation",
        count: dashboardKeyWord.PendingConfirmation,
        required: true,
        width: 2,
        type: "PO",
        status: ["PO Requires Confirmation"],
      },

      {
        uid: 1,
        name: "Confirmed",
        count: dashboardKeyWord.Confirmed,
        required: true,
        width: 2,
        type: "PO",
        status: ["PO Confirmed"],
      },
      {
        uid: 1,
        name: "Delivered",
        count: dashboardKeyWord.Delivered,
        required: true,
        width: 2,
        type: "PO",
        status: ["GR Fully Completed", "GR Partially Completed"],
      },
      {
        uid: 1,
        name: "Blocked",
        count: dashboardKeyWord.Blocked,
        required: true,
        width: 2,
        type: "PO",
        status: ["PO Blocked"],
      },
      {
        uid: 1,
        name: "Production In Progress",
        count: dashboardKeyWord.ProductionInProgress,
        required: true,
        width: 2,
        type: "DPR",
        status: ["Production In Progress"],
      },
      {
        uid: 2,
        name: "Production Completed",
        count: dashboardKeyWord.ProductionCompleted,
        required: true,
        width: 2,
        type: "DPR",
        status: ["Production Completed"],
      },
      {
        uid: 2,
        name: "Pending ASN",
        count: dashboardKeyWord.PendingASN,
        required: true,
        width: 2,
        type: "CreateASN",
        status: [
          "ASN to be Initiated",
          "ASN In Progress",
          "Production In Progress",
        ],
      },
      {
        uid: 2,
        name: "Overdue Shipments",
        count: dashboardKeyWord.OverdueShipment,
        required: true,
        width: 2,
      },
      {
        uid: 2,
        name: "Pending Returns",
        count: dashboardKeyWord.PendingReturns,
        required: true,
        width: 2,
        type: "Returns",
        status: ["Pending Action"],
      },
      {
        uid: 2,
        name: "On Time Delivery",
        count: dashboardKeyWord.OnTimeDelivery,
        required: true,
        width: 2,
        isPercentage: true,
      },
      {
        uid: 2,
        name: "Order Fill Rate",
        count: dashboardKeyWord.OrderFillRate,
        required: true,
        width: 2,
        isPercentage: true,
      },
      {
        uid: 3,
        name: "Ready To Invoice",
        count: dashboardKeyWord.ReadyToINV,
        required: true,
        width: 2,
        type: "INV",
        status: [
          "GR Fully Completed",
          "GR Partially Completed",
          "Invoice Partially Completed",
        ],
      },
      {
        uid: 3,
        name: "Ready To Post Invoices",
        count: dashboardKeyWord.ReadyToPostINV,
        required: true,
        width: 2,
        type: "INVtracker",
        status: ["Ready To Post"],
      },
      {
        uid: 3,
        name: "Posted Invoices",
        count: dashboardKeyWord.PostedINV,
        required: true,
        width: 2,
        type: "INVtracker",
        status: ["Posted (Paid)", "Posted (Unpaid)"],
      },
      {
        uid: 3,
        name: "Paid Invoices",
        count: dashboardKeyWord.PaidINV,
        required: true,
        width: 2,
        type: "INVtracker",
        status: ["Posted (Paid)"],
      },
      {
        uid: 3,
        name: "Unpaid Invoices",
        count: dashboardKeyWord.UnpaidINV,
        required: true,
        width: 2,
        type: "INVtracker",
        status: ["Posted (Unpaid)"],
      },
      {
        uid: 3,
        name: "Rejected Invoices",
        count: dashboardKeyWord.RejectedINV,
        required: true,
        width: 2,
        type: "INVtracker",
        status: ["Rejected"],
      },
      {
        uid: 0,
        name: "Material Quantity",
        count: dashboardKeyWord.MaterialQuantity,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Material Value",
        count: dashboardKeyWord.MaterialValue,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "PO Status",
        count: dashboardKeyWord.POStatus,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Invoice Status",
        count: dashboardKeyWord.INVStatus,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Payment Status",
        count: dashboardKeyWord.PaymentStatus,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Production Status",
        count: dashboardKeyWord.ProductionStatus,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "SR Status",
        count: dashboardKeyWord.SRStatus,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "SR Priority Status",
        count: dashboardKeyWord.SRPriority,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Delivery Delay",
        count: dashboardKeyWord.DeliveryDelay,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Planning Task",
        count: dashboardKeyWord.PlanningTask,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Material Group",
        count: dashboardKeyWord.MaterialGroup,
        required: false,
        width: 2,
      },

      {
        uid: 0,
        name: "Pending Acknowledgement",
        count: dashboardKeyWord.PendingAck,
        required: true,
        width: 2.4,
        type: "PO",
        status: ["PO Open", "PO Requires Confirmation"],
        additionalStatus: "Not Acknowledged",
      },
      {
        uid: 0,
        name: "Pending Consumption",
        count: dashboardKeyWord.PendingConsumption,
        required: true,
        width: 2.4,
        type: "ASN",
        additionalStatus: "No",
      },
      {
        uid: 0,
        name: "Pending Planning",
        count: dashboardKeyWord.PendingPlanning,
        required: true,
        width: 2.4,
        type: "Planning",
        status: ["Planning Task Created", "Planning Task Submitted"],
      },
      {
        uid: 0,
        name: "Submiited Acknowledgement",
        count: dashboardKeyWord.SubmiitedAck,
        required: true,
        width: 2.4,
        type: "PO",
        status: [],
        additionalStatus: "Acknowledged",
      },
      {
        uid: 0,
        name: "Confirmation Submitted",
        count: dashboardKeyWord.ConfirmationSubmitted,
        required: false,
        width: 2.4,
        type: "PO",
        status: ["PO Confirmation Submitted"],
      },
      {
        uid: 0,
        name: "Submitted ASN",
        count: dashboardKeyWord.SubmittedASN,
        required: false,
        width: 2,
        type: "ASN",
        additionalStatus: "",
      },
      {
        uid: 0,
        name: "Submitted Consumption",
        count: dashboardKeyWord.SubmittedConsumption,
        required: true,
        width: 2.4,
        type: "ASN",
        additionalStatus: "Yes",
      },
      {
        uid: 0,
        name: "Consumption Summary",
        count: dashboardKeyWord.ConsumptionSummary,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Consumption PO",
        count: dashboardKeyWord.ConsumptionByPO,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Consumption ASN",
        count: dashboardKeyWord.ConsumptionByASN,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Consumption Material",
        count: dashboardKeyWord.ConsumptionByMaterial,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Planning Table",
        count: dashboardKeyWord.PlanningTable,
        required: false,
        width: 2,
      },
    ],
    Graphs: [
      
      {
        uid: 1,
        id: 1,
        name: "Top 5 Open POs",
        count: dashboardKeyWord.Open,
        required: true,
        xaxis: "poNumber",
        yaxis: "netPrice",
        type: "po",
        width: 6,
      },
      {
        uid: 1,
        id: 2,
        name: "Top 5 Pending Confirmation POs",
        count: dashboardKeyWord.PendingConfirmation,
        required: true,
        xaxis: "poNumber",
        yaxis: "netPrice",
        type: "po",
        width: 6,
      },
      {
        uid: 1,
        id: 3,
        name: "Top 5 Confirmed POs",
        count: dashboardKeyWord.Confirmed,
        required: true,
        xaxis: "poNumber",
        yaxis: "netPrice",
        type: "po",
        width: 6,
      },
      {
        uid: 1,
        id: 4,
        name: "Top 5 Delivered POs",
        count: dashboardKeyWord.Delivered,
        required: true,
        xaxis: "poNumber",
        yaxis: "netPrice",
        type: "po",
        width: 6,
      },
      {
        uid: 1,
        id: 5,
        name: "Top 5 Blocked POs",
        count: dashboardKeyWord.Blocked,
        required: true,
        xaxis: "poNumber",
        yaxis: "netPrice",
        type: "po",
        width: 6,
      },
      {
        uid: 1,
        id: 6,
        name: "Top 5 Production In Progress POs",
        count: dashboardKeyWord.ProductionInProgress,
        required: true,
        xaxis: "poNumber",
        yaxis: "netPrice",
        type: "po",
        width: 6,
      },
      {
        uid: 2,
        id: 7,
        name: "Top 5 Production Completed POs",
        count: dashboardKeyWord.ProductionCompleted,
        required: true,
        xaxis: "poNumber",
        yaxis: "netPrice",
        type: "po",
        width: 6,
      },
      {
        uid: 2,
        id: 8,
        name: "Top 5 Pending Advanced Shipment Notifications",
        count: dashboardKeyWord.PendingASN,
        required: true,
        xaxis: "poNumber",
        yaxis: "netPrice",
        type: "po",
        width: 6,
      },
      {
        uid: 2,
        id: 9,
        name: "Top 5 Overdue Shipments",
        count: dashboardKeyWord.OverdueShipment,
        required: true,
        xaxis: "poNumber",
        yaxis: "netPrice",
        type: "po",
        width: 6,
      },
      {
        uid: 2,
        id: 10,
        name: "Top 5 Pending Returns",
        count: dashboardKeyWord.PendingReturns,
        required: true,
        xaxis: "poNumber",
        yaxis: "netPrice",
        type: "po",
        width: 6,
      },
      {
        uid: 2,
        id: 11,
        name: "On Time Delivery",
        count: dashboardKeyWord.OnTimeDelivery,
        required: false,
        xaxis: "poNumber",
        yaxis: "netPrice",
        type: "po",
        width: 6,
      },
      {
        uid: 2,
        id: 12,
        name: "Order Fill Rate",
        count: dashboardKeyWord.OrderFillRate,
        required: false,
        xaxis: "poNumber",
        yaxis: "netPrice",
        type: "po",
        width: 6,
      },
      {
        uid: 3,
        id: 13,
        name: "Top 5 Ready To Invoice",
        count: dashboardKeyWord.ReadyToINV,
        required: true,
        xaxis: "poNumber",
        yaxis: "netPrice",
        type: "po",
        width: 6,
      },
      {
        uid: 3,
        id: 14,
        name: "Top 5 Ready To Post Invoices",
        count: dashboardKeyWord.ReadyToPostINV,
        required: true,
        yaxis: "invoiceTotal",
        xaxis: "extInvNumber",
        type: "inv",
        width: 6,
      },
      {
        uid: 3,
        id: 15,
        name: "Top 5 Posted Invoices",
        count: dashboardKeyWord.PostedINV,
        required: true,
        yaxis: "invoiceTotal",
        xaxis: "extInvNumber",
        type: "inv",
        width: 6,
      },
      {
        uid: 3,
        id: 16,
        name: "Top 5 Paid Invoices",
        count: dashboardKeyWord.PaidINV,
        required: true,
        yaxis: "invoiceTotal",
        xaxis: "extInvNumber",
        type: "inv",
        width: 6,
      },
      {
        uid: 3,
        id: 17,
        name: "Top 5 Unpaid Invoices",
        count: dashboardKeyWord.UnpaidINV,
        required: true,
        yaxis: "invoiceTotal",
        xaxis: "extInvNumber",
        type: "inv",
        width: 6,
      },
      {
        uid: 3,
        id: 18,
        name: "Top 5 Rejected Invoices",
        count: dashboardKeyWord.RejectedINV,
        required: true,
        yaxis: "invoiceTotal",
        xaxis: "extInvNumber",
        type: "inv",
        width: 6,
      },
      {
        uid: 0,
        id: 19,
        name: "Top 5 Materials By Quantity",
        count: dashboardKeyWord.MaterialQuantity,
        required: true,
        yaxis: "poQuantity",
        xaxis: "material",
        type: "ops",
        width: 6,
        ToolTip: "tooltipmaterialbypurchase",
        yaxisHeader: "Unit",
      },
      {
        uid: 0,
        id: 20,
        name: "Top 5 Materials By Value",
        count: dashboardKeyWord.MaterialValue,
        required: true,
        yaxis: "poValue",
        xaxis: "material",
        type: "ops",
        width: 6,
      },
      {
        uid: 0,
        id: 21,
        name: "Purchase Order Status",
        count: dashboardKeyWord.POStatus,
        required: true,
        yaxis: "poValue",
        xaxis: "material",
        type: "popie",
        isPie: true,
        width: 4,
      },
      {
        uid: 0,
        id: 22,
        name: "Invoice Status",
        count: dashboardKeyWord.INVStatus,
        required: true,
        yaxis: "poValue",
        xaxis: "material",
        type: "invpie",
        isPie: true,
        isDonut: true,
        width: 4,
      },
      {
        uid: 0,
        id: 23,
        name: "Payment Status",
        count: dashboardKeyWord.PaymentStatus,
        required: true,
        yaxis: "poValue",
        xaxis: "material",
        type: "invpie",
        isPie: true,
        width: 4,
      },
      {
        uid: 0,
        id: 24,
        name: "Production Status",
        count: dashboardKeyWord.ProductionStatus,
        required: true,
        yaxis: "poValue",
        xaxis: "material",
        type: "dpr",
        isPie: true,
        width: 4,
      },
      {
        uid: 0,
        id: 25,
        name: "Service Request Status",
        count: dashboardKeyWord.SRStatus,
        required: true,
        yaxis: "poValue",
        xaxis: "material",
        type: "sr",
        isPie: true,
        width: 4,
      },
      {
        uid: 0,
        id: 26,
        name: "Service Request Priority",
        count: dashboardKeyWord.SRPriority,
        required: true,
        yaxis: "poValue",
        xaxis: "material",
        type: "sr",
        isPie: true,
        width: 4,
      },
      {
        uid: 0,
        id: 27,
        name: "Delivery Delays",
        count: dashboardKeyWord.DeliveryDelay,
        required: true,
        yaxis: "count",
        xaxis: "name",
        type: "ops",
        width: 6,
        ToolTip: "deliveryDelays",
        yaxisHeader: "Days",
      },
      {
        uid: 0,
        id: 28,
        name: "Material Group",
        count: dashboardKeyWord.MaterialGroup,
        required: false,
        xaxisList: ["material", "material"],
        yaxisList: ["poQuantity", "poValue"],
        type: "po",
        width: 6,
        options: ["By Quantity", "By Amount"],
        ToolTipList: ["tooltipmaterialbypurchase", "common"],
        grouped: false,
        graphList: [
          dashboardKeyWord.MaterialQuantity,
          dashboardKeyWord.MaterialValue,
        ],
        // horizontal:true,
        types: ["ops", "ops"],
        axisHeaders: ["Unit", "EUR"],
        multiple: true,
      },
      {
        uid: 0,
        id: 29,
        name: "Consumption PO",
        count: dashboardKeyWord.ConsumptionByPO,
        required: false,
        yaxis: "requiredQuantity",
        xaxis: "poNumber",
        type: "po",
        width: 6,
        grouped: true,
        ToolTip: "consumption",
        yaxisHeader: "UNit",
      },
      {
        uid: 0,
        id: 30,
        name: "Consumption ASN",
        count: dashboardKeyWord.ConsumptionByASN,
        required: false,
        yaxis: "requiredQuantity",
        xaxis: "shipmentId",
        type: "ops",
        width: 6,
        ToolTip: "consumption",
        yaxisHeader: "Unit",
        grouped: true,
      },
      {
        uid: 0,
        id: 31,
        name: "Consumption Material",
        count: dashboardKeyWord.ConsumptionByMaterial,
        required: false,
        yaxis: "requiredQuantity",
        xaxis: "subMaterial",
        type: "ops",
        width: 6,
        ToolTip: "consumption",
        yaxisHeader: "Unit",
        grouped: true,
      },
      {
        uid: 0,
        id: 32,
        name: "Consumption Report",
        count: dashboardKeyWord.ConsumptionSummary,
        required: false,
        xaxisList: ["poNumber", "shipmentId", "subMaterial"],
        yaxisList: [
          {
            obj1: "requiredQuantity",
            obj2: "issuedQuantity",
            obj3: "consumedQuantity",
          },
          {
            obj1: "requiredQuantity",
            obj2: "issuedQuantity",
            obj3: "consumedQuantity",
          },
          {
            obj1: "requiredQuantity",
            obj2: "issuedQuantity",
            obj3: "consumedQuantity",
          },
        ],
        legendList:["Requested","Issued","Consumed"],
        type: "po",
        width: 8,
        options: ["Purchase Order", "Shipment", "Material"],
        ToolTipList: ["consumption", "consumption", "consumption"],
        grouped: true,
        graphList: [
          dashboardKeyWord.ConsumptionByPO,
          dashboardKeyWord.ConsumptionByASN,
          dashboardKeyWord.ConsumptionByMaterial,
        ],
        // horizontal:true,
        types: ["consumption", "consumption", "consumption"],
        axisHeaders: ["Unit", "Unit", "Unit"],
        multiple: true,
      },
      {
        uid: 0,
        id: 0,
        name: "Planning Table",
        count: dashboardKeyWord.PlanningTable,
        required: true,
        width: 6,
        isTable: true,
      },
    ],
  },
  Viatris: {
    product: false,
    TabPanel: [
      { uid: 0, name: "Operational Metrics", icon: "Tune", required: true },
      { uid: 1, name: "Purchase Metrics", icon: "ReceiptLong", required: true },
    ],
    Tiles: [
      {
        uid: 1,
        name: "New Purchase Orders",
        count: dashboardKeyWord.Open,
        required: true,
        width: 1.5,
        type: "PO",
        status: ["PO Open"],
      },
      {
        uid: 1,
        name: "Acknowledgement Submitted",
        count: dashboardKeyWord.SubmiitedAck,
        required: true,
        width: 1.5,
        type: "PO",
        status: [],
        additionalStatus: "Acknowledged",
      },
      {
        uid: 1,
        name: "Confirmations Submitted",
        count: dashboardKeyWord.ConfirmationSubmitted,
        required: true,
        width: 1.5,
        type: "PO",
        status: ["PO Confirmation Submitted"],
      },

      {
        uid: 1,
        name: "Confirmed",
        count: dashboardKeyWord.Confirmed,
        required: false,
        width: 2,
        type: "PO",
        status: ["PO Confirmed"],
      },
      {
        uid: 1,
        name: "Blocked",
        count: dashboardKeyWord.Blocked,
        required: true,
        width: 1.5,
        type: "PO",
        status: ["PO Blocked"],
      },
      {
        uid: 1,
        name: "ASNs Submitted",
        count: dashboardKeyWord.SubmittedASN,
        required: true,
        width: 1.5,
        type: "ASN",
        additionalStatus: "",
      },
      {
        uid: 1,
        name: "Consumption Submitted",
        count: dashboardKeyWord.SubmittedConsumption,
        required: true,
        width: 1.5,
        type: "ASN",
        additionalStatus: "Yes",
      },

      {
        uid: 1,
        name: "Delivered",
        count: dashboardKeyWord.Delivered,
        required: true,
        width: 1.5,
        type: "PO",
        status: ["GR Fully Completed", "GR Partially Completed"],
      },
      {
        uid: 1,
        name: "Overdue Shipments",
        count: dashboardKeyWord.OverdueShipment,
        required: true,
        width: 1.5,
      },
      {
        uid: 1,
        name: "Production In Progress",
        count: dashboardKeyWord.ProductionInProgress,
        required: false,
        width: 2,
        type: "DPR",
        status: ["Production In Progress"],
      },
      {
        uid: 2,
        name: "Production Completed",
        count: dashboardKeyWord.ProductionCompleted,
        required: false,
        width: 2,
        type: "DPR",
        status: ["Production Completed"],
      },

      {
        uid: 2,
        name: "Pending Returns",
        count: dashboardKeyWord.PendingReturns,
        required: false,
        width: 2,
        type: "Returns",
        status: ["Pending Action"],
      },
      {
        uid: 2,
        name: "On Time Delivery",
        count: dashboardKeyWord.OnTimeDelivery,
        required: false,
        width: 2,
        isPercentage: true,
      },
      {
        uid: 2,
        name: "Order Fill Rate",
        count: dashboardKeyWord.OrderFillRate,
        required: false,
        width: 2,
        isPercentage: true,
      },
      {
        uid: 3,
        name: "Ready To Invoice",
        count: dashboardKeyWord.ReadyToINV,
        required: false,
        width: 2,
        type: "INV",
        status: [
          "GR Fully Completed",
          "GR Partially Completed",
          "Invoice Partially Completed",
        ],
      },
      {
        uid: 3,
        name: "Ready To Post Invoices",
        count: dashboardKeyWord.ReadyToPostINV,
        required: false,
        width: 2,
        type: "INVtracker",
        status: ["Ready To Post"],
      },
      {
        uid: 3,
        name: "Posted Invoices",
        count: dashboardKeyWord.PostedINV,
        required: false,
        width: 2,
        type: "INVtracker",
        status: ["Posted (Paid)", "Posted (Unpaid)"],
      },
      {
        uid: 3,
        name: "Paid Invoices",
        count: dashboardKeyWord.PaidINV,
        required: false,
        width: 2,
        type: "INVtracker",
        status: ["Posted (Paid)"],
      },
      {
        uid: 3,
        name: "Unpaid Invoices",
        count: dashboardKeyWord.UnpaidINV,
        required: false,
        width: 2,
        type: "INVtracker",
        status: ["Posted (Unpaid)"],
      },
      {
        uid: 3,
        name: "Rejected Invoices",
        count: dashboardKeyWord.RejectedINV,
        required: false,
        width: 2,
        type: "INVtracker",
        status: ["Rejected"],
      },
      {
        uid: 0,
        name: "Material Quantity",
        count: dashboardKeyWord.MaterialQuantity,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Material Value",
        count: dashboardKeyWord.MaterialValue,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "PO Status",
        count: dashboardKeyWord.POStatus,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Invoice Status",
        count: dashboardKeyWord.INVStatus,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Payment Status",
        count: dashboardKeyWord.PaymentStatus,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Production Status",
        count: dashboardKeyWord.ProductionStatus,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "SR Status",
        count: dashboardKeyWord.SRStatus,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "SR Priority Status",
        count: dashboardKeyWord.SRPriority,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Delivery Delay",
        count: dashboardKeyWord.DeliveryDelay,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Planning Task",
        count: dashboardKeyWord.PlanningTask,
        required: false,
        width: 2.4,
      },
      {
        uid: 0,
        name: "Material Group",
        count: dashboardKeyWord.MaterialGroup,
        required: false,
        width: 2.4,
      },

      {
        uid: 0,
        name: "Pending Acknowledgement",
        count: dashboardKeyWord.PendingAck,
        required: true,
        width: 2.4,
        type: "PO",
        status: ["PO Open", "PO Requires Confirmation"],
        additionalStatus: "Not Acknowledged",
      },
      {
        uid: 0,
        name: "Pending Confirmations",
        count: dashboardKeyWord.PendingConfirmation,
        required: true,
        width: 2.4,
        type: "PO",
        status: ["PO Requires Confirmation"],
      },
      {
        uid: 0,
        name: "Pending ASNs",
        count: dashboardKeyWord.PendingASN,
        required: true,
        width: 2.4,
        type: "CreateASN",
        status: [
          "ASN to be Initiated",
          "ASN In Progress",
          "Production In Progress",
        ],
      },
      {
        uid: 0,
        name: "Pending Consumption",
        count: dashboardKeyWord.PendingConsumption,
        required: true,
        width: 2.4,
        type: "ASN",
        additionalStatus: "No",
      },

      {
        uid: 0,
        name: "Pending Planning Tasks",
        count: dashboardKeyWord.PendingPlanning,
        required: true,
        width: 2.4,
        type: "Planning",
        status: ["Planning Task Created", "Planning Task Submitted"],
      },
      {
        uid: 0,
        name: "Planning Tasks",
        count: dashboardKeyWord.PlanningTask,
        required: false,
        width: 2.4,
        type: "Planning",
        status: ["Planning Task Created", "Planning Task Submitted"],
      },

      {
        uid: 0,
        name: "Consumption Summary",
        count: dashboardKeyWord.ConsumptionSummary,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Consumption PO",
        count: dashboardKeyWord.ConsumptionByPO,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Consumption ASN",
        count: dashboardKeyWord.ConsumptionByASN,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Consumption Material",
        count: dashboardKeyWord.ConsumptionByMaterial,
        required: false,
        width: 2,
      },
      {
        uid: 0,
        name: "Planning Table",
        count: dashboardKeyWord.PlanningTable,
        required: false,
        width: 2,
      },
    ],
    Graphs: [
      {
        uid: 0,
        id: 0,
        name: "Planning Table",
        count: dashboardKeyWord.PlanningTable,
        required: true,
        width: 6,
        isTable: true,
      },
      {
        uid: 0,
        id: 1,
        name: "Planning Tasks",
        count: dashboardKeyWord.PlanningTask,
        required: true,
        yaxis: "count",
        xaxis: "name",
        type: "ops",
        width: 6,
        horizontal: true,
        ToolTip: "planning",
        yaxisHeader: "Count",
      },
      {
        uid: 0,
        id: 19,
        name: "Material Quantity",
        count: dashboardKeyWord.MaterialQuantity,
        required: false,
        yaxis: "poQuantity",
        xaxis: "material",
        type: "ops",
        width: 6,
        ToolTip: "tooltipmaterialbypurchase",
        yaxisHeader: "Unit",
      },
      {
        uid: 0,
        id: 20,
        name: "Material Value",
        count: dashboardKeyWord.MaterialValue,
        required: false,
        yaxis: "poValue",
        xaxis: "material",
        type: "ops",
        width: 6,
      },
      {
        uid: 1,
        id: 32,
        name: "Consumption Report",
        count: dashboardKeyWord.ConsumptionSummary,
        required: true,
        xaxisList: ["poNumber", "shipmentId", "subMaterial"],
        yaxisList: [
          {
            obj1: "requiredQuantity",
            obj2: "issuedQuantity",
            obj3: "consumedQuantity",
          },
          {
            obj1: "requiredQuantity",
            obj2: "issuedQuantity",
            obj3: "consumedQuantity",
          },
          {
            obj1: "requiredQuantity",
            obj2: "issuedQuantity",
            obj3: "consumedQuantity",
          },
        ],
        legendList:["Requested","Issued","Consumed"],
        type: "po",
        width: 8,
        options: ["Purchase Order", "Shipment", "Material"],
        ToolTipList: ["consumption", "consumption", "consumption"],
        grouped: true,
        graphList: [
          dashboardKeyWord.ConsumptionByPO,
          dashboardKeyWord.ConsumptionByASN,
          dashboardKeyWord.ConsumptionByMaterial,
        ],
        // horizontal:true,
        types: ["consumption", "consumption", "consumption"],
        axisHeaders: ["Unit", "Unit", "Unit"],
        multiple: true,
      },
      {
        uid: 1,
        id: 21,
        name: "Purchase Order Status",
        count: dashboardKeyWord.POStatus,
        required: true,
        yaxis: "poValue",
        xaxis: "material",
        type: "popie",
        isPie: true,
        width: 4,
        isDonut: true,
      },

      {
        uid: 0,
        id: 28,
        name: "Top 5 PO Materials",
        count: dashboardKeyWord.MaterialGroup,
        required: true,
        xaxisList: ["material", "material"],
        yaxisList: ["poQuantity", "poValue"],
        type: "po",
        width: 6,
        options: ["By Quantity", "By Amount"],
        ToolTipList: ["tooltipmaterialbypurchase", "common"],
        grouped: false,
        graphList: [
          dashboardKeyWord.MaterialQuantity,
          dashboardKeyWord.MaterialValue,
        ],
        // horizontal:true,
        types: ["ops", "ops"],
        axisHeaders: ["Unit", "EUR"],
        multiple: true,
      },
      {
        uid: 1,
        id: 29,
        name: "Consumption PO",
        count: dashboardKeyWord.ConsumptionByPO,
        required: false,
        yaxis: "requiredQuantity",
        xaxis: "poNumber",
        type: "po",
        width: 6,
        grouped: true,
        ToolTip: "consumption",
        yaxisHeader: "UNit",
      },
      {
        uid: 1,
        id: 30,
        name: "Consumption ASN",
        count: dashboardKeyWord.ConsumptionByASN,
        required: false,
        yaxis: "requiredQuantity",
        xaxis: "shipmentId",
        type: "ops",
        width: 6,
        ToolTip: "consumption",
        yaxisHeader: "Unit",
        grouped: true,
      },
      {
        uid: 1,
        id: 31,
        name: "Consumption Material",
        count: dashboardKeyWord.ConsumptionByMaterial,
        required: false,
        yaxis: "requiredQuantity",
        xaxis: "subMaterial",
        type: "ops",
        width: 6,
        ToolTip: "consumption",
        yaxisHeader: "Unit",
        grouped: true,
      },
    ],
  },
};
export default dashboardConfig;
