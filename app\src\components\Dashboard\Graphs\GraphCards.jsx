import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>utton, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Dialog, <PERSON>alogContent, DialogTitle, FormControl, Select, MenuItem, useMediaQuery, useTheme } from "@mui/material";
import { NoDataFound } from "@cw/rds/illustrations";
import { colors } from "@constant/colors";
import { GraphCard } from "@cw/rds";
import { DragIndicator, Fullscreen, Close } from "@mui/icons-material";
import { useState, useMemo } from "react";
import { CHART_TYPE, LINEAR_CHART_TYPES, CIRCULAR_CHART_TYPES, STACKED_CHART_TYPES } from "@constant/enum";
import  useLang  from '../../../hooks/useLang';

// Function to get colors based on palette name
const getColorsFromPalette = (paletteName) => {
  return colors.colorPalleteDashboard[paletteName] || colors.colorPalleteDashboard.default;
};

const isCircularChart = (chartType) => {
  return chartType === CHART_TYPE.PIE || chartType === CHART_TYPE.DONUT;
};

const isStackedChart = (chartType) => {
  return STACKED_CHART_TYPES.includes(chartType);
};

const getBaseChartType = (chartType) => {
  switch (chartType) {
    case CHART_TYPE.STACKED_BAR:
      return CHART_TYPE.BAR;
    case CHART_TYPE.STACK_COLUMN:
      return CHART_TYPE.COLUMN;
    case CHART_TYPE.STACKED_LINE:
    case CHART_TYPE.STACK_LINE:
      return CHART_TYPE.LINE;
    case CHART_TYPE.STACKED_AREA:
      return CHART_TYPE.AREA;
    default:
      return chartType;
  }
};

const transformDataForGraphCard = (data, chartType) => {
  if (!data) return data;
  
  if (data.data && Array.isArray(data.data)) {
    const categories = data.data[0]?.data?.map(item => item.x) || [];
    
    const series = data.data.map(seriesItem => ({
      name: seriesItem.name,
      data: seriesItem.data.map(item => item.y)
    }));
    
    return {
      ...data,
      categories,
      series,
      isStacked: isStackedChart(chartType),
      graphDetails: {
        ...data.graphDetails,
        chartType
      }
    };
  }
  
  return {
    ...data,
    isStacked: isStackedChart(chartType),
    graphDetails: {
      ...data.graphDetails,
      chartType
    }
  };
};

const GraphCards = ({ title, data = [], handleOpenDialog = () => {}, dragHandleProps = {} }) => {
  const theme = useTheme();
  const isLargeScreen = useMediaQuery(theme.breakpoints.up('md'));
  const [maximized, setMaximized] = useState(false);
  const [tempChartType, setTempChartType] = useState(null);
  const { t } = useLang();
  
  const originalChartType = data?.graphDetails?.chartType;
  
  useMemo(() => {
    if (maximized && !tempChartType) {
      setTempChartType(originalChartType);
    }
  }, [maximized, originalChartType, tempChartType]);

  const hasValidChartData = (data) => {
    return !!data && Object.keys(data).length > 0;
  };

  const handleMaximize = () => {
    setMaximized(true);
    setTempChartType(originalChartType);
  };

  const handleClose = () => {
    setMaximized(false);
    setTempChartType(null);
  };

  const handleChartTypeChange = (event) => {
    setTempChartType(event.target.value);
  };

  const modifiedData = useMemo(() => {
    if (!tempChartType || !data) return data;
    const transformedData = transformDataForGraphCard(data, tempChartType);

    if (transformedData?.graphDetails?.graphName) {
      transformedData.graphDetails.graphName = t(transformedData.graphDetails.graphName);
    }
    
    return transformedData;
  }, [data, tempChartType, t]);

  const renderChart = () => {
    if (!hasValidChartData(data)) {
      return renderNoData();
    }
    const transformedData = transformDataForGraphCard(data, originalChartType);

    if (transformedData?.graphDetails?.graphName) {
      transformedData.graphDetails.graphName = t(transformedData.graphDetails.graphName);
    }
    const paletteColors = getColorsFromPalette(transformedData?.graphDetails?.colorPallete);
    return <GraphCard 
      values={transformedData} 
      isTable={true} 
      showDownload 
      showGraphName 
      graphColor={paletteColors}
    />;
  };

  const renderNoData = () => (
    <div style={{ textAlign: "center" }}>
      <img alt={t("No Data Found")} style={{ height: "250px" }} src={NoDataFound} />
      <Typography variant="h6" style={{ marginTop: "10px" }}>
        {t("No Data Found")}
      </Typography>
    </div>
  );

  return (
    <>
      <Card
        sx={{
          borderRadius: "10px",
          boxShadow: 1,
          height: "auto",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <CardContent>
          <Stack justifyContent="flex-end" alignItems="center" direction="row" spacing={1}>
            <IconButton 
              size="small" 
              onClick={handleMaximize}
              title="Maximize"
              sx={{ 
                backgroundColor: 'rgba(0, 0, 0, 0.05)',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                }
              }}
            >
              <Fullscreen fontSize="small" />
            </IconButton>
            <IconButton {...dragHandleProps} size="small" style={{ cursor: "grab" }}>
              <DragIndicator />
            </IconButton>
          </Stack>
          {renderChart()}
        </CardContent>
      </Card>

      <Dialog
        open={maximized}
        onClose={handleClose}
        fullWidth
        maxWidth="lg"
        PaperProps={{
          sx: {
            height: {
              xs: "100vh",
              sm: "100vh",
              md: "80vh",
              lg: "80vh",
              xl: "80vh",
            },
            maxHeight: {
              xs: "100vh",
              sm: "100vh",
              md: "80vh",
              lg: "80vh",
              xl: "80vh",
            },
            borderRadius: "10px",
            margin: { xs: "0", md: "24px" },
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "16px 24px",
            borderBottom: `1px solid ${colors.dialog.borderBottom}`,
          }}
        >
          <Stack direction="row" spacing={2} alignItems="center">
            <Typography variant="h6">
              {data?.graphDetails?.graphName ? t(data.graphDetails.graphName) : t("Chart View")}
            </Typography>
            
            {hasValidChartData(data) && (
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <Select
                  value={tempChartType || ""}
                  onChange={handleChartTypeChange}
                  displayEmpty
                  variant="outlined"
                  sx={{ height: '32px' }}
                >
                  {isCircularChart(originalChartType) 
                    ? CIRCULAR_CHART_TYPES.map(option => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))
                    : LINEAR_CHART_TYPES.map(option => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))
                  }
                </Select>
              </FormControl>
            )}
          </Stack>
          <IconButton onClick={handleClose} size="small">
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent
          sx={{
            display: "flex",
            flexDirection: "column",
            padding: "24px",
            height: "calc(100% - 64px)",
            overflow: "hidden",
          }}
        >
          <div style={{ 
            flex: 1, 
            display: 'flex', 
            flexDirection: 'column',
            height: '100%',
            overflow: 'hidden'
          }}>
            {hasValidChartData(data) ? (
              (() => {
                const paletteColors = getColorsFromPalette(modifiedData?.graphDetails?.colorPallete);
                return (
                  <GraphCard 
                    values={modifiedData} 
                    isTable={true} 
                    showDownload 
                    showGraphName 
                    height="100%" 
                    graphColor={paletteColors}
                  />
                );
              })()
            ) : (
              renderNoData()
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default GraphCards;
