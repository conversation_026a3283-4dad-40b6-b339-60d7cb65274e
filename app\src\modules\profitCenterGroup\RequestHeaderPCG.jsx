import React, { useState, useEffect } from "react";
import {
  <PERSON>rid,
  <PERSON>Field,
  MenuItem,
  Button,
  Card,
  Typography,
  Select,
  InputLabel,
  FormControl,
  Stack,
  Box,
} from "@mui/material";
import { useSelector, useDispatch } from "react-redux";
import { doAjax } from "../../components/Common/fetchService";
import { container_Padding } from "../../components/Common/commonStyles";
import FilterFieldGlobal from "../../components/MasterDataCockpit/FilterFieldGlobal";
import {
  destination_ProfitCenter_Mass,

} from "../../destinationVariables";

import {
  ERROR_MESSAGES,
  MODULE_MAP,
  REQUEST_HEADER_FILED_NAMES,
  REQUEST_TYPE,
  SUCCESS_MESSAGES,
  VISIBILITY_TYPE,
} from "@constant/enum";

import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { setRequestHeaderData } from "@app/redux/requestHeaderSlice";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import useProfitCenterChangeFieldConfig from "@hooks/useProfitCenterChangeFieldConfig";
import DownloadDialog from "@components/Common/DownloadDialog";
import { END_POINTS } from "@constant/apiEndPoints";
import { APP_END_POINTS } from "@constant/appEndPoints";
import { useLocation, useNavigate } from "react-router-dom";

import usePCGRequestHeaderConfig from "@hooks/usePCGRequestHeaderConfig";
import { setRequestHeader } from "@app/requestDataSlice";
import ReusableDialogForHierarchy from "@components/Common/ReusableDialogForHierarchy";
import { Hierarchy_Templates } from "@constant/changeTemplates";
import { setRequestHeaderPayloadDataPCG } from "@app/hierarchyDataSlice";
import { MODULE } from "@constant/enum";
import { MANDATORY_FILTERS } from "@constant/changeTemplates";

const RequestHeaderPCG = ({
  downloadClicked,
  setDownloadClicked,
  setIsSecondTabEnabled,
  setIsAttachmentTabEnabled,
}) => {
  const currentHash = window.location.hash;
  const parts = currentHash.split("/");
  const activeLocation = parts[parts.length - 1];

  const today = new Date();
  const formattedDate = today.toLocaleDateString("en-GB");
  const dispatch = useDispatch();
  const payloadFields = useSelector(
    (state) => state.hierarchyData.requestHeaderData
  );
  const requestHeaderData = useSelector((state) => state.request.requestHeader);
  const userData = useSelector((state) => state.userManagement.userData);
  const hierarchyData = useSelector((state) => state.hierarchyData);
  const requestHeaderDetails = useSelector(
    (state) => state.tabsData.requestHeaderData
  );
  const fieldNames = useSelector(
    (state) => state.AllDropDown.dropDown.FieldName || []
  );

  const dropDownDataFromRedux = useSelector(
    (state) => state.AllDropDown.dropDown
  );
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const RequestType = queryParams.get("RequestType");
  const RequestId = queryParams.get("RequestId");
  const isreqBench = queryParams.get("reqBench");
  const isWorkspace = queryParams.get("RequestId");
  const currentDate = `/Date(${Date.now()})/`;

  const [requestType, setRequestType] = useState("");
  const [requestPriorityValue, setRequestPriorityValue] = useState("");
  const [requestDesc, setRequestDesc] = useState("");
  const [changeCategory, setChangeCategory] = useState("");
  const [changeReason, setChangeReason] = useState([]);
  const navigate = useNavigate();
  const [isButtonEnabled, setIsButtonEnabled] = useState(false);
  const [openSnackBar, setOpenSnackBar] = useState(false);
  const [alertMsg, setAlertMsg] = useState("");
  const [alertType, setAlertType] = useState("success");
  const [isLoading, setIsLoading] = useState(false);
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [successMsg, setSuccessMsg] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState("");
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);

  const { getChangeTemplate } = useProfitCenterChangeFieldConfig();
  const { getRequestHeaderTemplatePCG } = usePCGRequestHeaderConfig();
  const rowData = location.state;
  const requestTypeData = [
    {
      code: "Create",
      tooltip: "Create New Profit Center Group Directly in Application",
    },
    {
      code: "Change",
      tooltip: "Modify Existing Profit Center Group Directly in Application",
    },
    {
      code: "Create with Upload",
      tooltip: "Create New Profit Center Group with Excel Upload",
    },
    {
      code: "Change with Upload",
      tooltip: "Modify Existing Profit Center Group with Excel Upload",
    },
  ];

  const templateNames = [
    { code: "All Other Fields", desc: "" },
    { code: "Address Change", desc: "" },
    { code: "Block", desc: "" },
    { code: "Temporary Block/Unblock", desc: "" },
  ];

  const requestPriority = [
    { code: "High", desc: "" },
    { code: "Medium", desc: "" },
    { code: "Low", desc: "" },
  ];

  dispatch(
    setRequestHeaderPayloadDataPCG({ keyName: "RequestStatus", data: "DRAFT" })
  );
  dispatch(
    setRequestHeaderPayloadDataPCG({
      keyName: "ReqCreatedBy",
      data: userData?.user_id,
    })
  );

  useEffect(() => {
    if (downloadClicked) {
      if (payloadFields?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) {
        setOpenDownloadDialog(true);
        return;
      }
      if (payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
        setDialogOpen(true);
        return;
      }
    }
  }, [downloadClicked]);

  const checkAllFieldsFilled = () => {
    let allFilled = true;
    if (
      payloadFields &&
      requestHeaderDetails[Object.keys(requestHeaderDetails)]?.length
    ) {
      requestHeaderDetails[Object.keys(requestHeaderDetails)[0]]?.forEach(
        (reqst) => {
          if (
            !payloadFields[reqst.jsonName] &&
            reqst.visibility === VISIBILITY_TYPE?.MANDATORY
          ) {
            allFilled = false;
          }
        }
      );
    } else {
      allFilled = false;
    }
    return allFilled;
  };

  useEffect(() => {
    if (payloadFields?.TemplateName) {
      getChangeTemplate(payloadFields?.TemplateName);
    }
  }, [payloadFields?.TemplateName]);

  useEffect(() => {
    getRequestHeaderTemplatePCG();
  }, [payloadFields?.RequestType]);

  useEffect(() => {
    const baseValid =
      requestType && requestPriorityValue && requestDesc.trim() !== "";
    const changeFieldsValid =
      requestType !== "Change" ||
      requestType === "Change with Upload" ||
      (changeCategory && changeReason.length > 0);
    setIsButtonEnabled(baseValid && changeFieldsValid);
  }, [
    requestType,
    requestPriorityValue,
    requestDesc,
    changeCategory,
    changeReason,
  ]);

  useEffect(() => {
    if (RequestId && (RequestType === REQUEST_TYPE?.CREATE || RequestType === REQUEST_TYPE?.CHANGE) && !rowData?.parentNode?.length) {
      setDialogOpen(true);
    }
  }, [RequestId]);

  const handleButtonClick = () => {
    const epochTime = new Date(payloadFields?.ReqCreatedOn).getTime();
    
    setDialogOpen(false);

    const payload = {
      RequestId: "",
      ReqCreatedBy: userData?.user_id || "",
      ReqCreatedOn: epochTime ? `/Date(${epochTime})/` : currentDate,
      ReqUpdatedOn: epochTime ? `/Date(${epochTime})/` : currentDate,
      RequestType: payloadFields?.RequestType || "",
      RequestPrefix: "",
      RequestPriority: payloadFields?.RequestPriority || "",
      RequestDesc: payloadFields?.RequestDesc || "",
      RequestStatus: "DRAFT",
      FirstProd: "",
      LaunchDate: "",
      LeadingCat: "",
      Division: "",
      TemplateName: "",
      FieldName: "",
      Region: payloadFields?.Region || "",
      FilterDetails: "",
      IsBifurcated: true,
      IsHierarchyGroup: true,
    };

    const hSuccess = (data) => {
      setSuccessMsg(true);
      setMessageDialogMessage(
        `Request Header Created Successfully! Request ID: ${data?.body?.requestId}`
      );
      setIsLoading(false);
      setAlertType("success");
      handleSnackBarOpen();
      setIsAttachmentTabEnabled(true);
      dispatch(setRequestHeaderPayloadDataPCG({ keyName: REQUEST_HEADER_FILED_NAMES.REQUEST_ID, data: data?.body?.requestId}))
      dispatch(setRequestHeaderData(data?.body));
      dispatch(setRequestHeader(data.body));
      if (
        payloadFields?.RequestType === REQUEST_TYPE.CREATE ||
        payloadFields?.RequestType === REQUEST_TYPE.CHANGE
      ) {
        setDialogOpen(true);
        return;
      }

      if (
        payloadFields?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
        payloadFields?.RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
      ) {
        setOpenDownloadDialog(true);
        return;
      }
    };

    const hError = (error) => {
      console.error("APIError", error);
      setSuccessMsg(true);
      setAlertType("error");
      setMessageDialogMessage("Error occured while saving Request Header");
      handleSnackBarOpen();
    };

    const destination = destination_ProfitCenter_Mass;

    // 📡 Trigger API
    doAjax(
      `/${destination}/massAction/createRequestHeader`,
      "post",
      hSuccess,
      hError,
      payload
    );

    // doAjax(
    //   `/${destination_ProfitCenter_Mass}/massAction/createRequestHeader`,
    //   "post",
    //   hSuccess,
    //   hError,
    //   payload
    // );
  };

  const handleDownloadDialogClose = () => {
    setDownloadClicked(false);
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
    if (!isWorkspace) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
  };

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };

  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };

  const handleDownload = () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
  
    const hSuccess = (response) => {
      if (response?.size == 0) {
        setBlurLoading(false);
        setLoaderMessage("");
        setSuccessMsg(true);
        setMessageDialogMessage("No data found for the selected criteria.");
        setAlertType("danger");
        handleSnackBarOpen();
        return;
      }
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute(
        "download",
        payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
          ? `${MODULE_MAP?.PCG}_Mass Change.xlsx`
          : `${MODULE_MAP?.PCG}_Mass Create.xlsx`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");

      setSuccessMsg(true);
      setMessageDialogMessage(
        `${
          payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
            ? `${MODULE_MAP?.PCG}_Mass Change`
            : `${MODULE_MAP?.PCG}_Mass Create`
        }.xlsx has been downloaded successfully.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(`/requestBench`);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
    };

    const downloadUrl = `/${destination_ProfitCenter_Mass}${
      payloadFields?.RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
        ? END_POINTS.EXCEL.DOWNLOAD_EXCEL_PCG_CHANGE_WITH_UPLOAD
        : END_POINTS.EXCEL.DOWNLOAD_EXCEL_PCG_CREATE_WITH_UPLOAD
    }`;
    doAjax(downloadUrl, "getblobfile", hSuccess, hError);
  };

  const handleEmailDownload = () => {
    setBlurLoading(true);
    const hSuccess = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      setSuccessMsg(true);
      setMessageDialogMessage(SUCCESS_MESSAGES?.DOWNLOAD_MAIL_INITIATED);
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
      setSuccessMsg(true);
      setMessageDialogMessage(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL);
      setAlertType("danger");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };

    const downloadUrl = `/${destination_MaterialMgmt}${
      payloadFields?.RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
        ? END_POINTS.EXCEL.DOWNLOAD_EXCEL_PCG_CHANGE_WITH_UPLOAD_MAIL
        : END_POINTS.EXCEL.DOWNLOAD_EXCEL_PCG_CREATE_WITH_UPLOAD_MAIL
    }`;

    doAjax(downloadUrl, "get", hSuccess, hError);
  };

  let INITIAL_DIALOG_SCENARIO = {
    [REQUEST_TYPE?.CREATE]: "CREATE_PCG",
    [REQUEST_TYPE?.CHANGE]: "CHANGE_PCG",
  };
  return (
    <div>
      <Stack spacing={2}>
        {Object.entries(requestHeaderDetails).map(([key, fields]) => (
          <Grid
            item
            md={12}
            key={key}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              borderRadius: "8px",
              border: "1px solid #E0E0E0",
              mt: 0.25,
              boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              ...container_Padding,
            }}
          >
            <Typography
              sx={{
                fontSize: "12px",
                fontWeight: "700",
                paddingBottom: "10px",
              }}
            >
              {key}
            </Typography>
            <Box>
              <Grid container spacing={1}>
                {fields
                  .filter((field) => field.visibility !== "Hidden")
                  .sort((a, b) => a.sequenceNo - b.sequenceNo)
                  .map((innerItem) => (
                    <FilterFieldGlobal
                      isHeader={true}
                      key={innerItem.id}
                      field={innerItem}
                      dropDownData={{
                        RequestType: requestTypeData,
                        RequestPriority: requestPriority,
                        TemplateName: templateNames,
                      }}
                      disabled={isWorkspace || !!requestHeaderData?.requestId}
                      requestHeader={true}
                      module={"PCG"}
                    />
                  ))}
              </Grid>
            </Box>
            {!isWorkspace && !requestHeaderData?.requestId && (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  marginTop: "20px",
                }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  disabled={!checkAllFieldsFilled()}
                  onClick={handleButtonClick}
                >
                  Save Request Header
                </Button>
              </Box>
            )}
          </Grid>
        ))}
        <ReusableBackDrop
          blurLoading={blurLoading}
          loaderMessage={loaderMessage}
        />
        {successMsg && (
          <ReusableSnackBar
            openSnackBar={openSnackbar}
            alertMsg={messageDialogMessage}
            alertType={alertType}
            handleSnackBarClose={handleSnackBarClose}
          />
        )}

        {dialogOpen && (
          <ReusableDialogForHierarchy
            open={dialogOpen}
            onClose={() => {
              setDialogOpen(false);
            }}
            parameters={
              Hierarchy_Templates[
                INITIAL_DIALOG_SCENARIO?.[payloadFields?.RequestType]
              ]
            }
            setShowTable={false}
            allDropDownData={dropDownDataFromRedux}
            setIsSecondTabEnabled={setIsSecondTabEnabled}
            module={MODULE?.PCG}
            mandatoryFields={
              MANDATORY_FILTERS?.[
                INITIAL_DIALOG_SCENARIO?.[payloadFields?.RequestType]
              ]
            }
          />
        )}
        <DownloadDialog
          onDownloadTypeChange={onDownloadTypeChange}
          open={openDownloadDialog}
          downloadType={downloadType}
          handleDownloadTypeChange={handleDownloadTypeChange}
          onClose={handleDownloadDialogClose}
        />
      </Stack>
    </div>
  );
};

export default RequestHeaderPCG;
