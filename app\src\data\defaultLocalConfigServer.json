{"accessItems": [{"id": 1, "iwaName": "Home", "name": "Home", "type": "module", "componentName": "Homepage", "itemCode": "", "isSideOption": false, "displayName": "Home", "icon": "Home", "routePath": "/", "importPath": "Homepage/Homepage", "isAccessible": true, "childItems": []}, {"id": 2, "iwaName": "Dashboard", "name": "Dashboard", "type": "module", "componentName": "Dashboard", "itemCode": "", "isSideOption": true, "displayName": "Dashboard", "icon": "Dashboard", "routePath": "/dashboard", "importPath": "Dashboard/Dashboard", "isAccessible": true, "childItems": []}, {"id": 3, "iwaName": "ITM Workbench", "name": "Workspace", "type": "module", "componentName": "", "itemCode": "", "isSideOption": true, "displayName": "Workspace", "icon": "GroupWork", "routePath": "/workspace", "importPath": "ITMWorkbench/MyTasks", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "ITM My Tasks", "name": "MyTasks", "type": "submodule", "componentName": "MyTasks", "itemCode": "", "isSideOption": true, "displayName": "My Tasks", "icon": "PendingActions", "routePath": "/workspace/mytasks", "importPath": "ITMWorkbench/MyTasks", "isAccessible": true, "childItems": []}, {"id": 2, "iwaName": "ITM Completed Tasks", "name": "CompletedTasks", "type": "submodule", "componentName": "CompletedTasks", "itemCode": "", "isSideOption": true, "displayName": "Completed Tasks", "icon": "Task", "routePath": "/workspace/completedtasks", "importPath": "ITMWorkbench/CompletedTasks", "isAccessible": true, "childItems": []}]}, {"id": 4, "iwaName": "Purchase Management", "name": "PurchaseManagement", "type": "module", "componentName": "", "itemCode": "", "isSideOption": true, "displayName": "Purchase Management", "icon": "ReceiptLong", "routePath": "/purchaseOrder", "importPath": "PurchaseOrder/PurchaseOrder", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "Purchase Order", "name": "PurchaseOrder", "type": "submodule", "componentName": "PurchaseOrder", "itemCode": "", "isSideOption": true, "displayName": "Purchase Order", "icon": "Assignment", "routePath": "/purchaseOrder/management", "importPath": "PurchaseOrder/PurchaseOrder", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "", "name": "POUserActions", "type": "feature", "componentName": "", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/X/", "importPath": "", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "", "name": "MarkForASN", "type": "feature", "componentName": "", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/X/", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 1, "iwaName": "", "name": "MarkForDPR", "type": "feature", "componentName": "", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/X/", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 1, "iwaName": "", "name": "MarkForDPRAndASN", "type": "feature", "componentName": "", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/X/", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 1, "iwaName": "", "name": "MarkForRequiresConfirmation", "type": "feature", "componentName": "", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/X/", "importPath": "", "isAccessible": true, "childItems": []}]}, {"id": 1, "iwaName": "", "name": "SinglePO", "type": "feature", "componentName": "SinglePO", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/purchaseOrder/management/singlePurchaseOrder/:poId/*", "importPath": "PurchaseOrder/SinglePO", "isAccessible": true, "childItems": []}]}, {"id": 2, "iwaName": "Daily Production Report", "name": "DailyProductionReport", "type": "submodule", "componentName": "DPR", "itemCode": "", "isSideOption": true, "displayName": "Daily Production Report", "icon": "Task", "routePath": "/purchaseOrder/DPR", "importPath": "PurchaseOrder/DPR/DPR.page", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "", "name": "SingleDPR", "type": "feature", "componentName": "SingleDPR", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/purchaseOrder/DPR/singleDPR/:poId", "importPath": "PurchaseOrder/DPR/DPR.details", "isAccessible": true, "childItems": []}]}, {"id": 3, "iwaName": "Advanced Shipment Notification", "name": "AdvancedShipmentNotification", "type": "submodule", "componentName": "ASNHome", "itemCode": "", "isSideOption": true, "displayName": "Advanced Shipment Notification", "icon": "LocalShipping", "routePath": "/purchaseOrder/ASN", "importPath": "PurchaseOrder/ASN/ASNHome", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "", "name": "AdvanceShipmentNotificationPage", "type": "feature", "componentName": "AdvanceShipmentNotificationPage", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/purchaseOrder/ASN/CreateASN", "importPath": "PurchaseOrder/ASN/AdvanceShipmentNotification.page.jsx", "isAccessible": true, "childItems": []}, {"id": 1, "iwaName": "", "name": "SingleASN", "type": "feature", "componentName": "SingleASN", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/purchaseOrder/ASN/singleASN/:poId", "importPath": "PurchaseOrder/ASN/SingleASN", "isAccessible": true, "childItems": []}, {"id": 1, "iwaName": "", "name": "ASNdetails", "type": "feature", "componentName": "ASNdetails", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/purchaseOrder/ASN/details/:asnId", "importPath": "PurchaseOrder/ASN/ASNdetails", "isAccessible": true, "childItems": []}]}, {"id": 4, "iwaName": "Confirmation Tracker", "name": "ConfirmationTracker", "type": "submodule", "componentName": "ConfirmationTracker", "itemCode": "", "isSideOption": true, "displayName": "Confirmation Tracker", "icon": "GroupWork", "routePath": "/purchaseOrder/confirmationTracker", "importPath": "WorkBench/ConfirmationTracker", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "", "name": "SingleWorkbench", "type": "feature", "componentName": "SingleWorkbench", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/purchaseOrder/confirmationTracker/taskDetail/:reqId", "importPath": "WorkBench/SingleWorkbench", "isAccessible": true, "childItems": []}]}, {"id": 5, "iwaName": "Consumption Tracker", "name": "ConsumptionTracker", "type": "submodule", "componentName": "ConsumptionTracker", "itemCode": "", "isSideOption": true, "displayName": "Consumption Tracker", "icon": "Table<PERSON>hart", "routePath": "/purchaseOrder/consumptionTracker", "importPath": "PurchaseOrder/ASN/ConsumptionTracker", "isAccessible": true, "childItems": []}]}, {"id": 5, "iwaName": "Invoice Management", "name": "InvoiceManagement", "type": "module", "componentName": "", "itemCode": "", "isSideOption": true, "displayName": "Invoice Management", "icon": "Description", "routePath": "/invoices", "importPath": "Invoice/EInvoice&CreateInvoice/EInvoice", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "E-Invoice", "name": "EInvoice", "type": "submodule", "componentName": "EInvoice", "itemCode": "", "isSideOption": true, "displayName": "E-Invoice", "icon": "Receipt", "routePath": "/invoices/management", "importPath": "Invoice/EInvoice&CreateInvoice/EInvoice", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "", "name": "CreateInvoice", "type": "feature", "componentName": "CreateInvoice", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/invoices/management/createInvoice", "importPath": "Invoice/EInvoice&CreateInvoice/CreateInvoice", "isAccessible": true, "childItems": []}]}, {"id": 2, "iwaName": "Invoice Tracker", "name": "InvoiceTracker", "type": "submodule", "componentName": "InvoiceTracker", "itemCode": "", "isSideOption": true, "displayName": "Invoice Tracker", "icon": "TrackChanges", "routePath": "/invoices/invoiceTracker", "importPath": "Invoice/InvoiceTracker/InvoiceTracker", "isAccessible": true, "childItems": []}, {"id": 3, "iwaName": "", "name": "SingleInvoice", "type": "feature", "componentName": "SingleInvoice", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/invoices/workbench/singleInvoice/:reqId", "importPath": "Invoice/InvoiceWorkbench/SingleInvoice", "isAccessible": true, "childItems": []}, {"id": 4, "iwaName": "", "name": "SingleInvoiceForTracker", "type": "feature", "componentName": "SingleInvoiceForTracker", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/invoices/invoiceTracker/singleInvoice/:reqId", "importPath": "Invoice/InvoiceTracker/SingleInvoiceForTracker", "isAccessible": true, "childItems": []}]}, {"id": 6, "iwaName": "Document Management", "name": "DocumentManagement", "type": "module", "componentName": "Document", "itemCode": "", "isSideOption": true, "displayName": "Document Management", "icon": "FolderOpen", "routePath": "/documentManagement", "importPath": "DocumentManagement/Document", "isAccessible": true, "childItems": []}, {"id": 7, "iwaName": "Config <PERSON>", "name": "ConfigCockpit", "type": "module", "componentName": "", "itemCode": "", "isSideOption": true, "displayName": "Config <PERSON>", "icon": "Settings", "routePath": "/configCockpit", "importPath": "UserManagement/Components/MasterData", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "User Management", "name": "UserManagement", "type": "submodule", "componentName": "UserManagement", "itemCode": "", "isSideOption": true, "displayName": "User Management", "icon": "Person", "routePath": "/configCockpit/userManagement?component=users", "importPath": "UserManagement/Components/MasterData", "isAccessible": true, "childItems": []}, {"id": 2, "iwaName": "Broadcast Management", "name": "BroadcastManagement", "type": "submodule", "componentName": "", "itemCode": "", "isSideOption": true, "displayName": "Broadcast Management", "icon": "BroadcastOnPersonal", "routePath": "/configCockpit/broadcastManagement", "importPath": "BroadcastManagement/BroadcastHome", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "", "name": "ViewBroadcast", "type": "feature", "componentName": "", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/broadcastManagement/viewBroadcast/:BroadcastID", "importPath": "BroadcastManagement/ViewBroadcast", "isAccessible": true, "childItems": []}, {"id": 1, "iwaName": "", "name": "NewBroadcast", "type": "feature", "componentName": "NewBroadcast", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/broadcastManagement/newBroadcast", "importPath": "BroadcastManagement/NewBroadcast", "isAccessible": true, "childItems": []}, {"id": 1, "iwaName": "", "name": "EditBroadcast", "type": "feature", "componentName": "", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/broadcastManagement/editBroadcast/:BroadcastID", "importPath": "BroadcastManagement/EditBroadcast", "isAccessible": true, "childItems": []}]}, {"id": 3, "iwaName": "Config Cockpit DPR", "name": "DailyProductionReportConfig", "type": "submodule", "componentName": "DPR_Config", "itemCode": "", "isSideOption": true, "displayName": "Daily Production Report", "icon": "Summarize", "routePath": "/configCockpit/dailyProductionReport", "importPath": "ConfigCockpit/DPR_Config.jsx", "isAccessible": true, "childItems": []}, {"id": 4, "iwaName": "Config Cockpit Purchase Order", "name": "PurchaseOrderConfig", "type": "submodule", "componentName": "", "itemCode": "", "isSideOption": true, "displayName": "Purchase Order", "icon": "StickyNote2", "routePath": "/X/", "importPath": "", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "Config Cockpit Post Purchase Order", "name": "PostPOConfirmation", "type": "feature", "componentName": "PostPOConfig", "itemCode": "", "isSideOption": true, "displayName": "Post PO Confirmation", "icon": "FileOpen", "routePath": "/configCockpit/poConfig/postPO", "importPath": "ConfigCockpit/PostPOConfig.jsx", "isAccessible": true, "childItems": []}, {"id": 2, "iwaName": "Config Co<PERSON>pit Add On Purchase Order", "name": "AddOnPOProcess", "type": "feature", "componentName": "AddOnPOConfig", "itemCode": "", "isSideOption": true, "displayName": "Add On PO Process", "icon": "PostAdd", "routePath": "/configCockpit/poConfig/addOnPO", "importPath": "ConfigCockpit/AddOnPOConfig.jsx", "isAccessible": true, "childItems": []}]}, {"id": 5, "iwaName": "Document Management Settings", "name": "DocumentManagementSettings", "type": "submodule", "componentName": "DocumentManagementSettings", "itemCode": "", "isSideOption": true, "displayName": "Document Management Settings", "icon": "Person", "routePath": "/configCockpit/DocumentManagement", "importPath": "/ConfigCockpit/DocumentManagementSettings.jsx", "isAccessible": true, "childItems": []}, {"id": 6, "iwaName": "SLA Management", "name": "SLAManagement", "type": "submodule", "componentName": "", "itemCode": "", "isSideOption": true, "displayName": "SLA Management", "icon": "AccessAlarm", "routePath": "/configCockpit/SLAManagement", "importPath": "ConfigCockpit/SLAManagement", "isAccessible": false, "childItems": []}, {"id": 7, "iwaName": "Email Template Management", "name": "EmailTemplateManagement", "type": "submodule", "componentName": "", "itemCode": "", "isSideOption": true, "displayName": "Email Template Management", "icon": "MailOutline", "routePath": "/X/", "importPath": "", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "Entity Management", "name": "EntityManagement", "type": "feature", "componentName": "EmailEntityComponent", "itemCode": "", "isSideOption": true, "displayName": "Entity Management", "icon": "Account<PERSON>ree", "routePath": "/configCockpit/EmailTemplateManagement/entityManagement", "importPath": "ConfigCockpit/EmailManagement/EmailEntityComponent", "isAccessible": true, "childItems": []}, {"id": 2, "iwaName": "Group Description", "name": "GroupDescription", "type": "feature", "componentName": "EmailGroupDescriptionComponent", "itemCode": "", "isSideOption": true, "displayName": "Group Description", "icon": "Groups3", "routePath": "/configCockpit/EmailTemplateManagement/groupManagement", "importPath": "ConfigCockpit/EmailManagement/EmailGroupDescriptionComponent", "isAccessible": true, "childItems": []}, {"id": 3, "iwaName": "Templates", "name": "Templates", "type": "feature", "componentName": "", "itemCode": "", "isSideOption": true, "displayName": "Templates", "icon": "PostAdd", "routePath": "/configCockpit/EmailTemplateManagement/Templates", "importPath": "ConfigCockpit/EmailManagement/EmailTemplateComponent", "isAccessible": true, "childItems": []}]}, {"id": 7, "iwaName": "Document Management Settings", "name": "DocumentManagementSettings", "type": "submodule", "componentName": "DocumentManagementSettings", "itemCode": "", "isSideOption": true, "displayName": "Document Management Settings", "icon": "Person", "routePath": "/configCockpit/DocumentManagement", "importPath": "/ConfigCockpit/DocumentManagementSettings", "isAccessible": true, "childItems": []}]}, {"id": 8, "iwaName": "Return Management", "name": "ReturnManagement", "type": "module", "componentName": "ReturnManagement", "itemCode": "", "isSideOption": true, "displayName": "Return Management", "icon": "AssignmentReturn", "routePath": "/ReturnManagement", "importPath": "ReturnManagement/ReturnOrder", "isAccessible": true, "childItems": []}, {"id": 9, "iwaName": "Service Request", "name": "ServiceRequest", "type": "module", "componentName": "ServiceRequestPage", "itemCode": "", "isSideOption": true, "displayName": "Service Request", "icon": "ManageAccounts", "routePath": "/serviceRequest", "importPath": "ServiceRequest/ServiceRequest.page", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "", "name": "SingleServiceRequest", "type": "feature", "componentName": "ServiceRequestDetailsPage", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/serviceRequest/details/:reqId", "importPath": "ServiceRequest/ServiceRequest.detailsPage", "isAccessible": true, "childItems": []}, {"id": 1, "iwaName": "", "name": "CreateServiceRequest", "type": "feature", "componentName": "ServiceRequestDetailsPage", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/serviceRequest/details/:reqId", "importPath": "ServiceRequest/ServiceRequest.detailsPage", "isAccessible": true, "childItems": []}]}, {"id": 10, "iwaName": "Purchase Request Management", "name": "PurchaseRequest", "type": "module", "componentName": "PurchaseRequest", "itemCode": "", "isSideOption": true, "displayName": "Purchase Request", "icon": "Playlist<PERSON><PERSON><PERSON><PERSON><PERSON>", "routePath": "/purchaseRequest/management", "importPath": "PurchaseRequest/PurchaseRequest", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "", "name": "SinglePurchaseRequest", "type": "feature", "componentName": "SinglePurchaseRequest", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/purchaseRequest/management/singlePurchaseRequest/:prId", "importPath": "PurchaseRequest/SinglePurchaseRequest", "isAccessible": true, "childItems": []}]}, {"id": 11, "iwaName": "Service Sheet Management", "name": "ServiceEntrySheet", "type": "module", "componentName": "ServiceEntrySheet", "itemCode": "", "isSideOption": true, "displayName": "Service Sheet Management", "icon": "NoteAlt", "routePath": "/serviceSheet/serviceEntryOrder", "importPath": "ServiceEntrySheet/ServiceEntryOrder", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "", "name": "ServiceEntrySheet", "type": "subModule", "componentName": "ServiceEntryOrder", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/serviceSheet/serviceEntryOrder", "importPath": "ServiceEntrySheet/ServiceEntryOrder/ServiceEntryOrder", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "", "name": "SingleServiceSheet", "type": "feature", "componentName": "SingleServiceSheet", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/serviceSheet/serviceEntryOrder/singleServiceSheet/:serviceSheetId", "importPath": "ServiceEntrySheet/ServiceEntryOrder/SingleServiceSheet", "isAccessible": true, "childItems": []}, {"id": 1, "iwaName": "", "name": "CreateServiceEntry", "type": "feature", "componentName": "CreateServiceEntry", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/serviceSheet/serviceEntryOrder/createServiceEntry", "importPath": "ServiceEntrySheet/ServiceEntryOrder/CreateServiceEntry", "isAccessible": true, "childItems": []}, {"id": 1, "iwaName": "", "name": "EditServiceEntrySheet", "type": "feature", "componentName": "EditServiceEntrySheet", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/serviceSheet/serviceEntryOrder/editServiceEntry/:reqId", "importPath": "ServiceEntrySheet/ServiceEntryOrder/EditServiceEntrySheet", "isAccessible": true, "childItems": []}, {"id": 1, "iwaName": "", "name": "InitiateServiceEntry", "type": "feature", "componentName": "InitiateServiceEntry", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/serviceSheet/serviceEntryOrder/initiateServiceEntry", "importPath": "ServiceEntrySheet/ServiceEntryOrder/InitiateServiceEntry", "isAccessible": true, "childItems": []}]}]}, {"id": 12, "iwaName": "Planning Management", "name": "PlanningManagement", "type": "module", "componentName": "PlanningManagement", "itemCode": "", "isSideOption": true, "displayName": "Planning Management", "icon": "Psychology", "routePath": "/planningManagement", "importPath": "PlanningManagement/PlanningManagement", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "", "name": "PlanningManagement", "type": "subModule", "componentName": "PlanningManagement", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/planningManagement", "importPath": "PlanningManagement/PlanningManagement", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "", "name": "SinglePlanningTask", "type": "feature", "componentName": "SinglePlanningTask", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/planningManagement/singlePlanningTask/:reqId", "importPath": "PlanningManagement/SinglePlanningTask", "isAccessible": true, "childItems": []}, {"id": 1, "iwaName": "", "name": "CreateRequirementPlanDialog", "type": "feature", "componentName": "CreateRequirementPlanDialog", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/X/", "importPath": "PlanningManagement/CreateRequirementPlanDialog", "isAccessible": true, "childItems": []}, {"id": 1, "iwaName": "", "name": "SingleServiceSheet", "type": "feature", "componentName": "SingleServiceSheet", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/serviceSheet/serviceEntryOrder/singleServiceSheet/:serviceSheetId", "importPath": "ServiceEntrySheet/ServiceEntryOrder/SingleServiceSheet", "isAccessible": true, "childItems": []}]}]}, {"id": 13, "iwaName": "", "name": "Playground", "type": "module", "componentName": "", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/playground", "importPath": "Playground/Playground", "isAccessible": true, "childItems": []}, {"id": 14, "iwaName": "", "name": "POFlow", "type": "feature", "componentName": "", "itemCode": "", "isSideOption": false, "displayName": "", "icon": "", "routePath": "/X/", "importPath": "", "isAccessible": true, "childItems": []}], "faviconLink": "/favicon.ico", "appHeaderLogoClientName": "viatris_AppheaderImg", "sideNavMoreOptions": 6, "system": "scp"}