import * as icon from "@mui/icons-material";

import { createElement } from "react";

 

function ReusableIcon(props) {

  return createElement(

    icon[

    props?.isSelected ? `${props?.iconName}` : `${props?.iconName}Outlined`

    ],

    {

      sx: {

        color: `${props?.isSelected ? "rgb(55, 48, 199)" : props?.iconColor ?? "rgba(0, 0, 0, 0.54)"

          }`,

        fontSize: `${props?.iconSize ?? "24px"}`,

      },

      className: "iconClass"

    }

  );

};

 

export default ReusableIcon;