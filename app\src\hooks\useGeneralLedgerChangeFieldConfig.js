import { changeTemplateDT } from "@app/tabsDetailsSlice";
import { doAjax } from "@components/Common/fetchService";
import React from "react";
import { useSelector, useDispatch } from "react-redux";
import { destination_IDM } from "../destinationVariables";
import { END_POINTS } from "@constant/apiEndPoints";
import { API_CODE, REQUEST_TYPE, TASK_NAME } from "@constant/enum";
import { setDropDown } from "../app/dropDownDataSlice";
import useLogger from "./useLogger";
import {setChangeFieldSelectionData} from "../app/payloadSlice";

const useGeneralLedgerChangeFieldConfig = () => {

  const currentHash = window.location.hash; 
  const parts = currentHash.split("/");
  const activeLocation = parts[parts.length - 1]; 
  const { customError } = useLogger();
  const initialPayload = useSelector((state) => state?.costCenter?.payload?.requestHeaderData);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const dispatch = useDispatch();

  const module ="General Ledger";
  const getChangeTemplate = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_GL_CHANGE_TEMPLATE_DT",
      version: "v3",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_ROLE": TASK_NAME.REQ_INITIATE_FIN,
          "MDG_CONDITIONS.MDG_GL_ACCOUNT_TYPE": "X",
          "MDG_CONDITIONS.MDG_GL_SCENARIO":REQUEST_TYPE?.CHANGE,

        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        let responseData =
          data?.data?.result[0]?.MDG_MAT_CHANGE_TEMPLATE;
           dispatch(setChangeFieldSelectionData(responseData));
        const templateNames = [
        ...new Set(
        responseData.map((item) => item?.MDG_MAT_TEMPLATE).filter(Boolean)
        ),
        ].map((name) => ({ code: name }));
        dispatch(setDropDown({keyName:"TemplateName", data: templateNames}));
        dispatch(setChangeFieldSelectionData(uniqueLevels));
        let fieldSelectionCompanyCode = [];
        let fieldSelectionCOA = [];
        let fieldSelectionTempBlock = [];
        responseData?.map((element, index) => {
          if (element.MDG_FIELD_SELECTION_LVL == "COMPANY CODE") {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = element.MDG_SELECT_OPTION;
            fieldSelectionCompanyCode.push(COHash);
          } else if (element.MDG_FIELD_SELECTION_LVL == "CHART OF ACCOUNT") {
            let COAHash = {};
            COAHash["id"] = index;
            COAHash["name"] = element.MDG_SELECT_OPTION;
            fieldSelectionCOA.push(COAHash);
          } else {
            let COAHash = {};
            COAHash["id"] = index;
            COAHash["name"] = element.MDG_SELECT_OPTION;
            fieldSelectionTempBlock.push(COAHash);
          }
        });
        const uniqueNames = new Set();
        const distinctCompanyCodeData = fieldSelectionCompanyCode.filter(
          (obj) => {
            if (!uniqueNames.has(obj.name)) {
              uniqueNames.add(obj.name);
              return true;
            }
            return false;
          }
        );
        const uniqueNamesCOA = new Set();
        const distinctCartOfAccoutData = fieldSelectionCOA.filter((obj) => {
          if (!uniqueNamesCOA.has(obj.name)) {
            uniqueNamesCOA.add(obj.name);
            return true;
          }
          return false;
        });

        setDataList(distinctCompanyCodeData);
        setDataListCOA(distinctCartOfAccoutData);
        setDataListBlocked(fieldSelectionTempBlock);
      } else {
      }
      handleClose();
    };

    const hError = (error) => {
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  
  return { getChangeTemplate };
};

export default useGeneralLedgerChangeFieldConfig;
