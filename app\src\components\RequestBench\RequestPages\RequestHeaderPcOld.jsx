import {
  <PERSON>,
  <PERSON><PERSON>,
  Grid,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Checkbox,
  TextField,
  Autocomplete,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
} from "@mui/material";
import React, { Fragment, useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { setDropDown } from "../../../app/dropDownDataSlice";
import FilterField from "../../common/ReusableFilterBox/FilterField";
import { container_Padding } from "../../Common/commonStyles";
import {
  setRequestHeader,
  setRequestIdHeader,
  setRequestIdPrefix,
  setTabValue,
} from "../../../app/requestDataSlice";
import { ToastContainer } from "react-toastify";
import { doAjax } from "../../Common/fetchService";
import {
  destination_IDM,
  destination_MaterialMgmt,
  destination_ProfitCenter_Mass,
  destination_ProfitCenter,
} from "../../../destinationVariables";
import { setSelectedSections } from "../../../app/selectedSelectionsSlice";
import {
  newMaterialData,
  updateAllTabsData,
  changeTemplateDT,
} from "../../../app/tabsDetailsSlice";
import { useNavigate } from "react-router-dom";
import {
  setGeneralInformation,
  setMatRequiredFieldsGI,
  setMultipleMaterialPayloadKey,
  setSingleMaterialPayload,
} from "../../../app/payloadslice";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import { useLocation } from "react-router-dom";
import useMaterialRequestHeaderConfig from "@hooks/useMaterialRequestHeaderConfig";
import useProfitcenterRequestHeaderConfig from "@hooks/useProfitcenterRequestHeaderConfig";
import useMaterialChangeFieldConfig from "@hooks/useMaterialChangeFieldConfig";
import { setTaskData } from "@app/userManagementSlice";
import RequestDetailsForChange from "/@material/change/RequestDetailsForChange";
import { TEMPLATE_KEYS } from "@constant/changeTemplates";
import { ENABLE_STATUSES, REQUEST_TYPE } from "@constant/enum";

const RequestHeaderPC = ({
  setIsSecondTabEnabled,
  setIsAttachmentTabEnabled,
  requestStatus,
}) => {
  const [dropDownData, setDropDownData] = useState({});
  const [open, setOpen] = useState(false);
  const [mandFields, setMandFields] = useState([]);
  const [successMsg, setSuccessMsg] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [displayedFields, setDisplayedFields] = useState([]);
  const [formData, setFormData] = useState({});
  const [distributionChannelLookupData, setDistributionChannelLookupData] =
    useState([]);
  const [questions, setQuestions] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const requestHeaderDetails = useSelector(
    (state) => state.tabsData.requestHeaderData
  );
  console.log("requestHeaderDetails", requestHeaderDetails);
  const changeData = useSelector((state) => state.tabsData.changeFieldsDT);

  const payloadFields = useSelector((state) => state.payload.payloadData);
  console.log("payloadFields", payloadFields);
  const userData = useSelector((state) => state.userManagement.userData);
  console.log("userData", userData);
  const requestHeaderData = useSelector((state) => state.request.requestHeader);
  console.log("requestHeaderData", requestHeaderData);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");

  //   const { getRequestHeaderTemplate } = useMaterialRequestHeaderConfig();
  const { getChangeTemplate } = useMaterialChangeFieldConfig();
  const { getRequestHeaderTemplatePc } = useProfitcenterRequestHeaderConfig();

  const [reqBench, setreqBench] = useState(isreqBench);
  const requestTypeData = [
    {
      code: "Create",
      desc: "Create New ProfitCenter Directly in Application",
      java: "Create",
    },
    {
      code: "Change",
      desc: "Modify Existing ProfitCenter Directly in Application",
      java: "Change",
    },
    {
      code: "Create with Upload",
      desc: "Create New ProfitCenter with Excel Upload",
      java: "Mass Create Excel",
    },
    {
      code: "Change with Upload",
      desc: "Modify Existing ProfitCenter with Excel Upload",
      java: "Mass Change Excel",
    },
  ];
  //   const leadingCategory = [
  //     { code: "Oncology", desc: ""},
  //     { code: "Anesthesia/Pain Management", desc: "" },
  //     { code: "Cardiovascular", desc: "" },
  //   ];
  //   const templateNames = [
  //     { code: TEMPLATE_KEYS?.LOGISTIC, desc: ""},
  //     { code: TEMPLATE_KEYS?.MRP, desc: "" },
  //     { code: TEMPLATE_KEYS?.WARE_VIEW_2, desc: "" },
  //     { code: TEMPLATE_KEYS?.ITEM_CAT, desc: "" },
  //     { code: TEMPLATE_KEYS?.SET_DNU, desc: "" },
  //     { code: TEMPLATE_KEYS?.UPD_DESC, desc: "" },
  //     { code: TEMPLATE_KEYS?.CHG_STAT, desc: "" },
  //   ];
  const requestPriority = [
    { code: "High", desc: "" },
    { code: "Medium", desc: "" },
    { code: "Low", desc: "" },
  ];

  // let filterFields = Object?.entries(requestHeaderDetails);
  dispatch(setDropDown({ keyName: "RequestType", data: requestTypeData }));
  //   dispatch(setDropDown({ keyName: "LeadingCat", data: leadingCategory }))
  dispatch(setDropDown({ keyName: "RequestPriority", data: requestPriority }));
  //   dispatch(setDropDown({ keyName: "TemplateName", data: templateNames }))
  dispatch(
    setMultipleMaterialPayloadKey({
      keyName: "ReqCreatedBy",
      data: userData?.user_id,
    })
  );
  dispatch(
    setMultipleMaterialPayloadKey({ keyName: "RequestStatus", data: "DRAFT" })
  );

  // Define field data to be used in drop-downs and lookups
  const fieldListAccordingToView = [
    { label: "Basic Data", value: "basicData", fields: [], code: "K" },
    {
      label: "Sales",
      value: "sales",
      fields: ["Plant", "Sales Organization", "Distribution Channel"],
      code: "V",
    },
    { label: "Purchasing", value: "purchasing", fields: ["Plant"], code: "E" },
    { label: "Accounting", value: "accounting", fields: ["Plant"], code: "B" },
  ];

  const fixedOption = "Basic Data";
  const [selectedSections, setSelectedSectionsState] = useState([fixedOption]);
  const [filteredViewType, setFilteredViewType] = useState("");
  const [fieldReference, setFieldReference] = useState("");
  const [enableSaveRequest, setEnableSaveRequest] = useState(false);
  const [disableProceed, setDisableProceed] = useState(true);

  useEffect(() => {
    dispatch(setSelectedSections(selectedSections));
  }, [dispatch, selectedSections]);

  useEffect(() => {
    if (payloadFields?.MatlType) {
      getViews(payloadFields);
    }

    if (
      payloadFields &&
      requestHeaderDetails[Object.keys(requestHeaderDetails)]?.length
    ) {
      let allFilled = true;
      requestHeaderDetails[Object.keys(requestHeaderDetails)[0]]?.forEach(
        (reqst) => {
          if (
            !payloadFields[reqst.jsonName] &&
            reqst.jsonName !== "ReqCreatedOn" &&
            reqst.jsonName !== "ReqUpdatedOn" &&
            reqst.jsonName !== "Division" &&
            reqst.jsonName !== "MatlType" &&
            reqst.fieldName !== "First Production Date" &&
            reqst.fieldName !== "Launch Date" &&
            reqst.fieldName !== "Leading Category"
          ) {
            allFilled = false;
          }
        }
      );
      setEnableSaveRequest(allFilled);
    }
  }, [payloadFields]);

  const getViews = (payloadFields) => {
    const hSuccess = (data) => {
      setFilteredViewType(data.body[0].MaintStatus.split(""));
      setFieldReference(data.body[0].MaterialType);
      //getAutocompleteOptions(filteredViewType);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getViewForMaterialType?materialType=${payloadFields?.MatlType?.code}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getGITemplate = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_GI_MATERIAL_QUESTIONS",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          // "MDG_CONDITIONS.MDG_GI_MODULE": "CC SUNOCO",
          "MDG_CONDITIONS.MDG_GI_SCENARIO": "Create",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    // setIsLoading(true);
    // const formData = new FormData();

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        const questionsData =
          data?.data?.result[0]?.MDG_GI_QUESTIONS_ACTION_TYPE || [];
        setQuestions(questionsData);
        questionsData.map((question) => {
          const input = question?.MDG_GI_INPUT_OPTION;
        });

        questionsData.map((question) => {
          if (question?.MDG_GI_INPUT_OPTION === "Radio Button") {
            if (question?.MDG_GI_VISIBILITY === " Mandatory") {
              setMatRequiredFieldsGI(
                question.MDG_GI_QUESTION_TYPE.replaceAll("(", "")
                  .replaceAll(")", "")
                  .replaceAll("/", "")
                  .replaceAll("-", "")
                  .replaceAll(".", "")
                  .split(" ")
                  .join("")
              );
            }
            if (question?.MDG_GI_QUESTION_TYPE !== "Choose Priority Level") {
              dispatch(
                setSingleMaterialPayload({
                  keyName: question.MDG_GI_QUESTION_TYPE.replaceAll("(", "")
                    .replaceAll(")", "")
                    .replaceAll("/", "")
                    .replaceAll("-", "")
                    .replaceAll(".", "")
                    .split(" ")
                    .join(""),
                  data: "No",
                })
              );
            } else {
              dispatch(
                setSingleMaterialPayload({
                  keyName: question.MDG_GI_QUESTION_TYPE.replaceAll("(", "")
                    .replaceAll(")", "")
                    .replaceAll("/", "")
                    .replaceAll("-", "")
                    .replaceAll(".", "")
                    .split(" ")
                    .join(""),
                  data: "Medium",
                })
              );
            }
          } else {
            if (question?.MDG_GI_VISIBILITY === " Mandatory") {
              dispatch(
                setMatRequiredFieldsGI(
                  question.MDG_GI_QUESTION_TYPE.replaceAll("(", "")
                    .replaceAll(")", "")
                    .replaceAll("/", "")
                    .replaceAll("-", "")
                    .replaceAll(".", "")
                    .split(" ")
                    .join("")
                )
              );
            }
            dispatch(
              setSingleMaterialPayload({
                keyName: question.MDG_GI_QUESTION_TYPE.replaceAll("(", "")
                  .replaceAll(")", "")
                  .replaceAll("/", "")
                  .replaceAll("-", "")
                  .replaceAll(".", "")
                  .split(" ")
                  .join(""),
                data: "",
              })
            );
          }
        });
        dispatch(setGeneralInformation(questionsData));
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };
  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  // Optimized handleClose function
  const handleClose = () => setOpen(false);

  // Helper function to handle lookups
  const fetchOrgLookupData = (field) => {
    const endpoints = {
      Plant: "/getPlant",
      "Sales Organization": "/getSalesOrg",
    };
    const successHandler = (data) =>
      setDropDownData((prev) => ({ ...prev, [field]: data.body }));
    const errorHandler = (error) => console.error(error);

    doAjax(
      `/${destination_MaterialMgmt}/data${endpoints[field]}`,
      "get",
      successHandler,
      errorHandler
    );
  };

  // Function to handle fetching of dependent data based on Sales Organization selection
  const salesOrgDependentOrgElement = (salesOrg) => {
    if (displayedFields.includes("Distribution Channel")) {
      const hSuccess = (data) => setDistributionChannelLookupData(data?.body);
      const hError = (error) => console.error(error);

      doAjax(
        `/${destination_MaterialMgmt}/data/getDistrChan?salesOrg=${salesOrg.code}`,
        "get",
        hSuccess,
        hError
      );
    }
  };
  const dataToSend = {
    orgData: ["Plant", "Sales Organization", "Distribution Channel"].map(
      (key) => ({
        info: formData[key] || { code: "", desc: "" }, // Get the formData value or default if not present
        desc: key,
      })
    ),
    selectedViews: { selectedSections },
  };

  // Determine fields to display based on selected sections
  const handleProceed = () => {
    // Get selected fields based on selected sections

    const selectedFields = selectedSections.flatMap(
      (section) =>
        fieldListAccordingToView.find((item) => item.label === section)
          ?.fields || []
    );

    // Check if selectedFields has more than one element
    if (selectedFields.length > 1) {
      // Remove duplicates using Set and spread syntax
      const uniqueFields = [...new Set(selectedFields)];
      // Fetch lookup data for each unique field
      uniqueFields.forEach(fetchOrgLookupData);

      // Set the displayed fields and open the dialog
      setDisplayedFields(uniqueFields);
      setOpen(true);
    } else if (selectedFields.length < 1) {
      const attachView = {
        label: "Attachments & Comments",
        value: "attachments&comments",
      };
      const giView = {
        label: "General Information",
        value: "generalInformation",
      };

      // Using selectedSections as the variable holding selected views instead of undefined value
      const modifiedViews = [giView, ...selectedSections, attachView];

      // Update selectedViews with the array directly
      dataToSend.selectedViews = modifiedViews;

      // Dispatching the updated data
      dispatch(newMaterialData(dataToSend));

      // Navigating to the next page with the updated data
      dispatch(setTabValue(1));
      setIsSecondTabEnabled(true);
    }

    // }
  };

  // Update form data based on field selection
  const handleFieldChange = (fieldName, selectedValue) => {
    setFormData((prev) => ({ ...prev, [fieldName]: selectedValue }));

    // If Sales Organization changes, update dependent dropdowns
    if (fieldName === "Sales Organization") {
      salesOrgDependentOrgElement(selectedValue);
    }
  };
  const currentDate = `/Date(${Date.now()})/`;
  // Handle request header saving
  const handleButtonClick = () => {
    setDialogOpen(false);
    const epochTime = new Date(payloadFields?.ReqCreatedOn).getTime();
    const payload = {
      RequestId: requestHeaderData?.requestId
        ? requestHeaderData?.requestId
        : "",
      MatlType: payloadFields?.MatlType || "",
      ReqCreatedBy: userData?.user_id || "",
      ReqCreatedOn: epochTime ? `/Date(${epochTime})/` : currentDate,
      ReqUpdatedOn: epochTime ? `/Date(${epochTime})/` : currentDate,
      RequestType: payloadFields?.RequestType || "",
      RequestDesc: payloadFields?.RequestDesc || "",
      Division: payloadFields?.Division || "",
      RequestStatus: "DRAFT",
      RequestPriority: payloadFields?.RequestPriority || "",
      LeadingCat: payloadFields?.LeadingCat || "",
      FieldName: payloadFields?.FieldName?.join("$^$") || "",
      TemplateName: payloadFields?.TemplateName || "",
    };
    // const payload={
    //     RequestId: "",
    //     Region: "",
    //     MatlType: "",
    //     ReqCreatedBy: "P001344",
    //     ReqCreatedOn: "/Date(1743576164967)/",
    //     ReqUpdatedOn: "/Date(1743576164967)/",
    //     RequestType: "Create",
    //     RequestDesc: "dddf",
    //     Division: "00",
    //     RequestStatus: "DRAFT",
    //     RequestPriority: "Medium",
    //     LeadingCat: "Anesthesia/Pain Management",
    //     FieldName: "",
    //     TemplateName: ""
    // }

    const hSuccess = (data) => {
      const newRequestId = data?.body?.requestId;
      const requestPrefix = data?.body?.requestPrefix;
      setSuccessMsg(true);
      setMessageDialogMessage(
        `Request Header Created Successfully with request ID ${
          (payloadFields?.RequestType, data?.body?.requestId)
        }`
      );
      setAlertType("success");
      handleSnackBarOpen();
      dispatch(setRequestHeader(data.body));
      dispatch(setRequestIdHeader(newRequestId));
      dispatch(setRequestIdPrefix(requestPrefix));
      setIsAttachmentTabEnabled(true);
      setDisableProceed(false);
      dispatch(updateAllTabsData({}));
      dispatch(setTaskData({}));
      if (initialPayload?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
        setDialogOpen(true);
        return;
      }
      if (initialPayload?.RequestType === REQUEST_TYPE?.CHANGE) {
        const filteredConfig = filterConfigData(
          changeData?.["Config Data"],
          initialPayload?.FieldName,
          ["Material", "Plant"]
        );
        dispatch(
          changeTemplateDT({ ...changeData, "Config Data": filteredConfig })
        );
      }
      // if (initialPayload?.RequestType?.code === "Change") {
      setTimeout(() => {
        dispatch(setTabValue(1));
        setIsSecondTabEnabled(true);
      }, 2500);
    };
    const hError = (error) => {
      console.error("Error fetching data:", error);
      setSuccessMsg(true);
      setAlertType("error");
      setMessageDialogMessage("Error occured while saving Request Header");
      handleSnackBarOpen();
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/createRequestHeader`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const filterConfigData = (configData, fieldNames, additionalFields = []) => {
    const fieldsToKeep = new Set([...fieldNames, ...additionalFields]);

    const filteredData = {};
    Object.keys(configData).forEach((key) => {
      filteredData[key] = configData[key].filter((item) =>
        fieldsToKeep.has(item.fieldName)
      );
    });

    return filteredData;
  };

  // Helper function to select all sections
  const handleSelectAll = () =>
    setSelectedSectionsState(
      fieldListAccordingToView.map((field) => field.label)
    );

  function checkOrgValues(array) {
    return array.every((item) => {
      return item.info.code && item.info.desc;
    });
  }

  const handleCheckValidationError = () => {
    const hasEmptyOrgData = checkOrgValues(dataToSend.orgData);
    if (!hasEmptyOrgData) {
      setSuccessMsg(true);
      setAlertType("error");
      setMessageDialogMessage(`Please choose all mandatory fields`);
      handleSnackBarOpen();
    } else {
      const attachView = {
        label: "Attachments & Comments",
        value: "attachments&comments",
      };
      const giView = {
        label: "General Information",
        value: "generalInformation",
      };

      // Using selectedSections as the variable holding selected views instead of undefined value
      const modifiedViews = [giView, ...selectedSections, attachView];

      // Update selectedViews with the array directly
      dataToSend.selectedViews = modifiedViews;

      // Dispatching the updated data
      dispatch(newMaterialData(dataToSend));

      // Navigating to the next page with the updated data
      dispatch(setTabValue(1));
      setIsSecondTabEnabled(true);
    }
  };

  useEffect(() => {
    getRequestHeaderTemplatePc();
  }, [initialPayload?.RequestType]);

  useEffect(() => {
    if (initialPayload?.TemplateName) getChangeTemplate();
  }, [initialPayload?.TemplateName]);

  useEffect(() => {
    // getCreateTemplate();
    getGITemplate();
  }, []);
  return (
    <div>
      <Stack spacing={2}>
        {Object.entries(requestHeaderDetails).map(([key, fields]) => (
          <Grid
            item
            md={12}
            key={key}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              borderRadius: "8px",
              border: "1px solid #E0E0E0",
              mt: 0.25,
              boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              ...container_Padding,
            }}
          >
            <Typography
              sx={{
                fontSize: "12px",
                fontWeight: "700",
                paddingBottom: "10px",
              }}
            >
              {key}
            </Typography>
            <Box>
              <Grid container spacing={1}>
                {fields
                  .filter((field) => field.visibility !== "Hidden")
                  .sort((a, b) => a.sequenceNo - b.sequenceNo)
                  .map((innerItem) => (
                    <FilterField
                      isHeader={true}
                      key={innerItem.id}
                      field={innerItem}
                      dropDownData={dropDownData}
                      disabled={!ENABLE_STATUSES.includes(requestStatus)}
                    />
                  ))}
              </Grid>
            </Box>
            {!reqBench && (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  marginTop: "20px",
                }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  disabled={!enableSaveRequest}
                  onClick={handleButtonClick}
                >
                  Save Request Header
                </Button>
              </Box>
            )}
            <ToastContainer />
          </Grid>
        ))}

        <Dialog open={open} onClose={handleClose}>
          <DialogTitle sx={{ backgroundColor: "#EAE9FF" }}>
            Select Org Data
          </DialogTitle>
          <DialogContent>
            <Grid container columnSpacing={1}>
              {displayedFields.map((fieldName, index) => {
                return (
                  <Fragment key={index}>
                    <Grid item md={4}>
                      <Typography>
                        {fieldName}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                    </Grid>
                    <Grid item md={8}>
                      <Autocomplete
                        options={
                          fieldName === "Distribution Channel"
                            ? distributionChannelLookupData
                            : dropDownData[fieldName] || []
                        }
                        size="small"
                        getOptionLabel={(option) =>
                          `${option.code} - ${option.desc}`
                        }
                        renderOption={(props, option) => (
                          <li {...props}>
                            <Typography>{`${option.code} - ${option.desc}`}</Typography>
                          </li>
                        )}
                        onChange={(e, newValue) =>
                          handleFieldChange(fieldName, newValue)
                        }
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            placeholder={`Select ${fieldName}`}
                          />
                        )}
                      />
                    </Grid>
                  </Fragment>
                );
              })}
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose} variant="outlined">
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={() => {
                handleCheckValidationError();
              }}
            >
              Proceed
            </Button>
          </DialogActions>
        </Dialog>

        {dialogOpen && <RequestDetailsForChange />}

        {successMsg && (
          <ReusableSnackBar
            openSnackBar={openSnackbar}
            alertMsg={messageDialogMessage}
            alertType={alertType}
            handleSnackBarClose={handleSnackBarClose}
          />
        )}
      </Stack>
    </div>
  );
};

export default RequestHeaderPC;
