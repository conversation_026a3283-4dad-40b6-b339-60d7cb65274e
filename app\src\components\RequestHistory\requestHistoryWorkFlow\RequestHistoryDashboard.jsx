import { useState } from 'react';
import RequestHistoryDiagram from './RequestHistoryDiagram';
import RequestTaskModal from '../workFlow/TaskModal';

const RequestHistoryDashboard = ({ data }) => {
  const [selectedTask, setSelectedTask] = useState(null);

  return (
    <div>
      <RequestHistoryDiagram data={data} onTaskClick={setSelectedTask} />
      {selectedTask && (
        <RequestTaskModal task={selectedTask} onClose={() => setSelectedTask(null)} />
      )}
    </div>
  );
};

export default RequestHistoryDashboard;
