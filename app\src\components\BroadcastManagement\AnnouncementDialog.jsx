import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  <PERSON>alogContent,
  IconButton,
  Typography,
  Box,
  Chip,
  Card,
  CardMedia,
  Fade,
  Backdrop,
  Button,
  Divider
} from '@mui/material';
import {
  Close,
  CalendarToday,
  Person,
  Visibility,
  Launch,
  PlayCircleOutline,
  Image as ImageIcon,
  Link as LinkIcon
} from '@mui/icons-material';
import { colors } from '../../constant/colors';
import useLang from "@hooks/useLang";


// Media Viewer Component for enlarged images/videos
const MediaViewer = ({ open, onClose, media, type }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: colors.announcement.mediaViewerBackground,
          borderRadius: 2,
          maxHeight: '90vh'
        }
      }}
    >
      <Box sx={{ position: 'relative', p: 2 }}>
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: colors.basic.white,
            zIndex: 1,
            backgroundColor: colors.announcement.mediaOverlayBackground,
            '&:hover': { backgroundColor: colors.announcement.mediaOverlayHover }
          }}
        >
          <Close />
        </IconButton>
        
        {type === 'image' ? (
          <img
            src={media}
            alt="Enlarged view"
            style={{
              width: '100%',
              height: 'auto',
              maxHeight: '80vh',
              objectFit: 'contain',
              borderRadius: 8
            }}
          />
        ) : (
          <video
            src={media}
            controls
            autoPlay
            style={{
              width: '100%',
              height: 'auto',
              maxHeight: '80vh',
              borderRadius: 8
            }}
          />
        )}
      </Box>
    </Dialog>
  );
};

// Main Announcement Dialog Component
const AnnouncementDialog = ({ open, onClose, announcement }) => {
  const [mediaViewer, setMediaViewer] = useState({ open: false, media: null, type: null });
  const { t } = useLang();

  if (!announcement) return null;

  const handleMediaClick = (media, type) => {
    setMediaViewer({ open: true, media, type });
  };

  const handleExternalLinkClick = (url) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Mock data for demonstration - replace with actual media URLs
  const hasImage = announcement.fileName && announcement.fileType?.startsWith('image/');
  const hasVideo = announcement.fileName && announcement.fileType?.startsWith('video/');
  const hasExternalUrl = announcement.externalUrl && announcement.externalUrl.trim() !== '';

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            background: colors.announcement.dialogBackground,
            maxHeight: '100vh',
            width: "40%",
            border: `2px solid ${colors.announcement.dialogBorder}`,
          }
        }}
        TransitionComponent={Fade}
        TransitionProps={{ timeout: 300 }}
      >
        {/* Header */}
        <DialogTitle
          sx={{
            color: colors.basic.white,
            position: 'relative',
            pb: 3
          }}
        >
          <Box sx={{ pr: 6 }}>
            <Typography variant="h3" sx={{ fontWeight: 700, mb: 1, color: colors.announcement.headerText }}>
              {announcement.broadcastTitle}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap', mt: 2 }}>
              <Chip
                label={announcement.module}
                size="small"
                sx={{
                  background: colors.announcement.chipBackground,
                  color: colors.basic.white,
                  fontWeight: 600
                }}
              />
              <Chip
                label={announcement.status}
                size="small"
                sx={{
                  background: announcement.status === 'Active' ? colors.announcement.chipActive : colors.announcement.chipInactive,
                  color: colors.basic.white,
                  fontWeight: 600
                }}
              />
            </Box>
          </Box>
          
          <IconButton
            onClick={onClose}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: colors.basic.white,
              background: colors.announcement.headerBackground,
              '&:hover': { background: colors.announcement.headerHover }
            }}
          >
            <Close />
          </IconButton>
        </DialogTitle>

        <Divider sx={{ mx: 3 }}/>

        {/* Content */}
        <DialogContent sx={{ p: 0 }}>
          {/* Description Section */}
          <Box sx={{ p: 3 }}>
            <Typography
              variant="h6"
              sx={{ mb: 2, fontWeight: 600, color: colors.announcement.textPrimary }}
            >
              {t("Description")}
            </Typography>
            <Typography
              variant="body1"
              sx={{
                lineHeight: 1.7,
                color: colors.announcement.textSecondary,
                fontSize: '1rem',
                mb: 3,
                p: 2,
                backgroundColor: colors.announcement.descriptionBackground,
                borderRadius: 2,
                border: `1px solid ${colors.announcement.descriptionBorder}`
              }}
            >
              {announcement.description}
            </Typography>

            {/* Media Section */}
            {(hasImage || hasVideo) && (
              <>
                <Typography
                  variant="h6"
                  sx={{ mb: 2, fontWeight: 600, color: colors.announcement.textPrimary }}
                >
                  {t("Media Attachments")}
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
                  {hasImage && (
                    <Card
                      sx={{
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: colors.announcement.cardShadow
                        },
                        borderRadius: 2,
                        overflow: 'hidden',
                        position: 'relative'
                      }}
                      onClick={() => handleMediaClick(`/api/files/${announcement.fileName}`, 'image')}
                    >
                      <Box sx={{ position: 'relative', width: 200, height: 150 }}>
                        <CardMedia
                          component="div"
                          sx={{
                            height: '100%',
                            background: colors.announcement.mediaGradient,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          <ImageIcon sx={{ fontSize: 40, color: colors.basic.white }} />
                        </CardMedia>
                        <Box
                          sx={{
                            position: 'absolute',
                            bottom: 0,
                            left: 0,
                            right: 0,
                            background: colors.announcement.mediaOverlayGradient,
                            color: colors.basic.white,
                            p: 1,
                            textAlign: 'center'
                          }}
                        >
                          <Typography variant="caption" sx={{ fontWeight: 600 }}>
                            {announcement.fileName}
                          </Typography>
                        </Box>
                      </Box>
                    </Card>
                  )}

                  {hasVideo && (
                    <Card
                      sx={{
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'scale(1.05)',
                          boxShadow: colors.announcement.cardShadow
                        },
                        borderRadius: 2,
                        overflow: 'hidden',
                        position: 'relative'
                      }}
                      onClick={() => handleMediaClick(`/api/files/${announcement.fileName}`, 'video')}
                    >
                      <Box sx={{ position: 'relative', width: 200, height: 150 }}>
                        <CardMedia
                          component="div"
                          sx={{
                            height: '100%',
                            background: colors.announcement.mediaGradient,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          <PlayCircleOutline sx={{ fontSize: 50, color: colors.basic.white }} />
                        </CardMedia>
                        <Box
                          sx={{
                            position: 'absolute',
                            bottom: 0,
                            left: 0,
                            right: 0,
                            background: colors.announcement.mediaOverlayGradient,
                            color: colors.basic.white,
                            p: 1,
                            textAlign: 'center'
                          }}
                        >
                          <Typography variant="caption" sx={{ fontWeight: 600 }}>
                            {announcement.fileName}
                          </Typography>
                        </Box>
                      </Box>
                    </Card>
                  )}
                </Box>
              </>
            )}

            {/* External Link Section */}
            {hasExternalUrl && (
              <>
                <Typography
                  variant="h6"
                  sx={{ mb: 2, fontWeight: 600, color: colors.announcement.textPrimary }}
                >
                  {t("External Link")}
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<LinkIcon />}
                  endIcon={<Launch />}
                  onClick={() => handleExternalLinkClick(announcement.externalUrl)}
                  sx={{
                    mb: 3,
                    borderColor: colors.announcement.linkBorder,
                    color: colors.announcement.linkColor,
                    '&:hover': {
                      borderColor: colors.announcement.linkBorderHover,
                      backgroundColor: colors.announcement.linkBackgroundHover
                    },
                    borderRadius: 2,
                    textTransform: 'none',
                    fontSize: '0.95rem'
                  }}
                >
                  {t("Open External Link")}
                </Button>
              </>
            )}

            <Divider sx={{ my: 3 }} />

            {/* Meta Information */}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CalendarToday sx={{ fontSize: 18, color: colors.announcement.metaIconColor }} />
                <Typography variant="body2" color="text.secondary">
                  <strong>{t("Start Date")}:</strong> {formatDate(announcement.startDate)}
                </Typography>
              </Box>
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CalendarToday sx={{ fontSize: 18, color: colors.announcement.metaIconColor }} />
                <Typography variant="body2" color="text.secondary">
                  <strong>{t("End Date")}:</strong> {formatDate(announcement.endDate)}
                </Typography>
              </Box>

              {announcement.createdBy && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Person sx={{ fontSize: 18, color: colors.announcement.metaIconColor }} />
                  <Typography variant="body2" color="text.secondary">
                    <strong>{t("Created by")}:</strong> {announcement.createdBy}
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>
        </DialogContent>
      </Dialog>

      {/* Media Viewer Dialog */}
      <MediaViewer
        open={mediaViewer.open}
        onClose={() => setMediaViewer({ open: false, media: null, type: null })}
        media={mediaViewer.media}
        type={mediaViewer.type}
      />
    </>
  );
};

export default AnnouncementDialog;