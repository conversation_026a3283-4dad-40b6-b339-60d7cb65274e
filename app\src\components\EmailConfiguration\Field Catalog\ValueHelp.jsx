import React, { createRef } from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import "./CwMSFieldsDetails.css";
import fetchWrapper from "../utility/fetchWrapper";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import TableContainer from "@mui/material/TableContainer";
import CloseIcon from "@mui/icons-material/Close";
import AppBar from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import AddIcon from "@mui/icons-material/Add";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Input from "@mui/material/Input";
import TextField from "@mui/material/TextField";
import Stack from "@mui/material/Stack";
import Paper from "@mui/material/Paper";
import Button from "@mui/material/Button";
import Grid from "@mui/material/Grid";
import FormControl from "@mui/material/FormControl";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import MenuItem from "@mui/material/MenuItem";
import InputLabel from "@mui/material/InputLabel";
import Select from "@mui/material/Select";
import FormGroup from "@mui/material/FormGroup";
import FormControlLabel from "@mui/material/FormControlLabel";
import Switch from "@mui/material/Switch";
import FormLabel from "@mui/material/FormLabel";
import IconButton from "@mui/material/IconButton";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import RadioGroup from "@mui/material/RadioGroup";
import Radio from "@mui/material/Radio";
import Autocomplete from "@mui/material/Autocomplete";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import Slide from "@mui/material/Slide";
import InputBase from "@mui/material/OutlinedInput";
import InputAdornment from "@mui/material/InputAdornment";
import SvgIcon from "@mui/material/SvgIcon";
import ValueHelpDialog from "./valueHelpDialog";
import Composite from "./CompositeValueHelpDialog";
import SearchIcon from "@mui/icons-material/Search";
import Divider from "@mui/material/Divider";
import Tooltip from '@mui/material/Tooltip';

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

class ValueHelp extends React.Component {
  constructor(props) {
    super(props);
    this.valuehelp = React.createRef();
    let emptyRow = this.emptyRow();
    this.hiddenFileInput = createRef();
    this.state = {
      // value: "DataElement",
      // tableVisible: true,
      // valueHelpList: this.props.parentState.valueList,
      searched: "",
      valueHelpList: [],
      busyIndicator: true,
      compositeRow: {},
      dataElement: [],
      showVHTableDialog: false,
      valueList: [],
      hostdialogOpen: false,
      hostDetails: {
        dbName: "",
        url: "",
        driver: "",
        host: "",
        id: "",
        password: "",
        user: "",
        dbType: "",
      },
      showValueHelpDialog: false,
      selectedRow: props.selectedRow,
      constraints: [],
      createHost: {},
      fields: [],
      showPassword: false,
      apiType: [
        {
          name: "Rest",
        },
        {
          name: "Odata",
        },
      ],
      databaseType: [
        {
          type: "HANA",
          driver: "com.sap.db.jdbc.Driver",
        },
        {
          type: "MYSQL",
          driver: "com.mysql.cj.jdbc.Driver",
        },
        {
          type: "POSTGRESQL",
          driver: "org.postgresql.Driver",
        },
        {
          type: "MSSQL",
          driver: "com.microsoft.sqlserver.jdbc.SQLServerDriver",
        },
        {
          type: "ORACLE",
          driver: "oracle.jdbc.driver.OracleDriver",
        },
      ],
      constraintType: [
        {
          name: "PATH VARIABLE",
        },
        {
          name: "QUERY PARAMETER",
        },
        {
          name: "HEADER PARAMETER",
        },
      ],
      fileTypeDropDown: [
        {
          name: ".XLSX",
        },
        {
          name: ".CSV",
        },
        {
          name: ".XML",
        },
      ],
      valueHelpType: [
        {
          name: "Static",
          key: "VL",
        },
        {
          name: "API",
          key: "API",
        },
        {
          name: "Database",
          key: "DB",
        },
        {
          name: "File",
          key: "FILE",
        },
      ],
      destinations: [],
      dataTable: [],
      apiMetaData: {
        url: "",
        destination: "",
        apiType: "",
        resultPath: null,
        constraints: null,
      },
    };
  }
  // state = {
  //     tableVisible: true,

  // };

  emptyRow = () => {
    const selectedRow = {
      attributeId: "",
      createdBy: "",
      createdOn: null,
      dataType: "",
      description: "",
      destinations: null,
      isLookup: true,
      label: "",
      length: "",
      lookUpId: "",
      lookUpType: "",
      lookupConfig: null,
      name: "",
      source: "",
      updatedBy: "",
      updatedOn: null,
      propertyDto: {
        isMandatory: true,
        isVisible: true,
        isNullable: true,
        defaultValue: "abc",
        isFilterable: true,
        isSortable: true,
        isEditable: true,
      },
    };
    return selectedRow;
  };
  createNewField = (event, newValue) => {
    this.setState({
      ...this.state,
      tableVisible: false,
    });
  };
  changePasswordVisibility = () => {
    this.setState({
      ...this.state,
      showPassword: !this.state.showPassword,
    });
  };
  handleSubmit = (evt) => {
    let a = evt;
  };
  editFields = (row) => {
    this.setState({
      ...this.state,
      tableVisible: false,
      selectedRow: row,
    });
  };

  onValueHelpType = (event) => {
    let value = event.target.value;
    this.props.lookupType = value;
  };

  staticValuesHelpList = (data) => {
    if (data === undefined || data === null) return this.props.parentState.valueList;
    else if (data.length >= 0) {
      return data;
    }
  };

  fnChangeHostDetails(evt) {
    if (evt.target.name !== "dbType") {
      this.setState({
        ...this.state,
        hostDetails: {
          ...this.state.hostDetails,
          [evt.target.name]: evt.target.value,
        },
      });
    } else {
      let temp = this.state.databaseType.filter((ele) => ele.type === evt.target.value);
      this.setState({
        ...this.state,
        hostDetails: {
          ...this.state.hostDetails,
          dbType: temp[0].type,
          driver: temp[0].driver,
        },
      });
    }
  }
  fnComposite = (row, index) => {
    let type = row.lookupType;

    if (this.props.parentState.lookupType === "COMPOSITE") {
      let url = "/WorkUtilsServices/v1/lapi?lookupId=" + row.referenceId;
      fetchWrapper(url, { headers: { Authorization: this.props.authorization } }, this.props.destinations)
        .then((res) => res.json())
        .then((result) => {
          // destination = result.data ? result.data : [];
          var valueList = [];
          if (row.lookupType === "API" || row.lookupType === "DB") {
            // this.getAttributeList(this.props.selectedRow.metaData.applicationId);
            let url = "/WorkUtilsServices/v1/lapi/destination";
            fetchWrapper(url, { headers: { Authorization: this.props.authorization } }, this.props.destinations)
              .then((res) => res.json())
              .then((result) => {
                // destination = result.data ? result.data : [];
                this.setState({
                  ...this.state,
                  destinations: result.data ? result.data : [],
                  //  fields: [],
                  //  constraints:[]
                });
              });
          }
          if (row.lookupType === "DB") {
            let url = "/WorkUtilsServices/v1/lapi/databaseValuehelp";
            fetchWrapper(url, { headers: { Authorization: this.props.authorization } }, this.props.destinations)
              .then((res) => res.json())
              .then((lookupTable) => {
                // destination = result.data ? result.data : [];
                this.setState({
                  ...this.state,
                  dataTable: lookupTable.data ? lookupTable.data : [],
                  //  fields: [],
                  //  constraints:[]
                });
              });
          }
          if (result.data) {
            if (result.data.valueList) {
              this.setState({
                ...this.state,
                valueList: result.data.valueList,
                busyIndicator: false,
                // fields: fields
              });
            } else if (result.data.fileMetadata) {
              this.setState(
                {
                  ...this.state,
                  sheets: result.data.fileMetadata.properties.sheets,
                  busyIndicator: false,
                },
                () => {
                  var sheets = this.state.sheets;
                  sheets.filter(function (element, index) {
                    var num = element.columnStart + 1;
                    let s = "",
                      t;

                    while (num > 0) {
                      t = (num - 1) % 26;
                      s = String.fromCharCode(65 + t) + s;
                      num = ((num - t) / 26) | 0;
                    }
                    element.columnStart = s || undefined;
                  });
                }
              );

              this.setState({
                ...this.state,

                fileMetadata: result.data.fileMetadata,
                busyIndicator: false,
                fields: result.data.fileMetadata.fields ? result.data.fileMetadata.fields : [],
                constraints: result.data.fileMetadata.constraints ? result.data.fileMetadata.constraints : [],
              });
              console.log(this.state.fileMetadata, "filemetadata");
            } else if (result.data.apiMetadata) {
              var apiMetadata = {
                url: result.data.apiMetadata.url,
                destination: result.data.apiMetadata.destination,
                apiType: result.data.apiMetadata.apiType,
                resultPath: result.data.apiMetadata.resultPath,
                //"constraints": null,
              };
              this.setState({
                ...this.state,
                busyIndicator: false,
                apiMetaData: apiMetadata,
                tempdata: apiMetadata,
                constraints: result.data.apiMetadata.constraints ? result.data.apiMetadata.constraints : [],
                fields: result.data.apiMetadata.fields ? result.data.apiMetadata.fields : [],
              });
            } else if (result.data.dbMetadata) {
              this.setState({
                ...this.state,
                busyIndicator: false,
                dbMetaData: result.data.dbMetadata,
                //tempdata: apiMetadata,
                constraints: result.data.dbMetadata.constraints ? result.data.dbMetadata.constraints : [],
                fields: result.data.dbMetadata.fields ? result.data.dbMetadata.fields : [],
              });
            }
          } else {
            this.setState({
              ...this.state,
              //destinations: result.data ? result.data : [],
              fields: [],
              constraints: [],
            });
          }
        });
    }
    //setTimeout(() => {

    this.setState({
      ...this.state,
      showVHTableDialog: true,
      index: index,
      lookupTypeC: type,
      compositeRow: row,
      // selectedRow: row
    });

    //}, 3000)

    // else if(this.props.parentState.lookupType==="COMPOSITE"){
    //
    //
    // }
    // this.getAttributeList(this.props.parentState.selectedApplication);
  };
  getAttributeList = (appId) => {
    let url;

    url = "/WorkUtilsServices/v1/attribute-master?applicationId=" + appId;
    fetchWrapper(url, { headers: { Authorization: this.props.authorization } }, this.props.destinations)
      .then((res) => res.json())
      .then((result) => {
        // this.setState({
        //     ...this.state,
        //     tempArr: result.output
        // });

        this.setState({
          ...this.state,
          selectedApplication: appId,
          fieldCatalogArray: result.data ? result.data : [],
        });
      });
  };
  setHost = (val) => {
    this.setState({
      ...this.state,
      hostdialogOpen: val,
    });
  };
  // discardFieldCatalog(){
  //
  //     this.setState({
  //         ...this.state,
  //         //existing: false,
  //         tableVisible: true
  //         //selectedRow: emptyRow
  //     }, ()=>console.log(this.state.tableVisible,"discard"));
  // }
  closeDialog = (action) => {
    if (action === "DISCARD") {
      //    this.discardFieldCatalog();
    } else if (action === "SUBMIT") {
      //   this.onSaveMappingRoles();
    } else if (action === "SAVE") {
      //   this.onSaveMappingRolesDraft();
      this.onSaveFieldCatalog();
    }

    this.setState({
      ...this.state,
      showVHTableDialog: false,
    });
  };
  onLookupType = (evnt, keyProps) => {
    this.setState({
      ...this.state,
      lookupType: keyProps.props.value,
    });
  };
  getData = () => {
    this.props.getData();
  };

  onChangeSearchQuery = (evt) => {
    const valueHelpList = this.props.parentState.valueList.filter((e) => e.key.toLowerCase().includes(evt.target?.value?.toLowerCase()) || e.value.toLowerCase().includes(evt.target?.value?.toLowerCase()) || e.additionalValue.toLowerCase().includes(evt.target?.value?.toLowerCase()));

    // this.staticValuesHelpList(valueHelpList);
    this.setState({
      ...this.state,
      valueHelpList: valueHelpList,
      searched: evt.target.value,
    });
  };

  render() {
    return (
      <div style={{ background: "white", borderRadius: "0.5rem" }}>
        <FormControl component="fieldset">
          <div style={{ display: "flex" }}>
            <FormLabel style={{ padding: "0.5rem", fontSize: "0.875rem" }}>Choose the Value Help Type |</FormLabel>
            <RadioGroup row name="row-radio-buttons-group" value={this.props.parentState.lookupType} style={{ marginLeft: "1rem" }} onChange={(e, newvalue) => this.props.handleChangeValueHelpType(e, newvalue)}>
              <FormControlLabel className="styleWURadioButtonText" value="VL" control={<Radio color="primary" className="styleWURadioButton" />} label="Static Values Help" />
              <FormControlLabel className="styleWURadioButtonText" value="API" control={<Radio color="primary" className="styleWURadioButton" disabled={this.props.editLookUpData} />} label="Api Based" />
              <FormControlLabel className="styleWURadioButtonText" value="DB" control={<Radio color="primary" className="styleWURadioButton" disabled={this.props.editLookUpData} />} label="Database Based" />
              {/* aman */}
              {/* {!this.props.editLookUpData && (
                <>
                  <FormControlLabel className="styleWURadioButtonText" value="API" control={<Radio color="primary" className="styleWURadioButton" />} label="Api Based" />
                  <FormControlLabel className="styleWURadioButtonText" value="DB" control={<Radio color="primary" className="styleWURadioButton" />} label="Database Based" />
                </>
              )} */}
              {/* <FormControlLabel className='styleWURadioButtonText' value="FILE" control={<Radio color="primary" className='styleWURadioButton'/>} label="File Type" />
                            <FormControlLabel className='styleWURadioButtonText' value="COMPOSITE" control={<Radio color="primary" className='styleWURadioButton'/>} label="Composite" /> */}
            </RadioGroup>
          </div>
        </FormControl>
        <Box>
          {(this.props.parentState.lookupType === "API" || this.props.parentState.lookupType === "DB") && (
            <div>
              <Grid container spacing={3} hSpacing="2rem">
                <Grid item xs={12} sm={3} md={3} hidden={this.props.parentState.lookupType !== "API"}>
                  <div>
                    <p style={{ margin: "0.5rem" }}>{this.props.translation.DESTINATION.longDescription}</p>
                    <Autocomplete
                      sx={{ width: 220, height: "2.4rem" }}
                      // sx={{ width: "12rem", height: "1.7rem", fontSize: "0.8rem", padding: "5px 5px" }}
                      name="destination"
                      id="destination"
                      className="styleAutoComplete"
                      size="small"
                      // value={this.props.parentState.apiMetadata.destination}
                      inputValue={this.props.parentState.apiMetadata.destination}
                      options={this.props.parentState.destinations}
                      getOptionLabel={(option) => option.name}
                      // renderOption={(option) => option.name}
                      onChange={(evt, val) => this.props.inputChangeHandler1(evt, val)}
                      renderInput={(params) => <TextField {...params} variant="outlined" value={this.props.parentState.apiMetadata.destination} />}
                    />
                  </div>
                </Grid>
                <Grid item xs={12} sm={3} md={3} hidden={this.props.parentState.lookupType !== "DB"}>
                  <div>
                    <Stack direction="row" justifyContent="space-between" alignItems="center" width="14rem">
                      <p style={{ margin: "0.2rem" }}>{this.props.translation.HOST.longDescription}</p>
                      <span className="iconBtn">
                        <Tooltip title="Add Host" placement="right">
                          <IconButton size="small" onClick={() => this.setHost(true)}>
                            <AddIcon fontSize="small" color="primary" />
                          </IconButton>
                        </Tooltip>
                      </span>
                    </Stack>
                    <FormControl style={{ width: "16rem" }} variant="outlined" size="small">
                      <Select
                        sx={{ width: 220, height: "1.8rem" }}
                        name="hosts"
                        labelId="demo-simple-select-outlined-label"
                        id="demo-simple-select-outlined"
                        className="styleDTname"
                        value={this.props.parentState?.dbMetadata?.hosts ? this.props.parentState?.dbMetadata?.hosts : ""}
                        // renderOption={(option) => option.name}
                        onChange={(evt) => this.props.dbInputChangeHandler(evt)}
                      >
                        {this.props.parentState.hosts.map((option) => (
                          <MenuItem
                            value={option.host}
                            key={option.host}
                            // onClick={() =>
                            //     this.applicationChangeHandler(option.id, index)

                            // }
                            // style={{ width: 30 }}
                          >
                            {option.host}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </div>
                </Grid>

                <Grid item xs={12} sm={3} md={3} hidden={this.props.parentState.lookupType !== "DB"}>
                  <div>
                    <Stack>
                      <p style={{ margin: "0.2rem" }}>{this.props.translation.DATA_TABLE.longDescription}</p>
                    </Stack>
                    <FormControl style={{ width: "16rem" }} variant="outlined" size="small">
                      <Select
                        sx={{ width: 220, height: "1.8rem" }}
                        name="lookupTable"
                        labelId="demo-simple-select-outlined-label"
                        id="demo-simple-select-outlined"
                        required
                        className="styleSelectFields"
                        value={this.props.parentState?.dbMetadata?.lookupTable ? this.props.parentState?.dbMetadata?.lookupTable : ""}

                        // renderOption={(option) => option.name}
                      >
                        {this.props.parentState.dataTable.map((option) => (
                          <MenuItem
                            value={option.lookupTableName}
                            key={option.lookupTableName}
                            onClick={(evt) => this.props.inputChangeHandler2(evt, option)}
                            // onClick={() =>
                            //     this.applicationChangeHandler(option.id, index)

                            // }
                            // style={{ width: 30 }}
                          >
                            {option.lookupTableName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </div>
                  {/* <Input
                            required
                            // hidden={this.props.parentState.lookupType !== "DB"}
                            id="filled-disabled"
                            label={this.props.translation.DATA_TABLE.longDescription}
                            name="lookupTable"
                            value={this.props.parentState.dbMetadata.lookupTable}
                            variant="outlined"
                            //style={{ marginLeft: 30 }}
                            size="small"
                            onChange={(evt) => this.props.inputChangeHandler1(evt)}
                        // className="customInput2" 
                        /> */}
                </Grid>
                {/* <Grid item xs={12} sm={3} md={3} hidden={this.props.parentState.lookupType !== "DB"}>
                  <span className="iconBtn">
                    <Button
                      disableRipple
                      size="small"
                      variant="outlined"
                      color="primary"
                      style={{ textTransform: "none" }}
                      onClick={() => this.setHost(true)}
                      // className="styledWTPrimaryButton"
                      // disabled={this.props.constraints.length !== 0 ? true : false}
                      //className={classes.buttonAdd}
                    >
                      <AddIcon fontSize="small" /> Add
                    </Button>
                  </span>
                </Grid> */}
                <Grid item xs={12} sm={3} md={3} hidden={this.props.parentState.lookupType === "DB"}>
                  <div>
                    <p style={{ margin: "0.5rem" }}>{this.props.translation.VALUE_HELP_URL.longDescription}</p>
                    <TextField
                      required
                      className="styleDTname"
                      id="outlined-basic"
                      // label={this.props.translation.VALUE_HELP_URL.longDescription}
                      name="url"
                      disable
                      underline
                      value={this.props.parentState.apiMetadata.url}
                      variant="outlined"
                      sx={{ width: "70%" }}
                      //style={{ marginLeft: 30 }}
                      size="small"
                      onChange={(evt) => this.props.inputChangeHandler1(evt)}
                      // className="customInput2"
                    />
                  </div>
                </Grid>

                <Grid item xs={12} sm={3} md={3} hidden={this.props.parentState.lookupType === "DB"}>
                  <div>
                    <p style={{ margin: "0.5rem" }}>{this.props.translation.API_TYPE.longDescription}</p>
                    <FormControl style={{ width: 220 }} variant="outlined" size="small">
                      {/* <InputLabel id="demo-simple-select-outlined-label" required>{this.props.translation.API_TYPE.longDescription}</InputLabel> */}
                      <Select
                        // sx={{ width: "12rem", padding: "0px 0px 0px 0px" }}
                        sx={{ width: 220, height: "1.8rem" }}
                        labelId="demo-simple-select-outlined-label"
                        id="demo-simple-select-outlined"
                        // label={this.props.translation.API_TYPE.longDescription}
                        value={this.props.parentState.apiMetadata.apiType}
                        // size="small"
                        name="apiType"
                        className="styleSelectFields"
                        onChange={(evt) => this.props.inputChangeHandler1(evt)}
                        // hidden={true}
                      >
                        {this.state.apiType.map((option) => (
                          <MenuItem
                            value={option.name}
                            key={option.name}
                            // onClick={() =>
                            //     this.applicationChangeHandler(option.id, index)

                            // }
                            // style={{ width: 30 }}
                          >
                            {option.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </div>
                </Grid>
                <Grid item xs={12} sm={3} md={3} hidden={this.props.parentState.lookupType === "DB"}>
                  <div>
                    <p style={{ margin: "0.5rem" }}>{this.props.translation.RESPONSE_PATH.longDescription}</p>
                    <TextField
                      id="outlined-basic"
                      className="styleDTname"
                      // label={this.props.translation.RESPONSE_PATH.longDescription}
                      name="resultPath"
                      value={this.props.parentState.apiMetadata.resultPath}
                      variant="outlined"
                      // style={{ border: "2px solid #d4d5d6", borderRadius: "5px", height: "2rem" }}
                      sx={{ width: "70%" }}
                      size="small"
                      onChange={(evt) => this.props.inputChangeHandler1(evt)}
                      // className="customInput2"
                    />
                  </div>
                </Grid>
              </Grid>
              <Divider style={{ minHeight: "2px", margin: "0.5rem 0", width: "100%" }} />
              {/* height and overflowY were removed from here */}
              <div>
                <span className="iconBtn" style={{ float: "right", marginTop: "0.5rem", marginRight: "1rem", marginBottom: "0.5rem" }}>
                  <Button disableRipple size="small" variant="outlined" color="primary" sx={{ marginLeft: 10, textTransform: "none" }} onClick={() => this.props.addNewConstraint()}>
                    <AddIcon fontSize="small" /> Add
                  </Button>
                </span>
                <TableContainer component={Paper}  className="styleRMStableContainerList" style={{ width: "99%" }}>
                  <Table size="small" aria-label="a dense table" name="constraintTable" stickyHeader>
                    <TableHead>
                      <TableRow className="styleHeaderCellList" >
                        <TableCell width="10%" style={{ fontWeight: 700 }}>
                          {this.props.translation.CONSTRAINT.longDescription}
                        </TableCell>
                        <TableCell width="10%" style={{ fontWeight: 700 }}>
                          {this.props.translation.DISPLAY_NAME.longDescription}
                        </TableCell>
                        <TableCell width="10%" style={{ fontWeight: 700 }}>
                          {this.props.translation.MAPPED_NAME.longDescription}
                        </TableCell>
                        <TableCell width="10%" style={{ fontWeight: 700 }}>
                          {this.props.translation.CONSTRAINT_TYPE.longDescription}
                        </TableCell>
                        <TableCell width="10%" style={{ fontWeight: 700 }}>
                          {this.props.translation.OPERATOR.longDescription}
                        </TableCell>
                        <TableCell width="10%" style={{ fontWeight: 700 }}>
                          {this.props.translation.VALUE.longDescription}
                        </TableCell>
                        <TableCell width="10%" style={{ fontWeight: 700 }}>Actions</TableCell>
                        {/* <TableCell width="10%" style={{ fontWeight: 700 }}></TableCell> */}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {this.props.constraints.length !== 0 &&
                        this.props.constraints.map((row, index) => {
                          return (
                            <TableRow key={row.id} className="styleDataCell">
                              {/* <TableCell >{row.application}</TableCell> */}

                              <TableCell>
                                <FormControl
                                  // style={{ width: 220 }}
                                  variant="outlined"
                                  size="small"
                                >
                                  <TextField
                                    name="constraintColumn"
                                    id="constraintTable"
                                    value={row.constraintColumn}
                                    variant="outlined"
                                    // disabled
                                    className="customInputpackage"
                                    InputProps={{
                                      endAdornment: (
                                        <InputAdornment position="end">
                                          <IconButton
                                            onClick={(evt) => this.props.fnAttrValueHelp(index, evt, "constraintTable")}
                                            // onMouseDown={handleMouseDownPassword}
                                            edge="end"
                                          >
                                            <SvgIcon>
                                              <path d="M17.391,2.406H7.266c-0.232,0-0.422,0.19-0.422,0.422v3.797H3.047c-0.232,0-0.422,0.19-0.422,0.422v10.125c0,0.232,0.19,0.422,0.422,0.422h10.125c0.231,0,0.422-0.189,0.422-0.422v-3.797h3.797c0.232,0,0.422-0.19,0.422-0.422V2.828C17.812,2.596,17.623,2.406,17.391,2.406 M12.749,16.75h-9.28V7.469h3.375v5.484c0,0.231,0.19,0.422,0.422,0.422h5.483V16.75zM16.969,12.531H7.688V3.25h9.281V12.531z"></path>
                                            </SvgIcon>
                                          </IconButton>
                                        </InputAdornment>
                                      ),
                                    }}
                                  ></TextField>
                                </FormControl>
                              </TableCell>
                              <TableCell>
                                <TextField
                                  required
                                  id="constraintTable"
                                  disabled
                                  name="constraintName"
                                  value={row.constraintName}
                                  // className="customInputpackage"
                                  variant="outlined"
                                  // style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                  className="styleDTname"
                                  sx={{ width: "100%" }}
                                  //style={{ marginLeft: 30 }}
                                  size="small"
                                  onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                  // className="customInput2"
                                />
                              </TableCell>

                              <TableCell>
                                {this.props.parentState.lookupType !== "DB" ? (
                                  <TextField
                                    required
                                    id="constraintTable"
                                    className="styleDTname"
                                    sx={{ width: "100%" }}
                                    name="mappedName"
                                    value={row.mappedName}
                                    variant="outlined"
                                    // style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                    //style={{ marginLeft: 30 }}
                                    size="small"
                                    onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                    // className="customInput2"
                                  />
                                ) : (
                                  <FormControl style={{ width: 220 }} variant="outlined" size="small">
                                    <Select
                                      labelId="demo-simple-select-outlined-label"
                                      id="demo-simple-select-outlined"
                                      style={{ width: "16rem", height: "1.5rem" }}
                                      name="mappedName"
                                      required
                                      // value={this.props.parentState.apiMetadata.destination}
                                      value={row.mappedName ? row.mappedName : ""}
                                      // renderOption={(option) => option.name}

                                      onChange={(evt) => this.props.valueHelpTableInputs(evt, index, "constraintTable")}
                                    >
                                      {this.props.parentState.mappedNameList.map((option) => (
                                        <MenuItem
                                          value={option.columnName}
                                          key={option.columnName}
                                          style={this.props.parentState.uniqueMappedNameList.findIndex((ele) => ele.columnName === option.columnName) !== -1 ? { display: "none" } : {}}

                                          // onClick={() =>
                                          //     this.applicationChangeHandler(option.id, index)

                                          // }
                                          // style={{ width: 30 }}
                                        >
                                          {option.columnName}
                                        </MenuItem>
                                      ))}
                                    </Select>
                                  </FormControl>
                                )}
                              </TableCell>

                              {/* <TableCell >{row.permissionType}</TableCell> */}
                              <TableCell>
                                {/* <TextField
                                  required
                                  id="constraintTable"
                                  className="styleDTname"
                                  sx={{ width: "100%" }}
                                  name="constraintType"
                                  value={row.constraintType}
                                  variant="outlined"
                                  // style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                  //style={{ marginLeft: 30 }}
                                  size="small"
                                  onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                  // className="customInput2"
                                /> */}
                                <FormControl style={{ width: 220 }} variant="outlined" size="small">
                                    <Select
                                      id="constraintTable"
                                      className="styleDTname"
                                      sx={{ width: "100%" }}
                                      name="constraintType"
                                      value={row.constraintType ? row.constraintType : ""}
                                      variant="outlined"
                                      // style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                      //style={{ marginLeft: 30 }}
                                      size="small"
                                      onChange={(evt) => this.props.valueHelpTableInputs(evt, index, "constraintTable")}
                                      // className="customInput2"
                                    >
                                      {["Query Parameter", "Path Variable"].map((option) => (
                                        <MenuItem
                                          value={option}
                                        >
                                          {option}
                                        </MenuItem>
                                      ))}
                                    </Select>
                                  </FormControl>
                              </TableCell>
                              <TableCell>
                                <TextField
                                  className="styleDTname"
                                  sx={{ width: "100%" }}
                                  required
                                  id="constraintTable"
                                  disabled
                                  name="constraintOperator"
                                  value={row.constraintOperator}
                                  variant="outlined"
                                  // style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                  //style={{ marginLeft: 30 }}
                                  size="small"
                                  onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                  // className="customInput2"
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  required
                                  id="constraintTable"
                                  className="styleDTname"
                                  sx={{ width: "100%" }}
                                  name="constraintValue"
                                  value={row.constraintValue}
                                  variant="outlined"
                                  // style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                  //style={{ marginLeft: 30 }}
                                  size="small"
                                  onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                  // className="customInput2"
                                />
                              </TableCell>
                              <TableCell>
                                {/* <Tooltip title= 'Edit'> */}
                                <IconButton aria-label="Edit" onClick={() => this.props.deleteConstraintRows(row.id, index)}>
                                  <DeleteOutlineIcon fontSize="small" style={{ color: "red" }} />
                                </IconButton>
                                {/* </Tooltip> */}
                              </TableCell>
                            </TableRow>
                          );
                        })}
                    </TableBody>
                  </Table>
                </TableContainer>
                {/* button's new position */}
                {/* <span className="iconBtn" style={{ float: "right", marginTop: "0.5rem", marginRight: "1rem", marginBottom: "0.5rem" }}>
                  <Button disableRipple size="small" variant="outlined" color="primary" sx={{ marginLeft: 10, textTransform: "none" }} onClick={() => this.props.addNewConstraint()} >
                    <AddIcon fontSize="small" /> Add New
                  </Button>
                </span> */}
                {/* button's new position */}
                <span className="iconBtn" style={{ float: "right", marginTop: "0.5rem", marginRight: "1rem", marginBottom: "0.5rem" }}>
                  <Button
                    size="small"
                    disableRipple
                    variant="outlined"
                    color="primary"
                    style={{ marginLeft: 10, textTransform: "none" }}
                    onClick={() => this.props.addNewFields()}
                    // className="styledWTPrimaryButton"
                    //className={classes.buttonAdd}
                  >
                    <AddIcon fontSize="small" /> Add
                  </Button>
                </span>
                <TableContainer component={Paper} style={{ width: "99%", height: "100%", overflowY: "none" }} className="styleRMStableContainerList">
                  <Table size="small" aria-label="a dense table" name="fieldsTable">
                    <TableHead>
                      <TableRow  className='styleHeaderCellList'>
                        <TableCell width="10%" style={{ fontWeight: 700 }}>
                          {this.props.translation.FIELDS.longDescription}
                        </TableCell>
                        <TableCell width="10%" style={{ fontWeight: 700 }}>
                          {this.props.translation.DISPLAY_NAME.longDescription}
                        </TableCell>
                        <TableCell width="10%" style={{ fontWeight: 700 }}>
                          {this.props.translation.MAPPED_NAME.longDescription}
                        </TableCell>
                        <TableCell width="10%" style={{ fontWeight: 700 }}>
                          {this.props.translation.SEARCHABLE.longDescription}
                        </TableCell>
                        <TableCell width="10%" style={{ fontWeight: 700 }}>
                          {this.props.translation.UI_DISPLAY_NAME.longDescription}
                        </TableCell>

                        <TableCell width="10%" style={{ fontWeight: 700 }}>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {this.props.fields.map((row, index) => {
                        return (
                          <TableRow key={row.id} className="styleDataCell">
                            {/* <TableCell >{row.application}</TableCell> */}

                            <TableCell>
                              <FormControl
                                // style={{ width: 220 }}
                                variant="outlined"
                                size="small"
                              >
                                <TextField
                                  name="columnName"
                                  id="fieldsTable"
                                  value={row.columnName}
                                  variant="outlined"
                                  // disabled
                                  className="styleDTname"
                                  sx={{ width: "100%" }}
                                  disabled={!index}
                                  InputProps={{
                                    readOnly: true,
                                    endAdornment: (
                                      <InputAdornment position="end">
                                        <IconButton
                                          disabled={!index}
                                          onClick={(evt) => this.props.fnAttrValueHelp(index, evt, "fieldsTable")}
                                          // onMouseDown={handleMouseDownPassword}
                                          edge="end"
                                        >
                                          <SvgIcon>
                                            <path d="M17.391,2.406H7.266c-0.232,0-0.422,0.19-0.422,0.422v3.797H3.047c-0.232,0-0.422,0.19-0.422,0.422v10.125c0,0.232,0.19,0.422,0.422,0.422h10.125c0.231,0,0.422-0.189,0.422-0.422v-3.797h3.797c0.232,0,0.422-0.19,0.422-0.422V2.828C17.812,2.596,17.623,2.406,17.391,2.406 M12.749,16.75h-9.28V7.469h3.375v5.484c0,0.231,0.19,0.422,0.422,0.422h5.483V16.75zM16.969,12.531H7.688V3.25h9.281V12.531z"></path>
                                          </SvgIcon>
                                        </IconButton>
                                      </InputAdornment>
                                    ),
                                  }}
                                ></TextField>
                              </FormControl>
                            </TableCell>
                            <TableCell>
                              <TextField
                                required
                                disabled={!index}
                                id="fieldsTable"
                                className="styleDTname"
                                sx={{ width: "100%" }}
                                name="displayName"
                                value={row.displayName}
                                variant="outlined"
                                // style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                //style={{ marginLeft: 30 }}
                                size="small"
                                onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                // className="customInput2"
                              />
                            </TableCell>

                            {/* <TableCell >{row.permissionType}</TableCell> */}
                            <TableCell>
                              {this.props.parentState.lookupType !== "DB" ? (
                                <TextField
                                  required
                                  id="fieldsTable"
                                  className="styleDTname"
                                  sx={{ width: "100%" }}
                                  name="mappedName"
                                  value={row.mappedName}
                                  variant="outlined"
                                  // style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                  //style={{ marginLeft: 30 }}
                                  size="small"
                                  onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                  // className="customInput2"
                                />
                              ) : (
                                <FormControl style={{ width: 220 }} variant="outlined" size="small">
                                  <Select
                                    labelId="demo-simple-select-outlined-label"
                                    id="demo-simple-select-outlined"
                                    style={{ width: "16rem", height: "1.5rem" }}
                                    name="mappedName"
                                    required
                                    // value={this.props.parentState.apiMetadata.destination}
                                    value={row.mappedName ? row.mappedName : ""}
                                    // renderOption={(option) => option.name}

                                    onChange={(evt) => this.props.valueHelpTableInputs(evt, index, "fieldsTable")}
                                  >
                                    {this.props.parentState.mappedNameList.map((option) => (
                                      <MenuItem
                                        value={option.columnName}
                                        key={option.columnName}
                                        style={this.props.parentState.uniqueMappedNameList.findIndex((ele) => ele.columnName === option.columnName) !== -1 ? { display: "none" } : {}}

                                        // onClick={() =>
                                        //     this.applicationChangeHandler(option.id, index)

                                        // }
                                        // style={{ width: 30 }}
                                      >
                                        {option.columnName}
                                      </MenuItem>
                                    ))}
                                  </Select>
                                </FormControl>
                              )}
                            </TableCell>
                            <TableCell>
                              <FormControl component="fieldset" style={{ marginLeft: -10, width: "100%" }}>
                                <FormGroup aria-label="position" row>
                                  <FormControlLabel
                                    required
                                    className="customInputpackage"
                                    // id="fieldsTable"
                                    size="small"
                                    value={row.searchable}
                                    //checked={this.state.obj.active}
                                    name="searchable"
                                    onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                    //value={row.active}
                                    labelPlacement="start"
                                    control={<Switch disabled={!index} color="primary" checked={row.searchable} id="fieldsTable" />}
                                  />
                                </FormGroup>
                              </FormControl>
                            </TableCell>
                            <TableCell>
                              <RadioGroup row style={{ marginLeft: "1rem" }} name="isDisplayName">
                                <Radio id="fieldsTable" checked={row.isDisplayName} onChange={(evt) => this.props.valueHelpTableInputs(evt, index)} />
                              </RadioGroup>
                            </TableCell>
                            <TableCell>
                              {/* <Tooltip title= 'Edit'> */}
                              <IconButton
                                hidden={!index}
                                aria-label="Edit"
                                onClick={() => this.props.deleteFieldRows(row.id, index)}
                                disabled={!index}
                                InputProps={{
                                  readOnly: true,
                                }}
                              >
                                <DeleteOutlineIcon fontSize="small" style={ index > 0 ? { color: "red" } : { color: "rgb(243, 107, 107)" } } />
                              </IconButton>
                              {/* </Tooltip> */}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
                {/* button's new position */}
                {/* <span className="iconBtn" style={{ float: "right", marginTop: "0.5rem", marginRight: "1rem", marginBottom: "0.5rem" }}>
                  <Button
                    size="small"
                    disableRipple
                    variant="outlined"
                    color="primary"
                    style={{ marginLeft: 10, textTransform: "none" }}
                    onClick={() => this.props.addNewFields()}
                    // className="styledWTPrimaryButton"
                    //className={classes.buttonAdd}
                  >
                    <AddIcon fontSize="small" /> Add New
                  </Button>
                </span> */}
                {/* button's new position */}
              </div>
            </div>
          )}
          {this.props.parentState.lookupType === "VL" && (
            <div>
              <span className="iconBtn" style={{ float: "right", marginBottom: "0.5rem" }}>
                <TextField
                  variant="outlined"
                  style={{
                    marginbotton: "0.5rem",
                    textAlign: "center",

                    width: "10rem",
                    background: "#ffffff",
                  }}
                  sx={{ "& .MuiOutlinedInput-root": { fontSize: "14px", padding: "0" }, ".MuiOutlinedInput-input": { padding: "0.5rem" } }}
                  size="small"
                  placeholder="Search"
                  className="styleSearch"
                  onChange={(evt) => this.onChangeSearchQuery(evt)}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton aria-label="toggle password visibility">
                          <SearchIcon />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
                <Button size="small" variant="outlined" color="primary" style={{ marginLeft: 10, textTransform: "none" }} disableRipple onClick={() => this.hiddenFileInput.current.click()}>
                  Import <input type="file" ref={this.hiddenFileInput} onChange={(e) => this.props.handleExcelImport(e)} hidden />
                </Button>
                <Button size="small" variant="outlined" color="primary" style={{ marginLeft: 10, textTransform: "none" }} onClick={() => this.props.handleExcelExport()} disableRipple>
                  Export
                </Button>
                <Button
                  size="small"
                  variant="outlined"
                  color="primary"
                  style={{ marginLeft: 10, textTransform: "none" }}
                  onClick={() => this.props.addNewStaticVL()}
                  disableRipple
                  //className={classes.buttonAdd}
                >
                  <AddIcon fontSize="small" /> Add
                </Button>
              </span>
              {/* height: "14.5rem", overflowY: "scroll" was removed */}
              <TableContainer component={Paper} style={{ width: "99%" }}  className="styleRMStableContainerList" >
                <Table size="small" aria-label="a dense table" stickyHeader>
                  <TableHead>
                    <TableRow  className='styleHeaderCellList' >
                      <TableCell width="10%" style={{ fontWeight: 700, padding: "0" }}>
                        {this.props.translation?.KEY?.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700, padding: "0" }}>
                        {this.props.translation?.VALUE?.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700, padding: "0" }}>
                        {this.props.translation?.ADDITIONAL_VALUE?.longDescription}
                      </TableCell>

                      <TableCell width="10%" style={{ fontWeight: 700, padding: "0" }}>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {/* {this.props.parentState.valueList.map((row, index) => { */}
                    {(this.state.valueHelpList.length || this.state.searched !== "" ? this.state.valueHelpList : this.props.parentState.valueList).map((row, index) => {
                      {
                        /* {this.staticValuesHelpList().map((row, index) => { */
                      }
                      // {this.state.valueHelpList.map((row, index) => {
                      return (
                        <TableRow key={row.id} className="styleDataCell">
                          {/* <TableCell >{row.application}</TableCell> */}

                          <TableCell style={{ padding: "0" }}>
                            {/* <FormControl
                                                style={{ width: 220 }}
                                                variant="outlined" size="small">

                                                <Select
                                                    id="demo-simple-select-outlined"
                                                    value={row.label}
                                                    // size="small"
                                                    name="dataElement"
                                                    style={{ width: "100%" }}
                                                    onChange={(evt) => this.inputChangeHandler(evt)}
                                                >

                                                </Select>
                                            </FormControl> */}
                            <TextField
                              required
                              sx={{ width: "80%" }}
                              id="outlined-basic"
                              className="customInputpackage styleDTname"
                              name="key"
                              value={row.key}
                              variant="outlined"
                              //style={{ marginLeft: 30 }}
                              size="small"
                              onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                              // className="customInput2"
                            />
                          </TableCell>
                          <TableCell style={{ padding: "0" }}>
                            <TextField
                              required
                              id="outlined-basic"
                              name="value"
                              value={row.value}
                              variant="outlined"
                              sx={{ width: "80%" }}
                              className="styleDTname"
                              //style={{ marginLeft: 30 }}
                              size="small"
                              onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                              // className="customInput2"
                            />
                          </TableCell>
                          <TableCell style={{ padding: "0" }}>
                            <TextField
                              required
                              className="styleDTname"
                              id="outlined-basic"
                              name="additionalValue"
                              value={row.additionalValue}
                              variant="outlined"
                              sx={{ width: "80%" }}
                              //style={{ marginLeft: 30 }}
                              size="small"
                              onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                              // className="customInput2"
                            />
                          </TableCell>

                          {/* <TableCell >{row.permissionType}</TableCell> */}
                          <TableCell style={{ padding: "0" }}>
                            {/* <Tooltip title= 'Edit'> */}
                            <IconButton aria-label="Edit" style={{ padding: "0.5rem" }} size="small" onClick={() => this.props.deleteStaticVL(row, index)}>
                              <DeleteOutlineIcon style={{ padding: "0", color: "red" }} fontSize="small" />
                            </IconButton>
                            {/* </Tooltip> */}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
          )}
          {this.props.parentState.lookupType === "FILE" && (
            <div>
              <Grid container spacing={3} hSpacing="2rem" style={{ margin: "1rem" }}>
                <Grid item xs={12} sm={3} md={3}>
                  <FormControl style={{ width: 220 }} variant="outlined" size="small">
                    <InputLabel id="demo-simple-select-outlined-label" required>
                      {this.props.translation.FILE_TYPE.longDescription}
                    </InputLabel>
                    <Select
                      labelId="demo-simple-select-outlined-label"
                      id="demo-simple-select-outlined"
                      className="customInputpackage"
                      label={this.props.translation.FILE_TYPE.longDescription}
                      value={this.props.parentState.fileMetadata.fileType}
                      // size="small"
                      name="fileType"
                      style={{ width: "100%", height: "2.5rem" }}
                      onChange={(evt) => this.props.inputChangeHandler1(evt)}
                      // hidden={true}
                    >
                      {this.state.fileTypeDropDown.map((option) => (
                        <MenuItem
                          value={option.name}
                          key={option.name}
                          // onClick={() =>
                          //     this.applicationChangeHandler(option.id, index)

                          // }
                          // style={{ width: 30 }}
                        >
                          {option.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={3} md={3} hidden={this.props.parentState.fileMetadata.fileType !== ".XML"}>
                  <TextField
                    required
                    // hidden={this.props.parentState.fileMetadata.fileType !== ".xml"}
                    id="filled-disabled"
                    label="ParentTag"
                    className="styleDTname"
                    sx={{ width: "100%" }}
                    name="parentTag"
                    value={this.props.parentState.fileMetadata.properties.parentTag}
                    variant="outlined"
                    style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                    //style={{ marginLeft: 30 }}
                    size="small"
                    onChange={(evt) => this.props.inputChangeHandler1(evt)}
                    // className="customInput2"
                  />
                </Grid>
                <Grid item xs={12} sm={3} md={3}>
                  <Button
                    hidden={this.props.parentState.fileMetadata.fileType !== ".XLSX"}
                    size="small"
                    variant="contained"
                    color="primary"
                    onClick={() => this.props.addSheets()}
                    //className={classes.buttonAdd}
                  >
                    Add Sheets
                  </Button>
                </Grid>
              </Grid>
              {this.props.parentState.sheets.map((row, index) => {
                return (
                  <Grid container spacing={3} hSpacing="2rem" style={{ margin: "1rem" }} hidden={this.props.parentState.fileMetadata.fileType !== ".XLSX"}>
                    <Grid item xs={12} sm={3} md={3}>
                      <Input required className="customInputpackage" id="filled-disabled" label={this.props.translation.SHEET_INDEX.longDescription} name="sheetIndex" value={row.sheetName ? row.sheetName : row.sheetIndex} variant="outlined" style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }} size="small" onChange={(evt) => this.props.sheetsInputs(evt, index)} />
                    </Grid>

                    <Grid item xs={12} sm={3} md={3}>
                      <Input
                        required
                        id="filled-disabled"
                        className="customInputpackage"
                        label={this.props.translation.ROW_START.longDescription}
                        name="rowStart"
                        value={row.rowStart}
                        variant="outlined"
                        style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                        //style={{ marginLeft: 30 }}
                        size="small"
                        onChange={(evt) => this.props.sheetsInputs(evt, index)}
                        // className="customInput2"
                      />
                    </Grid>

                    <Grid item xs={12} sm={3} md={3}>
                      <Input
                        className="customInputpackage"
                        id="filled-disabled"
                        label="Column Start"
                        name="columnStart"
                        value={row.columnStart}
                        variant="outlined"
                        style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                        //style={{ marginLeft: 30 }}
                        size="small"
                        onChange={(evt) => this.props.sheetsInputs(evt, index)}
                        // className="customInput2"
                      />
                    </Grid>
                    <IconButton aria-label="Edit" onClick={() => this.props.deleteSheet(row.id, index)}>
                      <DeleteOutlineIcon fontSize="small" style={{ color: "red" }} />
                    </IconButton>
                  </Grid>
                );
              })}
              <span className="iconBtn" style={{ float: "right", marginBottom: "0.5rem" }}>
                <Button
                  size="small"
                  disableRipple
                  variant="outlined"
                  color="primary"
                  style={{ marginLeft: 10, textTransform: "none" }}
                  onClick={() => this.props.addNewConstraint()}
                  // className="styledWTPrimaryButton"
                  disabled={this.props.constraints.length !== 0 ? true : false}
                  //className={classes.buttonAdd}
                >
                  <AddIcon /> Add
                </Button>
              </span>
              <TableContainer component={Paper} style={{ margin: "0.5rem", width: "99%", height: "100%" }}  className="styleRMStableContainerList">
                <Table size="small" aria-label="a dense table" name="constraintTable">
                  <TableHead>
                    <TableRow  className='styleHeaderCellList'>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation.CONSTRAINT.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation.DISPLAY_NAME.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation.MAPPED_NAME.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation.CONSTRAINT_TYPE.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation.OPERATOR.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation.VALUE.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {this.props.constraints.length !== 0 &&
                      this.props.constraints.map((row, index) => {
                        return (
                          <TableRow key={row.id}>
                            {/* <TableCell >{row.application}</TableCell> */}

                            <TableCell>
                              <FormControl
                                // style={{ width: 220 }}
                                variant="outlined"
                                style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                size="small"
                              >
                                <InputBase
                                  name="constraintColumn"
                                  id="constraintTable"
                                  value={row.constraintColumn}
                                  variant="outlined"
                                  className="customInputpackage"
                                  // disabled
                                  InputProps={{
                                    readOnly: true,
                                  }}
                                  endAdornment={
                                    <InputAdornment position="end">
                                      <IconButton
                                        onClick={(evt) => this.props.fnAttrValueHelp(index, evt, "constraintTable")}
                                        // onMouseDown={handleMouseDownPassword}
                                        edge="end"
                                      >
                                        <SvgIcon>
                                          <path d="M17.391,2.406H7.266c-0.232,0-0.422,0.19-0.422,0.422v3.797H3.047c-0.232,0-0.422,0.19-0.422,0.422v10.125c0,0.232,0.19,0.422,0.422,0.422h10.125c0.231,0,0.422-0.189,0.422-0.422v-3.797h3.797c0.232,0,0.422-0.19,0.422-0.422V2.828C17.812,2.596,17.623,2.406,17.391,2.406 M12.749,16.75h-9.28V7.469h3.375v5.484c0,0.231,0.19,0.422,0.422,0.422h5.483V16.75zM16.969,12.531H7.688V3.25h9.281V12.531z"></path>
                                        </SvgIcon>
                                      </IconButton>
                                    </InputAdornment>
                                  }
                                ></InputBase>
                              </FormControl>
                            </TableCell>
                            <TableCell>
                              <Input
                                required
                                id="constraintTable"
                                disabled
                                name="constraintName"
                                className="customInputpackage"
                                value={row.constraintName}
                                variant="outlined"
                                style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                //style={{ marginLeft: 30 }}
                                size="small"
                                onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                // className="customInput2"
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                required
                                id="constraintTable"
                                className="customInputpackage"
                                name="mappedName"
                                value={row.mappedName}
                                variant="outlined"
                                style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                //style={{ marginLeft: 30 }}
                                size="small"
                                onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                // className="customInput2"
                              />
                            </TableCell>

                            {/* <TableCell >{row.permissionType}</TableCell> */}
                            <TableCell>
                              <Input
                                required
                                id="constraintTable"
                                className="customInputpackage"
                                name="constraintType"
                                value={row.constraintType}
                                variant="outlined"
                                style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                //style={{ marginLeft: 30 }}
                                size="small"
                                onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                // className="customInput2"
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                required
                                id="constraintTable"
                                disabled
                                className="customInputpackage"
                                name="constraintOperator"
                                value={row.constraintOperator}
                                variant="outlined"
                                style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                //style={{ marginLeft: 30 }}
                                size="small"
                                onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                // className="customInput2"
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                required
                                id="constraintTable"
                                className="customInputpackage"
                                name="constraintValue"
                                value={row.constraintValue}
                                variant="outlined"
                                style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                //style={{ marginLeft: 30 }}
                                size="small"
                                onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                // className="customInput2"
                              />
                            </TableCell>
                            <TableCell>
                              {/* <Tooltip title= 'Edit'> */}
                              <IconButton aria-label="Edit" onClick={() => this.props.deleteConstraintRows(row.id, index)}>
                                <DeleteOutlineIcon fontSize="small" style={{ color: "red" }} />
                              </IconButton>
                              {/* </Tooltip> */}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                  </TableBody>
                </Table>
              </TableContainer>
              <span className="iconBtn" style={{ float: "right", marginBottom: "0.5rem" }}>
                <Button
                  size="small"
                  variant="outlined"
                  color="primary"
                  style={{ marginLeft: 10, textTransform: "none" }}
                  onClick={() => this.props.addNewFields()}
                  // className="styledWTPrimaryButton"
                  disableRipple
                  //className={classes.buttonAdd}
                >
                  <AddIcon /> Add
                </Button>
              </span>
              <TableContainer component={Paper} style={{ width: "99%" }}  className="styleRMStableContainerList">
                <Table size="small" aria-label="a dense table" name="fieldsTable">
                  <TableHead>
                    <TableRow  className='styleHeaderCellList'>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation.FIELDS.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation.DISPLAY_NAME.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation.MAPPED_NAME.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation.SEARCHABLE.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation.UI_DISPLAY_NAME.longDescription}
                      </TableCell>

                      <TableCell width="10%" style={{ fontWeight: 700 }}>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {this.props.fields.map((row, index) => {
                      return (
                        <TableRow key={row.id}>
                          {/* <TableCell >{row.application}</TableCell> */}
                          <TableCell>
                            <FormControl
                              // style={{ width: 220 }}
                              variant="outlined"
                              size="small"
                            >
                              <InputBase
                                name="columnName"
                                id="fieldsTable"
                                value={row.columnName}
                                variant="outlined"
                                // disabled
                                disabled={!index}
                                className="customInputpackage"
                                InputProps={{
                                  readOnly: true,
                                }}
                                endAdornment={
                                  <InputAdornment position="end">
                                    <IconButton
                                      disabled={!index}
                                      onClick={(evt) => this.props.fnAttrValueHelp(index, evt, "fieldsTable")}
                                      // onMouseDown={handleMouseDownPassword}
                                      edge="end"
                                    >
                                      <SvgIcon>
                                        <path d="M17.391,2.406H7.266c-0.232,0-0.422,0.19-0.422,0.422v3.797H3.047c-0.232,0-0.422,0.19-0.422,0.422v10.125c0,0.232,0.19,0.422,0.422,0.422h10.125c0.231,0,0.422-0.189,0.422-0.422v-3.797h3.797c0.232,0,0.422-0.19,0.422-0.422V2.828C17.812,2.596,17.623,2.406,17.391,2.406 M12.749,16.75h-9.28V7.469h3.375v5.484c0,0.231,0.19,0.422,0.422,0.422h5.483V16.75zM16.969,12.531H7.688V3.25h9.281V12.531z"></path>
                                      </SvgIcon>
                                    </IconButton>
                                  </InputAdornment>
                                }
                              ></InputBase>
                            </FormControl>
                          </TableCell>
                          <TableCell>
                            <Input
                              required
                              id="fieldsTable"
                              disabled={!index}
                              className="customInputpackage"
                              InputProps={{
                                readOnly: true,
                              }}
                              name="displayName"
                              value={row.displayName}
                              variant="outlined"
                              style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                              //style={{ marginLeft: 30 }}
                              size="small"
                              onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                              // className="customInput2"
                            />
                          </TableCell>

                          {/* <TableCell >{row.permissionType}</TableCell> */}
                          <TableCell>
                            <Input
                              required
                              id="fieldsTable"
                              className="customInputpackage"
                              name="mappedName"
                              value={row.mappedName}
                              variant="outlined"
                              style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                              //style={{ marginLeft: 30 }}
                              size="small"
                              onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                              // className="customInput2"
                            />
                          </TableCell>
                          <TableCell>
                            <FormControl component="fieldset" style={{ marginLeft: 10, width: "100%" }}>
                              <FormGroup aria-label="position" row>
                                <FormControlLabel
                                  required
                                  // id="fieldsTable"
                                  size="small"
                                  disabled={!index}
                                  InputProps={{
                                    readOnly: true,
                                  }}
                                  value={row.searchable}
                                  className="customInputpackage MuiFormControlLabel-asterisk"
                                  //checked={this.state.obj.active}
                                  name="searchable"
                                  onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                  //value={row.active}
                                  // labelPlacement="start"
                                  control={<Switch color="primary" checked={row.searchable} id="fieldsTable" />}
                                />
                              </FormGroup>
                            </FormControl>
                          </TableCell>
                          <TableCell>
                            <RadioGroup row style={{ marginLeft: "1rem" }} name="isDisplayName">
                              <Radio id="fieldsTable" checked={row.isDisplayName} onChange={(evt) => this.props.valueHelpTableInputs(evt, index)} />
                            </RadioGroup>
                          </TableCell>
                          <TableCell>
                            {/* <Tooltip title= 'Edit'> */}
                            <IconButton
                              aria-label="Edit"
                              onClick={() => this.props.deleteFieldRows(row.id, index)}
                              disabled={!index}
                              InputProps={{
                                readOnly: true,
                              }}
                            >
                              <DeleteOutlineIcon fontSize="small" style={{ color: "red" }} />
                            </IconButton>
                            {/* </Tooltip> */}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </div>
          )}

          {this.props.parentState.lookupType === "COMPOSITE" && (
            <div>
              <span className="iconBtn" style={{ float: "right", marginBottom: "0.5rem" }}>
                <Button
                  size="small"
                  variant="outlined"
                  color="primary"
                  style={{ marginLeft: 10, textTransform: "none" }}
                  onClick={() => this.props.addNewConstraint()}
                  disabled={this.props.constraints.length !== 0 ? true : false}
                  // className="styledWTPrimaryButton"
                  disableRipple
                  //className={classes.buttonAdd}
                >
                  <AddIcon /> Add
                </Button>
              </span>
              <TableContainer component={Paper} style={{ margin: "0.5rem", width: "99%" }}  className="styleRMStableContainerList">
                <Table size="small" aria-label="a dense table" name="constraintTable">
                  <TableHead>
                    <TableRow  className='styleHeaderCellList'>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation?.CONSTRAINT?.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation?.DISPLAY_NAME?.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation?.MAPPED_NAME?.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation?.CONSTRAINT_TYPE?.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation?.OPERATOR?.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>
                        {this.props.translation?.VALUE?.longDescription}
                      </TableCell>
                      <TableCell width="10%" style={{ fontWeight: 700 }}>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {this.props.constraints.length !== 0 &&
                      this.props.constraints.map((row, index) => {
                        return (
                          <TableRow key={row.id}>
                            {/* <TableCell >{row.application}</TableCell> */}

                            <TableCell>
                              <FormControl
                                // style={{ width: 220 }}
                                variant="outlined"
                                size="small"
                              >
                                <InputBase
                                  name="constraintColumn"
                                  id="constraintTable"
                                  value={row.constraintColumn}
                                  className="customInputpackage"
                                  variant="outlined"
                                  // disabled
                                  InputProps={{
                                    readOnly: true,
                                  }}
                                  endAdornment={
                                    <InputAdornment position="end">
                                      <IconButton
                                        onClick={(evt) => this.props.fnAttrValueHelp(index, evt, "constraintTable")}
                                        // onMouseDown={handleMouseDownPassword}
                                        edge="end"
                                      >
                                        <SvgIcon>
                                          <path d="M17.391,2.406H7.266c-0.232,0-0.422,0.19-0.422,0.422v3.797H3.047c-0.232,0-0.422,0.19-0.422,0.422v10.125c0,0.232,0.19,0.422,0.422,0.422h10.125c0.231,0,0.422-0.189,0.422-0.422v-3.797h3.797c0.232,0,0.422-0.19,0.422-0.422V2.828C17.812,2.596,17.623,2.406,17.391,2.406 M12.749,16.75h-9.28V7.469h3.375v5.484c0,0.231,0.19,0.422,0.422,0.422h5.483V16.75zM16.969,12.531H7.688V3.25h9.281V12.531z"></path>
                                        </SvgIcon>
                                      </IconButton>
                                    </InputAdornment>
                                  }
                                ></InputBase>
                              </FormControl>
                            </TableCell>
                            <TableCell>
                              <Input
                                required
                                id="constraintTable"
                                className="customInputpackage"
                                disabled
                                name="constraintName"
                                value={row.constraintName}
                                variant="outlined"
                                style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                //style={{ marginLeft: 30 }}
                                size="small"
                                onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                // className="customInput2"
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                required
                                id="constraintTable"
                                className="customInputpackage"
                                name="mappedName"
                                value={row.mappedName}
                                variant="outlined"
                                style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                //style={{ marginLeft: 30 }}
                                size="small"
                                onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                // className="customInput2"
                              />
                            </TableCell>

                            {/* <TableCell >{row.permissionType}</TableCell> */}
                            <TableCell>
                              <Input
                                required
                                id="constraintTable"
                                className="customInputpackage"
                                name="constraintType"
                                value={row.constraintType}
                                variant="outlined"
                                style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                //style={{ marginLeft: 30 }}
                                size="small"
                                onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                // className="customInput2"
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                required
                                id="constraintTable"
                                className="customInputpackage"
                                disabled
                                name="constraintOperator"
                                value={row.constraintOperator}
                                variant="outlined"
                                style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                //style={{ marginLeft: 30 }}
                                size="small"
                                onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                // className="customInput2"
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                required
                                id="constraintTable"
                                className="customInputpackage"
                                name="constraintValue"
                                value={row.constraintValue}
                                variant="outlined"
                                style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                                //style={{ marginLeft: 30 }}
                                size="small"
                                onChange={(evt) => this.props.valueHelpTableInputs(evt, index)}
                                // className="customInput2"
                              />
                            </TableCell>
                            <TableCell>
                              {/* <Tooltip title= 'Edit'> */}
                              <IconButton aria-label="Edit" onClick={() => this.props.deleteConstraintRows(row.id, index)}>
                                <DeleteOutlineIcon fontSize="small" style={{ color: "red" }} />
                              </IconButton>
                              {/* </Tooltip> */}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                  </TableBody>
                </Table>
              </TableContainer>
              <span className="iconBtn" style={{ float: "left", marginBottom: "0.5rem" }} hidden={!(this.props.parentState.attributeLapiDataInput && this.props.constraints.length !== 0)}>
                <Button
                  size="small"
                  variant="outlined"
                  color="primary"
                  style={{ marginLeft: 10, textTransform: "none" }}
                  onClick={() => this.props.addComposite()}
                  // className="styledWTPrimaryButton"
                  disableRipple
                  //className={classes.buttonAdd}
                >
                  <AddIcon /> Add
                </Button>
              </span>
              <span hidden={!(this.props.parentState.attributeLapiDataInput && this.props.constraints.length !== 0)}>
                {this.props.parentState.compositeArray.map((row, index) => {
                  return (
                    <Grid container spacing={3} hSpacing="2rem" style={{ margin: "1rem" }} hidden={!(this.props.parentState.attributeLapiDataInput && this.props.constraints.length !== 0)}>
                      <Grid item xs={12} sm={3} md={3}>
                        <FormControl variant="outlined" size="small">
                          <InputBase
                            name=""
                            id={row.key}
                            label=""
                            value={row.value}
                            variant="outlined"
                            style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                            // disabled
                            InputProps={{
                              readOnly: true,
                            }}
                            endAdornment={
                              <InputAdornment position="end">
                                <IconButton
                                  onClick={() => this.props.compositeValueHelp(index)}
                                  // onMouseDown={handleMouseDownPassword}
                                  edge="end"
                                >
                                  <SvgIcon>
                                    <path d="M17.391,2.406H7.266c-0.232,0-0.422,0.19-0.422,0.422v3.797H3.047c-0.232,0-0.422,0.19-0.422,0.422v10.125c0,0.232,0.19,0.422,0.422,0.422h10.125c0.231,0,0.422-0.189,0.422-0.422v-3.797h3.797c0.232,0,0.422-0.19,0.422-0.422V2.828C17.812,2.596,17.623,2.406,17.391,2.406 M12.749,16.75h-9.28V7.469h3.375v5.484c0,0.231,0.19,0.422,0.422,0.422h5.483V16.75zM16.969,12.531H7.688V3.25h9.281V12.531z"></path>
                                  </SvgIcon>
                                </IconButton>
                              </InputAdornment>
                            }
                          ></InputBase>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} sm={3} md={3}>
                        <FormControl style={{ width: 220 }} variant="outlined" size="small">
                          <InputLabel id="demo-simple-select-outlined-label" required>
                            Value Help Type
                          </InputLabel>
                          <Select
                            labelId="demo-simple-select-outlined-label"
                            id="demo-simple-select-outlined"
                            label="Value Help Type"
                            value={row.lookupType}
                            className="customInputpackage"
                            // size="small"
                            name="apiType"
                            style={{ width: "100%", height: "1.8rem" }}
                            onChange={(evt, keyProps) => this.props.onLookupType(evt, keyProps, index)}
                          >
                            {this.state.valueHelpType.map((option) => (
                              <MenuItem value={option.key} key={option.key}>
                                {option.name}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                        <IconButton aria-label="Edit" color="primary" hidden={row.lookupType === null ? true : false} onClick={() => this.fnComposite(row, index)}>
                          <ArrowForwardIosIcon fontSize="small" />
                        </IconButton>
                      </Grid>

                      <IconButton aria-label="Edit" onClick={() => this.props.deleteComposite(row, index)}>
                        <DeleteOutlineIcon fontSize="small" style={{ color: "red" }} />
                      </IconButton>
                    </Grid>
                  );
                })}
              </span>

              {/* {this.state.busyIndicator ? <div style={{ display: "flex", float: "center", paddingBottom: 20, marginLeft: 500, marginTop: 200 }}><CircularProgress /></div> : */}
              <Composite
                source={this.props.source}
                open={this.state.showVHTableDialog}
                actions={["DISCARD", "CANCEL"]}
                onClose={() => this.closeDialog()}
                rowSelected={(row) => this.props.rowSelected(row, this.state.index)}
                {...this.props}
                lookupType={this.state.lookupTypeC}
                getData1={(data) => this.props.getData(data, this.state.index)}
                data={this.props.parentState.selectedRow}
                lookupId={this.state.compositeRow.referenceId}
                name={this.props.parentState.selectedRow.name}
                compositeRow={this.state.compositeRow}
                destinations={this.state.destinations}
                dataTable={this.state.dataTable}
                sheets={this.state.sheets}
                fileMetadata={this.state.fileMetadata}
                apiMetadata1={this.state.tempdata}
                dbMetaData={this.state.dbMetaData}
                valueList={this.state.valueList}
                constraints={this.state.constraints}
                fields={this.state.fields}
              />
            </div>
          )}
        </Box>
        <ValueHelpDialog title="Value Help Table " open={this.props.parentState.showVHTableDialog} actions={["DISCARD", "CANCEL"]} onClose={() => this.props.closeDialog()} tempDataEle={this.props.parentState.fieldCatalogArray} dataElement={this.props.parentState.fieldCatalogArray} rowSelected={(row) => this.props.rowSelected(row, this.state.index)} />
        <Dialog
          open={this.state.hostdialogOpen}
          // TransitionComponent={Transition}
          maxWidth={"md"}
          onClose={() => this.setHost(false)}
          aria-describedby="alert-dialog-slide-description"
        >
          {/* <AppBar sx={{ position: "relative" }}>
            <Toolbar>
              <IconButton edge="start" color="inherit" onClick={() => this.setHost(false)} aria-label="close">
                <CloseIcon />
              </IconButton>
              <Typography sx={{ ml: 2, flex: 1 }} variant="h6" component="div">
                Create Host
              </Typography>
              <Button autoFocus color="inherit">
                save
              </Button>
            </Toolbar>
          </AppBar> */}

          <Stack
            direction="row"
            columnGap={2}
            alignItems="center"
            sx={{
              background: "#ffffff",

              borderRadius: "7px",

              width: "100%",

              height: "63%",
            }}
          >
            <Typography sx={{ ml: 2, flex: 1, marginLeft: 0 }} variant="h6" component="div">
              Create Host
            </Typography>

            <IconButton size="small" color="inherit" onClick={() => this.setHost(false)} aria-label="close">
              <CloseIcon />
            </IconButton>
          </Stack>

          <DialogContent sx={{ background: "#F1F5FE" }}>
            <Stack sx={{ padding: 0 }}>
              <Stack direction="column" columnGap={2} alignItems="flex-start" style={{ width: "100%", justifyContent: "space-between" }}>
                <Stack>
                  <InputLabel id="demo-simple-select-outlined-label" sx={{fontSize: "0.875rem"}}>
                    Database Name<span style={{ color: "red" }}>*</span>
                  </InputLabel>
                  <TextField required id="outlined-basic" name="dbName" sx={{ width: "90%" }} disableUnderline value={this.state.hostDetails.dbName} variant="outlined" size="small" onChange={(evt) => this.fnChangeHostDetails(evt)} className="styleDTname" />
                </Stack>
                <Stack>
                  <InputLabel id="demo-simple-select-outlined-label" sx={{fontSize: "0.875rem"}}>Host</InputLabel>
                  <TextField required id="outlined-basic" name="host" sx={{ width: "90%" }} disableUnderline value={this.state.hostDetails.host} variant="outlined" size="small" onChange={(evt) => this.fnChangeHostDetails(evt)} className="styleDTname" />
                </Stack>
                <Stack>
                  <InputLabel id="demo-simple-select-outlined-label" sx={{fontSize: "0.875rem"}}>Url</InputLabel>
                  <TextField required id="outlined-basic" name="url" sx={{ width: "90%" }} disableUnderline value={this.state.hostDetails.url} variant="outlined" onChange={(evt) => this.fnChangeHostDetails(evt)} size="small" className="styleDTname" />
                </Stack>
                <Stack>
                  <InputLabel id="demo-simple-select-outlined-label" sx={{fontSize: "0.875rem"}}>Database Type</InputLabel>
                  <FormControl style={{ width: "11.5rem" }} variant="outlined" size="small">
                    <Select
                      style={{ width: "12rem" }}
                      name="dbType"
                      labelId="demo-simple-select-outlined-label"
                      id="demo-simple-select-outlined"
                      size="small"
                      value={this.state.hostDetails.dbType}
                      sx={{ width: 180, height: "1.8rem" }}
                      onChange={(evt) => this.fnChangeHostDetails(evt)}
                    >
                      {this.state.databaseType.map((option) => (
                        <MenuItem
                          value={option.type}
                          key={option.type}
                        >
                          {option.type}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Stack>
                <Stack>
                  <InputLabel id="demo-simple-select-outlined-label" sx={{fontSize: "0.875rem"}}>Username</InputLabel>
                  <TextField required id="outlined-basic" name="user" sx={{ width: "90%" }} disableUnderline value={this.state.hostDetails.user} variant="outlined" size="small" onChange={(evt) => this.fnChangeHostDetails(evt)} className="styleDTname" />
                </Stack>
                <Stack>
                  <InputLabel id="demo-simple-select-outlined-label" sx={{fontSize: "0.875rem"}}>Password</InputLabel>
                  <TextField
                    required
                    id="outlined-basic"
                    name="password"
                    value={this.state.hostDetails.password}
                    type={this.state.showPassword ? "input" : "password"}
                    sx={{ width: "70%" }}
                    disableUnderline
                    onChange={(evt) => this.fnChangeHostDetails(evt)}
                    variant="outlined"
                    size="small"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton aria-label="toggle password visibility" onClick={() => this.changePasswordVisibility()}>
                            {this.state.showPassword ? <Visibility /> : <VisibilityOff />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    className="styleDTname"
                  />
                </Stack>
              </Stack>

              <Stack sx={{ justifyContent: "flex-end", margin: 0, marginTop: "1rem", width: "5rem" }}>
                <Button autoFocus variant="contained" size="small">
                  Save
                </Button>
              </Stack>
            </Stack>
          </DialogContent>
        </Dialog>
      </div>
    );
  }
}

export default ValueHelp;
