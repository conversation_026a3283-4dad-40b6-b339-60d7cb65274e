import { useSelector, useDispatch } from 'react-redux';
import { Authoring } from '@cw/idm_qa';
// import wrColors from '../model/wrColors.json';
// import features from "../utility/features.json";
import { useMemo } from 'react';
import {Box} from "@mui/material";
import { useNavigate } from "react-router";
import { destination_IDM } from "../../../destinationVariables"; 
import { APP_END_POINTS } from "../../../constant/appEndPoints";


const DecisionTable = (props) => {
  const navigate = useNavigate();
  const destinationsList = [

    {
      "Description": "",
      "Name": "WorkRulesServices",
      "URL": destination_IDM
    },
    {
      "Description": "",
      "Name": "CW_Worktext",
      "URL": destination_IDM
    },
    {
      "Description": "",
      "Name": "WorkRuleEngineServices",
      "URL": destination_IDM
    },
    {
      "Description": "",
      "Name": "WorkUtilsServices",
      "URL": destination_IDM
    }

  ]
  const NavbackToList = () => {
    let dt_details = {...props.DTdetails};
    dt_details["DTid"] = "";
    dt_details["ruleName"]= '',
    dt_details["version"]= '',
    props.setShowDT(false);
    navigate(APP_END_POINTS?.BUSINESS_RULES?.AUTHORING);
  }
  const colors={
    "headerColors_C": "#DFEAFB",
    "headerColors_A": "#FFF7E2",
    "headerColors_P": "#EEEEEE",
    "headerColors_S": "#EEEEEE",
    "textColor": "rgb(66, 66, 66)",
    "hoveredTextColor": "rgb(25, 118, 210)",
    "fontFamily": "inherit"
}


  const authoringMemo = useMemo(() => (
    <Authoring colors={colors} translationDataObjects={[]} Dtdetails={props?.DTdetails} destinations={destinationsList} NavBackHandler={NavbackToList} dateFormat="dd-MMM-yyyy" disableExistingRows={false} />
  ), [props.DTdetails?.DTid]);
  

  return (
    <Box> 
      {authoringMemo}
    </Box>
  )
}
export default DecisionTable;