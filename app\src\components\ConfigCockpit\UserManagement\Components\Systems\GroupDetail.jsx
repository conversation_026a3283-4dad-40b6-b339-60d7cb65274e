import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  Paper,
  Stack,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import CloseIcon from "@mui/icons-material/Close";
import {
  Add,
  Remove,
  Close,
  CheckBoxOutlineBlank,
  CheckBox,
} from "@mui/icons-material";
import { Autocomplete } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import Loading from "../Loading";
import {
  findApplicationById,
  findRoleById,
  findUserById,
} from "../../Utility/basic";
import {
  setGroups,
  setResponseMessage,
} from "../../../../../app/userManagementSlice";
import DeletionMessageBox from "../DeletionMessageBox";
import {
  appHeaderHeight,
  buttonHeight,
  crossIconContainerHeight,
  groupDetailPageCss,
  groupPageHeaderHeight,
} from "../../Data/cssConstant";
import {
  destination_IWA,
  destination_IWA_NPI,
  destination_Po,
} from "../../../../../destinationVariables";
import PeopleIcon from "@mui/icons-material/People";
import InfoIcon from "@mui/icons-material/Info";
import { TabContext, TabList, TabPanel } from "@mui/lab";
// import { doAjax } from "../../../../common/fetchService";
import localConfigServer from "../../../../../data/localConfigServer.json";
import ReusableTable from "../../../../common/ReusableTable";
import ReusablePromptBox from "../../../../common/ReusablePromptBox/ReusablePromptBox";
import { doAjax } from "../../../../Common/fetchService";
const useStyle = makeStyles((theme) => ({
  groupInfoContainer: {
    display: "flex",
    flexDirection: "column",
    padding: 10,
    height: `calc(100vh - ${appHeaderHeight} - ${groupPageHeaderHeight} - ${crossIconContainerHeight} - ${groupDetailPageCss?.tabsContainerHeight} - ${groupDetailPageCss?.footerHeight} - 18px)`,
  },
  groupInfoItemContainer: {
    margin: 4,
    alignItems: "center",
  },
  groupInfoContainerLabel: {
    margin: "0px",
    fontFamily: "Roboto, Helvetica, Arial, sans-serif",
    fontWeight: "400",
    fontSize: "12px",
  },
  groupInfoContainerText: {
    fontFamily: "Roboto, Helvetica, Arial, sans-serif",
    fontWeight: "400",
    fontSize: "12px",
  },

  newGroupAssociateUsersDialogTitle: {
    height: "3rem",
    display: "flex",
    alignItems: "center",
    borderBottom: "1px solid #d9d9d9",
    margin: 0,
    padding: 0,
    paddingLeft: 10,
  },
  newGroupAssociateUsersDialogActions: {
    height: "3rem",
    borderTop: "1px solid #d9d9d9",
  },

  groupAssociateUsersTableContainer: {
    height: `calc(100vh - ${appHeaderHeight} - ${groupPageHeaderHeight} - ${crossIconContainerHeight} - ${groupDetailPageCss?.tabsContainerHeight} - ${buttonHeight} - ${groupDetailPageCss?.footerHeight} - 26px)`,
    width: "100%",
  },
  groupAssociateUsersTableHead: {
    position: "sticky",
    top: 0,
    zIndex: 99,
    backgroundColor: "#F1F5FE",
  },
  groupAssociateUsersTableHeadCell: {
    whiteSpace: "nowrap",
    fontWeight: "bold",
    borderLeft: "1px solid #E5E5E5",
    borderRight: "1px solid #E5E5E5",
  },
  groupAssociateUsersTableBody: {
    height: "100%",
  },
  groupAssociateUsersTableBodyRow: {
    "&:hover": {
      backgroundColor: theme.palette.action.hover,
    },
  },
  groupAssociateUsersTableBodyCell: {
    whiteSpace: "nowrap",
    fontSize: 12,
    backgroundColor: "white",
    borderLeft: "1px solid #E5E5E5",
    borderRight: "1px solid #E5E5E5",
  },
  groupAssociateUsersBottomAddButton: {
    margin: "4px 10px",
    textTransform: "capitalize",
    height: buttonHeight,
  },

  newGroupAssignedRolesDialogTitle: {
    height: "3rem",
    display: "flex",
    alignItems: "center",
    borderBottom: "1px solid #d9d9d9",
    margin: 0,
    padding: 0,
    paddingLeft: 10,
  },
  newGroupAssignedRolesDialogActions: {
    height: "3rem",
    borderTop: "1px solid #d9d9d9",
  },

  groupAssignedRolesTableContainer: {
    height: `calc(100vh - ${appHeaderHeight} - ${groupPageHeaderHeight} - ${crossIconContainerHeight} - ${groupDetailPageCss?.tabsContainerHeight} - ${buttonHeight} - ${groupDetailPageCss?.footerHeight} - 26px)`,
    width: "100%",
  },
  groupAssignedRolesTableHead: {
    position: "sticky",
    top: 0,
    zIndex: 99,
    backgroundColor: "#F1F5FE",
  },
  groupAssignedRolesTableHeadCell: {
    whiteSpace: "nowrap",
    fontSize: 9,
    fontWeight: "bold",
    borderLeft: "1px solid #E5E5E5",
    borderRight: "1px solid #E5E5E5",
  },
  groupAssignedRolesTableBody: {
    height: "100%",
  },
  groupAssignedRolesTableBodyRow: {
    "&:hover": {
      backgroundColor: theme.palette.action.hover,
    },
  },
  groupAssignedRolesTableBodyCell: {
    whiteSpace: "nowrap",
    fontSize: 12,
    backgroundColor: "white",
    borderLeft: "1px solid #E5E5E5",
    borderRight: "1px solid #E5E5E5",
  },
  groupAssignedRolesBottomAddButton: {
    margin: "4px 10px",
    textTransform: "capitalize",
    height: buttonHeight,
  },

  groupDetailContainer: {
    flexDirection: "column",
    height: "100%",
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
    backgroundColor: theme.palette.background.paper,
    margin: 0,
    padding: 0,
    position: "relative",
  },
  groupDetailCrossContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end",
    paddingRight: 10,
    paddingTop: 10,
    height: crossIconContainerHeight,
  },
  groupDetailHeaderContainer: {
    display: "flex",
    alignItems: "center",
    padding: 10,
    borderBottom: `1px solid ${theme.palette.text.secondary}`,
    height: groupDetailPageCss?.tabsContainerHeight,
  },
  groupDetailHeaderItem: {
    color: theme.palette.text.secondary,
    fontWeight: "normal",
    cursor: "pointer",
    width: 150,
    fontSize: 14,
    marginLeft: 8,
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
  },
  groupDetailHeaderItemSelected: {
    color: theme.palette.text.primary,
    fontWeight: "bold",
  },
  groupDetailFooter: {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end",
    boxShadow: "0px 0px 9px #D8D8D8",
    padding: "8px 16px",
    // position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: theme.palette.background.paper,
    zIndex: 999,
    height: groupDetailPageCss?.footerHeight,
  },
  groupDetailFooterButton: {
    textTransform: "capitalize",
    fontSize: 14,
    fontWeight: "bold",
  },
}));

const GroupInfo = ({ groupDetail, setGroupDetail, params, load }) => {
  const classes = useStyle();
  let masterData = useSelector((state) => state.masterData);
  const [suppliersData, setsuppliersData] = useState({
    codeList: [],
    dataMap: {},
  });
  const userReducerState = useSelector((state) => state.userReducer);
  const basicReducerState = useSelector((state) => state.userManagement);
  const [purchasingGroups, setPurchasingGroups] = useState({});

  const getApplicationNameById = (id) => {
    const application = findApplicationById(
      Number(id),
      basicReducerState?.applications
    );
    return application?.name || "-";
  };
  const getPurchGroupDetails = () => {
    // if (userData?.supplierId) {
    //   setVendorDetailsSet({
    //     [userData?.supplierId]: userData?.supplierName,
    //   });
    // } else {

    // if (moduleFilter?.companyCode !== "") {
    const hSuccess = (data) => {
      setPurchasingGroups(data.data);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_Po}/Odata/purchasingGroup`, "get", hSuccess, hError);
    // }
    // }
  };
  useEffect(() => {
    doAjax(`/${destination_Po}/Odata/getAllSuppliers`, "get", (res) => {
      setsuppliersData({
        codeList: Object.keys(res),
        dataMap: res,
      });
    });
    getPurchGroupDetails();
  }, []);

  return (
    <>
      <div
        style={{
          minHeight: "22rem",
        }}
      >
        <Grid container>
          <Grid
            container
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Grid
              item
              xs={4}
              sx={{
                display: "flex",
                alignItems: "center",
                height: "max-content",
                marginTop: ".5rem",
              }}
            >
              <Typography variant="body2">
                Name<span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>

            <Grid item xs={6}>
              <div
                className={`inputContainer ${
                  basicReducerState?.groups?.find(
                    (group) =>
                      group?.name === groupDetail?.name &&
                      Number(group?.id) !== Number(params?.groupId)
                  ) &&
                  !load &&
                  "inputError"
                }`}
              >
                <TextField
                  disabled={true}
                  value={groupDetail?.name}
                  onChange={(e) =>
                    setGroupDetail({ ...groupDetail, name: e.target.value })
                  }
                  inputProps={{
                    style: {
                      fontFamily: "Roboto, Helvetica, Arial, sans-serif",
                      fontWeight: "400",
                      fontSize: "12px",
                    },
                  }}
                  fullWidth
                  size="small"
                />
              </div>

              {basicReducerState?.groups?.find(
                (group) =>
                  group?.name === groupDetail?.name &&
                  Number(group?.id) !== Number(params?.groupId)
              ) &&
                !load && (
                  <p style={{ color: "red", fontSize: 10, marginTop: 3 }}>
                    Group name already exists
                  </p>
                )}

              {groupDetail?.name?.length === 0 && !load && (
                <p style={{ color: "red", fontSize: 10, marginTop: 3 }}>
                  Please fill it
                </p>
              )}
            </Grid>
          </Grid>

          {groupDetail?.roleName && (
            <Grid
              container
              sx={{
                marginTop: ".5rem",
              }}
            >
              <Grid
                item
                xs={4}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  height: "max-content",
                  marginTop: ".5rem",
                }}
              >
                <Typography variant="body2">
                  Role Name
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <div>
                  <TextField
                    value={groupDetail?.roleName}
                    disabled={true}
                    fullWidth
                    inputProps={{
                      style: {
                        fontFamily: "Roboto, Helvetica, Arial, sans-serif",
                        fontWeight: "400",
                        fontSize: "12px",
                      },
                    }}
                    size="small"
                  />
                </div>
              </Grid>
            </Grid>
          )}

          {groupDetail?.purchasingGroup && (
            <Grid
              container
              sx={{
                marginTop: ".5rem",
              }}
            >
              <Grid
                item
                xs={4}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  height: "max-content",
                  marginTop: ".5rem",
                }}
              >
                <Typography variant="body2">Purchasing Group</Typography>
              </Grid>
              <Grid item xs={6}>
                <div>
                  <TextField
                    value={groupDetail?.purchasingGroup}
                    disabled={true}
                    fullWidth
                    inputProps={{
                      style: {
                        fontFamily: "Roboto, Helvetica, Arial, sans-serif",
                        fontWeight: "400",
                        fontSize: "12px",
                      },
                    }}
                    size="small"
                  />
                </div>
              </Grid>
              {/* <Grid item xs={6}>
              <Autocomplete
                size="small"
                // disableCloseOnSelect
                filterSelectedOptions
                style={{ fontSize: 12 }}
                value={parseInt(groupDetail?.purchasingGroup)}
                onChange={(e, data) => {
                  console.log(groupDetail?.purchasingGroup);
                  // console.log(e)

                  setGroupDetail({
                    ...groupDetail,
                    purchasingGroup: data,
                  });
                }}
                options={Object.keys(purchasingGroups)}
                getOptionLabel={(option) => {
                  if (option) {
                    return `${option} - ${purchasingGroups?.[option]}`;
                  } else {
                    return "";
                  }
                }}
                // renderOption={(props, option, { selected }) => (
                //   <li {...props}>
                //     <Checkbox
                //       icon={<CheckBoxOutlineBlank fontSize="small" />}
                //       checkedIcon={<CheckBox color="primary" fontSize="small" />}
                //       checked={selected}
                //     />

                //     <Typography style={{ fontSize: 12 }}>
                //       {`${option} - ${purchasingGroups?.[option]}`}
                //     </Typography>
                //   </li>
                // )}

                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    style={{ fontSize: 12 }}
                  />
                )}
              />
            </Grid> */}
            </Grid>
          )}
          {groupDetail?.supplierId && (
            <Grid
              container
              sx={{
                marginTop: ".5rem",
              }}
            >
              <Grid
                item
                xs={4}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  height: "max-content",
                  marginTop: ".5rem",
                }}
              >
                <Typography variant="body2">Supplier</Typography>
              </Grid>
              <Grid item xs={6}>
                <div>
                  <TextField
                    value={groupDetail?.supplierId}
                    disabled={true}
                    fullWidth
                    inputProps={{
                      style: {
                        fontFamily: "Roboto, Helvetica, Arial, sans-serif",
                        fontWeight: "400",
                        fontSize: "12px",
                      },
                    }}
                    size="small"
                  />
                </div>
              </Grid>
              {/* <Grid item xs={6}>
              <Autocomplete
                // multiple
                size="small"
                // disableCloseOnSelect
                filterSelectedOptions
                style={{ fontSize: 12 }}
                value={groupDetail?.supplierId}
                onChange={(e, data) => {
                  setGroupDetail({
                    ...groupDetail,
                    supplierId: data,
                  });
                }}
                options={suppliersData?.codeList}
                getOptionLabel={(option) => {
                  if (option) {
                    return `${option} - ${suppliersData.dataMap[option]}`;
                  } else {
                    return "";
                  }
                }}
                renderOption={(props, option, { selected }) => (
                  <li {...props}>
                    <Checkbox
                      icon={<CheckBoxOutlineBlank fontSize="small" />}
                      checkedIcon={
                        <CheckBox color="primary" fontSize="small" />
                      }
                      checked={selected}
                    />

                    <Typography style={{ fontSize: 12 }}>
                      {`${option} - ${suppliersData.dataMap[option]}`}
                    </Typography>
                  </li>
                )}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    style={{ fontSize: 12 }}
                  />
                )}
              />
            </Grid> */}
            </Grid>
          )}
        </Grid>
      </div>
    </>
  );
};

const NewGroupAssociateUsers = ({
  open,
  onClose,
  groupDetail,
  setGroupDetail,
  params,
}) => {
  const classes = useStyle();
  const basicReducerState = useSelector((state) => state.userManagement);
  const [newAssociateUsers, setNewAssociateUsers] = useState([]);
  const [usersList, setUsersList] = useState([]);
  useEffect(() => {
    let tAssociateUsers = {};
    let tUsersData = {};
    groupDetail?.associateUsers?.forEach((i) => (tAssociateUsers[i] = true));

    let tList = [];
    basicReducerState?.users.forEach((user) => {
      //handle the prevention of duplicate entry of user data
      if (!tAssociateUsers[user?.emailId] && !tUsersData[user?.emailId]) {
        //if email id do not exist in tUserData
        //add the email id to tUserData
        tUsersData[user?.emailId] = true;
        tList.push(user);
        //check if selected user is in tAssociatedUsers list. if not add the user to usersList
      }
    });
    setUsersList(tList);
  }, []);
  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose}>
      <DialogTitle
        sx={{
          height: "3rem",
          display: "flex",
          margin: 0,
          justifyContent: "space-between",
          alignItems: "center",
          padding: ".5rem",
          paddingLeft: "1rem",
          backgroundColor: "#EAE9FF40",
        }}
      >
        <Typography variant="h6">New Associate Users</Typography>
        <IconButton
          sx={{ width: "max-content" }}
          onClick={onClose}
          children={<CloseIcon />}
        />
      </DialogTitle>

      <DialogContent sx={{ padding: "1rem 1rem" }}>
        <Grid
          container
          sx={{
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Grid
            item
            xs
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Typography variant="body1">
              Associate Users<span style={{ color: "red" }}>*</span>
            </Typography>
            <Autocomplete
              multiple
              size="small"
              disableCloseOnSelect
              filterSelectedOptions
              style={{ fontSize: 12 }}
              value={newAssociateUsers}
              onChange={(e, users) => {
                setNewAssociateUsers(users || []);
              }}
              options={
                usersList
                // ?.filter(
                //   (user) =>
                //     user?.applicationName?.includes(
                //       getApplicationNameById(groupDetail?.applicationId)
                //     ) && !presentUser(user)
                // )
              }
              getOptionLabel={(option) => option.displayName}
              renderOption={(props, option, { selected }) => (
                <li {...props}>
                  <Checkbox
                    icon={<CheckBoxOutlineBlank fontSize="small" />}
                    checkedIcon={<CheckBox color="primary" fontSize="small" />}
                    checked={selected}
                  />

                  <Typography style={{ fontSize: 12 }}>
                    {option?.displayName}
                  </Typography>
                </li>
              )}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  style={{ fontSize: 12 }}
                  placeholder={newAssociateUsers[0] ? `` : `Select Users`}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
        <Button
          key={"CANCEL"}
          size="small"
          variant="outlined"
          onClick={() => {
            onClose();
            setNewAssociateUsers([]);
          }}
        >
          Cancel
        </Button>

        <Button
          key={"ADD"}
          size="small"
          variant={newAssociateUsers?.length === 0 ? "outlined" : "contained"}
          className="btn-ml"
          onClick={() => {
            setGroupDetail({
              ...groupDetail,
              associateUsers: [
                ...newAssociateUsers?.map((user) => ({
                  ...user,
                  status: "Draft",
                })),
                ...groupDetail?.associateUsers,
              ],
            });
            onClose();
            setNewAssociateUsers([]);
          }}
          style={{ textTransform: "capitalize" }}
          disabled={newAssociateUsers?.length === 0}
        >
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const GroupAssociateUsers = ({
  groupDetail,
  setGroupDetail,
  getGroupInfoById,
  load,
  setLoad,
  params,
}) => {
  const classes = useStyle();
  const [openAssociateUserDialog, setOpenAssociateUserDialog] = useState(false);
  const [deletingAssociateUser, setDeletingAssociateUser] = useState(null);
  const dispatch = useDispatch();

  //<-- Functions and variables for ReusablePromptBox *promptAction_Functions -->
  const [promptBoxState, setPromptBoxState] = useState({
    open: false,
    type: "",
    redirectOnClose: true,
    message: "",
    title: "",
    severity: "",
  });
  const [promptBoxScenario, setPromptBoxScenario] = useState("");

  const promptAction_Functions = {
    handleClosePromptBox: () => {
      setPromptBoxState((prev) => ({
        open: false,
        type: "",
        redirectOnClose: false,
        message: "",
        title: "",
        severity: "",
      }));
      getGroupInfoById();
      setPromptBoxScenario("");
    },
    handleOpenPromptBox: (ref, data = {}) => {
      // SUCCESS,FAILURE
      let initialData = {
        open: true,
        title: "",
        message: "",
        okButton: true,
        cancelButton: true,
        okButtonText: "Ok",
        cancelText: "Cancel",
        type: "dialog",
      };
      if (ref === "SUCCESS") {
        initialData.type = "snackbar";
      }
      setPromptBoxScenario(ref);
      setPromptBoxState({
        ...initialData,
        ...data,
      });
    },
    handleCloseAndRedirect: () => {
      promptAction_Functions.handleClosePromptBox();
    },
    getCancelFunction: () => {
      switch (promptBoxScenario) {
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getCloseFunction: () => {
      switch (promptBoxScenario) {
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getOkFunction: () => {
      switch (promptBoxScenario) {
        case "DELETE":
          return deleteAssociateUser(deletingAssociateUser);
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getCloseAndRedirectFunction: () => {
      if (!promptBoxState.redirectOnClose) {
        return promptAction_Functions.handleClosePromptBox;
      }
      return promptAction_Functions.handleCloseAndRedirect;
    },
  };

  const deleteAssociateUser = (associateUser) => {
    console.log(associateUser)
    if (associateUser?.status === "Draft") {
      setGroupDetail({
        ...groupDetail,
        associateUsers: groupDetail?.associateUsers?.filter(
          (user) => user?.emailId !== associateUser?.emailId
        ),
      });
    } else {
      setLoad(true);
      const deleteAssociateUserUrl = `/${destination_IWA}/api/v1/groups/modify`;
      const deleteAssociateUserPayload = {
        id: Number(params?.groupId),
        name: groupDetail?.name,
        applicationId: groupDetail?.applicationId?.join(","),
        userIdList:
          groupDetail?.associateUsers
            ?.filter((user) => user?.emailId !== associateUser?.emailId)
            ?.map((user) => user?.emailId)
            .join(",") || "",
        roleIdList:
          groupDetail?.assignedRoles
            ?.map((role) => Number(role?.id))
            .join(",") || "",
      };
      const deleteAssociateUserRequestParam = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(deleteAssociateUserPayload),
      };
      fetch(deleteAssociateUserUrl, deleteAssociateUserRequestParam)
        .then((res) => res.json())
        .then((data) => {
          if (data.statusCode === 201) {
            promptAction_Functions.handleOpenPromptBox("SUCCESS", {
              message: `User deleted successfully`,
              redirectOnClose: false,
            });
          } else {
            promptAction_Functions.handleOpenPromptBox("ERROR", {
              title: "Failed",
              message: `User deletion failed`,
              severity: "danger",
              cancelButton: false,
            });
          }

          setLoad(false);
          setGroupDetail({
            ...groupDetail,
            associateUsers:
              groupDetail?.associateUsers?.filter(
                (user) => user?.emailId !== associateUser?.emailId
              ) || [],
          });
          setDeletingAssociateUser(null);
          getGroupInfoById();

          // dispatch(
          //   setResponseMessage({
          //     open: true,
          //     status: data?.status ? "success" : "error",
          //     message: data?.status
          //       ? "Associated user to group deleted successfully"
          //       : "Something went wrong",
          //   })
          // );
        })
        .catch((err) => {
          setLoad(false);
        });
    }
  };

  const associateUsersColumns = [
    {
      field: "userId",
      headerName: "Name",
      hide: true,
      flex: 1,
    },
    {
      field: "displayName",
      headerName: "Name",
      flex: 1,
    },

    // {
    //   field: "action",
    //   headerName: "Action",
    //   sortable: false,
    //   filterable: false,
    //   flex: 1,
    //   align: "center",
    //   headerAlign: "center",
    //   renderCell: (cellValues) => {
    //     let associateUser = cellValues.row;
    //     return (
    //       <Tooltip
    //         title={associateUser?.status === "Draft" ? "Remove" : "Delete"}
    //       >
    //         <IconButton
    //           onClick={(e) => {
    //             e.stopPropagation();
    //             // deleteAssociateUser(associateUser);
    //             setDeletingAssociateUser(associateUser);
    //             promptAction_Functions.handleOpenPromptBox("DELETE", {
    //               title: "Confirm Delete",
    //               message: `Do you want to delete the user?`,
    //               severity: "warning",
    //               cancelButton: true,
    //               okButton: true,
    //               okButtonText: "Delete",
    //             });
    //           }}
    //           disabled={load}
    //         >
    //           {associateUser?.status === "Draft" ? (
    //             <Remove style={{ fontSize: 16 }} />
    //           ) : (
    //             <DeleteOutlinedIcon color="danger" />
    //           )}
    //         </IconButton>
    //       </Tooltip>
    //     );
    //   },
    // },
  ];

  return (
    <>
      <NewGroupAssociateUsers
        open={openAssociateUserDialog}
        onClose={() => setOpenAssociateUserDialog(false)}
        groupDetail={groupDetail}
        setGroupDetail={setGroupDetail}
        params={params}
      />

      <ReusablePromptBox
        type={promptBoxState.type}
        promptState={promptBoxState.open}
        setPromptState={promptAction_Functions.handleClosePromptBox}
        onCloseAction={promptAction_Functions.getCloseFunction()}
        promptMessage={promptBoxState.message}
        dialogSeverity={promptBoxState.severity}
        dialogTitleText={promptBoxState.title}
        handleCancelButtonAction={promptAction_Functions.getCancelFunction()}
        cancelButtonText={promptBoxState.cancelText} //Cancel button display text
        showCancelButton={promptBoxState.cancelButton} //Enable Cancel button
        handleSnackBarPromptClose={promptAction_Functions.getCloseAndRedirectFunction()}
        handleOkButtonAction={promptAction_Functions.getOkFunction}
        okButtonText={promptBoxState.okButtonText}
        showOkButton={promptBoxState.okButton}
      />

      <ReusableTable
        width="100%"
        status_onRowSingleClick={false}
        rows={
          groupDetail?.associateUsers ?? []
        }
        columns={associateUsersColumns}
        getRowIdValue={"userId"}
        stopPropagation_Column={"action"}
        hideFooter={false}
        noOfColumns={5}
        rowsPerPageOptions={[5, 10, 15]}
        checkboxSelection={false}
        disableSelectionOnClick={false}
      />

      {/* <div style={{ display: "flex", justifyContent: "flex-end" }}>
        <Button
          size="small"
          variant="contained"
          sx={{ margin: "1rem 0" }}
          onClick={() => setOpenAssociateUserDialog(true)}
          startIcon={<Add />}
          disabled={load}
        >
          Add User
        </Button>
      </div> */}
    </>
  );
};

const NewGroupAssignedRoles = ({
  open,
  onClose,
  groupDetail,
  setGroupDetail,
  params,
}) => {
  const classes = useStyle();
  const basicReducerState = useSelector((state) => state.userManagement);
  const [newAssignedRoles, setNewAssignedRoles] = useState([]);

  const presentRole = (p_role) => {
    return groupDetail?.assignedRoles?.filter(
      (role) => Number(role?.id) === Number(p_role.id)
    )?.length > 0
      ? true
      : false;
  };
  const getApplicationNameById = (applicationId) => {
    const application = findApplicationById(
      Number(applicationId),
      basicReducerState?.applications
    );
    return application?.name || "-";
  };

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose}>
      <DialogTitle className={classes.newGroupAssignedRolesDialogTitle}>
        New Assigned Roles
      </DialogTitle>

      <DialogContent>
        <Autocomplete
          multiple
          size="small"
          disableCloseOnSelect
          filterSelectedOptions
          style={{ fontSize: 12 }}
          value={newAssignedRoles}
          onChange={(e, roles) => {
            setNewAssignedRoles(roles || []);
          }}
          options={basicReducerState?.roles?.filter(
            (role) =>
              // groupDetail?.applicationId?.find(
              //   (id) => Number(id) === Number(role?.applicationId)
              // ) &&
              // Number(role?.applicationId) ===
              //   Number(groupDetail?.applicationId) &&
              !presentRole(role)
          )}
          groupBy={(option) => getApplicationNameById(option.applicationId)}
          getOptionLabel={(option) => option.name}
          renderOption={(props, option, { selected }) => (
            <li {...props}>
              <Checkbox
                icon={<CheckBoxOutlineBlank fontSize="small" />}
                checkedIcon={<CheckBox color="primary" fontSize="small" />}
                checked={selected}
              />
              <Typography style={{ fontSize: 12 }}>{option.name}</Typography>
            </li>
          )}
          renderInput={(params) => (
            <TextField
              {...params}
              variant="standard"
              style={{ fontSize: 12 }}
              label="Assigned Roles"
            />
          )}
        />
      </DialogContent>

      <DialogActions className={classes.newGroupAssignedRolesDialogActions}>
        <Button
          key={"CANCEL"}
          size="small"
          variant="outlined"
          onClick={() => {
            onClose();
            setNewAssignedRoles([]);
          }}
        >
          Cancel
        </Button>

        <Button
          key={"ADD"}
          size="small"
          variant={newAssignedRoles?.length === 0 ? "outlined" : "contained"}
          className="btn-ml"
          onClick={() => {
            setGroupDetail({
              ...groupDetail,
              assignedRoles: [
                ...newAssignedRoles?.map((role) => ({
                  ...role,
                  status: "Draft",
                })),
                ...groupDetail?.assignedRoles,
              ],
            });
            onClose();
            setNewAssignedRoles([]);
          }}
          style={{ textTransform: "capitalize" }}
          disabled={newAssignedRoles?.length === 0}
        >
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const GroupAssignedRoles = ({
  groupDetail,
  setGroupDetail,
  load,
  setLoad,
  params,
}) => {
  const classes = useStyle();
  const dispatch = useDispatch();
  const [openAssignRoleDialog, setOpenAssignRoleDialog] = useState(false);
  const [deletingAssignedRole, setDeletingAssignedRole] = useState(null);

  const deleteAssignedRole = (assignedRole) => {
    if (assignedRole?.status === "Draft") {
      setGroupDetail({
        ...groupDetail,
        assignedRoles: groupDetail?.assignedRoles?.filter(
          (user) => user?.emailId !== assignedRole?.emailId
        ),
      });
    } else {
      setLoad(true);
      var applicationIds = {};
      groupDetail?.assignedRoles
        ?.filter((role) => Number(role?.id) !== Number(assignedRole?.id))
        ?.map((role) => {
          applicationIds[role?.applicationId] = 1;
          return null;
        });
      const deleteAssignedRoleUrl = `/${destination_IWA}/api/v1/groups/modify`;
      const deleteAssignedRolePayload = {
        id: Number(params?.groupId),
        name: groupDetail?.name,
        applicationId: Object?.keys(applicationIds)?.join(","),
        userIdList:
          groupDetail?.associateUsers?.map((user) => user?.emailId).join(",") ||
          "",
        roleIdList:
          groupDetail?.assignedRoles
            ?.filter((role) => Number(role?.id) !== Number(assignedRole?.id))
            ?.map((role) => Number(role?.id))
            .join(",") || "",
      };
      const deleteAssignedRoleRequestParam = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(deleteAssignedRolePayload),
      };
      fetch(deleteAssignedRoleUrl, deleteAssignedRoleRequestParam)
        .then((res) => res.json())
        .then((data) => {
          setGroupDetail({
            ...groupDetail,
            assignedRoles:
              groupDetail?.assignedRoles?.filter(
                (role) => Number(role?.id) !== Number(assignedRole?.id)
              ) || [],
          });
          setDeletingAssignedRole(null);
          setLoad(false);

          dispatch(
            setResponseMessage({
              open: true,
              status: data?.status ? "success" : "error",
              message: data?.status
                ? "Assigned role of group deleted successfully"
                : "Something went wrong",
            })
          );
        })
        .catch((err) => {
          setLoad(false);
        });
    }
  };

  return (
    <>
      <NewGroupAssignedRoles
        open={openAssignRoleDialog}
        onClose={() => setOpenAssignRoleDialog(false)}
        groupDetail={groupDetail}
        setGroupDetail={setGroupDetail}
        params={params}
      />

      <DeletionMessageBox
        open={deletingAssignedRole ? true : false}
        onClose={() => setDeletingAssignedRole(null)}
        onDelete={() => {
          deleteAssignedRole(deletingAssignedRole);
        }}
        load={load}
      />

      <TableContainer
        className={`${classes.groupAssignedRolesTableContainer} iagScroll`}
      >
        <Table size="small">
          <TableHead className={classes.groupAssignedRolesTableHead}>
            <TableRow>
              <TableCell className={classes.groupAssignedRolesTableHeadCell}>
                Name
              </TableCell>

              <TableCell
                align="center"
                className={classes.groupAssignedRolesTableHeadCell}
              >
                Action
              </TableCell>
            </TableRow>
          </TableHead>

          <TableBody
            className={`${classes.groupAssignedRolesTableBody} iagScroll`}
          >
            {groupDetail?.assignedRoles?.map((assignedRole, index) => {
              return (
                <TableRow
                  key={`${assignedRole?.id}-${index}`}
                  className={classes.groupAssignedRolesTableBodyRow}
                >
                  <TableCell
                    className={classes.groupAssignedRolesTableBodyCell}
                  >
                    {assignedRole?.name}
                  </TableCell>

                  <TableCell
                    align="center"
                    className={classes.groupAssignedRolesTableBodyCell}
                  >
                    <Tooltip
                      title={
                        assignedRole?.status === "Draft" ? "Remove" : "Delete"
                      }
                    >
                      <IconButton
                        onClick={(e) => {
                          e.stopPropagation();
                          // deleteAssignedRole(assignedRole);
                          setDeletingAssignedRole(assignedRole);
                        }}
                        disabled={load}
                      >
                        {assignedRole?.status === "Draft" ? (
                          <Remove style={{ fontSize: 16 }} />
                        ) : (
                          <DeleteOutlinedIcon color="danger" />
                        )}
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      <Button
        size="small"
        variant="contained"
        sx={{
          margin: "1rem 0",
        }}
        onClick={() => setOpenAssignRoleDialog(true)}
        startIcon={<Add />}
        disabled={load}
      >
        Add
      </Button>
    </>
  );
};

function GroupDetail({ params, setParams, openPromptBox }) {
  const classes = useStyle();
  let masterData = useSelector((state) => state.masterData);
  const [suppliersData, setsuppliersData] = useState({
    codeList: [],
    dataMap: {},
  });
  const basicReducerState = useSelector((state) => state.userManagement);
  const [selectedGroupDetailContentType, setSelectedGroupDetailContentType] =
    useState();
  const [load, setLoad] = useState(false);
  const [groupDetail, setGroupDetail] = useState({
    companyCode: "",
    supplierId: "",
    purchasingGroupId: "",
  });
  const dispatch = useDispatch();
  const [value, setValue] = useState("Basic Info");
  const [purchasingGroups, setPurchasingGroups] = useState({});

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };
  const getPurchGroupDetails = () => {
    // if (userData?.supplierId) {
    //   setVendorDetailsSet({
    //     [userData?.supplierId]: userData?.supplierName,
    //   });
    // } else {

    // if (moduleFilter?.companyCode !== "") {
    const hSuccess = (data) => {
      setPurchasingGroups(data.data);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_Po}/Odata/purchasingGroup`, "get", hSuccess, hError);
    // }
    // }
  };
  useEffect(() => {
    if (params?.groupId) {
      getGroupInfoById();
    }
  }, [params?.groupId]);
  useEffect(() => {
    getPurchGroupDetails();
    doAjax(`/${destination_Po}/Odata/getAllSuppliers`, "get", (res) => {
      setsuppliersData({
        codeList: Object.keys(res),
        dataMap: res,
      });
    });
  }, []);
  const getGroupInfoById = () => {
    setLoad(true);
    const getGroupByIdUrl = `/${destination_IWA_NPI}/api/v1/groupsMDG/getGroupsByIdMDG?id=${params?.groupId}`;
    const getGroupByIdRequestParams = {
      headers: {
        "Content-Type": "application/json",
      },
    };
    fetch(getGroupByIdUrl, getGroupByIdRequestParams)
      .then((res) => res.json())
      .then((groupData) => {
        setGroupDetail({
          ...groupData?.data,
          // companyCode: groupData?.data?.companyCode?.split(" - ")?.[0],
          supplierId: groupData?.data?.supplierId,
          purchasingGroup: groupData?.data?.purchasingGroup,
          applicationId: groupData?.data?.applicationId?.split(","),
          associateUsers:
            groupData?.data?.userIdList?.length === 0
              ? []
              : groupData?.data?.userIdList
                  ?.split(",")
                  ?.map((emailId) =>
                    findUserById(emailId, basicReducerState?.users)
                  )
                  ?.filter((user) => user && user !== "null") || [],
          assignedRoles:
            groupData?.data?.roleIdList?.length === 0
              ? []
              : groupData?.data?.roleIdList
                  ?.split(",")
                  ?.map((roleId) =>
                    findRoleById(Number(roleId), basicReducerState?.roles)
                  )
                  .filter((role) => role && role !== "null") || [],
        });
        setLoad(false);
      })
      .catch((err) => {
        //   let groupData = {data:{
        //     "id": 30,
        //     "name": "test2",
        //     "applicationId": "",
        //     "userIdList": "<EMAIL>,<EMAIL>",
        //     "roleIdList": "",
        //     "updatedBy": "<EMAIL>",
        //     "updatedOn": "2023-07-18 09:22:00.*********",
        //     "companyCode": "",
        //     "supplierId": "",
        //     "purchasingGroup": "110 - PUR GRP GDT",
        //     "getUsersCountPerGroup": 0
        // }}
        //   setGroupDetail({
        //     ...groupData?.data,
        //     companyCode: groupData?.data?.companyCode?.split(" - ")?.[0],
        //     supplierId: groupData?.data?.supplierId?.split(" - ")?.[0],
        //     purchasingGroup:groupData?.data?.purchasingGroup?.split(" - ")?.[0],
        //     applicationId: groupData?.data?.applicationId?.split(","),
        //     associateUsers:
        //       groupData?.data?.userIdList?.length === 0
        //         ? []
        //         : groupData?.data?.userIdList
        //             ?.split(",")
        //             ?.map((emailId) =>
        //               findUserById(emailId, basicReducerState?.users)
        //             )
        //             ?.filter((user) => user && user !== "null") || [],
        //     assignedRoles:
        //       groupData?.data?.roleIdList?.length === 0
        //         ? []
        //         : groupData?.data?.roleIdList
        //             ?.split(",")
        //             ?.map((roleId) =>
        //               findRoleById(Number(roleId), basicReducerState?.roles)
        //             )
        //             .filter((role) => role && role !== "null") || [],
        //   });
        setLoad(false);
      });
  };
  const updateGroupInfo = () => {
    setLoad(true);
    var applicationIds = {};
    groupDetail?.assignedRoles?.map((role) => {
      applicationIds[role?.applicationId] = 1;
      return null;
    });
    const updateGroupInfoUrl = `/${destination_IWA_NPI}/api/v1/groupsMDG/modifyGroupMDG`;
    const updateGroupInfoPayload = {
      id: Number(params?.groupId),
      name: groupDetail?.name,
      applicationId: Object?.keys(applicationIds)?.join(","),
      userIdList:
        groupDetail?.associateUsers?.map((user) => user?.emailId).join(",") ||
        "",
      roleIdList:
        groupDetail?.assignedRoles?.map((role) => Number(role?.id)).join(",") ||
        "",
      purchasingGroup: groupDetail.purchasingGroup
        ? `${groupDetail.purchasingGroup} - ${
            purchasingGroups[groupDetail.purchasingGroup]
          }`
        : "",

      companyCode: groupDetail.companyCode
        ? `${groupDetail.companyCode} - ${
            masterData.companyCode[groupDetail.companyCode]
          }`
        : "",
      supplierId: groupDetail.supplierId
        ? `${groupDetail.supplierId} - ${
            suppliersData.dataMap[groupDetail.supplierId]
          }`
        : "",
      updatedBy: basicReducerState.userData.emailId,
    };
    const updateGroupInfoRequestParams = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateGroupInfoPayload),
    };
    fetch(updateGroupInfoUrl, updateGroupInfoRequestParams)
      .then((res) => res.json())
      .then((data) => {
        openPromptBox("SUCCESS", {
          message: `Group Updated Successfully`,
          redirectOnClose: false,
        });
        setGroupDetail({
          ...groupDetail,
          associateUsers:
            groupDetail?.associateUsers?.map((user) => ({
              ...user,
              status: "Active",
            })) || [],
          assignedRoles:
            groupDetail?.assignedRoles?.map((role) => ({
              ...role,
              status: "Active",
            })) || [],
        });
        dispatch(
          setGroups(
            basicReducerState?.groups?.map((group) =>
              Number(group?.id) === Number(params?.groupId)
                ? { ...group, name: groupDetail?.name }
                : { ...group }
            ) || []
          )
        );
        setLoad(false);

        dispatch(
          setResponseMessage({
            open: true,
            status: data?.status ? "success" : "error",
            message: data?.status
              ? "Group details updated successfully"
              : "Something went wrong",
          })
        );
      })
      .catch((err) => {
        setLoad(false);
      });
  };

  return (
    <Paper sx={{ height: "max-content" }}>
      <Loading load={load} />

      <>
        <TabContext value={value}>
          <Box
            sx={{
              borderBottom: 1,
              borderColor: "divider",
              padding: "0px 12px 0px",
              position: "relative",
            }}
          >
            <Box
              sx={{
                position: "absolute",
                right: "1rem",
                top: "1rem",
                zIndex: "10",
              }}
            >
              <IconButton
                onClick={() => {
                  setParams({});
                }}
              >
                <Close style={{ fontSize: 16, cursor: "pointer" }} />
              </IconButton>
            </Box>
            <TabList onChange={handleChange} aria-label="basic tabs example">
              <Tab
                label={
                  <Stack
                    direction="row"
                    sx={{
                      alignItems: "center",
                    }}
                  >
                    <InfoIcon sx={{ fontSize: "15px" }} />
                    <Typography
                      variant="body1"
                      ml={1}
                      sx={{ fontWeight: 600, fontSize: "14px" }}
                    >
                      Basic Info
                    </Typography>
                  </Stack>
                }
                value="Basic Info"
                sx={{ textTransform: "none", fontWeight: "bold" }}
              />

              <Tab
                label={
                  <Stack
                    direction="row"
                    sx={{
                      alignItems: "center",
                    }}
                  >
                    <PeopleIcon sx={{ fontSize: "15px" }} />
                    <Typography
                      variant="body1"
                      ml={1}
                      sx={{ fontWeight: 600, fontSize: "14px" }}
                    >
                      Associated Users
                    </Typography>
                  </Stack>
                }
                value="Associated Users"
                sx={{ textTransform: "none", fontWeight: "bold" }}
              />
            </TabList>
            <TabPanel
              value={"Associated Users"}
              sx={{ padding: "0px", minHeight: "22rem" }}
            >
              <Stack sx={{ paddingTop: ".5rem" }}>
                <GroupAssociateUsers
                  getGroupInfoById={getGroupInfoById}
                  groupDetail={groupDetail}
                  setGroupDetail={setGroupDetail}
                  load={load}
                  setLoad={setLoad}
                  params={params}
                />
              </Stack>
            </TabPanel>
            <TabPanel value={"Basic Info"} sx={{ padding: "0px" }}>
              <Stack sx={{ paddingTop: ".5rem" }}>
                <GroupInfo
                  groupDetail={groupDetail}
                  setGroupDetail={setGroupDetail}
                  params={params}
                  load={load}
                />
              </Stack>
            </TabPanel>
          </Box>
        </TabContext>
        {/* {selectedGroupDetailContentType === "Assigned Roles" && (
          <GroupAssignedRoles
            groupDetail={groupDetail}
            setGroupDetail={setGroupDetail}
            load={load}
            setLoad={setLoad}
            params={params}
          />
        )} */}

        <div className={classes.groupDetailFooter}>
          {/* <Button
            size="small"
            variant={
              load ||
              value === "Basic Info" ||
              basicReducerState?.groups?.find(
                (group) =>
                  group?.name === groupDetail?.name &&
                  group?.applicationId === groupDetail?.applicationId &&
                  Number(group?.id) !== Number(params?.groupId)
              ) ||
              groupDetail?.name?.length === 0
                ? "outlined"
                : "contained"
            }
            onClick={updateGroupInfo}
            disabled={
              load ||
              value === "Basic Info" ||
              basicReducerState?.groups?.find(
                (group) =>
                  group?.name === groupDetail?.name &&
                  group?.applicationId === groupDetail?.applicationId &&
                  Number(group?.id) !== Number(params?.groupId)
              ) ||
              groupDetail?.name?.length === 0
            }
          >
            Submit
          </Button> */}
        </div>
      </>
    </Paper>
  );
}

export default GroupDetail;
