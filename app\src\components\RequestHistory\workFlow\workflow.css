/* --- Base Styles --- */
.container {
  padding: 20px;
  margin: auto;
  color: #333;
  width: 90%;
}

h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
  font-weight: 700;
  text-align: center;
}

.subtitle {
  text-align: center;
  color: #7f8c8d;
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto 30px;
  line-height: 1.6;
}

.stat-number {
  font-size: 2.2rem;
  font-weight: 700;
}

.stat-label {
  font-size: 0.9rem;
  color: #7f8c8d;
  font-weight: 500;
}


.group-title {
  font-size: 1.4rem;
  font-weight: 600;
}

.group-info {
  font-size: 0.9rem;
  opacity: 0.9;
  margin-top: 5px;
}

.toggle-icon {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.group-card.collapsed .toggle-icon {
  transform: rotate(180deg);
}

.workflow-content {
  padding: 25px;
}

/* --- Workflow Diagram --- */
.workflow-diagram {
  display: flex;
  position: relative;
  overflow-x: auto;
  padding: 40px 0;
  min-height: 200px;
}

.level {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 220px;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

.level-label {
  background: #ecf0f1;
  color: #2c3e50;
  padding: 8px 15px;
  border-radius: 30px;
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 20px;
  text-align: center;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
  min-width: 120px;
}

.tasks-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.task {
  background: white;
  border-left: 4px solid #3498db;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: transform 0.1s ease;
}

.task:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.task-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.task-icon {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
}

.task-name {
  font-weight: 600;
  font-size: 0.95rem;
  color: #2c3e50;
  flex: 1;
}

.task-sla {
  background: #e1f0fa;
  color: #3498db;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* --- Modal --- */
.modal {
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 20px;
  width: 90%;
  max-width: 500px;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.modal-header {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  padding: 20px;
  position: relative;
}

.modal-title {
  font-size: 1.4rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.2);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}

.modal-body {
  padding: 25px;
}

.task-detail {
  display: flex;
  margin-bottom: 18px;
  padding-bottom: 18px;
  border-bottom: 1px solid #eee;
}

.detail-label {
  font-weight: 600;
  color: #7f8c8d;
  width: 120px;
  flex-shrink: 0;
}

.detail-value {
  flex: 1;
  color: #2c3e50;
  font-weight: 500;
}

.sla-badge {
  display: inline-block;
  padding: 5px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.85rem;
}

.sla-normal {
  background: #e1f0fa;
  color: #3498db;
}

/* --- SVG Connectors --- */
.svg-connectors {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}
