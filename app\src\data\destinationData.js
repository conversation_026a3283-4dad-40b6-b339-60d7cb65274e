const destinationData = {
  APPLICATION_NAME: "1783",
  CRUD_API_ENV: "itm_dev",
  SERVICE_BASE_URL: [
    {
      Description: "",
      Name: "ITMJavaServices",
      URL: "https://coreservices.cherryworkproducts.com",
    },
    {
      Description: "",
      Name: "ConfigServer",
      URL: "https://configservice.cherryworkproducts.com",
    },
    {
      Description: "",
      Name: "WorkNetServices",
      URL: "https://wnservices.cherryworkproducts.com",
    },
    {
      Description: "",
      Name: "CrudApiServices",
      URL: "https://crudservicesdev.cherryworkproducts.com",
    },
    {
      Description: "",
      Name: "WorkFormsServices",
      URL: "https://wfservicesdev.cherryworkproducts.com/workforms",
    },
    {
      Description: "",
      Name: "WorkCollabServices",
      URL: "https://workcollab-java-dev.cfapps.eu10.hana.ondemand.com",
    },
    {
      Description: "",
      Name: "NativeWorkflowServices",
      URL: "https://customwfdev.cherryworkproducts.com",
    },
  ],
};

export default destinationData;
