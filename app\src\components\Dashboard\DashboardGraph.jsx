import { Box, Card, CardContent, Checkbox, Dialog, DialogActions, DialogContent, DialogTitle, FormControl, Grid, IconButton, ListItemText, MenuItem, Select, Stack, Typography } from "@mui/material";

import React, { useEffect, useState } from "react";
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Line,
  LineChart,
  Tooltip,
  XAxis,
  YAxis,
  Text,
  ResponsiveContainer,
} from "recharts";

import "./Dashboard.css";
import LegendBox from "./LegendBox";
import TooltipMaterialByPurchase from "./TooltipMaterialByPurchase";

import DeliveryTooltip from "./DeliveryTooltip";
import CommonTooltip from "./CommonTooltip";
import ConsumptionTooltip from "./ConsumptionTooltip";
import { useNavigate } from "react-router-dom";
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import CloseIcon from "@mui/icons-material/Close";
import { useDispatch, useSelector } from "react-redux";

import { font_Small } from "../common/commonStyles";
import { doAjax } from "../Common/fetchService";
import { destination_Dashboard } from "../../destinationVariables";
import { commonFilterUpdate } from "../../app/commonFilterSlice";
import { Button } from "rsuite";


const DashboardGraph = ({
  handleSearch,
  header,
  graphDataSet,
  xaxisData,
  yaxisData,
  bar,
  line,
  tooltip_Content,
  yaxisHeader = "",
  type,
  multi = false,
  horizontal = false,
  grouped = false,
  stackedBar = false,
  groupBar = false,
  isDrillDown = false,
  lineCycle = false,
  legendList = ["", "", ""],
  id
}) => {
  let matflag = false;
  if (type == "ops") {
    matflag = true;
  }
  const data = graphDataSet;

  const [openFilterGroup, setOpenFilterGroup] = useState(false)
  const [openFilterRole, setOpenFilterRole] = useState(false)
  const [requestTypeOptions, setReqTypeOptions] = useState([])
  const [requestStatusOptionsDisabled, setRequestStatusOptionsDisabled] = useState(false)
  const [requestTypeOptionsDisabled, setRequestTypeOptionsDisabled] = useState(false)
  const dashboardSearchForm = useSelector(
    (state) => state?.commonFilter["Dashboard"]
  );

  const dispatch = useDispatch();

  console.log(dashboardSearchForm.selectedRequestType, "dashboardSearchForm")
  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };

  const legendData = [{}];
  const barColors = [
    " #b6af70",
    "#6769b6",
    "#fc8d8b",
    "#dab174",
    "#8ec590",
    "#69c1d1",
    "#b6af70",
  ];

  useEffect(() => {

    getRequestType()


  }, []);

  const getRequestType = () => {
    const hSuccess3 = (data) => {
      let req_type_arr = data.body
      console.log(req_type_arr, "req_type_arr")
      setReqTypeOptions(req_type_arr)

    };

    const hError3 = () => { };
    doAjax(
      `/${destination_Dashboard}/GraphConfig/getReqTypes?module=${dashboardSearchForm?.dashBoardModuleName ? dashboardSearchForm?.dashBoardModuleName : "Cost Center"}`,
      "get",
      hSuccess3,
      hError3,

    );
  }


  /*const CustomTooltip = ({ active, payload }) => {
    if (active) {
      const data = payload[0].payload;
      return (
        <div
          style={{
            background: "white",
            border: "1px solid #ccc",
            padding: "10px",
          }}
        >
          <p>
            <strong>Status: {data.status}</strong>
          </p>
          <p>Create Requests: {data.requestCreate}</p>
          
          <p>Change Requests: {data.requestChange}</p>
          <p>Extend Requests: {data.requestExtend}</p>
        </div>
      );
    }
    return null;
  };*/
  const CustomTooltip2 = ({ active, payload }) => {
    if (active) {
      const data = payload[0].payload;
      return (
        <div
          style={{
            background: "white",
            border: "1px solid #ccc",
            padding: "10px",
          }}
        >
          <p>
            <strong>Status: {data.processName}</strong>
          </p>
          <p>Create Requests: {data.createCount}</p>
          <p>Extend Requests: {data.extendCount}</p>
          <p>Change Requests: {data.changeCount}</p>
        </div>
      );
    }
    return null;
  };
  /*const legendDataStackedBar = [
    {},
    { name: "Create Requests", col: "#59C2E3" },
    
    { name: "Change Requests", col: "#C3F0FE" },
    { name: "Extend Requests", col: "#FFFDCC" },
  ];*/
  let legendDataStackedBar = []
  data?.map((stackbar_item) => {
    let stack_hash = {}
    if (id === 'Roles') {
      stack_hash["name"] = stackbar_item.role
      stack_hash["col"] = '#59C2E3'
    } else {
      stack_hash["name"] = stackbar_item.Group
      stack_hash["col"] = '#59C2E3'
    }
    legendDataStackedBar.push(stack_hash)
  })
  console.log(legendDataStackedBar, "legendDataStackedBar")
  const legendDatagroupBar = [
    {},
    { name: "Create Requests", col: "#59C2E3" },
    { name: "Extend Requests", col: "#FFFDCC" },
    { name: "Change Requests", col: "#C3F0FE" },
  ];

  for (let i = 0; i < data?.length; i++) {
    legendData?.push({ name: data[i][xaxisData], col: barColors[i] });
  }
  const navigate = useNavigate();
  let controller_ToolTip = (ref = "404") => {
    switch (ref) {
      case "tooltipmaterialbypurchase":
        return <TooltipMaterialByPurchase />;

      case "deliveryDelays":
        return <DeliveryTooltip />;
      case "consumption":
        return <ConsumptionTooltip />;
      default:
        return (
          <CommonTooltip
            currency={(data?.length > 0 && data[0]?.currency) || yaxisHeader}
          />
        );
    }
  };
  const CustomizedTick = ({ x, y, payload, type }) => {
    const handleClick = () => {
      // Function to be executed on tick click
      if (type == "shipmentId") {
        navigate(`/purchaseOrder/ASN/details/${payload.value}`);
      } else if (type == "poNumber")
        navigate(
          `/purchaseOrder/management/singlePurchaseOrder/${payload.value}`
        );
      else {
        console.log("Tick clicked:", payload.value);
      }
    };



    return (
      <g transform={`translate(${x},${y})`}>
        <text
          className="customTick"
          x={0}
          y={0}
          dy={16}
          textAnchor="middle"
          onClick={handleClick}
          fontSize="9px"
        >
          {payload.value}
        </text>
      </g>
    );
  };

  console.log(stackedBar, "stackebar========")
  const openPopUpForFilterRolesRequestType = () => {
    //alert("coming")
    setOpenFilterRole(true)
  }
  const openPopUpForFilterGroupRequestType = () => {
    setOpenFilterGroup(true)
  }
  const handleReqTypeOption = (e, value) => {
    if (e.target.value !== null) {
      let temprequestType = e.target.value
      //console.log(tempusersId[0].split('-')[0],"tempusersId")
      //setSelectedRequestType(temprequestType)
      if (temprequestType.length > 0) {

        setRequestStatusOptionsDisabled(true)

        //setSelectedRequestStatus("All")

      } else {
        setRequestStatusOptionsDisabled(false)
      }
      dispatch(
        commonFilterUpdate({
          module: "Dashboard",
          filterData: {
            ...dashboardSearchForm,
            selectedRequestTypeInGroup: temprequestType,
            selectedRequestStatus: [],
          },
        })
      );

    }
  }
  const handleReqTypeOptionRole = (e, value) => {
    if (e.target.value !== null) {
      let temprequestType = e.target.value
      //console.log(tempusersId[0].split('-')[0],"tempusersId")
      //setSelectedRequestType(temprequestType)
      if (temprequestType.length > 0) {

        setRequestStatusOptionsDisabled(true)

        //setSelectedRequestStatus("All")

      } else {
        setRequestStatusOptionsDisabled(false)
      }
      dispatch(
        commonFilterUpdate({
          module: "Dashboard",
          filterData: {
            ...dashboardSearchForm,
            selectedRequestTypeRole: temprequestType,
            //selectedRequestStatus: [],
          },
        })
      );

    }
  }
  const handleCloseRequestTypeGroups = () => {
    setOpenFilterGroup(false)
  }
  const handleCloseRequestTypeRole = () =>{
    setOpenFilterRole(false)
  }
  
  const handleProceedRequestTypeGroups = () => {
    setOpenFilterGroup(false)
    handleSearch()
  }
  const handleProceedRequestTypeRole =() =>{
    setOpenFilterRole(false)
    handleSearch()
  }
  return (

    <>
      <Dialog
        open={openFilterGroup}
        //onClose={handleCloseRequestTypeGroups}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            // borderBottom: "1px solid grey",
            display: "flex",
          }}
        >
          <Typography variant="h6">Add Filter</Typography>

          <IconButton
            sx={{ width: "max-content" }}
            onClick={handleCloseRequestTypeGroups}
            children={<CloseIcon />}
          />
        </DialogTitle>
        <DialogContent>
          <Grid
            item
            md={6}
            sx={{ width: "100%", marginTop: ".5rem" }}
          >
            <Typography>
              REQUEST TYPE
            </Typography>
            <FormControl fullWidth
              //size="small"
              sx={{ margin: ".5em 0px", minWidth: "250px", maxWidth: "250px" }}
            >
              <Select
                placeholder={"Select Request Type"}
                multiple
                size="small"
                value={dashboardSearchForm?.selectedRequestTypeInGroup}
                name="moduleName"
                onChange={(e) => handleReqTypeOption(e)}
                //displayEmpty={true}
                // input={<OutlinedInput label="Tag" />}
                disabled={requestTypeOptionsDisabled}
                renderValue={(selected) => selected.join(", ")}
                MenuProps={MenuProps}
              >
                <MenuItem sx={font_Small} value={""}>
                  <div
                    style={{ color: "#C1C1C1", fontSize: "12px" }}
                  >
                    SELECT REQUEST TYPE
                  </div>
                </MenuItem>
                {requestTypeOptions?.map((name) => (
                  <MenuItem
                    //sx={font_Small}

                    key={name}
                    value={name}
                    style={{ fontSize: "12px !important" }}
                  >
                    <Checkbox
                      checked={dashboardSearchForm?.selectedRequestTypeInGroup.indexOf(name) > -1
                      }
                    />
                    <ListItemText
                      //sx={font_Small}
                      primary={name}
                      style={{ fontSize: "12px !important" }}
                    />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseRequestTypeGroups}>Cancel</Button>
          <Button onClick={handleProceedRequestTypeGroups} autoFocus>
            Proceed
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openFilterRole}
        //onClose={handleCloseRequestTypeGroups}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            // borderBottom: "1px solid grey",
            display: "flex",
          }}
        >
          <Typography variant="h6">Add Filter</Typography>

          <IconButton
            sx={{ width: "max-content" }}
            onClick={handleCloseRequestTypeRole}
            children={<CloseIcon />}
          />
        </DialogTitle>
        <DialogContent>
          <Grid
            item
            md={6}
            sx={{ width: "100%", marginTop: ".5rem" }}
          >
            <Typography>
              REQUEST TYPE
            </Typography>
            <FormControl fullWidth
              //size="small"
              sx={{ margin: ".5em 0px", minWidth: "250px", maxWidth: "250px" }}
            >
              <Select
                placeholder={"Select Request Type"}
                multiple
                size="small"
                value={dashboardSearchForm?.selectedRequestTypeRole}
                name="moduleName"
                onChange={(e) => handleReqTypeOptionRole(e)}
                //displayEmpty={true}
                // input={<OutlinedInput label="Tag" />}
                disabled={requestTypeOptionsDisabled}
                renderValue={(selected) => selected.join(", ")}
                MenuProps={MenuProps}
              >
                <MenuItem sx={font_Small} value={""}>
                  <div
                    style={{ color: "#C1C1C1", fontSize: "12px" }}
                  >
                    SELECT REQUEST TYPE
                  </div>
                </MenuItem>
                {requestTypeOptions?.map((name) => (
                  <MenuItem
                    //sx={font_Small}

                    key={name}
                    value={name}
                    style={{ fontSize: "12px !important" }}
                  >
                    <Checkbox
                      checked={dashboardSearchForm?.selectedRequestTypeRole.indexOf(name) > -1
                      }
                    />
                    <ListItemText
                      //sx={font_Small}
                      primary={name}
                      style={{ fontSize: "12px !important" }}
                    />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseRequestTypeRole}>Cancel</Button>
          <Button onClick={handleProceedRequestTypeRole} autoFocus>
            Proceed
          </Button>
        </DialogActions>
      </Dialog>
      <Card
        sx={{
          borderRadius: !multi ? "10px" : "0px",
          boxShadow: !multi ? "4" : "0",
          minHeight: "390px",
          maxHeight: "390px",

        }}
      >
        <CardContent className="abc">
          <Stack alignItems="center">
            {!multi && (
              <>
                <Stack
                  direction="row"
                  sx={{
                    alignItems: "center",
                  }}>
                    
                  <Typography variant="subtitle2" color="#1d1d1d">
                    <strong>{header}</strong>
                  </Typography>
                  {id === 'Roles' ?
                    <Typography >
                      <FilterAltIcon
                        sx={{
                          fontSize: "20px", cursor: "pointer",
                        }}
                        onClick={openPopUpForFilterRolesRequestType}
                      />
                    </Typography> : id === 'Groups' ?
                      <Typography>
                        <FilterAltIcon
                          sx={{
                            fontSize: "20px", cursor: "pointer",
                          }}
                          onClick={openPopUpForFilterGroupRequestType}
                        />
                      </Typography> : ''}

                </Stack>
              </>
            )}

            {/* <Box justifyContent="flex-start" className="usd">
           
          </Box> */}
            {!line && horizontal && (
              <>
                <Box>
                  {/* <Typography variant="subtitle2" fontWeight="normal" ml={6}>
              {yaxisHeader}
            </Typography> */}
                  <BarChart
                    width={300}
                    height={220}
                    data={data}
                    layout="vertical"
                  >
                    <XAxis hide axisLine={false} type="number" />
                    <YAxis
                      yAxisId={0}
                      dataKey={xaxisData}
                      type="category"
                      // axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: "11px", color: "black" }}
                    />
                    <YAxis
                      orientation="right"
                      yAxisId={1}
                      dataKey={yaxisData}
                      type="category"
                      axisLine={false}
                      tickLine={false}
                      tickFormatter={(value) => value.toLocaleString()}
                    //  mirror
                    />

                    {/* <CartesianGrid vertical={false} /> */}
                    <Tooltip
                      content={
                        tooltip_Content && controller_ToolTip(tooltip_Content)
                      }
                    />

                    <Bar dataKey={yaxisData} barSize={30} radius={10}>
                      {data.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={barColors[index]} />
                      ))}
                    </Bar>
                  </BarChart>

                  {/* <LegendBox
                content={legendData}
                margin={10}
                type={type}
                itemCount={4}
                mat={matflag}
              /> */}
                </Box>
              </>
            )}
            {grouped && (
              <>
                <>
                  <Typography variant="subtitle2" fontWeight="normal" mr={"79%"}>
                    Unit
                  </Typography>
                  <ResponsiveContainer width="95%" height={250}>
                    <BarChart data={data}>
                      <XAxis
                        dataKey={xaxisData}
                        tick={<CustomizedTick type={xaxisData} />}
                        interval={0}
                      />
                      <YAxis
                        type="number"
                        tick={{ fontSize: "15px" }}
                        domain={[0, "dataMax + 200"]}
                        dataKey={yaxisData?.obj1}

                      // tickFormatter={(value) =>
                      //   new Intl.NumberFormat("en-US", {
                      //     notation: "compact",
                      //     compactDisplay: "short",
                      //   }).format(value)
                      // }
                      />
                      <CartesianGrid vertical={false} />
                      {/* <Tooltip/> */}
                      <Tooltip
                        content={
                          tooltip_Content && controller_ToolTip(tooltip_Content)
                        }
                      />

                      <Bar dataKey={yaxisData?.obj1} barSize={50} radius={5}>
                        {data.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={"#ff5555"} />
                        ))}
                      </Bar>
                      <Bar dataKey={yaxisData?.obj2} barSize={50} radius={5}>
                        {data.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={"#a7eff3"} />
                        ))}
                      </Bar>
                      <Bar dataKey={yaxisData?.obj3} barSize={50} radius={5}>
                        {data.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={"#b1aade"} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                  <LegendBox
                    content={[
                      { name: "undefined" },
                      { name: legendList[0], col: "#ff5555" },
                      { name: legendList[1], col: "#a7eff3" },
                      { name: legendList[2], col: "#b1aade" },
                    ]}
                    margin={"30%"}
                    type={type}
                    itemCount={4}
                    mat={matflag}
                  />
                </>
              </>
            )}
            {id === 'Roles' && (
              <>
                <Box>
                  <Stack>
                    <Typography variant="subtitle2" fontWeight="normal" ml={6}>
                      {(data?.length > 0 && data[0]?.currency) || yaxisHeader}
                    </Typography>

                  </Stack>
                  <BarChart
                    width={550}
                    height={300}
                    data={data}
                    barSize={40}
                    //barCategoryGap={20}
                    barcategorygap="10%"
                  >
                    {/* <CartesianGrid strokeDasharray="3 3" /> */}
                    <XAxis
                      dataKey="role"
                      tick={{ fontSize: 9, fontFamily: "Roboto, sans-serif" }}
                    />
                    <YAxis
                      type="number"
                      tick={{ fontSize: "10px" }}
                    />
                    {/* <Tooltip content={<CustomTooltip />} /> */}
                    <Tooltip content={tooltip_Content} />
                    <Bar
                      dataKey="avgTime"
                      stackId="a"
                      fill="#59C2E3"
                      radius={[5, 5, 5, 5]}
                    />
                    {/* <Bar
                    dataKey="requestChange"
                    stackId="a"
                    fill="#C3F0FE"
                    radius={[5, 5, 5, 5]}
                  />
                  <Bar
                    dataKey="requestExtend"
                    stackId="a"
                    fill="#FFFDCC"
                    radius={[5, 5, 5, 5]}
                  /> */}

                  </BarChart>
                </Box>
                <Box sx={{ marginRight: "100", width: "100%" }}>
                  <div onClick={() => { }} style={{ pointerEvents: "none" }}>
                    <LegendBox
                      content={legendDataStackedBar}
                      margin={"20%"}
                      type={type}
                      itemCount={2}
                      mat={matflag}
                    />
                  </div>
                </Box>
              </>
            )}
            {id === 'Groups' && (
              <>
                <Box>
                  <Stack>
                    <Typography variant="subtitle2" fontWeight="normal" ml={6}>
                      {/* {(data?.length > 0 && data[0]?.currency) || yaxisHeader} */}
                      Count
                    </Typography>

                  </Stack>
                  <BarChart
                    width={550}
                    height={300}
                    data={data}
                    barSize={40}
                    //barCategoryGap={20}
                    barcategorygap="10%"
                  >
                    {/* <CartesianGrid strokeDasharray="3 3" /> */}
                    <XAxis
                      dataKey="role"
                      tick={{ fontSize: 9, fontFamily: "Roboto, sans-serif" }}
                    />
                    <YAxis
                      type="number"
                      tick={{ fontSize: "10px" }}
                    />
                    {/* <Tooltip content={<CustomTooltip />} /> */}
                    <Tooltip content={tooltip_Content} />
                    <Bar
                      dataKey="Count"
                      stackId="a"
                      fill="#59C2E3"
                      radius={[5, 5, 5, 5]}
                    />
                    {/* <Bar
                    dataKey="requestChange"
                    stackId="a"
                    fill="#C3F0FE"
                    radius={[5, 5, 5, 5]}
                  />
                  <Bar
                    dataKey="requestExtend"
                    stackId="a"
                    fill="#FFFDCC"
                    radius={[5, 5, 5, 5]}
                  /> */}

                  </BarChart>
                </Box>
                <Box sx={{ marginRight: "100px", width: "100%" }}>
                  <div onClick={() => { }} style={{ pointerEvents: "none" }}>
                    <LegendBox
                      content={legendDataStackedBar}
                      margin={"20%"}
                      type={type}
                      itemCount={2}
                      mat={matflag}
                    />
                  </div>
                </Box>
              </>
            )}
            {groupBar && !stackedBar && (
              <>

                <Box>
                  <Typography variant="subtitle2" fontWeight="normal" ml={6}>
                    {(data?.length > 0 && data[0]?.currency) || yaxisHeader}
                  </Typography>
                  <BarChart
                    width={900}
                    height={300}
                    data={data}
                    barSize={40}
                    //barCategoryGap={20}
                    barcategorygap="10%"
                  >
                    {/* <CartesianGrid strokeDasharray="3 3" /> */}
                    <XAxis
                      dataKey="status"
                      tick={{ fontSize: 9, fontFamily: "Roboto, sans-serif" }}
                    />
                    <YAxis
                      type="number"
                      tick={{ fontSize: "10px" }}
                    />
                    {/* <Tooltip content={<CustomTooltip />} /> */}
                    <Bar
                      dataKey="requestCreate"
                      // stackId="a"
                      fill="#59C2E3"
                      radius={[5, 5, 5, 5]}
                    />
                    <Bar
                      dataKey="requestExtend"
                      // stackId="a"
                      fill="#FFFDCC"
                      radius={[5, 5, 5, 5]}
                    />
                    <Bar
                      dataKey="requestChange"
                      // stackId="a"
                      fill="#C3F0FE"
                      radius={[5, 5, 5, 5]}
                    />
                  </BarChart>
                </Box>
              </>
            )}
            {!horizontal && !grouped && bar && (
              <>
                <Box>
                  <Typography variant="subtitle2" fontWeight="normal" ml={6}>
                    {(data?.length > 0 && data[0]?.currency) || yaxisHeader}
                  </Typography>
                  <BarChart
                    width={900}
                    height={isDrillDown ? 100 : 270}
                    data={data}
                  >
                    <XAxis dataKey={xaxisData} tick={false} />
                    <YAxis
                      type="number"
                      tick={{ fontSize: "10px" }}
                      domain={[0, "dataMax"]}
                      dataKey={yaxisData}

                    // tickFormatter={(value) =>
                    //   new Intl.NumberFormat("en-US", {
                    //     notation: "compact",
                    //     compactDisplay: "short",
                    //   }).format(value)
                    // }
                    />
                    <CartesianGrid vertical={false} />
                    <Tooltip
                      content={
                        tooltip_Content && controller_ToolTip(tooltip_Content)
                      }
                    />

                    <Bar dataKey={yaxisData} barSize={30} radius={10}>
                      {data.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={barColors[index]} />
                      ))}
                    </Bar>
                  </BarChart>
                  <div onClick={() => { }} style={{ pointerEvents: "none" }}>
                    <LegendBox
                      content={legendData}
                      margin={10}
                      type={type}
                      itemCount={3}
                      mat={matflag}
                    />
                  </div>
                </Box>
              </>
            )}

            {!grouped && line && (
              <Box mt={1} ml={-5}>
                <Typography variant="subtitle2" fontWeight="normal" ml={8}>
                  {(data?.length > 0 && data[0]?.currency) || yaxisHeader}
                </Typography>
                <LineChart
                  width={900}
                  height={isDrillDown ? 200 : 350}
                  data={data}
                  margin={{ top: 5, right: 10, left: 10, bottom: 60 }}
                >
                  <CartesianGrid vertical={false} />
                  <XAxis dataKey={xaxisData} tick={{ fontSize: "10px" }} />
                  <YAxis
                    dataKey={yaxisData}
                    type="number"
                    tick={{ fontSize: "10px" }}
                  />
                  <Tooltip
                    content={
                      tooltip_Content && controller_ToolTip(tooltip_Content)
                    }
                  />

                  <Line type="monotoneX" dataKey={yaxisData} stroke="#8884d8" />
                </LineChart>
              </Box>
            )}
            {/* {
            multiLine && (

            )
          } */}
          </Stack>
        </CardContent>
      </Card>
    </>
  );
};

export default DashboardGraph;
