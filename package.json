{"name": "mdgmatdev", "version": "1.0.0", "description": "A simple CAP project.", "repository": "<Add your repository here>", "license": "UNLICENSED", "private": true, "dependencies": {"@mui/icons-material": "^5.14.8", "@mui/lab": "^5.0.0-alpha.143", "@mui/material": "^5.14.8", "@mui/x-charts": "^6.0.0-alpha.10", "@mui/x-data-grid": "^5.17.2", "@progress/kendo-react-pdf": "^7.2.3", "@sap/cds": "^6", "antd": "^5.26.0", "chart.js": "^2.9.4", "express": "^4", "jspdf": "^2.5.1", "lucide-react": "^0.514.0", "pdf-lib": "^1.3.1", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^2.11.1", "react-router-dom": "^6.7.0", "react-to-pdf": "^1.0.1", "react-youtube": "^10.1.0", "recharts": "^2.8.0", "sweetalert2": "^11.21.2", "xlsx": "^0.18.5"}, "devDependencies": {"sqlite3": "^5.0.4"}, "scripts": {"start": "cds run"}}