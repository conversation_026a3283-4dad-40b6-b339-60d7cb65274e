import { createSlice } from "@reduxjs/toolkit";

let utilityInitialState = {
    redirection:{
      url_LastPath : null,
      history_Module: null,
      data:null
     },
     redirectionFilter:{
       data: null,
       targetModule: null,
       ref:null
     },
     commonHookMethods:{
      
     }
   }
   
   let utilityReducer = createSlice({
    name:'utilityReducers',
    initialState: utilityInitialState,
    reducers: {
     setHistoryPath(state, action){
         state.redirection.url_LastPath = action.payload.url;
         state.redirection.history_Module = action.payload.module;
         state.redirection.data = action.payload.data? action.payload.data : null;
         
         return state
     },
     clearLastPath(state,action){
       state.redirection.url_LastPath = null;
       state.redirection.history_Module = null;
       state.redirection.data =  null;
       return state;
     },
     setRedirectionFilter(state,action){
       state.redirectionFilter.data = action.payload.filter;
       state.redirectionFilter.targetModule = action.payload.targetModule;
       state.redirectionFilter.ref = action.payload.ref;

       return state;
     },
     clearRedirectionFilter(state,action){
       state.redirectionFilter.data = null;
       state.redirectionFilter.targetModule = null;
       state.redirectionFilter.ref = null;

       return state;
     },
     updateHookMethods(state,action){
      state.commonHookMethods = {
        ...state.commonHookMethods,
        ...action.payload
      }
     }
   
   }
})

export const {setHistoryPath,clearLastPath,setRedirectionFilter,clearRedirectionFilter,updateHookMethods} = utilityReducer.actions
export default utilityReducer.reducer