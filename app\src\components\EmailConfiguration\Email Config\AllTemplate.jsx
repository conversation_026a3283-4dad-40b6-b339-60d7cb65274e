import React, { useState } from "react";
import Stack from "@mui/material/Stack";
import Grid from "@mui/material/Grid";
import DisplayTemplateCard from "../utility/DisplayTemplateCard";
import { Paper, Skeleton } from "@mui/material";

function AllTemplate(props) {
  const [openTemplateDialog, setOpenTemplateDialog] = useState({});
  return (
    <>
      {props?.isLoading ? (
        <>
          <Grid
            container
            rowSpacing={{ xs: 2, sm: 2, md: 3, lg: 3 }}
            columnSpacing={{ xs: 2, sm: 2, md: 3 }}
          >
            {Array.from(Array(10)).map((_, index) => (
              <Grid
                item
                xs={2}
                sm={3}
                md={3}
                xl={3}
                key={index}
                height={"10rem"}
              >
                <Paper width={"100%"} sx={{ padding: "1rem" }}>
                  <Stack
                    direction="column"
                    spacing={1}
                    height={"100%"}
                    width={"100%"}
                    justifyContent="center"
                    alignItems="flex-start"
                  >
                    <Skeleton
                      variant="text"
                      sx={{ fontSize: "1rem" }}
                      width={200}
                      p={2}
                    />
                    <Skeleton
                      variant="rounded"
                      width={"80%"}
                      height={60}
                      p={2}
                    />
                    <Stack
                      direction="row"
                      spacing={2}
                      justifyContent="flex-start"
                      alignItems="center"
                      width={"100%"}
                    >
                      <Skeleton
                        variant="rounded"
                        width={"45%"}
                        height={24}
                        sx={{ borderRadius: "9px" }}
                      />
                      <Skeleton
                        variant="rounded"
                        width={"45%"}
                        height={24}
                        sx={{ borderRadius: "9px" }}
                      />
                    </Stack>
                  </Stack>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </>
      ) : (
        <Grid container rowSpacing={2} columnSpacing={{ xs: 2, sm: 2, md: 3 }}>
          {(props?.filteredData.length || props?.searchParam !== ""
            ? props?.filteredData
            : props?.emailTemplateData
          ).map((ele, index) => (
            <>
              <DisplayTemplateCard
                cardData={ele}
                index={index}
                setCreationType={props?.setCreationType}
                setSelectedRow={props?.setSelectedRow}
                setOpenCreateTemplate={props?.setOpenCreateTemplate}
                setOpenTemplateDialog={setOpenTemplateDialog}
                mailmappingData={props?.mailmappingData}
                groupList={props?.groupList}
                userList={props?.userList}
                setIsEditing={props?.setIsEditing}
                allGroups={props?.allGroups}
                headers={props?.headers}
                setScenario={props?.setScenario}
              />
            </>
          ))}
        </Grid>
      )}
    </>
  );
}

export default AllTemplate;
