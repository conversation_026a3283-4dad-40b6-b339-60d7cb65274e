import React, { useState,useMemo,useEffect } from "react";
import Stack from "@mui/material/Stack";
import Grid from "@mui/material/Grid";
import DisplayTemplateCard from "../utility/DisplayTemplateCard";
import { Skeleton } from "@mui/material";
import { Box, Chip, Paper, Typography } from "@mui/material";
import FolderIcon from '@mui/icons-material/Folder';
import DescriptionIcon from '@mui/icons-material/Description';
import GroupIcon from '@mui/icons-material/Group';
import SettingsIcon from '@mui/icons-material/Settings';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import NoTemplatePlacholder from '../component/NoTemplatesPlaceholder'
import { INFO_MESSAGES } from "@constant/enum";
import { colors } from "@constant/colors";

function Draft(props) {
   const [selectedEntity, setSelectedEntity] = useState(null);

    const dataToUse = props.filteredData?.length || props.searchParam !== "" 
      ? props.filteredData 
      : props.draft?.length 
        && props.draft 
  
    const groupedData = useMemo(() => {
      if(dataToUse){
      const groups = {};
      dataToUse?.forEach((item) => {
        const entityDesc = item.entityDesc || 'Uncategorized';
        if (!groups[entityDesc]) {
          groups[entityDesc] = [];
        }
        groups[entityDesc].push(item);
      });
      return groups;
    }
    }, [dataToUse]);
  
    useEffect(() => {
      if (!selectedEntity && groupedData && Object.keys(groupedData)?.length > 0) {
        setSelectedEntity(Object.keys(groupedData)?.[0]);
      }
    }, [groupedData, selectedEntity]);

    if(props?.draft?.length < 1) {
      return (
        <NoTemplatePlacholder/>
      )
    }
  
    const getEntityIcon = (entityDesc) => {
      if (entityDesc.toLowerCase().includes('activity')) return GroupIcon;
      if (entityDesc.toLowerCase().includes('cost center')) return SettingsIcon;
      if (entityDesc.toLowerCase().includes('group')) return FolderIcon;
      return DescriptionIcon;
    };
  
    const getEntityColor = (entityDesc) => {
      if (entityDesc.toLowerCase().includes('activity')) return 'blue';
      if (entityDesc.toLowerCase().includes('cost center')) return 'green';
      if (entityDesc.toLowerCase().includes('group')) return 'purple';
      return 'gray';
    };

    const colorClasses = {
      blue: {
        bg: '#e3f2fd',
        border: '#90caf9',
        icon: '#1e88e5',
        text: '#0d47a1',
        badge: '#bbdefb',
      },
      green: {
        bg: '#e8f5e9',
        border: '#a5d6a7',
        icon: '#43a047',
        text: '#1b5e20',
        badge: '#c8e6c9',
      },
      purple: {
        bg: '#f3e5f5',
        border: '#ce93d8',
        icon: '#8e24aa',
        text: '#4a148c',
        badge: '#e1bee7',
      },
      gray: {
        bg: '#f5f5f5',
        border: '#e0e0e0',
        icon: '#757575',
        text: '#212121',
        badge: '#eeeeee',
      },
    };



  return (
    <>
    {props?.isLoading ? (
        <Grid container rowSpacing={{xs:2,sm:2,md:3,lg:3}} columnSpacing={{ xs: 2, sm: 2, md: 3 }}>
        {Array.from(Array(10)).map((_, index) => (
          <Grid item xs={2} sm={3} md={3} xl={3}  key={index} height={"10rem"}>
            <Paper width={"100%"}sx={{padding:"1rem"}}>
              <Stack direction="column" spacing={1} height={"100%"}width={"100%"} justifyContent="center" alignItems="flex-start">
                <Skeleton variant="text" sx={{ fontSize: "1rem" }} width={200}  p={2} />
                <Skeleton variant="rounded" width={"80%"} height={60} p={2} />
                <Stack direction="row" spacing={2} justifyContent="flex-start" alignItems="center"  width={"100%"}>
                  <Skeleton variant="rounded" width={"45%"} height={24} sx={{ borderRadius: "9px" }} />
                  <Skeleton variant="rounded" width={"45%"} height={24} sx={{ borderRadius: "9px" }} />
                </Stack>
              </Stack>
            </Paper>
          </Grid>
        ))}
      </Grid>
    ) : 
    (
     <Box display="flex" bgcolor={colors?.background?.templateBg}>
      <Box width="20%" bgcolor="#fff" borderRight="1px solid #e0e0e0" boxShadow={1}>
        <Box p={3} borderBottom="1px solid #f0f0f0">
          <Typography variant="h6" fontWeight="bold" color="text.primary">
            Template Categories
          </Typography>
          <Typography variant="body2" color="text.secondary" mt={0.5}>
            Select a category to view templates
          </Typography>
        </Box>

        <Box p={2} display="flex" flexDirection="column" gap={2}>
          {Object.entries(groupedData).map(([entityDesc, templates]) => {
            const IconComponent = getEntityIcon(entityDesc) || FolderIcon;
            const colorScheme = getEntityColor(entityDesc);
            const isSelected = selectedEntity === entityDesc;
            const activeCount = templates.filter(t => t.status === 'Active').length;
            const colors = colorClasses[colorScheme] || colorClasses.gray;

            return (
              <Paper
                key={entityDesc}
                elevation={isSelected ? 4 : 1}
                onClick={() => setSelectedEntity(entityDesc)}
                sx={{
                  border: `2px solid ${isSelected ? colors.border : '#e0e0e0'}`,
                  backgroundColor: isSelected ? colors.bg : '#fff',
                  p: 2,
                  borderRadius: 2,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  '&:hover': {
                    boxShadow: 2,
                    backgroundColor: !isSelected ? '#f5f5f5' : colors.bg,
                  },
                }}
              >
                <Stack direction="row" justifyContent="space-between" alignItems="center">
                  <Stack direction="row" spacing={2} alignItems="center">
                    <Box p={1} borderRadius={2} bgcolor="#ffffff">
                      <IconComponent sx={{ color: colors.icon, fontSize: 20 }} />
                    </Box>
                    <Box>
                      <Typography variant="subtitle2" fontWeight={600} color={colors.text} noWrap>
                        {entityDesc}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {templates.length} template{templates.length !== 1 ? 's' : ''}
                      </Typography>
                    </Box>
                  </Stack>
                  {isSelected && <ChevronRightIcon sx={{ color: '#9e9e9e', fontSize: 16 }} />}
                </Stack>

                <Stack direction="row" justifyContent="space-between" mt={2}>
                  <Chip label={`${activeCount} Active`} size="small" sx={{ backgroundColor: colors.badge, color: colors.text }} />
                  {templates.length - activeCount > 0 && (
                    <Chip label={`${templates.length - activeCount} Inactive`} size="small" sx={{ backgroundColor: '#ffecb3', color: '#ff6f00' }} />
                  )}
                </Stack>
              </Paper>
            );
          })}
        </Box>
      </Box>

      <Box flex={1} overflow="auto">
        {selectedEntity ? (
          <Box p={3}>
            <Box mb={4}>
              <Stack direction="row" spacing={2} alignItems="center" mb={1}>
                {React.createElement(getEntityIcon(selectedEntity), {
                  style: { color: '#1e88e5', fontSize: 28 },
                })}
                <Typography variant="h5" fontWeight="bold">
                  {selectedEntity}
                </Typography>
              </Stack>
              <Typography variant="body1" color="text.secondary">
                {groupedData[selectedEntity].length} template{groupedData[selectedEntity].length !== 1 ? 's' : ''} available
              </Typography>
            </Box>

            <Grid container spacing={3}>
              {groupedData[selectedEntity].map((template, index) => (
                <Grid item xs={12} sm={6} md={4} key={template.emailDefinitionId}>
                  <DisplayTemplateCard
                    headers={props.headers}
                    cardData={template}
                    index={index}
                    setCreationType={props.setCreationType}
                    setSelectedRow={props.setSelectedRow}
                    setOpenCreateTemplate={props.setOpenCreateTemplate}
                    mailmappingData={props.mailmappingData}
                    groupList={props.groupList}
                    userList={props.userList}
                    setIsEditing={props.setIsEditing}
                    allGroups={props.allGroups}
                    setScenario={props.setScenario}
                  />
                </Grid>
              ))}
            </Grid>
          </Box>
        ) : (
          <Box height="100%" display="flex" alignItems="center" justifyContent="center">
            <Box textAlign="center">
              <DescriptionIcon sx={{ fontSize: 64, color: '#e0e0e0', mb: 2 }} />
              <Typography variant="h6" color="text.primary">
                Select a Category
              </Typography>
              <Typography color="text.secondary">
                {INFO_MESSAGES.TEMPLATE_MESSAGE}
              </Typography>
            </Box>
          </Box>
        )}
      </Box>
    </Box>
    )
      }
      </> 
  );
}

export default Draft;
