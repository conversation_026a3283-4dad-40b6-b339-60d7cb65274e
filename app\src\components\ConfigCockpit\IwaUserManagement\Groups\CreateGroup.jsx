import { useNavigate } from "react-router-dom";
import { useSnackbar } from "@hooks/useSnackbar";
import { CreateGroup } from "@cw/creategroup";

import {APP_END_POINTS} from "@constant/appEndPoints";

const CreateGroupContainer = () => {
  const navigate = useNavigate();
  const { showSnackbar } = useSnackbar();

  const onCreateGroupActionClick = (action, response) => {
    if (action === "groupSummary") {
      navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.GROUPS_SUMMARY);
    }
    if (response) {
    showSnackbar(response.message ,"info" );
    }
  };

  const dateTimeConfig = {
    dateFormat: "DD-MMM-YYYY",
    timeFormat: "24hr",
  };

  return (
    <>
      <CreateGroup onCreateGroupActionClick={onCreateGroupActionClick} dateTimeConfig={dateTimeConfig} />
    </>
  );
};

export default CreateGroupContainer;
