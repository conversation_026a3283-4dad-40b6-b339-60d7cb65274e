import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import RequestHeaderCC from "./RequestHeaderCC";
import ArrowCircleLeftOutlined from "@mui/icons-material/ArrowCircleLeftOutlined";
import RequestDetailsCC from "./RequestDetailsCC";
import { setActiveStep } from "@app/redux/stepperSlice";
import { doAjax } from "@components/Common/fetchService";
import {
  Step,
  StepButton,
  Stepper,
  IconButton,
  Box,
  Grid,
  Typography,
  Button,
  DialogContent,
  DialogActions,
} from "@mui/material";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import { DIALOUGE_BOX_MESSAGES, LOCAL_STORAGE_KEYS, REQUEST_STATUS, REQUEST_TYPE } from "@constant/enum";
import { APP_END_POINTS } from "@constant/appEndPoints";
import {
  destination_CostCenter_Mass,
  destination_IDM,
} from "../../destinationVariables";
import RequestDetailsChangeCC from "./RequestDetailsChangeCC";
import {
  resetCostCenterStateCc,
  setCCPayload,
  setRequestHeaderPayloadData,
} from "@app/costCenterTabsSlice";
import { clearChangeLogData, resetPayloadData } from "@app/payloadSlice";
import {
  fetchCurrencyBasedOnCompCode,
  fetchRegionBasedOnCountryCC,
  getProfitCenter,
  idGenerator,
  transformApiResponseToReduxPayloadCc,
  createPayloadForCC,
  changePayloadForCC
} from "../../functions";
import { setRequestHeader } from "@app/requestDataSlice";
import { setDropDown } from "@app/dropDownDataSlice";
import ExcelOperationsCard from "@components/Common/ExcelOperationsCard";
import PreviewPage from "@components/RequestBench/PreviewPage.jsx";
import useLang from "@hooks/useLang";
import { MODULE_MAP } from "@constant/enum";
import useDropdownFMDData from "../modulesHooks/useDropdownFMDData.js";
import { clearLocalStorageItem, setLocalStorage } from "@helper/helper.js";
import useCostCenterChangeFieldConfig from "@hooks/useCostCenterChangeFieldConfig";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { END_POINTS } from "@constant/apiEndPoints";
import { setDropDown as setDropDownAction, setOdataApiCall } from "@costCenter/slice/costCenterDropDownSlice";
import SummarizeOutlinedIcon from "@mui/icons-material/SummarizeOutlined";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import FileUploadOutlinedIcon from "@mui/icons-material/FileUploadOutlined";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { WarningOutlined } from "@mui/icons-material";
import { colors } from "@constant/colors";
import { button_Outlined, button_Primary } from "@components/Common/commonStyles.jsx";

import AttachmentsCommentsTab from "@components/RequestBench/RequestPages/AttachmentsCommentsTab";
import ChangeLogGL from "@components/Changelog/ChangeLogGL";
import { clearCreateChangeLogDataGL, setCreatePayloadCopyForChangeLog } from "@app/changeLogReducer";
import ErrorReportDialog from "@components/Common/ErrorReportDialog";
const steps = [
  "Request Header",
  "CostCenter List",
  "Attachments & Comments",
  "Preview",
];
const CostCenterRequestTab = () => {
  const { t } = useLang();
  const tabValue = useSelector((state) => state.CommonStepper.activeStep);
  const requestHeaderData = useSelector(
    (state) => state.costCenter.payload.requestHeaderData
  );
  const requestIdHeader = useSelector(
    (state) => state.request.requestHeader?.requestId
  );
  const requestHeaderSlice = useSelector(
    (state) => state.request.requestHeader
  );
  const dispatch = useDispatch();
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(true);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const [isDialogVisible,setisDialogVisible] = useState(false)
  const location = useLocation();
  const navigate = useNavigate();
  const [apiResponses, setApiResponses] = useState([]);
  const [completed, setCompleted] = useState([]);
  const [downloadClicked, setDownloadClicked] = useState(false);
  const [ccNumber, setCCNumber] = useState("");
  const rowData = location.state;
  const queryParams = new URLSearchParams(location.search);
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [attachmentsData, setAttachmentsData] = useState([]);
  const reqBench = queryParams.get("reqBench");
  const RequestId = queryParams.get("RequestId");
  const RequestType = queryParams.get("RequestType");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const onlyDigits = (val) => String(val || "").replace(/\D/g, "");
  const { getChangeTemplate } = useCostCenterChangeFieldConfig(MODULE_MAP.CC);
  const templateFullData = useSelector(
    (state) => state?.payload?.changeFieldSelectiondata || []
  );
  const applicationConfig = useSelector((state) => state.applicationConfig);  
  const isCostCenterApiCalled = useSelector((state) => state.costCenterDropDownData?.isOdataApiCalled)
  const { fetchAllDropdownFMD } = useDropdownFMDData(destination_CostCenter_Mass,setDropDownAction);
  let task = useSelector((state) => state?.userManagement.taskData);
  const reduxPayload = useSelector((state) => state.costCenter.payload);
  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
  const { fetchedCostCenterData, fetchReqBenchDataCC } = useSelector(
    (state) => state.costCenter
  );

  const isChildRequest =
  rowData?.childRequestIds !== "Not Available" &&
  typeof task === "object" &&
  task !== null &&
  Object.keys(task).length === 0 &&
  reqBench === "true";
// Note: This was used before, might use LastPageRounded, kept it for reference
  // const fieldDisable=Object.keys(task).length !== 0
    let localStorageData = localStorage?.getItem(LOCAL_STORAGE_KEYS.CURRENT_TASK);
    const taskData = JSON.parse(localStorageData || "{}");
    let fieldDisable = taskData?taskData?.itmStatus : "";

  const handleUploadCC = (file) => {
    let url = "";
    url = "getAllCostCenterFromExcel";
    setLoaderMessage("Initiating Excel Upload");
    setBlurLoading(true);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    formData.append(
      "dtName",
      RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
        RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
        ? "MDG_CC_FIELD_CONFIG"
        : "MDG_CHANGE_TEMPLATE_DT"
    );
    formData.append(
      "version",
      RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
        RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
        ? "v3"
        : "v6"
    );
    formData.append("requestId", RequestId ? RequestId : "");
    formData.append("IsSunoco", "false");
    formData.append("screenName", RequestType ? RequestType : "");
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      } else {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }
    };
    const hError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/${url}`,
      "postformdata",
      hSuccess,
      hError,
      formData
    );
  };
  useEffect(() => {
    getAttachmentsIDM();
  }, []);
    const getCompanyCodeBasedOnControllingArea = (CA) => {
      const hSuccess = (data) => {
              dispatch(setDropDownAction({
                    keyName:"CompCode",
                    data: data?.body || [],
              }))
        dispatch(setDropDown({ keyName: "CompCode", data: data.body }));
      };
      const hError = () => {
      };
      doAjax(
        `/${destination_CostCenter_Mass}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${CA}&rolePrefix=ETP`,
        "get",
        hSuccess,
        hError
      );
    };
  const getAttachmentsIDM = () => {
    const payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "ET Cost Center",
          // Note Might use later for dynamic module name
          // "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": MODULE_MAP.CC,
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": REQUEST_TYPE.CREATE,
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1, // ensure backend expects number not string
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    const hSuccess = (data) => {
      if (data?.statusCode === 200) {
        const attachmentList =
          data?.data?.result?.[0]?.MDG_ATTACHMENTS_ACTION_TYPE ?? [];
        setAttachmentsData(attachmentList);
      } else {
        console.warn("Unexpected statusCode:", data?.statusCode);
      }
    };
    const hError = (error) => {
      console.error("Attachment fetch error:", error);
    };
        const endpoint =
          applicationConfig.environment === "localhost"
            ? END_POINTS.INVOKE_RULES.LOCAL
            : END_POINTS.INVOKE_RULES.PROD;

    doAjax(`/${destination_IDM}${endpoint}`, "post", hSuccess, hError, payload);
  };
  const handleTabChange = (index) => {
    dispatch(setActiveStep(index));
  };
  const handleDownload = () => {
    setDownloadClicked(true);
  }

   const openChangeLog = () => {
    setisChangeLogopen(true);
  };
  const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };
 
  const exportExcel=()=>{
    const payloadData={
      dtName:"MDG_CC_FIELD_CONFIG",
      version:"v3",
      requestId:RequestId,
      scenario:"Create with Upload",
      isChild:"false"
    }
 
    const hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
 
      link.href = href;
      link.setAttribute("download", `${payloadData?.scenario ? payloadData?.scenario : "Mass_Create"}_Data Export.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
 
      setBlurLoading(false);
      setLoaderMessage("");
    };
 
    const hError = (error) => {
      console.error("Attachment fetch error:", error);
    };
 
    doAjax(
      `/${destination_CostCenter_Mass}/excel/exportCCExcel`,
      "postandgetblob",
      hSuccess,
      hError,
      payloadData
    );
 
  }
 useEffect(() => {
  clearChangeLogData();
  clearCreateChangeLogDataGL();
 },[])
  
  useEffect(() => {
    if(!isCostCenterApiCalled){
      fetchAllDropdownFMD("costCenter")
      dispatch(setOdataApiCall(true))
    }
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE,MODULE_MAP.CC)
    getAttachmentsIDM();
    setCCNumber(idGenerator("CC"));
    return () => {
      clearLocalStorageItem(LOCAL_STORAGE_KEYS.MODULE)
    }
  },[])

  useEffect(() => {
    if (isSecondTabEnabled) {
      setCompleted([true]);
    }
  }, [isSecondTabEnabled]);
  const getDisplayDataCC = (RequestId) => {
    const isChildPresent = rowData?.childRequestIds !== "Not Available";
    
      const payload = {
        sort: "id,asc",
        parentId: !isChildPresent ? RequestId : "",
        massCreationId:
          isChildPresent &&
          (RequestType === REQUEST_TYPE.CREATE ||
            RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD )
            ? RequestId
            : "",
        massChangeId:
          isChildPresent &&
          (RequestType === REQUEST_TYPE.CHANGE ||
            RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD )
            ? RequestId
            : "",
        page: 0,
        size: 10,
      };
      const hSuccess = (response) => {
        const apiResponse = response?.body || [];
        let requestHeaderData = response?.body[0]?.Torequestheaderdata;
        let TotalIntermediateTasks = response?.body[0]?.TotalIntermediateTasks;
        let displayData = {
          RequestId: requestHeaderData.RequestId,
          RequestPrefix: requestHeaderData.RequestPrefix,
          ReqCreatedBy: requestHeaderData.ReqCreatedBy,
          ReqCreatedOn: requestHeaderData.ReqCreatedOn,
          ReqUpdatedOn: requestHeaderData.ReqUpdatedOn,
          RequestType: requestHeaderData.RequestType,
          RequestDesc: requestHeaderData.RequestDesc,
          RequestStatus: apiResponse?.[0]?.RequestStatus,
          RequestPriority: requestHeaderData.RequestPriority,
          FieldName: requestHeaderData.FieldName,
          TemplateName: requestHeaderData.TemplateName,
          Division: requestHeaderData.Division,
          region: requestHeaderData.region,
          leadingCat: requestHeaderData.leadingCat,
          firstProd: requestHeaderData.firstProd,
          launchDate: requestHeaderData.launchDate,
          isBifurcated: requestHeaderData.isBifurcated,
          screenName: requestHeaderData.screenName,
          TotalIntermediateTasks: TotalIntermediateTasks,
        };
        dispatch(setRequestHeaderPayloadData(displayData));
        dispatch(setRequestHeader(requestHeaderData));
        setApiResponses(apiResponse);
        const transformedPayload =
          transformApiResponseToReduxPayloadCc(apiResponse);
        apiResponse.forEach((item) => {
          if (item?.ToCostCenterData?.[0]?.AddrCountry) {
            fetchRegionBasedOnCountryCC(
              item?.ToCostCenterData?.[0]?.AddrCountry,
              dispatch,
              item?.ToCostCenterData?.[0]?.CostCenterID
            );
          }
          if (apiResponse?.[0]?.ControllingArea) {
              getCompanyCodeBasedOnControllingArea(apiResponse?.[0]?.ControllingArea)
          }
          if (item?.ToCostCenterData?.[0]?.CompCode) {
            fetchCurrencyBasedOnCompCode(
              item?.ToCostCenterData?.[0]?.CompCode,
              dispatch,
              item?.ToCostCenterData?.[0]?.CostCenterID
            );
              getProfitCenter(
              apiResponse?.[0]?.ControllingArea,
              item?.ToCostCenterData?.[0]?.CompCode,
              dispatch,
              item?.ToCostCenterData?.[0]?.CostCenterID
            );
          }
          if (item?.Torequestheaderdata?.RequestType) {
            getChangeTemplate();
          }
          if (item?.Torequestheaderdata?.TemplateName) {
            let selectedTemplate = item?.Torequestheaderdata?.TemplateName;
            const filteredAndSortedFields = templateFullData
              .filter(
                (item) =>
                  item?.MDG_CHANGE_TEMPLATE_NAME === selectedTemplate &&
                  item?.MDG_MAT_CHANGE_TYPE === "Item" &&
                  item?.MDG_MAT_FIELD_VISIBILITY !== "Hidden" &&
                  item?.MDG_MAT_FIELD_VISIBILITY !== "Display"
              )
              .sort((a, b) => {
                // Convert to number for proper sorting, handle nulls/fallbacks
                const seqA = Number(a?.MDG_MAT_FIELD_SEQUENCE) || 0;
                const seqB = Number(b?.MDG_MAT_FIELD_SEQUENCE) || 0;
                return seqA - seqB;
              });
            const uniqueFieldNames = [
              ...new Set(
                filteredAndSortedFields
                  .map((item) => item?.MDG_MAT_FIELD_NAME)
                  .filter(Boolean)
              ),
            ].map((field) => ({ code: field }));
            // return uniqueFieldNames;
            //   const changeFieldNames = uniqueFieldNames.map((item) => ({
            //   code: item?.fieldName,
            //   desc: "",
            // }));
            dispatch(
              setDropDown({
                keyName: "FieldName",
                data: uniqueFieldNames || [],
                // keyName2: uniqueId,
              })
            );
          }
        });
        dispatch(setCCPayload(transformedPayload?.payload));
        dispatch(setCreatePayloadCopyForChangeLog(transformedPayload?.payload));
      };
      const hError = (error) => {
        console.error("Error fetching CC Create data:", error);
      };
      doAjax(
        `/${destination_CostCenter_Mass}/data/displayMassCostCenterDto`,
        "post",
        hSuccess,
        hError,
        payload
      );
    
  };
  useEffect(() => {
    const loadData = async () => {
      if (RequestId) {
        await getDisplayDataCC(RequestId);
        if (
          ((RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD &&
            !rowData?.length) ||
            RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
            RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) &&
          (rowData?.reqStatus === REQUEST_STATUS.DRAFT ||
            rowData?.reqStatus === REQUEST_STATUS.UPLOAD_FAILED)
        ) {
          dispatch(setActiveStep(0));
          setIsSecondTabEnabled(false);
          setIsAttachmentTabEnabled(false);
        } else {
          dispatch(setActiveStep(1));
          setIsSecondTabEnabled(true);
          setIsAttachmentTabEnabled(true);
        }
      } else {
        dispatch(setActiveStep(0));
      }
    };
    loadData();
    return () => {
      dispatch(resetPayloadData());
      dispatch(resetCostCenterStateCc());
      dispatch(setRequestHeader({}));
      dispatch(setDropDown({ keyName: "FieldName", data: [] }));
    };
  }, [RequestId, dispatch]);
  const handleYes = () => {
    if (RequestId && !reqBench) {
      navigate(APP_END_POINTS?.MY_TASK);
    } else if (reqBench) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    } else if (!RequestId && !reqBench) {
      navigate(APP_END_POINTS?.MASTER_DATA_CC);
    }
  };
  const handleCancel = () => {
    setisDialogVisible(false);
  };

  const ccPayload =
  RequestType === REQUEST_TYPE?.CREATE ||
  RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
    ? createPayloadForCC(
        reduxPayload,
        requestHeaderSlice,
        RequestId,
        task,
        dynamicData
      )
    : changePayloadForCC(
        requestHeaderData,
        task,
        reqBench,
        fetchReqBenchDataCC,
        fetchedCostCenterData
      );
 
 
  const payloadForPreviewDownloadExcel = {
        costCenterDetails: ccPayload,
        dtName: "MDG_CC_FIELD_CONFIG",
        version: "v3",
        requestId:RequestId || "",
        scenario: "Create",
        templateName:"" ,
        region: "US",
      };

 

  return (
    <div>
      <Box sx={{ padding: 2 }}>
        <Grid
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {RequestId || requestIdHeader ? (
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                textAlign: "left",
                display: "flex",
                alignItems: "center",
                gap: 1,
              }}
            >
              <PermIdentityOutlinedIcon sx={{ fontSize: "1.5rem" }} />
              {t("Request Header ID")}:{" "}
              <span>
                {requestIdHeader ? 
                 requestHeaderSlice?.requestId
                  : `${RequestId}`}
              </span>
            </Typography>
          ) : (
            <div style={{ flex: 1 }} />
          )}
        </Grid>
        {isChangeLogopen && (
            <ChangeLogGL
              module={MODULE_MAP.CC}
              open={true}
              closeModal={handleClosemodalData}
              requestId={requestHeaderData?.RequestId || RequestId}
              requestType={"create"}
            />
          )}
 
        {tabValue === 1 && (
            <Box
              sx={{ display: "flex", justifyContent: "flex-end", gap: "1rem", paddingBottom:"10px" }}
            >
              <Button
                variant="outlined"
                size="small"
                title="Error Report"
                disabled={!RequestId}
                onClick={() => setDialogOpen(true)}
                // Note: might use later
                // onClick={() => {
                //   navigate(
                //     `/requestBench/errorHistory?RequestId=${
                //       RequestId ? RequestId : ""
                //     }`,
                //     { state: {childRequest: isChildRequest?true:false, module: MODULE_MAP.CC, display: true } }
                //   );
                // }}
                color="primary"
              >
                <SummarizeOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
 
              <Button
                variant="outlined"
                disabled={!RequestId}
                size="small"
                onClick={openChangeLog}
                title="Change Log"
              >
                <TrackChangesTwoToneIcon sx={{ padding: "2px" }} />
              </Button>
 
              <Button
                variant="outlined"
                disabled={!RequestId}
                size="small"
                onClick={exportExcel}
                title="Export Excel"
              >
                <FileUploadOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
            </Box>
          )}
          <Grid>
        <IconButton
          onClick={() => {
            if (reqBench === "true") {
              navigate(APP_END_POINTS?.REQUEST_BENCH);
              return;
            }
            setisDialogVisible(true);
          }}
          color="primary"
          aria-label="upload picture"
          component="label"
          sx={{ left: "-10px" }}
          title="Back"
        >
          <ArrowCircleLeftOutlined
            sx={{ fontSize: "25px", color: "#000000" }}
          />
        </IconButton>
        <Stepper
          nonLinear
          activeStep={tabValue}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            margin: "25px 14%",
            marginTop: "-20px",
          }}
        >
          {steps.map((label, index) => (
            <Step key={label} completed={completed[index]}>
              <StepButton
                color="error"
                disabled={
                  (index === 1 && !isSecondTabEnabled) ||
                  (index === 2 && !isAttachmentTabEnabled) ||
                  (index === 3 &&
                    !isSecondTabEnabled &&
                    !isAttachmentTabEnabled)
                }
                onClick={() => handleTabChange(index)}
                sx={{ fontSize: "50px", fontWeight: "bold" }}
              >
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>
                  {label}
                </span>
              </StepButton>
            </Step>
          ))}
        </Stepper>
        </Grid>
        {tabValue === 0 && (
          <>
            <RequestHeaderCC
              apiResponse={apiResponses}
              reqBench={reqBench}
              downloadClicked={downloadClicked}
              setDownloadClicked={setDownloadClicked}
              setIsSecondTabEnabled={setIsSecondTabEnabled}
              setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
            />
            {(RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD ||
              RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
              RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) &&
              ((rowData?.reqStatus == REQUEST_STATUS.DRAFT &&
                !rowData?.material?.length) ||
                rowData?.reqStatus == REQUEST_STATUS.UPLOAD_FAILED) && (
                <ExcelOperationsCard
                  handleDownload={handleDownload}
                  setEnableDocumentUpload={setEnableDocumentUpload}
                  enableDocumentUpload={enableDocumentUpload}
                  handleUploadMaterial={handleUploadCC}
                />
              )}
          </>
        )}
        {tabValue === 1 &&
          requestHeaderData.RequestType &&
          (requestHeaderData.RequestType === "Change" ||
          requestHeaderData.RequestType === "Change with Upload" ? (
            <RequestDetailsChangeCC
              reqBench={reqBench}
              requestId={RequestId}
              apiResponses={apiResponses}
              setIsAttachmentTabEnabled={true}
              setCompleted={setCompleted}
              downloadClicked={downloadClicked}
              setDownloadClicked={setDownloadClicked}
              module={MODULE_MAP.CC}
            />
          ) : (
            <RequestDetailsCC 
            reqBench={reqBench} 
            apiResponses={apiResponses}
            module={MODULE_MAP.CC} 
            isDisabled={isChildRequest}
            fieldDisable={fieldDisable}
            />
          ))}
        {tabValue === 2 && (
          <AttachmentsCommentsTab
            requestStatus={
              rowData?.reqStatus
                ? rowData?.reqStatus
                : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME
            }
            attachmentsData={attachmentsData}
            requestIdHeader={
              requestIdHeader
                ? requestIdHeader
                : RequestId
            }
            pcNumber={ccNumber}
            module={MODULE_MAP.CC}
            childRequestIds={rowData?.childRequestIds}
          />
        )}
        {tabValue === 3 && (
          <Box
            sx={{
              width: "100%",
              overflow: "auto",
            }}
          >
            <PreviewPage module={MODULE_MAP?.CC} payloadForPreviewDownloadExcel={payloadForPreviewDownloadExcel} />
          </Box>
        )}
      </Box>
      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />
              <ErrorReportDialog
          dialogState={dialogOpen}
          closeReusableDialog={() => setDialogOpen(false)}
          module={MODULE_MAP?.CC}
          isHierarchyCheck={false}
        />
      {isDialogVisible && (
        <CustomDialog
          isOpen={isDialogVisible}
          titleIcon={
            <WarningOutlined
              size="small"
              sx={{ color: colors?.secondary?.amber, fontSize: "20px" }}
            />
          }
          Title={"Warning"}
          handleClose={handleCancel}
        >
          <DialogContent sx={{ mt: 2 }}>
            {DIALOUGE_BOX_MESSAGES.LEAVE_PAGE_MESSAGE}
          </DialogContent>
          <DialogActions>
            <Button
              variant="outlined"
              size="small"
              sx={{ ...button_Outlined }}
              onClick={handleCancel}
            >
              {t("No")}
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary }}
              onClick={handleYes}
            >
              {t("Yes")}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </div>
  );
};
export default CostCenterRequestTab;
