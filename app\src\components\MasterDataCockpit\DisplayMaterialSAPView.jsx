import React, { useEffect, useState } from "react";
import { Typo<PERSON>, IconButton, Grid, Box, Stack, Tabs, Tab, Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import { iconButton_SpacingSmall, outermostContainer_Information } from "../Common/commonStyles";
import { doAjax } from "../Common/fetchService";
import { destination_MaterialMgmt } from "../../destinationVariables";
import { makeStyles } from "@mui/styles";
import InventoryIcon from '@mui/icons-material/Inventory';
import BusinessIcon from '@mui/icons-material/Business';
import CategoryIcon from '@mui/icons-material/Category';
import DescriptionIcon from '@mui/icons-material/Description';
import { useDispatch, useSelector } from "react-redux";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { pushMaterialDisplayData, setAdditionalData, setUOmData } from "../../app/payloadslice";
import GenericTabsForChange from "./GenericTabsForChange";
import { ERROR_MESSAGES, LOADING_MESSAGE, MATERIAL_VIEWS } from "@constant/enum";
import { colors } from "@constant/colors";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import useMaterialFieldConfig from "@hooks/useMaterialFieldConfig";
import GenericViewGeneral from "./GenericViewGeneral";
import AdditionalData from "./AdditionalData";
import { END_POINTS } from "@constant/apiEndPoints";
import TaxDataSAP from "./TaxDataSAP";
import { convertSAPDateForCalendar } from "../../helper/helper";
import useLogger from "@hooks/useLogger";
import useLang from "@hooks/useLang";


const RenderRow = ({ label, value, labelWidth = "25%", centerWidth = "5%", icon }) => (
  <Stack flexDirection="row" alignItems="center">
    {icon && <div style={{ marginRight: "10px" }}>{icon}</div>}
    <Typography variant="body2" color={colors.secondary.grey} style={{ width: labelWidth }}>
      {label}
    </Typography>
    <Typography variant="body2" fontWeight="bold" sx={{ width: centerWidth, textAlign: "center" }}>
      :
    </Typography>
    <Typography variant="body2" fontWeight="bold" justifyContent="flex-start">
      {value || ""}
    </Typography>
  </Stack>
);

const DisplayMaterialSAPView = () => {
  const { fetchMaterialFieldConfig } = useMaterialFieldConfig();
  const useStyles = makeStyles(() => ({
    customTabs: {
      "& .MuiTabs-scroller": {
        overflowX: "auto !important",
        overflowY: "hidden !important",
      },
    },
  }));

  const payloadData = useSelector((state) => state.payload);
  const allTabsData = useSelector((state) => state.tabsData.allTabsData);
  const { t } = useLang();


  const navigate = useNavigate();
  const classes = useStyles();
  const location = useLocation();
  const dispatch = useDispatch();
  const structureData = location.state;
  const { customError } = useLogger()

  const dropDownData = [];

  const [tabNames, setTabNames] = useState([]);
  const [tabContentData, setTabContentData] = useState({}); 
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [activeTab, setActiveTab] = useState(0);
  const [expandedAccordion, setExpandedAccordion] = useState(null);

  useEffect(() => {
    fetchMaterialData();
    fetchMaterialFieldConfig(structureData?.materialType.split(" - ")[0]);
  }, []);

  const fetchMaterialData = () => {
    setBlurLoading(true);
    const payload = {
      materialNo: structureData?.Number,
    };

    const tabsMapping = {
      "Basic Data": "Toclientdata",
      Sales: "Tosalesdata",
      Purchasing: "Toplantdata",
      Costing: "Toplantdata",
      Accounting: "Toaccountingdata",
      MRP: "Toplantdata",
      Warehouse: "Towarehousedata",
      "Sales-Plant": "Tosalesdata",
      "Work Scheduling": "Toplantdata",
    };
    function transformToUniqueTaxDataSet(dataArray) {
      if (!Array.isArray(dataArray)) return { UniqueTaxDataSet: [] };
    
      const resultMap = new Map();
    
      dataArray.forEach(entry => {
        const country = entry?.Depcountry;
    
        for (let i = 1; i <= 9; i++) {
          const taxType = entry[`TaxType${i}`];
          const taxClass = entry[`Taxclass${i}`];
    
          if (taxType && taxClass !== undefined) {
            const key = `${country}-${taxType}`;
            const taxClassDesc = taxClass === '1' ? 'Taxable' : 'Exempt';
    
            if (!resultMap.has(key)) {
              resultMap.set(key, {
                Country: country,
                TaxType: taxType,
                TaxClasses: [],
                SelectedTaxClass: {
                  TaxClass: taxClass,
                  TaxClassDesc: taxClassDesc
                }
              });
            }
    
            const existing = resultMap.get(key);
            const exists = existing.TaxClasses.some(
              cls => cls.TaxClass === taxClass
            );
            if (!exists) {
              existing.TaxClasses.push({
                TaxClass: taxClass,
                TaxClassDesc: taxClassDesc
              });
            }
            existing.SelectedTaxClass = {
              TaxClass: taxClass,
              TaxClassDesc: taxClassDesc
            };
          }
        }
      });
    
      return {
        UniqueTaxDataSet: Array.from(resultMap.values())
      };
    }
    
       
    const hSuccess = (data) => {
      if (data?.body) {
        // Extract ViewNames from data.body
        const viewNames = data.body[0].ViewNames?.split(",").map((view) => view.trim()) || [];
        const transformedTaxData = transformToUniqueTaxDataSet(data?.body[0]?.Tocontroldata);
        const newTabNames = [];
        const newTabContentData = {};
        viewNames.forEach((view) => {
          const mappedKey = tabsMapping[view] ?? "";
          if (mappedKey !== "" && data.body[0][mappedKey]) {
            newTabNames.push(view);
            newTabContentData[view] = data.body[0][mappedKey];
          }
        });

        if (viewNames.includes("Sales")) {
          newTabNames.push("Sales-Plant");
          newTabContentData["Sales-Plant"] = data.body[0]["Toplantdata"];
        }
        newTabNames.push("Additional Data");

        setTabNames(newTabNames);
        setTabContentData(newTabContentData);
        const clientData = data?.body[0]?.Toclientdata;
        if (clientData) {
          if (clientData.Pvalidfrom) {
            const dateObj = convertSAPDateForCalendar(clientData.Pvalidfrom);
            clientData.Pvalidfrom = dateObj ? dateObj.toISOString().split('T')[0] : "";
          }
          
          if (clientData.Svalidfrom) {
            const dateObj = convertSAPDateForCalendar(clientData.Svalidfrom);
            clientData.Svalidfrom = dateObj ? dateObj.toISOString().split('T')[0] : "";
          }
        }

        dispatch(
          pushMaterialDisplayData({
            materialID: data?.body[0]?.Material,
            viewID: "Basic Data",
            itemID: "basic",
            data: clientData,
          })
        );
        dispatch(
          pushMaterialDisplayData({
            materialID: data?.body[0]?.Material,
            viewID: MATERIAL_VIEWS.PURCHASING_GENERAL,
            itemID: MATERIAL_VIEWS.PURCHASING_GENERAL,
            data: data?.body[0]?.Toclientdata,
          })
        );
        dispatch(
          pushMaterialDisplayData({
            materialID: data?.body[0]?.Material,
            viewID: MATERIAL_VIEWS.SALES_GENERAL,
            itemID: MATERIAL_VIEWS.SALES_GENERAL,
            data: data?.body[0]?.Toclientdata,
          })
        );
        dispatch(
          setUOmData({
            materialID: data?.body[0]?.Material,
            data: data?.body[0]?.Touomdata?.map((item, index) => ({
              ...item,
              id: `${item.Material}-${index}`,
              uomId: `${item.Material}-${index}`,
              xValue: item?.Denominatr || "",
              aUnit: item?.AltUnit || "",
              measureUnitText: "",
              yValue: item?.Numerator || "",
              eanUpc: item?.EanUpc || "",
              eanCategory: item?.EanCat || "",
              length: item?.Length,
              width: item?.Width,
              height: item?.Height,
              unitsOfDimension: item?.UnitDim || "",
              volume: item?.Volume || "",
              volumeUnit: item?.Volumeunit || "",
              grossWeight: item?.GrossWt || "",
              netWeight: index === 0 ? (data?.body[0]?.Toclientdata?.NetWeight || item?.NetWeight || "") : "",
              weightUnit: item?.UnitOfWt || "",
            })),
          })
        );
        dispatch(setAdditionalData({ materialID: data?.body[0]?.Material, data: data?.body[0]?.Tomaterialdescription.map((item, index) => ({
          id: index+1,
          language : item?.Langu,
          materialDescription : item?.MatlDesc,
        })) 
      })
    );

    dispatch(pushMaterialDisplayData({
      materialID: data?.body[0]?.Material,
      viewID: "TaxData",
      itemID: "TaxData",
      data: transformedTaxData || { TaxData: { UniqueTaxDataSet: [] } }
    }));
    
        setBlurLoading(false);
        setLoaderMessage("");

      }
    };

    const hError = () => {}
    
    doAjax(
      `/${destination_MaterialMgmt}/data/displayLimitedMaterialData`,
      "post",
      hSuccess,
      hError,
      payload
    )
  }

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    setExpandedAccordion(null); // Reset accordion state when switching tabs
  };

  const handleAccordionChange = (accordionIndex, accData, view) => (event, isExpanded) => {
    if(view === MATERIAL_VIEWS.COSTING){
      let payload1 = {
        materialNo: accData?.Material,
        plant: accData?.Plant,
      };
      let plantData = accData?.Plant;
      let payload2 = {
        materialNo: accData?.Material,
        valArea: accData?.Plant,
      };
      let url1 = `/${destination_MaterialMgmt}/${END_POINTS.ACCORDION_API.PLANT}`;
      let url2 = `/${destination_MaterialMgmt}/${END_POINTS.ACCORDION_API.ACCOUNTING}`;
      
      const hSuccess = (data) => {
        const plantData = data?.body[0].Toplantdata[0];

        const hSuccess2 = (data2) => {
          const accountingData = data2?.body[0]?.Toaccountingdata[0];

          const mergedData = {
            ...plantData,
            ...accountingData
          };
          
          dispatch(
            pushMaterialDisplayData({
              materialID: accData?.Material,
              viewID: view,
              itemID: accData?.Plant,
              data: mergedData,
            })
          );
        };
        
        const hError2 = () => {};
        doAjax(url2, "post", hSuccess2, hError2, payload2);
      };
      
      const hError = () => {};
      doAjax(url1, "post", hSuccess, hError, payload1);
      setExpandedAccordion(isExpanded ? accordionIndex : null);
      return;
    }
    let payload = {};
    let url = "";
    let plantData = "";

    if (view === MATERIAL_VIEWS.PURCHASING || view === MATERIAL_VIEWS.MRP || view === MATERIAL_VIEWS.WORKSCHEDULING || view === MATERIAL_VIEWS.SALES_PLANT) {
      payload = {
        materialNo: accData?.Material,
        plant: accData?.Plant,
      };
      plantData = accData?.Plant;
      url = `/${destination_MaterialMgmt}/data/displayLimitedPlantData`;
    } 
    else if(view === MATERIAL_VIEWS.WAREHOUSE){
      payload = {
        materialNo: accData?.Material,
        whNumber: accData?.WhseNo,
      };
      plantData = accData?.WhseNo;
      url = `/${destination_MaterialMgmt}/${END_POINTS.ACCORDION_API.WAREHOUSE}`;
    }
    else if (view === MATERIAL_VIEWS.ACCOUNTING) {
      payload = {
        materialNo: accData?.Material,
        valArea: accData?.ValArea,
      };
      plantData = accData?.ValArea;
      url = `/${destination_MaterialMgmt}/data/displayLimitedAccountingData`;
    } else if (view === MATERIAL_VIEWS.SALES) {
      payload = {
        materialNo: accData?.Material,
        salesOrg: accData?.SalesOrg,
        distChnl: accData?.DistrChan,
      };
      plantData = `${accData?.SalesOrg}-${accData?.DistrChan}`;
      url = `/${destination_MaterialMgmt}/data/displayLimitedSalesData`;
    }

    const hSuccess = (data) => {
      if (view === MATERIAL_VIEWS.PURCHASING || view === MATERIAL_VIEWS.MRP || view === MATERIAL_VIEWS.WORKSCHEDULING || view === MATERIAL_VIEWS.SALES_PLANT) {
        dispatch(
          pushMaterialDisplayData({
            materialID: accData?.Material,
            viewID: view,
            itemID: accData?.Plant,
            data: {
              ...data?.body[0].Toplantdata[0],
              ProdProf: data?.body[0].Toplantdata[0]?.Prodprof || ""
            },
          })
        );
      } else if (view === MATERIAL_VIEWS.ACCOUNTING) {
        dispatch(
          pushMaterialDisplayData({
            materialID: accData?.Material,
            viewID: view,
            itemID: accData?.ValArea,
            data: data?.body[0]?.Toaccountingdata[0],
          })
        );
      }
      else if (view === MATERIAL_VIEWS.WAREHOUSE) {
        dispatch(
          pushMaterialDisplayData({
            materialID: accData?.Material,
            viewID: view,
            itemID: accData?.WhseNo,
            data: data?.body[0]?.Towarehousedata[0],
          })
        );
      }
      else if (view === MATERIAL_VIEWS.SALES) {
        if (data?.body[0]?.Tosalesdata[0]?.ValidFrom) {
          const dateObj = convertSAPDateForCalendar(data.body[0].Tosalesdata[0].ValidFrom);
          data.body[0].Tosalesdata[0].ValidFrom = dateObj ? dateObj.toISOString().split('T')[0] : "";
        }
        
        dispatch(
          pushMaterialDisplayData({
            materialID: accData?.Material,
            viewID: view,
            itemID: `${accData?.SalesOrg}-${accData?.DistrChan}`,
            data: data?.body[0]?.Tosalesdata[0],
          })
        );
      }
    };

    const hError = (error) => {
      customError(error);
    };

    const shouldCallApi = !payloadData?.[accData?.Material]?.payloadData?.[view]?.[plantData];

    if (shouldCallApi) {
      doAjax(url, "post", hSuccess, hError, payload);
    }

    setExpandedAccordion(isExpanded ? accordionIndex : null); // Toggle accordion expansion
  };
  const salesGeneralFields = allTabsData?.hasOwnProperty(MATERIAL_VIEWS.SALES_GENERAL) ? Object.entries(allTabsData[MATERIAL_VIEWS.SALES_GENERAL]) : [];
  const purchaseGeneralFields = allTabsData?.hasOwnProperty(MATERIAL_VIEWS.PURCHASING_GENERAL) ? Object.entries(allTabsData[MATERIAL_VIEWS.PURCHASING_GENERAL]) : [];
  return (
    <div style={{ backgroundColor: "#FAFCFF" }}>
      {/* Header Section */}
      <Grid container sx={outermostContainer_Information}>
        <Grid item md={12} sx={{ padding: "16px", display: "flex" }}>
          <Grid md={9} sx={{ display: "flex" }}>
            <IconButton color="primary" sx={iconButton_SpacingSmall} onClick={() => navigate(-1)}>
              <ArrowCircleLeftOutlinedIcon sx={{ fontSize: "25px", color: "#000000" }} />
            </IconButton>
            <Grid item md={12}>
              <Typography variant="h3">
                <strong>{t("Display Material")}</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                {t("This view displays the details of the materials")}
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      {/* Content Section */}
      <Grid
        container
        display="flex"
        flexDirection="row"
        flexWrap="nowrap"
        sx={{
          justifyContent: "space-between",
          alignItems: "center",
          paddingLeft: "29px",
          backgroundColor: colors.basic.lighterGrey,
          borderRadius: "10px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.06)",
        }}
      >
        {/* Left Section */}
        <Stack
          width="48%"
          spacing={1}
          sx={{
            padding: "10px 15px",
            borderRight: "1px solid #eaedf0"
          }}
        >
          <Grid item>
            <RenderRow
              label={t("Material")}
              value={structureData?.Number || ""}
              labelWidth="35%"
              icon={<InventoryIcon sx={{ color: colors.blue.indigo, fontSize: "20px" }} />}
            />
          </Grid>
          <Grid item>
            <RenderRow
              label={t("Industry Sector")}
              value={structureData?.indSector || ""}
              labelWidth="35%"
              icon={<BusinessIcon sx={{ color: colors.blue.indigo, fontSize: "20px" }} />}
            />
          </Grid>
        </Stack>

          <Stack
            width="48%"
            spacing={1}
            marginRight={"-10%"}
            sx={{
              padding: "10px 15px"
            }}
          >
            <Grid item>
              <RenderRow
                label={t("Material Type")}
                value={structureData?.materialType || ""}
                labelWidth="35%"
                icon={<CategoryIcon sx={{ color: colors.blue.indigo, fontSize: "20px" }} />}
              />
            </Grid>
            <Grid item>
              <RenderRow
                label={t("Material Description")}
                value={structureData?.materialDesc || ""}
                labelWidth="35%"
                icon={<DescriptionIcon sx={{ color: colors.blue.indigo, fontSize: "20px" }} />}
              />
            </Grid>
          </Stack>
      </Grid>
      <Grid>
        {allTabsData && tabNames.length > 0 ? (
          <Box
            sx={{
              marginTop: "30px",
              border: "1px solid #e0e0e0",
              padding: "16px",
              background: colors.primary.white,
            }}
          >
            <Box sx={{ marginTop: "-10px", marginLeft: "5px" }}>
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                aria-label="material tabs"
                sx={{
                  top: 100,
                  zIndex: 1000,
                  background: "#fafcff",
                  marginLeft: "20px",
                  marginBottom: "-20px",
                }}
              >
                {tabNames.map((name, index) => (
                  <Tab label={t(name)} key={index} />
                ))}
              </Tabs>
              <Box sx={{ padding: 2, marginTop: 2 }}>
                {tabNames[activeTab] === "Basic Data" && payloadData ? (
                  <GenericTabsForChange disabled={true} materialID={structureData?.Number} dropDownData={dropDownData} basicDataTabDetails={allTabsData["Basic Data"]} activeViewTab={"Basic Data"} plantData={"basic"} />
                ) : tabNames[activeTab] === "Additional Data" ? (
                  <AdditionalData disableCheck={true} materialID={structureData?.Number} />
                ) : (
                  <>
                    {tabNames[activeTab] === MATERIAL_VIEWS.SALES && (
                      <>
                        <TaxDataSAP materialID={structureData?.Number} />
                        {salesGeneralFields?.length > 0 && <GenericViewGeneral materialID={structureData?.Number} GeneralFields={salesGeneralFields} disabled={true} dropDownData={dropDownData} viewName={MATERIAL_VIEWS?.SALES_GENERAL} />}
                      </>
                    )}
                    {tabNames[activeTab] === MATERIAL_VIEWS.PURCHASING && <> {purchaseGeneralFields?.length > 0 && <GenericViewGeneral materialID={structureData?.Number} GeneralFields={purchaseGeneralFields} disabled={true} dropDownData={dropDownData} viewName={MATERIAL_VIEWS?.PURCHASING_GENERAL} />}</>}
                    {tabContentData[tabNames[activeTab]]?.map((item, index) => (
                      <Accordion
                        key={index}
                        sx={{ marginBottom: "20px", boxShadow: 3 }}
                        expanded={expandedAccordion === index} // Control expansion state per accordion
                        onChange={handleAccordionChange(index, item, tabNames[activeTab])} // Update expansion state
                      >
                        <AccordionSummary
                          expandIcon={<ExpandMoreIcon />}
                          sx={{
                            backgroundColor: "#f5f5f5",
                            borderRadius: "10px",
                            padding: "8px 16px",
                            "&:hover": {
                              backgroundColor: "#e0e0e0", // Change background on hover
                            },
                          }}
                        >
                          <Typography variant="h6" sx={{ fontWeight: "bold" }}>
                            {tabNames[activeTab] === MATERIAL_VIEWS.PURCHASING  || tabNames[activeTab] === MATERIAL_VIEWS.COSTING || tabNames[activeTab] === MATERIAL_VIEWS.MRP || tabNames[activeTab] === MATERIAL_VIEWS.WORKSCHEDULING || tabNames[activeTab] === MATERIAL_VIEWS.SALES_PLANT 
                            ? `Plant - ${item?.Plant}` 
                            : tabNames[activeTab] === MATERIAL_VIEWS.SALES ? `Sales Org - ${item?.SalesOrg} ,  Distribution Channel - ${item?.DistrChan}` 
                            : tabNames[activeTab] === MATERIAL_VIEWS.ACCOUNTING ? `Plant - ${item?.ValArea}` 
                            : tabNames[activeTab] === MATERIAL_VIEWS.WAREHOUSE ? `Warehouse - ${item?.WhseNo}` : `${item?.Material}`}
                          </Typography>
                        </AccordionSummary>
                        {payloadData && 
                        <AccordionDetails sx={{ padding: "16px" }}>
                          <Typography sx={{ fontSize: "0.875rem", color: "#555" }}>
                            {/* {JSON.stringify(item, null, 2)} */}
                            <GenericTabsForChange disabled={true} materialID={structureData?.Number} dropDownData={dropDownData} basicDataTabDetails={allTabsData[tabNames[activeTab]]} activeViewTab={tabNames[activeTab]} plantData={tabNames[activeTab] === "Sales" ? `${item?.SalesOrg}-${item?.DistrChan}` : tabNames[activeTab] === "Purchasing" ? `${item?.Plant}` : tabNames[activeTab] === "Accounting" ? `${item?.ValArea}` : tabNames[activeTab] === "Warehouse" ? `${item?.WhseNo}` : `${item?.Plant}`} />
                          </Typography>
                        </AccordionDetails>
                        }
                      </Accordion>
                    ))}
                  </>
                )}
              </Box>
            </Box>
          </Box>
        ) : (
          <Box
            sx={{
              marginTop: "30px",
              border: `1px solid ${colors.secondary.grey}`,
              padding: "16px",
              background: `${colors.primary.white}`,
              textAlign: "center",
            }}
          >
            <span>{ERROR_MESSAGES.NO_DATA_AVAILABLE}</span>
          </Box>
        )}
      </Grid>
      <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
    </div>
  );
};

export default DisplayMaterialSAPView;
