import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  destination_GeneralLedger_Mass,
  destination_ProfitCenter,
  destination_ProfitCenter_Mass,
} from "../../destinationVariables";
import { v4 as uuidv4 } from "uuid";
import { doAjax } from "../../components/Common/fetchService";
import useButtonDTConfig from "@hooks/useButtonDTConfig";
import { DataGrid } from "@mui/x-data-grid";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { useLocation, useNavigate } from "react-router-dom";
import DescriptionIcon from "@mui/icons-material/Description";
import CloseIcon from "@mui/icons-material/Close";
import { useChangeLogUpdateGl } from "@hooks/useChangeLogUpdateGl";
import useGeneralLedgerFieldConfig from "@hooks/useGeneralLedgerFieldConfig";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import {
  TextField,
  IconButton,
  Box,
  Typography,
  Paper,
  Button,
  Tabs,
  Tab,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Radio,
  RadioGroup,
  FormControlLabel,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Snackbar,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Grid,
  Checkbox,
  Tooltip,
  Autocomplete,
  TableContainer,
  Chip,
  FormLabel
} from "@mui/material";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";

import { Add } from "@mui/icons-material";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import TaskAltIcon from "@mui/icons-material/TaskAlt";

import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import { colors } from "@constant/colors";
import { setGLRows, setSelectedRowIdGL, updateModuleFieldDataGL, setValidatedStatus, setdropdownDataForExtendedCode, setSelecteddropdownDataForExtendedCode, setGLPayload } from "@app/generalLedgerTabSlice";
import CloseFullscreenIcon from "@mui/icons-material/CloseFullscreen";
import CropFreeIcon from "@mui/icons-material/CropFree";
import useLang from "@hooks/useLang";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { CHANGE_LOG_STATUSES, DROP_DOWN_SELECT_OR_MAP, LOCAL_STORAGE_KEYS, VALIDATION_STATUS } from "@constant/enum";
import ReusableBackDrop from "../../components/Common/ReusableBackDrop";
import ReusableDataTable from "../../components/Common/ReusableTable";
import GenericTabsGlobal from "../../components/MasterDataCockpit/GenericTabsGlobal";
import { createPayloadForGL } from "../../functions";
import BottomNavGlobal from "../../components/RequestBench/RequestPages/BottomNavGlobal";
import { CHANGE_KEYS, MATERIAL_TYPE_DRODOWN, REQUEST_TYPE } from "../../constant/enum";
import moment from "moment/moment";
import { setDependentDropdown, setDropDown } from "./slice/generalLedgerDropDownSlice";
import { getLocalStorage } from "@helper/glhelper";
import useLogger from "@hooks/useLogger";
import FilterChangeDropdown from "@components/Common/ui/dropdown/FilterChangeDropdown";


const RequestDetailsExtendGL = ({ reqBench, apiResponses, setIsAttachmentTabEnabled,
  moduleName }) => {


  const [selectedRow, setSelectedRow] = useState(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [rowTabData, setRowTabData] = useState({});
  const [dropdownDataCompany, setDropdownDataCompany] = useState([]);
  const [dropdownDataCompanyForExtend, setDropdownDataCompanyForExtend] = useState([]);
  const [dropdownDataAccountType, setDropdownDataAccountType] = useState([]);
  const [dropdownDataTaxJur, setDropdownDataTaxJur] = useState([]);
  const [dropdownDataAccountGroup, setDropdownDataAccountGroup] = useState([]);
  const [dropdownDataProfitCtrGrp, setDropdownDataProfitCtrGrp] = useState([]);
  const [dropdownDataFormPlanning, setDropdownDataFormPlanning] = useState([]);
  const [dropdownDataCOA, setDropdownDataCOA] = useState([]);
  const [dropdownDataCountry, setDropdownDataCountry] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState("");
  const [dropdownDataRegion, setDropdownDataRegion] = useState([]);
  const [dropdownDataSegment, setDropdownDataSegment] = useState([]);
  const [dropdownDataLanguage, setDropdownDataLanguage] = useState([]);
  const [missingFieldsDialogOpen, setMissingFieldsDialogOpen] = useState(false);
  const [missingFields, setMissingFields] = useState([]);
  const [isAddRowEnabled, setIsAddRowEnabled] = useState(false);
  const [isSaveAsDraftEnabled, setIsSaveAsDraftEnabled] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [openSnackBar, setOpenSnackBar] = useState(false);
  const [alertMsg, setAlertMsg] = useState('');
  const [alertType, setAlertType] = useState('success');
  const [isLoading, setIsLoading] = useState(false);
  const [validatedRows, setValidatedRows] = useState({});
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const { getButtonsDisplayGlobal } = useButtonDTConfig();
  const [originalRowData, setOriginalRowData] = useState({});
  const [originalTabData, setOriginalTabData] = useState({});
  const [isGridZoomed, setIsGridZoomed] = useState(false);
  const [isTabsZoomed, setIsTabsZoomed] = useState(false);
  const [openOrgData, setOpenOrgData] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [openAddMatPopup, setOpenAddMatPopup] = useState(false);
  const [withReference, setWithReference] = useState("yes");
  const [selectedMaterials, setSelectedMaterials] = useState(null);
  const [withRefValues, setWithRefValues] = useState({});
  const [dropDownData, setDropDownData] = useState({});
  const [selectedMatLines, setSelectedMatLines] = useState([]);
  const [isDropdownLoading, setIsDropdownLoading] = useState({
    "Material No": false,
  });
  const [lineNumberCounter, setLineNumberCounter] = useState(0);
  const [isFocused, setIsFocused] = useState(false);
  const [charCount, setCharCount] = useState({});
  const [validateEnabled, setValidateEnabled] = useState(false);
  const [selectedCostCenters, setSelectedCostCenters] = useState([]);
  
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useLang();

  const initialPayload = useSelector((state) => state.payload.payloadData);
  const requestType = initialPayload?.RequestType;
  const isFieldMandatory = (fieldName) => mandatoryFields.includes(fieldName);
  let task = useSelector((state) => state?.userManagement.taskData);
  const { updateChangeLogGl } = useChangeLogUpdateGl();
  const validatedStatus = useSelector(
    (state) => state?.generalLedger?.validatedRowsStatus
  );
  const selectedExtendDropdownData = useSelector(
    (state) => state?.generalLedger?.selecteddropdownDataForExtendedCode
  );
  const fixedOption = "Basic Data";
  const [selectedViews, setSelectedViews] = useState([fixedOption]);
  const selectedRowId = useSelector((state) => state.generalLedger.selectedRowId);
  const companycodeExtendedTo = useSelector((state) => state.generalLedger.dropdownDataForExtendedCode);

  const generalLedgerTabs = useSelector((state) => {
    const tabs = state.generalLedger.generalLedgerTabs || [];
    return tabs.filter((tab) => tab.tab !== "Initial Screen");
  });
  const createPayloadCopyForChangeLog = useSelector((state) => state.changeLog.createChangeLogDataGL || []);
  const createChangeLogData = useSelector((state) => state.changeLog.createChangeLogDataGL);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  const isrequestId = queryParams.get("RequestId");
  const requestId = queryParams.get("RequestId");
  const selectedModule = getLocalStorage(LOCAL_STORAGE_KEYS.MODULE,true, {})
  const requestHeaderSlice = useSelector((state) => state.request.requestHeader)
  const selectedModuleSelector = DROP_DOWN_SELECT_OR_MAP[selectedModule] || (() => ({}));
  const allDropDownData = useSelector(selectedModuleSelector)
  const requestHeaderData = useSelector((state) => state.requestHeader);
  const { loading, error, fetchGeneralLedgerFieldConfig } =useGeneralLedgerFieldConfig(moduleName);
  const glRows = useSelector((state) => state.generalLedger.payload.rowsHeaderData);
  const [rows, setRows] = useState(glRows || []);
  const reduxPayload = useSelector((state) => state.generalLedger.payload);
  const rowsBodyData = useSelector(
    (state) => state.generalLedger.payload?.rowsBodyData || {}
  );
  let requestStatus = rowsBodyData?.[glRows[0]?.id]?.["Torequestheaderdata"]?.["RequestStatus"]
  const selectedCompanyCodeData = useSelector(
    (state) => state.generalLedger.payload?.rowsHeaderData[0]?.companyCode || {}
  );
  const { customError } = useLogger()
  
  const firstrowData = useSelector(
    (state) => state.generalLedger.payload?.rowsHeaderData
  );
  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons)
  const rmSearchForm = useSelector(
    (state) => state.commonFilter["GeneralLedger"]
  );
  const selectAllOption = { code: "ALL", desc: "Select All" };
  const withRefParams = ["Chart Of Account", "Company Code", "Account Type", "Account Group", "GL Account"];
  const handleChangeExtendCompanycode = (event, newValue) => {
    const isSelectAllClicked = newValue?.some(
      (val) => val.code === selectAllOption.code
    );
    if (isSelectAllClicked) {
      const allWithoutSelectAll = companycodeExtendedTo?.[selectedRow?.id];
      setSelectedOptions(allWithoutSelectAll);
      dispatch(setSelecteddropdownDataForExtendedCode({
        uniqueId: selectedRow?.id,
        data: allWithoutSelectAll
      }));
    } else {
      dispatch(setSelecteddropdownDataForExtendedCode({
        uniqueId: selectedRow?.id,
        data: newValue
      }));
    }
  };
  const getOptionLabel = (option) =>
    option.code === "ALL" ? option.desc : `${option.code} - ${option.desc}`;
  const isOptionSelected = (option, value) =>
    value.some((val) => val.code === option.code);
  const toggleGridZoom = () => {
    setIsGridZoomed(!isGridZoomed);
    if (isTabsZoomed) setIsTabsZoomed(false);
  };
  const toggleTabsZoom = () => {
    setIsTabsZoomed(!isTabsZoomed);
    if (isGridZoomed) setIsGridZoomed(false);
  };
 

useEffect(() => {
    if( reqBench == "true" && ( glRows?.length > 0 )) {
      setSelectedRow(glRows[0]);
      dispatch(setSelectedRowIdGL(glRows[0]?.id));
    }
  }, []);

  useEffect(() => {
    if (!generalLedgerTabs?.length && (withReference === "yes")) {
      fetchGeneralLedgerFieldConfig();
    }
  }, []);

  useEffect(() => {
    if (withReference === "no") {
      setWithRefValues({});
    }
  }, [withReference])

  useEffect(() => {
    if ((task?.ATTRIBUTE_1 || isrequestId)) {
      getButtonsDisplayGlobal("General Ledger", "MDG_DYN_BTN_DT", "v2");
    }
  }, [task]);

  useEffect(() => {
      const allRowsValidatedAndClean =
        glRows.length > 0 &&
        glRows.every((row) => {
          const isValidated = validatedRows[row.id] === true;
          const isClean = !isRowDirty(row.id);
          return isValidated && isClean;
        });
  
      setIsAddRowEnabled(allRowsValidatedAndClean);
      setValidateEnabled(allRowsValidatedAndClean);
    }, [glRows, rowsBodyData, validatedRows, originalRowData, originalTabData]);

  const getAllmandetoryjson = () => {
    const extractedFields = [];
    generalLedgerTabs.forEach((tabObj) => {
      const data = tabObj.data;
      Object.values(data).forEach((fieldArray) => {
        extractedFields.push(...fieldArray);
      });
    });
    return extractedFields
  }

  const columns = [
    {
      field: "included",
      headerName: "Included",
      width: 100,       
      minWidth: 100,
      align: "center",
      headerAlign: "center",
      renderCell: (params) =>
        <Checkbox
          checked={params.row.included}
          disabled={false}
          onChange={(e) =>
            handleRowInputChange(e.target.checked, params.row.id, "included")
          }
        />,
    },
    {
      field: "lineNumber",
      headerName: "SL No,",
      width: 100,  
      minWidth: 100,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "chartOfAccount",
      headerName: "Chart Of Account",
      align: "center",
      headerAlign: "center",
      width: 250, 
      minWidth: 200,
      // renderCell: (params) => {
      //   return (

      //     <TextField
      //       value={params.row.chartOfAccount}
      //       disabled={true}
      //       variant="outlined"
      //       size="small"
      //       sx={{
      //         position: "relative",
      //         "& .MuiInputBase-root": {
      //           height: "45px",
      //           alignItems: "center",
      //         },
      //         "& .MuiFormHelperText-root": {
      //           marginLeft: "0px",
      //         },
      //         "& .MuiInputBase-root.Mui-disabled": {
      //           "& > input": {
      //             WebkitTextFillColor: colors.black.dark,
      //             color: colors.black.dark,
      //           },
      //         },
      //       }}
      //     />
          
      //   );
      // },
    },
    {
      field: "companyCode",
      headerName: "Company Code",
      align: "center",
      headerAlign: "center",
      width: 250,
      minWidth: 200,
      // renderCell: (params) => {
      //   return (
      //      <TextField
      //       value={params.row.companyCode}
      //       disabled={true}
      //       variant="outlined"
      //       size="small"
      //       sx={{
      //         position: "relative",
      //         "& .MuiInputBase-root": {
      //           height: "45px",
      //           alignItems: "center",
      //         },
      //         "& .MuiFormHelperText-root": {
      //           marginLeft: "0px",
      //         },
      //         "& .MuiInputBase-root.Mui-disabled": {
      //           "& > input": {
      //             WebkitTextFillColor: colors.black.dark,
      //             color: colors.black.dark,
      //           },
      //         },
      //       }}
      //     />
      //   );
      // },
    },
    {
      field: "accountType",
      headerName: "Account Type",
      width: 250, 
      minWidth: 200,
      // renderCell: (params) => {
      //   return (
      //    <TextField
      //       value={params.row.accountType}
      //       disabled={true}
      //       variant="outlined"
      //       size="small"
      //       sx={{
      //         position: "relative",
      //         "& .MuiInputBase-root": {
      //           height: "45px",
      //           alignItems: "center",
      //         },
      //         "& .MuiFormHelperText-root": {
      //           marginLeft: "0px",
      //         },
      //         "& .MuiInputBase-root.Mui-disabled": {
      //           "& > input": {
      //             WebkitTextFillColor: colors.black.dark,
      //             color: colors.black.dark,
      //           },
      //         },
      //       }}
      //     />
      //   );
      // },
    },
    {
      field: "accountGroup",
      headerName: "Account Group",
      width: 250, 
      minWidth: 200,
    //  renderCell: (params) => {
    //     return (
    //      <TextField
    //         value={params.row.accountGroup}
    //         disabled={true}
    //         variant="outlined"
    //         size="small"
    //         sx={{
    //           position: "relative",
    //           "& .MuiInputBase-root": {
    //             height: "45px",
    //             alignItems: "center",
    //           },
    //           "& .MuiFormHelperText-root": {
    //             marginLeft: "0px",
    //           },
    //           "& .MuiInputBase-root.Mui-disabled": {
    //             "& > input": {
    //               WebkitTextFillColor: colors.black.dark,
    //               color: colors.black.dark,
    //             },
    //           },
    //         }}
    //       />
    //     );
    //   },
    },
    {
      field: "glAccountNumber",
      headerName: "General Ledger Number",
      width: 250, 
      minWidth: 200,

      // renderCell: (params) => {
      //   const accountGroupValue = params.row.accountGroup
      //   const value = params.row.glAccountNumber || "";
      //   const isInvalid = value.length > 0 && value.length < 10;

      //   const handleKeyDown = (e) => {
      //     if (
      //       ["Backspace", "Delete", "Tab", "Escape", "Enter", "ArrowLeft", "ArrowRight"].includes(
      //         e.key
      //       )
      //     ) {
      //       return;
      //     }
      //     if (!/^\d$/.test(e.key)) {
      //       e.preventDefault();
      //     }
      //   };

      //   const handlePaste = (e) => {
      //     const paste = e.clipboardData.getData("text");
      //     if (!/^\d+$/.test(paste)) {
      //       e.preventDefault(); 
      //     }
      //   };
      //   return (
      //     <TextField
      //       value={value}
      //       onChange={(e) => {
      //         const numericValue = e.target.value.slice(0, 10);
      //         handleRowInputChange(numericValue, params.row.id, "glAccountNumber");
      //       }}
      //       disabled={true}
      //       onKeyDown={handleKeyDown}
      //       onPaste={handlePaste}
      //       variant="outlined"
      //       size="small"
      //       placeholder={accountGroupValue?.FromAcct && accountGroupValue?.ToAcct
      //         ? `${accountGroupValue?.FromAcct} - ${accountGroupValue?.ToAcct}`
      //         : `-`}
      //       fullWidth
      //       error={isInvalid}
      //       helperText={isInvalid ? "Number should be 10 digits" : ""}
      //       FormHelperTextProps={{
      //         sx: {
      //           marginTop: "30px",
      //           paddingLeft: "15px",
      //           position: "absolute",
      //           style: {
      //             paddingBottom: "0px", 
      //           },
      //         },
      //       }}
      //       inputProps={{
      //         inputMode: "numeric",
      //         maxLength: 10,
      //       }}
      //       sx={{
      //         position: "relative",
      //         "& .MuiInputBase-root": {
      //           height: "45px",
      //           alignItems: "center",
      //         },
      //         "& .MuiFormHelperText-root": {
      //           marginLeft: "0px",
      //         },
      //         "& .MuiInputBase-root.Mui-disabled": {
      //           "& > input": {
      //             WebkitTextFillColor: colors.black.dark,
      //             color: colors.black.dark,
      //           },
      //         },
      //       }}
      //     />

      //   );
      // }
    },
    {
      field: "businessSegment",
      headerName: "Business Segment",
      width: 250,      
      minWidth: 200,
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={[{ code: "CRUDE", desc: "" }, { code: "INTERSTATE", desc: "" }, { code: "NA", desc: "" }] || []}
            value={params.row.businessSegment}
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "businessSegment")
            }
            placeholder={t('Select Business Segment')}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {      
      field: "CoCodeToExtend",
      headerName: "Company Code Extend To", 
      width: 250,     
      minWidth: 200,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <Button
            variant="contained"
            size="small"
            sx={{ marginLeft: "4px" }}
            onClick={() => {
              setOpenOrgData(true)
            }}
          >
            Company Code Extend To
          </Button>
        );
      },
      
    },
    // {
    //   field: "action",
    //   headerName: "Action",
    //   width: 80,        
    //   minWidth: 80,
    //   headerAlign: "center",
    //   renderHeader: () => <span style={{ fontWeight: "bold" }}>{t("Action")}</span>,
    //   renderCell: (params) => {

    //     const rowId = params.row.id;
    //     const rowData = {
    //       id: rowId,
    //       ...rowsBodyData[rowId],
    //     };

    //     const validateStatus = getValidationStatus(rowId);

    //     let mandetoryFieldsData = getAllmandetoryjson()

    //     const minrangeOfGlAccount = parseInt(params.row.accountGroup?.FromAcct);
    //     const maxrangeOfGlAccount = parseInt(params.row.accountGroup?.ToAcct);
    //     const glValue = parseInt(params.row?.glAccountNumber);
    //     const isInRange =
    //       !isNaN(minrangeOfGlAccount) &&
    //       !isNaN(maxrangeOfGlAccount) &&
    //       glValue >= minrangeOfGlAccount &&
    //       glValue <= maxrangeOfGlAccount;



    //     const handleValidateClick = (e) => {
    //       e.stopPropagation();
    //       if (glValue == '' || glValue == NaN || glValue == undefined) {
    //         if (isInRange) {
    //           handleValidate(params.row, rowData, generalLedgerTabs);
    //         } else {
    //           setAlertType('error');
    //           setAlertMsg('GlAccount not In Range.');
    //           setOpenSnackBar(true);
    //         }
    //       } else {
    //         handleValidate(params.row, rowData, generalLedgerTabs);
    //       }

    //     };

    //     return (
    //       <Box>
    //         <Tooltip
    //           title={
    //             validateStatus === "success"
    //               ? "Validated Successfully"
    //               : validateStatus === "error"
    //                 ? "Validation Failed"
    //                 : "Click to Validate"
    //           }
    //         >
    //           <IconButton onClick={handleValidateClick} color={validateStatus}>
    //             {validateStatus === "error" ? (
    //               <CancelOutlinedIcon />
    //             ) : (
    //               <TaskAltIcon />
    //             )}
    //           </IconButton>
    //         </Tooltip>
           
    //       </Box>
    //     );
    //   },
    // },
  ];
  const mandatoryFields =
    ["chartOfAccount", "companyCode", "accountType", "accountGroup", "glAccountNumber", "longDescription", "businessSegment"];

  const isEqual = (a, b) => JSON.stringify(a) === JSON.stringify(b);


  const handleSnackBarClose = () => {
    setOpenSnackBar(false);
  };

  useEffect(() => {
    const allRowsValidated = glRows.length > 0 &&
      glRows.every(row => validatedRows[row.id] === true);
    setIsAddRowEnabled(allRowsValidated);
    setIsSaveAsDraftEnabled(allRowsValidated);
  }, [glRows, validatedRows]);


  const checkGLAccountDuplicateCheckGeneralLedger = (row) => {
    setIsLoading(true);
    var duplicateCheckDescription = {
      glAccount: row?.glAccountNumber,
      coa: row?.longDescription,
    };
    const hSuccess = (data) => {

      setIsLoading(false);
      if (data?.body?.length > 0) {
        setAlertType('error');
        setAlertMsg('Duplicate GL Account Number  found.');
        setValidatedRows(prev => ({ ...prev, [row.id]: false }));
      } else {
        checkDuplicateNameCheckGeneralLedger(row)
      }
      setOpenSnackBar(true);
    };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType('error');
      setAlertMsg('An error occurred during validation.');
      setOpenSnackBar(true);
      setValidatedRows(prev => ({ ...prev, [row.id]: false }));
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/alter/fetchGlAccountNCoaDupliChk`,
      "post",
      hSuccess,
      hError,
      duplicateCheckDescription
    )
  };

  const checkDuplicateNameCheckGeneralLedger = (row) => {
    setIsLoading(true);
    var duplicateCheckDescription = {
      coa: row?.chartOfAccount,
      glName: row?.shortDescription,
    };
    const hSuccess = (data) => {

      setIsLoading(false);
      if (data?.body?.length > 0) {
        setAlertType('error');
        setAlertMsg('Duplicate GL Name found.');
        setValidatedRows(prev => ({ ...prev, [row.id]: false }));
      } else {
        checkDuplicateDescCheckGeneralLedger(row)

      }
      setOpenSnackBar(true);
    };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType('error');
      setAlertMsg('An error occurred during validation.');
      setOpenSnackBar(true);
      setValidatedRows(prev => ({ ...prev, [row.id]: false }));
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/alter/fetchGlNameNCoaDupliChk`,
      "post",
      hSuccess,
      hError,
      duplicateCheckDescription
    )
  };


  const checkDuplicateDescCheckGeneralLedger = (row) => {
    setIsLoading(true);
    var duplicateCheckDescription = {
      coa: row?.chartOfAccount,
      glDesc: row?.chartOfAccount
    };
    const hSuccess = (data) => {

      setIsLoading(false);
      if (data?.body?.length > 0) {
        setAlertType('error');
        setAlertMsg('Duplicate Gl Long Text found.');
        setValidatedRows(prev => ({ ...prev, [row.id]: false }));
      } else {
        setAlertType('success');
        setAlertMsg('Validation Successful');
        setValidatedRows(prev => ({ ...prev, [row.id]: true }));
        setIsAttachmentTabEnabled(true);
      }
      setOpenSnackBar(true);
    };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType('error');
      setAlertMsg('An error occurred during validation.');
      setOpenSnackBar(true);
      setValidatedRows(prev => ({ ...prev, [row.id]: false }));
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/alter/fetchGlDescNCoaDupliChk`,
      "post",
      hSuccess,
      hError,
      duplicateCheckDescription
    )
  };

  const handleValidate = (row, tab, config) => {
    const missing = [];

    const allFields = config.flatMap(section => {
      return Object.values(section.data).flat(); 
    });

    allFields?.forEach((field) => {
      if (field.visibility === "Mandatory") {
        const value = tab[field.jsonName];
        if (
          value === null ||
          value === undefined ||
          (typeof value === "string" && value.trim() === "")
        ) {
          missing.push(field.fieldName);
        }
      }
    });
    const headerMap = {
      companyCode: "Company Code",
      glAccountNumber: "General Ledger Number",
      businessSegment: "Business Segment",
      controllingArea: "Controlling Area",
      longDescription: "Long Text",
      shortDescription: "Short Text",
      longDescription: "Long Text"
    };
    mandatoryFields.forEach((field) => {
      const value = row[field];
      const displayName = headerMap[field] || field;
      if (
        value === null ||
        value === undefined ||
        (typeof value === "string" && value.trim() === "")
      ) {
        missing.push(displayName);
      } else if (
        field === "costCenterNumber" &&
        (value.length !== 10 || !/^[a-zA-Z0-9]+$/.test(value))
      ) {
        missing.push(displayName);
      }
    });

    const status = missing.length > 0 ? "error" : "success";

    dispatch(setValidatedStatus({ rowId: row.id, status }));

    if (status === "error") {
      setMissingFields(missing);
      setMissingFieldsDialogOpen(true);
    } else {
      setOriginalRowData((prev) => ({
        ...prev,
        [row.id]: JSON.parse(JSON.stringify(row)),
      }));
      setOriginalTabData((prev) => {
        const { id, ...restTab } = tab;
        return {
          ...prev,
          [row.id]: JSON.parse(JSON.stringify(restTab)),
        };
      });
      checkGLAccountDuplicateCheckGeneralLedger(row);
    }
  };

  const handleMassValidate = (row) => {
    const missing = [];
    mandatoryFields.forEach((field) => {
      if (!row[field]) {
        missing.push(field);
      }
    });

    if (missing.length > 0) {
      setMissingFields(missing);
      setMissingFieldsDialogOpen(true);
      setValidatedRows(prev => ({ ...prev, [row.id]: false }));
      return false;
    } else {
      return new Promise((resolve) => {
        const hSuccess = (data) => {
          if (data?.body?.length > 0) {
            setValidatedRows(prev => ({ ...prev, [row.id]: false }));
            resolve(false);
          } else {
            setValidatedRows(prev => ({ ...prev, [row.id]: true }));
            resolve(true);
          }
        };

        const hError = () => {
          setValidatedRows(prev => ({ ...prev, [row.id]: false }));
          resolve(false);
        };

        doAjax(
          `/${destination_ProfitCenter}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${row?.controllingArea?.code}$$${row?.profitCenterNumber}`,
          "get",
          hSuccess,
          hError
        );
      });
    }
  };

  const validateAllRows = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData,
      selectedExtendDropdownData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);

      setAlertMsg("General Ledgers Validation initiated");
      setIsSaveAsDraftEnabled(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 3000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while validating the request");
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/validateMassGeneralLedger`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );

  };
  const handleCloseDialog = () => {
    setMissingFieldsDialogOpen(false);
  };

  const handleDelete = (id) => {
    const updatedRows = glRows.filter((row) => row.id !== id);
    dispatch(setGLRows(updatedRows))
    if (selectedRow?.id === id) {
      setSelectedRow(null);
    }
    const updatedRowTabData = { ...rowTabData };
    delete updatedRowTabData[id];
    setRowTabData(updatedRowTabData);

    setValidatedRows(prev => {
      const updated = { ...prev };
      delete updated[id];
      return updated;
    });
  };

  const minMaxRangeDetailsOfGlAccount = (value) => {

  }

  const handleRowInputChange = (value, id, field) => {
    if (field === "chartOfAccount") {
      getCompanyCode(value?.code, id)
      getAccountGroup(value?.code, id)
      getSortKey(id)
      getLanguage(id)

    }
    if (field === "companyCode") {
      getAccountCurrency(value?.code, id)
      getTaxCategory(value?.code, id)
      getHouseBank(value?.code, id)
      getAccontId(value?.code, id)
      getFiledStatusGroup(value?.code, id)
      getreconAccountType(id)
      getPlanningLevel(id)

      let filteredCompanyCodeForExtend = dropdownDataCompany?.filter(
        (item) => item.code !== value?.code)

      dispatch(setdropdownDataForExtendedCode({
        uniqueId: id,
        data: filteredCompanyCodeForExtend
      }));


      dispatch(
        updateModuleFieldDataGL({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "CompanyCode",
          data: value?.code,
          viewID: "Comp Codes"
        })
      );
    }
    if (field === "accountType") {
      getCostElementCategory(value?.code)
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "Accounttype",
          data: value?.code,
          viewID: "Type/Description"
        })
      );
      {
        requestId && !CHANGE_LOG_STATUSES.includes(requestStatus) && updateChangeLogGl({
          uniqueId: selectedRowId || selectedRow?.id,
          viewName: "Type/Description",
          plantData: '',
          fieldName: 'Account Type',
          jsonName: "Accounttype",
          currentValue: value?.code,
          requestId: initialPayload?.RequestId,
          childRequestId: requestId
        });
      }
    }
    if (field === "accountGroup") {
      minMaxRangeDetailsOfGlAccount(value)
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "AccountGroup",
          data: value?.code,
          viewID: "Type/Description"
        })
      );
      {
        requestId && !CHANGE_LOG_STATUSES.includes(requestStatus) && updateChangeLogGl({
          uniqueId: selectedRowId || selectedRow?.id,
          viewName: "Type/Description",
          plantData: '',
          fieldName: 'Account Group',
          jsonName: "AccountGroup",
          currentValue: value?.code,
          requestId: initialPayload?.RequestId,
          childRequestId: requestId
        });
      }


    }
    if (field === "longDescription") {
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "Description",
          data: value,
          viewID: "Basic Data"
        })
      );
      {
        requestId && !CHANGE_LOG_STATUSES.includes(requestStatus) && updateChangeLogGl({
          uniqueId: selectedRowId || selectedRow?.id,
          viewName: "Basic Data",
          plantData: '',
          fieldName: 'Long Text',
          jsonName: "Description",
          currentValue: value,
          requestId: initialPayload?.RequestId,
          childRequestId: requestId
        });
      }
    }
    if (field === "shortDescription") {
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "GLname",
          data: value,
          viewID: "Basic Data"
        })
      );
      {
        requestId && !CHANGE_LOG_STATUSES.includes(requestStatus) && updateChangeLogGl({
          uniqueId: selectedRowId || selectedRow?.id,
          viewName: "Basic Data",
          plantData: '',
          fieldName: 'Short Text',
          jsonName: "GLname",
          currentValue: value,
          requestId: initialPayload?.RequestId,
          childRequestId: requestId
        });
      }
    }
    const updatedRows =
      glRows.map((row) => (row.id === id ? { ...row, [field]: value } : row));
    dispatch(setGLRows(updatedRows))

  };

  const handleRowClick = (params) => {
    const clickedRow = params.row;
    setSelectedRow(clickedRow);
    dispatch(setSelectedRowIdGL(clickedRow?.GeneralLedgerID));
  };

  const handleAddRow = () => {
    const id = uuidv4();
    const newRow = {
      id,
      chartOfAccount: '',
      lineNumber: lineNumberCounter + 1,
      companyCode: '',
      accountType: '',
      accountGroup: '',
      glAccountNumber: '',
      businessSegment: '',
      included: true,
      isNew: true
    };
    dispatch(setGLRows([...glRows, newRow]))
    setSelectedRow(newRow);
    dispatch(setSelectedRowIdGL(newRow?.id));
    setValidatedRows(prev => ({ ...prev, [newRow.id]: false }));
  };

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const fieldsSyncedFromRow = {
    Description: "longDescription",
    GLname: "shortDescription",
    AccountType: "accountType",
    AccountGroup: "accountGroup",
  };


  const cleanTabForComparison = (tab, row) => {
    const cleanedTab = { ...tab };
    delete cleanedTab.id;
    for (const [tabKey, rowPath] of Object.entries(fieldsSyncedFromRow)) {
      const rowValue = rowPath
        .split(".")
        .reduce((acc, key) => (acc ? acc[key] : undefined), row);
      if (cleanedTab[tabKey] === rowValue) {
        delete cleanedTab[tabKey];
      }
    }
    return cleanedTab;
  };


  const isRowDirty = (rowId) => {
    const originalRow = originalRowData[rowId];
    const originalTab = originalTabData[rowId];
    const currentRow = glRows.find((r) => r.id === rowId);
    const currentTab = rowsBodyData[rowId];
    if (!originalRow || !originalTab || !currentRow || !currentTab) return true;

    const cleanedCurrentTab = cleanTabForComparison(currentTab, currentRow);
    const cleanedOriginalTab = cleanTabForComparison(originalTab, originalRow);

    return (
      !isEqual(originalRow, currentRow) ||
      !isEqual(cleanedOriginalTab, cleanedCurrentTab)
    );
  };

  const handleSelectAll = () => setSelectedViews(dropdownDataCompany || [fixedOption]);

  const getCompanyCodeBasedOnChartOfAccountExtend = (value, GlAccount,id) => {
      const hSuccesspresentGLAccount = (dataOfpresentGLAccount) => {
        if (dataOfpresentGLAccount?.statusCode === 200) {
          
          let filteredCompanyCodeForExtend = [];
          dataOfpresentGLAccount?.body?.map((item, index) => {
            let hash = {};
            hash["code"] = item.code;
            hash["desc"] = item.desc;
            filteredCompanyCodeForExtend.push(hash);
          });
         
          //setExtendDropdownList(filteredCompanyCodeForExtend);
  
          dispatch(setdropdownDataForExtendedCode({
                  uniqueId: id,
                  data: filteredCompanyCodeForExtend
                }));
        } else {
          
        }
      };
  
      const hErrorpresentGlAccount = (error) => {
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getAvailableCompCodesToExtend?coa=${value}&glAccount=${GlAccount}`,
        "get",
        hSuccesspresentGLAccount,
        hErrorpresentGlAccount
      );
    };

  const AddCopiedGLData = () => {
    setOpenAddMatPopup(false);
    if (withReference === "yes") {

      if (glRows?.length >= 0) {

        const id = uuidv4();
        setLineNumberCounter(lineNumberCounter + 1)
        const newRows = selectedCostCenters.map((glItem, index) => {
          const id = uuidv4();
          
          getCompanyCodeBasedOnChartOfAccountExtend(glItem.coa, glItem.code,id)
          return {
            id: id,
            chartOfAccount: glItem.coa,
            companyCode: glItem.companyCode,
            lineNumber: lineNumberCounter + index + 10,
            accountType: glItem.accType,
            accountGroup: glItem.accGroup,
            glAccountNumber: glItem.code,
            businessSegment: '',
            included: true,
            isNew: true
          };

        });

        setSelectedRow(newRows);
        dispatch(setSelectedRowIdGL(newRows[0]?.id));


        dispatch(setGLRows([...glRows, ...newRows]));
       //// setValidatedRows(prev => ({ ...prev, [newRow.id]: false }));
        //fetchGeneralLedgerFieldConfig();
       // getCopiedGeneralLedgerData(newRow);

      }
    } else {
      handleAddRow();
    }
  };

  const getCopiedGeneralLedgerData = (newRow) => {

    const payload = {
      "glAccCOACoCode": [
        {
          "glAccount": newRow?.glAccountNumber,
          "chartOfAccount": newRow?.chartOfAccount,
          "companyCode": newRow?.companyCode
        },
      ]
    };
    const successHandler = (data) => {
      const rawData = data?.body || [];
      const flatData = rawData.reduce((acc, obj) => ({
        ...acc,
        ...obj.typeNDescriptionViewDto,
        ...obj.controlDataViewDto,
        ...obj.createBankInterestViewDto,
        ...obj.keywordNTranslationViewDto,
        ...obj.informationViewDto
      }), {});

      const updatedDatarawData = flatData && typeof flatData === 'object'
        ? {
            ...Object.fromEntries(
              Object.entries(flatData).map(([key, value]) => [
                key,
                (key === "GLname" || key === "Description") ? "" : value
              ])
            ),
            CompanyCode: newRow?.companyCode
          }
        : {};

  
      const requestHeaderData = reduxPayload?.requestHeaderData;


      let rowsbodydatares = reduxPayload?.rowsBodyData || {};

      let rowsBodyData = {
        ...rowsbodydatares,
        [newRow?.id]: {
          ...updatedDatarawData,
        },
      };
      const rowsHeaderDataapi = reduxPayload?.rowsHeaderData;

      const rowsHeaderData = [...rowsHeaderDataapi, newRow]


      const payload = {
        requestHeaderData,
        rowsBodyData,
        rowsHeaderData
      };


      dispatch(setGLPayload(payload));

    };

    const errorHandler = (err) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGeneralLedgersData`,
      "post",
      successHandler,
      errorHandler,
      payload
    );

  }

  const getValidationStatus = (rowId) => {
    const status = validatedStatus[rowId];
    const dirty = isRowDirty(rowId);
    if (!status) return "default";
    return isRowDirty(rowId) ? "error" : status;
  };

  useEffect(() => {
    setSelectedRow(glRows[0])
  }, []);

  const getCompanyCode = (coa) => {
    const hSuccess = (data) => {
      setDropdownDataCompany(data.body)
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getCompanyCode?chartAccount=${coa}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCompanyCoderef = (coa) => {
    const hSuccess = (data) => {
      setDropDownData((prev) => ({ ...prev, ["Company Code"]: data.body }))
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getCompanyCode?chartAccount=${coa}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccountType = () => {
    const hSuccess = (data) => {
      setDropdownDataAccountType(data.body)
      setDropDownData((prev) => ({ ...prev, ["Account Type"]: data.body }))
      dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "Accounttype",
          data: data.body || [],
          keyName2: selectedRowId ?? selectedRow?.id,
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGLAccountType`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccountCurrency = (compCode, id = '') => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "AccountCurrency",
          data: data.body || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountCurrency?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getFiledStatusGroup = (compCode, id) => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "FieldStsGrp",
          data: data.body || [],
          keyName2: selectedRowId || selectedRow?.id, id,
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getFieldStatusGroup?fieldStatusVariant=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getTaxCategory = (compCode, id = '') => {
    const hSuccess = (data) => {

      dispatch(
        setDependentDropdown({
          keyName: "Taxcategory",
          data: data.body || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getTaxCategory?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getHouseBank = (compCode, id = '') => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "HouseBank",
          data: data.body || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getHouseBank?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccontId = (compCode) => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "AccountId",
          data: data.body || [],
          keyName2: selectedRowId || selectedRow?.id || companyCode,
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountId?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };


  const getCostElementCategory = (accType, id = '') => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "CostEleCategory",
          data: data.body || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getCostElementCategory?accountType=${accType}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getreconAccountType = ( id = '') => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "ReconAcc",
          data: data.body || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getReconAccountForAccountType`,
      "get",
      hSuccess,
      hError
    );
  };

  const getPlanningLevel = ( id = '') => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "Planninglevel",
          data: data.body || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getPlanningLevel`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccountGroup = (coa, id = '') => {
    const hSuccess = (data) => {

      let accGrparr = []
      data?.body?.map((item) => {
        let hash = {}
        hash["code"] = item?.AccountGroup
        hash["desc"] = item?.Description
        hash["FromAcct"] = item?.FromAcct
        hash["ToAcct"] = item?.ToAcct
        accGrparr?.push(hash)
      })
      setDropdownDataAccountType(accGrparr)
      dispatch(
        setDependentDropdown({
          keyName: "AccountGroup",
          data: accGrparr || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )

      dispatch(setDropDown({ keyName: "accountGroup", data: accGrparr }));
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountGroup?chartAccount=${coa}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccountGroupref = (coa) => {
    const hSuccess = (data) => {

      let accGrparr = []
      data?.body?.map((item) => {
        let hash = {}
        hash["code"] = item?.AccountGroup
        hash["desc"] = item?.Description
        hash["FromAcct"] = item?.FromAcct
        hash["ToAcct"] = item?.ToAcct
        accGrparr?.push(hash)
      })

      setDropDownData((prev) => ({ ...prev, ["Account Group"]: accGrparr }))

    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountGroup?chartAccount=${coa}`,
      "get",
      hSuccess,
      hError
    );
  };


  const getFormPlanningFrp = () => {
    const hSuccess = (data) => {
      setDropdownDataFormPlanning(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Template", data: data.body },
      });
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_ProfitCenter}/data/getFormPlanningTemp`,
      "get",
      hSuccess,
      hError
    );
  };

  const getChartOfAccount = () => {
    const hSuccess = (data) => {
      setDropDownData((prev) => ({ ...prev, ["Chart Of Account"]: data.body }))
      dispatch(setDropDown({ keyName: "COA", data: data.body }));
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getChartOfAccounts`,
      "get",
      hSuccess,
      hError
    );
  };

  const getBusSeg = () => {
    const hSuccess = (data) => {
      setDropdownDataCOA(data.body);
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_ProfitCenter}/data/getBusinessSegment`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    getChartOfAccount()
    getAccountType()
    getBusSeg()
  }, []);


  const [rowRegionData, setRowRegionData] = useState({});


  const getSegment = () => {
    const hSuccess = (data) => {
      setDropdownDataSegment(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Segment", data: data.body },
      });
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_ProfitCenter}/data/getSegment`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getSegment();
  }, []);

  const getLanguage = () => {
    const hSuccess = (data) => {
      setDropdownDataLanguage(data.body);
      dispatch(
        setDependentDropdown({
          keyName: "Language",
          data: data.body || [],
          keyName2: selectedRowId ?? selectedRow?.id,
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_ProfitCenter}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };

  const getSortKey = (id = '') => {
    const hSuccess = (data) => {
      setDropdownDataLanguage(data.body);
      dispatch(
        setDependentDropdown({
          keyName: "Sortkey",
          data: data.body || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getSortKey`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleSaveAsDraft = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData, createChangeLogData, selectedExtendDropdownData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("General Ledger Submission saved as draft.");
      setOpenSnackBar(true);
      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/generalLedgersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );

  };

  const handleSubmitForReview = () => {
    setLoaderMessage("");
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData, createChangeLogData, selectedExtendDropdownData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(
        "General Ledger submission for save as draft initiated"
      );
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/generalLedgersSubmitForReview`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );

  };

  const handleValidateAndSyndicate = (type) => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData, createChangeLogData, selectedExtendDropdownData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      if (type === "validate")
        setAlertMsg("General Ledger Validation initiated");
      else if (type === "syndicate")
        setAlertMsg("General Ledger Syndication initiated");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while validating the request");
    };

    doAjax(
      type === "validate" ? `/${destination_GeneralLedger_Mass}/massAction/validateMassGeneralLedger` : `/${destination_GeneralLedger_Mass}/massAction/createGeneralLedgersApproved`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );

  };

  const handleSubmitForApprove = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData, createChangeLogData, selectedExtendDropdownData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(
        "General Ledger submission for Approve initiated"
      );
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/generalLedgersApprovalSubmit`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );

  };


  const viewsClose = (event, reason) => {
    setOpenOrgData(false);
  };

  const handleSendBack = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData, createChangeLogData, selectedExtendDropdownData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Profit Centers submission for Approve initiated");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersSendForCorrection`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const handleCorrection = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData, createChangeLogData, selectedExtendDropdownData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(data?.message ?? "General Ledgers Sent for Correction !");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while sending for correction");
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersSendForReview`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const getGlAcountDropdownDataFromSearchFilter = () => {

    let payload = {
      glAccount: "",
      chartOfAccount: withRefValues?.["Chart Of Account"]?.["code"],
      postAutoOnly: "",
      companyCode: withRefValues?.["Company Code"]?.["code"] ?? '',
      taxCategory: "",
      glAcctLongText: "",
      postingWithoutTaxAllowed: "",
      blockedForPostingInCOA: "",
      shortText: "",
      blockedForPostingInCompany: "",
      accountGroup: withRefValues?.["Account Group"]?.["code"] ?? '',
      glAccountType: withRefValues?.["Account Type"]?.["code"] ?? '',
      fieldStatusGroup: "",
      openItemMgmtbyLedgerGroup: "",
      openItemManagement: "",
      reconAccountforAcctType: "",
      fromDate:
        moment(rmSearchForm?.createdOn[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate:
        moment(rmSearchForm?.createdOn[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      createdBy: "",
      top: 100,
      skip: 0,
    };
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        let glAccountArr = [];

        data?.body?.list?.forEach((item) => {
            let glAccountHash = {};
            glAccountHash["code"] = item?.GLAccount;
            glAccountHash["desc"] = item?.GLname;
            glAccountHash["coa"] = item?.COA;
            glAccountHash["accType"] = item?.Accounttype;
            glAccountHash["companyCode"] = item?.CompanyCode;
            glAccountHash["accGroup"] = item?.AccountGroup;
            glAccountArr.push(glAccountHash);
        });

        setDropDownData((prev) => ({ ...prev, ["GL Account"]: glAccountArr }))

      }
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGeneralLedgersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );


  }

  const handleSalesOrgWithREF = (key, newValue) => {
    setWithRefValues((prev) => ({ ...prev, [key]: newValue }));
    if (key === "Chart Of Account" && newValue) {

      getCompanyCoderef(newValue?.code);
      getAccountGroupref(newValue?.code)
    }
    if (key === "Account Group") {
      getGlAcountDropdownDataFromSearchFilter()
    }  
  }
  
  const handleRejectAndCancel = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(data?.message ?? "General Ledgers Rejected !");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while rejecting the request");
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersRejected`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  return (
    <div>
      <ReusableSnackBar
        openSnackBar={openSnackBar}
        alertMsg={alertMsg}
        handleSnackBarClose={handleSnackBarClose}
        alertType={alertType}
        isLoading={isLoading}
      />

      {error && <Typography color="error">{t("Error loading data")}</Typography>}
      <div
        style={{ padding: "0", width: "100%", margin: "0", marginTop: "20px" }}
      >

      <Box
        sx={{
           position: isGridZoomed ? "fixed" : "relative",
            top: isGridZoomed ? 0 : "auto",
            left: isGridZoomed ? 0 : "auto",
            right: isGridZoomed ? 0 : "auto",
            bottom: isGridZoomed ? 0 : "auto",
            width: isGridZoomed ? "100vw" : "100%",
            height: isGridZoomed ? "100vh" : "auto",
            zIndex: isGridZoomed ? 1004 : 1,
            backgroundColor: isGridZoomed ? "white" : "transparent",
            padding: isGridZoomed ? "20px" : "0",
            display: "flex",
            flexDirection: "column",
            boxShadow: isGridZoomed
              ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
              : "none",
            transition: "all 0.3s ease",
            borderRadius: "8px",
            border: "1px solid #e0e0e0",
        }}
      >

        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "8px 16px",
            backgroundColor: "#f5f5f5",
            borderRadius: "8px 8px 0 0",
          }}
        >
          <Typography gutterBottom sx={{ fontWeight: "bold", fontSize: "16px", mb: -2 }}>
            {t("General Ledger Data")}
          </Typography>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<Add />}
              onClick={() => {

                setOpenAddMatPopup(true);
                setSelectedMatLines([]);
                setSelectedMaterials(null);
                setWithRefValues({});
                setSelectedRow(null)
                dispatch(setSelectedRowIdGL([]));
                
              }}
              sx={{ mb: 1 }}
            >
              {t("Add")}
            </Button>
            <Tooltip
              title={isGridZoomed ? "Exit Zoom" : "Zoom In"}
              sx={{ zIndex: "1009" }}
            >
              <IconButton
                onClick={toggleGridZoom}
                color="primary"
                sx={{
                  backgroundColor: "rgba(0, 0, 0, 0.05)",
                  "&:hover": {
                    backgroundColor: "rgba(0, 0, 0, 0.1)",
                  },
                }}
              >
                {isGridZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
            <div style={{ height: "100%" }}>
              <div>
          <ReusableDataTable
            isLoading={loading}
            rows={glRows}
            columns={columns}
            pageSize={10}
            tempheight={'50vh'}
            getRowIdValue={"id"}
            status_onRowSingleClick={true}
            callback_onRowSingleClick={handleRowClick}
            getRowClassName={(params) =>
              selectedRow?.id === params.row.id ? "Mui-selected" : ""
            }
          />
        </div>
        </div>
        </div>
      </Box>
      </div>

      {openAddMatPopup && (
        <Dialog
          fullWidth
          open
          maxWidth="lg"
          sx={{
            "&::webkit-scrollbar": {
              width: "1px",
            },
          }}
        >
          <Box
            sx={{
                backgroundColor: "#e3f2fd",
                padding: "1rem 1.5rem",
                display: "flex",
                alignItems: "center",
            }}
            >
            <FeedOutlinedIcon color="primary" sx={{ marginRight: "0.5rem" }} />
            <Typography variant="h6" component="div" color="primary">
                Extend Search Filter(s)
            </Typography>
            </Box>
          <DialogContent sx={{ padding: ".5rem 1rem", alignItems: "center", justifyContent: "center", margin: "0px 25px" }}>
            
            <Grid container spacing={2}>

              {/* First row: 4 dropdowns */}
              <Grid item xs={12}>
                <Grid container spacing={2}>

                  {withRefParams?.slice(0, 4).map((key) => (
                    <Grid item xs={3} key={key}>
                      <SingleSelectDropdown
                        options={dropDownData?.[key] || []}
                        value={withRefValues[key] || ""}
                        onChange={(newValue) => {
                          handleSalesOrgWithREF(key, newValue);
                        }}
                        placeholder={t(`Select ${key}`)}
                        minWidth={180}
                        listWidth={306}
                        sx={{
                          minWidth: 270,
                          "& .MuiAutocomplete-popper": { minWidth: 306 },
                        }}
                        disabled={selectedMatLines?.length || withReference === "no"}
                        isLoading={isDropdownLoading[key]}
                      />
                    </Grid>
                  ))}
                </Grid>
              </Grid>

              {/* Second row: 4 dropdowns + "OR" section */}
              <Grid item xs={12}>
                <Grid container spacing={2} alignItems="center">
                  
                  {withRefParams?.slice(4).map((key) => (
                    <Grid item xs={3} key={key}>
                      <FilterChangeDropdown
                        param={{ key: "generalLedger", label: "general Ledger" }}
                        dropDownData={{
                          generalLedger: dropDownData?.[key] || [],
                        }}
                        selectedValues={{
                          generalLedger: selectedCostCenters || [],
                        }}
                        handleSelectAll={(key) => {
                          const allOptions = dropDownData?.[key] || [];
                          if (selectedCostCenters?.length === allOptions.length) {
                            setSelectedCostCenters([]);
                          } else {
                            setSelectedCostCenters(allOptions);
                          }
                        }}
                        handleSelectionChange={(key, value) => {
                          setSelectedCostCenters(value || []);
                        }}
                        formatOptionLabel={(option) =>
                          typeof option === "string"
                            ? option
                            : option?.code || ""
                        }
                        isSelectAll={true}
                        errors={{}}
                      />
                    </Grid>
                  ))}
                  
                  
                </Grid>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{
                width: "max-content",
                textTransform: "capitalize",
              }}
              onClick={() => setOpenAddMatPopup(false)}
              variant="outlined"
            >
              {t("Cancel")}
            </Button>
            <Button className="button_primary--normal" type="save"
              onClick={AddCopiedGLData}
              variant="contained">
              {t("OK")}
            </Button>
          </DialogActions>
        </Dialog>
      )}


      {openOrgData && (
        <Dialog fullWidth maxWidth={false} open={true} onClose={viewsClose} sx={{ display: "flex", justifyContent: "center" }} disableEscapeKeyDown>
          <Box sx={{ width: "600px !important" }}>
            <DialogTitle sx={{ backgroundColor: "#EAE9FF", marginBottom: ".5rem" }}>
              <DescriptionIcon
                style={{
                  height: "20px",
                  width: "20px",
                  marginBottom: "-5px",
                }}
              />
              <span>Select Company Code to Extend</span>
            </DialogTitle>
            <DialogContent sx={{ paddingBottom: ".5rem" }}>
              <Box display="flex" alignItems="center" sx={{ flex: 1, padding: "22px 0px", gap: "5px" }}>

                <Autocomplete
                  size="small"
                  multiple
                  fullWidth
                  options={[selectAllOption, ...companycodeExtendedTo?.[selectedRow?.id]]}
                  value={selectedExtendDropdownData?.[selectedRow?.id]}
                  getOptionLabel={getOptionLabel}
                  disableCloseOnSelect
                  isOptionEqualToValue={(option, value) => option.code === value.code}
                  onChange={handleChangeExtendCompanycode}
                  renderOption={(props, option, { selected }) => (
                    <li {...props}>
                      <Checkbox checked={selected} sx={{ marginRight: 1 }} />
                      {getOptionLabel(option)}
                    </li>
                  )}
                  renderTags={(tagValue, getTagProps) =>
                    tagValue.map((option, index) => {
                      const { key, ...tagProps } = getTagProps({ index });
                      return (
                        <Chip key={key} label={`${option.code}`} {...tagProps} />
                      );
                    })
                  }
                  renderInput={(params) => <TextField {...params} />}
                />
              </Box>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => {
                  setOpenOrgData(false), handleCellEdit({ id: rowId, field: "views", value: selectedViews });
                }}
                variant="contained"
              >
                Ok
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

      )}


      <Dialog
        open={missingFieldsDialogOpen}
        onClose={handleCloseDialog}
        aria-labelledby="missing-fields-dialog-title"
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          id="missing-fields-dialog-title"
          sx={{
            backgroundColor: "#fff3e0",
            color: "#e65100",
            display: "flex",
            alignItems: "center",
            gap: 1,
            fontWeight: "bold"
          }}
        >
          <WarningAmberIcon fontSize="medium" />
          {t("Missing Mandatory Fields")}
        </DialogTitle>

        <DialogContent sx={{ pt: 2 }}>
          <Typography variant="body1" gutterBottom>
            {t("Please complete the following mandatory fields:")}
          </Typography>
          <List dense>
            {missingFields.map((field, index) => (
              <ListItem key={index} disablePadding>
                <ListItemIcon sx={{ minWidth: 30 }}>
                  <WarningAmberIcon fontSize="small" color="warning" />
                </ListItemIcon>
                <ListItemText primary={field} />
              </ListItem>
            ))}
          </List>
        </DialogContent>

        <DialogActions sx={{ pr: 3, pb: 2 }}>
          <Button
            onClick={handleCloseDialog}
            variant="contained"
            color="warning"
            sx={{ textTransform: "none", fontWeight: 500 }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* {selectedRow &&
        ((reqBench === "true" && selectedRowId) ? (
          <Box
            sx={{
              position: isTabsZoomed ? "fixed" : "relative",
              top: isTabsZoomed ? 0 : "auto",
              left: isTabsZoomed ? 0 : "auto",
              right: isTabsZoomed ? 0 : "auto",
              bottom: isTabsZoomed ? 0 : "auto",
              width: isTabsZoomed ? "100vw" : "100%",
              height: isTabsZoomed ? "100vh" : "auto",
              zIndex: isTabsZoomed ? 1004 : 1,
              backgroundColor: isTabsZoomed ? "white" : "transparent",
              padding: isTabsZoomed ? "20px" : "0",
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              boxShadow: isTabsZoomed
                ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
                : "none",
              transition: "all 0.3s ease",
              borderRadius: "8px",
              border: "1px solid #e0e0e0",
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: "8px 16px",
                borderRadius: "8px 8px 0 0",
              }}
            >
              <Typography variant="h6">{t("View Details")}</Typography>
              <Tooltip
                title={isTabsZoomed ? "Exit Zoom" : "Zoom In"}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleTabsZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isTabsZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ mt: 3 }}>
              <Tabs
                value={selectedTab}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
                aria-label="Request tabs"
                variant="scrollable"
                scrollButtons="auto"
                sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
              >
                {generalLedgerTabs.map((tab, index) => (
                  <Tab key={index} label={tab.tab} />
                ))}
              </Tabs>
              <Paper elevation={2} sx={{ p: 3, borderRadius: 4 }}>
                {generalLedgerTabs[selectedTab] && (
                  <GenericTabsGlobal
                    disabled={false}
                    basicDataTabDetails={generalLedgerTabs[selectedTab].data}
                    dropDownData={{
                      "CompanyCode": allDropDownData?.["accountType"],
                      "Country": dropdownDataCountry,
                      "Accounttype": allDropDownData?.["accountType"],
                      "AccountGroup": allDropDownData?.["accountGroup"],
                      "Segment": dropdownDataSegment,
                      "Language": dropdownDataLanguage,
                      "Template": dropdownDataFormPlanning,
                      "COArea": dropdownDataCOA,
                      "TaxJurisdiction": dropdownDataTaxJur,
                    }}
                    activeViewTab={generalLedgerTabs[selectedTab].tab}
                    uniqueId={selectedRow?.id || selectedRowId || glRows[0]?.id}
                    selectedRow={selectedRow || {}}
                    module={"GeneralLedger"}
                  />
                )}
              </Paper>
            </Box>
          </Box>
        ) : (
          <Box
            sx={{
              position: isTabsZoomed ? "fixed" : "relative",
              top: isTabsZoomed ? 0 : "auto",
              left: isTabsZoomed ? 0 : "auto",
              right: isTabsZoomed ? 0 : "auto",
              bottom: isTabsZoomed ? 0 : "auto",
              width: isTabsZoomed ? "100vw" : "100%",
              height: isTabsZoomed ? "100vh" : "auto",
              zIndex: isTabsZoomed ? 1004 : 1,
              backgroundColor: isTabsZoomed ? "white" : "transparent",
              padding: isTabsZoomed ? "20px" : "0",
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              boxShadow: isTabsZoomed
                ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
                : "none",
              transition: "all 0.3s ease",
              borderRadius: "8px",
              border: "1px solid #e0e0e0",
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: "8px 16px",
                borderRadius: "8px 8px 0 0",
              }}
            >
              <Typography variant="h6">{t("View Details")}</Typography>
              <Tooltip
                title={isTabsZoomed ? "Exit Zoom" : "Zoom In"}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleTabsZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isTabsZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}>
              <Tabs
                value={selectedTab}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
                aria-label="Request tabs"
                variant="scrollable"
                scrollButtons="auto"
                sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
              >
                {generalLedgerTabs.map((tab, index) => (
                  <Tab key={index} label={tab.tab} />
                ))}
              </Tabs>
              <Paper elevation={2} sx={{ p: 3, borderRadius: 4 }}>
                {generalLedgerTabs[selectedTab] && (
                  
                  <GenericTabsGlobal
                    disabled={false}
                    basicDataTabDetails={generalLedgerTabs[selectedTab].data}
                    dropDownData={{
                      "CompanyCode": allDropDownData?.["accountType"],
                      "Country": dropdownDataCountry,
                      "Accounttype": allDropDownData?.["accountType"],
                      "AccountGroup": allDropDownData?.["accountGroup"],
                      "Segment": dropdownDataSegment,
                      "Language": dropdownDataLanguage,
                      "Template": dropdownDataFormPlanning,
                      "COArea": dropdownDataCOA,
                      "TaxJurisdiction": dropdownDataTaxJur,
                    }}
                    activeViewTab={generalLedgerTabs[selectedTab].tab}
                    uniqueId={selectedRow?.id || selectedRowId || glRows[0]?.id}
                    selectedRow={selectedRow || {}}
                    module={"GeneralLedger"}
                  />
                )}
              </Paper>
            </Box>
          </Box>
        ))
      } */}

      <BottomNavGlobal
        handleSaveAsDraft={handleSaveAsDraft}
        handleSubmitForReview={handleSubmitForReview}
        handleSubmitForApprove={handleSubmitForApprove}
        handleSendBack={handleSendBack}
        handleCorrection={handleCorrection}
        handleRejectAndCancel={handleRejectAndCancel}
        handleValidateAndSyndicate={handleValidateAndSyndicate}
        validateAllRows={validateAllRows}
        isSaveAsDraftEnabled={isSaveAsDraftEnabled}
        filteredButtons={filteredButtons}
        validateEnabled={validateEnabled}
        moduleName={"GeneralLedger"}
      />
      <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
    </div>
  );
};

export default RequestDetailsExtendGL;
