import React from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import App from "./App";
import { Provider } from "react-redux";
import store from "./app/store";
import { HashRouter } from "react-router-dom";

// const keycloak = Keycloak(`/keycloak.json`);

// keycloak.init({ onLoad: "login-required" }).then((authenticated) => {
//   return ReactDOM.createRoot(document.getElementById("root")).render(
//     authenticated ? (
//         <Provider store={store}>
//             <App keycloak={keycloak} />
//         </Provider>) : null,
//   );
// });

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
    <Provider store={store}>
      <HashRouter hashType="noslash">
        <App />
      </HashRouter>
    </Provider>,
);