import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
let initialState_appSettings = {
  dateFormat: "DD MMM YYYY",
  timeFormat: "hh:mm A",
  language:"English",
  range: 7,
  // timeZone: "Asia/Kolkata",
};
const appSettingsSlice = createSlice({
  name: "appSettingsSlice",
  initialState: initialState_appSettings,
  reducers: {
    appSettingsUpdate(state, action) {
      const { dateFormat, timeFormat, range, language, timeZone } = action.payload;

      if (dateFormat !== "") {
        state.dateFormat = dateFormat;
      }

      if (timeFormat !== "") {
        state.timeFormat = timeFormat;
      }

      if (range !== "") {
        state.range = range;
      }

      if (language !== "") {
        state.language = language;
      }

      if (language !== "") {
        state.language = language;
      }


      // if (timeZone !== "") {
      //   state.timeZone = timeZone;
      // }
    },
  },
});
export default appSettingsSlice.reducer;
export const { appSettingsUpdate } = appSettingsSlice.actions;