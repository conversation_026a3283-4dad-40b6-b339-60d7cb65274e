const fetchWrapper = (baseurl, options, destinations) => {
  if( destinations && destinations !== {} ) {
    const updateOptions = { ...options };
    let splitUrl = baseurl.split("/");
    let url = baseurl.replace("/" + splitUrl[1], destinations[splitUrl[1]]);
      updateOptions.headers = {
        ...updateOptions.headers,
        "Access-Control-Allow-Origin": "*"
      };
  
      return fetch(url, updateOptions);
  }else
  return Promise.reject("Error Occured");
 
   
}

export default fetchWrapper;