import React, { useState } from "react";
import { <PERSON><PERSON>ield, Dialog, DialogActions, DialogContent, DialogTitle, <PERSON>ton, Typography, Tooltip } from "@mui/material";
import { useSelector } from "react-redux";
import { ERROR_MESSAGES, CHARACTER_LIMIT, REGION_CODE } from "../../constant/enum";
import { colors } from "../../constant/colors";
import useLang  from "@hooks/useLang";

const MaterialDescriptionColumn = ({ params, disabled = false, handleCellEdit, isAdd = true }) => {
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const Region = initialPayload?.Region;
  const [openDialog, setOpenDialog] = useState(false);
  const [mainDesc, setMainDesc] = useState("");
  const [specialDesc, setSpecialDesc] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const { t } = useLang();

  const materialDesc = isAdd ? params?.row?.materialDescription : params?.row?.globalMaterialDescription || "";

  const handleOpenDialog = () => {
    setMainDesc(materialDesc.slice(0, CHARACTER_LIMIT?.US).trim());
    setSpecialDesc(materialDesc.slice(27).trim());
    setErrorMessage("");
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleSave = () => {
    if (!mainDesc.trim()) {
      setErrorMessage(ERROR_MESSAGES?.MAIN_DESCR_MANDATORY);
      return;
    }
    let formattedMainDesc = mainDesc.toUpperCase().padEnd(CHARACTER_LIMIT?.US, " ");
    let formattedSpecialDesc = specialDesc.toUpperCase();
    let finalDesc = formattedSpecialDesc ? `${formattedMainDesc} ${formattedSpecialDesc}` : formattedMainDesc.trim();

    let allowedCharsRegex;
    if (Region === REGION_CODE?.US) {
      allowedCharsRegex = /^[A-Z0-9\/"\-\s]*$/;
    } else if (Region === REGION_CODE?.EUR) {
      allowedCharsRegex = /^[A-Z0-9"\-\s]*$/;
    }

    if (!allowedCharsRegex.test(finalDesc)) {
      setErrorMessage(Region === REGION_CODE?.US ? ERROR_MESSAGES?.DESCRIPTION_VALIDITY_US : ERROR_MESSAGES?.DESCRIPTION_VALIDITY_EUR);
      return;
    }
    if (params?.row?.id) {
      isAdd
        ? handleCellEdit(params?.row?.id, finalDesc)
        : handleCellEdit({
            id: params.row.id,
            field: "globalMaterialDescription",
            value: finalDesc,
          });
    }
    setOpenDialog(false);
  };

  return (
    <>
      <Tooltip title={<span style={{ whiteSpace: "pre" }}>{materialDesc}</span>} arrow>
        <span>
          <TextField fullWidth variant="outlined" disabled={disabled} size="small" value={materialDesc} placeholder={t("ENTER MATERIAL DESCRIPTION")} onClick={handleOpenDialog} InputProps={{ readOnly: true }} />
        </span>
      </Tooltip>

      {/* Dialog for Material Description Input */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>{t("Enter Material Description")}</DialogTitle>
        <DialogContent>
          <Typography>
            {t("Material Description")} ({Region === REGION_CODE?.EUR ? `Max ${CHARACTER_LIMIT?.EUR} Chars, Only Alphabets` : `Max ${CHARACTER_LIMIT?.US} Chars`}) <span style={{ color: colors?.error?.red }}>*</span>
          </Typography>
          <TextField
            variant="outlined"
            fullWidth
            size="small"
            placeholder={Region === REGION_CODE?.EUR ? `Enter description (Max ${CHARACTER_LIMIT?.EUR} chars)` : `Enter First Part (Max ${CHARACTER_LIMIT?.US} chars)`}
            value={mainDesc}
            onChange={(e) => {
              const newValue = e.target.value.toUpperCase();
              setMainDesc(Region === REGION_CODE?.EUR ? newValue?.replace(/[^A-Z0-9"\-\s]/g, "")?.slice(0, CHARACTER_LIMIT?.EUR) : newValue?.replace(/[^A-Z0-9\/"\-\s]/g, "")?.slice(0, CHARACTER_LIMIT?.US));
            }}
            helperText={Region === REGION_CODE?.EUR ? `${mainDesc.length}/${CHARACTER_LIMIT?.EUR} characters used (Only letters, numbers, quotes, hyphen and spaces allowed)` : `${mainDesc.length}/${CHARACTER_LIMIT?.US} characters used (Only letters, numbers, /, ", - and spaces allowed)`}
          />

          {Region === REGION_CODE?.US && (
            <>
              <Typography sx={{ marginTop: 2 }}>{`Special Material Description (Max ${CHARACTER_LIMIT?.US_SPECIAL} Chars)`}</Typography>
              <TextField
                variant="outlined"
                fullWidth
                size="small"
                placeholder={`Enter special Description (Max ${CHARACTER_LIMIT?.US_SPECIAL} chars)`}
                value={specialDesc}
                onChange={(e) => {
                  const newValue = e.target.value.toUpperCase();
                  setSpecialDesc(newValue?.replace(/[^A-Z0-9\/"\-\s]/g, "")?.slice(0, CHARACTER_LIMIT?.US_SPECIAL));
                }}
                helperText={`${specialDesc.length}/${CHARACTER_LIMIT?.US_SPECIAL} characters used (Optional)`}
              />
            </>
          )}

          {errorMessage && (
            <Typography color={colors?.error?.dark} sx={{ marginTop: 1 }}>
              {errorMessage}
            </Typography>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={handleCloseDialog} color="secondary">
            Cancel
          </Button>
          <Button onClick={handleSave} color="primary" variant="contained" disabled={!mainDesc.trim()}>
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default MaterialDescriptionColumn;
