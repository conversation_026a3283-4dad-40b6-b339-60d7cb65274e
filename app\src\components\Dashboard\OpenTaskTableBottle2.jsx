import {
  LinearProgress,
  Stack,
  Box,
  Typography,
  Select,
  MenuItem,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  container_table,
  container_tableHeader,
  primary_Color,
} from "../common/commonStyles";
import { useSelector } from "react-redux";
import moment from "moment/moment";
import { DataGrid } from "@mui/x-data-grid";
import styled from "@emotion/styled";
import { useNavigate } from "react-router-dom";

export const selectedOptionBottle = "Form To Supplier";

export default function OpenTaskTableBottle2({
  tableRow,
  loader,
  count,
  selectedOptionBottle,
  setSelectedOptionBottle,
}) {
  const StyledGridOverlay = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    marginRight: "10px",
    justifyContent: "center",
    height: "100%",
  }));

  function CustomNoRowsOverlay() {
    return (
      <StyledGridOverlay>
        <Box sx={{ mt: 1 }}>No Data Available</Box>
      </StyledGridOverlay>
    );
  }

  const appSettings = useSelector((state) => state.appSettings);
  const navigate = useNavigate();

  const handleOptionChange = (event) => {
    setSelectedOptionBottle(event.target.value);
  };

  // Modify the rows prop to contain only the first 5 rows
  const rowsToShow = tableRow?.slice(0, 5) ?? [];

  const columns = [
    {
      field: "requestId",
      headerName: "Request Id",
      width: 190,
      headerAlign: "left",
      align: "left",
    },
    {
      field: "createdAt",
      headerName: "Created At",
      editable: false,
      flex: 1,
      align: "left",
      headerAlign: "left",
      // renderCell: (params) => (
      //   <Typography variant="body2">
      //     {moment(params.row.createdAt).format(appSettings.date)}
      //   </Typography>
      // ),
      renderCell:(params)=>{
        return(<Typography sx={{fontSize:"12px"}}>{moment(params.row.createdAt).format(appSettings?.dateFormat)}</Typography>)
      }
    },
    {
      field: "updatedAt",
      headerName: "Completed At",
      editable: false,
      flex: 1,
      align: "left",
      headerAlign: "left",
      // renderCell: (params) => (
      //   <Typography variant="body2">
      //     {moment(params.row.createdAt).format(appSettings.date)}
      //   </Typography>
      // ),
      renderCell:(params)=>{
        return(<Typography sx={{fontSize:"12px"}}>{moment(params.row.createdAt).format(appSettings?.dateFormat)}</Typography>)
      }
    },
    {
      field: "createdBy",
      headerName: "Created By",
      editable: false,
      flex: 1,
      align: "left",
      headerAlign: "left",
    },
  ];

  return (
    <>
      <Box sx={{container_table}} mb={2}>
        <div className="reusable-table" style={{ position: "relative" }}>
          <Stack
            justifyContent="space-between"
            direction="row"
            sx={container_tableHeader}
          >
            <Typography fontSize={"12px"} fontWeight={"bold"} marginTop={2}>
              List of Latest{" "}
              {selectedOptionBottle}{" "}
              Requests
            </Typography>
            <Typography fontSize={"10px"}>
              <Select
                value={selectedOptionBottle}
                onChange={handleOptionChange}
                style={{ minWidth: "200px" }}
                size="small"
              >
                <MenuItem value="Form To Supplier" style={{ width: "80px" }}>
                  Form To Supplier
                </MenuItem>
                <MenuItem
                  value="Finance & Compliance"
                  style={{ width: "80px" }}
                >
                  Finance & Compliance
                </MenuItem>
                <MenuItem value="Buyer Review" style={{ width: "80px" }}>
                  Buyer Review
                </MenuItem>
                <MenuItem
                  value="Procurement Lead Review"
                  style={{ width: "80px" }}
                >
                  Procurement Lead Review
                </MenuItem>
              </Select>
            </Typography>
          </Stack>
          <DataGrid
            loading={loader}
            getRowId={(row) => row.id}
            rows={rowsToShow}
            width="100%"
            autoHeight
            rowHeight={50}
            disableSelectionOnClick
            columns={columns}
            disableExtendRowFullWidth={false}
            hideFooter={true}
            marginRight={10}
            sx={{
              "& .MuiDataGrid-row:hover": {
                backgroundColor: "#EAE9FF40",
              },
              backgroundColor: "#fff",
            }}
            components={{
              LoadingOverlay: LinearProgress,
              NoRowsOverlay: CustomNoRowsOverlay,
            }}
          />
        </div>
      </Box>
    </>
  );
}
