import React, { useEffect, useState } from "react";
import {
  destination_Po,
  destination_IWA,
  destination_ITM,
  // destination_IWA_SCP,
  destination_IWA_NPI,
} from "../../../destinationVariables";
// Workflow imports
import Workflow from "@cw/cherrywork-itm-workspace/Workflow";
import destinationData from "../../../data/destinationData";
import { userRawData, returnUserMap } from "../../../data/userData";
import { accessToken, userData } from "../../../data/propData";
import { SwipeableDrawer } from "@mui/material";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { doAjax } from "../fetchService";
import CloseIcon from "@mui/icons-material/Close";
import Divider from "@mui/material/Divider";
import {
  Accordion,
  AccordionDetails,
  Box,
  Card,
  Grid,
  IconButton,
  Skeleton,
  styled,
  Typography,
} from "@mui/material";
import { Stack } from "@mui/system";
import configData from "../../../data/configData";
const WorkFlow = ({ id, open, handleClose }) => {
  const [taskData, settaskData] = useState(null);
  let userData = useSelector((state) => state.userManagement.userData);

  const navigate = useNavigate();
  const [userRawData, setUserRawData] = useState(null);
  const [userGroupRawData, setUserGroupRawData] = useState(null);
  const fetchUserRawData = () => {
    doAjax(
      `/${destination_IWA_NPI}/api/v1/usersMDG/getUsersMDG`,
      "get",
      (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        var finalData = { ...resData, data: tempUserData };
        setUserRawData(finalData);
      }
    );
  };

  const fetchUserGroupRawData = () => {
    doAjax(`/${destination_IWA}/api/v1/groups`, "get", (resData) => {
      var tempData = resData.data;
      var tempGroupData = tempData?.map((gData) => {
        return { ...gData, groupName: gData?.name };
      });
      var finalData = { ...resData, data: tempGroupData };
      setUserGroupRawData(finalData);
    });
  };

  let fetchTaskId = (id) => {
    doAjax(
      `/${destination_Po}/task/workflow-task-Id/${id}`,
      "get",
      (data) => {
        console.log(data, "1st")
        if (data?.data) {
          doAjax(
            `/${destination_ITM}/v1/detailPage/dynamicDetails/${data?.data}`,
            "get",
            (res) => {
              console.log(res, "2nd")
              if (res.statusCode === 200) {
                settaskData(res?.data?.taskDetails?.[0]);
              }
            }
          );
        }
      }
    );
  };

  useEffect(() => {
    fetchTaskId(id);
    fetchUserRawData();
    fetchUserGroupRawData();
  }, []);
  return (
    <SwipeableDrawer
      anchor="right"
      open={open}
      onClose={() => handleClose("WORKFLOW")}
      onOpen={() => { }}
      PaperProps={{
        elevation: 4,
        sx: {
          minWidth: "35vw",

          border: "none",
        },
      }}
    >
      <Stack spacing={1}>
        <Grid
          container
          sx={{
            padding: "0",
          }}
        >
          <Grid item md={10} style={{ padding: "16px" }}>
            <Typography
              variant="h5"
              sx={{
                color: "#1D1D1D",
                font: "Roboto",
                fontWeight: "400",
                fontSize: "24px",
              }}
            >
              Workflow
            </Typography>
          </Grid>
          <Grid item md={2} style={{ margin: "auto" }}>
            <IconButton
              onClick={(e) => {
                e.stopPropagation();
                handleClose("WORKFLOW");
              }}
            >
              <CloseIcon />
            </IconButton>
          </Grid>
        </Grid>
        <Divider />
        {taskData ? (
          <Workflow
            task={taskData}
            useWorkAccess={false}
            token={""}
            configData={configData}
            destinationData={{}}
            userData={{ ...userData, user_id: userData?.emailId }}
            userList={returnUserMap(userRawData)}
            useConfigServerDestination={false}
          ></Workflow>
        ) : (
          <Typography
            variant="caption"
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            No Workflow Found
          </Typography>
        )}
      </Stack>
    </SwipeableDrawer>
  );
};

export default WorkFlow;
