import { useState, useEffect } from "react";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import Modal from "@mui/material/Modal";
import { doAjax } from "../common/fetchService";
import { destination_MaterialMgmt,destination_GeneralLedger_Mass } from "../../destinationVariables";
import moment from "moment";
import ChangeCircleOutlinedIcon from "@mui/icons-material/ChangeCircleOutlined";
import { useSelector } from "react-redux";
import { Grid, Stack, IconButton, Tooltip, Tabs, Tab } from "@mui/material";
import ReusableDataTable from "../Common/ReusableTable";
import CloseIcon from "@mui/icons-material/Close";
import { v4 as uuidv4 } from "uuid";
import ReusableIcon from "../Common/ReusableIcon";
import {
    iconButton_SpacingSmall,
} from "../Common/commonStyles";
import { CHANGE_LOG_TEMPS } from "../../constant/changeLogTemplates.js";
import { saveExcel, saveExcelMultiSheets, } from "../../functions";
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "@hooks/useLogger";
import { API_CODE, ERROR_MESSAGES, LOADING_MESSAGE } from "@constant/enum";
import { convertDate, extractDataBaedOnTemplateName, filterNavigation, formatDateValue, getObjectValue, getSegregatedPart, mergeArrays } from "@helper/helper";
import { CHANGE_TEMPLATES_FIELD_IDENTIFICATION, TEMPLATE_NAME_MANIPULATION } from "@constant/changeTemplates";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";

const ChangeLogGL = ({module, open, closeModal, requestId, requestType }) => {
    const { customError } = useLogger()
    const [loading, setloading] = useState(true);
    const [apiResponse, setApiResponse] = useState(null);
    const payloadData = useSelector((state) => state.payload.payloadData);
    const [changeLogRows, setChangeLogRows] = useState([]);
    const templateKey = payloadData?.TemplateName;

    const {destination} = filterNavigation(module)

    const handleTabChange = (event, newValue) => {
        setSelectedTab({ number: newValue, label: tabData[newValue].label });
    };
    const style = {
        position: "absolute",
        top: "50%",
        left: "52%",
        transform: "translate(-50%, -50%)",
        width: "80%",
        height: "auto",
        bgcolor: "#fff",
        boxShadow: 4,
        p: 2,
    };

    const onClose = () => {
        closeModal(false)
    }
    let columnsForChangeLog=[
        { field: 'objectNumber', headerName: 'Object Number', flex: 1 },
        { field: 'FieldName', headerName: 'Field Name', flex: 1 },
        { field: 'PreviousValue', headerName: 'Previous Value', flex: 1 },
        { field: 'CurrentValue', headerName: 'Current Value', flex: 1 },
        { field: 'SAPValue', headerName: 'SAP Value', flex: 1 },
        { field: 'ChangedBy', headerName: 'Changed By', flex: 1 },
        { field: 'ChangedOn', headerName: 'Changed On', flex: 1 },
    ]

    useEffect(() => {
        const fetchChangeLogData = async () => {
            if (open && !apiResponse) {
                try {
                    const result = await changelogget(requestId, requestType);
                    setApiResponse(result);
                } catch (error) {
                    customError("Error fetching changelog data:", error);
                }
            }
        };       
        fetchChangeLogData();
    }, [open, requestId]);

    useEffect(() => {
        if (apiResponse?.length) {
            try {
            let allRows =[]


            apiResponse[0]["ChangeLogData"]?.map((item)=>{

                let hash={}
                hash["id"]=uuidv4(),
                hash["objectNumber"] = getSegregatedPart(item.ObjectNo, 1),
                hash["SAPValue"] = item.SAPValue,
                hash["PreviousValue"] = item.PreviousValue,
                hash["CurrentValue"]= item.CurrentValue,
                hash["ChangedOn"] = formatDateValue(item.ChangedOn),
                hash["ChangedBy"] = item.ChangedBy,
                hash["FieldName"] = item.FieldName,
                allRows?.push(hash)

            })
            
            setChangeLogRows(allRows);
            } catch (error) {
            customError(ERROR_MESSAGES.CHANGE_LOG_MESSAGE, error);
            }
        }
        }, [apiResponse]);

    const changelogget = (requestId) => {
        setloading(true);
        //\\const url = `/${destination_MaterialMgmt}/${END_POINTS?.CHG_DISPLAY_REQUESTOR.FETCH_CHANGELOG_DATA}`;
        const url = `/${destination}${END_POINTS.DATA.GET_CHANGELOG_DATA}`;
        
        let changeLogPayload = {
            requestId: requestId,
            childRequestId :""
        };
        
        return new Promise((resolve, reject) => {
            const hSuccess = (data) => {
                if (data?.statusCode === API_CODE.STATUS_200 && data?.body?.length > 0) {
                    const result = (data?.body);
                    setloading(false);
                    resolve(result);
                } else {
                    setloading(false);
                    resolve([]);
                }
            };

            const hError = (error) => {
                setloading(false);
                customError(error);
                reject(error);
            };

            doAjax(url, "post", hSuccess, hError, changeLogPayload);
        });
    };

    const presentDate = new Date();
    const backDate = new Date();
    backDate.setDate(backDate.getDate() - 15);



    const functions_ExportAsExcel = {
        convertJsonToExcel: () => {
            let excelColumns = [];
            columnsForChangeLog.forEach((item) => {
                if (item.headerName.toLowerCase() !== "action" && !item.hide) {
                    excelColumns.push({ header: item.headerName, key: item.field });
                }
            });
            saveExcel({
                fileName: `${requestId}_ChangeLog`,
                columns: excelColumns,
                rows: changeLogRows,
            })
        },
        button: () => {
            return (
                <Button
                    sx={{
                        textTransform: "capitalize",
                        position: "absolute",
                        right: 0,
                        top: 0,
                    }}
                    onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
                >
                    Download
                </Button>
            );
        },
    };
    

    return (
        <>
        {loading && <ReusableBackDrop blurLoading={loading} loaderMessage={LOADING_MESSAGE.CHANGELOG_LOADING}/>}
        <Modal
            open={open}
            onClose={onClose}
            aria-labelledby="modal-modal-title"
            aria-describedby="modal-modal-description"
        >
            <Box sx={style}>
                <Stack>
                    <Grid
                        item
                        md={12}
                        sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}
                    >
                        <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                            <ChangeCircleOutlinedIcon
                                sx={{
                                    color: "black",
                                    fontSize: "20px",
                                    "&:hover": {
                                        transform: "rotate(360deg)",
                                        transition: "0.9s",
                                    },
                                    textAlign: "center",
                                    marginTop: "4px",
                                }}
                            />
                            <Typography
                                id="modal-modal-title"
                                variant="subtitle1"
                                fontSize={"16px"}
                                fontWeight={"bold"}
                                sx={{ color: "black" }}
                            >
                                Change Log
                            </Typography>
                        </Box>

                        <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                            <Tooltip title="Export Table">
                                <IconButton
                                    sx={iconButton_SpacingSmall}
                                    onClick={functions_ExportAsExcel.convertJsonToExcel}
                                >
                                    <ReusableIcon iconName={"IosShare"} />
                                </IconButton>
                            </Tooltip>

                            <IconButton sx={{ padding: "0 0 0 5px" }} onClick={onClose}>
                                <CloseIcon />
                            </IconButton>
                        </Box>
                    </Grid>
                </Stack>

                <div
                    className="tab-content"
                    style={{ position: "relative", height: "100%", marginTop: 16 }}
                >
                    
                    <Stack>
                        <ReusableDataTable
                            rows={changeLogRows}
                            columns={columnsForChangeLog}
                            getRowIdValue={"id"}
                            pageSize={changeLogRows?.length}
                            autoHeight
                            scrollbarSize={10}
                            sx={{
                                "& .MuiDataGrid-row:hover": {
                                    backgroundColor: "#EAE9FF40",
                                },
                                backgroundColor: "#fff",
                            }}
                        />
                    </Stack>
                                    
                </div>
            </Box>
        </Modal>
        </>
    );
};

export default ChangeLogGL;
