import { useCallback, useState } from "react";
import { doAjax } from "@components/Common/fetchService";
import { destination_BOM } from "../../../destinationVariables";
import { useLocation, useNavigate } from "react-router-dom";
import { API_CODE, REQUEST_TYPE } from "@constant/enum";
import { useSelector } from "react-redux";
import { APP_END_POINTS } from "@constant/appEndPoints";
import { useSnackbar } from "@hooks/useSnackbar";
import { END_POINTS } from "@constant/apiEndPoints";


const useBomMassUpload = ({
  setBlurLoading,
  setLoaderMessage,
  setEnableDocumentUpload,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { showSnackbar } = useSnackbar();
  const queryParams = new URLSearchParams(location.search);
  const RequestId = queryParams.get("RequestId");
  const RequestType = queryParams.get("RequestType");
  const payloadData = useSelector((state) => state.bom.BOMpayloadData);

  const handleUploadBOM = useCallback((file) => {
    setLoaderMessage("Initiating Excel Upload");
    setBlurLoading(true);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    formData.append("dtName", (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ) ? "MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG" : "MDG_MAT_CHANGE_TEMPLATE");
    formData.append("version", (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ) ? "v1" : "v5");
    formData.append("requestId", RequestId ? RequestId : "");
    formData.append("region", payloadData?.Region ? payloadData?.Region : "US");
    formData.append("bomType", "ALL");

    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      } else {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        showSnackbar(data?.message, "error");
      }
    };
    const hError = (error) => {
      setBlurLoading(false);
      setLoaderMessage("");
      showSnackbar(error?.message, "error");
    };
    doAjax(
      `/${destination_BOM}${END_POINTS.MASS_ACTION.UPLOAD_BOM_FILE}`,
      "postformdata",
      hSuccess,
      hError,
      formData
    );
  }, [setBlurLoading, setLoaderMessage, setEnableDocumentUpload, navigate]);

  return { handleUploadBOM };
};

export default useBomMassUpload;
