import { useEffect, useState } from "react";
import { doAjax } from "../components/Common/fetchService";
import { getLocalStorage } from "../helper/helper";
import { LOCAL_STORAGE_KEYS, API_CODE, TASK_NAME } from "../constant/enum";
import { useSelector } from "react-redux";
import {EXTEND_BUTTON_PRIORITY} from '@constant/buttonPriority';
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "./useLogger";

export const useExtendDynamicButton = (
  taskData,
  applicationConfig,
  destination_IDM,
  BUTTON_NAME
) => {
  const [buttonsIDM, setButtonsIDM] = useState([]);
  const [extendFilteredButtons, setExtendFilteredButtons] = useState([]);
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const queryParams = new URLSearchParams(location.search);
   const isrequestType = queryParams.get("RequestType");
  const requestTypeFromTask = useSelector((state) => state?.userManagement?.taskData?.ATTRIBUTE_2);
  const [showWfLevels, setShowWfLevels] = useState(false);
  const {customError} = useLogger() 

  const effectiveRequestType = initialPayload?.RequestType || requestTypeFromTask || isrequestType
  useEffect(() => {
    if(effectiveRequestType){
      getButtons();
    }
  }, [taskData,effectiveRequestType]);

  const getButtons = () => {
    const payload = {
      decisionTableId: null,
      decisionTableName: "MDG_MAT_DYN_BUTTON_CONFIG",
      version: "v3",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME": "Material",
          "MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE": effectiveRequestType
            
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      if (data.statusCode === API_CODE.STATUS_200) {
        const savedTask = getLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK, true, {});
        const effectiveTaskDesc = taskData?.taskDesc || savedTask?.taskDesc;
        const responseData = data?.data?.result[0]?.MDG_MAT_DYN_BUTTON_CONFIG || [];

        const filteredButtonData = responseData.filter(
          (item) =>
            item?.MDG_MAT_DYN_BTN_TASK_NAME === (effectiveTaskDesc ?? TASK_NAME.INITIATOR)
        );
        setButtonsIDM(filteredButtonData);
        const shouldShow =
          filteredButtonData.find((btn) => btn.MDG_MAT_DYN_BTN_BUTTON_NAME === BUTTON_NAME.SEND_BACK) ||
          filteredButtonData.find((btn) => btn.MDG_MAT_DYN_BTN_BUTTON_NAME === BUTTON_NAME.CORRECTION);
        if (shouldShow) setShowWfLevels(true);
      }
    };

    const hError = (error) => {
      customError("Dynamic Button Fetch Error:", error);
    };

    const url =
      applicationConfig.environment === "localhost"
        ? `/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}`
        : `/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`;

    doAjax(url, "post", hSuccess, hError, payload);
  };

  useEffect(() => {
    const sorted = [...buttonsIDM].sort((a, b) => {
      const priorityA = EXTEND_BUTTON_PRIORITY[a.MDG_MAT_DYN_BTN_BUTTON_NAME] ?? 999;
      const priorityB = EXTEND_BUTTON_PRIORITY[b.MDG_MAT_DYN_BTN_BUTTON_NAME] ?? 999;
      return priorityA - priorityB;
    });
    setExtendFilteredButtons(sorted);
  }, [buttonsIDM]);

  return { extendFilteredButtons, showWfLevels };
};

