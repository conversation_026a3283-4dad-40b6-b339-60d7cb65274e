import { useCallback } from "react";
import { message } from "antd";
import { useDispatch } from "react-redux";
import { setTreeData, updateTreeChanges } from "@app/hierarchyDataSlice";

// Helper to remove tag from node
const removeTagFromNodeForMovement = (nodes, nodeId, tag) => {
  return nodes.map((node) => {
    if (node.id === nodeId) {
      return {
        ...node,
        tags: node.tags?.filter((t) => t !== tag) || [],
      };
    }
    if (node.child) {
      return {
        ...node,
        child: removeTagFromNodeForMovement(node.child, nodeId, tag),
      };
    }
    return node;
  });
};

const useRemoveOperation = ({
  rawTreeData,
  treeChanges,
  addToChangeLog,
  object,
  setIsEditModalVisible,
  editForm,
}) => {
  const dispatch = useDispatch();

  const handleRemoveTag = useCallback(
    async (tag, parentNode) => {
      try {
        // Remove tag from current node
        const updatedTree = removeTagFromNodeForMovement(
          [...rawTreeData],
          parentNode.id,
          tag
        );

        addToChangeLog(
          `REMOVE ${object} `,
          `${tag} removed from ${parentNode?.label}`
        );

        const existingNode = treeChanges?.[parentNode.label] || {};
        const currentTags = existingNode?.tags || [];
        const currentReplaceTags = existingNode?.replacedTags || [];

        const isTagInTagList = currentTags.includes(tag);

        const updatedTags = isTagInTagList
          ? currentTags.filter((t) => t !== tag)
          : currentTags;

        const updatedReplaceTags = isTagInTagList
          ? currentReplaceTags
          : [...new Set([...currentReplaceTags, tag])];

        dispatch(
          updateTreeChanges({
            nodeLabel: parentNode.label,
            changes: {
              tags: updatedTags,
              replacedTags: updatedReplaceTags,
            },
          })
        );

        dispatch(setTreeData(updatedTree));

        setIsEditModalVisible(false);
        editForm.resetFields();
        message.success(`${object} ${tag} Removed Successfully`);
      } catch (error) {
        console.error("Validation failed:", error);
      }
    },
    [
      rawTreeData,
      treeChanges,
      dispatch,
      addToChangeLog,
      object,
      setIsEditModalVisible,
      editForm,
    ]
  );

  return { handleRemoveTag };
};

export default useRemoveOperation;
