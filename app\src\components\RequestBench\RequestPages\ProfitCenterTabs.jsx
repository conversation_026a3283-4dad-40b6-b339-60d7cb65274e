import React from 'react';
import { useSelector } from 'react-redux';
import { Box, Tab, Tabs, TextField, Typography, Paper } from '@mui/material';

const ProfitCenterTabs = () => {
  const tabsData = useSelector((state) => state.profitCenterTabs.tabsData);
  const [currentTab, setCurrentTab] = React.useState(0);

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  return (
    <Box>
      <Tabs value={currentTab} onChange={handleTabChange} variant="scrollable" scrollButtons="auto">
        {tabsData.map((tabItem, index) => (
          <Tab key={index} label={tabItem.tab} />
        ))}
      </Tabs>

      {tabsData.map((tabItem, index) => (
        <Paper key={index} sx={{ marginTop: 2, padding: 2, display: currentTab === index ? 'block' : 'none' }}>
          {Object.entries(tabItem.data).map(([cardName, cardDetails]) => (
            <Box key={cardName} mb={3}>
              <Typography variant="subtitle1" gutterBottom>{cardName}</Typography>
              <Box display="flex" flexWrap="wrap" gap={2}>
                {cardDetails.map((field) => (
                  <TextField
                    key={field.fieldName}
                    label={field.fieldName}
                    variant="outlined"
                    size="small"
                    fullWidth
                  />
                ))}
              </Box>
            </Box>
          ))}
        </Paper>
      ))}
    </Box>
  );
};

export default ProfitCenterTabs;
