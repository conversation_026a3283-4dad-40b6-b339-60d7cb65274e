import { Box, Grid, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { container_Padding } from "../common/commonStyles";
import FilterField from "../Common/ReusableFilterBox/FilterField";

const AccountingTab = (props) => {
  console.log('accounting', props.accountingTabDetails);
  let filterFields = Object?.entries(props?.accountingTabDetails);
  const [accountingJsx, setaccountingJsx] = useState([]);
  useEffect(() => {
    setaccountingJsx(
      filterFields?.map((item) => {
        return (
          <Grid
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              borderRadius: "8px",
              border: "1px solid #E0E0E0",
              mt: 0.25,
              boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              ...container_Padding,
              // ...container_columnGap,
            }}
          >
            <Grid container>
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                }}
              >
                {item[0]}
              </Typography>
            </Grid>
            <Box>
              <Grid container spacing={1}>
                {[...item[1]]
                  .sort((a, b) => a.sequenceNo - b.sequenceNo)
                  .map((innerItem) => {
                    return (
                      <FilterField
                        field={innerItem}
                        dropDownData={props.dropDownData}
                      />
                    );
                  })}
              </Grid>
            </Box>
          </Grid>
        );
      })
    );
  }, [props.accountingTabDetails]);
  return <>{accountingJsx}</>;
};

export default AccountingTab;
