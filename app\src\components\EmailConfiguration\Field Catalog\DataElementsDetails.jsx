import React from "react";
import Button from "@mui/material/Button";
import Grid from "@mui/material/Grid";
import Input from "@mui/material/Input";
import FormControl from "@mui/material/FormControl";
import FormGroup from "@mui/material/FormGroup";
import FormControlLabel from "@mui/material/FormControlLabel";
import Switch from "@mui/material/Switch";
import RadioGroup from "@mui/material/RadioGroup";
import Radio from "@mui/material/Radio";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";

class dataElementDialog extends React.Component {
  constructor(props) {
    super(props);
    this.valuehelp = React.createRef();
    let emptyRow = this.emptyRow();
    this.state = {
      // value: "DataElement",
      // tableVisible: true,
      selectedRow: props.selectedRow,
      constraints: [],
      fields: [],
      apiType: [
        {
          name: "REST",
        },
        {
          name: "ODATA",
        },
      ],
      fileTypeDropDown: [
        {
          name: ".xlsx",
        },
        {
          name: ".csv",
        },
        {
          name: ".xml",
        },
      ],
    };
  }
 
  componentDidUpdate(prevProps) {
   
  }
  emptyRow = () => {
    const selectedRow = {
      attributeId: "",
      createdBy: "",
      createdOn: null,
      dataType: "",
      description: "",
      destinations: null,
      isLookup: true,
      label: "",
      length: "",
      lookUpId: "",
      lookUpType: "",
      lookupConfig: null,
      name: "",
      source: "",
      updatedBy: "",
      updatedOn: null,
      propertyDto: {
        isMandatory: true,
        isVisible: true,
        isNullable: true,
        defaultValue: "abc",
        isFilterable: true,
        isSortable: true,
        isEditable: true,
      },
    };
    return selectedRow;
  };
  createNewField = (event, newValue) => {
    this.setState({
      ...this.state,
      tableVisible: false,
    });
  };
  handleSubmit = (evt) => {
    let a = evt;
  };
  editFields = (row) => {
    this.setState({
      ...this.state,
      tableVisible: false,
      selectedRow: row,
    });
  };

  onValueHelpType = (event) => {
    let value = event.target.value;
    this.props.lookupType = value;
  };

  goToMsgComp = (event) => {
    this.props.history.push("/messageSummary");
  };

  goToThemeComp = (event) => {
    this.props.history.push("/themeStyling");
  };
  render() {
   
    return (
      <Dialog
        open={this.props.open}

        //onClose={() => props.onClose('CANCEL')}
      >
        <DialogTitle style={{ display: "flex", alignItems: "center", borderBottom: "1px solid #d9d9d9" }}>Data Element Details</DialogTitle>
        <DialogContent>
          <DialogContentText style={{ width: "35rem", marginTop: "0.5rem" }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={3} md={3}>
                <Input
                  required
                  disabled
                  id="filled-disabled"
                  label="Name"
                  name="name"
                  value={this.props.rowData.name}
                  variant="outlined"
                  style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                  //style={{ marginLeft: 30 }}
                  size="small"
                  onChange={(evt) => this.inputChangeHandler(evt)}
                  inputProps={{ maxLength: 30 }}
                  // className="customInput2"
                />
              </Grid>
              <Grid item xs={12} sm={3} md={3}>
                <Input
                  required
                  disabled
                  id="filled-disabled"
                  label="Label"
                  name="label"
                  value={this.props.rowData.label}
                  variant="outlined"
                  style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                  //style={{ marginLeft: 30 }}
                  size="small"
                  onChange={(evt) => this.inputChangeHandler(evt)}
                  inputProps={{ maxLength: 30 }}
                  // className="customInput2"
                />
              </Grid>
              <Grid item xs={12} sm={3} md={3}>
                <Input
                  required
                  disabled
                  id="filled-disabled"
                  label="Description"
                  name="description"
                  value={this.props.rowData.description}
                  variant="outlined"
                  style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                  //style={{ marginLeft: 30 }}
                  size="small"
                  onChange={(evt) => this.inputChangeHandler(evt)}
                  inputProps={{ maxLength: 30 }}
                  // className="customInput2"
                />
              </Grid>
              <Grid item xs={12} sm={3} md={3}>
                <Input
                  required
                  disabled
                  id="filled-disabled"
                  label="Data Type"
                  name="description"
                  value={this.props.rowData.dataType}
                  variant="outlined"
                  style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                  //style={{ marginLeft: 30 }}
                  size="small"
                  onChange={(evt) => this.inputChangeHandler(evt)}
                  inputProps={{ maxLength: 30 }}
                  // className="customInput2"
                />
              </Grid>
              <Grid item xs={12} sm={3} md={3}>
                <Input
                  required
                  disabled
                  // error={this.state.validLength}
                  // helperText={this.state.errorMssg}
                  // disabled={this.state.selectedRow.dataType === "BOOLEAN" || this.state.selectedRow.dataType === "TIMESTAMP" || this.state.selectedRow.dataType === "DATE"}

                  id="filled-disabled"
                  label="Length"
                  name="length"
                  value={this.props.rowData.length}
                  variant="outlined"
                  style={{ border: "2px solid #d4d5d6", borderRadius: "5px" }}
                  //style={{ marginLeft: 30 }}
                  size="small"
                  onChange={(evt) => this.inputChangeHandler(evt)}
                  inputProps={{ maxLength: 5 }}
                  // className="customInput2"
                />
              </Grid>
              <Grid item xs={12} sm={3} md={3}>
                <FormControl component="fieldset" style={{ marginLeft: 10, width: "100%" }}>
                  {/* <InputLabel id="demo-simple-select-outlined-label" ></InputLabel> */}
                  <FormGroup aria-label="position" row>
                    <FormControlLabel
                      required
                      label="Value Help"
                      size="small"
                      // checked={this.state.selectedRow.isLookup}
                      //checked={this.state.obj.active}
                      name="active"
                      onChange={(evt) => this.onValueHelpSwitch(evt)}
                      //value={row.active}
                      labelPlacement="start"
                      control={<Switch color="primary" />}
                    />
                  </FormGroup>
                </FormControl>
              </Grid>
            </Grid>
            <FormControl component="fieldset">
              <RadioGroup
                row
                name="row-radio-buttons-group"
                // value={this.props.parentState.lookupType}
                style={{ marginLeft: "1rem" }}
                // onChange={(e, newvalue) => this.props.handleChangeValueHelpType(e, newvalue)}
              >
                <FormControlLabel value="VL" control={<Radio color="primary" />} label="Static Valus Help" />
                <FormControlLabel value="API" control={<Radio color="primary" />} label="Api Based" />
                <FormControlLabel value="DB" control={<Radio color="primary" />} label="Database Based" />
                <FormControlLabel value="FILE" control={<Radio color="primary" />} label="File Type" />
                <FormControlLabel value="COMPOSITE" control={<Radio color="primary" />} label="Composite" />
              </RadioGroup>
            </FormControl>
          </DialogContentText>
        </DialogContent>
        <DialogActions style={{ height: "3rem", borderTop: "1px solid #d9d9d9" }}>
          {/* {props.actions.map(action => <Button key={action} variant="contained" size="small" onClick={() => props.handleClose(action)}>{action}</Button>)} */}
          <Button
            key={"CANCEL"}
            variant="contained"
            size="small"
            onClick={() => {
              this.props.onClose("CANCEL");
            }}
          >
            CANCEL
          </Button>
        </DialogActions>
      </Dialog>
    );
  }
}

export default dataElementDialog;
