const mailDefinitionResponse = {
  statusCode: 200,
  data: [
    {
      emailDefinitionId: "37f4d867-6f19-4979-b880-f19f2b78f389",
      objectId: "kl34ef56gh78ij90kl12mn34op56pq53",
      name: "TEMPLATE_MAT_APPROVAL_REQUEST",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_SUBMIT_FOR_APPROVAL",
      processDesc: "Submission for Approval",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Action Required: Approval for Request - Material $REQUEST_TYPE - $REQUEST_ID ",
      content:
        '<div style="display: flex; flex-direction: column; justify-content: start;"><br>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Dear $USER,</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">This is a system-generated email to notify you that the Material $REQUEST_TYPE - $REQUEST_ID  is Pending for Approval.<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Please log into the system and review the request</p>\r\n<br>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;"><a href="https://ca-gbq-ca-mdg-mdg.cfapps.us10-001.hana.ondemand.com/LOGIN_URL" target="_self"><span style="color: rgb(18,54,255);background-color: rgb(240,251,255);font-size: 14px;">$LOGIN_URL</span></a><br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Thank you for your cooperation. <br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Best regards,&nbsp;</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Master Data Team&nbsp;&nbsp;</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">This is a system generated email. Please do not reply to or forward this email.</p>\r\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1738663948135,
      updatedBy: "<EMAIL>",
      updatedOn: 1738665850001,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "f82a6ccc-ea71-441d-a903-dae83e98f651",
      objectId: "kl34ef56gh78ij90kl12mn34op56pq54",
      name: "TEMPLATE_MAT_CORRECTION_REQUEST",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_SEND_FOR_CORRECTION",
      processDesc: "Sending for Correction by Intermediate Approvers",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Action Required: Resubmission Request - Material $REQUEST_TYPE - $REQUEST_ID ",
      content:
        '<div style="display: flex; flex-direction: column; justify-content: start;"><br>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Dear $USER,<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">This is a system-generated email to notify you that the Sunoco Cost Center &amp; Profit Center $REQUEST_TYPE - $REQUEST_ID is Pending for Correction.<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Reason for Return: $COMMENTS&nbsp;</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Rejected by: $REJECTED_BY_USER<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Please log into the system and make the necessary updates and resubmit your request.</p>\r\n<br>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;"><a href="https://ca-gbq-ca-mdg-mdg.cfapps.us10-001.hana.ondemand.com/LOGIN_URL" target="_self"><span style="color: rgb(18,54,255);background-color: rgb(240,251,255);font-size: 14px;">$LOGIN_URL</span></a> <br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Thank you for your cooperation.<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Best regards,&nbsp;</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Master Data Team<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">This is a system generated email. Please do not reply to or forward this email.</p>\r\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1738664924287,
      updatedBy: "<EMAIL>",
      updatedOn: 1738665867104,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "9ec72afd-20ad-49a7-a56a-05ed1985d3f4",
      objectId: "kj56kj78ij56kl12mn68op56pq68rp00",
      name: "TEMPLATE_MAT_FINANCE_REQUEST",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_FINANCE_REVIEW",
      processDesc: "Sending Finance Task for Review",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Action Required: $MATERIAL Submit for Request - Material $REQUEST_TYPE - $REQUEST_ID  ",
      content:
        '<div style="display: flex; flex-direction: column; justify-content: start;"><p style="margin: 0em; line-height: 1.2em; width: 100%;">Dear $USER,<br></p>\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">This is a system-generated email to notify you that the Material $REQUEST_TYPE - $REQUEST_ID  is Pending for Completion.</p>\n<br>\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Please log into the system and review the request</p>\n<br>\n<p style="margin: 0em; line-height: 1.2em; width: 100%;"><a href="LOGIN_URL" class="wysiwyg-mention" data-mention="" data-value="LOGIN_URL">$LOGIN_URL</a>&nbsp;</p>\n<br>\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Thank you for your cooperation.&nbsp;</p>\n<br>\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Best regards,&nbsp;</p>\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Master Data Team&nbsp;&nbsp;</p>\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">This is a system generated email. Please do not reply to or forward this email.</p>\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1744617280408,
      updatedBy: "<EMAIL>",
      updatedOn: 1744617497607,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "cb163620-d8dd-4794-9a81-171fd857e1fe",
      objectId: "ef56gh78ij90kl12mn34op56pq78rp03",
      name: "TEMPLATE_MAT_MASSUPLOAD_MANDATORYFIELDMISS",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_MANDATORY_FIELD_CHECK",
      processDesc: "Mandatory Fields Not Present",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Update - Material $REQUEST_TYPE - $FILE_NAME Uploading failed",
      content:
        '<div style="display: flex; flex-direction: column; justify-content: start;"><p style="margin: 0em; line-height: 1.2em; width: 100%;">&nbsp;&nbsp;&nbsp;<br>Dear $USER,<br><br>This is a system-generated email to notify you that the Material $REQUEST_TYPE - $FILE_NAME uploading has failed due to errors in uploaded excel.<br><br>Please Verify -<br>$ERROR_MSG<br><br>Please do the needful correction &amp; upload it again.<br><br>Thank you for your cooperation.<br><br>Best regards,<br>Master Data Team<br><br>This is a system generated email. Please do not reply to or forward this email.</p>\r\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1738666151881,
      updatedBy: "<EMAIL>",
      updatedOn: 1738666194653,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "c0c76512-9cd7-4104-a709-2420d24010b1",
      objectId: "ef56gh78ij90kl12mn34op56pq78rp13",
      name: "TEMPLATE_MAT_MASS_CHANGE_DUPLICATE",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_OBJECT_LOCK",
      processDesc: "Ongoing Duplicate Change Request",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Update - Material $REQUEST_TYPE - $FILE_NAME Uploading failed",
      content:
        '<div style="display: flex; flex-direction: column; justify-content: start;"><p style="margin: 0em; line-height: 1.2em; width: 100%;">&nbsp;&nbsp;&nbsp;Dear $USER,<br><br>This is a system-generated email to notify you that the Material $REQUEST_TYPE - $FILE_NAME uploading has failed. There is an open request with changes to these $LOCKED_REQUESTS , please remove the listed Materials from your template in order to proceed.<br><br>Thank you for your cooperation.<br><br>Best regards,<br>Master Data Team<br><br>This is a system generated email. Please do not reply to or forward this email.&nbsp;&nbsp;</p>\r\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1738666082706,
      updatedBy: "<EMAIL>",
      updatedOn: 1738666145410,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "c99a5583-7043-42a7-a735-bd99a6ffec1f",
      objectId: "ef56gh78ij90kl12mn34op56pq78rs45",
      name: "TEMPLATE_MAT_MASS_EXCELEMAIL",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_DOWNLOAD",
      processDesc: "Download Excel for Mass Change",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Update - Material $REQUEST_TYPE download excel mailed successfully ",
      content:
        '<div style="display: flex; flex-direction: column; justify-content: start;"><br>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">&nbsp;<br>Dear $USER,<br><br>This is a system-generated email to notify you that the Material $REQUEST_TYPE download excel mailed successfully. You can now download it &amp; fill the required details.<br><br>Please log into the system after filling excel &amp; upload it.<br><a href="https://ca-gbq-ca-mdg-cw-mdg.cfapps.us10-001.hana.ondemand.com/index.html#" target="_self">$LOGIN_URL</a><br><br>Thank yo u for your cooperation.<br><br>Best regards, <br>Master Data Team<br><br>This is a system generated email. Please do not reply to or forward this email.&nbsp;&nbsp;</p>\r\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1738666206044,
      updatedBy: "<EMAIL>",
      updatedOn: 1742448896283,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "1ae3d47a-0829-4a64-ad38-d1fbf2be22f9",
      objectId: "ef56gh78ij90kl12mn34op56pq78rs47",
      name: "TEMPLATE_MAT_MASS_EXCELUPLOAD_FAILED",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_UPLOAD_FAILED",
      processDesc: "Mass Excel Upload Failed",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Update - Material $REQUEST_TYPE excel upload has failed in Application  ",
      content:
        '<div style="display: flex; flex-direction: column; justify-content: start;"><p style="margin: 0em; line-height: 1.2em; width: 100%;"><br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Dear $USER,<br><br>This is a system-generated email to notify you that the Material $REQUEST_TYPE has failed during Excel upload in Application.<br>$ERROR_MSG<br><br>Please log into the system and review the request.</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;"><br><a href="https://ca-gbq-ca-mdg-cw-mdg.cfapps.us10-001.hana.ondemand.com/index.html#" target="_self">$LOGIN_URL</a><br><br>Thank yo u for your cooperation.<br><br>Best regards,<br>Master Data Team<br><br>This is a system generated email. Please do not reply to or forward this email.&nbsp;&nbsp;</p>\r\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1738665081020,
      updatedBy: "<EMAIL>",
      updatedOn: 1742448938244,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "65bc2137-72e5-453a-b593-e04ed9e51fe8",
      objectId: "ef56gh78ij90kl12mn34op56pq78rs46",
      name: "TEMPLATE_MAT_MASS_EXCELUPLOAD_SUCCESS",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_UPLOAD_SUCCESS",
      processDesc: "Mass Excel Upload Success",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Update - Material $REQUEST_TYPE - $REQUEST_ID excel upload successful in Application ",
      content:
        '<div style="display: flex; flex-direction: column; justify-content: start;"><br>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Dear $USER,<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">This is a system-generated email to notify you that the Material $REQUEST_TYPE - $REQUEST_ID Excel has successfully uploaded in Application.<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Please log into the system and review the request.<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;"><a href="https://ca-gbq-ca-mdg-cw-mdg.cfapps.us10-001.hana.ondemand.com/index.html#" target="_self">$LOGIN_URL</a><br>&nbsp;</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Thank you for your cooperation.<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Best regards,&nbsp;</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Master Data Team<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">This is a system generated email. Please do not reply to or forward this email.</p>\r\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1738665790898,
      updatedBy: "<EMAIL>",
      updatedOn: 1742448970957,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "325e2baa-b3cd-4cb2-828b-28f64a88fbbb",
      objectId: "ef56gh78ij90kl12mn34op56pq78rs49",
      name: "TEMPLATE_MAT_MASS_VALIDATION_FAILED",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_VALIDATE_FAILED",
      processDesc: "Mass Validation Failed",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Update - Material $REQUEST_TYPE - $REQUEST_ID validation has failed in SAP ",
      content:
        '<div style="display: flex; flex-direction: column; justify-content: start;"><br>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">&nbsp;<br>Dear $USER,<br><br>This is a system-generated email to notify you that the Material $REQUEST_TYPE - $REQUEST_ID is failed during SAP validation check.<br><br>Please log into the system and review the request.</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;"><br><a href="https://ca-gbq-ca-mdg-cw-mdg.cfapps.us10-001.hana.ondemand.com/index.html#" target="_self">$LOGIN_URL</a><br><br>Thank yo u for your cooperation.<br><br>Best regards, <br>Master Data Team<br><br>This is a system generated email. Please do not reply to or forward this email.&nbsp;&nbsp;</p>\r\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1738665969613,
      updatedBy: "<EMAIL>",
      updatedOn: 1742448995933,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "7d2efd2a-2cb2-4a1e-aa1b-4f20fde1c540",
      objectId: "ef56gh78ij90kl12mn34op56pq78rs48",
      name: "TEMPLATE_MAT_MASS_VALIDATION_SUCCESS",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_VALIDATE_SUCCESS",
      processDesc: "Mass Validation Success",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Update - Material $REQUEST_TYPE - $REQUEST_ID has successfully validated in SAP ",
      content:
        '<div style="display: flex; flex-direction: column; justify-content: start;"><p style="margin: 0em; line-height: 1.2em; width: 100%;"><br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Dear $USER,<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">This is a system-generated email to notify you that the Material $REQUEST_TYPE - $REQUEST_ID is successfully Validated in SAP.<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Please log into the system and review the request.<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;"><a href="https://ca-gbq-ca-mdg-cw-mdg.cfapps.us10-001.hana.ondemand.com/index.html#" target="_self">$LOGIN_URL</a><br>&nbsp;</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Thank you for your cooperation.<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Best regards,&nbsp;</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Master Data Team<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">This is a system generated email. Please do not reply to or forward this email.</p>\r\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1738666027203,
      updatedBy: "<EMAIL>",
      updatedOn: 1742449024664,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "c2231424-415e-441c-9782-be118fc2db85",
      objectId: "kl34ef56gh78ij90kl12mn34op56pq56",
      name: "TEMPLATE_MAT_REAPPROVAL_REQUEST",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_SUBMIT_FOR_RE_APPROVAL",
      processDesc: "Submission for Re-Approval",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Action Required: Re-Approval for Request - Material $REQUEST_TYPE - $REQUEST_ID ",
      content:
        '<div style="display: flex; flex-direction: column; justify-content: start;"><br>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Dear $USER,</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">This is a system-generated email to notify you that the Material $REQUEST_TYPE - $REQUEST_ID  is Pending for Re-Approval as the requestor made changes to the original request. <br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Please log into the system and review the request.</p>\r\n<br>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;"><a href="https://ca-gbq-ca-mdg-mdg.cfapps.us10-001.hana.ondemand.com/LOGIN_URL" target="_self"><span style="color: rgb(18,54,255);background-color: rgb(240,251,255);font-size: 14px;">$LOGIN_URL</span></a>&nbsp;</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Thank you for your cooperation. <br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Best regards,&nbsp;</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Master Data Team&nbsp;&nbsp;</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">This is a system generated email. Please do not reply to or forward this email.</p>\r\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1738664024758,
      updatedBy: "<EMAIL>",
      updatedOn: 1738665903382,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "435c3ebc-1bbe-4354-80c1-be988b32a7f3",
      objectId: "kl34ef56gh78ij90kl12mn34op56pq55",
      name: "TEMPLATE_MAT_RECTIFICATION_REQUEST",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_SEND_FOR_REVIEW",
      processDesc: "Sending for Correction by MDM",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Action Required: Resubmission Request - Material $REQUEST_TYPE - $REQUEST_ID    ",
      content:
        '<div style="display: flex; flex-direction: column; justify-content: start;"><br>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Dear $USER,<br><br>This is a system-generated email to notify you that the Material $REQUEST_TYPE - $REQUEST_ID is Pending for Correction.<br><br>Reason for Return: $COMMENTS<br>Rejected by: $REJECTED_BY_USER<br><br>Please log into the system and make the necessary updates and resubmit your request.</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;"><br>$LOGIN_URL<br><br>Thank you for your cooperation.<br><br>Best regards, <br>Master Data Team<br><br>This is a system generated email. Please do not reply to or forward this email.&nbsp;&nbsp;</p>\r\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1738664773452,
      updatedBy: "<EMAIL>",
      updatedOn: 1742449050032,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "cb885edc-fe39-4a5c-9a8f-42c2243ae192",
      objectId: "kl34ef56gh78ij90kl12mn34op56pq57",
      name: "TEMPLATE_MAT_REJECT",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_REJECT",
      processDesc: "MAT Rejected",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Update - Material $REQUEST_TYPE - $REQUEST_ID has been Rejected",
      content:
        '<div style="display: flex; flex-direction: column; justify-content: start;"><p style="margin: 0em; line-height: 1.2em; width: 100%;">&nbsp;&nbsp;&nbsp;Dear $USER,<br>This is a system-generated email to notify you that the Material $REQUEST_TYPE - $REQUEST_ID is Rejected &amp; Closed with the reason below:<br><br><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">Reason for Rejection: $COMMENTS </span></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">Rejected by: $REJECTED_BY_USER</span> <br><br>Please submit a new request if deemed necessary. <br><br>Thank you for your cooperation.<br><br>Best Regards,<br>Master Data Team <br>This is a system generated email. Please do not reply to or forward this email.&nbsp;&nbsp;</p>\r\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1738664703229,
      updatedBy: "<EMAIL>",
      updatedOn: 1738664761100,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "e0f45872-3c19-4ea1-ba6d-30f99f2ca8e8",
      objectId: "kl34ef56gh78ij90kl12mn34op56pq52",
      name: "TEMPLATE_MAT_REVIEW_REQUEST",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_SUBMIT_FOR_REVIEW",
      processDesc: "Submission for MDM Review",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Action Required: Review for SAP Syndication- Material $REQUEST_TYPE - $REQUEST_ID ",
      content:
        '<div style="display: flex; flex-direction: column; justify-content: start;"><br>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Dear $USER,</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">This is a system-generated email to notify you that the Material $REQUEST_TYPE - $REQUEST_ID  is Pending for Review and syndication into SAP with the attached details below:<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Please log into the system and review the request</p>\r\n<br>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;"><a href="https://ca-gbq-ca-mdg-mdg.cfapps.us10-001.hana.ondemand.com/LOGIN_URL" target="_self"><span style="color: rgb(18,54,255);background-color: rgb(240,251,255);font-size: 14px;">$LOGIN_URL</span></a>&nbsp;</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Thank you for your cooperation.<br></p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Best regards,&nbsp;</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Master Data Team&nbsp;&nbsp;</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">This is a system generated email. Please do not reply to or forward this email.</p>\r\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1738664469886,
      updatedBy: "<EMAIL>",
      updatedOn: 1738665938588,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "af9080b7-648a-42e7-b503-aed86e1ebd76",
      objectId: "bb12cd34ef56gh43hj35kl68mn12rr95",
      name: "TEMPLATE_MAT_SAP_FAILED",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_SYNDICATION_FAILED",
      processDesc: "SAP Syndication Completely Failed",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Update - Material - $REQUEST_TYPE - $REQUEST_ID has been failed ",
      content: '<div style="display: flex; flex-direction: column; justify-content: start;"><p style="margin: 0em; line-height: 1.2em; width: 100%;">&nbsp;&nbsp;&nbsp;<br>Dear $USER,<br><br>This is a system-generated email to notify you that the Material $REQUEST_TYPE - $REQUEST_ID has been completely failed during syndication in SAP.<br>See the attached details regarding this request.<br><br>Best regards,<br>Master Data Team<br><br>This is a system generated email. Please do not reply to or forward this email.</p>\r\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1738666299337,
      updatedBy: "<EMAIL>",
      updatedOn: 1738666332046,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "7cf38f89-3f87-4dbf-9fb5-2e71769daec2",
      objectId: "bb12cd34ef56gh43hj35kl68mn12rr85",
      name: "TEMPLATE_MAT_SAP_PARTIAL_SYNDICATE",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_SYNDICATION_PARTIAL",
      processDesc: "SAP Syndication Partially Successful",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Update - Material - $REQUEST_TYPE - $REQUEST_ID has been partially syndicated",
      content: '<div style="display: flex; flex-direction: column; justify-content: start;"><p style="margin: 0em; line-height: 1.2em; width: 100%;">&nbsp;&nbsp;&nbsp;<br>Dear $USER,<br><br>This is a system-generated email to notify you that the Material $REQUEST_TYPE - $REQUEST_ID has been partially completed during syndication in SAP.<br>See the attached details regarding this request.<br><br>Best regards,<br>Master Data Team<br><br>This is a system generated email. Please do not reply to or forward this email.&nbsp;&nbsp;</p>\r\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1738666340306,
      updatedBy: "<EMAIL>",
      updatedOn: 1738666365317,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "65bfbe54-c83b-43af-a684-85dd2be33004",
      objectId: "kl34ef56gh78ij90kl12mn34op56pq51",
      name: "TEMPLATE_MAT_SAP_SYNDICATE_REQUEST",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MAT_APPROVE",
      processDesc: "Sending for SAP Syndication",
      entity: "MATERIAL_UPDATES",
      entityDesc: "Material Updates",
      subject: "Update:  Material $REQUEST_TYPE - $REQUEST_ID has been approved",
      content:
        '<div style="display: flex; flex-direction: column; justify-content: start;"><p style="margin: 0em; line-height: 1.2em; width: 100%;">Dear $USER,</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">This is a system-generated email to notify you that the Material $REQUEST_TYPE - $REQUEST_ID has been successfully completed in SAP.</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">See the attached details regarding this request.&nbsp;</p>\r\n<br>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Best regards,&nbsp;</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">Master Data Team&nbsp;&nbsp;</p>\r\n<p style="margin: 0em; line-height: 1.2em; width: 100%;">This is a system generated email. Please do not reply to or forward this email.</p>\r\n</div>',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1738664519444,
      updatedBy: "<EMAIL>",
      updatedOn: 1738664679828,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "7a90bfc9-81d3-4d56-9eab-8f9bc2fb3a12",
      identifierDesc: "MDG_BATCH",
    },
    {
      emailDefinitionId: "e927823a-c34d-4c74-b982-3e491205d618",
      objectId: "7a572d0b-a767-4c42-9cf6-fdb431fa76d8",
      name: "Template_ASN_Amendment_Submitted",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "ASN_AMENDMENT_SUBMITTED",
      processDesc: "ASN Amendment Submitted",
      entity: "ADVANCED_SHIPMENT_NOTIFICATION",
      entityDesc: "Advanced Shipment Notification Updates",
      subject: "$ENV : ASN Amendment Submitted   ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">We are informing you that an ASN Amendment has been submitted for your order. Please review the updated details and confirm the amendment within the Supplier Collaboration Portal.</span></p>\r\n<p></p>\r\n<p><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong> ASN Details:</strong></span></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>$SUMMARY_TABLE</strong></span></p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Login Details</strong></span></p>\r\n<ul>\r\n<li><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Portal URL: </strong></span> <a href="https://incture-cherrywork-dev-cw-scp-dev-cw-scp-dev.cfapps.eu10-004.hana.ondemand.com/" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a></li>\r\n</ul>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">Please log in using your provided details and navigate to the relevant order to review the amendment. For any questions or assistance, please contact our support team.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span> <br></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1730438543101,
      updatedBy: "<EMAIL>",
      updatedOn: 1733118953005,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "61a22099-f73c-41f6-9b1f-1b8bc89e044f",
      objectId: "36d9c361-39e3-4216-bc8a-ef2efabd879c",
      name: "Template_ASN_Delayed_For_Pending_QC_Re_Inspection",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_ASN_DELAYED_REINSPECTION",
      processDesc: "Re-Inspection Due To ASN Delayed",
      entity: "ADVANCED_SHIPMENT_NOTIFICATION",
      entityDesc: "Advanced Shipment Notification Updates",
      subject: "$ENV: $SUBJECT: Delayed ASN Due To Pending Quality Check Reinspection   ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">We are writing to inform you that your ASN has been delayed due to <strong>pending quality check reinspection.</strong> Please review the details and ensure that the necessary actions are taken promptly to avoid further delays.</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>ASN Details:</strong></span></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>$SUMMARY_TABLE</strong></span></p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">Please log in to the Supplier Collaboration Portal to access further details and update the status of your ASN. You can find the link to the portal below. </span></p>\r\n<ul>\r\n<li><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Portal URL: </strong></span> <a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a>&nbsp;</li>\r\n</ul>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">We appreciate your prompt attention to this matter.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span> <br></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1730112961417,
      updatedBy: "<EMAIL>",
      updatedOn: 1733118975107,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "f4bf0304-2568-4e97-953a-1c93b2adee22",
      objectId: "2c91c78e-6128-41d3-a51d-15e5b14bed6b",
      name: "Template_ASN_Initiated",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "ASN_INITIATE",
      processDesc: "ASN Initiate",
      entity: "ADVANCED_SHIPMENT_NOTIFICATION",
      entityDesc: "Advanced Shipment Notification Updates",
      subject: "$ENV: ASN Initiated       ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email is to notify that an ASN has been initiated for below mentioned line items. You can access and manage the ASN details through the Supplier Collaboration Portal.</span></p>\r\n<p><br><strong>ASN Details:</strong></p>\r\n<p style="text-align:center;"><strong>$SUMMARY_TABLE</strong></p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">  for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span></p>\r\n<p>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1725879270858,
      updatedBy: "<EMAIL>",
      updatedOn: 1737977580520,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "5d6edbbb-e7d1-4888-a908-2b1946cf7626",
      objectId: "2ad9c9ea-22cd-4743-b2e3-c6bee0fe5b6c",
      name: "Template_ASN_Notify_Freight_Forwarder",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "ASN_NOTIFY_FREIGHT_FORWARDER",
      processDesc: "ASN Notify Freight Forwarder",
      entity: "ADVANCED_SHIPMENT_NOTIFICATION",
      entityDesc: "Advanced Shipment Notification Updates",
      subject: "$ENV:  ASN $STATUS         ",
      content:
        '<p>Dear <strong>$USER</strong>,&nbsp;</p>\r\n<p></p>\r\n<p>This email confirms that an ASN $STATUS for the details mentioned below. Please review the ASN details below for your reference and take any necessary actions.&nbsp;</p>\r\n<p></p>\r\n<p><strong>ASN Details:</strong></p>\r\n<p style="text-align:left;"><strong>$SUMMARY_TABLE</strong></p>\r\n<p></p>\r\n<p>If you have any questions, please contact our support team.&nbsp;</p>\r\n<p></p>\r\n<p>Thank you.&nbsp;</p>\r\n<p></p>\r\n<p>Best regards,<br>The Supplier Collaboration Portal Team</p>\r\n<p style="text-align:center;"><br><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1725966364574,
      updatedBy: "<EMAIL>",
      updatedOn: 1733119375353,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "719be638-e2d4-43eb-b2e0-404376e6d0d6",
      objectId: "5608783b-3b95-4266-b82f-e8bb1bb49a92",
      name: "Template_GCC_CPC_EXPIRY",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "GCC_CPC_EXPIRY",
      processDesc: "GCC CPC Expiry",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV : GCC/CPC Expiring        ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\r\n<p><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email is to inform you that following GCC/CPC id is expiring soon.</span>&nbsp;</p>\r\n<p><br><strong>Details:</strong></p>\r\n<p style="text-align:left;"><strong>$SUMMARY_TABLE</strong></p>\r\n<p></p>\r\n<p><strong>Login Details: </strong><a href="$APPLICATION_URL" target="_self"><strong>Supplier Collaboration Portal</strong></a>&nbsp;</p>\r\n<p></p>\r\n<p><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">We appreciate your prompt attention to this matter.If you have any queries please reach out to our support team.</span></p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span> <br>```</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1732255573601,
      updatedBy: "<EMAIL>",
      updatedOn: 1733476137719,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "c20114df-7b91-4424-a8d4-6c4618bd99b5",
      objectId: "069bf15e-0b5f-43d1-8303-ac6dd3584963",
      name: "Template_GCC_CPC_Update",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "GCC_CPC_UPDATE",
      processDesc: "GCC CPC Update",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: GCC/CPC Form Update     ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\n<p>This email is to notify you that the following GCC/CPC form is ready for an update.</p>\n<p></p>\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Update Details:</strong></span></p>\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>$SUMMARY_TABLE</strong></span></p>\n<p></p>\n<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Portal Url : </strong></span><a href="$APPLICATION_URL" target="_self"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Supplier Collaboration Portal</strong></span></a></p>\n<p></p>\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">Please review the updated form and adjust your submissions accordingly. If you have any questions or require assistance, please feel free to reach out to our support team.</span><br></p>\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span><br></p>\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Team</span></p>\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725348976747,
      updatedBy: "<EMAIL>",
      updatedOn: 1739782650267,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "cd60fbe2-4b0e-4dcc-97fb-9cfb4998cd83",
      objectId: "b8b5b48334ea4e91a12e7ba9f338c49a",
      name: "Template_New_PO_Created",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PO_CREATED",
      processDesc: "PO Created in ECC",
      entity: "PURCHASE_ORDER",
      entityDesc: "Purchase Order Updates",
      subject: "$ENV: New Purchase Order Created ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">We are pleased to inform you that a new purchase order has been created for your company.</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Purchase Order Details</strong></span></p>\r\n<ul>\r\n<li><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>PO Number: </strong></span> <span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>$PO_NUMBER</strong></span></li>\r\n<li><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Order Date: </strong></span> <span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>$ORDER_DATE</strong></span></li>\r\n<li><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Total Amount: </strong></span> <span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>$TOTAL_AMOUNT</strong></span></li>\r\n</ul>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">You can access the full details of the purchase order through our Supplier Collaboration Portal. Please log in using your provided credentials.</span><br></p>\r\n<ul>\r\n<li><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Portal URL: </strong></span> <a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins> </ins></strong></span>&nbsp;</li>\r\n</ul>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">If you have any questions or require further clarification, please do not hesitate to contact our support team.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span> <br></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725271002620,
      updatedBy: "<EMAIL>",
      updatedOn: 1729685968572,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "937c8b75-c989-4d27-ac2b-063b70504541",
      objectId: "59986014-a89e-413b-b09c-88ff6b3c5bd7",
      name: "Template_New_Quality_Booking_Created",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "QC_BOOKING",
      processDesc: "Quality Check Booking",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: Quality Check Booking Submitted      ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p style="text-align:left;">This email is to notify that quality check booking has been created for the below mentioned purchase orders.</p>\r\n<p></p>\r\n<p style="text-align:center;"><strong>$SUMMARY_TABLE</strong></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"> for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span></p>\r\n<p>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1725347995101,
      updatedBy: "<EMAIL>",
      updatedOn: 1737972865095,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "52d3daa9-2cd5-432a-8bc5-7b9ee0543f92",
      objectId: "6dbc0c4a-07c0-4ba0-aaf3-d2ccfb261c8f",
      name: "Template_New_Supplier_Change_Request_Rejected",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MSA_REQUEST_REJECT",
      processDesc: "Supplier Change Request Reject",
      entity: "MANAGE_SUPPLIER_ACCOUNT",
      entityDesc: "Supplier Details Update",
      subject: "$ENV: Update: Supplier $REQUEST_TYPE Request Rejected By $USER_GROUP  ",
      content:
        '<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">This is a system-generated email to notify you that the Supplier <strong>$REQUEST_TYPE</strong> request is rejected by <strong>$USER_GROUP</strong> with the Request Number <strong>$REQUEST_ID.</strong></span></p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">Thank you for your cooperation.</span></p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">Best regards,</span></p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">Supplier Collaboration Portal Team</span></p>\r\n<p style="text-align:center;"></p>\r\n<p style="text-align:left;"><span style="color: rgb(205,89,55);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">                                                                                            This is system generated email. Please do not reply or forward to this email</span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1717140421987,
      updatedBy: "<EMAIL>",
      updatedOn: 1719392466131,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "0bd01a6d-17aa-4425-adc6-96a04a764b20",
      objectId: "53573aae-fefd-48ce-9da9-a181f26c4b06",
      name: "Template_New_Supplier_Change_Request_Submission",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MSA_NEW_REQUEST_SUBMISSION",
      processDesc: "Supplier Change New Request Submission",
      entity: "MANAGE_SUPPLIER_ACCOUNT",
      entityDesc: "Supplier Details Update",
      subject: "$ENV: Action Required: Supplier $REQUEST_TYPE Request - $REQUEST_ID - $TASK_NAME ",
      content:
        '<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">This is a system-generated email to notify you that the task named <strong>$TASK_NAME</strong> is pending with you on the Supplier Collaboration Portal.</span></p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">Please log in to the </span><a href="https://incture-cherrywork-qa-cw-scp-qa-cw-scp-qa.cfapps.eu10-004.hana.ondemand.com/$APPLICATION_URL" target="_blank"><span style="color: rgb(44,130,201);background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"> to access the task details a nd to take appropriate action. If you have any questions or face any issues during the process, please refer to the User Guide.</span></p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">Thank you for your cooperation.</span></p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">Best regards,</span></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">Supplier Collaboration Portal</span> <span style="color: rgba(0,0,0,0.87);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"> Team</span></p>\r\n<p style="text-align:center;"></p>\r\n<p style="text-align:left;"><span style="color: rgb(205,89,55);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">This is system generated email. Please do not reply or forward to this email</span>&nbsp;&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1730811093715,
      updatedBy: "<EMAIL>",
      updatedOn: 1730811325763,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "ec848bf0-c5ff-44d3-9f51-0339ab29f694",
      objectId: "b1dbbd2f-14a6-4e46-b576-ec0243c429a6",
      name: "Template_New_User_Creation",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "NEW_USER_CREATION_NOTIFICATION",
      processDesc: "New User Creation Notification",
      entity: "USER_CREATION",
      entityDesc: "User Creation Updates",
      subject: "$ENV: You have been successfully onboarded to the Supplier Collaboration Portal    ",
      content:
        '<p></p>\n<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">We are pleased to inform you that you have been successfully onboarded to our Supplier Collaboration Portal as <strong>$ROLE_NAME</strong></span> <span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">. This platform aims to enhance communication and streamline collaboration for improved efficiency.</span><br></p>\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Login Details</strong></span></p>\n<ul>\n<li><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Portal URL: </strong></span> <a href="$LOGIN_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a>&nbsp;</li>\n</ul>\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">Please log in using the provided details and explore the platform\'s features. If you encounter any issues or require assistance, feel free to reach out to our support team.</span><br></p>\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span><br></p>\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span> <br></p>\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1690780624934,
      updatedBy: "<EMAIL>",
      updatedOn: 1742813042564,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "0adfd058-6af9-4e58-b5b8-99e4d6743f72",
      objectId: "6be8f17e-cd33-4a12-9054-7a0c94d05439",
      name: "Template_PO_Amended_Template",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PO_AMENDED",
      processDesc: "PO Amended",
      entity: "PURCHASE_ORDER",
      entityDesc: "Purchase Order Updates",
      subject: "$ENV: PO Amendment Pending $SUBJECT    ",
      content:
        '<p>Dear <strong>$USER</strong>,</p>\n<p></p>\n<p>This email is to notify that a change has been made to below Purchase Orders line items.  Please login to the Supplier Collaboration Portal to review the updated information.&nbsp;</p>\n<p></p>\n<p style="text-align:left;"><strong>$SUMMARY_TABLE</strong></p>\n<p></p>\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"> for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\n<p style="text-align:left;">&nbsp;</p>\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal</span></p>\n<p style="text-align:left;">&nbsp;</p>\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span></p>\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1736835089569,
      updatedBy: "<EMAIL>",
      updatedOn: 1741343707780,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "d08fbbd5-6252-411a-a63d-1243f6b0798e",
      objectId: "ada11ac9-e31b-4091-a601-9d9a231f17fb",
      name: "Template_PO_Amendment_Approval",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PO_AMENDMENT_APPROVAL",
      processDesc: "PO Amendment Approval",
      entity: "PURCHASE_ORDER",
      entityDesc: "Purchase Order Updates",
      subject: "$ENV: UPDATE: PO Line Item Change Approved  ",
      content:
        '<p>Dear <strong>$USER</strong>,</p>\r\n<p>This email is to notify that changes made to Purchase Orders <strong>$PO_NUMBER</strong>  line items have been approved.  Please login to the Supplier Collaboration Portal to review the updated information.&nbsp;</p>\r\n<p style="text-align:center;"><strong>$SUMMARY_TABLE</strong></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"> for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal</span>&nbsp;</p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span></p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1736834964547,
      updatedBy: "<EMAIL>",
      updatedOn: 1737970930107,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "d0adcdb3-4ad3-4f19-8712-da1dd114248c",
      objectId: "f1c56e67-a7aa-4dc9-96f9-0886030837a6",
      name: "Template_PO_Amendment_after_QC_inspection",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PO_AMENDMENT_AFFECTS_INSPECTION",
      processDesc: "PO Amendment Quality Check Inspection Follow-Up",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: PO Line Item Change Approved QC Inspection",
      content:
        '<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span>&nbsp;</p>\r\n<p style="text-align:left;"></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email is to inform you that a change has been made to a Purchase Order <strong>$PO_NUMBER</strong> after Quality Check Inspection is approved.  Please login to the Supplier Collaboration Portal to review the updated information.  </span></p>\r\n<ul>\r\n<li><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">STATUS - <strong>$STATUS</strong></span></li>\r\n</ul>\r\n<p style="text-align:center;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>$SUMMARY_TABLE</strong></span></p>\r\n<p style="text-align:left;"></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to<strong> </strong></span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">  for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal Team</span>&nbsp;</p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span></p>\r\n<p>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1730100049650,
      updatedBy: "<EMAIL>",
      updatedOn: 1737983003002,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "edba85fb-7c63-4ab2-9f00-92ef2e19beb3",
      objectId: "42ca5dc7-3f51-4996-a1ec-57df24f728b5",
      name: "Template_PO_Confirmation_Approval_Update",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PO_CONFIRMATION_APPROVAL",
      processDesc: "PO Confirmation Approval",
      entity: "PURCHASE_ORDER",
      entityDesc: "Purchase Order Updates",
      subject: "$ENV: PO Confimation Approval Update      ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email is to notify regarding the update on your purchase order confirmation approval. </span><br></p>\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>$SUMMARY_TABLE</strong></span></p>\n<p></p>\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(84,172,210);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">  for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\n<p style="text-align:left;">&nbsp;</p>\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal</span>&nbsp;</p>\n<p style="text-align:left;">&nbsp;</p>\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span>&nbsp;</p>\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725272869021,
      updatedBy: "<EMAIL>",
      updatedOn: 1740459096228,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "55d0fbe9-f8eb-4157-92c6-9fb23353cf6d",
      objectId: "a1709a1b-e316-43f4-aaf4-57ab0cc933a5",
      name: "Template_PO_Confirmation_Submitted",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PO_CONFIRMATION_SUBMITTED",
      processDesc: "PO Confirmation Submitted",
      entity: "PURCHASE_ORDER",
      entityDesc: "Purchase Order Updates",
      subject: "$ENV: Purchase Order Confirmation Submitted   ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email is to notify that confirmation has been submitted for the below mentioned Purchase Orders.</span></p>\r\n<p></p>\r\n<p style="text-align:center;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>$SUMMARY_TABLE</strong></span></p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">  for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span></p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1725272062708,
      updatedBy: "<EMAIL>",
      updatedOn: 1737958679449,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "b902a9c7-98f8-4ac0-b72a-2f15bd4c403f",
      objectId: "558f7032-2422-4b37-8a46-abfc7f9290dc",
      name: "Template_PO_Reject_Submission",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PO_REJECT",
      processDesc: "PO Reject",
      entity: "PURCHASE_ORDER",
      entityDesc: "Purchase Order Updates",
      subject: "$ENV: Purchase Order Update Request Submitted   ",
      content:
        '<p style="text-align:start;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span>&nbsp;</p>\r\n<p style="text-align:left;"></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email is to notify that your below Purchase Orders have been updated by the $SUPPLIER  is usually due to some required information missing or needing an update.  </span></p>\r\n<p></p>\r\n<p style="text-align:center;"><strong>$SUMMARY_TABLE</strong></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"> for further details. In case  you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal Team</span>&nbsp;</p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span></p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1725274654129,
      updatedBy: "<EMAIL>",
      updatedOn: 1737970214514,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "e453e026-5b71-408f-84b5-baff6fe63295",
      objectId: "7055054a-64e7-4ceb-81ef-2eb28fb925ab",
      name: "Template_PO_Rejection_Approval_Update",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PO_REJECTION_APPROVAL",
      processDesc: "PO Rejection Approval",
      entity: "PURCHASE_ORDER",
      entityDesc: "Purchase Order Updates",
      subject: "$ENV: Purchase Order Update Status     ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\r\n<p>This email is to inform you of the status regarding your purchase order update.&nbsp;</p>\r\n<ul>\r\n<li><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>STATUS - </strong> <strong>$STATUS</strong></span></li>\r\n</ul>\r\n<p style="text-align:center;"><strong><br>$SUMMARY_TABLE</strong></p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"> for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal Team</span>&nbsp;</p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span></p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1725274800439,
      updatedBy: "<EMAIL>",
      updatedOn: 1737970511836,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "31471ed5-3a88-4fb0-ada7-71926f48d867",
      objectId: "1dafd689-83a5-40bc-8373-fd5c285dc003",
      name: "Template_PO_Released",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PO_RELEASE",
      processDesc: "PO Release",
      entity: "PURCHASE_ORDER",
      entityDesc: "Purchase Order Updates",
      subject: "$ENV: Purchase Order Released      ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email confirms that the below purchase orders have been released. You can access the purchase order through the Supplier Collaboration Portal. </span><br></p>\r\n<p style="text-align:center;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>$SUMMARY_TABLE</strong></span></p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">  for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1725271353926,
      updatedBy: "<EMAIL>",
      updatedOn: 1738130301044,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "92d5d954-443d-4d02-931e-f9cf8253701f",
      objectId: "b983f4e0-d63f-4d4a-9662-4ba68440fffc",
      name: "Template_Pending_ASN_Quantity",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_ASN_QUANTITY",
      processDesc: "Pending ASN Quantity",
      entity: "ADVANCED_SHIPMENT_NOTIFICATION",
      entityDesc: "Advanced Shipment Notification Updates",
      subject: "$ENV: Pending ASN Quantity",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email is to notify you that the ASN quantity for the following purchase order(s) is pending:</span><br> <strong>$SUMMARY_TABLE</strong></p>\r\n<p style="text-align:left;"></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Login Details</strong></span></p>\r\n<ul>\r\n<li style="margin-left:1.5em;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Portal URL: </strong></span> <a href="$APPLICATION_URL" target="_blank"><span style="color: var(--rs-text-link);background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a><span style="color: var(--rs-text-link);background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins> </ins></strong></span>&nbsp;</li>\r\n</ul>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">Please log in and review the ASN information.  If you have any questions, please contact our support team.</span>  <br>&nbsp;</p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span> <br></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1729685521785,
      updatedBy: "<EMAIL>",
      updatedOn: 1729685713713,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "e59b9773-c6db-458f-865f-d56a887dee2d",
      objectId: "33393006-d33b-401d-ad29-4f9e94a29d66",
      name: "Template_Pending_ASN_To_Be_Initiated",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_ASN_INITIATE",
      processDesc: "Pending ASN Initiate",
      entity: "ADVANCED_SHIPMENT_NOTIFICATION",
      entityDesc: "Advanced Shipment Notification Updates",
      subject: "$ENV: $SUBJECT: ASN To Be Initiated    ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This is a friendly reminder that an ASN needs to be initiated for the below mentioned PO Lineitems . Please log in to the Supplier Collaboration Portal and initiate the ASN at your earliest convenience. </span><br></p>\r\n<p style="text-align:center;"><a href="SUMMARY_TABLE" class="wysiwyg-mention" data-mention data-value="SUMMARY_TABLE"><strong>$SUMMARY_TABLE</strong></a></p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Login Details</strong></span></p>\r\n<ul>\r\n<li><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Portal URL: </strong></span> <a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a>&nbsp;</li>\r\n</ul>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">For any assistance, please contact our support team.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span> <br></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725878455827,
      updatedBy: "<EMAIL>",
      updatedOn: 1730055240699,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "6fd4ccc1-737f-48fc-8bcf-576924ae1dd5",
      objectId: "ec7d8a54-da9d-4367-9cb5-caf4efbdc62d",
      name: "Template_Pending_CAR_Report",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_CAR_REPORT",
      processDesc: "Pending CAR Report For Completion",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: $SUBJECT: Pending CAR Report For Completion",
      content:
        '<p style="text-align:left;">Dear <strong>QC Team ( $USER )</strong>,<br>This email is to notify you that , the below mentioned CAR Reports are need to be completed and overdued more than one month.<br><br><strong>$SUMMARY_TABLE<br><br></strong><span style="color: rgb(36,36,36);background-color: rgb(255,255,255);font-size: 15px;font-family: Segoe UI;">Please login to</span> <a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(36,36,36);background-color: rgb(255,255,255);font-size: 15px;font-family: Segoe UI;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(36,36,36);background-color: rgb(255,255,255);font-size: 15px;font-family: Segoe UI;"><strong> </strong></span> <span style="color: rgb(36,36,36);background-color: rgb(255,255,255);font-size: 15px;font-family: Segoe UI;">for further details. In case you encounter any issues or require assistance, feel free to reach out to our support team.</span>&nbsp;&nbsp;</p>\n<p style="text-align:start;"></p>\n<p style="text-align:left;"><span style="color: windowtext;background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, Roboto_EmbeddedFont, Roboto_MSFontService, sans-serif;">Thank you.</span>&nbsp;&nbsp;</p>\n<p style="text-align:left;"><span style="color: windowtext;background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, Roboto_EmbeddedFont, Roboto_MSFontService, sans-serif;">Best regards, </span>&nbsp;&nbsp;</p>\n<p style="text-align:left;"><span style="color: windowtext;background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, Roboto_EmbeddedFont, Roboto_MSFontService, sans-serif;">Supplier Collaboration Portal</span></p>\n<p style="text-align:start;"></p>\n<p style="text-align:left;"></p>\n<p style="text-align:center;"><span style="color: rgb(226,80,65);background-color: rgb(255,255,255);font-size: 11px;font-family: Roboto, Roboto_EmbeddedFont, Roboto_MSFontService, sans-serif;"><em>This is an automated notification. Please do not reply to this email address.</em></span><span style="font-size: 11px;"> </span></p>\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1742981920166,
      updatedBy: "<EMAIL>",
      updatedOn: 1742982142342,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "1fe540d6-07af-416a-b991-74e80ee1e353",
      objectId: "32cbefff-3197-4436-a7b2-8b99a60491e3",
      name: "Template_Pending_GCC_CPC_Update",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_GCC_CPC",
      processDesc: "Pending GCC CPC",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: $SUBJECT: Pending GCC CPC Update     ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Please note that your GCC CPC update is pending. We kindly request you to take necessary action to complete the process. </span></p>\r\n<p></p>\r\n<p style="text-align:center;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">$SUMMARY_TABLE</span></p>\r\n<ul>\r\n<li><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Login Details</strong></span>&nbsp;</li>\r\n</ul>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>      Portal URL: </strong></span> <a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a>&nbsp;&nbsp;</p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">If you have any questions or need assistance, please feel free to contact our support team.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span> <br></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725349114056,
      updatedBy: "<EMAIL>",
      updatedOn: 1733194720314,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "e8f30197-0ef5-4f2b-b4d1-05e398c5f5f6",
      objectId: "aac1024c-0fea-4213-9c18-8c74a1aa18c7",
      name: "Template_Pending_PO_Confirmation",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_PO_CONFIRMATION",
      processDesc: "Pending PO Confirmation",
      entity: "PURCHASE_ORDER",
      entityDesc: "Purchase Order Updates",
      subject: "$ENV: $SUBJECT Purchase Order Pending for Confirmation     ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This is a reminder to confirm the Purchase Order. Kindly review the details and confirm your acceptance within the stipulated timeframe to ensure timely processing of your order. </span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Purchase Order Details</strong></span></p>\r\n<p style="text-align:center;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>$SUMMARY_TABLE</strong></span></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">You can access and review the Purchase Order details by logging into the</span> <a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a>&nbsp;</p>\r\n<p style="text-align:left;"></p>\r\n<p><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">Please log in and proceed with the confirmation. If you require assistance, feel free to reach out to our support team. </span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span> <br></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725271442469,
      updatedBy: "<EMAIL>",
      updatedOn: 1730054194527,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "e93f2654-b340-422c-8834-8b7248826e85",
      objectId: "a660edd6-e37e-44fe-ada7-d96f3984c0c8",
      name: "Template_Pending_PO_Confirmation_Approval",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_PO_CONFIRMATION_APPROVAL",
      processDesc: "Pending PO Confirmation Approval",
      entity: "PURCHASE_ORDER",
      entityDesc: "Purchase Order Updates",
      subject: "$ENV: Reminder: Pending Purchase Order Confirmation Approval ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This is a friendly reminder that you have pending Purchase Order Confirmation(s) awaiting your approval for the below mentioned Purchase Order.  Please log in to the Supplier Collaboration Portal to review and approve them promptly.</span></p>\r\n<p></p>\r\n<p><strong>Purchase Order Details:</strong></p>\r\n<ul>\r\n<li><strong>PO ID : $PO_ID</strong></li>\r\n</ul>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Login Details</strong></span></p>\r\n<ul>\r\n<li><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Portal URL: </strong></span> <a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins> </ins></strong></span></li>\r\n</ul>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">Your timely approval will ensure smooth processing of the orders and timely delivery.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you </span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span> <br></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725272662137,
      updatedBy: "<EMAIL>",
      updatedOn: 1729686536801,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "b112f3ee-7a97-41ce-8a33-c6ce85847779",
      objectId: "32b2d0bd-358a-413f-be57-9091c0844d1f",
      name: "Template_Pending_PO_Rejection_Approval_Update",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_PO_REJECTION_APPROVAL",
      processDesc: "Pending PO Rejection Approval",
      entity: "PURCHASE_ORDER",
      entityDesc: "Purchase Order Updates",
      subject: "$ENV: Pending purchase order rejection approval update ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email is to inform you about the update on the pending purchase order rejection request. Your approval for the rejection for changes of below mentioned Purchase Order is currently pending. Please log in to the Supplier Collaboration Portal to review the request and take necessary action. </span></p>\r\n<p></p>\r\n<p><strong>PO Details</strong></p>\r\n<ul>\r\n<li><strong>PO Number: </strong> <span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>$PO_NUMBER</strong></span> <br></li>\r\n</ul>\r\n<p><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">Please log in </span><a href="$APPLICATION_URL" target="_blank"><span style="color: var(--rs-text-link-hover);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a><span style="color: var(--rs-text-link-hover);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>  </ins></strong></span>  to access and update the relevant task<span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">. If you encounter any issues or require assistance, feel free to reach out to our support team.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span> <br></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725276098577,
      updatedBy: "<EMAIL>",
      updatedOn: 1729686606147,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "050df7b6-d74e-4916-8547-440a0d6f6fb6",
      objectId: "41399b68-3788-404f-8e2a-148e2f65f29a",
      name: "Template_Pending_PO_Release",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_PO_RELEASE",
      processDesc: "Pending PO Release",
      entity: "PURCHASE_ORDER",
      entityDesc: "Purchase Order Updates",
      subject: "$ENV: $SUBJECT Pending Purchase Order release   ",
      content:
        '<p><span style="font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\r\n<p><span style="font-size: 14px;font-family: Roboto, sans-serif;">This email is to inform you that the following Purchase Order (PO) line items are currently pending release.</span></p>\r\n<p><span style="font-size: 14px;font-family: Arial;"><strong>$SUMMARY_TABLE</strong></span></p>\r\n<p></p>\r\n<p style="margin-left:1.5em;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 15px;font-family: Segoe UI;">Please log in to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 15px;font-family: Segoe UI;"> to access furthur details. If you encounter any issues or require assistance, feel free to reach out to our support team.</span>&nbsp;</p>\r\n<p></p>\r\n<p><span style="font-size: 14px;font-family: Arial;">Thank you.</span></p>\r\n<p></p>\r\n<p><span style="font-size: 14px;font-family: Arial;">Best regards,</span></p>\r\n<p><span style="font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);font-size: 14px;font-family: Roboto, sans-serif;"><em>This is a system-generated email. Please do not reply or forward to this address.</em></span></p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1725271127622,
      updatedBy: "<EMAIL>",
      updatedOn: 1733120175290,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "93c3e2ec-ff14-4c79-afca-f4b670307cae",
      objectId: "69df974d-42d7-4358-8746-589beb85dfca",
      name: "Template_Pending_Production_Completion",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_PRODUCTION_COMPLETION",
      processDesc: "Pending Production Completion Before QC Inspection Date",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: $SUBJECT: Pending Production Completion Before QC Inspection   ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email serves as a reminder that your production for purchase order is nearing completion. Please ensure all production activities are finalized before proceeding with Quality Control (QC) inspection.</span></p>\r\n<p style="text-align:center;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">$SUMMARY_TABLE</span></p>\r\n<p style="text-align:left;"></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Login Details</strong></span></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Portal URL: </strong></span> <a href="$APPLICATION_URL" target="_blank"><span style="color: var(--rs-text-link);background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a>&nbsp;</p>\r\n<p style="text-align:left;"></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">Please log in and review the Production information.  If you have any questions, please contact our support team.</span>&nbsp;</p>\r\n<p style="text-align:left;"></p>\r\n<p style="text-align:left;"></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"></p>\r\n<p><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span>&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1729684953510,
      updatedBy: "<EMAIL>",
      updatedOn: 1733120227883,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "5b38443b-7359-4541-8390-158ad6599f8f",
      objectId: "28c7a529-37d4-4086-9f6e-125d64a6c5e4",
      name: "Template_Pending_Production_Delay_Approval_Update",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_PRODUCTION_DELAY_APPROVAL",
      processDesc: "Pending Production Delay Approval",
      entity: "PRODUCTION_REPORT",
      entityDesc: "Production Report Updates",
      subject: "$ENV: Reminder: Pending Production Delay Approval Task ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This is a friendly reminder that you have a pending Production Delay Approval task awaiting your action in the Supplier Collaboration Portal. Kindly review and approve the request promptly.</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Login Details</strong></span></p>\r\n<ul>\r\n<li><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Portal URL: </strong></span> <a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins> </ins></strong></span></li>\r\n</ul>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">Please log in using the provided details and complete the pending task.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span> <br></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725281913858,
      updatedBy: "<EMAIL>",
      updatedOn: 1729686691170,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "88a752fb-ded2-4de7-90da-1997d8b77bdd",
      objectId: "a94b93e5-22cf-4b6d-9228-61822875587c",
      name: "Template_Pending_Production_Start",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_PRODUCTION_START",
      processDesc: "Pending Production Start",
      entity: "PRODUCTION_REPORT",
      entityDesc: "Production Report Updates",
      subject: "$ENV: Pending Production Start ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email is to inform you that your production start is pending. We are awaiting your confirmation and necessary documentation to initiate the production process.</span><br>$SUMMARY_TABLE</p>\r\n<p style="text-align:left;"></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Login Details</strong></span></p>\r\n<ul>\r\n<li style="margin-left:1.5em;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Portal URL: </strong></span> <a href="$APPLICATION_URL" target="_blank"><span style="color: rgba(0,0,0,0.87);background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a>&nbsp;</li>\r\n</ul>\r\n<p style="text-align:left;"></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">Please log in and review the ASN information.  If you have any questions, please contact our support team.</span>&nbsp;</p>\r\n<p style="text-align:left;"></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span> <br>&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1729340530322,
      updatedBy: "<EMAIL>",
      updatedOn: 1733120431177,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "a5e12f94-4c53-42ea-91b3-c88c179c0a43",
      objectId: "62784ba9-a089-4ad6-bf82-2375e6e9d9eb",
      name: "Template_Pending_Production_Status_Update",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_PRODUCTION_STATUS_UPDATE",
      processDesc: "Pending Production Status Update",
      entity: "PRODUCTION_REPORT",
      entityDesc: "Production Report Updates",
      subject: "$ENV: $SUBJECT: Pending Production Status Update    ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This is a friendly reminder to update the production status for the below mentioned Purchase orders on our Supplier Collaboration Portal.</span><br></p>\r\n<p style="text-align:center;">$SUMMARY_TABLE</p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Login Details</strong></span></p>\r\n<ul>\r\n<li><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Portal URL: </strong></span> <a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a>&nbsp;</li>\r\n</ul>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">Please log in and update the status at your earliest convenience. This helps ensure smooth communication and timely completion of your order. If you face any difficulties, feel free to reach out to our support team.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span> <br></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725281224175,
      updatedBy: "<EMAIL>",
      updatedOn: 1733120474421,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "32664632-d75f-4487-8117-508ab901c35d",
      objectId: "f14a14cf-e5e5-4753-9a92-7a96aa89bdee",
      name: "Template_Pending_QC_Booking",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_QC_BOOKING",
      processDesc: "Pending Quality Check Booking",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: $SUBJECT:  Pending Quality check Booking     ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This is a reminder that you have a pending quality check booking. Please log in to the Supplier Collaboration Portal to complete the quality check and ensure timely delivery.</span><br></p>\r\n<p style="text-align:center;">$SUMMARY_TABLE</p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Login Details</strong></span></p>\r\n<ul>\r\n<li><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Portal URL: </strong></span> <a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a>&nbsp;</li>\r\n</ul>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">Please log in using the provided details and complete the quality check booking. If you encounter any issues or require assistance, feel free to reach out to our support team.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span> <br></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725348411269,
      updatedBy: "<EMAIL>",
      updatedOn: 1733120764323,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "03c8507e-adba-4e74-9505-4a6a9fb0846a",
      objectId: "78bb1a4e-8b97-4035-b768-e4747848684c",
      name: "Template_Pending_QC_Booking_Approval",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_QC_BOOKING_APPROVAL",
      processDesc: "Pending Quality Check Booking Approval",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: Action Required: Pending Quality Check Booking Approval ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This is a friendly reminder regarding below mentioned Quality Check Booking requires your approval.. Please review the details and take necessary action on the Supplier Collaboration Portal.</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Login Details</strong></span></p>\r\n<ul>\r\n<li><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Portal URL: </strong></span> <a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins> </ins></strong></span></li>\r\n</ul>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">Please log in using the provided details and review the pending Quality Check Booking. If you have any questions or need assistance, please contact our support team.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span> <br></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725349554843,
      updatedBy: "<EMAIL>",
      updatedOn: 1729686773801,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "7f79086d-b28e-49ce-bde3-6253e7957999",
      objectId: "2136fa6d-eac4-439b-a9b9-2b8889e6f234",
      name: "Template_Pending_QC_Inspection",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_QC_INSPECTION",
      processDesc: "Pending Quality Check Inspection",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: $SUBJECT: Pending Quality Inspection Report Submission     ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This is a reminder that you have  pending Quality Inspection Report submission for the below mention purchase order. Please submit the report as soon as possible to ensure to ensure timely delivery.</span></p>\r\n<p style="text-align:center;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>$SUMMARY_TABLE</strong></span></p>\r\n<p></p>\r\n<p><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">To submit the Quality Inspection Report, please log in to the </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;"> and access the relevant order. If you encounter any issues or require assistance, please feel free to contact our support team.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span><br></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span> <br></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725355444194,
      updatedBy: "<EMAIL>",
      updatedOn: 1733120850639,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "56f412b9-f982-4736-84f7-f1c32d80f8e5",
      objectId: "ae91936e-689d-4efd-9491-9b9b2452bb78",
      name: "Template_Pending_QC_Inspection_Approval",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_QC_INSPECTION_APPROVAL",
      processDesc: "Pending Quality Check Inspection Approval",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: Pending Quality Check Inspection Approval   ",
      content:
        '<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">Dear </span><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>$RECIPIENT</strong></span><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;"> User,</span>&nbsp;</p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">This is a system-generated reminder email regarding pending Quality Inspection Report task approval on the Supplier Collaboration Portal.</span> A<span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">nd we request you to review and take appropriate action.</span>&nbsp;</p>\r\n<p></p>\r\n<p style="text-align:center;"><strong>$SUMMARY_DATA</strong></p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Please log in to the</span><a href="https://incture-cherrywork-dev-cw-scp-dev-cw-scp-dev.cfapps.eu10-004.hana.ondemand.com/" target="_blank"> </a><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a><span style="color: rgb(68,114,196);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins> </ins></strong></span> <span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">to access the task and take necessary action. If you have any questions or face any issues during the process, please refer to the User Guide.</span>&nbsp;</p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">Thank you for your cooperation.</span>&nbsp;</p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">Best regards,</span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">Supplier Collaboration Portal Team</span>&nbsp;</p>\r\n<p style="text-align:center;"></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725357665012,
      updatedBy: "<EMAIL>",
      updatedOn: 1729686832021,
      attachments: null,
      toList: [],
      bccList: null,
      ccList: [],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "e834ab05-b93b-45a1-91a4-2aa2c724b972",
      objectId: "ae91936e-689d-4efd-9491-9b9b2452bb78",
      name: "Template_Pending_Quality_Check_Inspection_Approval",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PENDING_QC_INSPECTION_APPROVAL",
      processDesc: "Pending Quality Check Inspection Approval",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: Pending Quality Check Inspection Approval  ",
      content:
        '<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">Dear </span><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>$RECIPIENT</strong></span><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;"> User,</span>&nbsp;</p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">This is a system-generated email to notify you the there are pending quality inspections to submitted for the below listed Purchase order on the Supplier Collaboration Portal.</span>&nbsp;</p>\r\n<p></p>\r\n<p style="text-align:center;"><strong>$SUMMARY_DATA</strong></p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Please log in to the</span><a href="https://incture-cherrywork-dev-cw-scp-dev-cw-scp-dev.cfapps.eu10-004.hana.ondemand.com/" target="_blank"> </a><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a><span style="color: rgb(68,114,196);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins> </ins></strong></span> <span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">to access the confirmed PO and take necessary action. If you have any questions or face any issues during the process, please refer to the User Guide.</span>&nbsp;</p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">Thank you for your cooperation.</span>&nbsp;</p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">Best regards,</span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">Supplier Collaboration Portal Team</span>&nbsp;</p>\r\n<p style="text-align:center;"></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725357665012,
      updatedBy: "<EMAIL>",
      updatedOn: 1729686856164,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "a32663d5-9305-477e-bb12-462f9a243788",
      objectId: "b3edc1e0-1f94-46fe-8b4f-bafd54ffb693",
      name: "Template_Production_Delay_Approval_Update",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PRODUCTION_DELAY_APPROVAL",
      processDesc: "Production Delay Approval",
      entity: "PRODUCTION_REPORT",
      entityDesc: "Production Report Updates",
      subject: "$ENV: Production Delay Approval Update     ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email is to notify an update on your production delay approval request. </span></p>\n<p><br><strong>$SUMMARY_TABLE</strong></p>\n<p></p>\n<p>Please login to <a href="$APPLICATION_URL" target="_blank"><strong>Supplier Collaboration Portal</strong></a> for further details. In case  you encounter any issues or require assistance, feel free to reach out to our support team.</p>\n<p>&nbsp;</p>\n<p>Thank you. <br>Best regards,  <br>Supplier Collaboration Portal Team</p>\n<p>&nbsp;</p>\n<p></p>\n<p style="text-align:center;"><span style="color: rgb(226,80,65);">This is an automated notification. Please do not reply to this email address. </span></p>\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725281723641,
      updatedBy: "<EMAIL>",
      updatedOn: 1739877775994,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "13a139d1-436b-45ce-b091-381118548e55",
      objectId: "433561f7-a7ac-4071-985e-5ec5e5bf4659",
      name: "Template_Production_Delay_Request",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PRODUCTION_DELAY",
      processDesc: "Production Delay",
      entity: "PRODUCTION_REPORT",
      entityDesc: "Production Report Updates",
      subject: "$ENV: Delay In Production Completion     ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email is to notify about a delay in the completion of production of below mentioned purchase orders. </span><br></p>\r\n<p style="text-align:center;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>$SUMMARY_TABLE</strong></span></p>\r\n<p></p>\r\n<p style="text-align:left;"></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">  for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal</span>&nbsp;</p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span></p>\r\n<p>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1725281403791,
      updatedBy: "<EMAIL>",
      updatedOn: 1737971950263,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "47be9aac-2f6f-4820-ad33-a6f16ef2afb9",
      objectId: "1c0431ae-f45e-4e37-9912-23e00c10533a",
      name: "Template_Production_Status_Update",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "PRODUCTION_STATUS_UPDATE",
      processDesc: "Production Status Update",
      entity: "PRODUCTION_REPORT",
      entityDesc: "Production Report Updates",
      subject: "$ENV: Production Status Update      ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email is to notify the status of the production has been updated for the below mentioned purchase orders.</span><br></p>\r\n<p style="text-align:center;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>$SUMMARY_TABLE</strong></span></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">  for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal Team</span>&nbsp;</p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span></p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1725276403925,
      updatedBy: "<EMAIL>",
      updatedOn: 1737971636059,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "5c20e879-4583-4a51-9166-cc6fca9642ce",
      objectId: "2c3cdb75-0f0b-4055-9592-a75b58ccd982",
      name: "Template_QC_Booking_Approval_Update",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "QC_BOOKING_APPROVAL",
      processDesc: "Quality Check Booking Approval",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: Quality Check Booking Approval Update     ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email is to notfy that quality check booking request has been updated. </span><br></p>\r\n<p style="text-align:left;"><strong>Booking Details:</strong></p>\r\n<p style="text-align:center;"><strong>$SUMMARY_TABLE</strong></p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"> for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal</span>&nbsp;</p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span></p>\r\n<p>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1725349326058,
      updatedBy: "<EMAIL>",
      updatedOn: 1737973230207,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "12e68313-bd15-4bf7-b767-3a40403e3042",
      objectId: "ff0bb550-ad62-4995-96d7-3457d15e55d9",
      name: "Template_QC_Booking_Modified",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "QC_BOOKING_MODIFIED",
      processDesc: "Quality Check Booking Modified",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: Quality Check Booking Modified    ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email is to notify that the quality check booking for the below mentioned Purchase order has been modified. </span><br></p>\r\n<p style="text-align:center;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>$SUMMARY_TABLE</strong></span></p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">  for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal Team</span>&nbsp;</p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span></p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1725357023287,
      updatedBy: "<EMAIL>",
      updatedOn: 1737974553956,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "20b3bf67-1c57-46ae-8f11-6d9397189087",
      objectId: "e4ddc224-f721-4214-948b-579b4086431e",
      name: "Template_QC_Booking_Modified_Approval",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "QC_BOOKING_MODIFIED_APPROVAL",
      processDesc: "Quality Check Booking Modified Approval",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: Quality Check Booking Modified Approval ",
      content:
        '<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">Dear </span><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>$RECIPIENT</strong></span><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;"> User,</span>&nbsp;</p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">This is a system-generated email to notify you regarding Quality Check Booking modifications submited by</span><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong> $INITIATOR</strong></span><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;"> user on the Supplier Collaboration Portal.</span>&nbsp;</p>\r\n<p></p>\r\n<p style="text-align:center;"><strong>$SUMMARY_DATA</strong></p>\r\n<p style="text-align:center;"></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">The supplier has provided the necessary information, and we request you to review and take appropriate action on the changes.</span>&nbsp;</p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Please log in to the</span><a href="https://incture-cherrywork-dev-cw-scp-dev-cw-scp-dev.cfapps.eu10-004.hana.ondemand.com/" target="_blank"> </a><a href="https://incture-cherrywork-dev-cw-scp-dev-cw-scp-dev.cfapps.eu10-004.hana.ondemand.com/$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a> <span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">to access the changes in booking and take necessary action. If you have any questions or face any issues during the process, please refer to the User Guide.</span>&nbsp;</p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">Thank you for your cooperation.</span>&nbsp;</p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">Best regards,</span></p>\r\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;">Supplier Collaboration Portal Team</span>&nbsp;</p>\r\n<p style="text-align:center;"></p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1725358771947,
      updatedBy: "<EMAIL>",
      updatedOn: 1727763034493,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "6b1cb259-0bce-44db-896e-7389ccb3c62e",
      objectId: "bf21044f-e7e0-4833-b71f-11f90d8aba78",
      name: "Template_QC_Booking_Modified_Pre_Inspection",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "QC_BOOKING_MODIFIED_PRE_INSPECTION",
      processDesc: "Quality Check Booking Modified Before Inspection Date",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: Quality Check Booking Modified having less than three days for Quality Check Inspection    ",
      content:
        '<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email is to notify that the quality check booking for the below mentioned Purchase order has been modified <strong>having less than three days for Quality Check Inspection. </strong></span>&nbsp;</p>\r\n<p style="text-align:left;"></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Details:</strong></span></p>\r\n<p style="text-align:center;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>$SUMMARY_TABLE</strong></span></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"> for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal Team</span>&nbsp;</p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span> <br>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1730105518859,
      updatedBy: "<EMAIL>",
      updatedOn: 1737975678853,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "593d71cc-3616-4225-b80b-2a97ed1cf54c",
      objectId: "8a80722a-9adf-4fcb-b11b-de2ef7c43a3b",
      name: "Template_QC_Inspection_Approval_Update",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "QC_INSPECTION_APPROVAL",
      processDesc: "Quality Check Inspection Approval",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: Quality Check Inspection Approval         ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\r\n<p>This email is to notify that quality check inspection form has been updated.&nbsp;&nbsp;</p>\r\n<p style="text-align:center;"><strong>$SUMMARY_TABLE</strong></p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">  for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal Team</span>&nbsp;</p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span></p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1725355793285,
      updatedBy: "<EMAIL>",
      updatedOn: 1737975383735,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "0a09494b-f9a1-4649-b66b-d83b9aa738d0",
      objectId: "73f7feb3-47ff-424f-a751-1cdc3834a9aa",
      name: "Template_QC_Inspection_Submisison",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "QC_INSPECTION_SUBMITTED",
      processDesc: "Quality Check Inspection Submitted",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: Quality Inspection Report Submitted       ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email is to notify that quality inspection results have been submitted for the below mentioned purchase orders. You can now access and review the report within the Supplier Collaboration Portal.</span><br></p>\r\n<p style="text-align:center;"><strong>$SUMMARY_TABLE</strong></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">  for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal Team</span>&nbsp;</p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span></p>\r\n<p>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1725349774611,
      updatedBy: "<EMAIL>",
      updatedOn: 1737974932870,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "582356ef-3956-4682-ac07-01aaf2bf4628",
      objectId: "9c9ad66e-5091-4499-a922-0215f6de7db7",
      name: "Template_Quality_Check_Ready_For_Booking",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "QC_BOOKING_READY",
      processDesc: "Quality Check Ready For Booking",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: Ready For Quality Check Booking ",
      content:
        '<p style="text-align:start;"></p>\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span>&nbsp;</p>\n<p style="text-align:start;"></p>\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;">This email is to notify you that the following line items are ready for QC booking.</span></p>\n<p style="text-align:left;"></p>\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Update Details:</strong></span></p>\n<p style="text-align:left;"></p>\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>$SUMMARY_TABLE</strong></span></p>\n<p style="text-align:left;"></p>\n<p>Please login to <a href="$APPLICATION_URL" target="_blank"><strong>Supplier Collaboration Portal</strong></a><strong> </strong> for further details. In case you encounter any issues or require assistance, feel free to reach out to our support team.</p>\n<p>&nbsp;</p>\n<p>Thank you. <br>Best regards,  <br>Supplier Collaboration Portal Team</p>\n<p>&nbsp;</p>\n<p style="text-align:center;">&nbsp;</p>\n<p style="text-align:center;"><span style="color: rgb(209,72,65);">This is an automated notification. Please do not reply to this email address. </span></p>\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1739870647517,
      updatedBy: "<EMAIL>",
      updatedOn: 1740046750061,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "aa4f3868-a053-460a-8fb9-a6a25f898bf7",
      objectId: "d40949ae-d629-4c5e-907b-f92109dc4b24",
      name: "Template_Quality_Check_Summary",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "QC_SUMMARY",
      processDesc: "Quality Check Booking & Quality Check Inspection Summary",
      entity: "QUALITY_CHECK",
      entityDesc: "Quality Check Updates",
      subject: "$ENV: Quality Inspection recorded for past 5 days and QC booking for upcoming 5 days     ",
      content:
        '<p><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\r\n<p style="text-align:left;"><span style="color: rgb(87,87,87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">This email provides you with a summary of upcoming quality inspections scheduled for the next 5 days and a list of inspections that have been completed in the past 5 days.</span><br></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>$QC_INSPECTION_HEADING</strong></span></p>\r\n<p>$QC_INSPECTION_SUMMARY_TABLE</p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>$QC_BOOKING_HEADING</strong></span></p>\r\n<p>$QC_BOOKING_SUMMARY_TABLE</p>\r\n<p></p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Please log in to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a> <span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"> to access furthur details. If you encounter any issues or require assistance, feel free to reach out to our support team.</span>&nbsp;</p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;">Thank you.</span></p>\r\n<p style="text-align:start;"></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;">Best regards,</span></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span>&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1728044717741,
      updatedBy: "<EMAIL>",
      updatedOn: 1733380556090,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "ed93f3f1-f93f-4e33-b5ad-421b7993158b",
      objectId: "d638e28a-4f81-4770-9015-aba439f56907",
      name: "Template_Revised_Delivery_Modify",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "REVISED_DELIVERY_MODIFY",
      processDesc: "Revised Delivery Modify",
      entity: "PURCHASE_ORDER",
      entityDesc: "Purchase Order Updates",
      subject: "$ENV : Revised Delivery Modification Submitted   ",
      content:
        '<p><span style="font-size: 14px;font-family: Arial;">Dear <strong>$USER</strong>,</span>&nbsp;</p>\r\n<p><span style="font-size: 14px;font-family: Arial;">This email is to notify that the revised delivery modification submitted by <strong>$SUPPLIER</strong> has been received for the below mentioned purchased orders.</span>&nbsp;</p>\r\n<p></p>\r\n<p style="text-align:center;"><strong>$SUMMARY_TABLE</strong></p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">  for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal Team</span>&nbsp;</p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span></p>\r\n<p>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1732106215279,
      updatedBy: "<EMAIL>",
      updatedOn: 1737983167321,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "5d982da6-6ec6-4f48-9412-4b08db7dbd53",
      objectId: "45d6daa3-3f18-4806-8223-d542bfdfba76",
      name: "Template_Revised_Delivery_Modify_Approval",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "REVISED_DELIVERY_MODIFY_APPROVAL",
      processDesc: "Revised Delivery Modify Approval",
      entity: "PURCHASE_ORDER",
      entityDesc: "Purchase Order Updates",
      subject: "$ENV : Revised Delivery Modification Status   ",
      content:
        '<p>Dear <strong>$USER</strong>,</p>\r\n<p>This email is to notify the status of delivery revision of the below mentioned purchase orders.</p>\r\n<p></p>\r\n<p style="text-align:center;"><strong>$SUMMARY_TABLE</strong></p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">Please login to </span><a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;"><strong>Supplier Collaboration Portal</strong></span></a><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">  for further details. In case you</span> <span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 10.5pt;font-family: 等线;">encounter any issues or require assistance, feel free to reach out to our support team.</span></p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Thank you.</span>&nbsp;</p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Best regards, </span></p>\r\n<p style="text-align:left;"><span style="background-color: rgb(255,254,254);font-size: 10.5pt;font-family: 等线;">Supplier Collaboration Portal</span>&nbsp;</p>\r\n<p style="text-align:left;">&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(255,0,0);background-color: rgb(255,254,254);font-size: 10.5pt;font-family: Calibri;"><em>This is an automated notification. Please do not reply to this email address</em></span><span style="color: rgb(255,0,0);background-color: rgb(255,255,255);font-size: 7.5pt;font-family: Calibri;"><em>.</em></span></p>\r\n<p>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1732106678158,
      updatedBy: "<EMAIL>",
      updatedOn: 1737983392765,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "a54b13ed-4238-43a3-b64e-b236de2f7bc5",
      objectId: "cab40298-fc7a-45b7-a83a-50a045369072",
      name: "Template_Supplier_Change_Request_Approval",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "MSA_REQUEST_APPROVAL",
      processDesc: "Supplier Change Request Approval",
      entity: "MANAGE_SUPPLIER_ACCOUNT",
      entityDesc: "Supplier Details Update",
      subject: "$ENV: Update: Supplier $REQUEST_TYPE Request Approved By $USER_GROUP     ",
      content:
        '<p style="text-align:start;"><span style="font-size: inherit;">Dear <strong>$USER</strong>,</span></p>\r\n<p style="margin-left:0px;"><span style="font-size: inherit;">This is a system-generated email to notify you that the Supplier <strong>$REQUEST_TYPE</strong> request is successfully approved by<strong> $USER_GROUP</strong> with the Request Number <strong>$REQUEST_ID.</strong></span></p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;">Please log in to </span><a href="https://ca-gbd-ca-scp-dev-scp.cfapps.us10-001.hana.ondemand.com/$APPLICATION_URL" target="_blank"><span style="color: var(--rs-text-link);background-color: transparent;font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a> <span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"> to access furthur details. If you encounter any issues or require assistance, feel free to reach out to our support team.</span>&nbsp;</p>\r\n<p style="text-align:left;"></p>\r\n<p><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;">Thank you.</span></p>\r\n<p></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;">Best regards,</span></p>\r\n<p style="text-align:left;"><span style="color: rgba(0,0,0,0.87);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span>&nbsp;</p>\r\n<p style="text-align:center;"><span style="color: rgb(205,89,55);font-size: inherit;">This is system generated email. Please do not reply or forward to this email</span>&nbsp;</p>\r\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: false,
      createdBy: "<EMAIL>",
      createdOn: 1715862483363,
      updatedBy: "<EMAIL>",
      updatedOn: 1733122646849,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
    {
      emailDefinitionId: "33f90e1a-0ffb-4b39-a8c3-75f6e55c308f",
      objectId: "195b6040-19dd-40aa-b598-2e1242388f5a",
      name: "Template_Update_Existing_User_Details",
      application: "ITM",
      applicationDesc: "Intelligent Task Management",
      process: "USER_UPDATE_NOTIFICATION",
      processDesc: "Existing User Details Update Notification",
      entity: "USER_CREATION",
      entityDesc: "User Creation Updates",
      subject: "$ENV: Update: Your User Details Have Been Modified  ",
      content:
        '<p style="text-align:start;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;">Dear <strong>$USER</strong>,</span></p>\n<p></p>\n<p>We are pleased to inform that your profile details have been modified.&nbsp;&nbsp;</p>\n<p></p>\n<p><strong>PREVIOUS DETAILS:</strong></p>\n<p>$OLD_SUMMARY_TABLE</p>\n<p></p>\n<p><strong>UPDATED DETAILS:</strong></p>\n<p>$NEW_SUMMARY_TABLE</p>\n<p style="text-align:left;"></p>\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Arial;"><strong>Login Details</strong></span></p>\n<ul>\n<li style="margin-left:1.5em;"><span style="color: rgb(0,0,0);background-color: rgb(255,254,254);font-size: 14px;font-family: Roboto, sans-serif;"><strong>Portal URL: </strong></span> <a href="$APPLICATION_URL" target="_blank"><span style="color: rgb(68,114,196);background-color: rgb(255,255,255);font-size: 14px;font-family: Roboto, sans-serif;"><strong><ins>Supplier Collaboration Portal</ins></strong></span></a>&nbsp;&nbsp;</li>\n</ul>\n<p style="text-align:left;"><span style="color: rgb(0,0,0);background-color: rgb(255,255,255);font-size: 14px;font-family: Arial;">Please log in using the provided details and explore the platform\'s features. If you encounter any issues or require assistance, feel free to reach out to our support team.</span>&nbsp;</p>\n<p style="text-align:left;"></p>\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Thank you.</span>&nbsp;</p>\n<p style="text-align:left;"></p>\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Best regards, </span></p>\n<p style="text-align:left;"><span style="color: windowtext;background-color: transparent;font-size: 14px;font-family: Arial;">Supplier Collaboration Portal Team</span>&nbsp;</p>\n<p style="text-align:center;"></p>\n<p style="text-align:center;"><span style="color: rgb(209,72,65);background-color: rgb(255,255,255);font-size: 12px;font-family: Roboto, sans-serif;"><em>This is system generated email. Please do not reply or forward to this email</em></span>&nbsp;</p>\n',
      signature: null,
      fromAddress: null,
      status: "Active",
      bccAllowed: null,
      createdBy: "<EMAIL>",
      createdOn: 1742549674350,
      updatedBy: "<EMAIL>",
      updatedOn: 1742908362644,
      attachments: null,
      toList: null,
      bccList: null,
      ccList: ["<EMAIL>"],
      destinationName: null,
      identifier: "6dd4a772-7856-4632-bb49-694f8f499386",
      identifierDesc: "SCP_BATCH",
    },
  ],
};

export default mailDefinitionResponse;
