import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Divider,
  <PERSON><PERSON>,
  <PERSON>rid,
  <PERSON><PERSON>,
  Swipeable<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
} from "@mui/material";
import { Stack } from "@mui/system";
import moment from "moment";
import React, { useEffect, useState } from "react";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import EventNoteIcon from "@mui/icons-material/EventNote";
import DoneAllIcon from "@mui/icons-material/DoneAll";
import HistoryIcon from "@mui/icons-material/History";
import TaskIcon from "@mui/icons-material/Task";
import ReusableIcon from "./ReusableIcon";
import { useNavigate } from "react-router-dom";
import { setHistoryPath } from "../../app/utilitySlice";
import { useDispatch, useSelector } from "react-redux";
import { doAjax } from "./fetchService";
import { destination_Notification } from "../../destinationVariables";
import NavigationIcon from "@mui/icons-material/Navigation";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import nonotificationimg from "../../../src/utilityImages/nonotificationfound.png";

import { capitalize } from "../../functions";
import { capitalizeByWord } from "../../functions";
import { baseUrl_Notification } from "../../data/baseUrl";
// import AnimatedPage from "./AnimatedPage";
import { updateNotification } from "../../app/notificationSlice";
const AppNotification = ({ status, handleClose, data }) => {
  let dispatch = useDispatch();
  let navigate = useNavigate();
  let userData = useSelector((state) => state.userManagement.userData);

  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 1);
  let [compController, setCompController] = useState({
    endCount: 30,
  });
  let getModule = (item) => {
    //check if request id is available, if yes continue with requestid else po number

    let id = item.requestId ?? item.poNumber;

    let moduleData = {
      BCS: "Broadcast Management",
      RTC: "Return Order",
      INC: "Invoice",
    };
    //First 3 letters are prefix of request id which is based on module is belongs
    if (typeof id === "string") {
      //Slice the prefix
      let prefix = id?.slice(0, 3);
      //get the value for the prefix
      let result = moduleData[prefix];
      //if no value found return purchase order by default
      return result ?? "Purchase Order";
    }
  };
  let getSubmodule = (data) => {
    let module = data.module ?? "";

    if (typeof module === "string") {
      module = module?.toLowerCase();

      switch (module) {
        case "advanced shipment notification":
          return "ASN";
          break;
        case "purchase order":
          return "PO";
          break;
        case "return order":
          return "RTC";
          break;
        case "daily production report":
          return "DPR";
          break;
        case "invoice":
          return "INV";
          break;
        case "broadcast management":
          return "BCS";
          break;
        default:
          return "PO";
      }
    }
  };
  let getStatusMessage = (notificationStatus) => {
    if (typeof notificationStatus === "string") {
      let status = notificationStatus?.toLowerCase();
      // console.log(status)
      if (status.includes("confirmation submitted")) {
        //**
        return "";
      }
      if (status.includes("confirmed")) {
        return "";
      }
      if (status.includes("requires confirmation")) {
        return "";
      }
      if (status.includes("closed")) {
        return "";
      }
      if (status.includes("update")) {
        return "";
      } else {
        return ``;
      }
    }
  };
  let getTitle = (activityStatus) => {
    // if (activityStatus?.toLowerCase().includes("confirmed")) {
    //   return "Confirmation Approved";
    // }
    // if (activityStatus?.toLowerCase().includes("confirmed")) {
    //   return "Confirmation Approved";
    // }
    return activityStatus;
  };
  let getId_HyperLink = (data, index) => {
    let id;
    let url;
    if (data.requestId?.includes("SRV")) {
      id = data.requestId;
      url = "/serviceRequest/details/";
    } else if (data.module?.toLowerCase() === "broadcast management") {
      id = data.requestId;
      url = `/`;
    } else if (data.module?.toLowerCase() === "purchase request") {
      id = data?.requestId;
      url = `/purchaseRequest/management/singlePurchaseRequest/`;
    } else {
      id = data?.poNumber;
      url = "/purchaseOrder/management/singlePurchaseOrder/";
    }
    // if (data.requestId?.includes("SRV")) {
    //   id = data.requestId;
    //   url = "/serviceRequest/details/";
    // } else {
    //   id = data.poNumber;
    //   url = "/purchaseOrder/management/singlePurchaseOrder/";
    // }
    return (
      <a
        style={{
          maxWidth: "inherit",
          color: "#3B30C8",
          whiteSpace: "no-wrap",
          textOverflow: "ellipsis",
        }}
        className="pointer_cursor hrefLink_Color"
        onClick={() => {
          handleMarkAsRead_Single(data, index);
          handleClose();

          // dispatch(
          //   setHistoryPath({ url: window.location.pathname, module: "" })
          // );
          navigate(url + id);
        }}
      >
        {id}
      </a>
    );
  };
  let getReqId_HyperLink = (data, index) => {
    let id = data.requestId;
    let url;
    let module = getModule(data)?.toLowerCase();
    let urls = {
      "service request": "/serviceRequest/details/",
      "broadcast management": "/",
      "purchase request": `/purchaseRequest/management/singlePurchaseRequest/`,
      "return order": "/ReturnManagement/SingleReturnOrder/",
      "advance shipment notification": "/purchaseOrder/ASN/details/",
    };
    url = urls[module];
    if (id) {
      return (
        <a
          style={{
            maxWidth: "inherit",
            color: "#3B30C8",
            whiteSpace: "no-wrap",
            textOverflow: "ellipsis",
          }}
          className="pointer_cursor hrefLink_Color"
          onClick={() => {
            handleMarkAsRead_Single(data, index);
            handleClose();
            navigate(url + id);
          }}
        >
          {id}
        </a>
      );
    } else {
      return "";
    }
  };
  let buildNotification = (item, index) => {
    // let module = item.module?.toLowerCase();
    // let messageMap = {
    //   "broadcast management": (
    //     <Typography variant="body1" color="#757575">
    //       <span
    //         style={{ fontWeight: "bold" }}
    //       >{`Broadcast ${item.broadcastCategory}:`}</span>
    //       {`${item.broadcastTitle}  is ${getTitle(item.activityStatus)} by `}
    //       <span
    //         style={{ fontWeight: "bold" }}
    //       >{`${item.companyCode} - ${item.roleName}`}</span>
    //       {getId_HyperLink(item, index)}
    //     </Typography>
    //   ),
    //   "po open": (
    //     <Typography variant="body2" color="#757575">
    //       <span style={{ textTransform: "capitalize",
    //           fontWeight: "bold" }}>{`New ${getModule(
    //         item
    //       )} `}
    //       {getId_HyperLink(item, index)}
    //       </span>
    //       <span style={{ fontWeight: "bold" }}>{` Created`}</span>
    //     </Typography>
    //   ),
    //   "po acknowledge escalation": (
    //     <Typography variant="body2" color="#757575" sx={{}}>
    //       <span
    //         style={{
    //           textTransform: "capitalize",
    //           fontWeight: "bold",
    //         }}
    //       >
    //         {`${getTitle(capitalizeByWord(item.activityStatus, ["PO"]))} for `}{" "}
    //         {getId_HyperLink(item)}
    //       </span>
    //     </Typography>
    //   ),
    //   "po acknowledge reminder": (
    //     <Typography variant="body2" color="#757575" sx={{}}>
    //       <span
    //         style={{
    //           textTransform: "capitalize",
    //           fontWeight: "bold",
    //         }}
    //       >
    //         {`${getTitle(capitalizeByWord(item.activityStatus, ["PO"]))} for `}{" "}
    //         {getId_HyperLink(item)}
    //       </span>
    //       {/* <span style={{ fontWeight: "bold" }}>{`${
    //         item.companyCode ?? item.supplierId ?? item.createdBy
    //       } ${item.roleName ? " " + "-" + " " + item.roleName : ""}`}</span> */}
    //     </Typography>
    //   ),
    //   "po requires confirmation reminder": (
    //     <Typography variant="body2" color="#757575" sx={{}}>
    //       <span
    //         style={{
    //           textTransform: "capitalize",
    //           fontWeight: "bold",
    //         }}
    //       >
    //         {`${getTitle(capitalizeByWord(item.activityStatus, ["PO"]))} for `}{" "}
    //         {getId_HyperLink(item)}
    //       </span>
    //       {/* <span style={{ fontWeight: "bold" }}>{`${
    //         item.companyCode ?? item.supplierId ?? item.createdBy
    //       } ${item.roleName ? " " + "-" + " " + item.roleName : ""}`}</span> */}
    //     </Typography>
    //   ),
    //   "po confirmation reminder": (
    //     <Typography variant="body2" color="#757575" sx={{}}>
    //       <span
    //         style={{
    //           textTransform: "capitalize",
    //           fontWeight: "bold",
    //         }}
    //       >
    //         {`PO Confirmation Reminder for `}{" "}
    //         {getId_HyperLink(item)}
    //       </span>
    //       {/* <span style={{ fontWeight: "bold" }}>{`${
    //         item.companyCode ?? item.supplierId ?? item.createdBy
    //       } ${item.roleName ? " " + "-" + " " + item.roleName : ""}`}</span> */}
    //     </Typography>
    //   ),
    //   "po requires confirmation escalation": (
    //     <Typography variant="body2" color="#757575" sx={{}}>
    //       <span
    //         style={{
    //           textTransform: "capitalize",
    //           fontWeight: "bold",
    //         }}
    //       >
    //         {`PO Requires Confirmation Escalation for `}{" "}
    //         {getId_HyperLink(item)}
    //       </span>
    //       {/* <span style={{ fontWeight: "bold" }}>{`${
    //       item.companyCode ?? item.supplierId ?? item.createdBy
    //     } ${item.roleName ? " " + "-" + " " + item.roleName : ""}`}</span> */}
    //     </Typography>
    //   ),
    //   "po confirm submission accept": (
    //     <Typography variant="body2" color="#757575" sx={{}}>
    //       <span
    //         style={{
    //           textTransform: "capitalize",
    //           fontWeight: "bold",
    //         }}
    //       >
    //         {`PO `}{" "} {getId_HyperLink(item)}{" "} {`Has Been Confirmed`}

    //       </span>
    //       {/* <span style={{ fontWeight: "bold" }}>{`${
    //         item.companyCode ?? item.supplierId ?? item.createdBy
    //       } ${item.roleName ? " " + "-" + " " + item.roleName : ""}`}</span> */}
    //     </Typography>
    //   ),
    //   "po confirm submission reject": (
    //     <Typography variant="body2" color="#757575" sx={{}}>
    //       <span
    //         style={{
    //           textTransform: "capitalize",
    //           fontWeight: "bold",
    //         }}
    //       >
    //         {`Confirmation For PO `}{" "} {getId_HyperLink(item)}{" "} {`Has Been Rejected`}

    //       </span>
    //       {/* <span style={{ fontWeight: "bold" }}>{`${
    //         item.companyCode ?? item.supplierId ?? item.createdBy
    //       } ${item.roleName ? " " + "-" + " " + item.roleName : ""}`}</span> */}
    //     </Typography>
    //   ),
    //   "po approval accept": (
    //     <Typography variant="body2" color="#757575" sx={{}}>
    //       <span
    //         style={{
    //           textTransform: "capitalize",
    //           fontWeight: "bold",
    //         }}
    //       >
    //         {`PO `}{" "}
    //         {getId_HyperLink(item)}{" "} {`Has Been Approved`}
    //       </span>
    //       {/* <span style={{ fontWeight: "bold" }}>{`${
    //         item.companyCode ?? item.supplierId ?? item.createdBy
    //       } ${item.roleName ? " " + "-" + " " + item.roleName : ""}`}</span> */}
    //     </Typography>
    //   ),
    //   "po approval reject": (
    //     <Typography variant="body2" color="#757575" sx={{}}>
    //       <span
    //         style={{
    //           textTransform: "capitalize",
    //           fontWeight: "bold",
    //         }}
    //       >
    //         {`Approval For PO `}{" "}
    //         {getId_HyperLink(item)}{" "} {`Has Been Rejected`}
    //       </span>
    //       {/* <span style={{ fontWeight: "bold" }}>{`${
    //         item.companyCode ?? item.supplierId ?? item.createdBy
    //       } ${item.roleName ? " " + "-" + " " + item.roleName : ""}`}</span> */}
    //     </Typography>
    //   ),
    //   "pending asn": (
    //     <Typography variant="body2" color="#757575" sx={{}}>
    //       <span
    //         style={{
    //           textTransform: "capitalize",
    //           fontWeight: "bold",
    //         }}
    //       >
    //         {`${getTitle(capitalizeByWord(item.activityStatus, ["ASN"]))}`}
    //         {getReqId_HyperLink(item)}
    //         {` for PO `} {getId_HyperLink(item)}
    //       </span>
    //     </Typography>
    //   ),
    //   "new return request": (
    //     <Typography variant="body2" color="#757575" sx={{}}>
    //       <span
    //         style={{
    //           textTransform: "capitalize",
    //           fontWeight: "bold",
    //         }}
    //       >
    //         {`${getTitle(capitalizeByWord(item.activityStatus), [])} `}
    //         {getReqId_HyperLink(item)}
    //       </span>
    //       <span
    //         style={{
    //           textTransform: "capitalize",
    //           fontWeight: "bold",
    //         }}
    //       >
    //         {` for PO `}
    //         {getId_HyperLink(item)}
    //       </span>
    //     </Typography>
    //   ),
    // };
    // return messageMap[module];

    return (
      <Typography variant="body1" color="#757575">
        <span
          style={{ fontWeight: "bold" }}
        >{`${item.notificationType}: `}</span>
        {`${item.notification} `}
        {/* <span
          style={{ fontWeight: "bold" }}
        >{`${item.sentByName}`}</span> */}
        {getId_HyperLink(item, index)}
      </Typography>
    );
  };
  let getIcon = (data) => {
    let module = getSubmodule(data);
    switch (module) {
      case "ASN":
        return (
          <ReusableIcon
            iconName={"LocalShipping"}
            iconSize={"20px"}
            iconColor={"#7389AE"}
          />
        );

      case "PO":
        return (
          <ReusableIcon
            iconName={"ReceiptLong"}
            iconSize={"20px"}
            iconColor={"#FF9F69"}
          />
        );

      case "SRV":
        return (
          <ReusableIcon
            iconName={"ManageAccounts"}
            iconSize={"20px"}
            iconColor={""}
          />
        );

      case "DPR":
        return (
          <ReusableIcon
            iconName={"Factory"}
            iconSize={"20px"}
            iconColor={"#8239AF"}
          />
        );
      case "INV":
        return (
          <ReusableIcon
            iconName={"Description"}
            iconSize={"20px"}
            iconColor={"#007AD4"}
          />
        );
      case "RTC":
        return (
          <ReusableIcon
            iconName={"AssignmentReturn"}
            iconSize={"20px"}
            iconColor={"#7BBDCE"}
          />
        );
      case "BCS":
        return (
          <ReusableIcon
            iconName={"BroadcastOnPersonal"}
            iconSize={"20px"}
            iconColor={"#871e5f"}
          />
        );
      default:
        return (
          <ReusableIcon
            iconName={"ReceiptLong"}
            iconSize={"20px"}
            iconColor={"#FF9F69"}
          />
        );
    }
  };
  let getTimestamp = (itemData) => {
    // console.log(`sent date ${new Date(itemData.sentDate)} - created date ${new Date(itemData.createdAt)}`)
    if (
      new Date(itemData.sentDate).toDateString() === presentDate.toDateString()
    ) {
      return `Today ${moment(itemData.sentDate).format("hh:mm A")}`;
    }
    if (
      new Date(itemData.sentDate).toDateString() === backDate.toDateString()
    ) {
      return `Yesterday ${moment(itemData.sentDate).format("hh:mm A")}`;
    }
    return `${moment(itemData.sentDate).format("DD MMM YYYY hh:mm A")}`;
  };
  let handleMarkAsRead_All = () => {
    try {
      let tData = data.map((i) => {
        return {
          ...i,
          isRead: 1,
        };
      });
      dispatch(updateNotification([]));
      fetch(`/NotificationServices/v1/notification/dismissAllNotification`, {
        method: "POST",
      });
    } catch (e) {
      console.log(e);
    }
  };
  let handleMarkAsRead_Single = (itemData, index) => {
    try {
      fetch(
        `${baseUrl_Notification}/v1/notification/markAsReadSCP?notificationTxnId=${itemData.notificationTxnId}&userId=${userData.user_id}`
      );
      let tData = [];
      for (let i of data) {
        if (i.notificationTxnId === itemData.notificationTxnId) {
          tData.push({ ...i, isRead: 1 });
          break;
        }
        tData.push(i);
      }
      let slicedList = data?.slice(tData?.length) ?? [];
      tData.push(...slicedList);

      dispatch(updateNotification(tData));
    } catch (e) {
      console.log(e);
    }
  };
  let handleShowMore = () => {
    setCompController((prev) => {
      return {
        endCount: prev.endCount + 30,
      };
    });
  };
  let displayShowMoreButton = () => {
    if (data?.length === data?.slice(0, compController.endCount)?.length)
      return false;
    if (data?.length === 0) return false;
    if (data?.length > 30) return true;
  };

  // useEffect(() => {
  //   console.log(compController, "CompController");
  // }, [compController]);
  return (
    <>
      {/* <AnimatedPage delay={5}> */}
      <SwipeableDrawer
        anchor="right"
        open={status}
        onClose={handleClose}
        hideBackdrop={false}
        sx={{
          "& css-1u2w381-MuiModal-root-MuiDrawer-root": {
            zIndex: "12000",
          },
        }}
        PaperProps={{
          elevation: 4,
          sx: {
            minWidth: "max-content",
            width: "35vw",

            // boxShadow: "-4px 4px 4px rgba(0, 122, 212, 0.2)",
            border: "none",
          },
        }}
      >
        <Stack sx={{ width: "inherit" }}>
          <Stack
            sx={{
              padding: "0 0 1rem 0px",
            }}
          >
            <div
              style={{
                position: "sticky",
                overFlow: "auto",
                zIndex: "999",
                top: 0,
                left: 0,
                right: 0,
                background: "#fff",
              }}
            >
              <Grid
                container
                sx={{
                  backgroundColor: "#EAE9FF40",
                  height: "max-content",
                }}
                columns={15}
                p={1.5}
                py={1}
              >
                {/* <Grid item xs>
              <HistoryIcon />
              </Grid> */}

                <Grid xs={15}>
                  <Typography variant="h4" sx={{ fontSize: "18px" }}>
                    Notifications
                  </Typography>
                </Grid>
              </Grid>

              <Grid
                container
                sx={{
                  backgroundColor: "#EAE9FF40",
                  height: "max-content",
                }}
                columns={20}
                p={1.5}
                py={0}
              >
                <Grid xs={20}>
                  <Button
                    sx={{
                      maxHeight: "max-content",
                      maxWidth: "max-content",
                      fontSize: "12px",
                      fontWeight: "600",
                      textTransform: "capitalize",
                      padding: ".2rem .2rem .2rem 0",
                      color: "primary",
                    }}
                    onClick={handleMarkAsRead_All}
                    startIcon={
                      <DoneAllIcon
                        color="primary"
                        sx={{ paddingLeft: ".25rem" }}
                        fontSize="12px"
                      />
                    }
                  >
                    Mark all as read
                  </Button>
                </Grid>
              </Grid>
              <Divider sx={{ margin: "0px", marginBottom: "8px" }} />
            </div>
            <div>
              {data?.length === 0 && (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    height: "80vh",
                    opacity: ".5",
                  }}
                >
                  <img
                    src={nonotificationimg}
                    alt={"No New Notifications"}
                    style={{
                      width: "80%",
                    }}
                  ></img>
                  <Typography
                    sx={{
                      color: "grey",
                      fontWeight: "600",
                      fontSize: "16px",
                      marginTop: "1rem",
                    }}
                  >
                    No new notification found
                  </Typography>
                </Box>
              )}
              {data?.length > 0 && (
                <>
                  {data
                    ?.slice(0, compController.endCount)
                    .map((item, index) => {
                      return (
                        <>
                          <Box
                            sx={{
                              maxWidth: "inherit",
                              backgroundColor: "white",
                              border: "1px solid #f4f4f4",
                              borderRadius: "6px",
                              height: "max-content",
                              position: "relative",
                              display: "flex",
                              flexDirection: "row",
                            }}
                          >
                            <Grid
                              container
                              className="pointer_cursor"
                              sx={{
                                backgroundColor: "white",
                                "&:hover": { backgroundColor: "#f8f8f8" },
                                height: "max-content",
                              }}
                              onclick={() => {
                                console.log(item);
                              }}
                              columns={15}
                              p={1.5}
                            >
                              <Grid item xs={1}>
                                <Badge
                                  color="primary"
                                  variant="dot"
                                  invisible={item.isRead}
                                >
                                  {getIcon(item)}
                                </Badge>
                              </Grid>
                              <Grid
                                item
                                xs={14}
                                sx={{
                                  display: "flex",
                                  flexDirection: "column",
                                }}
                              >
                                {/* <Stack sx={{
                  display:'flex',
                  flexDirection:'row',
                  justifyContent:'center',
                  alignItems:'start'
                }}> */}

                                {buildNotification(item, index)}

                                {/* </Stack> */}

                                <Typography
                                  variant="body2"
                                  color="grey"
                                  sx={{
                                    width: "100%",
                                    // marginTop:'auto',
                                    fontWeight: "bold",
                                  }}
                                >
                                  {item.sentDate && getTimestamp(item)}
                                </Typography>
                                <Typography
                                  color="#757575"
                                  variant="body2"
                                  mt={0.5}
                                ></Typography>
                              </Grid>
                            </Grid>
                            {/* <ArrowForwardIosIcon fontSize="small"  sx={{
          
            }}/> */}
                          </Box>
                        </>
                      );
                    })}
                  {displayShowMoreButton() && (
                    <Box sx={{ display: "flex", justifyContent: "center" }}>
                      <Button
                        onClick={handleShowMore}
                        startIcon={
                          <ExpandMoreIcon
                            color="primary"
                            sx={{ paddingLeft: ".25rem" }}
                            fontSize="12px"
                          />
                        }
                        sx={{
                          maxHeight: "max-content",
                          maxWidth: "calc(max-content+1rem)",
                          fontSize: "12px",
                          fontWeight: "600",
                          textTransform: "capitalize",
                          padding: ".2rem ",
                          color: "primary",
                          borderRadius: "100px",
                        }}
                      >
                        Show More
                      </Button>
                    </Box>
                  )}
                </>
              )}
            </div>
          </Stack>
        </Stack>
      </SwipeableDrawer>
      {/* </AnimatedPage> */}
    </>
  );
};

export default AppNotification;
