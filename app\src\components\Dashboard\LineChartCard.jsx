import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  Select,
  MenuItem,
  LinearProgress,
  Collapse,
} from "@mui/material";
import {
  LineChart,
  XAxis,
  YAxis,
  Tooltip,
  Line,
  BarChart,
  Bar,
  ResponsiveContainer, // Import ResponsiveContainer
} from "recharts";
import { DataGrid } from "@mui/x-data-grid";
import { useSelector } from "react-redux";
import { destination_MaterialMgmt } from "../../destinationVariables";
import { doAjax } from "../common/fetchService";
import moment from "moment";

const cardStyle = {
  minHeight: "370px",
  maxHeight: "394px",
  display: "flex",
  flexDirection: "column",
  marginTop: "0px",
  borderRadius: "10px",
  boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2)",
  "&:hover": { boxShadow: "0 8px 16px 0 rgba(0, 0, 0, 0.2)" },
};

const lineChartStyle = {
  display: "flex",
  justifyContent: "space-between",
  alignItems: "baseline",
  marginTop: "10px",
  marginBottom: "15px",
};

const LineChartCard = ({ tempState, settempState }) => {
  const [selectedGraph, setSelectedGraph] = useState([]);
  const [tableContent, setTableContent] = useState([]);
  const [selectedOption, setSelectedOption] = useState("create");
  const [selectedDrop, setSelectedDrop] = useState([""]);
  const [selectedDropOption, setSelectedDropOption] = useState(""); // Initial empty value
  const dashboardSearchForm = useSelector(
    (state) => state.commonFilter["Dashboard"]
  );

  const columns = [
    { field: "requestId", headerName: "Request Id", flex: 1 },
    {
      field: "createdAt",
      headerName: "Created Date",
      flex: 1,
      renderCell: (params) => (
        <Typography fontSize={12}>
          {moment(params.row.createdAt).format("DD MMM YYYY")}
        </Typography>
      ),
    },
    { field: "daysDifference", headerName: "Days", flex: 1 },
  ];

  const handleOptionChange = (event) => {
    setSelectedOption(event.target.value);
    if (selectedDrop.length > 0) {
      setSelectedDropOption(selectedDrop[0]);
    }
  };

  useEffect(() => {
    if (selectedOption) {
      API_Controller.handleSearch2(selectedDropOption); // Pass the initial selected value
    }
    settempState("normal");
  }, [selectedDropOption, tempState, selectedOption]);

  const handleNewDropdownChange = (event) => {
    setSelectedDropOption(event.target.value);

    if (selectedOption && event.target.value) {
      API_Controller.handleSearch2(event.target.value); // Pass the updated selected value
    }
  };

  const API_Controller = {
    handleSearch2: (selectedValue) => {
      let payload = new FormData();
      payload.append("requestType", selectedOption);
      payload.append(
        "fromDate",
        moment(dashboardSearchForm?.dashboardDate[0]).format("YYYY-MM-DD")
      );
      payload.append(
        "toDate",
        moment(dashboardSearchForm?.dashboardDate[1]).format("YYYY-MM-DD")
      );
      const hSuccess = (data) => {
        const sortedData = data?.data.sort(
          (a, b) => a.daysDifference - b.daysDifference
        );
        const top5Rows = sortedData.slice(0, 5);

        // Add unique IDs to the rows
        const uniqueTableContent = top5Rows.map((row, index) => ({
          ...row,
          id: index + 1,
        }));

        setTableContent(uniqueTableContent);
      };
      const hError = () => {};
      doAjax(
        `/${destination_MaterialMgmt}/dashboard/getActivityWithDaysDiffByRequestStatus`,
        "postformdata",
        hSuccess,
        hError,
        payload
      );

      if (selectedOption || selectedValue) {
        // Use the selectedValue
        let payload2 = new FormData();
        payload2.append("requestType", selectedOption);
        payload2.append(
          "fromDate",
          moment(dashboardSearchForm?.dashboardDate[0]).format("YYYY-MM-DD")
        );
        payload2.append(
          "toDate",
          moment(dashboardSearchForm?.dashboardDate[1]).format("YYYY-MM-DD")
        );
        //payload2.append("requestId", selectedValue); // Use selectedValue
        const hSuccess2 = (data) => {
          setSelectedGraph(data?.data);
        };

        const hError2 = () => {};
        doAjax(
          // `/${destination_MaterialMgmt}/dashboard/getCycleTimeResponse`,
          "postformdata",
          hSuccess2,
          hError2,
          payload2
        );
      }
    },
  };

  return (
    <Grid container spacing={2} alignItems="center" marginLeft={"1px"}>
      <Grid item xs={6}>
        <>
          <Box
            style={{ display: "flex", justifyContent: "space-between" }}
            mt={0.5}
          >
            <Box mt={5}>
              <Typography component="div" fontSize={"12px"} fontWeight={"bold"}>
                Top 5 Request Types
              </Typography>
            </Box>

            <Typography fontSize={"12px"} fontWeight={"bold"} mt={3}>
              <Select
                value={selectedOption}
                onChange={handleOptionChange}
                style={{ width: "100px" }}
                size="small"
              >
                <MenuItem value="create">Create</MenuItem>
                <MenuItem value="extend">Extend</MenuItem>
                <MenuItem value="change">Change</MenuItem>
              </Select>
            </Typography>
          </Box>
          <div className="reusable-table">
            <Box
              style={{
                minHeight: "395px", // Set a minimum height for the DataGrid
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
              }}
              mt={1}
            >
              <DataGrid
                sx={{
                  "& .MuiDataGrid-row:hover": {
                    backgroundColor: "#EAE9FF40",
                  },
                  "& .custom-header": {
                    color: "red",
                  },
                  backgroundColor: "#fff",
                }}
                components={{
                  LoadingOverlay: LinearProgress,
                }}
                rows={tableContent}
                columns={columns}
                autoHeight
                rowHeight={60}
                headerClassName="custom-header"
                //disablePagination={true}
                hideFooter={true}
              />
            </Box>
          </div>
        </>
      </Grid>
      <Grid item xs={6}>
        <Box mt={5}>
          <Box style={lineChartStyle}>
            <Typography component="div" fontSize={"12px"} fontWeight={"bold"}>
              Line Chart on the basis of Request ID
            </Typography>
          </Box>
          <Card style={cardStyle}>
            <CardContent>
              <Box mt={1}>
                <Typography fontSize={12} marginLeft={3}>completed requests</Typography>
                <Box mx={1}>
                  <Collapse in={selectedGraph.length > 0} timeout={300}>
                    {/* Use ResponsiveContainer */}
                    <ResponsiveContainer width="100%" height={350}>
                      <BarChart data={selectedGraph} barSize={35}>
                        <XAxis dataKey="daysInterval" tick={{ fontSize: "10px" }} />

                        <YAxis tick={{ fontSize: "10px" }} />

                        <Tooltip />
                        <Bar
                          dataKey="requestIdCount"
                          fill="#59C2E3"
                          radius={[5, 5, 5, 5]}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </Collapse>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Grid>
    </Grid>
  );
};

export default LineChartCard;
