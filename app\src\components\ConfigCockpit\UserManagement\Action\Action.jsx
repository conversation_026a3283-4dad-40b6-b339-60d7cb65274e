import { destination_IWA_NPI } from "../../../../destinationVariables";
import { destination_IWA } from "../../../../destinationVariables";

export const getAllUsers = (fInitial, fSuccess, fError) => {
  const getAllUsersUrl = `/${destination_IWA_NPI}/api/v1/usersMDG/getUsersMDG`;
  const getAllUsersRequestParam = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  };
  fInitial();
  fetch(getAllUsersUrl, getAllUsersRequestParam)
    .then((res) => res.json())
    .then((data) => {
      fSuccess(data);
    })
    .catch((err) => {
      fError(err);
    });
};

export const getAllInactiveUsers = (fInitial, fSuccess, fError) => {
  const getAllInactiveUsersUrl = `/${destination_IWA_NPI}/api/v1/usersMDG/readAllDeactivatedUserMDG`;
  const getAllInactiveUsersRequestParam = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  };
  fInitial();
  fetch(getAllInactiveUsersUrl, getAllInactiveUsersRequestParam)
    .then((res) => res.json())
    .then((data) => {
      fSuccess(data);
    })
    .catch((err) => {
      fError(err);
    });
};

export const getExternalIdpUsers = (fInitial, fSuccess, fError) => {
  fInitial();
  const getIdpUsersUrl = `/${destination_IWA}/api/v1/users/SAP-IAS/getUsers`;
  const getIdpUsersRequestParams = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  };
  fetch(getIdpUsersUrl, getIdpUsersRequestParams)
    .then((res) => res.json())
    .then((data) => {
      fSuccess(data);
    })
    .catch((err) => {
      fError(err);
    });
};

export const getInternalIdpUsers = (fInitial, fSuccess, fError) => {
  fInitial();
  const getIdpUsersUrl = `/${destination_IWA}/api/v1/users/SAP-IAS/getUsers`;
  const getIdpUsersRequestParams = {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  };
  fetch(getIdpUsersUrl, getIdpUsersRequestParams)
    .then((res) => res.json())
    .then((data) => {
      fSuccess(data);
    })
    .catch((err) => {
      fError(err);
    });
};


export const getAllRoles = (fInitial, fSuccess, fError) => {
  fInitial();
  const getAllRolesUrl = `/${destination_IWA_NPI}/api/v1/rolesMDG/getRolesMDG`;
  const getAllRolesRequestParams = {
    headers: {
      "Content-Type": "application/json",
    },
  };
  fetch(getAllRolesUrl, getAllRolesRequestParams)
    .then((res) => res.json())
    .then((data) => {
      fSuccess(data);
    })
    .catch((err) => {
      fError(err);
    });
};

export const getAllApplications = (fInitial, fSuccess, fError) => {
  fInitial();
  const getAllApplicationsUrl = `/${destination_IWA_NPI}/api/v1/applicationsMDG/readApplicationMDG`;
  const getAllApplicationsRequestParams = {
    headers: {
      "Content-Type": "application/json",
    },
  };
  fetch(getAllApplicationsUrl, getAllApplicationsRequestParams)
    .then((res) => res.json())
    .then((data) => {
      fSuccess(data);
    })
    .catch((err) => {
      fError(err);
    });
};

export const getAllEntities = (fInitial, fSuccess, fError) => {
  fInitial();
  const getAllEntitiesUrl = `/${destination_IWA}/api/v1/entities`;
  const getAllEntitiesRequestParams = {
    headers: {
      "Content-Type": "application/json",
    },
  };
  fetch(getAllEntitiesUrl, getAllEntitiesRequestParams)
    .then((res) => res.json())
    .then((data) => {
      fSuccess(data);
    })
    .catch((err) => {
      fError(err);
    });
};

export const getAllActivities = (fInitial, fSuccess, fError) => {
  fInitial();
  const getAllActivitiesUrl = `/${destination_IWA}/api/v1/activities`;
  const getAllActivitiesRequestParams = {
    headers: {
      "Content-Type": "application/json",
    },
  };
  fetch(getAllActivitiesUrl, getAllActivitiesRequestParams)
    .then((res) => res.json())
    .then((data) => {
      fSuccess(data);
    })
    .catch((err) => {
      fError(err);
    });
};

export const getAllGroups = (fInitial, fSuccess, fError) => {
  fInitial();
  const getGroupsUrl = `/${destination_IWA}/api/v1/groups`;
  const getGroupsRequestParam = {
    headers: {
      "Content-Type": "application/json",
    },
  };
  fetch(getGroupsUrl, getGroupsRequestParam)
    .then((res) => res.json())
    .then((data) => {
      fSuccess(data);
    })
    .catch((err) => {
      fError(err);
    });
};

export const getAllRoleTemplates = (fInitial, fSuccess, fError) => {
  fInitial();
  const getGroupsUrl = `/${destination_IWA}/api/v1/roles/roleTemplates`;
  const getGroupsRequestParam = {
    headers: {
      "Content-Type": "application/json",
    },
  };
  fetch(getGroupsUrl, getGroupsRequestParam)
    .then((res) => res.json())
    .then((data) => {
      fSuccess(data);
    })
    .catch((err) => {
      fError(err);
    });
};
