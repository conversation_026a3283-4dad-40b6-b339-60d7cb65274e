import React from 'react';
import { Stepper, Step, StepLabel, StepConnector } from '@mui/material';
import { styled } from '@mui/material/styles';
import { useSelector, useDispatch } from 'react-redux';
import { setActiveStep } from '../../app/redux/stepperSlice';

// Custom connector between steps
const CustomConnector = styled(StepConnector)(({ theme }) => ({
    [`& .MuiStepConnector-line`]: {
      borderColor: theme.palette.grey[300],
      borderTopWidth: 3,
      borderRadius: 1,
    },
  }));

// Custom Step Icon Wrapper
const CustomStepIconRoot = styled('div')(({ theme, ownerState }) => ({
  backgroundColor: ownerState.active || ownerState.completed ? theme.palette.primary.main : theme.palette.grey[400],
  color: '#fff',
  display: 'flex',
  borderRadius: '50%',
  width: 20,
  height: 20,
  justifyContent: 'center',
  alignItems: 'center',
  fontWeight: 'bold',
  fontSize: 14,
}));

function CustomStepIcon(props) {
  const { active, completed, icon } = props;
  return (
    <CustomStepIconRoot ownerState={{ active, completed }}>
      {icon}
    </CustomStepIconRoot>
  );
}

const CommonStepper = ({ steps }) => {
  const dispatch = useDispatch();
  const activeStep = useSelector((state) => state.CommonStepper.activeStep); 

  console.log("activeStep",activeStep)

  const handleStepClick = (stepIndex) => {
    dispatch(setActiveStep(stepIndex));
  };

  return (
    <Stepper
      activeStep={activeStep}
      alternativeLabel
      connector={<CustomConnector />}
      sx={{
        padding: '24px 0',
        backgroundColor: 'transparent',
      }}
    >
      {steps.map((label, index) => (
        <Step key={label} onClick={() => handleStepClick(index)}>
          <StepLabel
            StepIconComponent={CustomStepIcon}
            sx={{
              cursor: 'pointer',
              '& .MuiStepLabel-label': {
                mt: 1,
                color: activeStep === index ? 'primary.main' : 'text.secondary',
                fontWeight: activeStep === index ? 600 : 400,
                fontSize: 14,
              },
            }}
          >
            {label}
          </StepLabel>
        </Step>
      ))}
    </Stepper>
  );
};

export default CommonStepper;
