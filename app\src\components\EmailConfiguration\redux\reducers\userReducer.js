import { createSlice } from "@reduxjs/toolkit";
// All user params
export const userReducer = createSlice({
  name: "userReducer",
  initialState: {
    userToken: {},
    userData: { user_id: "" },
    useWorkAccess: false,
    useConfigServerDestination: false,
    // activityId: "",
    // activityLabel: "",
    oDestinationUrlMap: {},
    aDestinationUrl: [],
    environment: "",
    // runtimeValues: {},
    applicationName: "",
    needHeading: true,
    refreshTemplates: {},
    showManageGroups:true,
    useCrud:true,
    feature:{},
   

    
  },
  reducers: {
    /************************ Application Constant ************************/
    setToken: (state, action) => {
      state.userToken = action.payload;
    },
    setUserData: (state, action) => {
      state.userData = action.payload;
    },
    // setActivity: (state, action) => {
    //   state.activityId = action.payload.activityId;
    //   state.activityLabel = action.payload.activityLabel;
    // },

setfeature:(state,action) =>{
state.feature=action.payload;
},

    setAPIBaseUrl: (state, action) => {
      state.environment = action.payload.environment;
      let destMap = {};
      for (let i = 0; i < action.payload?.destinations.length; i++) {
        destMap[action.payload.destinations[i].Name] =
          action.payload.destinations[i].URL;
      }
      state.oDestinationUrlMap = destMap;
      state.aDestinationUrl = action.payload.destinations;
    },
    setConfigs: (state, action) => {
      state.useWorkAccess = action.payload.useWorkAccess;
      state.useConfigServerDestination =
        action.payload.useConfigServerDestination;
      state.userData = { user_id: action.payload.userId };
      state.applicationName = action.payload.applicationName;
      state.applicationId = action.payload.applicationId;
      state.needHeading=action.payload.needHeading;
      state.showManageGroups=action.payload.needHeading
      state.useCrud=action.payload.useCrud;
    },
    updateBusyLoader: (state, action) => {
      state.showBusyLoader = action.payload;
    },
    setRefreshTemplate: (state, action) => {
      state.refreshTemplates = {};
    }
  },
});
// Action creators are generated for each case reducer function
export const { setToken, setUserData, setAPIBaseUrl, setConfigs, setRefreshTemplate,setfeature } =
  userReducer.actions;
export default userReducer.reducer;
