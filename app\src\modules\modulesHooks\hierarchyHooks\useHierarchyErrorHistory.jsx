import { useMemo } from "react";
import { Chip, Typography } from "@mui/material";

/**
 * Custom hook to generate tabular data from structured input
 * @param {Object} data - The main data object containing a `body` field
 * @param {number} activeTab - The index of the currently selected tab
 * @returns {Object} tabs, currentTabData, tabNames
 */
export const useTabularData = (data, activeTab) => {
  const generateTableData = (sectionData) => {
    if (!sectionData || sectionData.length === 0) {
      return { columns: [], rows: [] };
    }

    const firstItem = sectionData[0];
    const columns = Object.keys(firstItem).map((key) => ({
      field: key,
      headerName: key,
      width: 200,
      flex: 1,
      renderCell: (params) => {
        const value = params.value;
        if (value === "Validated Successfully") {
          return <Chip label={value} color="success" size="small" />;
        } else if (value && value.toString().includes("Error")) {
          return <Chip label={value} color="error" size="small" />;
        }
        return <Typography variant="body2">{value}</Typography>;
      },
    }));

    const maxLength = Math.max(
      ...Object.values(firstItem).map((val) =>
        Array.isArray(val) ? val.length : 0
      )
    );

    const rows = [];
    for (let i = 0; i < maxLength; i++) {
      const row = { id: i };
      Object.keys(firstItem).forEach((key) => {
        const value = firstItem[key];
        if (Array.isArray(value)) {
          row[key] = value[i] || "";
        } else {
          row[key] = i === 0 ? value : "";
        }
      });
      rows.push(row);
    }

    return { columns, rows };
  };

  const tabs = useMemo(() => {
    const bodyData = data?.body || {};
    return Object.keys(bodyData).filter(
      (key) => Array.isArray(bodyData[key]) && bodyData[key].length > 0
    );
  }, [data]);

  const currentTabData = useMemo(() => {
    if (tabs.length === 0 || activeTab >= tabs.length) {
      return { columns: [], rows: [] };
    }

    const tabName = tabs[activeTab];
    const sectionData = data.body[tabName];
    return generateTableData(sectionData);
  }, [activeTab, tabs, data]);

  console.log('check tabs', tabs, data)
  return { tabs, currentTabData };
};


