import { useState } from "react";
import { destination_MaterialMgmt } from "../../../destinationVariables";
import { doAjax } from "@components/Common/fetchService";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { useSelector } from "react-redux";
import ExtendSearchFilter from "@components/Common/ExtendSearchFilter";
import { END_POINTS } from "@constant/apiEndPoints";
import { MATERIAL_TYPE_DRODOWN } from "@constant/enum";
import { API_CODE, EXCLUDED_VIEWS } from "@constant/enum";

const ExtendMaterialSearch = ({openSearchMat, setOpenSearchMat, AddCopiedMaterial}) => {
    const [blurLoading, setBlurLoading] = useState(false);
    const dropDownDataFromRedux = useSelector((state) => state.AllDropDown.dropDown);
    const templates = {
        "Extend": [
          {
            key: 'Material Type',
            options: MATERIAL_TYPE_DRODOWN,
          },
          {
            key: 'Material Number',
            options: [],
          },
          {   
            key: 'Plant',
            options: [],
          },
          {
            key: 'Sales Org',
            options: [],
          },
          {
            key: 'Distribution Channel',
            options: [],
          },
          {
            key: 'Storage Location',
            options: [],
          },
          {
            key: 'Division',
            options: [],
          }
        ],
    }
    
    const searchMaterial = (searchParams, skip = "0", callback) => {
        const payload = {
            "materialNo": searchParams?.["Material Number"]?.map(item => item.code)?.join(",") ?? "",
            "division": searchParams?.["Division"]?.map(item => item.code)?.join(",") ?? "",
            "plant": searchParams?.["Plant"]?.map(item => item.code)?.join(",") ?? "",
            "salesOrg": searchParams?.["Sales Org"]?.map(item => item.code)?.join(",") ?? "",
            "distrChan": searchParams?.["Distribution Channel"]?.map(item => item.code)?.join(",") ?? "",
            "storageLocation": searchParams?.["Storage Location"]?.map(item => item.code)?.join(",") ?? "",
            "top": 200,
            "skip": skip 
        };

        const hSuccess = (data) => {
          if(data?.statusCode === API_CODE.STATUS_200){
            const processedData = data?.body?.map(item => {
                if (item.Views) {
                    const filteredViews = item.Views
                        .split(',')
                        .map(view => view.trim())
                        .filter(view => !EXCLUDED_VIEWS.includes(view))
                        .join(',');
                    return {
                        ...item,
                        Views: filteredViews
                    };
                }
                return item;
            });
            
            AddCopiedMaterial(processedData || []);
            callback?.(processedData || []);
            setBlurLoading(false);
          }
        };
    
        const hError = () => {
            setBlurLoading(false);
            callback?.([]);
        };

        setBlurLoading(true);
        doAjax(`/${destination_MaterialMgmt}${END_POINTS.DATA.GET_EXTEND_SEARCH_SET}`, "post", hSuccess, hError, payload);
    }

    return(
        <>
        <ExtendSearchFilter
            open={openSearchMat}
            onClose={() => setOpenSearchMat(false)}
            parameters={templates["Extend"]}
            onSearch={(params, skip, callback) => searchMaterial(params, skip, callback)}
            templateName={"Extend"}
            name="Extend"
            allDropDownData={dropDownDataFromRedux}
            buttonName={"Search"}
          />
        <ReusableBackDrop blurLoading={blurLoading}  />
        </>
    )
}

export default ExtendMaterialSearch;
