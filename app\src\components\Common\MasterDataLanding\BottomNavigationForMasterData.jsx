import React, { useState, useRef } from "react";
import { Paper, BottomNavigation, Button, Dialog, DialogTitle, DialogContent, DialogActions, Grid, Typography, IconButton, Popper, MenuList, MenuItem, ClickAwayListener } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import useLang from "@hooks/useLang";
import { setDisplayPayload } from "@slice/payloadSlice";
import { setRequestHeader } from "@slice/requestDataSlice";

const BottomNavigationForMasterData = ({
  buttonConfigs = [],
  showDeleteDialog = true,
  deleteDialogContent = null,
  onDeleteProceed = () => {},
  enableExportSearch = false,
  exportSearchComponent = null,
}) => {
  const { t } = useLang();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [value, setValue] = useState(0);
  const [openforDeletion, setOpenForDeletion] = useState(false);
  const [openExportSearch, setOpenExportSearch] = useState(false);
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);

  const [openButton, setOpenButton] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const anchorRef = useRef(null);
  const options = ["Main", "Option 1", "Option 2"];

  const [openButtonChange, setOpenButtonChange] = useState(false);
  const [selectedIndexChange, setSelectedIndexChange] = useState(0);
  const anchorRefChange = useRef(null);
  const optionsChange = ["Change", "Change A", "Change B"];

  const handleDialogCloseforDeletion = () => setOpenForDeletion(false);

  return (
    <Paper sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }} elevation={2}>
      <BottomNavigation showLabels sx={{ display: "flex", justifyContent: "flex-end", alignItems:"center",gap: 1 }} value={value} onChange={(event, newValue) => setValue(newValue)}>
        {buttonConfigs
          .filter((btn) => btn.condition)
          .map((btn) => (
            <Button key={btn.key} size="small" variant="contained" onClick={btn.onClick} sx={{mr:1}}>
              {btn.label}
            </Button>
          ))}

        {/* 🔽 Dialog for Deletion */}
        <Dialog open={openforDeletion} onClose={handleDialogCloseforDeletion}>
          <DialogTitle>
            <Typography variant="h6">{t("Inputs")}</Typography>
            <IconButton onClick={handleDialogCloseforDeletion}>
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent dividers>
            <Grid container spacing={1}>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleDialogCloseforDeletion}>{t("Cancel")}</Button>
            <Button variant="contained">{t("Proceed")}</Button>
          </DialogActions>
        </Dialog>

        {/* 🔽 Popper Menus */}
        <Popper open={openButton} anchorEl={anchorRef.current} placement="top-end" sx={{ zIndex: 1 }}>
          <Paper style={{ width: anchorRef.current?.clientWidth }}>
            <ClickAwayListener onClickAway={() => setOpenButton(false)}>
              <MenuList autoFocusItem>
                {options.slice(1).map((option, index) => (
                  <MenuItem
                    key={option}
                    selected={index === selectedIndex - 1}
                    onClick={() => {
                      setSelectedIndex(index + 1);
                      setOpenButton(false);
                    }}
                  >
                    {option}
                  </MenuItem>
                ))}
              </MenuList>
            </ClickAwayListener>
          </Paper>
        </Popper>

        <Popper open={openButtonChange} anchorEl={anchorRefChange.current} placement="top-end" sx={{ zIndex: 1 }}>
          <Paper style={{ width: anchorRefChange.current?.clientWidth }}>
            <ClickAwayListener onClickAway={() => setOpenButtonChange(false)}>
              <MenuList autoFocusItem>
                {optionsChange.slice(1).map((option, index) => (
                  <MenuItem
                    key={option}
                    selected={index === selectedIndexChange - 1}
                    onClick={() => {
                      setSelectedIndexChange(index + 1);
                      setOpenButtonChange(false);
                    }}
                  >
                    {option}
                  </MenuItem>
                ))}
              </MenuList>
            </ClickAwayListener>
          </Paper>
        </Popper>
      </BottomNavigation>
    </Paper>
  );
};

export default BottomNavigationForMasterData;
