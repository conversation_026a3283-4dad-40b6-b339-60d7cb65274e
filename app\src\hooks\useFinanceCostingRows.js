import { useSelector } from "react-redux";

const useFinanceCostingRows = () => {
  const unSelectedRows = useSelector((state) => state.payload.unselectedRows || []);
  const createFCRows = (apiResponse) => {
    const flattened = apiResponse?.flatMap((item) => {
      return item.Toplantdata.map((plantData) => ({
        ...plantData,
        FinanceCostingId: item.FinanceCostingId,
        MassSchedulingId: item.MassSchedulingId,
        RequestType: item.RequestType,
        RequestId: item.RequestId,
        Requester: item.Requester,
        CreatedOn: item.CreatedOn,
        Material: item.Material,
        MatlType: item.MatlType,
        IntlPoPrice: item.IntlPoPrice,
        PryVendor: item.PryVendor,
        FlagForBOM: item.FlagForBOM,
        VolInEA: item.VolInEA,
        VolInCA: item.VolInCA,
        VolInCAR: item.VolInCAR,
        NoOfUnitForCA: item.NoOfUnitForCA,
        NoOfUnitForCT: item.NoOfUnitForCT,
        Torequestheaderdata: item.Torequestheaderdata,
        Tomaterialerrordata: item.Tomaterialerrordata,
        id: plantData?.FinancePlantId
      }));
    });

    return flattened;
  };

  const createFCPayload = () => {
    const flattenedRows = unSelectedRows;
    const reconstructedMap = new Map();
    flattenedRows.forEach(row => {
        const {
            FinancePlantId,
            Material,
            IsDeleted,
            Plant,
            FPurStatus,
            FStdPrice,
            id,
            ...rest
        } = row;

        const plantObj = {
            FinancePlantId,
            Material,
            IsDeleted: true,
            Plant,
            FPurStatus,
            FStdPrice,
        };

        if (!reconstructedMap.has(row.FinanceCostingId)) {
            reconstructedMap.set(row.FinanceCostingId, {
                ...rest, 
                Material: row?.Material,
                Toplantdata: [plantObj],
            });
        } else {
            reconstructedMap.get(row.FinanceCostingId).Toplantdata.push(plantObj);
        }
    });

    const reconstructedArray = Array.from(reconstructedMap.values());
    return reconstructedArray;

  }
    
    return { createFCRows, createFCPayload };
}

export default useFinanceCostingRows