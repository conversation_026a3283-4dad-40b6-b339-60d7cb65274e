import React, { useState } from "react";
import { Box, Grid, CircularProgress, Typography } from "@mui/material";
import GraphCards from "./GraphCards";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";

const AllCharts = ({ cards = [], loading }) => {
  const [items, setItems] = useState(cards);

  React.useEffect(() => {
    setItems(cards);
  }, [cards]);

  const onDragEnd = (result) => {
    if (!result.destination) return;
    
    const newItems = Array.from(items);
    const [reorderedItem] = newItems.splice(result.source.index, 1);
    newItems.splice(result.destination.index, 0, reorderedItem);
    
    setItems(newItems);
  };

  if (loading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100%" }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!items.length) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100%" }}>
        <Typography variant="h6" color="text.secondary">
          No charts available. Please configure your dashboard.
        </Typography>
      </Box>
    );
  }

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId="charts" direction="horizontal">
        {(provided) => (
          <Grid
            container
            spacing={2}
            {...provided.droppableProps}
            ref={provided.innerRef}
          >
            {items.map((card, index) => (
              <Draggable key={card.id} draggableId={card.id.toString()} index={index}>
                {(provided) => (
                  <Grid
                    item
                    xs={12}
                    md={6}
                    lg={4}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                  >
                    <GraphCards
                      title={card.graphDetails?.graphName}
                      data={card}
                      dragHandleProps={provided.dragHandleProps}
                    />
                  </Grid>
                )}
              </Draggable>
            ))}
            {provided.placeholder}
          </Grid>
        )}
      </Droppable>
    </DragDropContext>
  );
};

export default AllCharts;
