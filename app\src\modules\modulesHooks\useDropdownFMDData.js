import useFMDFetchDropdownAndDispatch from "./useFMDFetchDropdownAndDispatch";
import lookup from "@data/lookup.json";

const useDropdownFMDData = (destination,setDropDown) => {
  const { fetchDataAndDispatch } = useFMDFetchDropdownAndDispatch(destination,setDropDown);
  const fetchAllDropdownFMD = (lookupModuleName) => {
    lookup?.[lookupModuleName]?.forEach(({ keyName, endPoint }) => {
      fetchDataAndDispatch(keyName,endPoint);
    });
  }
  return { fetchAllDropdownFMD };
};

export default useDropdownFMDData;
