import React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import SearchIcon from "@mui/icons-material/Search";
import "./ValueHelpDialog.css";
import DialogContentText from "@mui/material/DialogContentText";
import { TextField, InputAdornment } from "@mui/material";
import DialogTitle from "@mui/material/DialogTitle";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import CloseIcon from "@mui/icons-material/Close";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { IconButton } from "@mui/material";
import DataElementDetails from "./CwMSDataElementDetails";
require("./ValueHelpDialog.css");

class valueHelpDialog extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      value: "DataElement",
      tempDataEle: this.props.dataElement,
      open: false,
      isCreated: false,
    };
  }

  onChangeSearchQuery = (evt, val) => {
    if (val === "")
      this.setState({
        ...this.state,
        tempDataEle: this.props.dataElement,
      });
    else {
      let templist = this.props.dataElement.filter((app) => app.name.toUpperCase().includes(evt.target?.value?.toUpperCase()) || app?.dataType?.toUpperCase().includes(evt?.target?.value?.toUpperCase()));
      this.setState({
        ...this.state,
        tempDataEle: templist,
      });
    }
  };
  handleChange = (event, newValue) => {
    this.setState({
      ...this.state,
      value: newValue,
    });
  };

  handleCreate = (e) =>{
    this.onChangeSearchQuery(e, "");
    this.setState({ ...this.state, isCreated: true });
    // onClick={() => this.setState({ ...this.state, isCreated: true })}
  }

  render() {
    return (
      <Dialog fullScreen={false} open={this.props.dataElement && this.props.open} className="ValueHelpDialog" fullWidth maxWidth="md" aria-labelledby="simple-modal-title" aria-describedby="simple-modal-description">
        <DialogTitle sx={{ padding: "0.5rem 1.5rem 0 0.5rem" }}>
          <div
            style={{
              display: "flex",

              paddingLeft: "1rem",

              justifyContent: "space-between",

              alignItems: "center",

              height: "2.5rem",
            }}
          >
            <p>{this.state.isCreated ? "Create Data Element" : "Data Element"}</p>
            <IconButton
              aria-label="close"
              size="small"
              onClick={(evt) => {
                this.onChangeSearchQuery(evt, "");
                this.props.onClose(evt, "CANCEL");
              }}
            >
              <CloseIcon />
            </IconButton>
          </div>
        </DialogTitle>
        <DialogContent sx={{ width: "56rem", background: "#F1F5FE" }}>
          <DialogContentText sx={{ paddingTop : '1rem'}}>
            {!this.state.isCreated ? (
              <div style={{ width: "53rem", height: "100%" }}>
                <div className="Textfield" style={{ display: "flex", marginBottom: "0.5rem", justifyContent: "space-between" }}>
                  <TextField
                    variant="outlined"
                    style={{
                      marginbotton: "0.5rem",
                      textAlign: "center",

                      width: "10rem",
                      background: "#ffffff",
                    }}
                    sx={{ "& .MuiOutlinedInput-root": { fontSize: "14px", padding: "0" }, ".MuiOutlinedInput-input": { padding: "0.5rem" } }}
                    size="small"
                    placeholder="Search"
                    className="styleSearch"
                    onChange={(evt) => this.onChangeSearchQuery(evt)}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton aria-label="toggle password visibility">
                            <SearchIcon />
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />

                  {this.props.title !== "Value Help Table " && (
                    <Button variant="outlined" onClick={e => this.handleCreate(e)} style={{ borderColor: "#007AD4", height: "2rem", fontWeight: "500", color: "#007AD4", textTransform: "none" }}>
                      Create
                    </Button>
                  )}
                </div>
                {this.props.title !== "Value Help Table " ? (
                  <TableContainer component={Paper} sx={{ height: "16rem" }}>
                    <Table size="small" stickyHeader>
                      <TableHead>
                        <TableRow sx={{ background: "#F1F5FE", width: "25rem" }}>
                          <TableCell width="10%" style={{ fontWeight: 700 }}>
                            Name
                          </TableCell>
                          <TableCell width="10%" style={{ fontWeight: 700 }}>
                            Data Type
                          </TableCell>
                          <TableCell width="10%" style={{ fontWeight: 700 }}>
                            Is Value help Needed?
                          </TableCell>
                          <TableCell width="10%" style={{ fontWeight: 700 }}>
                            Length
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      {this.state.tempDataEle?.length !== 0 ? (
                        <TableBody>
                          {this.state.tempDataEle?.map((row, index) => {
                            return (
                              <TableRow key={index} onClick={() => this.props.rowSelected(row)}>
                                <TableCell>{row.name}</TableCell>
                                <TableCell>{row.dataType}</TableCell>
                                <TableCell>{row.isLookup ? "yes" : "no"}</TableCell>
                                <TableCell>{row.length}</TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      ) : (
                        <TableBody>
                          {this.props.DataElement?.map((row, index) => {
                            return (
                              <TableRow key={index} onClick={() => this.props.rowSelected(row)}>
                                <TableCell>{row.name}</TableCell>
                                <TableCell>{row.dataType}</TableCell>
                                <TableCell>{row.isLookup ? "yes" : "no"}</TableCell>
                                <TableCell>{row.length}</TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      )}
                    </Table>
                  </TableContainer>
                ) : (
                  <TableContainer component={Paper} sx={{ ".&.MuiDialog-paperWidthSm": { maxWidth: "900px" } }} style={{ width: "99%", height: "16rem" }}>
                    <Table size="small" aria-label="a dense table">
                      <TableHead>
                        <TableRow style={{ background: "#F1F5FE", width: "33rem" }}>
                          <TableCell width="10%" style={{ fontWeight: 700 }}>
                            Name
                          </TableCell>
                          <TableCell width="10%" style={{ fontWeight: 700 }}>
                            Description
                          </TableCell>
                          <TableCell width="10%" style={{ fontWeight: 700 }}>
                            Label
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      {this.props.tempDataEle?.length !== 0 ? (
                        <TableBody>
                          {this.props.tempDataEle?.map((row, index) => {
                            return (
                              <TableRow key={index} onClick={() => this.props.rowSelected(row)}>
                                <TableCell>{row.name}</TableCell>
                                <TableCell>{row.description}</TableCell>

                                <TableCell>{row.label}</TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      ) : (
                        <TableBody>
                          {this.props.DataElement?.map((row, index) => {
                            return (
                              <TableRow key={index} onClick={() => this.props.rowSelected(row)}>
                                <TableCell>{row.name}</TableCell>
                                <TableCell>{row.description}</TableCell>

                                <TableCell>{row.label}</TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      )}
                    </Table>
                  </TableContainer>
                )}
              </div>
            ) : (
              <DataElementDetails
                {...this.props}
                destinations={this.props.destinations}
                onClose={() => {
                  this.setState({ ...this.state, isCreated: false });
                  this.props.onValueHelp();
                }}
              />
            )}
          </DialogContentText>
        </DialogContent>
      </Dialog>
    );
  }
}

export default valueHelpDialog;
