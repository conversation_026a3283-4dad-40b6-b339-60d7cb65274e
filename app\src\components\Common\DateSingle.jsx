import { DatePicker } from "rsuite";
import "rsuite/dist/rsuite.css";
import { Typography } from "@mui/material";
import { font_Small } from "./commonStyles";

function DateSingle(props) {
  const defaultDate = props.date ? new Date(props.date) : new Date();
  return (
    <div style={{ position: "relative" }}>
      <Typography sx={font_Small}>{props?.filterName}</Typography>
      <DatePicker
        className="dates"
        size="small"
        disabled={props?.disabled}
        placeholder="Select Date"
        value={defaultDate}
        onChange={props.handleDate} // Handle single date selection
        format="dd MMM yyyy"
        placement="auto"
        style={{
          ...font_Small,
          zIndex: "1444",
          height: "2rem",
        }}
      />
    </div>
  );
}

export default DateSingle;
