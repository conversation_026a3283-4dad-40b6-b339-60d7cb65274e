import React, { useState, useEffect } from "react";
import moment from "moment/moment";
import {
  Paper,
  IconButton,
  Select,
  MenuItem,
  Tooltip,
  Button,
  Dialog,
  Box,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Tabs,
  Tab,
  TextField,
  Checkbox,
  Typography,
  FormControl,
  Switch,
  Stepper,
  Step,
  StepLabel,
  BottomNavigation,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import CloseIcon from "@mui/icons-material/Close";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import {
  Add,
  Refresh,
  GetApp,
  Publish,
  CheckBoxOutlineBlank,
  CheckBox,
} from "@mui/icons-material";
import { MdResetTv } from "react-icons/md";
import { useDispatch, useSelector } from "react-redux";
import NotFound from "../NotFound";
import UserDetail from "./UserDetail";
import { getAllUsers, getAllInactiveUsers } from "../../Action/action";
import {
  setUsers,
  setResponseMessage,
  setExternalIdpUsers,
  setInternalIdpUsers,
} from "../../../../../app/userManagementSlice";
import { downloadFile, userFileHeading } from "../../Utility/file";
import UploadFile from "../UploadFile";
import { Autocomplete } from "@mui/material";
import { appHeaderHeight, userPageHeaderHeight } from "../../Data/cssConstant";
import { TabPanel, a11yProps } from "../TabPanel";
import ReusableTable from "../../../../common/ReusableTable";
import {
  iconButton_SpacingSmall,
  font_Small,
} from "../../../../common/commonStyles";
import SearchBar from "../../../../common/SearchBar";
import {
  destination_IWA,
  destination_IWA_NPI,
  // destination_IWA_SCP,
  destination_Po,
} from "../../../../../destinationVariables";
import { v4 as uuidv4 } from "uuid";
import ReusablePromptBox from "../../../../common/ReusablePromptBox/ReusablePromptBox";
import { doAjax } from "../../../../common/fetchService";
import { checkIwaAccess } from "../../../../../functions";

const useStyle = makeStyles((theme) => ({
  idpUsersDialogTitle: {
    height: "3rem",
    display: "flex",
    alignItems: "center",
    borderBottom: "1px solid #d9d9d9",
    margin: 0,
    padding: 0,
    paddingLeft: 10,
  },
  idpUsersDialogContentSearchContainer: {
    marginBottom: 8,
    width: 200,
    position: "sticky",
    top: 0,
    zIndex: 999,
    backgroundColor: theme.palette.background.paper,
    color: theme.palette.text.primary,
  },
  idpUsersTableContainer: {
    height: "47vh",
    width: "100%",
  },
  idpUsersTableHead: {
    backgroundColor: theme.palette.text.primary,
    position: "sticky",
    top: 0,
    zIndex: 99,
  },
  idpUsersTableHeadCell: {
    fontWeight: 700,
    whiteSpace: "nowrap",
    color: theme.palette.background.paper,
    fontSize: 14,
  },
  idpUsersTableBody: {
    height: "100%",
  },
  idpUsersTableBodyRow: {
    "&:hover": {
      backgroundColor: theme.palette.action.hover,
    },
  },
  idpUsersTableBodyRowSelected: {
    backgroundColor: theme.palette.action.selected,
  },
  idpUsersTableBodyCell: {
    whiteSpace: "nowrap",
    fontSize: 12,
  },
  idpUsersTableBodyTextHide: {
    maxWidth: 180,
    overflow: "hidden",
    textOverflow: "ellipsis",
  },
  idpUsersDialogActions: {
    height: "3rem",
    borderTop: "1px solid #d9d9d9",
  },
  usersHeaderContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    paddingBottom: 10,
    position: "sticky",
    top: 0,
    zIndex: 99,
    height: userPageHeaderHeight,
    backgroundColor: theme.palette.background.paper,
  },
  userHeadeTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: theme.palette.text.primary,
  },
  usersHeaderAddButton: {
    marginLeft: 10,
    textTransform: "capitalize",
  },
  usersTableContainer: {
    height: "100%",
    width: "100%",
  },
  usersTableHead: {
    backgroundColor: theme.palette.text.primary,
    position: "sticky",
    top: 0,
    zIndex: 99,
  },
  usersTableHeadCell: {
    fontWeight: 700,
    whiteSpace: "nowrap",
    color: theme.palette.background.paper,
    fontSize: 14,
  },
  usersTableBody: {
    height: "100%",
  },
  usersTableBodyRow: {
    cursor: "pointer",
    "&:hover": {
      backgroundColor: theme.palette.action.hover,
    },
  },
  usersTableBodyRowSelected: {
    backgroundColor: theme.palette.action.selected,
  },
  usersTableBodyCell: {
    whiteSpace: "nowrap",
    fontSize: 12,
  },
  usersTableBodyTextHide: {
    overflow: "hidden",
    maxWidth: 180,
    textOverflow: "ellipsis",
  },
}));

const IdpUsers = ({ open, onClose, handleRefresh, handleOpenPromptBox }) => {
  const presentDate = new Date();
  const userReducerState = useSelector((state) => state.userReducer);
  const basicReducerState = useSelector((state) => state.userManagement);
  let userData = useSelector((state) => state.userManagement.userData);
  const [load, setLoad] = useState(false);
  const [extIdpUsers, setExtIdpUsers] = useState([]);
  const [externalFilteredIdpUsers, setExternalFilteredIdpUsers] = useState([]);
  const [intIdpUsers, setIntIdpUsers] = useState([]);
  const [internalFilteredIdpUsers, setInternalFilteredIdpUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState([]);
  const [tabName, setTabName] = useState(0);
  const initialUserState = {
    firstName: "",
    lastName: "",
    emailId: "",
  };
  const [newValue, setNewValue] = useState(initialUserState);
  const dispatch = useDispatch();
  const [error, setError] = useState(false);
  const [formError, setFormError] = useState({
    firstNameError: false,
    lastNameError: false,
    emailError: false,
  });
  const [errorMessage, setErrorMessage] = useState({
    firstNameValidation: "",
    lastNameValidation: "",
    emailIdValidation: "",
  });

  const clearIDPUsersSearchBar = () => {
    setExternalFilteredIdpUsers([]);
    setInternalFilteredIdpUsers([]);
  };
  const formcontroller_SearchBar = useSelector(
    (state) => state.commonSearchBar["IDPUsers"]
  );
  const searchExternalIdpUsers = (e) => {
    setExternalFilteredIdpUsers(
      extIdpUsers?.filter(
        (user) =>
          user?.pid?.toLowerCase()?.includes(e.target.value.toLowerCase()) ||
          user?.userName
            ?.toLowerCase()
            ?.includes(e.target.value.toLowerCase()) ||
          user?.name?.toLowerCase()?.includes(e.target.value.toLowerCase()) ||
          user?.firstName
            ?.toLowerCase()
            ?.includes(e.target.value.toLowerCase()) ||
          user?.lastName
            ?.toLowerCase()
            ?.includes(e.target.value.toLowerCase()) ||
          user?.userEmail?.toLowerCase()?.includes(e.target.value.toLowerCase())
      ) || []
    );
  };
  const searchInternalIdpUsers = (e) => {
    setInternalFilteredIdpUsers(
      intIdpUsers?.filter(
        (user) =>
          user?.pid?.toLowerCase()?.includes(e.target.value.toLowerCase()) ||
          user?.userName
            ?.toLowerCase()
            ?.includes(e.target.value.toLowerCase()) ||
          user?.name?.toLowerCase()?.includes(e.target.value.toLowerCase()) ||
          user?.firstName
            ?.toLowerCase()
            ?.includes(e.target.value.toLowerCase()) ||
          user?.lastName
            ?.toLowerCase()
            ?.includes(e.target.value.toLowerCase()) ||
          user?.userEmail?.toLowerCase()?.includes(e.target.value.toLowerCase())
      ) || []
    );
  };
  const insertNewUser = () => {
    setLoad(true);
    const insertUserUrl = `/${destination_IWA_NPI}/api/v1/usersMDG/insertMultipleIdpUsersMDG`;
    const payload =
      {
        usersDetailsList: selectedUser?.map((user) => ({
          companyCode: roleDetails?.companyCode,
          emailId: user?.userEmail,
          entityId: "",
          firstName: user?.firstName,
          lastName: user?.lastName,
          name: user?.name,
          platformId: "",
          rolesDetailsList: [
            {
              createdBy: userData?.emailId,
              createdOn: moment(new Date(presentDate).getTime()).format(
                "YYYY-MM-DD HH:mm:ss.*********"
              ),
              groupRole: "",
              isActive: 1,
              isDeleted: 0,
              isGroupRole: 0,
              roleId: parseInt((roleDetails?.assignedRole).split(" - ")[1]),
              status: "Active",
              updatedBy: "",
              updatedOn: "",
              userEmail: "",
            },
          ],
          purchasingGroup: roleDetails?.purchasingGroup
            ? `${roleDetails?.purchasingGroup
              ?.split(" - ")?.splice(-1)?.toString()} - ${roleDetails?.purchasingGroup
              ?.split(" - ")?.slice(0,-1)
              ?.toString()}`
            : ``,
          supplierId: roleDetails?.supplier
            ? `${roleDetails?.supplier
                ?.split(" - ")?.splice(-1)?.toString()} - ${roleDetails?.supplier
                ?.split(" - ")?.slice(0,-1)
                ?.toString()}`
            : ``,
          userId: user.pid,
          userName: user?.userName || user?.userEmail,
        })),
      } || [];
    const insertUserRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    };
    fetch(insertUserUrl, insertUserRequestParam)
      .then((res) => res.json())
      .then((user_data) => {
        setSelectedUser([]);
        setExternalFilteredIdpUsers(basicReducerState.externalIdpUsers);
        setInternalFilteredIdpUsers(basicReducerState.internalIdpUsers);
        setNewValue(initialUserState);

        handleOpenPromptBox("SUCCESS", {
          message: `User Added Successfully`,
          redirectOnClose: false,
        });
        onClose();
        setLoad(false);
        handleRefresh();
      })
      .catch((err) => {
        onClose();
        setSelectedUser([]);
        setLoad(false);
        // setLoad(false);
      });
  };

  const isPresent = (idpUsers) => {
    const i_User = new Set(
      basicReducerState?.users?.map((user) => user["emailId"])
    );
    return idpUsers?.filter((user) => !i_User?.has(user["userEmail"]));
  };

  useEffect(() => {
    setExtIdpUsers(basicReducerState.externalIdpUsers);
    setExternalFilteredIdpUsers(basicReducerState.externalIdpUsers);
  }, [basicReducerState.externalIdpUsers]);

  useEffect(() => {
    setIntIdpUsers(basicReducerState.internalIdpUsers);
    setInternalFilteredIdpUsers(basicReducerState.internalIdpUsers);
  }, [basicReducerState.internalIdpUsers]);

  let [roleDetails, setRoleDetails] = useState({
    assignedRole: "",
    companyCode: "",
    supplier: "",
    purchasingGroup: "",
  });
  const [activeStep, setActiveStep] = useState(0);

  let masterData = useSelector((state) => state.masterData);
  const [moduleMasterFilterData, setModuleMasterFilterData] = useState({
    companyCodeList: [],
    vendorList: [],
    purchGrpList: [],
  });
  const [moduleMasterFilterDataLoading, setModuleMasterFilterLoadingData] =
    useState({
      companyCodeList: false,
      vendorList: false,
      purchGrpList: false,
    });

  let setDefaultDropdownData = (fieldName) => {
    if (fieldName === "purchGrp")
      setModuleMasterFilterData((prev) => ({
        ...prev,
        purchGrpList: masterData?.purchasingGroups,
      }));
    if (fieldName === "vendorNo")
      setModuleMasterFilterData((prev) => ({
        ...prev,
        vendorList: masterData?.vendorCode,
      }));
    if (fieldName === "companyCode")
      setModuleMasterFilterData((prev) => ({
        ...prev,
        companyCodeList: masterData?.companyCode,
      }));
  };
  useEffect(() => {
    setModuleMasterFilterData({
      companyCodeList: masterData?.companyCode,
      vendorList: masterData?.vendorCode,
      purchGrpList: masterData?.purchasingGroups,
    });
  }, []);
  const fetchData = (name, newValue) => {
    setModuleMasterFilterLoadingData((prevData) => ({
      ...prevData,
      companyCodeList: true,
      vendorList: true,
      purchGrpList: true,
    }));
    var url = "";
    if (name === "purchGrp")
      url = `/${destination_Po}/Odata/getSearchDetails?name={##}&type=PurchasingGroup`;
    if (name === "vendorNo")
      url = `/${destination_Po}/Odata/getSearchDetails?name={##}&type=vendor`;
    if (name === "companyCode")
      url = `/${destination_Po}/Odata/getSearchDetails?name={##}&type=company`;

    if (newValue) {
      const hSuccess = (data) => {
        var formatLocalOptions = Object.keys(data.data).map(
          (id) => `${data.data[id]} - ${id}`
        );
        if (name === "purchGrp") {
          setModuleMasterFilterData((prevData) => ({
            ...prevData,
            purchGrpList: formatLocalOptions,
          }));
          setModuleMasterFilterLoadingData((prevData) => ({
            ...prevData,
            purchGrpList: false,
          }));
        }
        if (name === "vendorNo") {
          setModuleMasterFilterData((prevData) => ({
            ...prevData,
            vendorList: formatLocalOptions,
          }));

          setModuleMasterFilterLoadingData((prevData) => ({
            ...prevData,
            vendorList: false,
          }));
        }
        if (name === "companyCode") {
          setModuleMasterFilterData((prevData) => ({
            ...prevData,
            companyCodeList: formatLocalOptions,
          }));

          setModuleMasterFilterLoadingData((prevData) => ({
            ...prevData,
            companyCodeList: false,
          }));
        }
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `${url}`.replace("{##}", newValue?.split(" - ")[0]),
        "get",
        hSuccess,
        hError
      );
    }
  };
  let timeOutId = React.useRef(null);

  const onChangeFilter = (name, value) => {
    if (timeOutId) clearTimeout(timeOutId.current);
    if (value) {
      timeOutId.current = setTimeout(() => {
        fetchData(name, value);
      }, 1000);
    } else {
      setDefaultDropdownData(name);
    }
  };

  const handleFormChange = (e) => {
    // let key = e.target.name;
    let tempIF = { ...roleDetails, [e.target.name]: e.target.value };
    setRoleDetails(tempIF);
  };

  const onRowsSelectionHandler = (ids) => {
    const selectedRowsData = ids.map((id) =>
      externalFilteredIdpUsers.find((row) => row.pid === id)
    );
    setSelectedUser(selectedRowsData);
  };

  const onRowsSelectionHandlerIntUsers = (ids) => {
    const selectedRowsData = ids.map((id) =>
      internalFilteredIdpUsers.find((row) => row.pid === id)
    );
    setSelectedUser(selectedRowsData);
  };

  // useEffect(() => {
  //   if (masterData?.companyCode && Object.keys(masterData?.companyCode).length < 2) {
  //     setRoleDetails((prev)=>({
  //       ...prev,
  //       assignedRole: "",
  //       companyCode: `${Object.keys(masterData?.companyCode)[0]} - ${Object.values(masterData?.companyCode)[0]}`,
  //       supplier: "",
  //       purchasingGroup:''
  //     }))
  //   }

  // }, [activeStep]);

  function getSteps() {
    return ["Select Users", "Select Details"];
  }

  const idpUsersColumns = [
    {
      field: "pid",
      headerName: "User ID",
      flex: 1,
    },
    {
      field: "name",
      headerName: "Name",
      flex: 1,
      renderCell: (data) => {
        return (
          <Typography variant="body2">
            {`${data.row?.firstName} ${data.row?.lastName}`}
          </Typography>
        );
      },
    },
    {
      field: "userEmail",
      headerName: "Email",
      flex: 1,
    },
  ];

  function getStepContent(step) {
    let internalUserRoleAccess = {
      "procurement lead": true,
      "buyer  ": true,
      "it admin": true,
      "super user": true,
    };
    let externalUserRoleAccess = {
      "supplier admin": true,
      "test role/supplier": true,
      supplier: true,
    };
    let assignedRole = roleDetails?.assignedRole?.toLowerCase().split(" - ")[0];

    switch (step) {
      case 0:
        return (
          <>
            {tabName === 1 && (
              <div>
                <div style={{ margin: "1rem 0" }}>
                  <SearchBar
                    title="Search IDP Users"
                    onChange={searchExternalIdpUsers}
                    elasticSearch={true}
                    message={"Search IDP Users"}
                    module="IDPUsers"
                    keyName="name"
                  />
                </div>
                {externalFilteredIdpUsers?.length === 0 ? (
                  <NotFound />
                ) : (
                  <>
                    {
                      <ReusableTable
                        module={"user management"}
                        width="100%"
                        // isLoading={isLoading}
                        rows={isPresent(externalFilteredIdpUsers)}
                        columns={idpUsersColumns}
                        hideFooter={false}
                        getRowIdValue={"pid"}
                        disableSelectionOnClick={true}
                        stopPropagation_Column={"action"}
                        status_onRowSingleClick={true}
                        checkboxSelection={true}
                        onRowsSelectionHandler={onRowsSelectionHandler}
                      />
                    }
                  </>
                )}
              </div>
            )}
            {tabName === 0 && (
              <div>
                <div style={{ margin: "1rem 0" }}>
                  <SearchBar
                    title="Search IDP Users"
                    onChange={searchInternalIdpUsers}
                    elasticSearch={true}
                    message={"Search IDP Users"}
                    module="IDPUsers"
                    keyName="name"
                  />
                </div>
                {internalFilteredIdpUsers?.length === 0 ? (
                  <NotFound />
                ) : (
                  <>
                    {
                      <ReusableTable
                        module={"user management"}
                        width="100%"
                        // isLoading={isLoading}
                        rows={isPresent(internalFilteredIdpUsers)}
                        columns={idpUsersColumns}
                        hideFooter={false}
                        getRowIdValue={"pid"}
                        disableSelectionOnClick={true}
                        stopPropagation_Column={"action"}
                        status_onRowSingleClick={true}
                        checkboxSelection={true}
                        onRowsSelectionHandler={onRowsSelectionHandlerIntUsers}
                      />
                    }
                  </>
                )}
              </div>
            )}
          </>
        );
      case 1:
        return (
          <div>
            <Box sx={{ minWidth: 120, paddingTop: "1rem" }}>
              <Typography sx={font_Small}>
                Role<span style={{ color: "red" }}>*</span>
              </Typography>
              <FormControl fullWidth required>
                <Select
                  sx={font_Small}
                  placeholder="Role"
                  select
                  size="small"
                  value={roleDetails.assignedRole}
                  name="assignedRole"
                  onChange={handleFormChange}
                  displayEmpty={true}
                  renderValue={() =>
                    roleDetails.assignedRole !== "" ? (
                      roleDetails?.assignedRole?.split(" - ")[0]
                    ) : (
                      <div
                        className="placeholderstyle"
                        style={{ color: "#C1C1C1" }}
                      >
                        Select Role{" "}
                      </div>
                    )
                  }
                >
                  <MenuItem value={""}>
                    <div style={{ color: "#C1C1C1" }}>Select Role </div>
                  </MenuItem>

                  {tabName === 0 &&
                    basicReducerState.roles
                      ?.filter((role) => role.userType === "Internal")
                      ?.map((role) => (
                        <MenuItem value={`${role.name} - ${role.id}`}>
                          {role.name}
                        </MenuItem>
                      ))}
                  {tabName === 1 &&
                    basicReducerState.roles
                      ?.filter((role) => role.userType === "External")
                      ?.map((role) => (
                        <MenuItem value={`${role.name} - ${role.id}`}>
                          {role.name}
                        </MenuItem>
                      ))}
                </Select>
              </FormControl>
            </Box>
            {(assignedRole?.toLowerCase().includes("procurement lead") ||
              assignedRole?.toLowerCase().includes("buyer")) && (
              <Box sx={{ minWidth: 120, paddingTop: "1rem" }}>
                <Typography sx={font_Small}>
                  Purchasing Group
                  <span style={{ color: "red" }}>*</span>{" "}
                </Typography>
                <Autocomplete
                  disablePortal
                  placeholder={`Enter Purchasing Group`}
                  id="combo-box-demo"
                  options={moduleMasterFilterData?.purchGrpList}
                  fullWidth
                  sx={font_Small}
                  loading={moduleMasterFilterDataLoading.purchGrpList}
                  size="small"
                  freeSolo={true}
                  value={roleDetails?.purchasingGroup}
                  name="purchasingGroup"
                  renderInput={(params) => (
                    <TextField
                      placeholder={`Enter Purchasing Group`}
                      {...params}
                    />
                  )}
                  onInputChange={(e, newValue) => {
                    setRoleDetails({
                      ...roleDetails,
                      purchasingGroup: newValue,
                    });
                    onChangeFilter("purchGrp", newValue);
                  }}
                />
              </Box>
            )}
            {assignedRole?.toLowerCase().includes("supplier") && (
              <Box sx={{ minWidth: 120, paddingTop: "1rem" }}>
                {/* <div>
                  <Typography sx={font_Small}>Company</Typography>
                  <FormControl fullWidth size="small">
                    <Select
                      sx={font_Small}
                      placeholder="Company"
                      select
                      size="small"
                      labelId="demo-select-small"
                      id="demo-select-small"
                      value={roleDetails?.companyCode}
                      name="companyCode"
                      onChange={handleFormChange}
                      displayEmpty={true}
                      disabled={false}
                      renderValue={() =>
                        roleDetails?.companyCode !== "" ? (
                          roleDetails?.companyCode
                        ) : (
                          <div
                            className="placeholderstyle"
                            style={{ color: "#C1C1C1" }}
                          >
                            Select Company{" "}
                          </div>
                        )
                      }
                    >
                      <MenuItem sx={font_Small} value={""}>
                        <div style={{ color: "#C1C1C1" }}>Select Company </div>
                      </MenuItem>
                      {masterData?.companyCode &&
                        Object.keys(masterData?.companyCode).map((com) => (
                          <MenuItem
                            sx={font_Small}
                            value={`${com} - ${masterData?.companyCode[com]}`}
                          >
                            {com + " - " + masterData?.companyCode[com]}
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                </div> */}
                <div>
                  <Typography sx={font_Small}>
                    Supplier
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                  <Autocomplete
                    disablePortal
                    placeholder={`Enter Supplier`}
                    id="combo-box-demo"
                    options={moduleMasterFilterData?.vendorList}
                    fullWidth
                    sx={font_Small}
                    loading={moduleMasterFilterDataLoading.vendorList}
                    size="small"
                    freeSolo={true}
                    value={roleDetails?.supplier}
                    name="supplier"
                    renderInput={(params) => (
                      <TextField placeholder={`Enter Supplier`} {...params} />
                    )}
                    onInputChange={(e, newValue) => {
                      setRoleDetails({
                        ...roleDetails,
                        supplier: newValue,
                      });
                      onChangeFilter("vendorNo", newValue);
                    }}
                  />
                </div>
              </Box>
            )}
          </div>
        );
      default:
        return "Unknown step";
    }
  }

  const handleClearFields = () => {
    setRoleDetails({
      assignedRole: "",
      companyCode: "",
      supplier: "",
    });
  };

  const steps = getSteps();
  const handleNext = () => {
    if (
      (activeStep === 1 && tabName === 0) ||
      (activeStep === 1 && tabName === 1)
    ) {
      insertNewUser();
      handleClearFields();
      setActiveStep(0);
      setSelectedUser([]);
      return;
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
    setSelectedUser([]);
    if (activeStep === 0) {
      handleClearFields();
      onClose();
      setActiveStep(0);
    }
  };

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={() => {
        onClose();
        handleClearFields();
        setActiveStep(0);
        setSelectedUser([]);
        setExternalFilteredIdpUsers(basicReducerState.externalIdpUsers);
        setInternalFilteredIdpUsers(basicReducerState.internalIdpUsers);
      }}
    >
      <DialogTitle
        sx={{
          height: "3rem",
          display: "flex",
          margin: 0,
          justifyContent: "space-between",
          alignItems: "center",
          padding: ".5rem",
          paddingLeft: "1rem",
          backgroundColor: "#EAE9FF40",
        }}
      >
        <Typography variant="h6">Add Users</Typography>
        <IconButton
          sx={{ width: "max-content" }}
          onClick={onClose}
          children={<CloseIcon />}
        />
      </DialogTitle>

      <DialogContent sx={{ padding: "1rem 1rem" }}>
        {/* <Loading load={load} /> */}

        <div>
          {/* <Stepper activeStep={activeStep}>
            {steps.map((label, index) => {
              const stepProps = {};
              const labelProps = {};
              return (
                <Step key={label} {...stepProps}>
                  <StepLabel {...labelProps}>{label}</StepLabel>
                </Step>
              );
            })}
          </Stepper> */}
          <div>
            <Tabs
              value={tabName}
              onChange={(e, newValue) => {
                setTabName(newValue);
                setActiveStep(0);
                handleClearFields();
                setSelectedUser([]);
              }}
              indicatorColor="primary"
              textColor="primary"
            >
              <Tab
                label="Internal Users"
                {...a11yProps(0)}
                variant="body1"
                ml={1}
                sx={{
                  fontWeight: 600,
                  fontSize: "14px",
                  textTransform: "none",
                }}
              />
              {/* <Tab
                label="External Users"
                {...a11yProps(1)}
                variant="body1"
                ml={1}
                sx={{
                  fontWeight: 600,
                  fontSize: "14px",
                  textTransform: "none",
                }}
              /> */}
            </Tabs>

            <TabPanel value={tabName} index={0}>
              <div style={{ paddingTop: "1rem" }}>
                <Stepper activeStep={activeStep}>
                  {steps.map((label, index) => {
                    const stepProps = {};
                    const labelProps = {};
                    return (
                      <Step key={label} {...stepProps}>
                        <StepLabel {...labelProps}>{label}</StepLabel>
                      </Step>
                    );
                  })}
                </Stepper>
              </div>
              <div>
                <Typography>{getStepContent(activeStep)}</Typography>
              </div>
            </TabPanel>

            <TabPanel value={tabName} index={1}>
              <div style={{ paddingTop: "1rem" }}>
                <Stepper activeStep={activeStep}>
                  {steps.map((label, index) => {
                    const stepProps = {};
                    const labelProps = {};
                    return (
                      <Step key={label} {...stepProps}>
                        <StepLabel {...labelProps}>{label}</StepLabel>
                      </Step>
                    );
                  })}
                </Stepper>
              </div>
              <div>
                <Typography>{getStepContent(activeStep)}</Typography>
              </div>
            </TabPanel>
          </div>
        </div>
      </DialogContent>

      <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
        <Button
          size="small"
          variant="outlined"
          onClick={() => handleBack()}
          // disabled={activeStep === 0}
        >
          {activeStep === 0 ? "Cancel" : "Back"}
        </Button>

        <Button
          variant={
            load ||
            (activeStep === 0 && selectedUser?.length === 0) ||
            (activeStep === 1 && roleDetails.assignedRole === "") ||
            (activeStep === 1 &&
              roleDetails.assignedRole?.toLowerCase()?.includes("supplier") &&
              roleDetails.supplier === "") ||
            (activeStep === 1 &&
              (roleDetails.assignedRole?.toLowerCase()?.includes("buyer") ||
                roleDetails.assignedRole
                  ?.toLowerCase()
                  ?.includes("procurement lead")) &&
              roleDetails.purchasingGroup === "")
              ? "outlined"
              : "contained"
          }
          size="small"
          onClick={() => handleNext(activeStep)}
          style={{ textTransform: "capitalize" }}
          disabled={
            load ||
            (activeStep === 0 && selectedUser?.length === 0) ||
            (activeStep === 1 && roleDetails.assignedRole === "") ||
            (activeStep === 1 &&
              roleDetails.assignedRole?.toLowerCase()?.includes("supplier") &&
              roleDetails.supplier === "") ||
            (activeStep === 1 &&
              (roleDetails.assignedRole?.toLowerCase()?.includes("buyer") ||
                roleDetails.assignedRole
                  ?.toLowerCase()
                  ?.includes("procurement lead")) &&
              roleDetails.purchasingGroup === "")
          }
        >
          {activeStep === steps?.length - 1 ? "Add" : "Next"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const Users = () => {
  const classes = useStyle();
  const userReducerState = useSelector((state) => state.userReducer);
  const basicReducerState = useSelector((state) => state.userManagement);
  let userData = useSelector((state) => state.userManagement.userData);
  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["User Management"]
  );
  const [load, setLoad] = useState(true);
  const [inactiveUsersSwitch, setInactiveUsersSwitch] = useState(false);
  const [users, setusers] = useState(basicReducerState.users);
  const [filteredUsers, setFilteredUsers] = useState([
    // {
    //   id: 1123,
    //   userId: "P000717",
    //   employeeId: null,
    //   userName: "Akashjena",
    //   displayName: "Akash Jena",
    //   firstName: "Akash",
    //   lastName: "Jena",
    //   emailId: "<EMAIL>",
    //   idp: "P000717",
    //   status: "Active",
    //   isDeleted: 0,
    //   isActive: 1,
    //   createdBy: "null",
    //   createdOn: "2023-08-08 15:14:24.*********",
    //   updatedBy: "<EMAIL>",
    //   updatedOn: "2023-08-09 05:10:39.*********",
    //   supplierId: "INC - InstaBasket",
    //   companyCode: "0001 - SAP A.G.",
    //   purchasingGroup: null,
    //   applicationName: ["CW-SCP-DEV"],
    //   role: "buyer",
    // },
  ]);
  const [inactiveUsers, setInactiveUsers] = useState([
    //   {
    //   id: 123454,
    //   userId: "P000718",
    //   employeeId: null,
    //   userName: "Koushik",
    //   displayName: "Koushik Majumder",
    //   firstName: "Koushik",
    //   lastName: "Majumder",
    //   emailId: "<EMAIL>",
    //   idp: "P000718",
    //   status: "Active",
    //   isDeleted: 0,
    //   isActive: 1,
    //   createdBy: "Admin",
    //   createdOn: "2023-01-27 09:44:18.*********",
    //   updatedBy: "Admin",
    //   updatedOn: "2023-01-27 09:44:18.*********",
    //   applicationName: ["CW-SCP"],
    // }
  ]);
  const [pageDetails, setPageDetails] = useState({
    startCount: 0,
    userCount: 100,
    currPage: 1,
  });
  const [totalRecords, setTotalRecords] = useState(0);
  const [openIdpDialog, setOpenIdpDialog] = useState(false);
  const [openUserFileDialog, setOpenUserFileDialog] = useState(false);
  const [selectingFileUserApplications, setSelectingFileUserApplications] =
    useState([]);
  const [search, setSearch] = useState("");
  const [file, setFile] = useState(null);
  const [deletingUser, setDeletingUser] = useState(null);
  const [params, setParams] = useState({});
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(true);

  //<-- Functions and variables for ReusablePromptBox *promptAction_Functions -->
  const [promptBoxState, setPromptBoxState] = useState({
    open: false,
    type: "",
    redirectOnClose: true,
    message: "",
    title: "",
    severity: "",
  });
  const [promptBoxScenario, setPromptBoxScenario] = useState("");

  const promptAction_Functions = {
    handleClosePromptBox: () => {
      setPromptBoxState((prev) => ({
        open: false,
        type: "",
        redirectOnClose: false,
        message: "",
        title: "",
        severity: "",
      }));
      getUsersList();
      getExternalIdpUsers();
      getInternalIdpUsers();
      getInActiveUsers();
      setPromptBoxScenario("");
    },
    handleOpenPromptBox: (ref, data = {}) => {
      // SUCCESS,FAILURE
      let initialData = {
        open: true,
        title: "",
        message: "",
        okButton: true,
        cancelButton: true,
        okButtonText: "Ok",
        cancelText: "Cancel",
        type: "dialog",
      };
      if (ref === "SUCCESS") {
        initialData.type = "snackbar";
      }
      setPromptBoxScenario(ref);
      setPromptBoxState(Object.assign(initialData, data));
    },
    handleCloseAndRedirect: () => {
      promptAction_Functions.handleClosePromptBox();
      // navigate("/purchaseOrder/management");
    },
    getCancelFunction: () => {
      switch (promptBoxScenario) {
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getCloseFunction: () => {
      switch (promptBoxScenario) {
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getOkFunction: () => {
      switch (promptBoxScenario) {
        case "DELETE":
          return deleteUser;
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getCloseAndRedirectFunction: () => {
      if (!promptBoxState.redirectOnClose) {
        return promptAction_Functions.handleClosePromptBox;
      }
      return promptAction_Functions.handleCloseAndRedirect;
    },
  };

  const usersTableColumns = [
    {
      field: "id",
      headerName: "ID",
      hide: true,
    },
    {
      field: "displayName",
      headerName: "Display Name",
      flex: 1,
    },
    {
      field: "userId",
      headerName: "User ID",
      width: 80,
    },
    {
      field: "emailId",
      headerName: "Email ID",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Tooltip title={params.row.emailId}>
          <span className="table-cell-trucate">{params.row.emailId}</span>
        </Tooltip>
      ),
    },
    {
      field: "role",
      headerName: "Role",
      editable: false,
      flex: 1,
    },
    {
      field: "status",
      headerName: "Status",
      sortable: false,
      width: 80,
    },
    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      flex: 1,
      hide: !checkIwaAccess(iwaAccessData, "User Management", "Delete User"),
      align: "center",
      headerAlign: "center",
      renderCell: (cellValues) => {
        let user = cellValues.row;
        return (
          <>
            {inactiveUsersSwitch && (
              <Tooltip title="Reactivate User">
                <IconButton
                  aria-label="Reactivate"
                  color="secondary"
                  onClick={(e) => {
                    e.stopPropagation();
                    updateInactiveUser(user);
                  }}
                >
                  <MdResetTv style={{ fontSize: 16 }} />
                </IconButton>
              </Tooltip>
            )}
            {!inactiveUsersSwitch && (
              <>
                {user?.status !== "Draft" && !inactiveUsersSwitch && (
                  <Tooltip title="Delete">
                    <IconButton
                      disabled={user.emailId === userData.emailId}
                      aria-label="Delete"
                      onClick={(e) => {
                        // setOpenDeletionDialog(true);
                        // setDeletingUser(user?.emailId);
                        // setPromptType("dialog");
                        // setPromptMessage("Do you want to delete the user?")
                        e.stopPropagation();
                        setDeletingUser(user?.emailId);

                        promptAction_Functions.handleOpenPromptBox("DELETE", {
                          title: "Confirm Delete",
                          message: `Do you want to delete the user?`,
                          severity: "warning",
                          cancelButton: true,
                          okButton: true,
                          okButtonText: "Delete",
                        });
                      }}
                    >
                      <DeleteOutlinedIcon
                        color={
                          user.emailId === userData.emailId ? "gray" : "danger"
                        }
                      />
                    </IconButton>
                  </Tooltip>
                )}
              </>
            )}
          </>
        );
      },
    },
  ];

  const deleteUser = () => {
    // setLoad(true);
    const disableUserUrl = `/${destination_IWA_NPI}/api/v1/usersMDG/deactivateUserMDG?id=${deletingUser}`;
    const disableUserRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    };
    fetch(disableUserUrl, disableUserRequestParam)
      .then((res) => res.json())
      .then((data) => {
        // setLoad(false);

        if (data.statusCode === 200) {
          promptAction_Functions.handleOpenPromptBox("SUCCESS", {
            message: `User deleted successfully`,
            redirectOnClose: false,
          });
        } else {
          promptAction_Functions.handleOpenPromptBox("ERROR", {
            title: "Failed",
            message: `User deletion failed`,
            severity: "danger",
            cancelButton: false,
          });
        }

        dispatch(
          setUsers(
            basicReducerState?.users?.filter(
              (user) => user?.emailId !== deletingUser
            ) || []
          )
        );
        setusers(users?.filter((user) => user?.emailId !== deletingUser) || []);
        setFilteredUsers(
          filteredUsers?.filter((user) => user?.emailId !== deletingUser) || []
        );
        setTotalRecords(filteredUsers?.length);

        setDeletingUser(null);
        getUsersList();
        getExternalIdpUsers();
        getInternalIdpUsers();
        // refreshData();

        // dispatch(
        //   setResponseMessage({
        //     open: true,
        //     status: data?.status ? "success" : "error",
        //     message: data?.status
        //       ? "User deleted successfully"
        //       : "Something went wrong",
        //   })
        // );
      })
      .catch((err) => {
        // setLoad(false);
      });
  };
  const changeStatus = (user) => {
    setFilteredUsers(
      filteredUsers?.map((f_user) =>
        f_user?.emailId === user?.emailId
          ? {
              ...f_user,
              status: f_user?.status === "Draft" ? "Active" : "Draft",
            }
          : { ...f_user }
      )
    );
  };
  const updateUser = (user) => {
    // setLoad(true);
    const updateUserUrl = `/${destination_IWA_NPI}/api/v1/usersMDG/userAndUserRoleMappingMDG`;
    const updateUserPayload = {
      userId: user?.userId,
      userName: user?.userName,
      displayName: user?.displayName,
      firstName: user?.firstName,
      lastName: user?.lastName,
      emailId: user?.emailId,
      status: "Active",
      isDeleted: 0,
      isActive: 1,
      userRoles: [],
      featuresException: [],
    };
    const updateUserRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateUserPayload),
    };
    fetch(updateUserUrl, updateUserRequestParam)
      .then((res) => res.json())
      .then((data) => {
        // setLoad(false);
        if (data?.statusCode === 201) {
          dispatch(
            setUsers(
              basicReducerState.users?.map((f_user) =>
                f_user?.emailId === user?.emailId
                  ? {
                      ...f_user,
                      status: "Active",
                      displayName: user?.displayName,
                    }
                  : { ...f_user }
              ) || []
            )
          );

          setFilteredUsers(
            filteredUsers?.map((f_user) =>
              f_user?.emailId === user?.emailId
                ? {
                    ...f_user,
                    status: "Active",
                    displayName: user?.displayName,
                  }
                : { ...f_user }
            ) || []
          );

          dispatch(
            setResponseMessage({
              open: true,
              status: data?.status ? "success" : "error",
              message: data?.status
                ? "Display name updated successfully"
                : "Something went wrong",
            })
          );
        }
      })
      .catch((err) => {
        // setLoad(false);
      });
  };
  const updateInactiveUser = (user) => {
    // setLoad(true);
    const updateInactiveUserUrl = `/${destination_IWA_NPI}/api/v1/usersMDG/updateUserMDG`;
    const updateInactiveUserPayload = {
      companyCode: user?.companyCode,
      createdBy: user?.createdBy,
      createdOn: user?.createdOn,
      displayName: user?.displayName,
      emailId: user?.emailId,
      employeeId: user?.employeeId,
      firstName: user?.firstName,
      idp: user?.idp,
      isActive: 1,
      isDeleted: 0,
      lastName: user?.lastName,
      rolesDetailsList: {
        createdBy: user?.createdBy,
        createdOn: user?.createdOn,
        groupRole: "",
        isActive: 1,
        isDeleted: 0,
        isGroupRole: 0,
        roleId: user?.roleId,
        status: "",
        updatedBy: userData?.emailId,
        updatedOn: "",
        userEmail: user?.emailId,
      },
      status: "Active",
      supplierId: user?.supplierId,
      terminationDate: "",
      updatedBy: "",
      updatedOn: "",
      userId: user?.userId,
      userName: user?.userName,
    };
    // const updateInactiveUserPayload = {
    //   employeeId: user?.employeeId,
    //   userId: user?.userId,
    //   userName: user?.userName /**** */,
    //   displayName: user?.displayName,
    //   firstName: user?.firstName /**** */,
    //   lastName: user?.lastName /**** */,
    //   emailId: user?.emailId,
    //   idp: user?.idp /**** */,
    //   status: "Active",
    //   isActive: 1,
    //   isDeleted: 0,
    //   createdBy: user?.createdBy,
    //   createdOn: user?.createdOn,
    //   terminationDate: "",
    //   updatedBy: userData?.emailId,
    //   updatedOn: "",
    // };
    const updateInactiveUserRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateInactiveUserPayload),
    };
    fetch(updateInactiveUserUrl, updateInactiveUserRequestParam)
      .then((res) => res.json())
      .then((data) => {
        // setLoad(false);
        if (data?.statusCode === 201) {
          getUsers();
          getInActiveUsers();
          dispatch(
            setResponseMessage({
              open: true,
              status: data?.status ? "success" : "error",
              message: data?.status
                ? "User status updated successfully"
                : "Something went wrong",
            })
          );
        }
      })
      .catch((err) => {
        // setLoad(false);
      });
  };
  const editUser = (userId) => {
    setParams({ userId: userId });
  };
  const getUsers = () => {
    getAllUsers(
      () => {
        // setLoad(true);
      },
      (data) => {
        dispatch(setUsers(data?.data || []));
        // setLoad(false);
      },
      (err) => {
        // setLoad(false);
      }
    );
  };
  const getUsersList = () => {
    // setLoad(true);
    setIsLoading(true); /***** */
    const getActiveUsersListUrl = `/${destination_IWA_NPI}/api/v1/usersMDG/paginationUsersMDG?startCount=${pageDetails?.startCount}&userCount=${pageDetails?.userCount}&searchValue=${formcontroller_SearchBar.name}`;
    const getActiveUsersListRequestParam = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    };
    fetch(getActiveUsersListUrl, getActiveUsersListRequestParam)
      .then((res) => res.json())
      .then((data) => {
        // setLoad(false);
        if (data?.statusCode === 200) {
          var rowsNew = [];
          for (
            let index = 0;
            index < data.data.userWithApplicationNamePojoMDG?.length;
            index++
          ) {
            var tempObj = data?.data?.userWithApplicationNamePojoMDG[index];
            var tempRow = {
              id: uuidv4(),
              displayName: tempObj["displayName"],
              userId: tempObj["userId"],
              emailId: tempObj["emailId"],
              role: tempObj["roleName"],
              status: tempObj["status"],
              employeeId: tempObj["employeeId"],
              userName: tempObj["userName"],
              firstName: tempObj["firstName"],
              lastName: tempObj["lastName"],
              idp: tempObj["idp"],
              createdBy: tempObj["createdBy"],
              createdOn: tempObj["createdOn"],
            };
            rowsNew.push(tempRow);
          }
          setFilteredUsers(rowsNew);
          // setFilteredUsers(data?.data?.userWithApplicationNamePojoSCP);
          setTotalRecords(data?.data?.totalUserCount);
          setIsLoading(false);
        }
      })
      .catch((err) => {
        // setLoad(false);
      });
  };
  const getExternalIdpUsers = () => {
    const getIdpUsersUrl = `/${destination_IWA}/api/v1/users/SAP-IAS/getUsers`;
    const getIdpUsersRequestParams = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    };
    fetch(getIdpUsersUrl, getIdpUsersRequestParams)
      .then((res) => res.json())
      .then((data) => {
        dispatch(setExternalIdpUsers(data?.data || []));
      })
      .catch((err) => {
        // fError(err);
      });
  };

  const getInternalIdpUsers = () => {
    const getIdpUsersUrl = `/${destination_IWA}/api/v1/users/SAP-IAS/getUsers`;
    const getIdpUsersRequestParams = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    };
    fetch(getIdpUsersUrl, getIdpUsersRequestParams)
      .then((res) => res.json())
      .then((data) => {
        dispatch(setInternalIdpUsers(data?.data || []));
      })
      .catch((err) => {
        // fError(err);
      });
  };

  const getInActiveUsers = () => {
    const getAllInactiveUsersUrl = `/${destination_IWA_NPI}/api/v1/usersMDG/readAllDeactivatedUserMDG`;
    const getAllInactiveUsersRequestParam = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    };
    fetch(getAllInactiveUsersUrl, getAllInactiveUsersRequestParam)
      .then((res) => res.json())
      .then((data) => {
        if (data.data?.length !== 0) {
          var rowsNew = [];
          for (let index = 0; index < data.data?.length; index++) {
            var tempObj = data?.data[index];
            var tempRow = {
              id: uuidv4(),
              displayName: tempObj["displayName"],
              userId: tempObj["userId"],
              emailId: tempObj["emailId"],
              role: tempObj["roleName"],
              status: tempObj["status"],
              employeeId: tempObj["employeeId"],
              userName: tempObj["userName"],
              firstName: tempObj["firstName"],
              lastName: tempObj["lastName"],
              idp: tempObj["idp"],
              createdBy: tempObj["createdBy"],
              createdOn: tempObj["createdOn"],
              roleId: tempObj["roleId"],
              companyCode: tempObj["companyCode"],
              supplierId: tempObj["supplierId"],
            };
            rowsNew.push(tempRow);
          }
          setInactiveUsers(rowsNew);
          setTotalRecords(data?.data?.length);
        } else {
          setInactiveUsers([]);
          setTotalRecords(0);
        }
        // setIsLoading(false);
      });
  };

  const uploadUsersFile = (e) => {
    if (!file) {
      console.log("no file found");
      return;
    }
    // setLoad(true);
    const applicationName = selectingFileUserApplications?.map(
      (application) => application?.name
    );
    const url = `/${destination_IWA}/api/v1/users/addUsersUsingCsv?applicationName=${applicationName.join(
      ","
    )}`;
    let formData = new FormData();
    formData.append("file", file);
    formData.append("name", file.name);
    const requestParam = {
      method: "POST",
      headers: {},
      body: formData,
    };
    fetch(url, requestParam)
      .then((res) => {
        console.log(res);
      })
      .then((data) => {
        // setLoad(false);
        setOpenUserFileDialog(false);
        setFile(null);
        setSelectingFileUserApplications([]);

        dispatch(
          setResponseMessage({
            open: true,
            status: "success",
            message: "User file uploaded successfully",
          })
        );
      })
      .catch((err) => {
        console.log(err);
        // setLoad(false);
        dispatch(
          setResponseMessage({
            open: true,
            status: "error",
            message: "Something went wrong",
          })
        );
      });
  };
  const isDefaultRole = (applicationId) => {
    return basicReducerState?.roles?.filter(
      (role) =>
        Number(role?.applicationId) === Number(applicationId) &&
        Number(role.isDefault) === 1
    )?.length > 0
      ? true
      : false;
  };
  const clearSearchBar = () => {
    setSearch("");
  };
  const formcontroller_SearchBar = useSelector(
    (state) => state.commonSearchBar["Users"]
  );
  const searchUser = () => {
    setPageDetails({
      startCount: 0,
      userCount: 100,
      currPage: 1,
    });
  };
  useEffect(() => {
    getUsersList();
  }, [pageDetails]);

  const refreshData = () => {
    getUsersList();
    getExternalIdpUsers();
    getInternalIdpUsers();
    getInActiveUsers();
  };

  return (
    <div>
      {/* <Loading load={isLoading} /> */}

      <div>
        <IdpUsers
          open={openIdpDialog}
          handleOpenPromptBox={promptAction_Functions.handleOpenPromptBox}
          onClose={() => {
            setOpenIdpDialog(false);
          }}
          handleRefresh={refreshData}
        />

        <UploadFile
          open={openUserFileDialog}
          onClose={() => {
            setOpenUserFileDialog(false);
            setFile(null);
            setSelectingFileUserApplications([]);
          }}
          onUpload={() => {
            uploadUsersFile();
          }}
          file={file}
          setFile={setFile}
          disableCondition={
            !file || selectingFileUserApplications?.length === 0
          }
          load={isLoading}
        >
          <Autocomplete
            required
            multiple
            size="small"
            style={{ fontSize: 12 }}
            disableCloseOnSelect
            filterSelectedOptions
            value={selectingFileUserApplications}
            onChange={(e, applications) => {
              setSelectingFileUserApplications(applications);
            }}
            options={basicReducerState?.applications?.filter((application) =>
              isDefaultRole(Number(application?.id))
            )}
            getOptionLabel={(option) => option?.name}
            renderOption={(props, option, { selected }) => (
              <li {...props}>
                <Checkbox
                  icon={<CheckBoxOutlineBlank fontSize="small" />}
                  checkedIcon={<CheckBox color="primary" fontSize="small" />}
                  checked={selected}
                />

                <Typography style={{ fontSize: 12 }}>{option?.name}</Typography>
              </li>
            )}
            renderInput={(params) => (
              <TextField
                {...params}
                variant="standard"
                label="Applications"
                style={{ fontSize: 12 }}
              />
            )}
          />
        </UploadFile>

        <ReusablePromptBox
          type={promptBoxState.type}
          promptState={promptBoxState.open}
          setPromptState={promptAction_Functions.handleClosePromptBox}
          onCloseAction={promptAction_Functions.getCloseFunction()}
          promptMessage={promptBoxState.message}
          dialogSeverity={promptBoxState.severity}
          dialogTitleText={promptBoxState.title}
          handleCancelButtonAction={promptAction_Functions.getCancelFunction()}
          cancelButtonText={promptBoxState.cancelText} //Cancel button display text
          showCancelButton={promptBoxState.cancelButton} //Enable Cancel button
          handleSnackBarPromptClose={promptAction_Functions.getCloseAndRedirectFunction()}
          handleOkButtonAction={promptAction_Functions.getOkFunction()}
          okButtonText={promptBoxState.okButtonText}
          showOkButton={promptBoxState.okButton}
        />

        {!inactiveUsersSwitch && (
          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0, zIndex: 1 }}
            elevation={2}
          >
            <BottomNavigation
              showLabels
              className="container_BottomNav"
              sx={{
                display: "flex",
                justifyContent: "flex-end",
              }}
            >
              <Button
                size="small"
                variant="contained"
                color="primary"
                sx={{
                  marginLeft: "auto",
                }}
                onClick={() => setOpenIdpDialog(true)}
                startIcon={<Add />}
                disabled={false}
              >
                Add User
              </Button>
            </BottomNavigation>
          </Paper>
        )}
        <div style={{ height: "38px", marginTop: ".5rem" }}>
          <div
            style={{
              display: "flex",
              alignItems: "start",
              justifyContent: "end",
              paddingBottom: ".1rem",
            }}
          >
            {!inactiveUsersSwitch && (
              <SearchBar
                title="Search Users"
                handleSearchAction={searchUser}
                query={search}
                module="Users"
                keyName="name"
                message={"Search Users"}
              />
            )}
            <Tooltip
              title={
                !inactiveUsersSwitch
                  ? "Switch To Inactive Users"
                  : "Switch To Active Users"
              }
            >
              <Switch
                className="btn-ml"
                sx={{
                  marginBottom: "auto",
                }}
                color="primary"
                checked={inactiveUsersSwitch}
                onClick={(e) => {
                  setInactiveUsersSwitch(e.target.checked);
                  if (e.target.checked) {
                    getInActiveUsers();
                  } else {
                    // setFilteredUsers(basicReducerState?.users || []);
                    getUsersList();
                  }
                }}
              />
            </Tooltip>
            <Tooltip title="Download template" placement="bottom-start">
              <IconButton
                className="btn-ml"
                sx={{
                  marginBottom: "auto",
                  ...iconButton_SpacingSmall,
                }}
                disabled={isLoading}
                onClick={(e) => {
                  downloadFile({
                    data: userFileHeading,
                    fileName: "cw_users.csv",
                    fileType: "text/csv",
                  });
                }}
              >
                <GetApp />
              </IconButton>
            </Tooltip>

            <Tooltip title="Upload file" placement="bottom-start">
              <IconButton
                className="btn-ml"
                sx={{
                  marginBottom: "auto",
                  ...iconButton_SpacingSmall,
                }}
                disabled={isLoading}
                onClick={() => {
                  setOpenUserFileDialog(true);
                }}
              >
                <Publish />
              </IconButton>
            </Tooltip>

            <Tooltip title="Refresh" placement="bottom-start">
              <IconButton
                className="btn-ml"
                sx={{
                  marginBottom: "auto",
                  ...iconButton_SpacingSmall,
                }}
                disabled={isLoading}
                onClick={() => {
                  getUsersList();
                  getExternalIdpUsers();
                  getInternalIdpUsers();
                  // getInActiveUsers();
                }}
              >
                <Refresh />
              </IconButton>
            </Tooltip>
          </div>
        </div>

        <Grid
          container
          spacing={2}
          style={{
            height: `calc(100vh - ${appHeaderHeight} - ${userPageHeaderHeight})`,
          }}
        >
          <Grid item xs={params?.userId ? 6 : 12} style={{ height: "100%" }}>
            {!inactiveUsersSwitch && filteredUsers && (
              <Grid item md={12} sx={{ position: "relative" }}>
                <ReusableTable
                  module={"user management"}
                  width="100%"
                  // isLoading={isLoading}
                  title={`List of Users (${filteredUsers?.length ?? 0})`}
                  rows={filteredUsers}
                  columns={usersTableColumns}
                  hideFooter={false}
                  getRowIdValue={"id"}
                  disableSelectionOnClick={true}
                  stopPropagation_Column={"action"}
                  status_onRowSingleClick={true}
                  noOfColumns={5}
                  rowsPerPageOptions={[5, 10, 15]}
                  callback_onRowSingleClick={(params) => {
                    if (
                      params?.row.status === "Draft" ||
                      params?.row.status === "Inactive"
                    ) {
                      return;
                    }
                    editUser(params.row?.emailId);
                  }}
                />
              </Grid>
            )}

            {inactiveUsersSwitch && inactiveUsers && (
              <Grid item md={12} sx={{ position: "relative" }}>
                <ReusableTable
                  module={"user management"}
                  width="100%"
                  // isLoading={isLoading}
                  title={`List of Users (${inactiveUsers?.length ?? 0})`}
                  rows={inactiveUsers}
                  columns={usersTableColumns}
                  hideFooter={false}
                  getRowIdValue={"id"}
                  disableSelectionOnClick={true}
                  noOfColumns={5}
                  rowsPerPageOptions={[5, 10, 15]}
                  stopPropagation_Column={"action"}
                  status_onRowSingleClick={true}
                  callback_onRowSingleClick={(params) => {
                    if (
                      params?.row.status === "Draft" ||
                      params?.row.status === "Inactive"
                    ) {
                      return;
                    }
                    editUser(params.row?.emailId);
                  }}
                />
              </Grid>
            )}
          </Grid>

          {params?.userId && filteredUsers?.length > 0 && (
            <Grid
              item
              xs={params?.userId ? 6 : false}
              sx={{ marginTop: "30px", paddingBottom: "4rem" }}
            >
              <UserDetail
                params={params}
                setParams={setParams}
                getUsersList={(emailId, displayName) => {
                  dispatch(
                    setUsers(
                      basicReducerState.users?.map((f_user) =>
                        f_user?.emailId === emailId
                          ? {
                              ...f_user,
                              status: "Active",
                              displayName: displayName,
                            }
                          : { ...f_user }
                      ) || []
                    )
                  );

                  setFilteredUsers(
                    filteredUsers?.map((f_user) =>
                      f_user?.emailId === emailId
                        ? {
                            ...f_user,
                            status: "Active",
                            displayName: displayName,
                          }
                        : { ...f_user }
                    ) || []
                  );
                }}
              />
            </Grid>
          )}
        </Grid>
      </div>
    </div>
  );
};

export default Users;
