import {
  <PERSON><PERSON>,
  Icon<PERSON>utton,
  <PERSON>ack,
  Typography,
  Box,
  Card,
  FormControl,
  Select,
  TextField,
  MenuItem,
  Chip,
  Tooltip,
  Modal,
} from "@mui/material";
import React, { useState, useEffect } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import CampaignOutlinedIcon from "@mui/icons-material/CampaignOutlined";
import SlideshowOutlinedIcon from "@mui/icons-material/SlideshowOutlined";
import { styled } from "@mui/material/styles";
import moment from "moment";
import { DatePicker } from "rsuite";
import ReactPlayer from "react-player/lazy";
import { MatView } from "../DocumentManagement/UtilDoc";
import { ViewDetailsIcon } from "../Common/icons";
import { doAjax } from "../Common/fetchService";
import { destination_Admin } from "../../destinationVariables";
import { useSelector } from "react-redux";

// Styled components matching NewBroadcast
const StyledCard = styled(Card)(({ theme }) => ({
  padding: theme.spacing(3),
  marginBottom: theme.spacing(2),
  borderRadius: theme.spacing(2),
  boxShadow: "0 2px 12px rgba(0,0,0,0.08)",
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: "18px",
  fontWeight: 600,
  color: theme.palette.text.primary,
  marginBottom: theme.spacing(2),
  borderBottom: `2px solid ${theme.palette.primary.main}`,
  paddingBottom: theme.spacing(1),
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  "& .MuiOutlinedInput-root": {
    borderRadius: theme.spacing(1),
    backgroundColor: theme.palette.grey[50],
  },
  "& .MuiOutlinedInput-input.Mui-disabled": {
    WebkitTextFillColor: theme.palette.text.primary,
  },
}));

const StyledSelect = styled(Select)(({ theme }) => ({
  borderRadius: theme.spacing(1),
  backgroundColor: theme.palette.grey[50],
  "& .MuiSelect-select.Mui-disabled": {
    WebkitTextFillColor: theme.palette.text.primary,
  },
}));

const InfoBox = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.grey[50],
  padding: theme.spacing(2),
  borderRadius: theme.spacing(1),
  border: `1px solid ${theme.palette.divider}`,
}));

const StatusChip = styled(Chip)(({ status }) => ({
  fontSize: "14px",
  fontWeight: 500,
  borderRadius: "8px",
  minWidth: "100px",
  backgroundColor: 
    status === "Active" ? "#e8f5e8" :
    status === "Draft" ? "#fff3e0" :
    status === "Archived" ? "#f3f4f6" : "#e3f2fd",
  color:
    status === "Active" ? "#2e7d32" :
    status === "Draft" ? "#ef6c00" :
    status === "Archived" ? "#616161" : "#1976d2",
  border: `1px solid ${
    status === "Active" ? "#c8e6c9" :
    status === "Draft" ? "#ffcc02" :
    status === "Archived" ? "#e0e0e0" : "#bbdefb"
  }`,
}));

const LinkBox = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.grey[50],
  padding: theme.spacing(1.5),
  borderRadius: theme.spacing(1),
  border: `1px solid ${theme.palette.divider}`,
  minHeight: "40px",
  display: "flex",
  alignItems: "center",
}));

const ViewBroadcast = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const BroadcastId = queryParams.get("BroadcastId");
  const navigate = useNavigate();
  
  const [broadcastData, setBroadcastData] = useState({
    title: "",
    description: "",
    category: "",
    startDate: new Date(),
    endDate: new Date(),
    module: "",
    fileName: "",
    fileBase64: "",
    status: "",
    link: "",
  });
  
  const [videoModalOpen, setVideoModalOpen] = useState(false);
  const source = `/${destination_Admin}/broadcastManagement/showBroadcastById/${BroadcastId}`;

  const modalStyle = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: "80%",
    maxWidth: "800px",
    bgcolor: "background.paper",
    borderRadius: 2,
    boxShadow: 24,
    p: 2,
  };

  const getDetails = () => {
    const handleSuccess = (data) => {
      setBroadcastData({
        title: data.broadcastDetailsDto.broadcastTitle,
        description: data.broadcastDetailsDto.description,
        category: data.broadcastDetailsDto.broadcastCategory,
        startDate: new Date(data.broadcastDetailsDto.startDate),
        endDate: new Date(data.broadcastDetailsDto.endDate),
        module: data.broadcastDetailsDto.module || "N/A",
        fileName: data.broadcastDetailsDto.fileName,
        fileBase64: data.broadcastDetailsDto.fileBase64,
        status: data.broadcastDetailsDto.status,
        link: data.broadcastDetailsDto.externalUrl,
      });
    };
    
    const handleError = (error) => {
      console.error("Error fetching broadcast details:", error);
    };

    doAjax(
      `/${destination_Admin}/broadcastManagement/getBroadcastDetailsById/${BroadcastId}`,
      'get',
      handleSuccess,
      handleError
    );
  };

  const handleVideoView = () => {
    setVideoModalOpen(true);
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case "Announcements":
        return <CampaignOutlinedIcon color="primary" />;
      case "Videos":
        return <SlideshowOutlinedIcon color="primary" />;
      default:
        return null;
    }
  };

  useEffect(() => {
    getDetails();
  }, [BroadcastId]);

  return (
    <Box sx={{ padding: 2, paddingBottom: 4 }}>
      {/* Header */}
      <Grid container sx={{ borderRadius: 2, marginBottom: 2 }}>
        <Grid container>
          <Grid item md={7} style={{ padding: "16px", paddingLeft: "" }}>
            <Stack direction="row">
              <IconButton
                onClick={() => navigate("/configCockpit/broadcastConfigurations")}
                color="primary"
                aria-label="back"
              >
                <ArrowCircleLeftOutlinedIcon
                  sx={{
                    fontSize: "25px",
                    color: "#000000",
                  }}
                />
              </IconButton>
              <Box>
                <Typography variant="h5" paddingTop="0.3rem" fontSize="20px">
                  <strong>Broadcast Details: {BroadcastId}</strong>
                </Typography>
                <Typography variant="body2" color="#777" fontSize="12px">
                  This view displays the details of the broadcast
                </Typography>
              </Box>
            </Stack>
          </Grid>
        </Grid>
      </Grid>

      {/* Main Content */}
      <StyledCard>
        <SectionTitle>Broadcast Information</SectionTitle>
        
        <Grid container spacing={3}>
          {/* First Row */}
          <Grid item xs={12} md={6} lg={3}>
            <Typography variant="subtitle2" gutterBottom>
              Broadcast Category
            </Typography>
            <FormControl fullWidth size="small">
              <StyledSelect
                value={broadcastData.category}
                disabled
                displayEmpty
                renderValue={(selected) => (
                  <Stack direction="row" spacing={1} alignItems="center">
                    {getCategoryIcon(selected)}
                    <Typography>{selected || "Not specified"}</Typography>
                  </Stack>
                )}
              >
                <MenuItem value="Announcements">
                  <Stack direction="row" spacing={1} alignItems="center">
                    <CampaignOutlinedIcon color="primary" />
                    <Typography>Announcements</Typography>
                  </Stack>
                </MenuItem>
                <MenuItem value="Videos">
                  <Stack direction="row" spacing={1} alignItems="center">
                    <SlideshowOutlinedIcon color="primary" />
                    <Typography>Videos</Typography>
                  </Stack>
                </MenuItem>
              </StyledSelect>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6} lg={3}>
            <Typography variant="subtitle2" gutterBottom>
              Module
            </Typography>
            <InfoBox>
              <Typography variant="body2">
                {broadcastData.module || "Not specified"}
              </Typography>
            </InfoBox>
          </Grid>

          <Grid item xs={12} md={6} lg={3}>
            <Typography variant="subtitle2" gutterBottom>
              Start Date
            </Typography>
            <DatePicker
              size="sm"
              value={broadcastData.startDate}
              format="dd MMM yyyy"
              style={{ width: '100%', height: '40px' }}
              disabled
            />
          </Grid>

          <Grid item xs={12} md={6} lg={3}>
            <Typography variant="subtitle2" gutterBottom>
              End Date
            </Typography>
            <DatePicker
              size="sm"
              value={broadcastData.endDate}
              format="dd MMM yyyy"
              style={{ width: '100%', height: '40px' }}
              disabled
            />
          </Grid>

          {/* Status Row */}
          <Grid item xs={12} md={6} lg={3}>
            <Typography variant="subtitle2" gutterBottom>
              Status
            </Typography>
            <StatusChip
              status={broadcastData.status}
              label={broadcastData.status || "Unknown"}
            />
          </Grid>

          {/* Second Row */}
          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              Broadcast Title
              <Typography component="span" variant="caption" color="text.secondary">
                {" "}({broadcastData.title.length}/100 characters)
              </Typography>
            </Typography>
            <StyledTextField
              fullWidth
              value={broadcastData.title}
              disabled
              placeholder="No title provided"
            />
          </Grid>

          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              Broadcast Description
              <Typography component="span" variant="caption" color="text.secondary">
                {" "}({broadcastData.description.length}/300 characters)
              </Typography>
            </Typography>
            <StyledTextField
              fullWidth
              multiline
              rows={4}
              value={broadcastData.description}
              disabled
              placeholder="No description provided"
            />
          </Grid>

          {/* File and URL Row */}
          <Grid item xs={12} md={8}>
            <Typography variant="subtitle2" gutterBottom>
              Attached Document
            </Typography>
            <InfoBox>
              {broadcastData.fileName ? (
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Typography variant="body2">
                    {broadcastData.fileName}
                  </Typography>
                  {broadcastData.category === "Videos" ? (
                    <Tooltip title="View Video">
                      <IconButton size="small" onClick={handleVideoView}>
                        {ViewDetailsIcon}
                      </IconButton>
                    </Tooltip>
                  ) : (
                    <MatView
                      index={BroadcastId}
                      name={broadcastData.fileName}
                      isBroadcast={true}
                      // documentViewUrl={broadcastData.fileBase64}
                    />
                  )}
                </Stack>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No document attached
                </Typography>
              )}
            </InfoBox>
          </Grid>

          <Grid item xs={12} md={4}>
            <Typography variant="subtitle2" gutterBottom>
              External URL
            </Typography>
            <LinkBox>
              {broadcastData.link ? (
                <Typography
                  variant="body2"
                  component="a"
                  href={broadcastData.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  sx={{
                    color: "primary.main",
                    textDecoration: "none",
                    "&:hover": {
                      textDecoration: "underline",
                    },
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                  }}
                >
                  {broadcastData.link}
                </Typography>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No external link provided
                </Typography>
              )}
            </LinkBox>
          </Grid>
        </Grid>
      </StyledCard>

      {/* Video Modal */}
      <Modal
        open={videoModalOpen}
        onClose={() => setVideoModalOpen(false)}
        aria-labelledby="video-modal-title"
      >
        <Box sx={modalStyle}>
          <Typography variant="h6" component="h2" gutterBottom>
            Video Preview
          </Typography>
          <Box sx={{ position: "relative", paddingTop: "56.25%" }}>
            <ReactPlayer
              url={source}
              controls
              width="100%"
              height="100%"
              style={{ position: "absolute", top: 0, left: 0 }}
            />
          </Box>
        </Box>
      </Modal>
    </Box>
  );
};

export default ViewBroadcast;