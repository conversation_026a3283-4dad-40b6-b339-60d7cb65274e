import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  payload: {},
  payloadForValue: {}, 
  payloadForChangeNewValue: {},
  payloadPC: {},
  responseFromAPI: {},
  description: {},
  editMode: false,
  headerDataCC: {},
  headerDataPC: {},
  activeStep: 0,
  companyCodeData: [],
  profitCenterTabsArray: [],
  generalInformation: [],
  mappedDataPC: [],
  buttonsData: [],
  filteredButtons: [],
  selectedCheckBox: [],
};

export const editPayloadSlice = createSlice({
  name: "edit",
  initialState,
  reducers: {
    setPayload: (state, action) => {
      state.payload[action.payload.keyname] = action.payload.data;
      return state;
    },
    setPayloadForValue: (state, action) => {
      state.payloadForValue[action.payload.keyname] = action.payload.data;
      return state;
    },
    setPayloadForNewChange: (state, action) => {
      let data = {
        ...state.payloadForChangeNewValue,
        [action.payload.keyname]: action.payload.data,
      };
      return {
        ...state,
        payloadForChangeNewValue: data,
      };
    },
    setResponseFromAPI: (state, action) => {
      state.responseFromAPI = action.payload;
    },
    descriptionHeader: (state, action) => {
      state.description = action.payload;
    },
    setPayloadWhole: (state, action) => {
      let data = { ...state.payload, ...action.payload };
      return {
        ...state,
        payload: data,
      };
    },
    setPayloadWholeForNewChange: (state, action) => {
      let data = { ...state.payloadForChangeNewValue, ...action.payload };
      return {
        ...state,
        payloadForChangeNewValue: data,
      };
    },

    setPayloadWholeForProfit: (state, action) => {
      state.payloadPC = action.payload;
      return state;
    },
    setPayloadForProfit: (state, action) => {
      state.payloadPC[action.payload.keyname] = action.payload.data;
      return state;
    },
    setEditMode: (state, action) => {
      state.editMode = action.payload;
      return state;
    },
    setHeaderdataCC: (state, action) => {
      state.headerDataCC = action.payload;
      return state;
    },
    setHeaderdataPC: (state, action) => {
      state.headerDataPC = action.payload;
      return state;
    },
    setCompanyCodeRowsForPC: (state, action) => {
      state.companyCodeData = action.payload;
    },
    setProfitCenterTabsArray: (state, action) => {
      state.profitCenterTabsArray = action.payload;
    },
    setCheckBox: (state, action) => {
      //For GL Extend
      state.selectedCheckBox = action.payload;
    },
    setGeneralInformation: (state, action) => {
      state.generalInformation = action.payload;
    },
    setProfitCenterMappedData: (state, action) => {
      state.mappedDataPC = action.payload;
    },
    setButtonsData: (state, action) => {
      state.buttonsData = action.payload;
    },
    setFilteredButtons: (state, action) => {
      state.filteredButtons = action.payload;
    },
    clearPayload: (state, action) => {
      state.payloadForValue = {};
    },
    setActiveStep: (state, action) => {
      state.activeStep = action.payload;
    },
    clearPayloadForEdit: (state, action) => {
      state.payload = {};
      // state.activeStep = 0;
      state.payloadForValue = {};
      state.payloadForChangeNewValue = {};
      state.generalInformation = [];
      state.buttonsData = [];
      state.filteredButtons = [];
    },
    clearActiveStep: (state, action) => {
      state.activeStep = 0;
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  setPayload,
  setPayloadForValue,
  setPayloadForNewChange,
  setPayloadWholeForNewChange,
  setResponseFromAPI,
  descriptionHeader,
  setPayloadWhole,
  setPayloadWholeForProfit,
  setPayloadForProfit,
  setEditMode,
  setHeaderdataCC,
  setHeaderdataPC,
  setCheckBox,
  setActiveStep,
  setCompanyCodeRowsForPC,
  setProfitCenterTabsArray,
  setGeneralInformation,
  clearPayload,
  clearPayloadForEdit,
  clearActiveStep,
  setProfitCenterMappedData,
  setButtonsData,
  setFilteredButtons,
} = editPayloadSlice.actions;

export const editPayloadReducer = editPayloadSlice.reducer;
