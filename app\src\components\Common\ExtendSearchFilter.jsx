import { useState, forwardRef, useRef, useEffect, useCallback } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tooltip,
  Box,
  Typography,
  Slide,
  FormControl,
  FormControlLabel,
  tooltipClasses,
  RadioGroup,
  IconButton,
  Radio,
  Tabs,
  Tab,
} from "@mui/material";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import styled from "@emotion/styled";
import { doAjax } from "./fetchService";
import { DataGrid } from "@mui/x-data-grid";
import { destination_MaterialMgmt } from "../../destinationVariables";
import { useDispatch, useSelector } from "react-redux";
import { setRequestorPayload } from "../../app/payloadslice";
import ReusableBackDrop from "./ReusableBackDrop";
import ReusableSnackBar from "./ReusableSnackBar";
import { useNavigate } from "react-router-dom";
import { Templates } from "@constant/changeTemplates";
import ReusableIcon from "./ReusableIcon";
import { saveExcel } from "../../functions";
import { setDropDown } from "../../app/dropDownDataSlice";
import { API_CODE, CHANGE_KEYS, ERROR_MESSAGES, LOADING_MESSAGE } from "@constant/enum";
import { colors } from "@constant/colors";
import FilterChangeDropdown from "./ui/dropdown/FilterChangeDropdown";
import useLogger from "../../hooks/useLogger";
import { END_POINTS } from "../../constant/apiEndPoints";
import HandleScrollDropdown from "./ui/dropdown/HandleScrollDropdown";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});

const ExtendSearchFilter = ({
  open,
  onClose,
  parameters,
  templateName,
  allDropDownData,
  name,
  onSearch,
}) => {
  const [selectedValues, setSelectedValues] = useState({});
  const [convertedValues, setConvertedValues] = useState({});
  const regionBasedSalesOrgData = useSelector(
    (state) => state.request.salesOrgDTData
  );
  const [errors, setErrors] = useState({});
  const [blurLoading, setBlurLoading] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [hasSearched, setHasSearched] = useState(false);
  const [successMsg, setSuccessMsg] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [loaderMessage, setLoaderMessage] = useState("");
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [materialOptions, setMaterialOptions] = useState([]);
  const [errorTextMessage, setErrorTextMessage] = useState("");
  const [errorText, setErrorText] = useState(false);
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const RequestId = useSelector(
    (state) => state.request.requestHeader.requestId
  );
  const loadForFetching = useSelector((state) => state.payload.dataLoading);
  const [dropDownData, setDropDownData] = useState({});
  const [skip, setSkip] = useState(0);
  const [limit] = useState(200);
  const [totalMaterialCount, setTotalMaterialCount] = useState(0);
  const [inputState, setInputState] = useState({ code: "", desc: "" });
  const [timerId, setTimerId] = useState(null);
  const dropdownRef = useRef(null);
  const [isLoading, setIsLoading] = useState({
    [CHANGE_KEYS.MATERIAL_NUM]: false,
    [CHANGE_KEYS.PLANT]: false,
    [CHANGE_KEYS.SALES_ORG]: false,
    [CHANGE_KEYS.DIVISION]: false,
    [CHANGE_KEYS.DIST_CHNL]: false,
    [CHANGE_KEYS.WAREHOUSE]: false,
    [CHANGE_KEYS.STORAGE_LOC]: false,
    [CHANGE_KEYS.MRP_CTRLER]: false
  });
  const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
  const [popoverContent, setPopoverContent] = useState("");
  const [isPopoverVisible, setIsPopoverVisible] = useState(false);
  const popoverRef = useRef(null);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState(0);
  const [activeHandler, setActiveHandler] = useState(null);
  const { customError } = useLogger();
  const [rowsOfMaterialData, setRowsOfMaterialData] = useState([]);
  const type = parameters;
  const columnsOfMaterialData = type?.map((item) => ({
    field: item.key,
    headerName: item.key,
    editable: true,
    flex: 2,
  }));

  const handlePasteMaterialData = useCallback((event) => {
    event.preventDefault();
    const clipboardData = event.clipboardData || window.clipboardData;
    const pastedData = clipboardData.getData("Text");

    const newRows = pastedData
      .trim()
      .split("\n")
      .map((row, rowIndex) => {
        const values = row.split("\t");
        const rowData = { id: rowIndex + 1 };
        columnsOfMaterialData.forEach((col, colIndex) => {
          rowData[col.field] = values[colIndex] || "";
        });
        return rowData;
      });
    setRowsOfMaterialData(newRows);
  }, []);

  useEffect(() => {
    if(!open){
      setSelectedValues({});
      setDropDownData({})
    }
  },[open])

  useEffect(() => {
    if (activeTab === 1) {
      document.addEventListener("paste", handlePasteMaterialData);
      return () => {
        document.removeEventListener("paste", handlePasteMaterialData);
      };
    }
  }, [activeTab, handlePasteMaterialData]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    if (activeTab === 1) {
      setActiveHandler("handlePasteMaterialData");
    }
  };

  const NoMaxWidthTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))({
    [`& .${tooltipClasses.tooltip}`]: {
      maxWidth: "none",
    },
  });

  const functions_ExportAsExcelMaterialData = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      columnsOfMaterialData?.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `Material Data`,
        columns: excelColumns,
        rows: rowsOfMaterialData,
      });
    },
  };

  const handlePopoverOpen = (event, content) => {
    setPopoverAnchorEl(event.currentTarget);
    setPopoverContent(content);
    setIsPopoverVisible(true);
  };

  const handlePopoverClose = () => {
    setIsPopoverVisible(false);
  };

  const handleMouseEnterPopover = () => {
    setIsPopoverVisible(true);
  };

  const handleMouseLeavePopover = () => {
    setIsPopoverVisible(false);
  };

  const popoverOpen = Boolean(popoverAnchorEl);
  const popoverId = popoverOpen ? "custom-popover" : undefined;

  const handleSelectionChange = (key, newValue) => {
    setSelectedValues((prev) => ({
      ...prev,
      [key]: newValue,
    }));
    if (key === CHANGE_KEYS.MATERIAL_TYPE) {
      setMaterialOptions([]);
      setInputState({ code: "", desc: "" });
      setSkip(0);
      if(newValue?.[0]?.code){
        getMaterialNo("", true,newValue);
      }
      
    }
    if (newValue.length > 0) {
      setErrors((prev) => ({
        ...prev,
        [key]: "",
      }));
    }
  };

  useEffect(() => {
    setConvertedValues(convertedData(selectedValues));
    dispatch(setRequestorPayload(convertedData(selectedValues)));
  }, [selectedValues]);

  useEffect(() => {
    if (rowsOfMaterialData) {
      let result = convertRowDataToSelectedValues(rowsOfMaterialData);
      setSelectedValues(result);
    }
  }, [rowsOfMaterialData]);

  const handleSelectAll = (key, allOptions) => {
    const allSelected = selectedValues[key]?.length === allOptions.length;
    setSelectedValues((prev) => ({
      ...prev,
      [key]: allSelected ? [] : allOptions,
    }));
    if (!allSelected) {
      setErrors((prev) => ({
        ...prev,
        [key]: "",
      }));
    }
  };

  const convertedData = (data) => {
    const result = {};
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        result[key] = data[key].map((item) => item.code).join(",");
      }
    }
    return result;
  };

  const convertRowDataToSelectedValues = (data) => {
    const result = {};
    data.forEach((row) => {
      Object.keys(row).forEach((key) => {
        if (key !== "id" && row[key].trim() !== "") {
          if (!result[key]) {
            result[key] = [];
          }
          result[key].push({ code: row[key].trim() });
        }
      });
    });
    return result;
  };
  const handleDownload = () => {
    setLoaderMessage(LOADING_MESSAGE.REPORT_LOADING);
    setBlurLoading(true);
    onClose();
    let templateKeys =
      Templates[initialPayload?.TemplateName]?.map((item) => item.key) || [];
    let payload = {};
    if (activeTab === 0) {
      payload = {
        materialDetails: [
          templateKeys.reduce((acc, key) => {
            acc[key] = convertedValues?.[key] ? convertedValues?.[key] : "";
            return acc;
          }, {}),
        ],
        templateHeaders: "",
        requestId: RequestId,
        templateName: initialPayload?.TemplateName
          ? initialPayload.TemplateName
          : "",
        dtName: "MDG_MAT_CHANGE_TEMPLATE",
        version: "v4",
        rolePrefix: "",
      };
    } else {
      payload = {
        materialDetails: [
          templateKeys.reduce((acc, key) => {
            acc[key] =
              rowsOfMaterialData
                .map((row) => row[key]?.trim())
                .filter((value) => value !== "")
                .join(",") || "";
            return acc;
          }, {}),
        ],
        templateHeaders: "",
        requestId: RequestId,
        templateName: initialPayload?.TemplateName
          ? initialPayload.TemplateName
          : "",
        dtName: "MDG_MAT_CHANGE_TEMPLATE",
        version: "v4",
        rolePrefix: "",
      };
    }
    const hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute(
        "download",
        `${initialPayload.TemplateName}_Mass Change.xlsx`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");

      setSuccessMsg(true);
      setMessageDialogMessage(
        `${initialPayload.TemplateName}_Mass Change.xlsx has been downloaded successfully.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(`/requestBench`);
      }, 2400);
    };
    const hError = () => {
      setBlurLoading(false);
    };
    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS.EXCEL.DOWNLOAD_EXCEL_WITH_DATA}`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const handleDownloadDialogClose = () => {
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
  };

  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };

  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      // handleEmailDownload();
      handleDownloadDialogClose();
    }
  };

  useEffect(() => {
    if(selectedValues?.[CHANGE_KEYS.MATERIAL_TYPE]?.code){
      getMaterialNo("", true);
    }
  }, []);

  const handleMatInputChange = (e) => {
    const inputValue = e.target.value?.toUpperCase();
    setInputState({ code: inputValue, desc: "" });
    setSkip(0);
    if (timerId) {
      clearTimeout(timerId);
    }
    const newTimerId = setTimeout(() => {
      if(selectedValues?.[CHANGE_KEYS.MATERIAL_TYPE]?.[0]?.code){
        getMaterialNo(inputValue, true, selectedValues?.[CHANGE_KEYS.MATERIAL_TYPE]?.code);
      }
    }, 500);
    setTimerId(newTimerId);
  };

  const getMaterialNo = (value = "", reset = false, materialType) => {
    setIsLoading(prev => ({ ...prev, [CHANGE_KEYS.MATERIAL_NUM]: true }));
    const payload = {
      matlType: (materialType?.[0]?.code || selectedValues?.[CHANGE_KEYS.MATERIAL_TYPE]?.[0]?.code) ?? "",
      materialNo: value ?? "",
      top: limit,
      skip: reset ? 0 : skip,
      salesOrg:
        regionBasedSalesOrgData?.uniqueSalesOrgList
          ?.map((item) => item.code)
          ?.join("$^$") || "",
    };
    
    const hSuccess = (data) => {
      if(data?.statusCode === API_CODE.STATUS_200){
        setTotalMaterialCount(data?.count || 0);
        if (reset) {
          setMaterialOptions(data?.body || []);
          setDropDownData((prev) => ({
            ...prev,
            [CHANGE_KEYS.MATERIAL_NUM]: data.body || [],
          }));
        } else {
          setMaterialOptions((prevOptions) => [...prevOptions, ...(data?.body || [])]);
          setDropDownData((prev) => ({
            ...prev,
            [CHANGE_KEYS.MATERIAL_NUM]: [
              ...(prev[CHANGE_KEYS.MATERIAL_NUM] || []),
              ...(data.body || []),
            ],
          }));
        }
        setIsLoading(prev => ({ ...prev, [CHANGE_KEYS.MATERIAL_NUM]: false }));
      }
    };
    
    const hError = (error) => {
      customError(error);
      setIsLoading(prev => ({ ...prev, [CHANGE_KEYS.MATERIAL_NUM]: false }));
    };
    
    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS?.DATA?.GET_SEARCH_PARAMS_MATERIAL_NO}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  useEffect(() => {
    if (skip > 0) {
      getMaterialNo(inputState?.code, false);
    }
  }, [skip]);

  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    if (scrollTop + clientHeight >= scrollHeight - 10 && 
        !isLoading[CHANGE_KEYS.MATERIAL_NUM] &&
        dropDownData?.[CHANGE_KEYS.MATERIAL_NUM]?.length < totalMaterialCount) {
      setSkip((prevSkip) => prevSkip + limit);
    }
  };

  useEffect(() => {
    parameters?.forEach((param) => {
      if (
        param.key === CHANGE_KEYS?.MRP_CTRLER &&
        allDropDownData?.["MrpCtrler"]
      ) {
        setDropDownData((prev) => ({
          ...prev,
          [CHANGE_KEYS?.MRP_CTRLER]: allDropDownData["MrpCtrler"],
        }));
      } else if (
        [
          CHANGE_KEYS?.PLANT,
          CHANGE_KEYS?.SALES_ORG,
          CHANGE_KEYS?.WAREHOUSE,
        ].includes(param.key)
      ) {
        fetchOrgLookupData(param.key);
      }
    });
  }, []);

  useEffect(() => {
    fetchDistChnlLookupData();
  }, []);

  useEffect(() => {
    if (selectedValues[CHANGE_KEYS?.SALES_ORG]) {
      fetchDistChnlLookupData();
    }
  }, [selectedValues[CHANGE_KEYS?.SALES_ORG]]);

  useEffect(() => {
    if (selectedValues[CHANGE_KEYS?.PLANT]) {
      fetchStoreLocLookupData();
    }
  }, [selectedValues[CHANGE_KEYS?.PLANT]]);

  const handleOkClick = async () => {
    onSearch(selectedValues, "0", (results) => {
      setSearchResults(results);
      setHasSearched(true);
      if (results && results.length > 0) {
        onClose();
      }
    });
  };

  const fetchDistChnlLookupData = () => {
    setIsLoading(prev => ({ ...prev, [CHANGE_KEYS.DIST_CHNL]: true }));
    let payload = {
      salesOrg: selectedValues[CHANGE_KEYS.SALES_ORG]
        ? selectedValues[CHANGE_KEYS.SALES_ORG]
            ?.map((item) => item?.code)
            .join("$^$")
        : "",
    };
    const successHandler = (data) => {
      setDropDownData((prev) => ({
        ...prev,
        [CHANGE_KEYS.DIST_CHNL]: data.body,
      }));
      dispatch(
        setDropDown({
          keyName: "StoreLoc",
          data: dropDownData?.[CHANGE_KEYS.DIST_CHNL],
        })
      );
      setIsLoading(prev => ({ ...prev, [CHANGE_KEYS.DIST_CHNL]: false }));
    };
    const errorHandler = (error) => {
      console.error(error);
      setIsLoading(prev => ({ ...prev, [CHANGE_KEYS.DIST_CHNL]: false }));
    };
    doAjax(
      `/${destination_MaterialMgmt}/data/getDistrChan`,
      "post",
      successHandler,
      errorHandler,
      payload
    );
  };

  const fetchStoreLocLookupData = () => {
    setIsLoading(prev => ({ ...prev, [CHANGE_KEYS.STORAGE_LOC]: true }));
    let payload = {
      salesOrg: selectedValues[CHANGE_KEYS.SALES_ORG]
        ? selectedValues[CHANGE_KEYS.SALES_ORG]
            ?.map((item) => item?.code)
            .join("$^$")
        : "",
    };
    const successHandler = (data) => {
      setDropDownData((prev) => ({
        ...prev,
        [CHANGE_KEYS.STORAGE_LOC]: data.body,
      }));
      dispatch(
        setDropDown({
          keyName: "DistrChan",
          data: dropDownData?.[CHANGE_KEYS.STORAGE_LOC],
        })
      );
      setIsLoading(prev => ({ ...prev, [CHANGE_KEYS.STORAGE_LOC]: false }));
    };
    const errorHandler = (error) => {
      console.error(error);
      setIsLoading(prev => ({ ...prev, [CHANGE_KEYS.STORAGE_LOC]: false }));
    };
    const commaSeparatedValues = selectedValues[CHANGE_KEYS.PLANT]
      ?.map((item) => item.code)
      .join(",");
    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS.DATA.GET_STORAGE_LOCATION_SET_BASED_ON_PLANT}`,
      "post",
      successHandler,
      errorHandler,
      {plant: commaSeparatedValues}
    );
  };

  const fetchOrgLookupData = (field) => {
    setIsLoading(prev => ({ ...prev, [field]: true }));
    const endpoints = {
      [CHANGE_KEYS.PLANT]: "/getPlant",
      [CHANGE_KEYS.SALES_ORG]: "/getSalesOrg",
      [CHANGE_KEYS.WAREHOUSE]: "/getWareHouseNo",
    };
    const successHandler = (data) => {
      setDropDownData((prev) => ({ ...prev, [field]: data.body }));
      dispatch(setDropDown({ keyName: field, data: data?.body }));
      setIsLoading(prev => ({ ...prev, [field]: false }));
    };
    const errorHandler = (error) => {
      console.log(error);
      setIsLoading(prev => ({ ...prev, [field]: false }));
    };
    doAjax(
      `/${destination_MaterialMgmt}/data${endpoints[field]}`,
      "get",
      successHandler,
      errorHandler
    );
  };

  const renderAutocomplete = (param) => {
    const formatOptionLabel = (option) => {
      if (option.code && option.desc) {
        return `${option.code} - ${option.desc}`;
      }
      return option.code || "";
    };
    if(param.key === CHANGE_KEYS.MATERIAL_TYPE) {
      return (
        <FilterChangeDropdown
          param={param}
          dropDownData={{ [CHANGE_KEYS.MATERIAL_TYPE]: param.options }}
          allDropDownData={allDropDownData}
          selectedValues={selectedValues}
          inputState={inputState}
          handleSelectAll={handleSelectAll}
          handleSelectionChange={handleSelectionChange}
          dropdownRef={dropdownRef}
          errors={errors}
          formatOptionLabel={formatOptionLabel}
          handlePopoverOpen={handlePopoverOpen}
          handlePopoverClose={handlePopoverClose}
          handleMouseEnterPopover={handleMouseEnterPopover}
          handleMouseLeavePopover={handleMouseLeavePopover}
          isPopoverVisible={isPopoverVisible}
          popoverId={popoverId}
          popoverAnchorEl={popoverAnchorEl}
          popoverRef={popoverRef}
          popoverContent={popoverContent}
          isLoading={isLoading[param.key]}
          singleSelect={true}
        />
      );
    }
    else if (param.key === CHANGE_KEYS.MATERIAL_NUM) {
      return (
        <HandleScrollDropdown
          param={param}
          dropDownData={dropDownData}
          allDropDownData={allDropDownData}
          selectedValues={selectedValues}
          inputState={inputState}
          handleSelectAll={handleSelectAll}
          handleSelectionChange={handleSelectionChange}
          handleMatInputChange={handleMatInputChange}
          handleScroll={handleScroll}
          dropdownRef={dropdownRef}
          errors={errors}
          formatOptionLabel={formatOptionLabel}
          handlePopoverOpen={handlePopoverOpen}
          handlePopoverClose={handlePopoverClose}
          handleMouseEnterPopover={handleMouseEnterPopover}
          handleMouseLeavePopover={handleMouseLeavePopover}
          isPopoverVisible={isPopoverVisible}
          popoverId={popoverId}
          popoverAnchorEl={popoverAnchorEl}
          popoverRef={popoverRef}
          popoverContent={popoverContent}
          isMaterialNum={true}
          isLoading={isLoading[CHANGE_KEYS.MATERIAL_NUM]}
          hasMoreItems={totalMaterialCount > (dropDownData?.[CHANGE_KEYS.MATERIAL_NUM]?.length || 0)}
          totalCount={totalMaterialCount}
          loadedCount={dropDownData?.[CHANGE_KEYS.MATERIAL_NUM]?.length || 0}
        />
      );
    } else if (
      param.key === CHANGE_KEYS.PLANT ||
      param.key === CHANGE_KEYS.SALES_ORG ||
      param.key === CHANGE_KEYS.MRP_CTRLER ||
      param.key === CHANGE_KEYS.DIVISION ||
      param.key === CHANGE_KEYS.WAREHOUSE ||
      param.key === CHANGE_KEYS.DIST_CHNL ||
      param.key === CHANGE_KEYS.STORAGE_LOC
    ) {
      return (
        <FilterChangeDropdown
          param={param}
          dropDownData={dropDownData}
          allDropDownData={allDropDownData}
          selectedValues={selectedValues}
          inputState={inputState}
          handleSelectAll={handleSelectAll}
          handleSelectionChange={handleSelectionChange}
          dropdownRef={dropdownRef}
          errors={errors}
          formatOptionLabel={formatOptionLabel}
          handlePopoverOpen={handlePopoverOpen}
          handlePopoverClose={handlePopoverClose}
          handleMouseEnterPopover={handleMouseEnterPopover}
          handleMouseLeavePopover={handleMouseLeavePopover}
          isPopoverVisible={isPopoverVisible}
          popoverId={popoverId}
          popoverAnchorEl={popoverAnchorEl}
          popoverRef={popoverRef}
          popoverContent={popoverContent}
          isLoading={isLoading[param.key]}
        />
      );
    }
  };

  const isAnyValueSelected = () => {
    return Object.values(selectedValues).some(
      (value) => Array.isArray(value) && value.length > 0
    );
  };
  return (
    <>
      <Dialog
        open={open}
        TransitionComponent={Transition}
        keepMounted
        onClose={() => {}}
        maxWidth={name === "Extend" ? "lg" : "xs"}
        fullWidth
      >
        <Box
          sx={{
            backgroundColor: "#e3f2fd",
            padding: "1rem 1.5rem",
            display: "flex",
            alignItems: "center",
          }}
        >
          <FeedOutlinedIcon color="primary" sx={{ marginRight: "0.5rem" }} />
          <Typography variant="h6" component="div" color="primary">
            {templateName} Search Filter(s)
          </Typography>
        </Box>

        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          sx={{ borderBottom: 1, borderColor: "divider" }}
        >
          <Tab label="Search Filter" />
          {name !== "Extend" && (
            <Tab
              label={
                <Box display="flex" alignItems="center">
                  <span>Copy Material</span>
                  {activeTab === 1 && (
                    <Tooltip title="Export Table">
                      <IconButton
                        sx={{ padding: "4px", width: "28px", height: "28px" }}
                        onClick={
                          functions_ExportAsExcelMaterialData.convertJsonToExcel
                        }
                      >
                        <ReusableIcon iconName="IosShare" />
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>
              }
            />
          )}
        </Tabs>

        <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
          {activeTab === 0 && (
            <>
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "repeat(3, 1fr)",
                  gap: 2,
                }}
              >
                {parameters?.map((param) => (
                  <Box key={param.key} sx={{ marginBottom: "1rem" }}>
                    {renderAutocomplete(param)}
                  </Box>
                ))}
              </Box>
            </>
          )}
          {activeTab === 1 && (
            <Box>
              <DataGrid
                style={{ height: 400, width: "100%" }}
                rows={rowsOfMaterialData}
                columns={columnsOfMaterialData}
              />
            </Box>
          )}
          {errorText && (
            <Typography variant="h6" color={colors?.error?.dark}>
              * {errorTextMessage}
            </Typography>
          )}
          <ReusableBackDrop blurLoading={loadForFetching} />
        </DialogContent>
        <DialogActions
          sx={{
            padding: "0.5rem 1.5rem",
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          <div>
            <Typography variant="h6" color={colors?.error?.dark}>
              {hasSearched && searchResults?.length === 0
                ? ERROR_MESSAGES.DATA_NOT_FOUND_FOR_SEARCH
                : ""}
            </Typography>
          </div>

          <div style={{ display: "flex", gap: "8px" }}>
            <Button
              onClick={onClose}
              color="error"
              variant="outlined"
              sx={{
                height: 36,
                minWidth: "3.5rem",
                textTransform: "none",
                borderColor: "#cc3300",
                fontWeight: 500,
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleOkClick}
              variant="contained"
              disabled={!isAnyValueSelected()}
              sx={{
                height: 36,
                minWidth: "3.5rem",
                backgroundColor: "#3B30C8",
                textTransform: "none",
                fontWeight: 500,
                "&:hover": {
                  backgroundColor: "#2c278f",
                },
              }}
            >
              OK
            </Button>
          </div>
        </DialogActions>
      </Dialog>
      <Dialog open={openDownloadDialog} onClose={handleDownloadDialogClose}>
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            display: "flex",
          }}
        >
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            Select Download Option
          </Typography>
        </DialogTitle>
        <DialogContent>
          <FormControl>
            <RadioGroup
              row
              aria-labelledby="demo-row-radio-buttons-group-label"
              name="row-radio-buttons-group"
              value={downloadType}
              onChange={handleDownloadTypeChange}
            >
              <NoMaxWidthTooltip
                arrow
                placement="bottom"
                title={
                  <span
                    style={{
                      whiteSpace: "nowrap",
                      fontSize: "12px",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }}
                  >
                    Here Excel will be downloaded
                  </span>
                }
              >
                <FormControlLabel
                  value="systemGenerated"
                  control={<Radio />}
                  label="System-Generated"
                />
              </NoMaxWidthTooltip>

              <NoMaxWidthTooltip
                arrow
                placement="bottom"
                title={
                  <span
                    style={{
                      whiteSpace: "nowrap",
                      fontSize: "12px",

                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }}
                  >
                    Here Excel will be sent to your email
                  </span>
                }
              >
                <FormControlLabel
                  value="mailGenerated"
                  control={<Radio />}
                  label="Mail-Generated"
                />
              </NoMaxWidthTooltip>
            </RadioGroup>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button variant="contained" onClick={onDownloadTypeChange}>
            OK
          </Button>
        </DialogActions>
      </Dialog>
      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />
      {successMsg && (
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={messageDialogMessage}
          alertType={alertType}
          handleSnackBarClose={handleSnackBarClose}
        />
      )}
    </>
  );
};



export default ExtendSearchFilter;
