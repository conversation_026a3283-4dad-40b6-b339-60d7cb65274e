import PushPinOutlinedIcon from "@mui/icons-material/PushPinOutlined";
import {
  Box,
  Button,
  Grid,
  IconButton,
  Menu,
  MenuItem,
  TextField,
  Typography,
  Tabs,
  Tab,
  Stack,
  Skeleton,
  Tooltip,
} from "@mui/material";
import React, { useEffect } from "react";
import { useState } from "react";
import ReusableDialog from "../Common/ReusableDialog";
import { useDispatch, useSelector } from "react-redux";
import { doAjax } from "./fetchService";
import ReusableSnackBar from "./ReusableSnackBar";
import ReusableIcon from "./ReusableIcon";
import { destination_Admin } from "../../destinationVariables";
import { commonFilterUpdate, commonFilterClear } from "../../app/commonFilterSlice";
import moment from 'moment';
import { colors } from "@constant/colors";
import { FIELD_NAME_MAPPINGS } from "../../constant/enum";
import useLang from "@hooks/useLang";
import { useGetPresetFilterQuery } from '@api/admin/adminsApi';
const getDisplayFieldName = (fieldName) => {
  return FIELD_NAME_MAPPINGS[fieldName] || fieldName;
};

export default React.memo(
  function ReusablePreset({
  moduleName,
  handleSearch = () => {},
  disabled = false,
  onPresetActiveChange = () => {},
  onClearPreset = () => {}
}) {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [presetName, setPresetName] = useState(null);
  const [presets, setPresets] = useState(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isFirstRender, setisFirstRender] = useState(true); 
  const { t } = useLang();

  const commonFilter = useSelector(
    (state) => state.commonFilter[moduleName]
  );
  const dispatch = useDispatch()
  const open = Boolean(anchorEl);
  const handleClose = () => {
    setPresetName("");
    setAnchorEl(null);
    handleSnackbarClose();
  };
  const filterData = useSelector((state) => state.commonFilterUpdate);
  const userData = useSelector((state) => state?.userManagement?.userData);
  const { data:listOfFiltersData,refetch } = useGetPresetFilterQuery({ moduleName, emailId: userData?.emailId });
  
  useEffect(() => {
    if (isFirstRender) {
      setisFirstRender(false);
      return;
    }
    
    if (isLoaded) {
      const timer = setTimeout(() => {
        handleSearch();
        setIsLoaded(false);
      }, 50);
      
      return () => clearTimeout(timer);
    }
  }, [commonFilter]);

  /**
 * Checks if a preset filter is pinned or not.
 * @param - no params needed.
 * @returns  set the preset as per pinned status.
 */

  const setPinnedPreset = () => {
    setIsLoaded(true);
    let hSuccess = (data) => {
      if(data?.length > 0){
      setPresets(data?.reverse());
      var result = data?.filter((obj) => {
        return obj.defaultPin === 1;
      });
   
        if (result.length > 0) {
          setActivePreset(result[0].id);
          setSelectedPreset(result[0].id);
          
          let jsonPreset = JSON.parse(result[0].presetDescription)
          
          if (jsonPreset.createdOn) {
            const presentDate = new Date();
            const backDate = new Date();
            backDate.setDate(backDate.getDate() - 7);
            
            try {
              let startDate = backDate;
              let endDate = presentDate;
              
              if (typeof jsonPreset.createdOn === 'string') {
                if (jsonPreset.createdOn.includes('[') || jsonPreset.createdOn.includes('"')) {
                  const cleanValue = jsonPreset.createdOn.replace(/\\"/g, '"').replace(/\\\\/g, '\\');
                  try {
                    const parsedDates = JSON.parse(cleanValue);
                    if (Array.isArray(parsedDates) && parsedDates.length === 2) {
                      startDate = new Date(parsedDates[0]);
                      endDate = new Date(parsedDates[1]);
                    }
                  } catch (e) {
                    console.log("Failed to parse date string:", e);
                  }
                }
              } else if (Array.isArray(jsonPreset.createdOn) && jsonPreset.createdOn.length === 2) {
                startDate = new Date(jsonPreset.createdOn[0]);
                endDate = new Date(jsonPreset.createdOn[1]);
              }
              
              if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
                jsonPreset.createdOn = [startDate, endDate];
              } else {
                jsonPreset.createdOn = [backDate, presentDate];
              }
            } catch (e) {
              console.error("Error processing date:", e);
              jsonPreset.createdOn = [backDate, presentDate];
            }
          }
          
          dispatch(commonFilterUpdate({ module: moduleName, filterData: jsonPreset }))
        }
      }
        setIsLoaded(false);
    };
    hSuccess(listOfFiltersData);
  };

  useEffect(() => {
    setPinnedPreset();
  },[listOfFiltersData])

  const [SnackbarOpen, setSnackbarOpen] = useState(false);
  const handleSnackBarOpen = () => {
    setSnackbarOpen(true);
  };
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };
  const saveFilterPreset = () => {
    setSkeletonLoader(true);
    const formData = new FormData();
    formData.append("userId", userData?.emailId);
    formData.append("filterName", presetName);
    formData.append("module", moduleName);
    formData.append("presetDescription", JSON.stringify(commonFilter));

    let hSuccess = (data) => {
      refetch();

      setdownloadError(true);

      setMessageDialogMessage(`Preset Filter ${presetName} Saved successfully`);
      handleSnackBarOpen();
      setSkeletonLoader(false);

    };
    let hError = (err) => {
      setdownloadError(true);
      setMessageDialogTitle("Error");
      setMessageDialogMessage(`Preset Filter ${presetName} Saving Failed `);
      setMessageDialogSeverity("danger");
      handleMessageDialogClickOpen();
    };
    doAjax(
      `/${destination_Admin}/presetFilter/savePresetFilter`,
      "postformdata",
      hSuccess,
      hError,
      formData
    );
  };
  const setFilterPreset = (preset) => {
    try {
      setActivePreset(preset.id);
      
      let jsonPreset = JSON.parse(preset.presetDescription);
      
      if (jsonPreset.createdOn) {
        const presentDate = new Date();
        const backDate = new Date();
        backDate.setDate(backDate.getDate() - 7);
        
        try {
          let startDate = backDate;
          let endDate = presentDate;
          
          if (typeof jsonPreset.createdOn === 'string') {
            if (jsonPreset.createdOn.includes('[') || jsonPreset.createdOn.includes('"')) {
              const cleanValue = jsonPreset.createdOn.replace(/\\"/g, '"').replace(/\\\\/g, '\\');
              try {
                const parsedDates = JSON.parse(cleanValue);
                if (Array.isArray(parsedDates) && parsedDates.length === 2) {
                  startDate = new Date(parsedDates[0]);
                  endDate = new Date(parsedDates[1]);
                }
              } catch (e) {
                console.log("Failed to parse date string:", e);
              }
            }
          } else if (Array.isArray(jsonPreset.createdOn) && jsonPreset.createdOn.length === 2) {
            startDate = new Date(jsonPreset.createdOn[0]);
            endDate = new Date(jsonPreset.createdOn[1]);
          }
          
          if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
            jsonPreset.createdOn = [startDate, endDate];
          } else {
            jsonPreset.createdOn = [backDate, presentDate];
          }
        } catch (e) {
          console.error("Error processing date:", e);
          jsonPreset.createdOn = [backDate, presentDate];
        }
      }
      
      dispatch(commonFilterUpdate({ module: moduleName, filterData: jsonPreset }));
    } catch (error) {
      console.error("Error setting filter preset:", error);
    }
  }
  const setFilterDefault = (defaultPin, id) => {
    const formData = new FormData();
    formData.append("filterId", id);
    formData.append("defaultPin", defaultPin ? 0 : 1);
    formData.append("module", moduleName);
    formData.append("userId", userData?.emailId);
    let hSuccess = (data) => refetch();
    let hError = () => { };
    doAjax(
      `/${destination_Admin}/presetFilter/pinFilter`,
      "putformdata",
      hSuccess,
      hError,
      formData
    );
  };
  const fetchPresetList = () => {
    setSkeletonLoader(true);

    let hSuccess = (data) => {
      setPresets(data.reverse());
      setSkeletonLoader(false);
    };
    let hError = () => {
      setSkeletonLoader(false);
    };
    doAjax(
      `/${destination_Admin}/presetFilter/listOfFilters/${moduleName}/${userData?.emailId}`,
      "get",
      hSuccess,
      hError
    );
  };
  const [skeletonLoader, setSkeletonLoader] = useState(false);
  const deletePreset = (id) => {
    setSkeletonLoader(true);
    let hSuccess = (data) => {
      fetchPresetList();
    };
    let hError = (data) => {
      fetchPresetList();
    };
    doAjax(
      `/${destination_Admin}/presetFilter/delete/id/${id}`,
      "delete",
      hSuccess,
      hError
    );
  };

  const handleClearPreset = () => {
    setActivePreset(null);
    setSelectedPreset(null);
    
    setIsLoaded(true);
    
    dispatch(commonFilterClear({ module: moduleName }));
    
    onClearPreset();
    
    setdownloadError(true);
    setMessageDialogMessage(`Preset Filter Cleared successfully`);
    handleSnackBarOpen();
    
    handleClose();
  };

  useEffect(() => {
    setPinnedPreset();
  }, []);

  const [selectedTab, setSelectedTab] = useState("SelectPreFilter");
  const handleChange = (event, newValue) => {
    setSelectedTab(newValue);
  };
  const [downloadError, setdownloadError] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
    handleClose();
  };
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const [selectedPreset, setSelectedPreset] = useState(null);
  const [activePreset, setActivePreset] = useState(null);
  
  useEffect(() => {
    onPresetActiveChange(!!activePreset);
  }, [activePreset, onPresetActiveChange]);

  const handlePresetClick = (preset) => {
    setSelectedPreset(preset.id);
    setActivePreset(preset.id);
    setFilterPreset(preset);
    setIsLoaded(true)
   
    setdownloadError(true);
    setMessageDialogMessage(`Preset Filter Applied successfully`);
    handleSnackBarOpen();
    
    handleClose();
  };

  return (
    <div className="reusable-preset">
      <Button
        id="demo-customized-button"
        aria-controls={open ? "demo-customized-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        variant="outlined"
        disableElevation
        onClick={handleClick}
        endIcon={<ReusableIcon iconName="KeyboardArrowDown" />}
        startIcon={activePreset && <ReusableIcon iconName="Check" />}
        sx={{
          position: 'relative',
          ...(activePreset && {
            borderColor: colors?.primary?.main,
            color: colors?.primary?.main,
            fontWeight: 'bold'
          })
        }}
      >
        {activePreset ? t("Preset Active") : t("Preset Filter")}
        {activePreset && (
          <Box
            sx={{
              position: 'absolute',
              top: -5,
              right: -5,
              width: 10,
              height: 10,
              borderRadius: '50%',
              backgroundColor: colors?.primary?.main,
            }}
          />
        )}
      </Button>
      <Menu
        MenuListProps={{
          style: {
            paddingTop: "0px",
            paddingBottom: "0px",
            position: "auto",
          },
        }}
        id="demo-customized-menu"
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
      >
        <Box
          sx={{
            width: "500px",
            overflowY: "hidden",
          }}
        >
          <Box
            sx={{ borderBottom: 1, width: "inherit", borderColor: "divider" }}
          >
            <Grid container>
              <Grid item md={12}>
                <Tabs
                  value={selectedTab}
                  onChange={handleChange}
                >
                  {
                    <Tab
                      label={t("Select Filter")}
                      value="SelectPreFilter"
                      sx={{
                        fontWeight: "700",
                        fontSize: "14px",
                        textTransform: "none",
                      }}
                    />
                  }
                  {
                    <Tab
                      label={t("Save Filter")}
                      value="SaveFilter"
                      sx={{
                        fontWeight: "700",
                        fontSize: "14px",
                        textTransform: "none",
                      }}
                    />
                  }
                </Tabs>
              </Grid>
            </Grid>
          </Box>
          {selectedTab === "SelectPreFilter" &&
            (skeletonLoader ? (
              <>
                <Box
                  sx={{
                    minHeight: "10.1rem",
                    maxHeight: "24.8vh",
                    overflowY: "scroll",
                  }}
                >
                  <MenuItem
                    PaperProps={{
                      style: {
                        minHeight: "auto",
                      },
                    }}
                    sx={{
                      fontWeight: 500,
                      padding: "11px",
                      "&:hover": {
                        backgroundColor: "#EAE9FF",
                        color: "#3B30C8",
                      },
                    }}
                  >
                    <Grid container>
                      <Grid item xs={12}>
                        <Skeleton variant="text" />
                      </Grid>
                      <Grid item xs={12}>
                        <Skeleton variant="text" />
                      </Grid>
                    </Grid>
                  </MenuItem>
                </Box>
                <Box
                  sx={{
                    borderTop: 1,
                    width: "inherit",
                    borderColor: "divider",
                  }}
                >
                  <MenuItem sx={{ display: "flex", justifyContent: "right" }}>
                    <Button
                      onClick={handleClose}
                      variant="outlined"
                      sx={{
                        textTransform: "none",
                        fontWeight: "bold",
                      }}
                      style={{
                        height: 40,
                        minWidth: "6rem",
                      }}
                    >
                      {t("Cancel")}
                    </Button>
                  </MenuItem>
                </Box>
              </>
            ) : (
              <>
                <Box
                  sx={{
                    minHeight: "10.1rem",
                    maxHeight: "24.8vh",
                    overflowY: "scroll",
                  }}
                >
                  {presets &&
                    presets?.map((preset) => (
                      <MenuItem
                        key={preset.id}
                        PaperProps={{
                          style: {
                            minHeight: "auto",
                          },
                        }}
                        sx={{
                          fontWeight: 500,
                          padding: "11px",
                          color: selectedPreset === preset.id ? "#3B30C8" : "inherit",
                          "&:hover": {
                            color: "#3B30C8",
                          },
                        }}
                        onClick={() => handlePresetClick(preset)}
                      >
                        <Grid container spacing={1}>
                          <Grid item xs={12} display="flex" justifyContent="space-between" alignItems="center">
                            <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                              {preset.filterName}
                            </Typography>
                            <Box>
                              <IconButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setFilterDefault(preset.defaultPin, preset.id);
                                }}
                                size="small"
                                sx={{ padding: 0, mr: 1 }}
                              >
                                <ReusableIcon iconName="PushPin" isSelected={preset.defaultPin} />
                              </IconButton>
                              <IconButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  deletePreset(preset.id);
                                }}
                                size="small"
                                sx={{ padding: 0 }}
                              >
                                <ReusableIcon iconName="Delete" iconColor="red" />
                              </IconButton>
                            </Box>
                          </Grid>
                          <Grid item xs={12}>
                            {(() => {
                              const presetData = JSON.parse(preset.presetDescription);
                              const activeFilters = Object.entries(presetData)
                                .filter(([key, value]) => 
                                  value && 
                                  value !== "" && 
                                  !(typeof value === 'object' && Object.values(value).every(v => !v))
                                );

                              return activeFilters.length > 0 && (
                                <Stack direction="row" spacing={0.5} flexWrap="wrap" gap={0.5}>
                                  {activeFilters.map(([key, value], index) => (
                                    <Box
                                      key={index}
                                      sx={{
                                        backgroundColor: '#EAE9FF',
                                        borderRadius: '4px',
                                        padding: '2px 8px',
                                        fontSize: '0.75rem',
                                        color: '#3B30C8',
                                        display: 'inline-flex',
                                        alignItems: 'center'
                                      }}
                                    >
                                      <Typography
                                        component="span"
                                        sx={{
                                          fontSize: '0.75rem',
                                          fontWeight: 500,
                                        }}
                                      >
                                        {`${getDisplayFieldName(key)}: `}
                                        <span style={{ fontWeight: 400 }}>
                                          {(() => {
                                            if (typeof value === 'object') {
                                              if (key === 'createdOn' && Array.isArray(value) && value.length === 2) {
                                                const startDate = moment(value[0]).format('DD MMM YYYY');
                                                const endDate = moment(value[1]).format('DD MMM YYYY');
                                                return `${startDate} - ${endDate}`;
                                              }
                                              return value.code || value.desc || JSON.stringify(value);
                                            }
                                            
                                            if (typeof value === 'string' && value.includes('$^$')) {
                                              return value.split('$^$').join(', ');
                                            }
                                            
                                            if (key === 'createdOn' && typeof value === 'string') {
                                              const dateRegex = /"([\d-]+T[\d:.]+Z)","([\d-]+T[\d:.]+Z)"/;
                                              const matches = value.match(dateRegex);
                                              
                                              if (matches && matches.length === 3) {
                                                const startDate = moment(matches[1]).format('DD MMM YYYY');
                                                const endDate = moment(matches[2]).format('DD MMM YYYY');
                                                return `${startDate} - ${endDate}`;
                                              }
                                              
                                              try {
                                                const cleanValue = value.replace(/\\"/g, '"').replace(/\\\\/g, '\\');
                                                const parsedValue = JSON.parse(cleanValue);
                                                
                                                if (Array.isArray(parsedValue) && parsedValue.length === 2) {
                                                  const startDate = moment(parsedValue[0]).format('DD MMM YYYY');
                                                  const endDate = moment(parsedValue[1]).format('DD MMM YYYY');
                                                  return `${startDate} - ${endDate}`;
                                                }
                                              } catch (e) {

                                              }
                                            }
                                            
                                            if (typeof value === 'string' && (value.includes('[') || value.includes('"'))) {
                                              try {
                                                const cleanValue = value.replace(/\\"/g, '"').replace(/\\\\/g, '\\');
                                                const parsedValue = JSON.parse(cleanValue);
                                                
                                                if (Array.isArray(parsedValue) && parsedValue.length === 2 && 
                                                    typeof parsedValue[0] === 'string' && parsedValue[0].includes('T')) {
                                                  const startDate = moment(parsedValue[0]).format('DD MMM YYYY');
                                                  const endDate = moment(parsedValue[1]).format('DD MMM YYYY');
                                                  return `${startDate} - ${endDate}`;
                                                }
                                                return value;
                                              } catch (e) {

                                                return value;
                                              }
                                            }
                                            
                                            return value;
                                          })()}
                                        </span>
                                      </Typography>
                                    </Box>
                                  ))}
                                </Stack>
                              );
                            })()}
                          </Grid>
                        </Grid>
                      </MenuItem>
                    ))}
                </Box>
                <Box
                  sx={{
                    borderTop: 1,
                    width: "inherit",
                    borderColor: "divider",
                  }}
                >
                  <MenuItem sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
                    <Button
                      onClick={handleClearPreset}
                      variant="outlined"
                      color="error"
                      sx={{
                        textTransform: "none",
                        fontWeight: "bold",
                      }}
                      style={{
                        height: 40,
                        minWidth: "6rem",
                      }}
                      disabled={!activePreset}
                    >
                      {t("Clear Preset")}
                    </Button>
                    <Button
                      onClick={handleClose}
                      variant="outlined"
                      sx={{
                        textTransform: "none",
                        fontWeight: "bold",
                      }}
                      style={{
                        height: 40,
                        minWidth: "6rem",
                      }}
                    >
                      {t("Cancel")}
                    </Button>
                  </MenuItem>
                  {downloadError && (
                    <ReusableSnackBar
                      openSnackBar={SnackbarOpen}
                      handleSnackbarClose={handleSnackbarClose}
                      alertMsg={messageDialogMessage}
                    />
                  )}
                  {downloadError && (
                    <ReusableDialog
                      dialogState={openMessageDialog}
                      openReusableDialog={handleMessageDialogClickOpen}
                      closeReusableDialog={handleMessageDialogClose}
                      dialogTitle={messageDialogTitle}
                      dialogMessage={messageDialogMessage}
                      handleDialogConfirm={handleMessageDialogClose}
                      dialogOkText={"OK"}
                      dialogSeverity={messageDialogSeverity}
                    />
                  )}
                </Box>
              </>
            ))}
          {selectedTab === "SaveFilter" &&
            (skeletonLoader ? (
              <Box
                sx={{
                  minHeight: "13.5rem",
                  maxHeight: "30vh",
                  overflow: "hidden",
                }}
              >
                <Stack mt={6} ml={6}>
                  <Skeleton variant="text" width="400px" />
                  <Skeleton variant="text" width="400px" />
                </Stack>
                <Box
                  mt={8}
                  mr={2}
                  display="flex"
                  justifyContent="flex-end"
                  alignItems="flex-end"
                >
                  <Button
                    variant="outlined"
                    sx={{
                      textTransform: "none",
                      fontWeight: "bold",
                    }}
                    style={{
                      height: 40,
                      marginLeft: "10px",
                      minWidth: "6rem",
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    disabled={disabled}
                    style={{
                      height: 40,
                      marginLeft: "1rem",
                      minWidth: "6rem",
                      textTransform: "none",
                    }}
                  >
                    Save
                  </Button>
                </Box>
              </Box>
            ) : (
              <Box
                sx={{
                  minHeight: "13.5rem",
                  maxHeight: "30vh",
                  overflow: "hidden",
                }}
              >
                {downloadError && (
                  <ReusableDialog
                    dialogState={openMessageDialog}
                    openReusableDialog={handleMessageDialogClickOpen}
                    closeReusableDialog={handleMessageDialogClose}
                    dialogTitle={messageDialogTitle}
                    dialogMessage={messageDialogMessage}
                    handleDialogConfirm={handleMessageDialogClose}
                    dialogOkText={"OK"}
                    dialogSeverity={messageDialogSeverity}
                  />
                )}
                {downloadError && (
                  <ReusableSnackBar
                    openSnackBar={SnackbarOpen}
                    handleSnackbarClose={handleSnackbarClose}
                    alertMsg={messageDialogMessage}
                  />
                )}
                <Stack mt={6} ml={6}>
                  <Typography variant="subtitle2" fontWeight="bold">
                    {t("Add Filter Name")}
                  </Typography>
                  <Stack direction="row">
                    <TextField
                      id="outlined-basic"
                      variant="outlined"
                      sx={{
                        width: "380px",
                      }}
                      value={presetName}
                      onChange={(e) => {
                        setPresetName(e.target.value);
                      }}
                      onKeyDown={(e) => {
                        e.stopPropagation();
                      }}
                      fullwidth
                      padding="none"
                      focused={false}
                      InputProps={{
                        disableUnderline: true,
                        sx: { height: "35px" },
                      }}
                    />

                    <IconButton sx={{ padding: 0, marginLeft: "1rem" }}>
                      <PushPinOutlinedIcon
                        sx={{
                          marginTop: "10px",
                        }}
                      />
                    </IconButton>
                  </Stack>
                </Stack>
                <Box
                  mt={8}
                  mr={2}
                  display="flex"
                  justifyContent="flex-end"
                  alignItems="flex-end"
                >
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleClose();
                    }}
                    variant="outlined"
                    sx={{
                      textTransform: "none",
                      fontWeight: "bold",
                    }}
                    style={{
                      height: 40,
                      marginLeft: "10px",
                      minWidth: "6rem",
                    }}
                  >
                    Cancel
                  </Button>
                  <Tooltip title={disabled ? "Please fill mandatory fields" : ""} arrow>
                    <span>
                      <Button
                        variant="contained"
                        disabled={disabled}
                        onClick={saveFilterPreset}
                        style={{
                          height: 40,
                          marginLeft: "1rem",
                          minWidth: "6rem",
                          textTransform: "none",
                        }}
                      >
                        Save
                      </Button>
                    </span>
                  </Tooltip>
                </Box>
              </Box>
            ))}
        </Box>
      </Menu>
    </div>
  );
})
