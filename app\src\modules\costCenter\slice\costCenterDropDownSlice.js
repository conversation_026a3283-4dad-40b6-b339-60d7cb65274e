import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  dropDown: {},
  isOdataApiCalled:false,
};

export const costCenterDropDownSlice = createSlice({
  name: "costCenterAllDropDown",
  initialState,
  reducers: {
    setDropDown: (state, action) => {
      state.dropDown[action.payload.keyName] = action.payload.data;
    },
    setOdataApiCall:(state,action) => {
      state.isOdataApiCalled = action.payload;
    }
  },
});

export const { setDropDown, setOdataApiCall } = costCenterDropDownSlice.actions;

export default costCenterDropDownSlice.reducer;
