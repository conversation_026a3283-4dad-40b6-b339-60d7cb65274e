import {
  Autocomplete,
  Box,
  Button,
  FormControl,
  Grid,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import React, { useState } from "react";

import ReusableTable from "../../Common/ReusableTable";
import { button_Outlined, container_Padding } from "../../Common/commonStyles";
import { DataGrid } from "@mui/x-data-grid";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setAdditionalData } from "../../../app/payloadslice";

const DisplayDescriptionTab = (props) => {
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch();
  const descriptionData = useSelector((state) => state?.payload?.additionalData);
  let displayData = useSelector(
    (state) => state?.tabsData?.additionalData
  );
  console.log("deac", displayData);
  const initialRows = displayData?.Description?.Data.map((item, index) => ({
    id: index + 1,
    language: item.Language, 
    materialDescription: "NPI Test Material",
  
  }));
  const columns = [
    {
      field: "id",
      headerName: "ID",
      width: 50,
    },
    {
      field: "language",
      headerName: "Language",
      width: 150,
      valueOptions: ["EN", "AR", "ES"],
      type: "singleSelect",
      align: "left",
      headerAlign: "left",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = descriptionData?.map((x) => {
          if (x.id === params.id) {
            return { ...x, language: params?.props?.value };
          }
          return x;
        });

        dispatch(setAdditionalData(rowsData));
      },
    },
    {
      field: "materialDescription",
      headerName: "Material Description",
      width: 250,
      type: "text",
      align: "left",
      headerAlign: "left",
      editable: true,
      preProcessEditCellProps: (params) => {
        let rowsData = descriptionData?.map((x) => {
          if (x.id === params.id) {
            return { ...x, materialDescription: params?.props?.value };
          }
          return x;
        });

        dispatch(setAdditionalData(rowsData));
      },
    },
  ];

  const handleAddNewRow = () => {
    const newRow = [
      ...descriptionData,
      { id: descriptionData.length + 1, language: "", materialDescription: "" },
    ];
    dispatch(setAdditionalData(newRow));
  };

  // useEffect(() => {
  //   console.log(initialRows);
  // }, [initialRows]);

  return (
    <div>
      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          mt: 0.25,
          ...container_Padding,
        }}
      >
        <Grid container display="block">
          <Grid item sx={{ paddingTop: "2px !important", mb: 1 }}>
            <Stack flexDirection="row" alignItems="center">
              <div style={{ width: "5%" }}>
                <Typography variant="body2" color="#777">
                  Material
                </Typography>
              </div>
              <Typography variant="body2" fontWeight="bold">
                :
              </Typography>
              <TextField
                size="small"
                sx={{ ml: 2 }}
                value={displayData.Material}
              />
            </Stack>
          </Grid>
          <Box sx={{ width: "50%" }} >
            <ReusableTable
              rows={initialRows ?? []}
              columns={columns}
              getRowIdValue={"id"}
              hideFooter={true}
              checkboxSelection={false}
              experimentalFeatures={{ newEditingApi: true }}
            />
          </Box>
        </Grid>
      </Grid>
    </div>
  );
};

export default DisplayDescriptionTab;
