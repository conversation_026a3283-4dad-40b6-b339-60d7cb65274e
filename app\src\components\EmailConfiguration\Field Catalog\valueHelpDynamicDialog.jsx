import React from "react";
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';

class valueHelpDialog extends React.Component {

    constructor(props) {
        super(props);
        this.state = {
            value: "DataElement",

        };
    }
    componentDidUpdate(prevProps) {

    }

    componentDidMount() {

        // fetch("/workrules/v1/dataElement")
        //   .then((response) => response.json())
        //   .then((data) =>
        //     this.setState({
        //       ...this.state,
        //       rows: this.getRows(),
        //     })
        //   );



    }
    handleChange = (event, newValue) => {
        this.setState({
            ...this.state,
            value: newValue
        });
    };
    rowSelected = (event) => {
        var s = event;
    }

    render() {

        return (<Dialog
            fullScreen={false}
            open={this.props.open}

        //onClose={() => props.onClose('CANCEL')}
        >
            <DialogTitle style={{ display: 'flex', alignItems: 'center', borderBottom: '1px solid #d9d9d9' }}>Value Help</DialogTitle>
            <DialogContent >
                <DialogContentText style={{ width: "35rem" }}>
                    <TableContainer  >
                        <Table  aria-label="a dense table">
                            <TableHead>

                                <TableRow>
                                    {this.props.dynamicColumn.map((column) => {
                                        return (
                                            <TableCell style={{ fontWeight: 700 }}>{column.displayName} </TableCell>
                                        );
                                    })}

                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {this.props.dataElement.map((row, i,) => {
                                    return (


                                        <TableRow key="a" onClick={() => this.props.rowSelected(row,i)}>

                                            {this.props.dynamicColumn.map((column) => {
                                                return (
                                                    <TableCell key={column.mappedName}>{row[column.mappedName]} </TableCell>
                                                );
                                            })}
                                        </TableRow>
                                    );
                                })}
                            </TableBody>
                        </Table>
                    </TableContainer>

                </DialogContentText>
            </DialogContent>
            <DialogActions style={{ height: '3rem', borderTop: '1px solid #d9d9d9' }}>
                {/* {props.actions.map(action => <Button key={action} variant="contained" size="small" onClick={() => props.handleClose(action)}>{action}</Button>)} */}
                <Button key={'CANCEL'} variant="contained" size="small" onClick={() => { this.props.onClose("CANCEL") }}>CANCEL</Button>

            </DialogActions>
        </Dialog>)
    }
}

export default valueHelpDialog;