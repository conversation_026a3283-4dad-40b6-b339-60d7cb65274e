import { alpha, Box, Button, Chip, Dialog, DialogActions, DialogContent, DialogTitle, Typography } from "@mui/material";
import { CHATBOT_CONFIG } from "../../constant/chatbotConstants";
import ReusableDataTable from "./ReusableTable"
import StorageIcon from '@mui/icons-material/Storage';
import { saveExcel } from "../../functions";

const functions_ExportAsExcel = {
convertJsonToExcel: (tableData) => {
    let excelColumns = [];

    tableData?.columns?.forEach((item) => {
    if (item.headerName.toLowerCase() !== "action" && !item.hide) {
        excelColumns.push({ header: item.headerName, key: item.field });
    }
    });
    const presentDate = new Date();
    saveExcel({
    fileName: `Database Query Results`,
    columns: excelColumns,
    rows: tableData?.rows?.map((row, index) => ({ id: index, ...row })),
    });
}
};

const ReusableDialogTable = ({
 tableDialogOpen = false,
 onClose = () => {},
 tableData
}) => (
  <Dialog
    open={tableDialogOpen}
    onClose={onClose}
    maxWidth="lg"
    fullWidth
    PaperProps={{
      sx: {
        borderRadius: CHATBOT_CONFIG.RADIUS.LARGE,
      }
    }}
    BackdropProps={{
      sx: {
        backgroundColor: 'transparent',
        backdropFilter: 'blur(8px)',
        background: alpha('#000', 0.2) // Very light overlay
      }
    }}
    sx={{
      '& .MuiDialog-container': {
        backdropFilter: 'blur(8px)'
      }
    }}
  >
    <DialogTitle
      sx={{
        background: CHATBOT_CONFIG.COLORS.HEADER_BACKGROUND,
        color: CHATBOT_CONFIG.COLORS.TEXT_WHITE,
        py: CHATBOT_CONFIG.SPACING.MD,
        px: CHATBOT_CONFIG.SPACING.LG,
        position: 'relative',
        '&::after': {
          content: '""',
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: '1px',
          background: `linear-gradient(90deg, transparent 0%, ${alpha('#fff', 0.3)} 50%, transparent 100%)`
        }
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: CHATBOT_CONFIG.SPACING.SM }}>
        <StorageIcon sx={{ fontSize: 24 }} />
        <Typography variant="h6" sx={{ fontWeight: CHATBOT_CONFIG.TYPOGRAPHY.FONT_WEIGHT.SEMIBOLD }}>
          Database Query Results
        </Typography>
        <Chip
          label={`${tableData.rows.length} records`}
          size="small"
          sx={{
            ml: 'auto',
            bgcolor: alpha('#fff', 0.2),
            color: 'white',
            fontSize: CHATBOT_CONFIG.TYPOGRAPHY.FONT_SIZE.TINY,
            fontWeight: CHATBOT_CONFIG.TYPOGRAPHY.FONT_WEIGHT.MEDIUM
          }}
        />
      </Box>
    </DialogTitle>
    
    <DialogContent
      sx={{
        p: 0,
        background: '#fafbfc',
        position: 'relative'
      }}
    >
      <Box
        sx={{ 
          height: 500, 
          width: '100%',
          padding: 2,
        }}
      >
        <ReusableDataTable
          rows={tableData?.rows?.map((row, index) => ({ id: index, ...row }))}
          columns={tableData?.columns}
          pageSize={25}
          rowsPerPageOptions={[10, 25, 50]}
          getRowIdValue={"id"}
          disableSelectionOnClick
          autoHeight={false}
          density="comfortable"
        />
      </Box>
    </DialogContent>
    
    <DialogActions
      sx={{
        px: CHATBOT_CONFIG.SPACING.LG,
        py: CHATBOT_CONFIG.SPACING.MD,
        background: 'linear-gradient(145deg, #f8fafc 0%, #ffffff 100%)',
        borderTop: `1px solid ${alpha(CHATBOT_CONFIG.COLORS.BORDER_LIGHT, 0.3)}`,
        gap: CHATBOT_CONFIG.SPACING.SM
      }}
    >
      <Button
        variant="outlined"
        onClick={onClose}
        sx={{
          borderRadius: CHATBOT_CONFIG.RADIUS.MEDIUM,
          textTransform: 'none',
          fontWeight: CHATBOT_CONFIG.TYPOGRAPHY.FONT_WEIGHT.MEDIUM,
          px: CHATBOT_CONFIG.SPACING.LG,
          borderColor: CHATBOT_CONFIG.COLORS.BORDER_MEDIUM,
          color: CHATBOT_CONFIG.COLORS.TEXT_SECONDARY,
          '&:hover': {
            borderColor: CHATBOT_CONFIG.COLORS.PRIMARY,
            backgroundColor: alpha(CHATBOT_CONFIG.COLORS.PRIMARY, 0.05),
            transform: 'translateY(-1px)',
            boxShadow: CHATBOT_CONFIG.SHADOWS.LIGHT
          },
          transition: 'all 0.2s ease'
        }}
      >
        Close
      </Button>
      <Button
        variant="contained"
        sx={{
          background: CHATBOT_CONFIG.COLORS.HEADER_BACKGROUND,
          borderRadius: CHATBOT_CONFIG.RADIUS.MEDIUM,
          textTransform: 'none',
          fontWeight: CHATBOT_CONFIG.TYPOGRAPHY.FONT_WEIGHT.MEDIUM,
          px: CHATBOT_CONFIG.SPACING.LG,
          '&:hover': {
            background: CHATBOT_CONFIG.COLORS.HEADER_BACKGROUND,
            transform: 'translateY(-1px)',
            boxShadow: CHATBOT_CONFIG.SHADOWS.MEDIUM
          },
          transition: 'all 0.2s ease'
        }}
        onClick={() => {
          functions_ExportAsExcel.convertJsonToExcel(tableData)
          console.log('Export data:', tableData.rows);
        }}
      >
        Export Data
      </Button>
    </DialogActions>
  </Dialog>
);

export default ReusableDialogTable