import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import GraphCards from './GraphCards';

export function SortableCard({ card }) {
  const {
    setNodeRef,
    transform,
    transition,
    attributes,
    listeners,
  } = useSortable({ id: card.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div ref={setNodeRef} style={style}>
      <GraphCards
        title={card?.graphDetails?.graphName}
        data={card}
        chartType={card?.graphDetails?.chartType}
        dragHandleProps={{ ...attributes, ...listeners }} // pass only to header
      />
    </div>
  );
}
