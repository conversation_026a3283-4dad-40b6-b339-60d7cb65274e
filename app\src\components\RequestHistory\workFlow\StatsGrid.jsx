import StatCard from './StatCard';
import useLang from '@hooks/useLang';

const StatsGrid = ({ data }) => {
  let totalTasks = 0;
  let totalGroups = Object.keys(data).length;
  let slaSum = 0;
  let taskCount = 0;
  let totalAverageSLA = 0;
  const { t } = useLang();

  Object.values(data).forEach(group => {
    const groupDetails = group.workflowDetails;
    totalAverageSLA = groupDetails?.totalAverageSLA
    totalTasks += 2;
    slaSum += (groupDetails.requestor_sla || 0) + (groupDetails.mdmApprover_sla || 0);
    groupDetails.workflowTaskDetailsByLevel?.forEach(level => {
      const tasksAtLevel = Object.values(level)?.[0];
      totalTasks += tasksAtLevel.length;
      taskCount += tasksAtLevel.length;
      tasksAtLevel.forEach(task => {
        slaSum += task.taskSla || 0;
      });
    });
  });

  return (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
        gap: 16,
        margin: '16px 0',
        padding: 8,
        borderRadius: 8,
      }}
    >
      <StatCard number={totalGroups} label={t("Workflow Groups")} />
      <StatCard number={totalTasks} label={t("Total Tasks")} />
      <StatCard number={totalAverageSLA} label={t("Avg SLA (Days)")} />
    </div>
  );
};

export default StatsGrid;
