import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom"; // ⬅️ to read query params
import axios from "axios"; // ⬅️ assuming you use axios for API
import CommonStepper from "../Common/Stepper";
import RequestHeaderPC from "./RequestPages/RequestHeaderPC";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import ArrowCircleLeftOutlined from "@mui/icons-material/ArrowCircleLeftOutlined";
import RequestDetailsPC from "./RequestPages/RequestDetailsPC";
import { setActiveStep } from "../../app/redux/stepperSlice";
import { setRequestHeaderData } from "../../app/redux/requestHeaderSlice";
import RequestDetailsChangePC from "./RequestPages/RequestDetailsChangePC";
import ExcelOperationsCard from "../Common/ExcelOperationsCard";
import { setRequestHeader } from "@app/requestDataSlice";

import {
  destination_DocumentManagement,
  destination_IDM,
  destination_MaterialMgmt,
  destination_ProfitCenter_Mass,
} from "../../destinationVariables";
import { doAjax } from "../Common/fetchService";
import { 
  transformApiResponseToReduxPayloadPc,
  fetchRegionBasedOnCountry,
  getProfitCenterGrp
} from "../../functions";
import {
  setProfitCenterRows,
  setProfitCenterTabs,
  setSelectedRowId,
} from "../../app/redux/profitCenterTabSlice";
import { setProfitCenterApiData } from "../../app/redux/profitCenterTabSlice";
import { Step, StepButton, Stepper, IconButton } from "@mui/material";
import { Box, Grid, Typography } from "@mui/material";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import { REQUEST_STATUS, REQUEST_TYPE } from "@constant/enum";
import { APP_END_POINTS } from "@constant/appEndPoints";
import { resetPayloadData, setPCPayload, setRequestHeaderPayloadData } from "../../app/profitCenterTabsSlice";
import { setDropDown } from "../../app/dropDownDataSlice";
import RequestDetailsGL from "./RequestPages/RequestDetailsGL";

// const steps = ["Request Header", "Profit Center List", "Attachments & Comments"];





const ReusableRequestTab = () => {
  const tabValue = useSelector((state) => state.CommonStepper.activeStep);
  const requestHeaderData = useSelector((state) => state.profitCenter.payload.requestHeaderData);
  const requestIdHeader = useSelector((state) => state.request.requestHeader?.requestId);
  const requestHeaderSlice = useSelector((state) => state.request.requestHeader);
  const dispatch = useDispatch();
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(false);
  const [addHardCodeData, setAddHardCodeData] = useState(false);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const [downloadClicked, setDownloadClicked] = useState(false);
  const location = useLocation();
  const [apiResponses, setApiResponses] = useState([]);
  const [completed, setCompleted] = useState([]);
  const navigate = useNavigate();
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [isDialogVisible, setisDialogVisible] = useState(false);
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);

  const handleTabChange = (index) => {
    dispatch(setActiveStep(index));
  };

  const rowData = location?.state;

  const steps =location?.state?.steaperData
  let moduleName = location?.state?.moduleName

  console.log(tabValue,"tabValueDaata")

  console.log(steps,"StepsData")
  const queryParams = new URLSearchParams(location.search);
  const reqBench = queryParams.get("reqBench");
  const requestId = queryParams.get("RequestId");
  const RequestId = queryParams.get("RequestId");
  const RequestType = queryParams.get("RequestType");
  const handleDownload = () => {
    setDownloadClicked(true);
  }

  const handleUploadPC = (file) => {
    let url = "";
    // if (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) {
      url = "getAllProfitCenterFromExcel";
    // } else {
    //   url = "getAllProfitCenterFromExcelForMassChange";
    // }
    setLoaderMessage("Initiating Excel Upload");
    setBlurLoading(true);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    formData.append("dtName", (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) ? "MDG_PC_FIELD_CONFIG" : "MDG_CHANGE_TEMPLATE_DT");
    formData.append("version", (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) ? "v2" : "v4");
    formData.append("requestId", requestId ? requestId : "");
    formData.append("IsSunoco", "false");
    formData.append("screenName", RequestType ? RequestType : "");

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      } else {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }
    };
    const hError = (error) => {
      setBlurLoading(false);
      setLoaderMessage("");
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    };

    doAjax(`/${destination_ProfitCenter_Mass}/massAction/${url}`, "postformdata", hSuccess, hError, formData);
  };

  const getDisplayDataPC = (requestId) => {
    const isChildPresent = rowData?.childRequestIds !== "Not Available"
    if (reqBench === "true") {
      const payload = {
        sort: "id,asc",
        parentId: !isChildPresent ? requestId : "",
        massCreationId: isChildPresent && (RequestType==="Create" || RequestType==="Mass Create" || RequestType==="Create with Upload") ? requestId : "",
        massChangeId: isChildPresent && (RequestType==="Change" || RequestType==="Mass Change" || RequestType==="Change with Upload") ? requestId : "",
        page: 0,
        size: 10,
      };

      const hSuccess = (response) => {
        const apiResponse = response?.body || [];
        //new added For header Data Constant
        let requestHeaderData = response?.body[0]?.Torequestheaderdata;
        let TotalIntermediateTasks = response?.body[0]?.TotalIntermediateTasks;

       
      };

      const hError = (error) => {
        console.error("Error fetching PC Create data:", error);
      };

      doAjax(
        `/${destination_ProfitCenter_Mass}/data/displayMassProfitCenterDto`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  useEffect(() => {
      if (isSecondTabEnabled) {
        setCompleted([true]);
      }
  }, [isSecondTabEnabled]);

  useEffect(() => {
      const loadData = async () => {
        if (RequestId) {
          await getDisplayDataPC(RequestId);
          if (((RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD && !rowData?.length) || RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) && (rowData?.reqStatus === REQUEST_STATUS.DRAFT || rowData?.reqStatus === REQUEST_STATUS.UPLOAD_FAILED)) {
            dispatch(setActiveStep(0));
            setIsSecondTabEnabled(false);
            setIsAttachmentTabEnabled(false);
          } else {
            dispatch(setActiveStep(1));
            setIsSecondTabEnabled(true);
            setIsAttachmentTabEnabled(true);
          }
  
          setAddHardCodeData(true);
        } else {
          dispatch(setActiveStep(0));
        }
      };
      
      loadData();
      return () => {
        dispatch(resetPayloadData())
        dispatch(setRequestHeader({}))
        dispatch(setDropDown({keyName:"FieldName",data:[]}))
        // dispatch(changeTemplateDT([]));
        // dispatch(clearChangeLogData());
        // dispatch(clearTemplateArray());
        // dispatch(clearMaterialFieldConfig());
        // dispatch(clearCreateTemplateArray());
        // dispatch(clearCreateChangeLogData());
        // dispatch(updateAllTabsData({}));
        // dispatch(resetPayloadData({ data: {} }));
        // dispatch(setMatlNoData([]))
        // dispatch(setPlantData([]))
        // dispatch(setRequestorPayload({}));
        // dispatch(clearDynamicKeyValue());
        // dispatch(updateSelectedRows([]));
        // dispatch(setChangeFieldRows([]));
        // dispatch(setChangeFieldRowsDisplay({}));
        // clearLocalStorageItem(LOCAL_STORAGE_KEYS.CURRENT_TASK);
        // clearLocalStorageItem(LOCAL_STORAGE_KEYS.ROLE);
      };
    }, [requestId, dispatch]);
    
  const handleYes = () => {
      if(requestId && !reqBench) {
        navigate(APP_END_POINTS?.MY_TASK);
      }
      else if(reqBench) {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }
      else if(!requestId && !reqBench) {
        navigate(APP_END_POINTS?.PROFIT_CENTER);
      }
    };
  
  const handleCancel = () => {
    setisDialogVisible(false)
  };

  return (
    <div>
      <Box sx={{ padding: 2 }}>
        <IconButton
          onClick={() => {
            if(reqBench === "true") {
              navigate("/requestBench");
              return;
            }
          }}
          color="primary"
          aria-label="upload picture"
          component="label"
          sx={{left: "-10px",}}
          title="Back"
        >
          <ArrowCircleLeftOutlined
            sx={{ fontSize: "25px", color: "#000000" }}
          />
        </IconButton>
      
        <Stepper nonLinear activeStep={tabValue} sx={{ 
          display: "flex", 
          alignItems: "center", 
          justifyContent: "center", 
          margin: "25px 14%", 
          marginTop: "-35px"
        }}>
          {steps.map((label, index) => (
            <Step key={label} completed={completed[index]}>
              <StepButton color="error" disabled={
                      (index === 1 && !isSecondTabEnabled) ||
                      (index === 2 && !isAttachmentTabEnabled)||
                      (index===3 && !isSecondTabEnabled && !isAttachmentTabEnabled)
                    } onClick={() => handleTabChange(index)} sx={{ fontSize: "50px", fontWeight: "bold" }}>
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>{label}</span>
              </StepButton>
            </Step>
          ))}
        </Stepper>
        
      {tabValue === 0 && (
        <>
          <RequestHeaderPC apiResponse={apiResponses} reqBench={reqBench} downloadClicked={downloadClicked} setDownloadClicked={setDownloadClicked} setIsSecondTabEnabled={setIsSecondTabEnabled} setIsAttachmentTabEnabled={setIsAttachmentTabEnabled} moduleName={moduleName} />
          {(RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD || RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) && ((rowData?.reqStatus == REQUEST_STATUS.DRAFT && !rowData?.material?.length) || rowData?.reqStatus == REQUEST_STATUS.UPLOAD_FAILED) && (
              <ExcelOperationsCard
                handleDownload={handleDownload}
                setEnableDocumentUpload={setEnableDocumentUpload}
                enableDocumentUpload={enableDocumentUpload}
                handleUploadMaterial={handleUploadPC}
              />
            )}
        </>)}

        {tabValue === 1 && moduleName == 'ProfitCenter' &&
          requestHeaderData.RequestType &&
          (requestHeaderData.RequestType === "Change" || requestHeaderData.RequestType === "Change with Upload" ? (
            <RequestDetailsChangePC reqBench={reqBench} requestId={requestId} apiResponses={apiResponses} setIsAttachmentTabEnabled={true} setCompleted={setCompleted} downloadClicked={downloadClicked} setDownloadClicked={setDownloadClicked} moduleName={moduleName} />
          ) : (
            <RequestDetailsPC reqBench={reqBench} apiResponses={apiResponses} moduleName={moduleName} />
          )) 
        }
         {tabValue === 1 && moduleName == 'GeneralLedger' &&
          requestHeaderData.RequestType &&
          (requestHeaderData.RequestType === "Change" || requestHeaderData.RequestType === "Change with Upload" ? (
            <RequestDetailsChangePC reqBench={reqBench} requestId={requestId} apiResponses={apiResponses} setIsAttachmentTabEnabled={true} setCompleted={setCompleted} downloadClicked={downloadClicked} setDownloadClicked={setDownloadClicked} moduleName={moduleName} />
          ) : (
            <RequestDetailsGL reqBench={reqBench} apiResponses={apiResponses} moduleName={moduleName} />
          )) 
        }
      </Box>
      <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
    </div>
  );
}


export default ReusableRequestTab;
