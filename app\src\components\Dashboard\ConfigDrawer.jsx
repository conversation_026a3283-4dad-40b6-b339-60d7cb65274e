import {
  AddOutlined,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>utlined,
  <PERSON>boardOutlined,
  <PERSON><PERSON><PERSON><PERSON>utlineOutlined,
  RemoveOutlined,
} from "@mui/icons-material";

import {
  Box,
  Button,
  FormControlLabel,
  Grid,
  Stack,
  SwipeableDrawer,
  Switch,
  Tab,
  Tabs,
  Text<PERSON>ield,
  Typography,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import React from "react";
import { button_Outlined, font_Small } from "../common/commonStyles";

import ShowChartIcon from "@mui/icons-material/ShowChart";

/***TabPanel***/
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 2 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}
/******/
function a22yProps(index) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}
const ConfigDrawer = (props) => {
  const [valueDrawer, setValueDrawer] = React.useState(props?.isProduct ? 1 : 0); //indiactor for which tab is active
  const handleChangeDrawer = (event, newValue) => {
    setValueDrawer(newValue);
  };
  return (
    <>
      <SwipeableDrawer
        anchor="right"
        open={props?.draweropenGraph}
        onClose={props?.handleClickCloseGraph}
        onOpen={() => {}}
        onBackdropClick={props?.handleClickCloseGraph}
        disableScrollLock
        PaperProps={{
          elevation: 4,
          sx: {
            width: "auto",
            border: "none",
            backgroundColor: "#FAFCFF",
          },
        }}
      >
        <div>
          <Box>
            <Stack spacing={0}>
              <Grid
                container
                sx={{
                  padding: "1rem",
                }}
                Spacing={1}
              >
                <Grid
                  item
                  md={10}
                  style={{
                    paddingLeft: "16px",
                    paddingRight: "16px",
                    paddingTop: "16px",
                  }}
                >
                  <Typography
                    variant="h5"
                    sx={{
                      color: "#1D1D1D",
                      font: "Roboto",
                      fontWeight: "400",
                      fontSize: "1.3rem",
                      letter: "1.5%",
                    }}
                  >
                    {props?.DrawerTitle} Configuration
                  </Typography>
                </Grid>
                <Grid
                  item
                  md={2}
                  style={{
                    margin: "auto",
                    display: "flex",
                    justifyContent: "flex-end",
                  }}
                ></Grid>
              </Grid>
              <Box
                sx={{
                  minWidth: 560,
                  paddingRight: "2rem",
                  paddingLeft: "2rem",
                }}
              >
                <Box sx={{ width: "100%" }}>
                  <Box>
                    <Tabs
                      value={valueDrawer}
                      onChange={handleChangeDrawer}
                      aria-label="basic tabs example"
                    >
                {props?.isProduct &&      <Tab
                        sx={{ textTransform: "none" }}
                        label={
                          <Typography
                            variant="body1"
                            sx={{ fontWeight: 600, fontSize: "14px" }}
                          >
                            Create Graph
                          </Typography>
                        }
                        {...a22yProps(0)}
                      />}
                      <Tab
                        sx={{ textTransform: "none" }}
                        label={
                          <Typography
                            variant="body1"
                            sx={{ fontWeight: 600, fontSize: "14px" }}
                          >
                            Manage Graph
                          </Typography>
                        }
                        {...a22yProps(props?.isProduct ? 0 : 1)}
                      />
                    </Tabs>
                  </Box>
                {props?.isProduct &&  <TabPanel value={valueDrawer} index={0}>
                    <Stack sx={{ maxWidth: "400px" }}>
                      <Box>
                        <Typography sx={font_Small}>
                          <strong>Graph Name</strong>
                        </Typography>
                        <TextField
                          fullWidth
                          placeholder="Enter Graph Name"
                          variant="outlined"
                          size="small"
                        ></TextField>
                      </Box>
                      <Grid container rowSpacing={1} mt={1}>
                        <Grid item xs={12}>
                          <Typography sx={font_Small}>
                            <strong>Select Chart Type</strong>
                          </Typography>
                        </Grid>
                        <Grid item xs={2}>
                          <ShowChartIcon sx={{ cursor: "pointer" }} />
                        </Grid>
                        <Grid item xs={2}>
                          <LeaderboardOutlined sx={{ cursor: "pointer" }} />
                        </Grid>
                        <Grid item xs={2}>
                          <PieChartOutlineOutlined sx={{ cursor: "pointer" }} />
                        </Grid>
                        <Grid item xs={2}>
                          <BubbleChartOutlined sx={{ cursor: "pointer" }} />
                        </Grid>
                        <Grid item xs={6}>
                          <FormControlLabel
                            control={<Switch defaultChecked />}
                            label="Show Grid"
                          />
                        </Grid>
                        <Grid item xs={6}>
                          <FormControlLabel
                            control={<Switch defaultChecked />}
                            label="Show Legend"
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <Typography sx={font_Small}>
                            <strong>Coordinate Settings</strong>
                          </Typography>
                        </Grid>
                        <Grid item xs={12}>
                          <Typography sx={font_Small}>
                            <strong>X:Name</strong>
                          </Typography>
                          <TextField
                            fullWidth
                            placeholder="Enter x-axis Name"
                            variant="outlined"
                            size="small"
                          ></TextField>
                        </Grid>
                        <Grid item xs={5}>
                          <Typography sx={font_Small}>
                            <strong>Coordinate Value</strong>
                          </Typography>
                          <TextField
                            fullWidth
                            placeholder="Enter Coordinate"
                            variant="outlined"
                            size="small"
                          ></TextField>
                        </Grid>
                        <Grid item xs={1}></Grid>
                        <Grid item xs={1} mt={3}>
                          <RemoveOutlined />
                        </Grid>
                        <Grid item xs={4}>
                          <Typography sx={font_Small}>
                            <strong>Maximum Value</strong>
                          </Typography>
                          <TextField
                            fullWidth
                            variant="outlined"
                            size="small"
                          ></TextField>
                        </Grid>
                        <Grid item xs={1} mt={3}>
                          <AddOutlined />
                        </Grid>
                        <Grid item xs={12}>
                          <Typography sx={font_Small}>
                            <strong>Y:Name</strong>
                          </Typography>
                          <TextField
                            fullWidth
                            placeholder="Enter y-axis Name"
                            variant="outlined"
                            size="small"
                          ></TextField>
                        </Grid>
                        <Grid item xs={5}>
                          <Typography sx={font_Small}>
                            <strong>Coordinate Value</strong>
                          </Typography>
                          <TextField
                            fullWidth
                            placeholder="Enter Coordinate "
                            variant="outlined"
                            size="small"
                          ></TextField>
                        </Grid>
                        <Grid item xs={1}></Grid>
                        <Grid item xs={1} mt={3}>
                          <RemoveOutlined />
                        </Grid>
                        <Grid item xs={4}>
                          <Typography sx={font_Small}>
                            <strong>Maximum Value</strong>
                          </Typography>
                          <TextField
                            fullWidth
                            variant="outlined"
                            size="small"
                          ></TextField>
                        </Grid>
                        <Grid item xs={1} mt={3}>
                          <AddOutlined />
                        </Grid>
                      </Grid>
                    </Stack>
                  </TabPanel>}
                  <TabPanel value={valueDrawer} index={props?.isProduct ? 1 : 0}>
                    <Stack spacing={1} >
                    <div className="reusable-table">
                      <Box sx={{ width: "100%", height: "422px" }}>
                        <DataGrid
                          rows={props?.graphnames}
                          hideFooter
                          columns={props?.columns}
                          disableSelectionOnClick
                          rowsPerPageOptions={[20]}
                          paging={false}
                        />
                      </Box>
                    </div>
                    <Stack direction='row' justifyContent='space-between'>
                     <Box></Box>
                    <Button  size="small" sx={{...button_Outlined,maxWidth:'max-content'}} 
                  variant="contained" onClick={props?.handleApply}>Apply</Button></Stack>
                  </Stack>
                  </TabPanel>
                </Box>
              </Box>
            </Stack>
          </Box>
        </div>
      </SwipeableDrawer>
    </>
  );
};

export default ConfigDrawer;
