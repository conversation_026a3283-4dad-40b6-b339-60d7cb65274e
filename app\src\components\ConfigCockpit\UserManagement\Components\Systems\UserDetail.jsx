import React, { useEffect, useState } from "react";
import {
  <PERSON>ton,
  <PERSON><PERSON>,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
  Checkbox,
  Stack,
  Tab,
  Box,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import CloseIcon from "@mui/icons-material/Close";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import {
  Add,
  Remove,
  Close,
  CheckBoxOutlineBlank,
  CheckBox,
} from "@mui/icons-material";
import { useDispatch, useSelector } from "react-redux";
import Loading from "../Loading";
import {
  setUsers,
  setResponseMessage,
} from "../../../../../app/userManagementSlice";
import {
  findApplicationById,
  findUserById,
  findRoleById,
} from "../../Utility/basic";
import { Autocomplete } from "@mui/material";
import DeletionMessageBox from "../DeletionMessageBox";
import {
  appHeaderHeight,
  buttonHeight,
  crossIconContainerHeight,
  userDetailPageCss,
  userPageHeaderHeight,
} from "../../Data/cssConstant";
import {
  destination_IWA,
  destination_IWA_NPI,
  // destination_IWA_SCP,
  destination_Po,
} from "../../../../../destinationVariables";
import { font_Small } from "../../../../common/commonStyles";
import ReusableTable from "../../../../common/ReusableTable";
import ReusablePromptBox from "../../../../common/ReusablePromptBox/ReusablePromptBox";
import ReusableIcon from "../../../../common/ReusableIcon";
import PeopleIcon from "@mui/icons-material/People";
import SettingsIcon from "@mui/icons-material/Settings";
import InfoIcon from "@mui/icons-material/Info";
import { TabContext, TabList, TabPanel } from "@mui/lab";
import { doAjax } from "../../../../common/fetchService";

const useStyle = makeStyles((theme) => ({
  userInfoContainer: {
    display: "flex",
    flexDirection: "column",
    padding: 10,
    height: `calc(100vh - ${appHeaderHeight} - ${userPageHeaderHeight} - ${crossIconContainerHeight} - ${userDetailPageCss?.tabsContainerHeight} - ${userDetailPageCss?.footerHeight} - 18px)`,
  },
  userInfoItemContainer: {
    margin: 4,
    alignItems: "center",
  },
  roleInfoContainer: {
    height: `calc(100vh - ${appHeaderHeight} - ${userPageHeaderHeight} - ${crossIconContainerHeight} - ${userDetailPageCss?.tabsContainerHeight} - ${userDetailPageCss?.footerHeight} - 18px)`,
  },
  userInfoContainerLabel: {
    margin: "0px",
    fontFamily: "Roboto, Helvetica, Arial, sans-serif",
    fontWeight: "400",
    fontSize: "12px",
  },
  userInfoContainerText: {
    fontFamily: "Roboto, Helvetica, Arial, sans-serif",
    fontWeight: "400",
    fontSize: "12px",
  },

  newUserAssociatedGroupDialogTitle: {
    height: "3rem",
    display: "flex",
    alignItems: "center",
    borderBottom: "1px solid #d9d9d9",
    margin: 0,
    padding: 0,
    paddingLeft: 10,
  },
  newUserAssociatedGroupDialogActions: {
    height: "3rem",
    borderTop: "1px solid #d9d9d9",
  },

  userAssociatedGroupsTableContainer: {
    height: `calc(100vh - ${appHeaderHeight} - ${userPageHeaderHeight} - ${crossIconContainerHeight} - ${userDetailPageCss?.tabsContainerHeight} - ${buttonHeight} - ${userDetailPageCss?.footerHeight} - 26px)`,
    width: "100%",
  },
  userAssociatedGroupsTableHead: {
    backgroundColor: theme.palette.text.primary,
    position: "sticky",
    top: 0,
    zIndex: 99,
  },
  userAssociatedGroupsTableHeadCell: {
    fontWeight: 700,
    whiteSpace: "nowrap",
    color: theme.palette.background.paper,
    fontSize: 14,
  },
  userAssociatedGroupsTableBody: {
    height: "100%",
  },
  userAssociatedGroupsTableBodyRow: {
    "&:hover": {
      backgroundColor: theme.palette.action.hover,
    },
  },
  userAssociatedGroupsTableBodyCell: {
    whiteSpace: "nowrap",
    fontSize: 12,
  },
  userAssociatedGroupsBottomAddButton: {
    margin: "4px 10px",
    textTransform: "capitalize",
    height: buttonHeight,
  },

  newUserAssignedRoleDialogTitle: {
    height: "3rem",
    display: "flex",
    alignItems: "center",
    borderBottom: "1px solid #d9d9d9",
    margin: 0,
    padding: 0,
    paddingLeft: 10,
  },
  newUserAssignedRoleDialogActions: {
    height: "3rem",
    borderTop: "1px solid #d9d9d9",
  },

  userAssignedRolesTableContainer: {
    height: `calc(100vh - ${appHeaderHeight} - ${userPageHeaderHeight} - ${crossIconContainerHeight} - ${userDetailPageCss?.tabsContainerHeight} - ${buttonHeight} - ${userDetailPageCss?.footerHeight} - 26px)`,
    width: "100%",
  },
  userAssignedRolesTableHead: {
    backgroundColor: theme.palette.text.primary,
    position: "sticky",
    top: 0,
    zIndex: 99,
  },
  userAssignedRolesTableHeadCell: {
    fontWeight: 700,
    whiteSpace: "nowrap",
    color: theme.palette.background.paper,
    fontSize: 14,
  },
  userAssignedRolesTableBody: {
    height: "100%",
  },
  userAssignedRolesTableBodyRow: {
    "&:hover": {
      backgroundColor: theme.palette.action.hover,
    },
  },
  userAssignedRolesTableBodyCell: {
    whiteSpace: "nowrap",
    fontSize: 12,
  },
  userAssignedRolesBottomAddButton: {
    margin: "4px 10px",
    textTransform: "capitalize",
    height: buttonHeight,
  },

  newUserAssignedLicenseDialogTitle: {
    height: "3rem",
    display: "flex",
    alignItems: "center",
    borderBottom: "1px solid #d9d9d9",
    margin: 0,
    padding: 0,
    paddingLeft: 10,
  },
  newUserAssignedLicenseDialogContentMsg: {
    textAlign: "center",
    fontSize: 14,
    fontWeight: "bold",
  },
  newUserAssignedLicenseDialogActions: {
    height: "3rem",
    borderTop: "1px solid #d9d9d9",
  },

  userAssignedLicensesTableContainer: {
    height: `calc(100vh - ${appHeaderHeight} - ${userPageHeaderHeight} - ${crossIconContainerHeight} - ${userDetailPageCss?.tabsContainerHeight} - ${buttonHeight} - ${userDetailPageCss?.footerHeight} - 26px)`,
    width: "100%",
  },
  userAssignedLicensesTableHead: {
    backgroundColor: theme.palette.text.primary,
    position: "sticky",
    top: 0,
    zIndex: 99,
  },
  userAssignedLicensesTableHeadCell: {
    fontWeight: 700,
    whiteSpace: "nowrap",
    color: theme.palette.background.paper,
    fontSize: 14,
  },
  userAssignedLicensesTableBody: {
    height: "100%",
  },
  userAssignedLicensesTableBodyRow: {
    "&:hover": {
      backgroundColor: theme.palette.action.hover,
    },
  },
  userAssignedLicensesTableBodyCell: {
    whiteSpace: "nowrap",
    fontSize: 12,
  },
  userAssignedLicensesBottomAddButton: {
    margin: "4px 10px",
    textTransform: "capitalize",
    height: buttonHeight,
  },

  newUserAdditionalFeatureDialogTitle: {
    height: "3rem",
    display: "flex",
    alignItems: "center",
    borderBottom: "1px solid #d9d9d9",
    margin: 0,
    padding: 0,
    paddingLeft: 10,
  },
  newUserAdditionalFeatureDialogActions: {
    height: "3rem",
    borderTop: "1px solid #d9d9d9",
  },

  userAdditionalFeaturesTableContainer: {
    height: `calc(100vh - ${appHeaderHeight} - ${userPageHeaderHeight} - ${crossIconContainerHeight} - ${userDetailPageCss?.tabsContainerHeight} - ${userDetailPageCss?.tabsContainerHeight} - ${userDetailPageCss?.footerHeight} - 20px)`,
    width: "100%",
  },
  userAdditionalFeaturesTableHead: {
    backgroundColor: theme.palette.text.primary,
    position: "sticky",
    top: 0,
    zIndex: 99,
  },
  userAdditionalFeaturesTableHeadCell: {
    fontWeight: 700,
    whiteSpace: "nowrap",
    color: theme.palette.background.paper,
    fontSize: 14,
  },
  userAdditionalFeaturesTableBody: {
    height: "100%",
  },
  userAdditionalFeaturesTableBodyRow: {
    "&:hover": {
      backgroundColor: theme.palette.action.hover,
    },
  },
  userAdditionalFeaturesTableBodyCell: {
    whiteSpace: "nowrap",
    fontSize: 12,
  },
  userAdditionalFeaturesBottomAddButton: {
    margin: "4px 10px",
    textTransform: "capitalize",
    height: buttonHeight,
  },

  userDetailContainer: {
    flexDirection: "column",
    height: "100%",
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
    backgroundColor: theme.palette.background.paper,
    margin: 0,
    padding: 0,
    position: "relative",
  },
  userDetailCrossContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end",
    paddingRight: 10,
    paddingTop: 10,
    height: crossIconContainerHeight,
  },
  userDetailHeaderContainer: {
    display: "flex",
    alignItems: "center",
    padding: 10,
    borderBottom: `1px solid ${theme.palette.text.secondary}`,
    height: userDetailPageCss?.tabsContainerHeight,
  },
  userDetailHeaderItem: {
    color: theme.palette.text.secondary,
    fontWeight: "normal",
    cursor: "pointer",
    width: 150,
    fontSize: 14,
    marginLeft: 8,
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
  },
  userDetailHeaderItemSelected: {
    color: theme.palette.text.primary,
    fontWeight: "bold",
  },
  userDetailFooter: {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end",
    boxShadow: "0px 0px 9px #D8D8D8",
    padding: "8px 16px",
    // position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: theme.palette.background.paper,
    zIndex: "auto",
    height: userDetailPageCss?.footerHeight,
  },
  userDetailFooterButton: {
    textTransform: "capitalize",
    fontSize: 14,
    fontWeight: "bold",
  },
}));

const UserInfo = ({ userDetail, setUserDetail }) => {
  const classes = useStyle();
  const userReducerState = useSelector((state) => state.userReducer);
  const basicReducerState = useSelector((state) => state.userManagement);
  let userData = useSelector((state) => state.userManagement.userData);
  const [companyCodeSet, setCompanyCodeSet] = useState([]);
  const [vendorDetailsSet, setVendorDetailsSet] = useState([]);
  const [purchasingGroups, setPurchasingGroups] = useState({});
  let internalUserRoleAccess = {
    "procurement lead": true,
    "buyer  ": true,
    "it admin": true,
    "super user": true,
  };
  let externalUserRoleAccess = {
    "supplier admin": true,
    "test role/supplier": true,
    supplier: true,
  };
  const getPurchGroupDetails = () => {
    // if (userData?.supplierId) {
    //   setVendorDetailsSet({
    //     [userData?.supplierId]: userData?.supplierName,
    //   });
    // } else {

    // if (moduleFilter?.companyCode !== "") {
    const hSuccess = (data) => {
      setPurchasingGroups(data.data);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_Po}/Odata/purchasingGroup`, "get", hSuccess, hError);
    // }
    // }
  };
  const getCompanyCodeSet = () => {
    fetch(`/${destination_Po}/Odata/populateCompanyCodeDetails`)
      .then((res) => res.json())
      .then((data) => {
        setCompanyCodeSet(data);
      });
  };
  const getVendorDetails = () => {
    const formData = new FormData();
    if (userDetail?.companyCode !== "") {
      formData.append("compCode", userDetail?.companyCode?.split(" - ")[0]);

      fetch(`/${destination_Po}/Odata/getVendorId`, {
        method: "POST",
        body: formData,
      })
        .then((res) => res.json())
        .then((data) => {
          setVendorDetailsSet(data);
        });
    }
  };
  useEffect(() => {
    getCompanyCodeSet();
    getPurchGroupDetails();
  }, []);
  useEffect(() => {
    if (userDetail?.companyCode) getVendorDetails();
  }, [userDetail.companyCode]);

  return (
    <div
      style={{
        minHeight: "22rem",
      }}
    >
      <Grid container>
        <Grid
          container
          sx={{
            marginTop: ".5rem",
          }}
        >
          <Grid
            item
            xs={4}
            sx={{
              display: "flex",
              alignItems: "center",
              height: "max-content",
              marginTop: "1rem",
            }}
          >
            <Typography variant="body2">
              Name<span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <div>
              <TextField
                disabled={true}
                className={classes.userInfoContainerText}
                inputProps={{
                  style: {
                    fontFamily: "Roboto, Helvetica, Arial, sans-serif",
                    fontWeight: "400",
                    fontSize: "12px",
                  },
                }}
                fullWidth
                size="small"
                value={`${userDetail?.firstName} ${userDetail?.lastName}`}
              />
            </div>
          </Grid>
        </Grid>

        <Grid
          container
          sx={{
            marginTop: ".5rem",
          }}
        >
          <Grid
            item
            xs={4}
            sx={{
              display: "flex",
              alignItems: "center",
              height: "max-content",
              marginTop: "1rem",
            }}
          >
            <Typography variant="body2">
              User ID<span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <div>
              <TextField
                disabled={true}
                className={classes.userInfoContainerText}
                inputProps={{
                  style: {
                    fontFamily: "Roboto, Helvetica, Arial, sans-serif",
                    fontWeight: "400",
                    fontSize: "12px",
                  },
                }}
                fullWidth
                size="small"
                value={userDetail?.userId}
              />
            </div>
          </Grid>
        </Grid>

        <Grid
          container
          sx={{
            marginTop: ".5rem",
          }}
        >
          <Grid
            item
            xs={4}
            sx={{
              display: "flex",
              alignItems: "center",
              height: "max-content",
              marginTop: "1rem",
            }}
          >
            <Typography variant="body2">
              Email ID<span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <div>
              <TextField
                disabled={true}
                className={classes.userInfoContainerText}
                inputProps={{
                  style: {
                    fontFamily: "Roboto, Helvetica, Arial, sans-serif",
                    fontWeight: "400",
                    fontSize: "12px",
                  },
                }}
                fullWidth
                size="small"
                value={userDetail?.emailId}
              />
            </div>
          </Grid>
        </Grid>

        <Grid
          container
          sx={{
            marginTop: ".5rem",
          }}
        >
          <Grid
            item
            xs={4}
            sx={{
              display: "flex",
              alignItems: "center",
              height: "max-content",
              marginTop: "1rem",
            }}
          >
            <Typography variant="body2">
              Username<span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <div>
              <TextField
                disabled={true}
                className={classes.userInfoContainerText}
                inputProps={{
                  style: {
                    fontFamily: "Roboto, Helvetica, Arial, sans-serif",
                    fontWeight: "400",
                    fontSize: "12px",
                  },
                }}
                fullWidth
                size="small"
                value={userDetail?.userName}
              />
            </div>
          </Grid>
        </Grid>

        <Grid
          container
          sx={{
            marginTop: ".5rem",
          }}
        >
          <Grid
            item
            xs={4}
            sx={{
              display: "flex",
              alignItems: "center",
              height: "max-content",
              marginTop: "1rem",
            }}
          >
            <Typography variant="body2">
              Display Name<span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <div>
              <TextField
                className={classes.userInfoContainerText}
                inputProps={{
                  style: {
                    fontFamily: "Roboto, Helvetica, Arial, sans-serif",
                    fontWeight: "400",
                    fontSize: "12px",
                  },
                }}
                fullWidth
                size="small"
                value={userDetail?.displayName}
                onChange={(e) => {
                  setUserDetail({ ...userDetail, displayName: e.target.value });
                }}
              />
            </div>

            {(basicReducerState?.users
              ?.filter((u) => u?.id !== userDetail?.id)
              ?.find((u) => u?.displayName === userDetail?.displayName) ||
              userDetail?.displayName?.length === 0) && (
              <p style={{ color: "red", fontSize: 12 }}>
                Please enter different display Name
              </p>
            )}
          </Grid>
        </Grid>

        {userDetail?.roleName && (
          <Grid
            container
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Grid
              item
              xs={4}
              sx={{
                display: "flex",
                alignItems: "center",
                height: "max-content",
                marginTop: "1rem",
              }}
            >
              <Typography variant="body2">
                Role<span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>

            <Grid item xs={6}>
              <div>
                <TextField
                  disabled={true}
                  className={classes.userInfoContainerText}
                  inputProps={{
                    style: {
                      fontFamily: "Roboto, Helvetica, Arial, sans-serif",
                      fontWeight: "400",
                      fontSize: "12px",
                    },
                  }}
                  fullWidth
                  size="small"
                  value={userDetail?.roleName}
                />
              </div>
            </Grid>
          </Grid>
        )}

        {/* {internalUserRoleAccess[userDetail?.roleName?.toLowerCase()] && ( */}
        {(userDetail?.roleName?.toLowerCase()?.includes("buyer") ||
          userDetail?.roleName
            ?.toLowerCase()
            ?.includes("procurement lead")) && (
          <Grid
            container
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Grid
              item
              xs={4}
              sx={{
                display: "flex",
                alignItems: "center",
                height: "max-content",
                marginTop: "1rem",
              }}
            >
              <Typography variant="body2">Purchasing Group</Typography>
            </Grid>

            <Grid item xs={6}>
              <div>
                <TextField
                  disabled={true}
                  className={classes.userInfoContainerText}
                  inputProps={{
                    style: {
                      fontFamily: "Roboto, Helvetica, Arial, sans-serif",
                      fontWeight: "400",
                      fontSize: "12px",
                    },
                  }}
                  fullWidth
                  size="small"
                  value={userDetail?.purchasingGroup}
                />
              </div>
            </Grid>

            {/* <Grid item xs={6}>
              <FormControl className={classes.userInfoContainerText} fullWidth>
                <Select
                  className={classes.userInfoContainerText}
                  placeholder="Purchasing Group"
                  select
                  size="small"
                  name="purchasingGroup"
                  value={userDetail?.purchasingGroup}
                  onChange={(e) => {
                    setUserDetail({
                      ...userDetail,
                      purchasingGroup: e.target.value,
                    });
                  }}
                  displayEmpty={true}
                  // disabled={
                  //   !userData?.role?.toLowerCase()?.includes("it admin") ?? false
                  // }
                  renderValue={() =>
                    userDetail?.purchasingGroup !== "" ? (
                      userDetail?.purchasingGroup
                    ) : (
                      <div
                        className="placeholderstyle"
                        style={{ color: "#C1C1C1" }}
                      >
                        Select Purchasing Group{" "}
                      </div>
                    )
                  }
                >
                  <MenuItem sx={font_Small} value={""}>
                    <div style={{ color: "#C1C1C1" }}>
                      Select Purchasing Group{" "}
                    </div>
                  </MenuItem>
                  {purchasingGroups &&
                    Object.keys(purchasingGroups)?.map((gData) => {
                      return (
                        <MenuItem
                          sx={font_Small}
                          value={`${gData} - ${purchasingGroups[gData]}`}
                        >
                          {gData + " - " + purchasingGroups[gData]}
                        </MenuItem>
                      );
                    })}
                </Select>
              </FormControl>

              {basicReducerState?.users
                ?.filter((u) => u?.id !== userDetail?.id)
                ?.find(
                  (u) => u?.purchasingGroup === userDetail?.purchasingGroup
                ) && (
                <p style={{ color: "red", fontSize: 12 }}>
                  Please enter different purchasing group
                </p>
              )}
            </Grid> */}
          </Grid>
        )}
        {userDetail?.roleName?.toLowerCase()?.includes("supplier") && (
          <Grid
            container
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Grid
              item
              xs={4}
              sx={{
                display: "flex",
                alignItems: "center",
                height: "max-content",
                marginTop: "1rem",
              }}
            >
              <Typography variant="body2">Supplier</Typography>
            </Grid>

            <Grid item xs={6}>
              <div>
                <TextField
                  disabled={true}
                  className={classes.userInfoContainerText}
                  inputProps={{
                    style: {
                      fontFamily: "Roboto, Helvetica, Arial, sans-serif",
                      fontWeight: "400",
                      fontSize: "12px",
                    },
                  }}
                  fullWidth
                  size="small"
                  value={userDetail?.supplierId}
                />
              </div>
            </Grid>

            {/* <Grid item xs={6}>
              <FormControl className={classes.userInfoContainerText} fullWidth>
                <Select
                  className={classes.userInfoContainerText}
                  placeholder="Supplier"
                  select
                  size="small"
                  name="supplierId"
                  value={userDetail?.supplierId}
                  onChange={(e) => {
                    setUserDetail({
                      ...userDetail,
                      supplierId: e.target.value,
                    });
                  }}
                  displayEmpty={true}
                  renderValue={() =>
                    userDetail?.supplierId !== "" ? (
                      userDetail?.supplierId
                    ) : (
                      <div
                        className="placeholderstyle"
                        style={{ color: "#C1C1C1" }}
                      >
                        Select Supplier{" "}
                      </div>
                    )
                  }
                >
                  <MenuItem sx={font_Small} value={""}>
                    <div style={{ color: "#C1C1C1" }}>Select Supplier </div>
                  </MenuItem>
                  {vendorDetailsSet &&
                    Object.keys(vendorDetailsSet)?.map((ven) => {
                      return (
                        <MenuItem
                          sx={font_Small}
                          value={`${ven} - ${vendorDetailsSet[ven]}`}
                        >
                          {ven + " - " + vendorDetailsSet[ven]}
                        </MenuItem>
                      );
                    })}
                </Select>
              </FormControl>

              {basicReducerState?.users
                ?.filter((u) => u?.id !== userDetail?.id)
                ?.find((u) => u?.supplierId === userDetail?.supplierId) && (
                <p style={{ color: "red", fontSize: 12 }}>
                  Please enter different Supplier ID
                </p>
              )}
            </Grid> */}
          </Grid>
        )}

        {/* {externalUserRoleAccess[userDetail?.roleName?.toLowerCase()] && (
          <>
            <Grid container sx={{
              marginTop: ".5rem",
            }}>
              <Grid item xs={4} sx={{
                display: "flex",
                alignItems: "center",
                height: "max-content",
                marginTop: "1rem",
              }}>
                <Typography variant="body2">Company</Typography>
              </Grid>

              <Grid item xs={6}>
                <FormControl
                  className={classes.userInfoContainerText}
                  fullWidth
                >
                  <Select
                    className={classes.userInfoContainerText}
                    placeholder="Company"
                    select
                    size="small"
                    name="companyCode"
                    value={userDetail?.companyCode}
                    onChange={(e) => {
                      setUserDetail({
                        ...userDetail,
                        companyCode: e.target.value,
                      });
                    }}
                    displayEmpty={true}
                    disabled={
                      (!userData?.role?.toLowerCase()?.includes("it admin") ||
                      !userData?.role?.toLowerCase()?.includes("super user")) ??
                      false
                    }
                    renderValue={() =>
                      userDetail?.companyCode !== "" ? (
                        userDetail?.companyCode
                      ) : (
                        <div
                          className="placeholderstyle"
                          style={{ color: "#C1C1C1" }}
                        >
                          Select Company{" "}
                        </div>
                      )
                    }
                  >
                    <MenuItem sx={font_Small} value={""}>
                      <div style={{ color: "#C1C1C1" }}>Select Company </div>
                    </MenuItem>
                    {companyCodeSet &&
                      Object.keys(companyCodeSet)?.map((com) => {
                        return (
                          <MenuItem
                            sx={font_Small}
                            value={`${com} - ${companyCodeSet[com]}`}
                          >
                            {com + " - " + companyCodeSet[com]}
                          </MenuItem>
                        );
                      })}
                  </Select>
                </FormControl>

                {basicReducerState?.users
                  ?.filter((u) => u?.id !== userDetail?.id)
                  ?.find((u) => u?.companyCode === userDetail?.companyCode) && (
                  <p style={{ color: "red", fontSize: 12 }}>
                    Please enter different Company Code
                  </p>
                )}
              </Grid>
            </Grid>
            {userDetail.companyCode && (
              <Grid container sx={{
                marginTop: ".5rem",
              }}>
                <Grid item xs={4} sx={{
                display: "flex",
                alignItems: "center",
                height: "max-content",
                marginTop: "1rem",
              }}>
                  <Typography variant="body2">Supplier</Typography>
                </Grid>

                <Grid item xs={6}>
                  <FormControl
                    className={classes.userInfoContainerText}
                    fullWidth
                  >
                    <Select
                      className={classes.userInfoContainerText}
                      placeholder="Supplier"
                      select
                      size="small"
                      name="supplierId"
                      value={userDetail?.supplierId}
                      onChange={(e) => {
                        setUserDetail({
                          ...userDetail,
                          supplierId: e.target.value,
                        });
                      }}
                      displayEmpty={true}
                      renderValue={() =>
                        userDetail?.supplierId !== "" ? (
                          userDetail?.supplierId
                        ) : (
                          <div
                            className="placeholderstyle"
                            style={{ color: "#C1C1C1" }}
                          >
                            Select Supplier{" "}
                          </div>
                        )
                      }
                    >
                      <MenuItem sx={font_Small} value={""}>
                        <div style={{ color: "#C1C1C1" }}>Select Supplier </div>
                      </MenuItem>
                      {vendorDetailsSet &&
                        Object.keys(vendorDetailsSet)?.map((ven) => {
                          return (
                            <MenuItem
                              sx={font_Small}
                              value={`${ven} - ${vendorDetailsSet[ven]}`}
                            >
                              {ven + " - " + vendorDetailsSet[ven]}
                            </MenuItem>
                          );
                        })}
                    </Select>
                  </FormControl>

                  {basicReducerState?.users
                    ?.filter((u) => u?.id !== userDetail?.id)
                    ?.find((u) => u?.supplierId === userDetail?.supplierId) && (
                    <p style={{ color: "red", fontSize: 12 }}>
                      Please enter different Supplier ID
                    </p>
                  )}
                </Grid>
              </Grid>
            )}
          </>
        )} */}
      </Grid>
    </div>
  );
};

const NewUserAssociatedGroup = ({
  open,
  onClose,
  userDetail,
  setUserDetail,
  params,
}) => {
  const classes = useStyle();
  const basicReducerState = useSelector((state) => state.userManagement);
  const [newAssociatedGroups, setNewAssociatedGroups] = useState([]);
  const [selectApplication, setSelectApplication] = useState("");

  const presentGroup = (groupId) => {
    return userDetail?.associatedGroups?.filter(
      (group) => group?.id === groupId
    )?.length > 0
      ? true
      : false;
  };

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose}>
      <DialogTitle className={classes.newUserAssociatedGroupDialogTitle}>
        New Associate Group
      </DialogTitle>

      <DialogContent>
        <Autocomplete
          multiple
          size="small"
          style={{ fontSize: 12 }}
          disableCloseOnSelect
          filterSelectedOptions
          value={newAssociatedGroups}
          onChange={(e, groups) => {
            setNewAssociatedGroups(groups);
          }}
          options={basicReducerState?.groups?.filter(
            (group) => !presentGroup(group?.id)
          )}
          getOptionLabel={(option) => option?.name}
          renderOption={(props, option, { selected }) => (
            <li {...props}>
              <Checkbox
                icon={<CheckBoxOutlineBlank fontSize="small" />}
                checkedIcon={<CheckBox color="primary" fontSize="small" />}
                checked={selected}
              />
              <Typography style={{ fontSize: 12 }}>{option?.name}</Typography>
            </li>
          )}
          renderInput={(params) => (
            <TextField
              {...params}
              variant="standard"
              style={{ fontSize: 12 }}
              label="Group"
            />
          )}
        />
      </DialogContent>

      <DialogActions className={classes.newUserAssociatedGroupDialogActions}>
        <Button
          key={"CANCEL"}
          size="small"
          variant="outlined"
          onClick={() => {
            onClose();
            setNewAssociatedGroups([]);
            setSelectApplication("");
          }}
        >
          Cancel
        </Button>

        <Button
          key={"ADD"}
          size="small"
          variant={newAssociatedGroups?.length === 0 ? "outlined" : "contained"}
          className="btn-ml"
          onClick={() => {
            setUserDetail({
              ...userDetail,
              associatedGroups: [
                ...newAssociatedGroups?.map((group) => ({
                  ...group,
                  status: "Draft",
                })),
                ...userDetail?.associatedGroups,
              ],
            });
            onClose();
            setNewAssociatedGroups([]);
            setSelectApplication("");
          }}
          style={{ textTransform: "capitalize" }}
          disabled={newAssociatedGroups?.length === 0}
        >
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const UserAssociatedGroups = ({
  userDetail,
  setUserDetail,
  load,
  setLoad,
  params,
}) => {
  const classes = useStyle();
  const dispatch = useDispatch();
  const basicReducerState = useSelector((state) => state.userManagement);
  const [openAssociatedGroupDialog, setOpenAssociatedGroupDialog] =
    useState(false);
  const [deletingAssociatedGroup, setDeletingAssociatedGroup] = useState(null);

  const getApplicationameById = (applicationId) => {
    const application = findApplicationById(
      applicationId,
      basicReducerState.applications
    );
    return application?.name;
  };
  const deleteAssociatedGroup = (associatedGroup) => {
    if (associatedGroup?.status === "Draft") {
      setUserDetail({
        ...userDetail,
        associatedGroups: userDetail?.associatedGroups?.filter(
          (group) => group?.id !== associatedGroup?.id
        ),
      });

      setDeletingAssociatedGroup(null);
    } else {
      setLoad(true);
      const disableUserAssociatedGroupUrl = `/${destination_IWA}/api/v1/groups/deleteUserFromGroup/deactivate`;
      const disableUserAssociatedGroupPayload = {
        userEmail: params?.userId,
        groupId: [Number(associatedGroup?.id)],
      };
      const disableUserAssociatedGroupRequestParam = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(disableUserAssociatedGroupPayload),
      };
      fetch(
        disableUserAssociatedGroupUrl,
        disableUserAssociatedGroupRequestParam
      )
        .then((res) => res.json())
        .then((groups) => {
          setLoad(false);
          setUserDetail({
            ...userDetail,
            associatedGroups:
              userDetail?.associatedGroups?.filter(
                (group) => group?.id !== associatedGroup?.id
              ) || [],
          });
          setDeletingAssociatedGroup(null);

          dispatch(
            setResponseMessage({
              open: true,
              status: groups?.status ? "success" : "error",
              message: groups?.status
                ? "Associated Group to user deleted successfully"
                : "Something went wrong",
            })
          );
        })
        .catch((err) => {
          setLoad(false);
        });
    }
  };

  return (
    <>
      <NewUserAssociatedGroup
        open={openAssociatedGroupDialog}
        onClose={() => setOpenAssociatedGroupDialog(false)}
        userDetail={userDetail}
        setUserDetail={setUserDetail}
        params={params}
      />

      <DeletionMessageBox
        open={deletingAssociatedGroup ? true : false}
        onClose={() => setDeletingAssociatedGroup(null)}
        onDelete={() => {
          deleteAssociatedGroup(deletingAssociatedGroup);
        }}
        load={load}
      />

      <TableContainer
        className={`${classes.userAssociatedGroupsTableContainer} iagScroll`}
      >
        <Table size="small">
          <TableHead className={classes.userAssociatedGroupsTableHead}>
            <TableRow>
              <TableCell className={classes.userAssociatedGroupsTableHeadCell}>
                Group
              </TableCell>

              <TableCell
                align="center"
                className={classes.userAssociatedGroupsTableHeadCell}
              >
                Action
              </TableCell>
            </TableRow>
          </TableHead>

          <TableBody
            className={`${classes.userAssignedRolesTableBody} iagScroll`}
          >
            {userDetail?.associatedGroups?.map((associatedGroup, index) => {
              return (
                <TableRow
                  key={`${associatedGroup?.id}-${index}`}
                  className={classes.userAssociatedGroupsTableBodyRow}
                >
                  <TableCell
                    className={classes.userAssociatedGroupsTableBodyCell}
                  >
                    {associatedGroup?.name}
                  </TableCell>

                  <TableCell
                    align="center"
                    className={classes.userAssociatedGroupsTableBodyCell}
                  >
                    <Tooltip
                      title={
                        associatedGroup?.status === "Draft"
                          ? "Remove"
                          : "Delete"
                      }
                    >
                      <IconButton
                        // color="secondary"
                        onClick={(e) => {
                          e.stopPropagation();
                          // deleteAssociatedGroup(associatedGroup);
                          setDeletingAssociatedGroup(associatedGroup);
                        }}
                        disabled={load}
                      >
                        {associatedGroup?.status === "Draft" ? (
                          <Remove style={{ fontSize: 16 }} />
                        ) : (
                          <DeleteOutlinedIcon color="danger" />
                        )}
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
      <div style={{ display: "flex", justifyContent: "flex-end" }}>
        <Button
          size="small"
          variant="contained"
          sx={{ margin: "1rem 0" }}
          onClick={() => setOpenAssociatedGroupDialog(true)}
          startIcon={<Add />}
          disabled={load}
        >
          Add
        </Button>
      </div>
    </>
  );
};

const NewUserAssignedRole = ({
  open,
  onClose,
  userDetail,
  setUserDetail,
  params,
}) => {
  const classes = useStyle();
  const basicReducerState = useSelector((state) => state.userManagement);
  const [applicationId, setApplicationId] = useState(
    basicReducerState?.applications[0].id
  );
  const initialNewAssignedRole = {
    userEmail: params?.userId,
    roleId: "",
    status: "Draft",
    isActive: 1,
    isDeleted: 0,
    // applicationId: "",
    // associationType: "ROLE",
    // createdBy: userReducerState?.user?.email,
    // createdOn: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
    // endDate: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
    // startDate: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
    // updatedBy: userReducerState?.user?.email,
    // updatedOn: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
    isEdited: false,
    isGroupRole: 0,
    groupRole: "",
  };
  const [newAssignedRoles, setNewAssignedRoles] = useState([]);
  // const [newAssignedRole, setNewAssignedRole] = useState(
  //   initialNewAssignedRole
  // );
  const assignedRolesId =
    userDetail?.assignedRoles?.map((assignedRole) =>
      Number(assignedRole?.roleId)
    ) || [];

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose}>
      <DialogTitle
        sx={{
          height: "3rem",
          display: "flex",
          margin: 0,
          justifyContent: "space-between",
          alignItems: "center",
          padding: ".5rem",
          paddingLeft: "1rem",
          backgroundColor: "#EAE9FF40",
        }}
      >
        <Typography variant="h6">New Assigned Role</Typography>
        <IconButton
          sx={{ width: "max-content" }}
          onClick={onClose}
          children={<CloseIcon />}
        />
      </DialogTitle>

      <DialogContent sx={{ padding: "1rem 1rem" }}>
        <Grid
          container
          sx={{
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Grid
            item
            xs
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Typography variant="body1">
              Roles<span style={{ color: "red" }}>*</span>
            </Typography>
            <Autocomplete
              // multiple
              size="small"
              style={{ fontSize: 12 }}
              disableCloseOnSelect
              filterSelectedOptions
              value={newAssignedRoles}
              onChange={(e, roles) => {
                setNewAssignedRoles(roles);
              }}
              options={basicReducerState?.roles}
              getOptionLabel={(option) => option?.name}
              renderOption={(props, option, { selected }) => (
                <li {...props}>
                  {/* <Checkbox
                    icon={<CheckBoxOutlineBlank fontSize="small" />}
                    checkedIcon={<CheckBox color="primary" fontSize="small" />}
                    checked={selected}
                  /> */}
                  <Typography style={{ fontSize: 12 }}>
                    {option?.name}
                  </Typography>
                </li>
              )}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  style={{ fontSize: 12 }}
                  placeholder={newAssignedRoles[0] ? `` : `Select Roles`}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
        <Button
          key={"CANCEL"}
          size="small"
          variant="outlined"
          onClick={() => {
            onClose();
            // setApplicationId(basicReducerState?.applications[0].id);
            setNewAssignedRoles([]);
          }}
        >
          Cancel
        </Button>

        <Button
          key={"ADD"}
          size="small"
          variant={newAssignedRoles?.length === 0 ? "outlined" : "contained"}
          className="btn-ml"
          onClick={() => {
            setUserDetail({
              ...userDetail,
              assignedRoles: [
                ...newAssignedRoles?.map((role) => ({
                  ...initialNewAssignedRole,
                  applicationId: Number(applicationId),
                  roleId: Number(role?.id),
                  associationId: Number(role?.id),
                })),
                ...userDetail?.assignedRoles,
              ],
            });
            onClose();
            // setApplicationId(basicReducerState?.applications[0].id);
            setNewAssignedRoles([]);
          }}
          style={{ textTransform: "capitalize" }}
          disabled={newAssignedRoles?.length === 0}
        >
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const UserAssignedRoles = ({
  userDetail,
  setUserDetail,
  load,
  setLoad,
  params,
}) => {
  const classes = useStyle();
  const basicReducerState = useSelector((state) => state.userManagement);
  const [openAssignRoleDialog, setOpenAssignRoleDialog] = useState(false);
  const [deletingAssignedRole, setDeletingAssignedRole] = useState(null);
  const dispatch = useDispatch();
  const [openDeletionDialog, setOpenDeletionDialog] = useState(false);
  const [promptType, setPromptType] = useState("dialog");
  const [promptMessage, setPromptMessage] = useState("");

  const getApplicationameById = (applicationId) => {
    const application = findApplicationById(
      applicationId,
      basicReducerState.applications
    );
    return application?.name;
  };
  const deleteAssignedRole = () => {
    if (deletingAssignedRole?.status === "Draft") {
      setUserDetail({
        ...userDetail,
        assignedRoles: userDetail?.assignedRoles?.filter(
          (role) =>
            !(
              role?.applicationId === deletingAssignedRole?.applicationId &&
              role?.associationId === deletingAssignedRole?.associationId
            )
        ),
      });
      setDeletingAssignedRole(null);
    } else {
      setLoad(true);
      const disableUserRoleMappingUrl = `/${destination_IWA}/api/v1/users/userRoleMapping`;
      const disableUserRoleMappingPayload = {
        userEmail: deletingAssignedRole?.userId,
        roleId: Number(deletingAssignedRole?.associationId),
        status: "Inactive",
        isDeleted: 1,
        isActive: 0,
        isGroupRole: deletingAssignedRole?.isGroupRole,
        groupRole: deletingAssignedRole?.groupRole,
      };
      const disableUserRoleMappingRequestParam = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(disableUserRoleMappingPayload),
      };
      fetch(disableUserRoleMappingUrl, disableUserRoleMappingRequestParam)
        .then((res) => res.json())
        .then((users) => {
          setLoad(false);
          setUserDetail({
            ...userDetail,
            assignedRoles:
              userDetail?.assignedRoles?.filter(
                (role) =>
                  !(
                    role?.applicationId ===
                      deletingAssignedRole?.applicationId &&
                    role?.associationId === deletingAssignedRole?.associationId
                  )
              ) || [],
          });

          if (users.statusCode === 201) {
            setPromptType("snackbar");
            setPromptMessage(`Role deleted successfully`);
            setOpenDeletionDialog(true);
          } else {
            setPromptType("snackbar");
            setPromptMessage(`Role deletion failed`);
            setOpenDeletionDialog(true);
          }
          setOpenDeletionDialog(false);
          setPromptType("");
          setPromptMessage("");

          var applicationNameMap = {};
          userDetail?.assignedRoles?.map((role) => {
            if (
              !(
                role?.applicationId === deletingAssignedRole?.applicationId &&
                role?.associationId === deletingAssignedRole?.associationId
              )
            ) {
              const application_name = getApplicationameById(
                role.applicationId
              );
              applicationNameMap[application_name] = 1;
            }
            return null;
          });
          dispatch(
            setUsers(
              basicReducerState?.users?.map((user) =>
                user?.emailId === params?.userId
                  ? {
                      ...user,
                      applicationName: Object.keys(applicationNameMap),
                    }
                  : user
              ) || []
            )
          );
          setDeletingAssignedRole(null);

          dispatch(
            setResponseMessage({
              open: true,
              status: users?.status ? "success" : "error",
              message: users?.status
                ? "Mapped role to user deleted successfully"
                : "Something went wrong",
            })
          );
        })
        .catch((err) => {
          setLoad(false);
        });
    }
  };

  const assignedRolesColumns = [
    // {
    //   field: "applicationName",
    //   headerName: "Application",
    //   flex: 1,
    //   renderCell: (data) => {
    //     return (
    //       <>
    //         {data.row?.status === "Draft" ? (
    //           <Select
    //             size="small"
    //             style={{ fontSize: 12 }}
    //             value={data.row?.applicationId}
    //             readOnly
    //           >
    //             {basicReducerState?.applications?.map(
    //               (application, index) => (
    //                 <MenuItem
    //                   key={`${application?.id}-${index}`}
    //                   value={application?.id}
    //                   style={{ fontSize: 12 }}
    //                 >
    //                   {application?.name}
    //                 </MenuItem>
    //               )
    //             )}
    //           </Select>
    //         ) : (
    //           data.row?.applicationName
    //         )}
    //       </>
    //     )
    //   }
    // },
    {
      field: "roleName",
      headerName: "Role",
      flex: 1,
      renderCell: (data) => {
        return (
          <>
            {data.row?.status === "Draft" ? (
              <Select
                size="small"
                style={{ fontSize: 12 }}
                value={data.row?.associationId}
                readOnly
              >
                {basicReducerState?.roles.map((role, index) => (
                  <MenuItem
                    key={`${role?.id}-${index}`}
                    value={role?.id}
                    style={{ fontSize: 12 }}
                  >
                    {role?.name}
                  </MenuItem>
                ))}
              </Select>
            ) : (
              data.row?.roleName
            )}
          </>
        );
      },
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
    },
    {
      field: "action",
      headerName: "Action",
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (data) => {
        return (
          <>
            <Tooltip title={data.row?.status === "Draft" ? "Remove" : "Delete"}>
              <IconButton
                // color="secondary"
                onClick={(e) => {
                  e.stopPropagation();
                  // deleteAssignedRole(assignedRole);
                  setOpenDeletionDialog(true);
                  setDeletingAssignedRole(data.row);
                  setPromptType("dialog");
                  setPromptMessage(`Do you want to delete the role?`);
                }}
                disabled={load}
              >
                {data.row?.status === "Draft" ? (
                  <Remove style={{ fontSize: 16 }} />
                ) : (
                  <DeleteOutlinedIcon color="danger" />
                )}
              </IconButton>
            </Tooltip>
          </>
        );
      },
    },
  ];

  return (
    <>
      <NewUserAssignedRole
        open={openAssignRoleDialog}
        onClose={() => setOpenAssignRoleDialog(false)}
        userDetail={userDetail}
        setUserDetail={setUserDetail}
        params={params}
      />

      <ReusablePromptBox
        type={promptType}
        promptState={openDeletionDialog}
        setPromptState={setOpenDeletionDialog}
        dialogSeverity={"danger"}
        dialogTitleText={"Role Deletion"}
        cancelButtonText={"Cancel"}
        showCancelButton={true}
        okButtonText={"Delete"}
        showOkButton={true}
        promptMessage={promptMessage}
        handleOkButtonAction={deleteAssignedRole}
        handleSnackBarPromptClose={() => {
          setOpenDeletionDialog(false);
        }}
      />

      <div className={classes.roleInfoContainer}>
        <ReusableTable
          width="100%"
          status_onRowSingleClick={false}
          rows={
            userDetail?.assignedRoles ?? []
            //  [ {
            //     "userId": "<EMAIL>",
            //     "applicationId": 4,
            //     "applicationLabel": "CW-SCP-DEV",
            //     "applicationName": "CW-SCP-DEV",
            //     "roleId": 10,
            //     "roleLabel": "Test Role/Supplier",
            //     "roleName": "Test Role/Supplier",
            //     "isComposite": 0,
            //     "associateRoles": "",
            //     "associationId": 10,
            //     "associationType": null,
            //     "permissionType": null,
            //     "status": "Active",
            //     "isDeleted": 0,
            //     "isActive": 1,
            //     "createdBy": "<EMAIL>",
            //     "createdOn": "2023-03-02 04:36:49.*********",
            //     "updatedBy": " ",
            //     "updatedOn": null
            //   }]
          }
          columns={assignedRolesColumns}
          getRowIdValue={"roleId"}
          stopPropagation_Column={"action"}
          hideFooter={true}
          noOfColumns={5}
          rowsPerPageOptions={[5, 10, 15]}
          checkboxSelection={false}
          disableSelectionOnClick={false}
        />
        <div style={{ display: "flex", justifyContent: "flex-end" }}>
          <Button
            size="small"
            variant="contained"
            sx={{ margin: "1rem 0" }}
            onClick={() => setOpenAssignRoleDialog(true)}
            startIcon={<Add />}
            disabled={load}
          >
            Add
          </Button>
        </div>
      </div>
    </>
  );
};

const NewUserAdditioanlFeature = ({
  open,
  onClose,
  userDetail,
  setUserDetail,
  params,
}) => {
  const classes = useStyle();
  const basicReducerState = useSelector((state) => state.userManagement);
  const [selecteApplication, setSelecteApplication] = useState(null);
  const [newAdditionalFeatures, setNewAdditionalFeatures] = useState([]);

  const getEmailById = (userId) => {
    const user = findUserById(userId, basicReducerState?.users);
    return user?.emailId || "";
  };

  const handleAddNewAdditionalFeature = () => {
    setUserDetail({
      ...userDetail,
      exceptionalActivities: [
        ...newAdditionalFeatures?.map((newAdditionalFeature) => ({
          activityId: Number(newAdditionalFeature?.activityId),
          emailId: params?.userId,
          type: "Access",
          status: "Draft",
          isActive: 1,
          isDeleted: 0,
          entityId: Number(newAdditionalFeature?.entityId),
          applicationId: Number(newAdditionalFeature?.applicationId),
          applicationName: newAdditionalFeature?.applicationName,
          entityName: newAdditionalFeature?.entityName,
          activityName: newAdditionalFeature?.activityName,
          userId: params?.userId,
          // createdBy: userReducerState?.user?.email,
          // createdOn: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
          // updatedBy: userReducerState?.user?.email,
          // updatedOn: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        })),
        ...userDetail?.exceptionalActivities,
      ],
      unassignedActivities:
        userDetail?.unassignedActivities.filter(
          (activity) =>
            !newAdditionalFeatures?.find(
              (activity) => activity?.id === activity.activityId
            )
        ) || [],
    });
  };

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose}>
      <DialogTitle
        sx={{
          height: "3rem",
          display: "flex",
          margin: 0,
          justifyContent: "space-between",
          alignItems: "center",
          padding: ".5rem",
          paddingLeft: "1rem",
          backgroundColor: "#EAE9FF40",
        }}
      >
        <Typography variant="h6">New Additional Feature</Typography>
        <IconButton
          sx={{ width: "max-content" }}
          onClick={onClose}
          children={<CloseIcon />}
        />
      </DialogTitle>

      <DialogContent sx={{ padding: "1rem 1rem" }}>
        <Grid
          container
          sx={{
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Grid
            item
            xs
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Typography variant="body1">
              Application Name<span style={{ color: "red" }}>*</span>
            </Typography>
            <FormControl variant="outlined" size="small" fullWidth required>
              <Select
                size="small"
                value={selecteApplication}
                onChange={(e) => {
                  setSelecteApplication(e.target.value);
                }}
              >
                {basicReducerState?.applications?.map((application, index) => (
                  <MenuItem
                    key={`${application}-${index}`}
                    value={application?.name}
                    style={{ fontSize: 12 }}
                  >
                    {application?.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid
            item
            xs
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Typography variant="body1">
              Features<span style={{ color: "red" }}>*</span>
            </Typography>
            <Autocomplete
              multiple
              size="small"
              style={{ fontSize: 12 }}
              disableCloseOnSelect
              filterSelectedOptions
              value={newAdditionalFeatures}
              onChange={(e, activities) => {
                setNewAdditionalFeatures(activities);
              }}
              options={userDetail?.unassignedActivities?.filter(
                (unassignedActivity) =>
                  unassignedActivity?.applicationName === selecteApplication
              )}
              groupBy={(option) => option?.entityName}
              getOptionLabel={(option) =>
                `${option?.activityName} (${option?.entityName}) - ${option?.applicationName}`
              }
              renderOption={(props, option, { selected }) => (
                <li {...props}>
                  <Checkbox
                    icon={<CheckBoxOutlineBlank fontSize="small" />}
                    checkedIcon={<CheckBox color="primary" fontSize="small" />}
                    checked={selected}
                  />
                  <Typography style={{ fontSize: 12 }}>
                    {`${option?.activityName} (${option?.entityName}) - ${option?.applicationName}`}
                  </Typography>
                </li>
              )}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  style={{ fontSize: 12 }}
                  placeholder={
                    newAdditionalFeatures[0] ? `` : `Select Features`
                  }
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
        <Button
          key={"CANCEL"}
          size="small"
          variant="outlined"
          onClick={() => {
            onClose();
            setNewAdditionalFeatures([]);
            setSelecteApplication(null);
          }}
        >
          Cancel
        </Button>

        <Button
          key={"ADD"}
          size="small"
          variant={
            newAdditionalFeatures?.length === 0 || selecteApplication === null
              ? "outlined"
              : "contained"
          }
          className="btn-ml"
          onClick={() => {
            handleAddNewAdditionalFeature();
            onClose();
            setNewAdditionalFeatures([]);
            setSelecteApplication(null);
          }}
          style={{ textTransform: "capitalize" }}
          disabled={
            newAdditionalFeatures?.length === 0 || selecteApplication === null
          }
        >
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const UserDefaultFeatures = ({
  userDetail,
  setUserDetail,
  revokeFeature,
  load,
  setLoad,
}) => {
  const classes = useStyle();
  const dispatch = useDispatch();
  const basicReducerState = useSelector((state) => state.userManagement);
  const [deletingUser, setDeletingUser] = useState(null);
  const [promptType, setPromptType] = useState("");
  const [promptMessage, setPromptMessage] = useState("");

  const getApplicationNameById = (applicationId) => {
    const application = findApplicationById(
      Number(applicationId),
      basicReducerState.applications
    );
    return application?.name || "-";
  };

  const defaultFeaturesColumns = [
    {
      field: "entityName",
      headerName: "Module",
      flex: 1,
    },
    {
      field: "activityName",
      headerName: "Feature",
      flex: 1,
    },
    {
      field: "action",
      headerName: "Action",
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (data) => {
        return (
          <Tooltip title={"Revoke"}>
            <IconButton
              onClick={() => {
                revokeFeature(data.row, () => {
                  setUserDetail({
                    ...userDetail,
                    exceptionalActivities: [
                      {
                        activityId: data.row?.activityId,
                        activityName: data.row?.activityName,
                        applicationId: data.row?.applicationId,
                        entityId: data.row?.entityId,
                        entityName: data.row?.entityName,
                        type: "Revoke",
                      },
                      ...userDetail.exceptionalActivities,
                    ],
                    assignedActivities: userDetail?.assignedActivities?.filter(
                      (activity) =>
                        Number(activity?.activityId) !==
                        Number(data.row?.activityId)
                    ),
                  });

                  dispatch(
                    setResponseMessage({
                      open: true,
                      status: "success",
                      message: "Feature removed successfully",
                    })
                  );
                });
              }}
              disabled={load}
            >
              <ReusableIcon iconName="Undo" iconColor="gray" />
            </IconButton>
          </Tooltip>
        );
      },
    },
  ];

  return (
    <>
      <Loading load={load} />

      <div className={classes.roleInfoContainer}>
        <ReusableTable
          width="100%"
          status_onRowSingleClick={false}
          rows={
            userDetail?.assignedActivities ?? []
            //   [{
            //     "applicationId": 4,
            //     "roleId": 10,
            //     "roleName": "Test Role/Supplier",
            //     "entityId": 34,
            //     "entityName": "E-Invoice",
            //     "activityId": 80,
            //     "activityName": "Create Service Request - EInvoice"
            //   },
            //   {
            //     "applicationId": 4,
            //     "roleId": 10,
            //     "roleName": "Test Role/Supplier",
            //     "entityId": 22,
            //     "entityName": "User Management",
            //     "activityId": 44,
            //     "activityName": "Users"
            //   }
            // ]
          }
          columns={defaultFeaturesColumns}
          getRowIdValue={"activityId"}
          stopPropagation_Column={"action"}
          hideFooter={false}
          noOfColumns={5}
          rowsPerPageOptions={[5, 10, 15]}
          checkboxSelection={false}
          disableSelectionOnClick={false}
        />
      </div>
    </>
  );
};

const UserAdditionalFeatures = ({
  userDetail,
  setUserDetail,
  load,
  setLoad,
  params,
}) => {
  const dispatch = useDispatch();
  const basicReducerState = useSelector((state) => state.userManagement);
  const classes = useStyle();
  const [openAdditionalFeatureDialog, setOpenAdditionalFeatureDialog] =
    useState(false);

  const getEmailById = (userId) => {
    const user = findUserById(userId, basicReducerState?.users);
    return user?.emailId || "";
  };
  const getApplicationNameById = (applicationId) => {
    const application = findApplicationById(
      Number(applicationId),
      basicReducerState.applications
    );
    return application?.name || "-";
  };
  const removeAdditionalFeature = (assignedActivity) => {
    setUserDetail({
      ...userDetail,
      exceptionalActivities:
        userDetail?.exceptionalActivities?.filter(
          (activity) => activity.activityId !== assignedActivity.activityId
        ) || [],
      unassignedActivities: [
        ...userDetail?.unassignedActivities,
        assignedActivity,
      ],
    });
  };
  const revokeAdditionalFeature = (assignedActivity) => {
    const updateAdditionalFeaturePayload = {
      activityId: Number(assignedActivity?.activityId),
      emailId: params?.userId,
      type: "Revoke",
      status: "Draft",
      isActive: 1,
      isDeleted: 0,
      // createdBy: userReducerState?.user?.email,
      // createdOn: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      // updatedBy: userReducerState?.user?.email,
      // updatedOn: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      entityId: Number(assignedActivity?.entityId),
      applicationId: Number(assignedActivity?.applicationId),
    };
    // console.log(updateAdditionalFeaturePayload);
    updateAdditionalFeature(updateAdditionalFeaturePayload, () => {
      setUserDetail({
        ...userDetail,
        unassignedActivities: [
          {
            activityId: Number(assignedActivity?.activityId),
            activityName: assignedActivity?.activityName,
            applicationId: Number(assignedActivity?.applicationId),
            applicationName: getApplicationNameById(
              Number(assignedActivity?.applicationId)
            ),
            entityId: Number(assignedActivity?.entityId),
            entityName: assignedActivity?.entityName,
          },
          ...userDetail.unassignedActivities,
        ],
        exceptionalActivities:
          userDetail.exceptionalActivities.filter(
            (activity) =>
              Number(activity?.activityId) !==
              Number(assignedActivity?.activityId)
          ) || [],
      });

      dispatch(
        setResponseMessage({
          open: true,
          status: "success",
          message: "Additional feature removed successfully",
        })
      );
    });
  };
  const accessAdditionalFeature = (assignedActivity) => {
    const updateAdditionalFeaturePayload = {
      activityId: Number(assignedActivity?.activityId),
      emailId: params?.userId,
      type: "Access",
      status: "Draft",
      isActive: 1,
      isDeleted: 0,
      // createdBy: userReducerState?.user?.email,
      // createdOn: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      // updatedBy: userReducerState?.user?.email,
      // updatedOn: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      entityId: Number(assignedActivity?.entityId),
      applicationId: Number(assignedActivity?.applicationId),
    };
    // console.log(updateAdditionalFeaturePayload);
    updateAdditionalFeature(updateAdditionalFeaturePayload, () => {
      setUserDetail({
        ...userDetail,
        assignedActivities: [
          {
            activityId: Number(assignedActivity?.activityId),
            activityName: assignedActivity?.activityName,
            applicationId: Number(assignedActivity?.applicationId),
            entityId: Number(assignedActivity?.entityId),
            entityName: assignedActivity?.entityName,
          },
          ...userDetail.assignedActivities,
        ],
        exceptionalActivities:
          userDetail.exceptionalActivities.filter(
            (activity) =>
              Number(activity?.activityId) !==
              Number(assignedActivity?.activityId)
          ) || [],
      });

      dispatch(
        setResponseMessage({
          open: true,
          status: "success",
          message: "Additional features added successfully",
        })
      );
    });
  };
  const updateAdditionalFeature = (
    updateAdditionalFeaturePayload,
    fSuccess
  ) => {
    setLoad(true);
    const updateAdditionalFeatureUrl = `/${destination_IWA}/api/v1/users/userFeatureException/deactivate?userEmail=${params?.userId}&activityId=${updateAdditionalFeaturePayload?.activityId}`;
    const updateAdditionalFeatureRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    };
    fetch(updateAdditionalFeatureUrl, updateAdditionalFeatureRequestParam)
      .then((res) => res.json())
      .then((data) => {
        fSuccess();
        setLoad(false);
      })
      .catch((err) => {
        setLoad(false);
      });
  };

  const exceptionFeaturesColumns = [
    {
      field: "entityName",
      headerName: "Module",
      flex: 1,
    },
    {
      field: "activityName",
      headerName: "Feature",
      flex: 1,
    },
    {
      field: "action",
      headerName: "Action",
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (data) => {
        return (
          <>
            {data.row?.status === "Draft" ? (
              <Tooltip title={data.row?.status}>
                <IconButton
                  color="secondary"
                  onClick={() => {
                    removeAdditionalFeature(data.row);
                  }}
                  disabled={load}
                >
                  <Remove style={{ fontSize: 16 }} />
                </IconButton>
              </Tooltip>
            ) : data.row?.type === "Revoke" ? (
              <Tooltip title={"Access"}>
                <IconButton
                  color="primary"
                  onClick={() => {
                    accessAdditionalFeature(data.row);
                  }}
                  disabled={load}
                >
                  <Add style={{ fontSize: 16 }} />
                </IconButton>
              </Tooltip>
            ) : (
              <Tooltip title={"Revoke"}>
                <IconButton
                  // color="secondary"
                  onClick={() => {
                    revokeAdditionalFeature(data.row);
                  }}
                  disabled={load}
                >
                  <DeleteOutlinedIcon color="danger" />
                </IconButton>
              </Tooltip>
            )}
          </>
        );
      },
    },
  ];

  return (
    <>
      <Loading load={load} />

      <NewUserAdditioanlFeature
        open={openAdditionalFeatureDialog}
        onClose={() => {
          setOpenAdditionalFeatureDialog(false);
        }}
        userDetail={userDetail}
        setUserDetail={setUserDetail}
        params={params}
      />

      <div className={classes.roleInfoContainer}>
        <ReusableTable
          width="100%"
          status_onRowSingleClick={false}
          rows={userDetail?.exceptionalActivities ?? []}
          columns={exceptionFeaturesColumns}
          getRowIdValue={"activityId"}
          stopPropagation_Column={"action"}
          hideFooter={false}
          noOfColumns={5}
          rowsPerPageOptions={[5, 10, 15]}
          checkboxSelection={false}
          disableSelectionOnClick={false}
        />
        <div style={{ display: "flex", justifyContent: "flex-end" }}>
          <Button
            size="small"
            variant="contained"
            sx={{ margin: "1rem 0" }}
            onClick={() => setOpenAdditionalFeatureDialog(true)}
            startIcon={<Add />}
            disabled={load}
          >
            Add
          </Button>
        </div>
      </div>
    </>
  );
};

const FeaturesContent = ({
  userDetail,
  setUserDetail,
  load,
  setLoad,
  params,
}) => {
  const basicReducerState = useSelector((state) => state.userManagement);
  const classes = useStyle();
  const [value, setValue] = useState("Default Features");
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const getEmailById = (userId) => {
    const user = findUserById(userId, basicReducerState?.users);
    return user?.emailId || "";
  };

  const revokeFeature = (assignedActivity, fSuccess) => {
    const updateAdditionalFeaturePayload = {
      activityId: Number(assignedActivity?.activityId),
      emailId: params?.userId,
      type: "Revoke",
      status: "Draft",
      isActive: 1,
      isDeleted: 0,
      applicationId: assignedActivity?.applicationId,
      entityId: assignedActivity?.entityId,
      userId: params?.userId,
    };
    updateAdditionalFeature(updateAdditionalFeaturePayload, fSuccess);
  };
  const updateAdditionalFeature = (
    updateAdditionalFeaturePayload,
    fSuccess
  ) => {
    setLoad(true);
    const updateAdditionalFeatureUrl = `/${destination_IWA}/api/v1/users/userFeatureException`;
    const updateAdditionalFeatureRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateAdditionalFeaturePayload),
    };
    fetch(updateAdditionalFeatureUrl, updateAdditionalFeatureRequestParam)
      .then((res) => res.json())
      .then((data) => {
        fSuccess();
        setLoad(false);
      })
      .catch((err) => {
        setLoad(false);
      });
  };

  return (
    <>
      <div 
      // style={{ height: "22rem" }}
      >
        <TabContext value={value}>
          <Box
            sx={{
              borderBottom: 1,
              borderColor: "divider",
              padding: "0px 12px 0px",
              position: "relative",
            }}
          >
            <TabList onChange={handleChange} aria-label="basic tabs example">
              <Tab
                label={
                  <Stack
                    direction="row"
                    sx={{
                      alignItems: "center",
                    }}
                  >
                    <SettingsIcon sx={{ fontSize: "15px" }} />
                    <Typography
                      variant="body1"
                      ml={1}
                      sx={{ fontWeight: 600, fontSize: "14px" }}
                    >
                      Default Features
                    </Typography>
                  </Stack>
                }
                value="Default Features"
                sx={{ textTransform: "none", fontWeight: "bold" }}
              />

              <Tab
                label={
                  <Stack
                    direction="row"
                    sx={{
                      alignItems: "center",
                    }}
                  >
                    <SettingsIcon sx={{ fontSize: "15px" }} />
                    <Typography
                      variant="body1"
                      ml={1}
                      sx={{ fontWeight: 600, fontSize: "14px" }}
                    >
                      Exceptional Features
                    </Typography>
                  </Stack>
                }
                value="Exceptional Features"
                sx={{ textTransform: "none", fontWeight: "bold" }}
              />
            </TabList>
            <TabPanel value={"Default Features"} sx={{ padding: "0px" }}>
              <Stack sx={{ paddingTop: ".5rem" }}>
                <UserDefaultFeatures
                  userDetail={userDetail}
                  setUserDetail={setUserDetail}
                  revokeFeature={revokeFeature}
                  load={load}
                  setLoad={setLoad}
                  params={params}
                />
              </Stack>
            </TabPanel>
            <TabPanel value={"Exceptional Features"} sx={{ padding: "0px" }}>
              <Stack sx={{ paddingTop: ".5rem" }}>
                <UserAdditionalFeatures
                  userDetail={userDetail}
                  setUserDetail={setUserDetail}
                  load={load}
                  setLoad={setLoad}
                  params={params}
                />
              </Stack>
            </TabPanel>
          </Box>
        </TabContext>
      </div>
    </>
  );
};

function UserDetail({ params, setParams, getUsersList }) {
  const basicReducerState = useSelector((state) => state.userManagement);
  const classes = useStyle();
  const [selectedUserDetailContentType, setSelectedUserDetailContentType] =
    useState("Basic Info");
  const [load, setLoad] = useState(false);
  const [userDetail, setUserDetail] = useState({
    // employeeId: null,
    // userId: "P000717",
    // userName: "Akash Jena",
    // displayName: "Akash Jena",
    // firstName: "Akash",
    // lastName: "Jena",
    // emailId: "<EMAIL>",
    // idp: "P000717",
    // status: "Active",
    // isDeleted: 0,
    // isActive: 1,
    // createdBy: "Admin",
    // createdOn: "2023-03-02 04:36:49.*********",
    // updatedBy: "null",
    // updatedOn: "2023-03-09 05:46:41.*********",
    // terminationDate: null,
    // supplierId: "INC - InstaBasket",
    // companyCode: "0001 - SAP A.G.",
    // roleName: "Test Role/Supplier",
    // rolesDetailsList: null,
  });
  const dispatch = useDispatch();

  useEffect(() => {
    if (params?.userId) {
      getUserInfoById();
    }
  }, [params?.userId]);

  const removeRepeatedValue = (valueList) => {
    var valueMap = {};
    valueList?.map((value) => {
      valueMap[value] = 1;
      return null;
    });
    return Object.keys(valueMap);
  };
  const getApplicationameById = (applicationId) => {
    const application = findApplicationById(
      Number(applicationId),
      basicReducerState.applications
    );
    return application?.name;
  };
  const getUserInfoById = () => {
    const getUserByIdUrl = `/${destination_IWA_NPI}/api/v1/usersMDG/userMDG/${params?.userId}`;
    const getUserByIdRequestParams = {
      headers: {
        "Content-Type": "application/json",
      },
    };
    const getUserAssociatedGroupUrl = `/${destination_IWA}/api/v1/groups/getUserGroup?userEmail=${params?.userId}`;
    const getUserAssociatedGroupRequestParams = {
      headers: {
        "Content-Type": "application/json",
      },
    };
    const getUserRoleMappingUrl = `/${destination_IWA}/api/v1/users/userRoleMapping/byUserEmail?userEmail=${params?.userId}`;
    const getUserRoleMappingRequestParam = {
      headers: {
        "Content-Type": "application/json",
      },
    };
    const userId = basicReducerState.users.find(
      (user) => user.emailId === params?.userId
    )?.userId;
    if (params?.userId && userId) {
      setLoad(true);
      setUserDetail({});
      Promise.all([
        fetch(getUserByIdUrl, getUserByIdRequestParams).then((res) =>
          res.json()
        ),
        fetch(
          getUserAssociatedGroupUrl,
          getUserAssociatedGroupRequestParams
        ).then((res) => res.json()),
        fetch(getUserRoleMappingUrl, getUserRoleMappingRequestParam).then(
          (res) => res.json()
        ),
      ])
        .then(
          ([
            userDetail,
            userAssociatedGroupDetail,
            userAssignedRolesDetail,
          ]) => {
            const postMappedAdditionFeatureUrl = `/${destination_IWA}/api/v1/applications/assignedAndUnassigneFeatures`;
            var roleIdList = [];
            userAssociatedGroupDetail?.data?.map((group) => {
              const roleIds = group?.roleIdList?.split(",");
              roleIds?.map((id) => {
                roleIdList.push(id);
                return null;
              });
              return null;
            });
            userAssignedRolesDetail?.data?.map((role) => {
              const roleDet = findRoleById(
                role?.roleId,
                basicReducerState?.roles
              );
              if (roleDet?.isComposite === 1) {
                const associateRolesId = role?.associateRoles?.split(",");
                associateRolesId.map((id) => {
                  roleIdList.push(id);
                  return null;
                });
              } else {
                roleIdList.push(role?.roleId);
              }
              return null;
            });
            const postMappedAdditionFeaturePayload = {
              userEmail: params?.userId,
              applicationId:
                removeRepeatedValue(
                  userAssignedRolesDetail?.data?.map(
                    (role) => role?.applicationId
                  ) || []
                )?.map((appId) => Number(appId)) || [],
              roleId:
                removeRepeatedValue(roleIdList)?.map((rId) => Number(rId)) ||
                [],
            };
            const postMappedAdditionFeatureRequestParam = {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(postMappedAdditionFeaturePayload),
            };
            fetch(
              postMappedAdditionFeatureUrl,
              postMappedAdditionFeatureRequestParam
            )
              .then((res) => res.json())
              .then((userAdditionalFeatures) => {
                setLoad(false);
                setUserDetail({
                  ...userDetail.data,
                  assignedRoles: userAssignedRolesDetail?.data || [],
                  assignedActivities: userAdditionalFeatures?.data?.[0] || [],
                  unassignedActivities: userAdditionalFeatures?.data?.[1] || [],
                  exceptionalActivities:
                    userAdditionalFeatures?.data?.[2] || [],
                  associatedGroups: userAssociatedGroupDetail?.data || [],
                });
                var applicationNameMap = {};
                userAssignedRolesDetail?.data?.map((role) => {
                  const application_name = getApplicationameById(
                    role.applicationId
                  );
                  applicationNameMap[application_name] = 1;
                  return null;
                });
                dispatch(
                  setUsers(
                    basicReducerState?.users?.map((user) =>
                      user?.emailId === params?.userId
                        ? {
                            ...user,
                            displayName: userDetail?.data?.displayName,
                            applicationName:
                              Object?.keys(applicationNameMap || {}) || [],
                          }
                        : user
                    ) || []
                  )
                );
              })
              .catch((err) => {
                setLoad(false);
              });
          }
        )
        .catch((err) => {
          setLoad(false);
          setUserDetail({});
        });
    }
  };

  const updateUserInfo = () => {
    setLoad(true);
    const updateUserInfoUrl = `/${destination_IWA_NPI}/api/v1/usersMDG/userAndUserRoleMappingMDG`;
    const updateUserInfoPayload = {
      userId: userDetail?.userId,
      userName: userDetail?.userName,
      displayName: userDetail?.displayName,
      firstName: userDetail?.firstName,
      lastName: userDetail?.lastName,
      emailId: userDetail?.emailId,
      status: "Active",
      isActive: 1,
      isDeleted: 0,
      companyCode: userDetail?.companyCode ?? "",
      supplierId: userDetail.companyCode ? userDetail?.supplierId ?? "" : "",
      purchasingGroup: userDetail?.purchasingGroup ?? "",
      userRoles:
        userDetail?.assignedRoles?.filter(
          (assignRole) => assignRole?.status === "Draft"
        ) || [],
      featuresException:
        userDetail?.exceptionalActivities?.filter(
          (assignedActivity) => assignedActivity?.status === "Draft"
        ) || [],
    };
    const updateUserInfoRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateUserInfoPayload),
    };
    const updateUserGroupUrl = `/${destination_IWA}/api/v1/groups/addUserToGroups`;
    const updateUserGroupPayload = {
      userEmail: params?.userId,
      groupId:
        userDetail?.associatedGroups
          ?.filter((group) => group?.status === "Draft")
          ?.map((group) => Number(group?.id)) || [],
    };
    const updateUserGroupRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateUserGroupPayload),
    };
    Promise.all([
      fetch(updateUserInfoUrl, updateUserInfoRequestParam).then((res) =>
        res.json()
      ),
      fetch(updateUserGroupUrl, updateUserGroupRequestParam).then((res) =>
        res.json()
      ),
    ])
      .then(([data, associateGroupData]) => {
        setLoad(false);
        getUserInfoById();
        getUsersList(userDetail?.emailId, userDetail?.displayName);

        dispatch(
          setResponseMessage({
            open: true,
            status:
              data?.status && associateGroupData?.status ? "success" : "error",
            message:
              data?.status && associateGroupData?.status
                ? "User details updated successfully"
                : "Something went wrong",
          })
        );
      })
      .catch((err) => {
        setLoad(false);
      });
  };

  const [value, setValue] = useState("Basic Info");
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <Paper sx={{ height: "max-content" }}>
      <Loading load={load} />

      <>
        <TabContext value={value}>
          <Box
            sx={{
              borderBottom: 1,
              borderColor: "divider",
              padding: "0px 12px 0px",
              position: "relative",
            }}
          >
            <Box
              sx={{
                position: "absolute",
                right: "1rem",
                top: "1rem",
                zIndex: "10",
              }}
            >
              <IconButton
                onClick={() => {
                  setParams({});
                }}
              >
                <Close style={{ fontSize: 16, cursor: "pointer" }} />
              </IconButton>
            </Box>
            <TabList onChange={handleChange} aria-label="basic tabs example">
              <Tab
                label={
                  <Stack
                    direction="row"
                    sx={{
                      alignItems: "center",
                    }}
                  >
                    <InfoIcon sx={{ fontSize: "15px" }} />
                    <Typography
                      variant="body1"
                      ml={1}
                      sx={{ fontWeight: 600, fontSize: "14px" }}
                    >
                      Basic Info
                    </Typography>
                  </Stack>
                }
                value="Basic Info"
                sx={{ textTransform: "none", fontWeight: "bold" }}
              />

              {/* <Tab
                label={
                  <Stack
                    direction="row"
                    sx={{
                      alignItems: "center",
                    }}
                  >
                    <PeopleIcon sx={{ fontSize: "15px" }} />
                    <Typography
                      variant="body1"
                      ml={1}
                      sx={{ fontWeight: 600, fontSize: "14px" }}
                    >
                      Assigned Roles
                    </Typography>
                  </Stack>
                }
                value="Assigned Roles"
                sx={{ textTransform: "none", fontWeight: "bold" }}
              /> */}

              <Tab
                label={
                  <Stack
                    direction="row"
                    sx={{
                      alignItems: "center",
                    }}
                  >
                    <SettingsIcon sx={{ fontSize: "15px" }} />
                    <Typography
                      variant="body1"
                      ml={1}
                      sx={{ fontWeight: 600, fontSize: "14px" }}
                    >
                      Features
                    </Typography>
                  </Stack>
                }
                value="Features"
                sx={{ textTransform: "none", fontWeight: "bold" }}
              />
            </TabList>
            <TabPanel value={"Assigned Roles"} sx={{ padding: "0px" }}>
              <Stack sx={{ paddingTop: ".5rem" }}>
                <UserAssignedRoles
                  userDetail={userDetail}
                  setUserDetail={setUserDetail}
                  load={load}
                  setLoad={setLoad}
                  params={params}
                />
              </Stack>
            </TabPanel>
            <TabPanel value={"Basic Info"} sx={{ padding: "0px" }}>
              <Stack sx={{ paddingTop: ".5rem" }}>
                <UserInfo
                  userDetail={userDetail}
                  setUserDetail={setUserDetail}
                  params={params}
                />
              </Stack>
            </TabPanel>
            <TabPanel value={"Features"} sx={{ padding: "0px" }}>
              <Stack sx={{ paddingTop: ".5rem" }}>
                <FeaturesContent
                  userDetail={userDetail}
                  setUserDetail={setUserDetail}
                  load={load}
                  setLoad={setLoad}
                  params={params}
                />
              </Stack>
            </TabPanel>
          </Box>
        </TabContext>

        <div className={classes.userDetailFooter}>
          <Button
            size="small"
            variant={
              load ||
              basicReducerState?.users
                ?.filter((u) => u?.emailId !== userDetail?.emailId)
                ?.find((u) => u?.displayName === userDetail?.displayName) ||
              userDetail?.displayName?.length === 0
                ? "outlined"
                : "contained"
            }
            onClick={updateUserInfo}
            disabled={
              load ||
              basicReducerState?.users
                ?.filter((u) => u?.emailId !== userDetail?.emailId)
                ?.find((u) => u?.displayName === userDetail?.displayName) ||
              userDetail?.displayName?.length === 0
            }
          >
            Submit
          </Button>
        </div>
      </>
    </Paper>
  );
}

export default UserDetail;
