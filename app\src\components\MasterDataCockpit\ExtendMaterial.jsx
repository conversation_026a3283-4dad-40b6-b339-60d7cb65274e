import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  TextField,
  Grid,
  Autocomplete,
  IconButton,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SaveIcon from "@mui/icons-material/Save";
import { DateRangePicker } from "rsuite";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { setPayload } from "../../app/editPayloadSlice";

function transformApiData(apiValue, dropDownData) {
  if (Array.isArray(dropDownData)) {
    const matchingOption = dropDownData.find((option) => option.code === apiValue);
    return matchingOption || "";
  } else {
    return "";
  }
}
const ExtendMaterial = ({
  label,
  value,
  units,
  onSave,
  isEditMode,
  isExtendMode,
  options = [],
  type,
}) => {
  const [editedValue, setEditedValue] = useState(value);
  const [changeStatus, setChangeStatus] = useState(false);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  const dispatch = useDispatch();
  const transformedValue = transformApiData(editedValue, dropDownData);
  console.log("dropdownData", editedValue);
  console.log("value e", value);
  console.log("label", label);
  console.log("units", units);
  console.log("transformedValue", transformedValue);
  
const editField = useSelector((state) => state.edit.payload)

console.log('editField', editField);
  const fieldData = {
    label,
    value: editedValue,
    units,
    type,
  };
  console.log("fieldData", fieldData);
  const handleSave = () => {
    onSave(fieldData);
  };
  let key = label
    .replaceAll("(", "")
    .replaceAll(")", "")
    .replaceAll("/", "")
    .replaceAll("-", "")
    .replaceAll(".", "")
    .split(" ")
    .join("");
  useEffect(() => {
    setEditedValue(value);
  }, [value]);


  useEffect(()=>{
    console.log('lkey', key)
    console.log('data', value)
    dispatch(setPayload({keyname: key.replaceAll("(", "")
    .replaceAll(")", "")
    .replaceAll("/", "")
    .replaceAll("-", "")
    .replaceAll(".", "")
    .split(" ")
    .join(""), data:value ? value : ""}))
  },[])
  console.log("editedValue[key] ", dropDownData[key] );
  console.log("editedValue[key] ", editedValue );
  return (
    <Grid item>
      <Stack>
        {isExtendMode ? (
          <>
            <Typography variant="body2" color="#777">
              {label}
            </Typography>
            {type === "Drop Down" ? (
              <Autocomplete
                options={dropDownData[key] ?? []}
                value={(editedValue && dropDownData[key]?.filter((x) => x.code === editedValue)) || ""}
                onChange={(event, newValue) => {
                  console.log('newValue',newValue)
                  setEditedValue(newValue.code);
                  setChangeStatus(true);
                  console.log("keys", key);
                }}
                getOptionLabel={(option) =>
                  {
                    console.log('optionoptionoption',option)
                    return option === "" ?"": `${option&&option[0]?.code} - ${option&&option[0]?.desc}`}
                }
                // isOptionEqualToValue={(a,b)=>{ return a.code===b.code}}
                renderOption={(props, option) =>{
                  console.log('option vakue',option)
                  return (
                  <li {...props}>
                    <Typography style={{ fontSize: 12 }}>
                    {`${option?.code} - ${option?.desc}`}
                    </Typography>
                  </li>
                )}}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    size="small"
                    label={null}
                  />
                )}
              />
            ) : type === "Input" ? (
              <TextField
                variant="outlined"
                size="small"
                value={editedValue}
                onChange={(event, newValue) => {
                  setEditedValue(newValue);
                }}
              />
            ) : type === "Calendar" ? (
              <DateRangePicker
                size="small"
                placeholder="Select Date Range"
                // value={editedValue}
                // onClean={props.handleClear}
                //   ranges={predefinedRanges}

                //   onChange={props.handleDate}

                format="dd MMM yyyy"
                placement="auto"
                sx={{ height: "2.32rem !important" }}
              />
            ) : type === "Radio Button" ? (
              <Checkbox
              sx={{borderRadius:"0 !important"}}
                checked={editedValue}
                onChange={(event, newValue) => {
                  setEditedValue(newValue);
                }}
              />
            ) : (
              ""
            )}
          </>
        ) : (
          <>
            <>
              <Typography variant="body2" color="#777">
                {label}
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {editedValue} {units}
              </Typography>
            </>
          </>
        )}
      </Stack>
    </Grid>
  );
};

export default ExtendMaterial;
