import React, { useState, useEffect, forwardRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Button,
  Select,
  Tooltip,
  MenuItem,
  FormControl,
  Slide,
  tooltipClasses,
  InputLabel,
  Typography,
  IconButton,
  Box,
  Paper,
  Checkbox,
  ListItemText,
  TextField,
} from "@mui/material";
import {
  destination_CostCenter,
  destination_CostCenter_Mass,
  destination_GeneralLedger,
  destination_GeneralLedger_Mass
} from "../../destinationVariables";
import { Search as SearchIcon } from "@mui/icons-material";
import { DataGrid } from "@mui/x-data-grid";
import { useLocation, useNavigate } from "react-router-dom";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import { colors } from "@constant/colors";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import { END_POINTS } from "@constant/apiEndPoints";
import {
  API_CODE,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  LOADER_MESSAGES,
  LOCAL_STORAGE_KEYS,
  DROP_DOWN_SELECT_OR_MAP,
} from "@constant/enum";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { REQUEST_TYPE } from "@constant/enum";
import {
  transformCostCenterResponseChange,
  showToast,
  //transformGLResponsChange,
  createPayloadForChangeGL,
  transformGLResponseChange,
  changePayloadForGL,
} from "../../functions";
import { APP_END_POINTS } from "@constant/appEndPoints";

import useButtonDTConfig from "@hooks/useButtonDTConfig";
import { resetGLStateGL, updateReqBenchRowGL } from "@app/generalLedgerTabSlice";
import { setChangedFieldsMapGL, setFetchedGeneralLedgerDataGL, setFetchReqBenchDataGL, setOriginalGeneralLedgerDataGL, setOriginalReqBenchDataGL, updateGeneralLedgerRowGL } from "@app/generalLedgerTabSlice";
import { doAjax } from "../../components/Common/fetchService";
import DownloadDialog from "../../components/Common/DownloadDialog";
import FilterChangeDropdown from "../../components/Common/ui/dropdown/FilterChangeDropdown";
import BottomNavGlobal from "../../components/RequestBench/RequestPages/BottomNavGlobal";
import ReusableDataTable from "../../components/Common/ReusableTable";
import moment from "moment";
import { setDropDown } from "./slice/generalLedgerDropDownSlice";
import { setDependentDropdown } from "@app/dropDownDataSlice";
import { getLocalStorage } from "@helper/glhelper";
import useLogger from "@hooks/useLogger";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});

const RequestDetailsChangeGL = ({
  reqBench,
  requestId,
  apiResponses,
  downloadClicked,
  setDownloadClicked,
}) => {  
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    fetchedGeneralLedgerData,
    originalGLData,
    fetchReqBenchData,
    originalReqBenchData,
    changedFieldsMap,
  } = useSelector((state) => state.generalLedger);
  const requestHeaderData = useSelector(
    (state) => state.generalLedger.payload.requestHeaderData
  );

  const initialPayload = useSelector((state) => state.request.requestHeader);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons);
  const rmSearchForm = useSelector(
      (state) => state.commonFilter["GeneralLedger"]
    );

  const { customError } = useLogger()
  
  const task = useSelector((state) => state?.userManagement.taskData);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const RequestId = queryParams.get("RequestId");
  const isreqBench = queryParams.get("reqBench");
  const isrequestId = queryParams.get("RequestId");
  const selectedModule = getLocalStorage(LOCAL_STORAGE_KEYS.MODULE,true, {})
  
  const selectedModuleSelector = DROP_DOWN_SELECT_OR_MAP[selectedModule] || (() => ({}));
  
  const allDropDownData = useSelector(selectedModuleSelector)
  const [open, setOpen] = useState(true);
  const [dropdown1Value, setDropdown1Value] = useState("");
  const [dropdown2Value, setDropdown2Value] = useState("");
  const [selectedRow, setSelectedRow] = useState(null);
  const [dropdownDataCompany, setDropdownDataCompany] = useState([]);
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [selectedCompanyCodes, setSelectedCompanyCodes] = useState([]);
  const [selectedAccountType, setSelectedAccountType] = useState([]);
  const [costCenterOptions, setCostCenterOptions] = useState([]);
  const [selectedCostCenters, setSelectedCostCenters] = useState([]);
  const [successMsg, setSuccessMsg] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState("");
  const [loaderMessage, setLoaderMessage] = useState("");
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [dropdownDataCountry, setDropdownDataCountry] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState("");
  const [dropdownDataRegion, setDropdownDataRegion] = useState([]);
  const [dropdownDataSegment, setDropdownDataSegment] = useState([]);
  const [dropdownAamnum, setDropdwnAamnum] = useState([]);
  const [costcenterResponse, setCostcenterResponse] = useState([]);
  const [openSnackBar, setOpenSnackBar] = useState(false);
  const [alertMsg, setAlertMsg] = useState("");
  const [alertType, setAlertType] = useState("success");
  const [isLoading, setIsLoading] = useState(false);
  const [dropdownDataAccountType, setDropdownDataAccountType] = useState([]);

  const { getButtonsDisplayGlobal } = useButtonDTConfig();

  const dropdownFields = {
    taxcategory:allDropDownData["Taxcategory"],
    sortKey:allDropDownData["Sortkey"],
    //filedStatusGrp:allDropDownData["Taxcategory"],
    housebank:allDropDownData["Taxcategory"],
    accountId:allDropDownData["Taxcategory"],
    reconAc:allDropDownData["reconAcc"]
  };
  const [showGrid, setShowGrid] = useState(false);


  useEffect(()=>{
    if((fetchedGeneralLedgerData?.length > 0) && ( reqBench !== "true")){
      setShowGrid(true);
    }
  },[])

  useEffect(()=>{
    getChartOfAccount()
  },[])

  useEffect(() => {
    if (task?.ATTRIBUTE_1 || RequestId) {
      getButtonsDisplayGlobal("ET PC", "MDG_DYN_BTN_DT", "v2");
    }
  }, [task]);


    const getCompanyCode = (coa) => {
      const hSuccess = (data) => {
        setDropdownDataCompany(data.body)
        dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getCompanyCode?chartAccount=${coa}`,
        "get",
        hSuccess,
        hError
      );
    };
  
    const getAccountType = () => {
  
      const hSuccess = (data) => {
        setDropdownDataAccountType(data.body)
        dispatch(setDropDown({ keyName: "AccountType", data: data.body }));
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getGLAccountType`,
        "get",
        hSuccess,
        hError
      );
    };
  
    const getAccountCurrency = (compCode) => {
      const hSuccess = (data) => {
        dispatch(
          setDependentDropdown({
            keyName: "AccountCurrency",
            data: data.body || [],
            keyName2: id,
          })
        )
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getAccountCurrency?companyCode=${compCode}`,
        "get",
        hSuccess,
        hError
      );
    };
  
     const getFiledStatusGroup = (compCode) => {
      const hSuccess = (data) => {
        dispatch(
          setDependentDropdown({
            keyName: "filedStatusGrp",
            data: data.body,
            keyName2: compCode,
          })
        )
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getFieldStatusGroup?fieldStatusVariant=${compCode}`,
        "get",
        hSuccess,
        hError
      );
    };
  
    const getTaxCategory = (compCode="1020") => {
  
      const hSuccess = (data) => {
        dispatch(setDropDown({ keyName: "Taxcategory", data: data.body }));

      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getTaxCategory?companyCode=${compCode}`,
        "get",
        hSuccess,
        hError
      );
    };
  
    const getHouseBank = (compCode) => {
      const hSuccess = (data) => {
        dispatch(
          setDependentDropdown({
            keyName: "HouseBank",
            data: data.body || [],
            keyName2: selectedRowId || selectedRow?.id,
          })
        )
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getHouseBank?companyCode=${compCode}`,
        "get",
        hSuccess,
        hError
      );
    };
  
    const getAccontId = (compCode) => {
      const hSuccess = (data) => {

        dispatch(
          setDependentDropdown({
            keyName: "AccountId",
            data: data.body || [],
            keyName2: selectedRowId || selectedRow?.id,
          })
        )
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getAccountId?companyCode=${compCode}`,
        "get",
        hSuccess,
        hError
      );
    };
  
  
    const getCostElementCategory = (accType) => {
      const hSuccess = (data) => {
        dispatch(
          setDependentDropdown({
            keyName: "CostEleCategory",
            data: data.body || [],
            keyName2: selectedRowId || selectedRow?.id,
          })
        )
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getCostElementCategory?accountType=${accType}`,
        "get",
        hSuccess,
        hError
      );
    };
  
    const getreconAccountType = () => {
      const hSuccess = (data) => {
        dispatch(setDropDown({ keyName: "reconAcc", data: data.body }));

      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getReconAccountForAccountType`,
        "get",
        hSuccess,
        hError
      );
    };

    const getSortKey = () => {
        const hSuccess = (data) => {
          dispatch(setDropDown({ keyName: "Sortkey", data: data.body }));
        };

        
    
        const hError = (error) => {
          customError(error);
        };
    
        doAjax(
          `/${destination_GeneralLedger_Mass}/data/getSortKey`,
          "get",
          hSuccess,
          hError
        );
      };
  
    const getPlanningLevel = (compCode) => {
      const hSuccess = (data) => {
  
        dispatch(
          setDependentDropdown({
            keyName: "Planninglevel",
            data: data.body || [],
            keyName2: selectedRowId || selectedRow?.id,
          })
        )
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getPlanningLevel`,
        "get",
        hSuccess,
        hError
      );
    };
  
    const getAccountGroup = (coa) => {
      const hSuccess = (data) => {
  
        let accGrparr = []
        data?.body?.map((item) => {
          let hash = {}
          hash["code"] = item?.AccountGroup
          hash["desc"] = item?.Description
          hash["FromAcct"] = item?.FromAcct
          hash["ToAcct"] = item?.ToAcct
          accGrparr?.push(hash)
        })
        setDropdownDataAccountType(accGrparr)
        dispatch(
          setDependentDropdown({
            keyName: "AccountGroup",
            data: accGrparr || [],
            keyName2: selectedRowId || selectedRow?.id,
          })
        )
  
        dispatch(setDropDown({ keyName: "accountGroup", data: accGrparr }));
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getAccountGroup?chartAccount=${coa}`,
        "get",
        hSuccess,
        hError
      );
    };
  
  
    const getChartOfAccount = () => {
      const hSuccess = (data) => {
        //setDropdownDataCOA(data.body);
        dispatch(setDropDown({ keyName: "COA", data: data.body }));
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getChartOfAccounts`,
        "get",
        hSuccess,
        hError
      );
    };
  
  const handleClose = () => {
    setOpen(false);
    setDownloadClicked(false);
    navigate("/requestbench");
  };

  const handleOk = () => {
    setOpen(false);
    setShowGrid(true);
    fetchCostCenterDetails(); 
  };
  const allColumns = [
    {
      field: "included",
      headerName: "Included",
      align: "center",
      headerAlign: "center",
      renderCell: (params) => (
        <Checkbox checked={params.row.included} disabled={false} />
      ),
    },
    {
      field: "lineNumber",
      headerName: "Sl No",
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        const rowIndex = (
          reqBench ? fetchReqBenchData : fetchedGeneralLedgerData
        ).findIndex((row) => row.id === params.row.id);

        return <div>{rowIndex + 1}</div>;
      },
    },
    {
      field: "generalLedger",
      headerName: "General Ledger",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
    {
      field: "compCode",
      headerName: "Company Codes",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
     {
      field: "accountType",
      headerName: "Account Type",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
     {
      field: "accGroup",
      headerName: "Account Group",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
    {
      field: "chartOfAccount",
      headerName: "Chart Of Account",
      width: 150,
      editable: false,
    },
    {
      field: "glname",
      headerName: "Short Text",
      width: 200,
      editable: true,
    },
    {
      field: "description",
      headerName: "Long Text",
      width: 200,
      editable: true,
    },
    {
      field: "onlyBalanceInLocalCurrency",
      headerName: "Only Balance In Local Currency",
      width: 250,
      editable: true,
    },
    {
      field: "taxcategory",
      headerName: "Tax Category",
      width: 250,
      editable: true,
    },
    {
      field: "pwotax",
      headerName: "Posting Without Tax Allowed",
      width: 250,
      editable: true,
    },
    { field: "reconAc", headerName: "Recon. Account For Account Type", width: 150 },
    {
      field: "openItemManage",
      headerName: "Open Item Management",
      width: 150,
    },
    { field: "openItemManageLedgerGrp", headerName: "Open Item Management By Ledger Group", width: 150 },
    { field: "sortKey", headerName: "Sort Key", width: 150, editable: true },
    {
      field: "filedStatusGrp",
      headerName: "Field Status Group",
      width: 150,
      editable: true,
    },
    { field: "postAutoOnly", headerName: "Post Automatically Only", width: 150, editable: true },
    { field: "housebank", headerName: "House Bank", width: 150, editable: true },
    { field: "accountId", headerName: "Account Id", width: 150, editable: true },
    { field: "blockedforPostingCoCode", headerName: "Blocked For Posting Company Code", width: 150, editable: true },
    { field: "blockedforPostingCoa", headerName: "Blocked For Posting at COA", width: 150, editable: true },
    { field: "name2", headerName: "Name 2", width: 150, editable: true },
    { field: "name3", headerName: "Name 3", width: 150, editable: true },
    { field: "name4", headerName: "Name 4", width: 150, editable: true },
  ];

  const fieldNameList = requestHeaderData?.FieldName || [];
  const fixedColumns = allColumns.slice(0, 6);

    const dynamicColumns = allColumns
      .slice(4)
      .filter((col) => fieldNameList?.includes(col.headerName))
      .map((col) => {
        if (dropdownFields[col?.field]) {
          return {
            ...col,
            editable: false,
            renderCell: (params) => {
              const value = params.value || "";
              return (
                <Select
                  value={value}
                  onChange={(e) => {
                    const newValue = e.target.value;
                    const updatedRow = {
                      ...params.row,
                      [params.field]: newValue,
                    };
                    if (reqBench) {
                      dispatch(updateReqBenchRowGL(updatedRow));
                    } else {
                      dispatch(updateGeneralLedgerRowGL(updatedRow));
                    }
                  }}
                  size="small"
                  fullWidth
                  sx={{ minHeight: "36px" }}
                >
                  {dropdownFields[col?.field]?.map((option, idx) => (
                    <MenuItem key={idx} value={option?.code }>
                      { option?.code}
                    </MenuItem>
                  ))}
                </Select>
              );
            },
          };
        }
        if ([
            "houseBank",
            "filedStatusGrp"           
          ].includes(col.field)) {
          
          return {
            ...col,
            editable: false,
            renderCell: (params) => {
              const value = params.value || "";
              const row = params.row || "";
              return (
                <Select
                  value={value}
                  onChange={(e) => {
                    const newValue = e.target.value;
                    const updatedRow = {
                      ...params.row,
                      [params.field]: newValue,
                    };
                     if (reqBench) {
                      dispatch(updateReqBenchRowGL(updatedRow));
                    } else {
                      dispatch(updateGeneralLedgerRowGL(updatedRow));
                    }
                  }}
                  size="small"
                  fullWidth
                  sx={{ minHeight: "36px" }}
                >
                  {allDropDownData[params?.field][params?.row?.compCode]?.map((option, idx) => (
                    <MenuItem key={idx} value={option?.code }>
                      { option?.code}
                    </MenuItem>
                  ))}
                </Select>
              );
            },
          };
        }
        if (
          [
            "description",
            "glname",
            "generalLedger",
          ].includes(col.field)
        ) {
          return {
            ...col,
            editable: true,
            renderCell: (params) => (
              <TextField
                value={params.value || ""}
                onChange={(e) =>
                  params.api.setEditCellValue({
                    id: params.id,
                    field: params.field,
                    value: e.target.value.toUpperCase(),
                  })
                }
                variant="outlined"
                size="small"
                fullWidth
              />
            ),
            renderEditCell: (params) => (
              <TextField
                value={params.value || ""}
                onChange={(e) =>
                  params.api.setEditCellValue({
                    id: params.id,
                    field: params.field,
                    value: e.target.value.toUpperCase(),
                  })
                }
                variant="outlined"
                size="small"
                fullWidth
                placeholder={
                  col.field === "longDescription"
                    ? "Enter Long Description"
                    : "Enter Short Description"
                }
                sx={{
                  "& .MuiInputBase-root.Mui-disabled": {
                    "& > input": {
                      WebkitTextFillColor: "#000",
                      color: "#000",
                    },
                  },
                }}
              />
            ),
          };
        }
        if (
          [
            "openItemManage",
            "pwotax",
            "openItemManageLedgerGrp",
            "blockedforPostingCoa",
            "blockedforPostingCoCode",
            "postAutoOnly",
            "onlyBalanceInLocalCurrency"
          ].includes(col.field)
        ) {
          return {
            ...col,
            editable: true,
            renderCell: (params) => (
              

              <Checkbox
                sx={{
                  padding: 0,
                  marginTop:"5px",
                  "&.Mui-disabled": {
                  color: colors.hover.light,
                  },
                  "&.Mui-disabled.Mui-checked": {
                  color: colors.hover.light,
                  },
                }}
                checked={params.value}
                onChange={(e) =>
                  params.api.setEditCellValue({
                    id: params.id,
                    field: params.field,
                    value: e.target.value.toUpperCase(),
                  })
                }
              />
            ),
            renderEditCell: (params) => (
              <Checkbox
                sx={{
                  padding: 0,
                  marginTop:"5px",
                  "&.Mui-disabled": {
                  color: colors.hover.light,
                  },
                  "&.Mui-disabled.Mui-checked": {
                  color: colors.hover.light,
                  },
                }}
                checked={params.value}
                onChange={(e) =>
                  params.api.setEditCellValue({
                    id: params.id,
                    field: params.field,
                    value: ! params.value,
                  })
                }
              />
              
            ),
          };
        }
        return {
          ...col,
          editable: true,
        };
      });

      const columns = [...fixedColumns, ...dynamicColumns];
  const processRowUpdate = (newRow) => {
     dispatch(updateGeneralLedgerRowGL(newRow));
    return newRow;
  };
  const processRowUpdateReqBench = (newRow) => {
    dispatch(updateReqBenchRowGL(newRow));
    return newRow;
  };

  const handleRowClick = (params) => {
    setSelectedRow(params.row);
  };

  useEffect(() => {
      getTaxCategory()
      getreconAccountType()
      getSortKey()
      getCompanyCode(dropdown1Value)
    
  }, [dropdown1Value]);

  const getCountryData = () => {
    const hSuccess = (data) => {
      setDropdownDataCountry(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Country", data: data.body },
      });
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_CostCenter}/data/getCountryOrReg`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    getCountryData();
  }, []);


  const fetchGLAccount =() =>{
    const hSuccess = (data) => {

      let arr=[]
      data?.body?.map((item)=>{
        let hash={}
          hash["code"] =item?.code
        arr?.push(hash)
      })
      setCostCenterOptions((prev) => [
        ...new Set([...prev, ...arr]),
      ]);
     
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGLAccountByCOA?chartAccount=ETCN`,
      "get",
      hSuccess,
      hError
    );
  };

  const getGlAcountDropdownDataFromSearchFilter = (valueAcctype) => {
      let payload = {
        glAccount: "",
        chartOfAccount: dropdown1Value,
        postAutoOnly: "",
        companyCode: selectedCompanyCodes?.join(","),
        taxCategory: "",
        glAcctLongText: "",
        postingWithoutTaxAllowed: "",
        blockedForPostingInCOA: "",
        shortText: "",
        blockedForPostingInCompany: "",
        accountGroup:  '',
        glAccountType: valueAcctype?.[0]?.code,
        fieldStatusGroup: "",
        openItemMgmtbyLedgerGroup: "",
        openItemManagement: "",
        reconAccountforAcctType: "",
        fromDate:
          moment(rmSearchForm?.createdOn[0]).format("YYYY-MM-DDT00:00:00") ?? "",
        toDate:
          moment(rmSearchForm?.createdOn[1]).format("YYYY-MM-DDT00:00:00") ?? "",
        createdBy: "",
        top: 100,
        skip: 0,
      };
      const hSuccess = (data) => {
        if (data.statusCode === 200) {
          let glAccountArr = [];
  
          data?.body?.list?.forEach((item) => {
              let glAccountHash = {};
              glAccountHash["code"] = item?.GLAccount;
              glAccountHash["desc"] = item?.GLname;
              glAccountHash["coa"] = item?.COA;
              glAccountHash["accType"] = item?.Accounttype;
              glAccountHash["accGroup"] = item?.AccountGroup;
              glAccountArr.push(glAccountHash);
          });

    
      
      setCostCenterOptions((prev) => [
        ...new Set([...prev, ...glAccountArr]),
      ]);
    }
      };
      const hError = (error) => {
        customError(error);
      };
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getGeneralLedgersBasedOnAdditionalParams`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }

  const transformGlData = (data) => {
        return data?.map((item) => ({
      
        id: item?.GLAccount,
        generalLedgerHeaderID: item?.costCenterHeaderId,
        genralLedgerID: item?.GeneralLedgerErrorID,
        generalLedger:item?.GLAccount,
        compCode:item?.CompanyCode,
        accGroup:item?.typeNDescriptionViewDto?.AccountGroup,
        accountType:item?.typeNDescriptionViewDto?.Accounttype,
        chartOfAccount:item?.COA,
        glname:item?.typeNDescriptionViewDto?.GLname,
        description:item?.typeNDescriptionViewDto?.Description,
        onlyBalanceInLocalCurrency:item?.controlDataViewDto?.Balanceinlocrcy,
        taxcategory:item?.controlDataViewDto?.Taxcategory,
        pwotax:item?.controlDataViewDto?.Pstnwotax,
        reconAc:item?.controlDataViewDto?.ReconAcc,
        openItemManage:item?.controlDataViewDto?.Openitmmanage,
        openItemManageLedgerGrp:item?.controlDataViewDto?.OpenItemManagebyLedgerGrp,
        sortKey:item?.controlDataViewDto?.Sortkey,
        filedStatusGrp:item?.createBankInterestViewDto?.FieldStsGrp,
        postAutoOnly:item?.createBankInterestViewDto?.PostAuto,
        housebank:item?.createBankInterestViewDto?.HouseBank,
        accountId:item?.createBankInterestViewDto?.AccountId,
        blockedforPostingCoCode:item?.createBankInterestViewDto?.PostingBlockedCoCd,
    }));
  };

  const fetchCostCenterDetails = () => {
    if (!selectedCostCenters.length || !dropdown1Value) return;

    const glAccCOACoCode = [];

      selectedCostCenters.forEach(gl => {
        selectedCompanyCodes.forEach(cc => {
          glAccCOACoCode.push({
            glAccount: gl,
            chartOfAccount: dropdown1Value,
            companyCode: cc
          });
        });
      });
     const payload = {
      glAccCOACoCode: glAccCOACoCode
    };

    const successHandler = (data) => {
      const rawData = data?.body || [];
      setCostcenterResponse(rawData);
      const transformed = transformGlData(rawData);
      rawData?.map((item)=>{
        getFiledStatusGroup(item?.CompanyCode,item?.GeneralLedgerID)
      })
      
      dispatch(setFetchedGeneralLedgerDataGL(transformed));
      dispatch(setOriginalGeneralLedgerDataGL(transformed));
    };

    const errorHandler = (err) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGeneralLedgersData`,
      "post",
      successHandler,
      errorHandler,
      payload
    );
  };

  useEffect(() => {
    if (
      reqBench === "true" &&
      Array.isArray(apiResponses) &&
      apiResponses.length > 0 &&
      fetchReqBenchData.length === 0
    ) {
      const transformedData = transformGLResponseChange(apiResponses);
      dispatch(setFetchReqBenchDataGL(transformedData));
      dispatch(setOriginalReqBenchDataGL(transformedData));
    }
  }, [apiResponses, reqBench]);

  useEffect(() => {
    if (downloadClicked) {
      setOpen(true);
    }
  }, [downloadClicked]);

  const parsedData = (apiResponses ?? []).map((item) => {
    let changedFields = {};
    if (typeof item.changedFields === "object" && item.changedFields !== null) {
      changedFields = item.changedFields;
    } else if (typeof item.ChangedFields === "string") {
      try {
        changedFields = JSON.parse(item.ChangedFields);
      } catch {
        changedFields = {};
      }
    }

    const { changedFields: _, ChangedFields, ...rest } = item;

    return {
      ...rest,
      changedFields,
    };
  });

  useEffect(() => {
    if (!parsedData || parsedData.length === 0) return;

    const newChangedFieldsMap = {};
    parsedData.forEach((row) => {
      newChangedFieldsMap[row.CostCenterID] = row.changedFields || {};
    });

    dispatch(setChangedFieldsMapGL(newChangedFieldsMap));
  }, [apiResponses]);
  const handleSaveAsDraft = () => {
    
    const Payload = changePayloadForGL(reqBench ? fetchReqBenchData : fetchedGeneralLedgerData,requestHeaderData,task,initialPayload);
    const hSuccess = (data) => {
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg(
        "General Ledger change submission for save as draft initiated"
      );
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/changeGeneralLedgersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      Payload
    );

  };
  const handleSendBack = () => {

    const Payload = changePayloadForGL(reqBench ? fetchReqBenchData : fetchedGeneralLedgerData,requestHeaderData,task,initialPayload);

    const hSuccess = (data) => {
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg(
        "Cost Centers change submission for save as draft initiated"
      );
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/changeCostCentersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };
  const handleRejectAndCancel = () => {
    const Payload = changePayloadForGL(reqBench ? fetchReqBenchData : fetchedGeneralLedgerData,requestHeaderData,task,initialPayload);;
    const hSuccess = (data) => {
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg(
        "Cost Centers change submission for save as draft initiated"
      );
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/changeCostCentersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };

  const handleSubmitForReview = () => {
    const Payload = changePayloadForGL(reqBench ? fetchReqBenchData : fetchedGeneralLedgerData,requestHeaderData,task,initialPayload);

    const hSuccess = (data) => {

      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("General Ledgers submit for review successful");
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving submit for review.");
      setOpenSnackBar(true);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/changeGeneralLedgersSubmitForReview`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };

  const handleSubmitForApprove = () => {
     const Payload = changePayloadForGL(reqBench ? fetchReqBenchData : fetchedGeneralLedgerData,requestHeaderData,task,initialPayload);;


    const hSuccess = (data) => {

      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("Cost Centers successfuly Approved");
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving submit for review.");
      setOpenSnackBar(true);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/changeCostCentersApprovalSubmit`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };

  const validateAllRows = () => {
    const Payload = changePayloadForGL(reqBench ? fetchReqBenchData : fetchedGeneralLedgerData,requestHeaderData,task,initialPayload);  
      const hSuccess = (data) => {
        setIsLoading(false);
        setAlertType("success");
        setAlertMsg(
          "General Ledger validate mass initiated"
        );
        setOpenSnackBar(true);
  
        setTimeout(() => {
          navigate("/requestbench");
        }, 2000);
      };
  
      const hError = (error) => {
        setIsLoading(false);
        setAlertType("error");
        setAlertMsg("Error occurred while saving the draft.");
        setOpenSnackBar(true);
      };
      doAjax(
        `/${destination_GeneralLedger_Mass}/massAction/validateMassGeneralLedger`,
        "POST",
        hSuccess,
        hError,
        Payload
      );
    };

  const handleValidateAndSyndicate = (type) => {
    const Payload = fetchReqBenchData.map((pcData) => {
      return {
        requestInProcess: "",
        ProfitCenterID: pcData.ProfitCenterID || "",
        TemplateName: requestHeaderData?.TemplateName || "",
        TemplateHeaders: "",
        IsScheduled: false,
        Action: "",
        RequestID: pcData.RequestID || "",
        TaskStatus: null,
        TaskId: task?.taskId,
        ReqCreatedBy: pcData?.createdBy || "",
        ReqCreatedOn: pcData?.historyTabDto?.ReqCreatedOn || "",
        RequestStatus: requestHeaderData?.requestStatus || "",
        CreationId: "",
        EditId: "",
        DeleteId: "",
        MassCreationId: "",
        MassEditId: requestId || "",
        MassDeleteId: "",
        ProfitCenterErrorID: pcData.ProfitCenterErrorID || "",
        RequestType: requestHeaderData?.requestType || "",
        MassRequestStatus: "",
        Remarks: null,
        TempLockRemarks: null,
        Info: "",
        ChangedFields: "",
        PrctrName: pcData?.profitCenterName || "",
        LongText: pcData?.description || "",
        InChargeUser: pcData?.userResponsible || "",
        InCharge: pcData?.personResponsible || "",
        Department: pcData?.Department || "",
        PrctrHierGrp: pcData?.PrctrHierGrp || "",
        Segment: pcData?.segment || "",
        LockInd: pcData?.indicatorsTabDto?.LockIndicator || false,
        Template: "",
        Title: pcData?.addressTabDto?.Title || "",
        Name1: pcData?.name1 || "",
        Name2: pcData?.name2 || "",
        Name3: pcData?.name3 || "",
        Name4: pcData?.name4 || "",
        Street: pcData?.street || "",
        City: pcData?.city || "",
        District: pcData?.district || "",
        Country: pcData?.country || "",
        Taxjurcode: pcData?.TaxJurisdiction || "",
        PoBox: pcData?.PoBox || "",
        PostalCode: pcData?.PostalCode || "",
        PobxPcd: pcData?.PobxPcd || "",
        Regio: pcData?.Regio || "",
        Language: pcData?.communicationTabDto?.Language || "",
        Telephone: pcData?.communicationTabDto?.Telephone || "",
        Telephone2: pcData?.communicationTabDto?.Telephone2 || "",
        Telebox: pcData?.communicationTabDto?.Telebox || "",
        Telex: pcData?.communicationTabDto?.Telex || "",
        FaxNumber: pcData?.communicationTabDto?.FaxNumber || "",
        Teletex: pcData?.communicationTabDto?.Teletex || "",
        Printer: pcData?.communicationTabDto?.Printer || "",
        DataLine: pcData?.communicationTabDto?.DataLine || "",
        ProfitCenter: pcData?.profitCenter || "",
        COArea: pcData?.controllingArea || "ETCA",
        ValidfromDate:
          pcData?.basicDataTabDto?.ValidfromDate || "/Date(-2208988800000)/",
        ValidtoDate:
          pcData?.basicDataTabDto?.ValidtoDate || "/Date(253402214400000)/",
        Testrun: null,
        IsFirstSynCompleted: false,
        TempLockIsSelectedForSyn: false,
        SelectedByRequestorToDisplay: false,
        IsSunoco: false,
        Countryiso: "",
        LanguIso: "",
        Logsystem: "",
        GeneralInfoID: null,
        RequestPriority: requestHeaderData?.requestPriority || "",
        BusinessJustification: null,
        SAPorJEErrorCheck: null,
        BusinessSegment: "CRUDE",
        HierarchyRegion: null,
        PCAAMNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
        ValidationDoneBy: "MDM Approval",
        TotalIntermediateTasks: pcData?.totalIntermediateTasks ?? "",

        ToCompanycode: [
          {
            CompCodeID: pcData.CompCodeID,
            CompanyCode: pcData.companyCode || "",
            CompanyName: "",
            AssignToPrctr: "",
            Venture: "",
            RecInd: "",
            EquityTyp: "",
            JvOtype: "",
            JvJibcl: "",
            JvJibsa: "",
          },
        ],

       Torequestheaderdata: {
          RequestId: ccData?.toRequestHeaderDataRequestId ?? initialPayload?.requestId ?? "",
          ReqCreatedBy: ccData?.toRequestHeaderDataReqCreatedBy ?? initialPayload?.reqCreatedBy ?? "",
          ReqCreatedOn: `/Date(${new Date(ccData?.toRequestHeaderDataReqCreatedOn??initialPayload?.reqCreatedOn).getTime()})/`,
          ReqUpdatedOn: `/Date(${new Date(ccData?.toRequestHeaderDataReqUpdatedOn??initialPayload?.reqUpdatedOn).getTime()})/`,
          RequestType: ccData?.toRequestHeaderDataRequestType ?? initialPayload?.requestType ?? "",
          RequestPrefix: ccData?.toRequestHeaderDataRequestPrefix ?? initialPayload?.requestPrefix ?? "",
          RequestPriority: ccData?.toRequestHeaderDataRequestPriority ?? initialPayload?.requestPriority ?? "",
          RequestDesc: ccData?.toRequestHeaderDataRequestDesc ?? initialPayload?.requestDesc ?? "",
          RequestStatus: ccData?.toRequestHeaderDataRequestStatus ?? initialPayload?.requestStatus ?? "",
          FirstProd: ccData?.toRequestHeaderDataFirstProd ?? initialPayload?.firstProd ?? "",
          LaunchDate: ccData?.toRequestHeaderDataLaunchDate ?? initialPayload?.launchDate ?? "",
          LeadingCat: ccData?.toRequestHeaderDataLeadingCat ?? initialPayload?.leadingCat ?? "",
          Division: ccData?.toRequestHeaderDataDivision ?? initialPayload?.division ?? "",
          TemplateName: ccData?.toRequestHeaderDataTemplateName ?? initialPayload?.templateName ?? "",
          FieldName: ccData?.toRequestHeaderDataFieldName ?? initialPayload?.fieldName ?? "",
          Region: ccData?.toRequestHeaderDataRegion ?? initialPayload?.region ?? "",
          FilterDetails: ccData?.toRequestHeaderFilterDetails ?? "",
          IsBifurcated: ccData?.toRequestHeaderIsBifurcated ?? initialPayload?.isBifurcated ?? "",
      },

        TochangeLogData: {
          ChangeLogData: null,
          ChangeLogId: pcData.ChangeLogId || "",
          RequestHeaderId: "",
          RequestId: "",
        },
         ToGeneralLedgerErrorData: {
            GeneralLedgerErrorId: body?.ToGeneralLedgerErrorData?.GeneralLedgerErrorId ?? "",
            RequestId: "",
            RequestHeaderId: "",
            GLAccount: "",
            CompanyCode: "",
            CoCodeToExtend: "",
            SapResponse: "",
            DbDuplicateCheck: "",
            ChartOfAccount: "",
            ObjectSapError: "",
            ObjectDbError: "",
            ObjectExcelError: "",
            ObjectNoRangeError: "",
            ShortDescSapError: "",
            ShortDescDbError: "",
            ShortDescExcelError: "",
            LongDescSapError: "",
            LongDescDbError: "",
            LongDescExcelError: "",
            BroSeriesShortDescError: "",
            BroSeriesLongDescError: "",
            DmsAttachmentErrorStatus: "",
            SapAttachmentErrorStatus: ""
        },

         ToGeneralInfoData: {
          GeneralInfoId: body?.GeneralInfoId ?? "",
            RequestPriority: requestHeaderData?.RequestPriority || "",
            BusinessJustification: "",
            SAPorJEErrorCheck: "",
            BusinessSegment: "NA",
            Region: "",
            SAPorJEErrorCheck: '',
            TaxableCheck: "",
            SpecificPostingCheck: "",
            ManualEntriesCheck: "",
            TradingPartnerCheck: "",
            GLSubAccountType: "",
            ProductionDateEstm: "",
            Message: ""
        }
      };
    });
    const hSuccess = (data) => {
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("Cost Centers submit for review successful");
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving submit for review.");
      setOpenSnackBar(true);
    };

    doAjax(
      type === "validate"
        ? `/${destination_GeneralLedger_Mass}/massAction/validateMassGeneralLedger`
        : `/${destination_GeneralLedger_Mass}/massAction/changeGeneralLedgersApproved`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const handleDownloadDialogOpen = () => {
    setOpenDownloadDialog(true);
  };

  const handleDownloadDialogClose = () => {
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
  };

  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };

  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };

  const handleDownload = () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    setOpen(false);
    setDownloadClicked(false);
    if (!RequestId) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
    const glAccCOACoCode = [];

      selectedCostCenters.forEach(gl => {
        selectedCompanyCodes.forEach(cc => {
          glAccCOACoCode.push({
            glAccount: gl,
            coa: dropdown1Value,
            compCode: cc,
            accountType: "X"
          });
        });
      });
    let payload ={
      "dtName": "MDG_GL_CHANGE_TEMPLATE_DT",
      "version": "v3",
      "templateHeaders": "GL Account,Description",
      "templateName": "Chart Of Account",
      "requestId": requestHeaderData?.RequestId || initialPayload?.requestId || "",
      "GlAccount": glAccCOACoCode,
      "headers": ["GL Account", "Description"]
    }
    const hSuccess = (response) => {
      if (response?.size == 0) {
        setBlurLoading(false);
        setLoaderMessage("");
        showToast(ERROR_MESSAGES?.NO_DATA_FOUND, "error", {
          position: "top-center",
          largeWidth: true,
        });
        setTimeout(() => {
          navigate(APP_END_POINTS?.REQUEST_BENCH);
        }, 2600);
        return;
      }
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute(
        "download",
        `${requestHeaderData?.TemplateName}_Mass Change.xlsx`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");

      setSuccessMsg(true);
      setMessageDialogMessage(
        `${requestHeaderData?.TemplateName}_Mass Change.xlsx has been downloaded successfully.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      showToast(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL, "error", {
        position: "top-center",
      });
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/excel/downloadExcelWithData`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  const handleEmailDownload = () => {
    setBlurLoading(true);
    onClose();
    let templateKeys =
      Templates[initialPayload?.TemplateName]?.map((item) => item.key) || [];
    let payload = {};
    if (activeTab === 0) {
      payload = {
        materialDetails: [
          templateKeys.reduce((acc, key) => {
            acc[key] = convertedValues?.[key] ? convertedValues?.[key] : "";
            return acc;
          }, {}),
        ],
        templateHeaders: initialPayload?.FieldName
          ? initialPayload.FieldName?.join("$^$")
          : "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName: initialPayload?.TemplateName
          ? initialPayload.TemplateName
          : "",
        dtName: "MDG_MAT_CHANGE_TEMPLATE",
        version: "v4",
        rolePrefix: "",
      };
    } else {
      payload = {
        materialDetails: [
          templateKeys.reduce((acc, key) => {
            acc[key] =
              rowsOfMaterialData
                .map((row) => row[key]?.trim())
                .filter((value) => value !== "")
                .join(",") || "";
            return acc;
          }, {}),
        ],
        templateHeaders: initialPayload?.FieldName
          ? initialPayload.FieldName?.join("$^$")
          : "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName: initialPayload?.TemplateName
          ? initialPayload.TemplateName
          : "",
        dtName: "MDG_MAT_CHANGE_TEMPLATE",
        version: "v4",
        rolePrefix: "",
      };
    }
    const hSuccess = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      setSuccessMsg(true);
      setMessageDialogMessage(
        `Download has been started. You will get the Excel file via email.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
      setSuccessMsg(true);
      setMessageDialogMessage(
        "Oops! Something went wrong. Please try again later."
      );
      setAlertType("danger");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    doAjax(
      `/${destination_MaterialMgmt}/excel/downloadExcelWithDataInMail`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  const highlightedColumns = columns.map((col) => ({
    ...col,
    renderCell: (params) => {
      const isChanged =
        changedFieldsMap[params.row.CostCenterID]?.[col.field];
      return (
        <div
          style={{
            backgroundColor: isChanged ? "rgba(255, 229, 100, 0.6)" : "inherit",
            padding: "0 4px",
            borderRadius: 4,
            height: "100%",
            display: "flex",
            alignItems: "center",
          }}
        >
          {params.value}
        </div>
      );
    },
  }));

  const isChangeFieldEmpty = (changedFieldsMap) =>
    changedFieldsMap &&
    Object.values(changedFieldsMap).every(
      (fields) =>
        typeof fields === "object" &&
        fields !== null &&
        Object.keys(fields).length === 0
    );

  return (
    <div>
      {successMsg && (
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={messageDialogMessage}
          alertType={alertType}
          handleSnackBarClose={handleSnackBarClose}
        />
      )}
      {(requestHeaderData?.TemplateName || downloadClicked) && (
        <>
          {fetchedGeneralLedgerData?.length === 0 && !reqBench && (
            <>
              <Dialog
                open={open}
                TransitionComponent={Transition}
                keepMounted
                onClose={(event, reason) => {
                  if (
                    reason === "backdropClick" ||
                    reason === "escapeKeyDown"
                  ) {
                    return;
                  }
                  handleClose();
                }}
                maxWidth="sm"
                fullWidth
              >
                <Box
                  sx={{
                    backgroundColor: "#e3f2fd",
                    padding: "1rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <FeedOutlinedIcon
                    color="primary"
                    sx={{ marginRight: "0.5rem" }}
                  />
                  <Typography variant="h6" component="div" color="primary">
                    {requestHeaderData?.TemplateName} Search Filter(s)
                  </Typography>
                </Box>

                <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{
                        key: "chartOfAccount",
                        label: "Chart Of Account",
                      }}
                      dropDownData={{
                        chartOfAccount: allDropDownData?.["COA"] || [],
                      }}
                      selectedValues={{
                        chartOfAccount: dropdown1Value
                          ? [{ code: dropdown1Value }]
                          : [],
                      }}
                      handleSelectionChange={(key, value) => {
                        setDropdown1Value(
                          value.length > 0 ? value[0].code || value[0] : ""
                        );
                        getAccountType( value[0].code)
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      singleSelect={true}
                      errors={{}}
                    />
                  </Box>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "companyCode", label: "Company Code" }}
                      dropDownData={{ companyCode: dropdownDataCompany || [] }}
                      selectedValues={{
                        companyCode: selectedCompanyCodes.map(
                          (code) =>
                            dropdownDataCompany?.find(
                              (item) => item.code === code
                            ) || { code }
                        ),
                      }}
                      handleSelectAll={(key) => {
                        if (
                          selectedCompanyCodes.length ===
                          dropdownDataCompany?.length
                        ) {
                          setSelectedCompanyCodes([]);
                        } else {
                          setSelectedCompanyCodes(
                            dropdownDataCompany?.map((item) => item.code) || []
                          );
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        setSelectedCompanyCodes(
                          value.map((item) =>
                            typeof item === "string" ? item : item.code || item
                          )
                        );
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>

                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "accountType", label: "account type" }}
                      dropDownData={{ accountType: dropdownDataAccountType || [] }}
                      selectedValues={{
                        accountType: selectedAccountType.map(
                          (code) =>
                            dropdownDataAccountType?.find(
                              (item) => item.code === code
                            ) || { code }
                        ),
                      }}
                     
                      handleSelectionChange={(key, value) => {
                        setSelectedAccountType(
                          value.map((item) =>
                            typeof item === "string" ? item : item.code || item
                          )
                        );
                        getGlAcountDropdownDataFromSearchFilter(value)
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      singleSelect={true}
                      //isSelectAll={true}
                      errors={{}}
                    />
                  </Box>

                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "generalLedger", label: "general Ledger" }}
                      dropDownData={{
                        generalLedger: costCenterOptions
                      }}
                      selectedValues={{
                        generalLedger:
                          selectedCostCenters?.map((code) => ({ code })) || [],
                      }}
                      handleSelectAll={(key) => {
                        if (
                          selectedCostCenters?.length ===
                          costCenterOptions?.length
                        ) {
                          setSelectedCostCenters([]);
                        } else {
                          setSelectedCostCenters(costCenterOptions || []);
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        setSelectedCostCenters(
                          value?.map((item) =>
                            typeof item === "string" ? item : item?.code || item
                          )
                        );
                      }}
                      formatOptionLabel={(option) =>
                        typeof option === "string"
                          ? option
                          : option?.code || option
                      }
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>
                </DialogContent>

                <DialogActions
                  sx={{
                    padding: "0.5rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Button
                      onClick={handleClose}
                      color="error"
                      variant="outlined"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        textTransform: "none",
                        borderColor: "#cc3300",
                        fontWeight: 500,
                      }}
                    >
                      Cancel
                    </Button>
                    {requestHeaderData?.RequestType !==
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                        onClick={handleOk}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        OK
                      </Button>
                    )}
                    {requestHeaderData?.RequestType ===
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                        onClick={() => {
                          handleDownloadDialogOpen();
                        }}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        Download
                      </Button>
                    )}
                  </Box>
                </DialogActions>
              </Dialog>

              <DownloadDialog
                onDownloadTypeChange={onDownloadTypeChange}
                open={openDownloadDialog}
                downloadType={downloadType}
                handleDownloadTypeChange={handleDownloadTypeChange}
                onClose={handleDownloadDialogClose}
              />
              <ReusableBackDrop
                blurLoading={blurLoading}
                loaderMessage={loaderMessage}
              />
            </>
          )}
          {showGrid && (
            <Box sx={{ mt: 4, px: 4 }}>
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 2 }}>
                General ledger List
              </Typography>

              <Paper
                elevation={3}
                sx={{
                  borderRadius: 3,
                  overflow: "hidden",
                  border: "1px solid #e0e0e0",
                  backgroundColor: "#fafbff",
                }}
              >
                <Box sx={{ p: 2 }}>
                  <ReusableDataTable
                    rows={fetchedGeneralLedgerData}
                    columns={columns}
                    pageSize={10}
                    tempheight="50vh"
                    getRowIdValue="id"
                    editMode="row"
                    status_onRowSingleClick
                    callback_onRowSingleClick={handleRowClick}
                    processRowUpdate={processRowUpdate}
                    experimentalFeatures={{ newEditingApi: true }}
                    isCellEditable={(params) =>
                      !["costCenter", "companyCode"].includes(params.field)
                    }
                    getRowClassName={(params) =>
                      selectedRow?.id === params.row.id ? "Mui-selected" : ""
                    }
                  />
                </Box>
              </Paper>

              <Box
                sx={{ display: "flex", justifyContent: "right", mt: 3, gap: 2 }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleSaveAsDraft}
                >
                  Save as draft
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={validateAllRows}
                >
                  Validate
                </Button>
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={handleSubmitForReview}
                >
                  Submit
                </Button>
              </Box>
            </Box>
          )}
        </>
      )}
      <>
        {fetchReqBenchData.length === 0 && reqBench === "true" && (
          <>
            <Dialog
              open={open}
              TransitionComponent={Transition}
              keepMounted
              onClose={(event, reason) => {
                if (reason === "backdropClick" || reason === "escapeKeyDown") {
                  return;
                }
                handleClose();
              }}
              maxWidth="sm"
              fullWidth
            >
              <Box
                sx={{
                  backgroundColor: "#e3f2fd",
                  padding: "1rem 1.5rem",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <FeedOutlinedIcon
                  color="primary"
                  sx={{ marginRight: "0.5rem" }}
                />
                <Typography variant="h6" component="div" color="primary">
                  {requestHeaderData?.TemplateName} Search Filter(s)
                </Typography>
              </Box>

              <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{
                        key: "chartOfAccount",
                        label: "Chart Of Account",
                      }}
                      dropDownData={{
                        chartOfAccount: allDropDownData?.["COA"] || [],
                      }}
                      selectedValues={{
                        chartOfAccount: dropdown1Value
                          ? [{ code: dropdown1Value }]
                          : [],
                      }}
                      handleSelectionChange={(key, value) => {
                        setDropdown1Value(
                          value.length > 0 ? value[0].code || value[0] : ""
                        );
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      singleSelect={true}
                      errors={{}}
                    />
                  </Box>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "companyCode", label: "Company Code" }}
                      dropDownData={{ companyCode: dropdownDataCompany || [] }}
                      selectedValues={{
                        companyCode: selectedCompanyCodes.map(
                          (code) =>
                            dropdownDataCompany?.find(
                              (item) => item.code === code
                            ) || { code }
                        ),
                      }}
                      handleSelectAll={(key) => {
                        if (
                          selectedCompanyCodes.length ===
                          dropdownDataCompany?.length
                        ) {
                          setSelectedCompanyCodes([]);
                        } else {
                          setSelectedCompanyCodes(
                            dropdownDataCompany?.map((item) => item.code) || []
                          );
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        setSelectedCompanyCodes(
                          value.map((item) =>
                            typeof item === "string" ? item : item.code || item
                          )
                        );
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>

                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "generalLedger", label: "general Ledger" }}
                      dropDownData={{
                        generalLedger: costCenterOptions
                      }}
                      selectedValues={{
                        generalLedger:
                          selectedCostCenters?.map((code) => ({ code })) || [],
                      }}
                      handleSelectAll={(key) => {
                        if (
                          selectedCostCenters?.length ===
                          costCenterOptions?.length
                        ) {
                          setSelectedCostCenters([]);
                        } else {
                          setSelectedCostCenters(costCenterOptions || []);
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        setSelectedCostCenters(
                          value?.map((item) =>
                            typeof item === "string" ? item : item?.code || item
                          )
                        );
                      }}
                      formatOptionLabel={(option) =>
                        typeof option === "string"
                          ? option
                          : option?.code || option
                      }
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>
                </DialogContent>

              <DialogActions
                sx={{
                  padding: "0.5rem 1.5rem",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Box sx={{ display: "flex", gap: 1 }}>
                  <Button
                    onClick={handleClose}
                    color="error"
                    variant="outlined"
                    sx={{
                      height: 36,
                      minWidth: "3.5rem",
                      textTransform: "none",
                      borderColor: "#cc3300",
                      fontWeight: 500,
                    }}
                  >
                    Cancel
                  </Button>
                  {requestHeaderData?.RequestType !==
                    REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                    <Button
                      onClick={handleOk}
                      variant="contained"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        backgroundColor: "#3B30C8",
                        textTransform: "none",
                        fontWeight: 500,
                        "&:hover": {
                          backgroundColor: "#2c278f",
                        },
                      }}
                    >
                      OK
                    </Button>
                  )}
                  {requestHeaderData?.RequestType ===
                    REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                    <Button
                      onClick={() => {
                        handleDownloadDialogOpen();
                      }}
                      variant="contained"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        backgroundColor: "#3B30C8",
                        textTransform: "none",
                        fontWeight: 500,
                        "&:hover": {
                          backgroundColor: "#2c278f",
                        },
                      }}
                    >
                      Download
                    </Button>
                  )}
                </Box>
              </DialogActions>
            </Dialog>

            <DownloadDialog
              onDownloadTypeChange={onDownloadTypeChange}
              open={openDownloadDialog}
              downloadType={downloadType}
              handleDownloadTypeChange={handleDownloadTypeChange}
              onClose={handleDownloadDialogClose}
            />
            <ReusableBackDrop
              blurLoading={blurLoading}
              loaderMessage={loaderMessage}
            />
          </>
        )}

         {reqBench === "true" && (
          <Box sx={{ marginTop: "20px", padding: "16px" }}>
            <Typography variant="h5" gutterBottom>
              General Ledger Lists
            </Typography>
            <Paper
              elevation={4}
              sx={{ p: 0, borderRadius: 2, overflow: "hidden", mt: "50px" }}
            >
              <div>
                <ReusableDataTable
                  rows={fetchReqBenchData}
                  columns={columns}
                  pageSize={10}
                  tempheight={"50vh"}
                  getRowIdValue={"id"}
                  status_onRowSingleClick={true}
                  editMode="cell"
                  callback_onRowSingleClick={handleRowClick}
                  processRowUpdate={processRowUpdateReqBench}
                  experimentalFeatures={{ newEditingApi: true }}
                  isCellEditable={(params) =>
                    !["costCenter", "companyCode"].includes(params.field)
                  }
                  getRowClassName={(params) =>
                    selectedRow?.id === params.row.id ? "Mui-selected" : ""
                  }
                />
              </div>
            </Paper>
            <Box
              sx={{ display: "flex", justifyContent: "right", mt: 3, gap: 2 }}
            >
              <BottomNavGlobal
                handleSaveAsDraft={handleSaveAsDraft}
                handleSubmitForReview={handleSubmitForReview}
                handleSubmitForApprove={handleSubmitForApprove}
                handleSendBack={handleSendBack}
                handleRejectAndCancel={handleRejectAndCancel}
                handleValidateAndSyndicate={handleValidateAndSyndicate}
                validateAllRows={validateAllRows}
                filteredButtons={filteredButtons}
              />
            </Box>
          </Box>
        )}
        
      </>
    </div>
  );
};

export default RequestDetailsChangeGL;
