import { Box ,Paper} from '@mui/material';
import React from 'react'

const ConsumptionTooltip = ({ active, payload, label }) => {
    if (active && payload && payload?.length) {
        return (
          <Box component={Paper} p={1}>
            <p className="desc">{`Requested : ${payload[0]?.value}`}</p>
            <p className="desc">{`Issued : ${payload[1]?.value}`}</p>
            <p className="desc">{`Consumed : ${payload[2]?.value}`}</p>

          </Box>
        );
      }
}

export default ConsumptionTooltip