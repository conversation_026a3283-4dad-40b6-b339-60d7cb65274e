import React from 'react';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { DESCRIPTION_DATA } from "@constant/enum";
import { colors } from "@constant/colors";

const DescriptionData = ({ descriptionDataPayload }) => {
  if (!descriptionDataPayload || descriptionDataPayload.length === 0) {
    return null;
  }

  return (
    <Accordion sx={{ boxShadow: 3 }} expanded={true}>
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls="description-data-content"
        id="description-data-header"
        sx={{
          backgroundColor: colors.background.subtle,
          padding: "8px 16px",
          "&:hover": { backgroundColor: colors.background.subtle },
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: "bold", color: colors.text.primary }}>
          {DESCRIPTION_DATA.ACCORDION_TITLE}
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        <TableContainer component={Paper} sx={{ 
          borderRadius: "8px", 
          boxShadow: colors.shadow.light,
          border: `1px solid ${colors.border.light}`
        }}>
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: colors.background.subtle }}>
                <TableCell sx={{ fontWeight: 600, color: colors.text.primary }}>{DESCRIPTION_DATA.TABLE_HEADERS.ID}</TableCell>
                <TableCell sx={{ fontWeight: 600, color: colors.text.primary }}>{DESCRIPTION_DATA.TABLE_HEADERS.LANGUAGE}</TableCell>
                <TableCell sx={{ fontWeight: 600, color: colors.text.primary }}>{DESCRIPTION_DATA.TABLE_HEADERS.DESCRIPTION}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {descriptionDataPayload?.map((item) => (
                <TableRow key={item[DESCRIPTION_DATA.FIELDS.ID]}>
                  <TableCell sx={{ color: colors.text.secondary }}>{item[DESCRIPTION_DATA.FIELDS.ID]}</TableCell>
                  <TableCell sx={{ color: colors.text.secondary }}>{item[DESCRIPTION_DATA.FIELDS.LANGUAGE]}</TableCell>
                  <TableCell sx={{ color: colors.text.secondary }}>{item[DESCRIPTION_DATA.FIELDS.DESCRIPTION]}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </AccordionDetails>
    </Accordion>
  );
};

export default DescriptionData;
