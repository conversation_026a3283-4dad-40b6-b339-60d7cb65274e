import {
    Box,
    Grid,
    Typography,
  } from "@mui/material";
  import { useEffect, useState } from "react";
  import { container_Padding, } from "../common/commonStyles";
  import FilterField from "../Common/ReusableFilterBox/FilterField";
  
  const GenericTabsForChange = (props) => {
    let filterFields = props?.basicDataTabDetails &&  Object?.entries(props?.basicDataTabDetails);
    const [basicJsx, setbasicJsx] = useState([]);
   
    useEffect(() => {
      setbasicJsx(
        filterFields?.map((item) => {
          return (
            <Grid
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                ...container_Padding,
                // ...container_columnGap,
              }}
              key={item[0]}
            >
              <Grid container>
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    paddingBottom:"10px"
                  }}
                >
                  {/* {item[1][0].cardName} */}
                  {item[0]}
                </Typography>
              </Grid>
              <Box>
                <Grid container spacing={1} paddingBottom={1}>
                  {[...item[1]]
                    .filter((x) => x.visibility != "Hidden")
                    .sort((a, b) => a.sequenceNo - b.sequenceNo)
                    ?.map((innerItem) => {
                      return (
                        <FilterField
                            key={innerItem.fieldName}
                            disabled={props?.disabled}
                            field={innerItem}
                            dropDownData={props.dropDownData}
                            materialID={props?.materialID}
                            viewName={props?.activeViewTab}
                            plantData={props?.plantData}
                        />
                      );
                    })}
                </Grid>
              </Box>
            </Grid>
          );
        })
      );
    }, [props?.basicDataTabDetails, props.activeViewTab, props?.materialID]);
  
    return <>{basicJsx}</>;
  };
  
  export default GenericTabsForChange;
  