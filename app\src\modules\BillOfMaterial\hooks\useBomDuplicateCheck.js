import { useState } from "react";
import { doAjax } from "@components/Common/fetchService";
import { END_POINTS } from "@constant/apiEndPoints";
import { destination_BOM } from "../../../destinationVariables";
import { useSelector } from "react-redux";

const useBomDuplicateCheck = () => {
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const createdRequestId = useSelector((state) => state.bom.requestHeaderID);
  const payloadFields = useSelector((state) => state.bom.BOMpayloadData);

  const checkBomDuplicates = (row) => {
    setLoading(true);
    setError(null);
    setResult(null);
    const payload = [{
      material: typeof row.material === 'object' ? row.material.code || row.material : row.material,
      plant: typeof row.plant === 'object' ? row.plant.code || row.plant : row.plant,
      alternativeBom: typeof row.altBom === 'object' ? row.altBom.code || row.altBom : row.altBom,
      bomUsage: typeof row.bomUsage === 'object' ? row.bomUsage.code || row.bomUsage : row.bomUsage,
      requestNo: createdRequestId || payloadFields?.RequestId,
    }];
    return new Promise((resolve, reject) => {
      doAjax(
        `/${destination_BOM}${END_POINTS.MASS_ACTION.FETCH_BOM_NO_DUPLICATE_CHECK}`,
        "post",
        (data) => {
          setResult(data);
          setLoading(false);
          resolve(data);
        },
        (err) => {
          setError(err);
          setLoading(false);
          reject(err);
        },
        payload
      );
    });
  };

  return { checkBomDuplicates, result, error, loading };
};

export default useBomDuplicateCheck; 