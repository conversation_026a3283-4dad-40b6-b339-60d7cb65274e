import React from 'react';

class MyErrorBoundary extends React.Component {
  state = {
   hasError:false,
  };

  static getDerivedStateFromError(error) {
    return { hasError:true };
  }

  componentDidCatch(error, info) {
    console.log(error,info)
  }

  // A fake logging service.
  logErrorToServices = console.log;

  render() {
    if (this.state.hasError) {
      return this.props.fallback
    }
    return this.props.children;
  }
}

export default MyErrorBoundary;