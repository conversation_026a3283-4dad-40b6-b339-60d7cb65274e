import React from "react";
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert';

function ModAlert(alertprops) {
  return <Alert elevation={6} variant="filled" {...alertprops} />;
}
const MessageStrip = (props) => {
  return (<Snackbar
    // open={props.MessageStripData.show}
    open={props.open}
    TransitionComponent='Fade'
    autoHideDuration={1500}
    onClose={props.onClose}
    anchorOrigin={{ vertical:'bottom', horizontal:'left' }}
  >
    <ModAlert onClose={props.onClose} severity={props.severity}>
      {/* {props.MessageStripData.message} */}
      {props.message}
    </ModAlert>
  </Snackbar>
  )
}
export default MessageStrip;