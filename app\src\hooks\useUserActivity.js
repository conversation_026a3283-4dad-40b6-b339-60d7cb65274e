import { useEffect, useRef, useCallback, useState } from 'react';

const useUserActivity = (config = {}) => {
  const {
    timeoutDuration = 5 * 60 * 1000,
    warningTime = 14 * 1000,
    checkInterval = 1000,
    onWarning = () => {},
    onTimeout = () => {},
    enabled = true
  } = config;

  const [isActive, setIsActive] = useState(true);
  const [showWarning, setShowWarning] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(timeoutDuration);

  const lastActivityRef = useRef(Date.now());
  const intervalRef = useRef(null);
  const isWarningShownRef = useRef(false);

  // Activity events to track
  const activityEvents = [
    'click'
  ];

  // Handle user activity
  const handleActivity = useCallback(() => {
    lastActivityRef.current = Date.now();
    
    // Reset warning state if user becomes active
    if (isWarningShownRef.current) {
      isWarningShownRef.current = false;
      setShowWarning(false);
    }
    
    setIsActive(true);
    setTimeRemaining(timeoutDuration);
  }, [timeoutDuration]);

  // Throttle function to prevent excessive calls
  const throttle = useCallback((func, limit) => {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }, []);

  // Check activity status
  const checkActivity = useCallback(() => {
    const now = Date.now();
    const timeSinceLastActivity = now - lastActivityRef.current;
    const remaining = timeoutDuration - timeSinceLastActivity;
    
    setTimeRemaining(Math.max(0, remaining));
    
    // Check if warning should be shown
    if (!isWarningShownRef.current && timeSinceLastActivity >= (timeoutDuration - warningTime)) {
      isWarningShownRef.current = true;
      setShowWarning(true);
      onWarning();
    }
    
    // Check if user should be logged out
    if (timeSinceLastActivity >= timeoutDuration) {
      setIsActive(false);
      setShowWarning(false);
      onTimeout();
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }
  }, [timeoutDuration, warningTime, onWarning, onTimeout]);

  const extendSession = useCallback(() => {
    lastActivityRef.current = Date.now();
    isWarningShownRef.current = false;
    setShowWarning(false);
    setIsActive(true);
    setTimeRemaining(timeoutDuration);
  }, [timeoutDuration]);


  const startMonitoring = useCallback(() => {
    if (!enabled || window.location.hostname === "localhost") {
      return;
    }

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Create throttled activity handler
    const throttledActivityHandler = throttle(handleActivity, 1000);

    // Add event listeners
    activityEvents.forEach(event => {
      document.addEventListener(event, throttledActivityHandler, { passive: true });
    });

    // Handle page visibility changes
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        handleActivity();
      }
    };
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Start interval
    intervalRef.current = setInterval(checkActivity, checkInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      
      activityEvents.forEach(event => {
        document.removeEventListener(event, throttledActivityHandler);
      });
      
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [enabled, handleActivity, checkActivity, checkInterval, throttle]);

  // Stop monitoring
  const stopMonitoring = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setIsActive(true);
    setShowWarning(false);
    setTimeRemaining(timeoutDuration);
  }, [timeoutDuration]);

  // Reset activity
  const resetActivity = useCallback(() => {
    handleActivity();
  }, [handleActivity]);

  // Setup and cleanup
  useEffect(() => {
    if (!enabled) return;

    const cleanup = startMonitoring();
    return cleanup;
  }, [enabled, startMonitoring]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const formatTimeRemaining = useCallback(() => {
    const minutes = Math.floor(timeRemaining / 60000);
    const seconds = Math.floor((timeRemaining % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }, [timeRemaining]);

  return {
    isActive,
    showWarning,
    timeRemaining,
    formatTimeRemaining,
    extendSession,
    resetActivity,
    stopMonitoring,
    startMonitoring
  };
};

export default useUserActivity;