import { configureStore } from "@reduxjs/toolkit";
import reducers from "./reducers/index";
import {notificationApi} from "@api/notification/NotificationApiService"

const store = configureStore({
  reducer: reducers,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
        immutableCheck: false,
        serializableCheck: false,
    }).concat(notificationApi.middleware)
});

export default store;
