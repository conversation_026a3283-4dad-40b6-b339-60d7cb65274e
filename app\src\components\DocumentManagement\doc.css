::-webkit-scrollbar-track{
    box-shadow: inset 0px 0px 2px rgba(128, 128, 128, 0.555);
    border-radius: 100px;
   
       
   }
   ::-webkit-scrollbar-track:horizontal{
       box-shadow: inset 0px 0px 2px rgba(128, 128, 128, 0.555);
       border-radius: 100px;
      
          
      }
   ::-webkit-scrollbar{
       width: 5px;
       
   }
   ::-webkit-scrollbar:horizontal{
       /* width: 5px; */
       height: 5px;
   
       
   }
   ::-webkit-scrollbar-thumb{
       background-color: rgba(107, 100, 100, 0.664);
   
       border-radius: 100px;
       
   }   
   ::-webkit-scrollbar-thumb:horizontal{
       background-color: rgba(107, 100, 100, 0.664);
   
       border-radius: 100px;
       
   }   
   ::-webkit-scrollbar-thumb:hover{
       background-color: rgba(146, 145, 145, 0.541);
   
       border-radius: 100px;
       
   }   
   .css-1jbbcbn-MuiDataGrid-columnHeaderTitle {
       white-space: pre-wrap !important;
       line-height: 1.2;
   }
   