import { useRef, useEffect, useState } from 'react';
import Level from './Level';
import SVGConnectors from './SVGConnectors';

const WorkflowDiagram = ({ groupName, groupData, onTaskClick }) => {
  const diagramRef = useRef(null);
  const [sourceTargets, setSourceTargets] = useState([]);
  const levels = [];

  const addLevel = (type, label, tasks, levelIndex) => {
    return {
      type,
      label,
      tasks: tasks.map((task, i) => ({
        ...task,
        id: `${groupName}-level${levelIndex}-task${i}`,
      })),
    };
  };

  // Requestor Level
  levels.push(
    addLevel('requestor', groupData.requestorTaskLevelName, [
      {
        name: groupData.requestorTaskName,
        sla: groupData.requestor_sla,
        group: groupData.requestorTaskGroup,
        approver: 'Requester',
        level: groupData.requestorTaskLevelName,
        status: 'Pending',
      },
    ], 0)
  );

  // Intermediate Levels
  groupData?.workflowTaskDetailsByLevel?.forEach((level, index) => {
    const key = Object.keys(level)[0];
    const taskList = level[key];
    if (taskList.length > 0) {
      levels.push(
        addLevel(
          'workflow',
          `Level ${taskList[0].workflowApprovalLevel}: ${taskList[0].workflowLevelName}`,
          taskList.map(t => ({
            name: t.workflowTaskName,
            sla: t.taskSla,
            group: t.workflowTaskGroup,
            approver: t.taskApprover,
            level: t.workflowLevelName,
            status: 'In Progress',
          })),
          index + 1
        )
      );
    }
  });

  // MDM Level
  levels.push(
    addLevel('mdm', groupData.mdmTaskLevelName, [
      {
        name: groupData.mdmTaskName,
        sla: groupData.mdmApprover_sla,
        group: groupData.mdmTaskGroup,
        approver: groupData.mdmApprover_RecipientUsers,
        level: groupData.mdmTaskLevelName,
        status: 'Not Started',
      },
    ], levels.length)
  );

  // Generate connector pairs
  useEffect(() => {
    const pairs = [];
    for (let i = 0; i < levels.length - 1; i++) {
      const fromLevel = levels[i];
      const toLevel = levels[i + 1];
      fromLevel.tasks.forEach(fromTask => {
        if (toLevel.tasks.length > 0) {
          pairs.push({
            fromId: fromTask.id,
            toId: toLevel.tasks[0].id,
          });
        }
      });
    }
    setSourceTargets(pairs);
  }, [groupName]);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'stretch',
        width: '100%',
        borderRadius: 8,
        padding: 16,
        minHeight: 120,
        position: 'relative',
        gap:"24px"
      }}
      ref={diagramRef}
    >
      {levels?.map((level, i) => (
        <Level
          key={i}
          type={level.type}
          label={level.label}
          tasks={level.tasks}
          onTaskClick={onTaskClick}
        />
      ))}
      <SVGConnectors containerRef={diagramRef} sourceTargets={sourceTargets} />
    </div>
  );
};

export default WorkflowDiagram;
