import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  TextField,
  Typography,
} from "@mui/material";
// import ReactCountryFlag from "react-country-flag"
import { useSelector, useDispatch } from "react-redux";
import InputType from "./InputType";
import AutocompleteType from "./AutoCompleteType";
import MultiSelectType from "./MultiSelectType";
import RadioType from "./RadioType";
import DateType from "./DateType";
import { setMultipleMaterialPayloadKey, setPayload } from "../../../app/payloadSlice";
import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { colors } from "@constant/colors";
import useLang from "@hooks/useLang";

export default function FilterField(props) {
  const dispatch  = useDispatch();
  const payloadState = useSelector((state) => state.payload);
  let taskData = useSelector((state) => state.userManagement.taskData);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isWorkspace = queryParams.get("RequestId");
  const { t } = useLang();

  useEffect(() =>{
    if(!taskData?.requestId){
    if((props?.field?.fieldName === "Created On") || (props?.field?.fieldName === "Updated On")){
      const currentDate = new Date();
      dispatch(
        setMultipleMaterialPayloadKey({
          materialID: props?.materialID,
          keyName: props.field.jsonName,
          data: currentDate,
        })
      );
    }
    // NOTE Might be used later.
    // else if(props?.field?.fieldName === "Request Status"){
    //   dispatch(
    //     setMultipleMaterialPayloadKey({
    //       materialID: props?.materialID,
    //       keyName: props.field.jsonName,
    //       data: "DRAFT",
    //     })
    //   );
    // }
  }
  },[])

  const userData = useSelector((state) => state.userManagement.userData);
  if (props?.field?.fieldName === "Created By") {
    return (
      <Grid item md={2}>
        <Stack>
          <Typography variant="body2" color="#777" sx={{whiteSpace: 'nowrap',overflow: 'hidden',textOverflow: 'ellipsis',maxWidth: '100%'}} title={props.field.fieldName}>
            {t(props.field.fieldName)}
          </Typography>
          <TextField
            title={!isWorkspace? userData?.emailId : payloadState?.payloadData?.ReqCreatedBy}
            size="small"
            value={!isWorkspace? userData?.emailId : payloadState?.payloadData?.ReqCreatedBy}
            disabled={
              userData?.emailId ? true
                : false
            }
            sx={{
              cursor: "not-allowed", 
              "& .MuiInputBase-root": {
                height: "34px",
              },
              "& .MuiInputBase-input": {
                cursor: "not-allowed",
              },
              '& .MuiInputBase-root.Mui-disabled': {
                '& > input': {
                  WebkitTextFillColor: colors.black.dark,
                  color: colors.black.dark,
                },
                backgroundColor: colors.hover.light,
              }
            }}
          
          />
        </Stack>
      </Grid>
    );
  }
  else if(props?.field?.fieldName === "Created On") {
    const currentDate = new Date();
    const day = String(currentDate.getDate()).padStart(2, "0"); 
    const month = String(currentDate.getMonth() + 1).padStart(2, "0"); 
    const year = currentDate.getFullYear();
    
    const formattedDate = `${day}-${month}-${year}`; 
    return (
      <Grid item md={2}>
        <Stack>
          <Typography variant="body2" color="#777" sx={{whiteSpace: 'nowrap',overflow: 'hidden',textOverflow: 'ellipsis',maxWidth: '100%'}} title={t(props.field?.fieldName)}>
            {t(props?.field?.fieldName)}
          </Typography>
          <TextField
            size="small"
            value={formattedDate} 
            disabled={true}
            sx={{
              cursor: "not-allowed",
              "& .MuiInputBase-root": {
                height: "34px",
              },
              "& .MuiInputBase-input": {
                cursor: "not-allowed",
              },
            '& .MuiInputBase-root.Mui-disabled': {
                '& > input': {
                  WebkitTextFillColor: colors.black.dark,
                  color: colors.black.dark,
                },
                backgroundColor: colors.hover.light,
              }
            }}
          />
        </Stack>
      </Grid>
    );
  }
  else if(props?.field?.fieldName === "Updated On") {
    const currentDate = new Date();
    const day = String(currentDate.getDate()).padStart(2, "0"); 
    const month = String(currentDate.getMonth() + 1).padStart(2, "0"); 
    const year = currentDate.getFullYear();
    const formattedDate = `${day}-${month}-${year}`; 
    return (
      <Grid item md={2}>
        <Stack>
          <Typography variant="body2" color="#777" sx={{whiteSpace: 'nowrap',overflow: 'hidden',textOverflow: 'ellipsis',maxWidth: '100%'}} title={t(props.field?.fieldName)}>
            {t(props?.field?.fieldName)}
          </Typography>
          <TextField
            size="small"
            value={formattedDate} 
            disabled={true}
            sx={{
              cursor: "not-allowed",
              "& .MuiInputBase-root": {
                height: "34px",
              },
              "& .MuiInputBase-input": {
                cursor: "not-allowed",
              },
            '& .MuiInputBase-root.Mui-disabled': {
                '& > input': {
                  WebkitTextFillColor: colors.black.dark,
                  color: colors.black.dark,
                },
                backgroundColor: colors.hover.light,
              }
            }}
          />
        </Stack>
      </Grid>
    );
  }
  switch (props?.field?.fieldType) {
    case "Input":
      return (
        <InputType
          details={props.field}
          materialID={props?.materialID}
          selectedMaterialNumber={props?.selectedMaterialNumber}
          disabled={props?.disabled}
          viewName={props?.viewName}
          plantData={props?.plantData}
          isRequestHeader={props?.isRequestHeader}
          keyName={
            props.field.jsonName 
            ? props.field.jsonName
            .replaceAll("(", "")
            .replaceAll(")", "")
            .replaceAll("/", "")
            .replaceAll("-", "")
            .replaceAll(".", "")
            .replaceAll("%", "")
            .split(" ")
            .join("")
            : props.field.fieldName
            .replaceAll("(", "")
            .replaceAll(")", "")
            .replaceAll("/", "")
            .replaceAll("-", "")
            .replaceAll(".", "")
            .replaceAll("%", "")
            .split(" ")
            .join("")
            }     
        />
      );
      break;
    case "Disable Input":
      return (
        <InputType
          details={props.field}
          disabled={true}
          materialID={props?.materialID}
          viewName={props?.viewName}
          plantData={props?.plantData}
          keyName={props.field.jsonName
          ? props.field.jsonName
            .replaceAll("(", "")
            .replaceAll(")", "")
            .replaceAll("/", "")
            .replaceAll("-", "")
            .replaceAll(".", "")
            .replaceAll("%", "")
            .split(" ")
            .join("")
            : props.field.fieldName
            .replaceAll("(", "")
            .replaceAll(")", "")
            .replaceAll("/", "")
            .replaceAll("-", "")
            .replaceAll(".", "")
            .replaceAll("%", "")
            .split(" ")
            .join("")
            }
        />
      );
      break;
    case "Drop Down":
      return (
        <AutocompleteType
          details={props.field}
          materialID={props?.materialID}
          selectedMaterialNumber={props?.selectedMaterialNumber}
          disabled={props?.disabled}
          viewName={props?.viewName}
          plantData={props?.plantData}
          isRequestHeader={props?.requestHeader}
          data={
            props.dropDownData[
              props.field.fieldName
                .replaceAll("(", "")
                .replaceAll(")", "")
                .replaceAll("/", "")
                .replaceAll("-", "")
                .replaceAll(".", "")
                .replaceAll(":", "")
                .replaceAll("%", "")
                .split(" ")
                .join("")
            ]
          }
          keyName={
            props.field.jsonName
            ? props.field.jsonName
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .replaceAll("%", "")
          .split(" ")
          .join("")
          : props.field.fieldName
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .replaceAll("%", "")
          .split(" ")
          .join("")}
        />
      );
      break;
    case "Multi Select":
      return (
        <MultiSelectType
          details={props.field}
          materialID={props?.materialID}
          selectedMaterialNumber={props?.selectedMaterialNumber}
          disabled={props?.disabled}
          viewName={t(props?.viewName)}
          plantData={props?.plantData}
          data={
            props.dropDownData[
              props.field.fieldName
                .replaceAll("(", "")
                .replaceAll(")", "")
                .replaceAll("/", "")
                .replaceAll("-", "")
                .replaceAll(".", "")
                .replaceAll(":", "")
                .replaceAll("%", "")
                .split(" ")
                .join("")
            ]
          }
          keyName={
            props.field.jsonName
            ? props.field.jsonName
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .replaceAll("%", "")
          .split(" ")
          .join("")
          : props.field.fieldName
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .replaceAll("%", "")
          .split(" ")
          .join("")}
        />
      );
      break;
    case "Radio Button":
      return (
        <RadioType
          details={props.field}
          materialID={props?.materialID}
          selectedMaterialNumber={props?.selectedMaterialNumber}
          disabled={props?.disabled}
          viewName={t(props?.viewName)}
          plantData={props?.plantData}
          keyName={
              props.field.jsonName
              ? props.field.jsonName
            .replaceAll("(", "")
            .replaceAll(")", "")
            .replaceAll("/", "")
            .replaceAll("-", "")
            .replaceAll(".", "")
            .replaceAll("%", "")
            .split(" ")
            .join("")
            : props.field.fieldName
            .replaceAll("(", "")
            .replaceAll(")", "")
            .replaceAll("/", "")
            .replaceAll("-", "")
            .replaceAll(".", "")
            .replaceAll("%", "")
            .split(" ")
            .join("")}
        />
      );
      break;
    case "Check Box":
      return (
        <RadioType
          details={props.field}
          materialID={props?.materialID}
          selectedMaterialNumber={props?.selectedMaterialNumber}
          disabled={props?.disabled}
          viewName={t(props?.viewName)}
          plantData={props?.plantData}
          keyName={
              props.field.jsonName
              ? props.field.jsonName
            .replaceAll("(", "")
            .replaceAll(")", "")
            .replaceAll("/", "")
            .replaceAll("-", "")
            .replaceAll(".", "")
            .replaceAll("%", "")
            .split(" ")
            .join("")
            : props.field.fieldName
            .replaceAll("(", "")
            .replaceAll(")", "")
            .replaceAll("/", "")
            .replaceAll("-", "")
            .replaceAll(".", "")
            .replaceAll("%", "")
            .split(" ")
            .join("")}
        />
      )
    case "Calendar":
      return (
        <DateType
          details={props.field}
          materialID={props?.materialID}
          selectedMaterialNumber={props?.selectedMaterialNumber}
          disabled={props?.disabled}
          viewName={t(props?.viewName)}
          plantData={props?.plantData}
          keyName={
            props.field.jsonName
            ? props.field.jsonName
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .replaceAll("%", "")
          .split(" ")
          .join("")
          : props.field.fieldName
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .replaceAll("%", "")
          .split(" ")
          .join("")}
        />
      );
      break;
    default:
      // return <h1> {props.field.fieldType}</h1>;
      break;
  }
}
