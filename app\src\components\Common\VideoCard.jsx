import { <PERSON>, Card, Grid, Stack, Typography } from "@mui/material";
import React from "react";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";

import ReactPlayer from "react-player";
import { destination_Admin } from "../../destinationVariables";
import PlayCircleOutlinedIcon from '@mui/icons-material/PlayCircleOutlined';
// import HomepageModal from "../HomepageModal";
const VideoCard = ({ title, description, date, id, viewall = false, link }) => {
  let source = link;
  if (link == "" || link == null) {
    source = `/${destination_Admin}/broadcastManagement/showBroadcastById/${id}`;
  }

  const [open, setOpen] = React.useState(false);
  const [playMode, setplayMode] = React.useState(true);

  const handleOpen = () => {setOpen(true);setplayMode(false)}
  const handleClose = () => {setOpen(false);setplayMode(true)}
  return (
    <>
      {!viewall && (
        <Stack
          direction="column"
          ml={1.5}
          justifyContent="center"
          alignItems="center"
          p={1.5}
          sx={{
            backgroundColor: "white",
            border: "1px solid #f4f4f4",
            borderRadius: "12px",
            height: "180px",
            "&:hover": { backgroundColor: "#f8f8f8" },
          }}
        >
          {/* <HomepageModal
            open={open}
            handleClose={handleClose}
            title={title}
            description={description}
            date={date}
            id={id}
            link={source}
          /> */}

          <Box sx={{ position: "relative" }} mb={1}>
            <div className="player-wrapper">
            {playMode ?  <ReactPlayer
                url={source}
                controls
               onStart={handleOpen}
               
                width="100%"
                height="70px"
                class="react-player"
              /> : <PlayCircleOutlinedIcon/> }
            </div>
          </Box>
          <Box width="100%">
            <Typography
              variant="subtitle2"
              fontWeight="bold"
              color="black"
              sx={{
                textOverflow: "ellipsis",
                overflow: "hidden",
                whiteSpace: "noWrap",
                textAlign:'center'
              }}
            >
              {title}
            </Typography>
            <Typography
              variant="body2"
              sx={{
                textOverflow: "ellipsis",
                overflow: "hidden",
                whiteSpace: "noWrap",
                textAlign:'center'
              }}
            >
              {description}
            </Typography>
            <Typography
              variant="body2"
              color="#473dcb"
              sx={{textAlign:'center', "&:hover": { cursor: "pointer" } }}
              onClick={handleOpen}
            
            >
              See More
            </Typography>
          </Box>
        </Stack>
      )}

      {viewall && (
        <Box
          sx={{
            width: "100%",
            backgroundColor: "white",
            border: "1px solid #f4f4f4",
            borderRadius: "6px",
            height: "100px",
          }}
        >
          {/* <HomepageModal
            open={open}
            handleClose={handleClose}
            title={title}
            description={description}
            date={date}
            id={id}
            link={source}
          /> */}

          <Grid
            container
            sx={{
              backgroundColor: "white",
              "&:hover": { backgroundColor: "#f8f8f8" },
            }}
            p={1.5}
          >
            <Grid item xs={2} sx={{ borderRight: "2px solid #8099b8" }}>
              <Stack
                direction="column"
                justifyContent="center"
                alignItems="center"
              >
                <Box sx={{ position: "relative" }} mb={1}>
                  <div className="player-wrapper">
                    <ReactPlayer
                      url={source}
                      controls
                      width="90%"
                      height="70px"
                      class="react-player"
                    />
                  </div>
                </Box>
              </Stack>
            </Grid>
            <Grid item xs={9}>
              <Stack direction="column" ml={1.5}>
                <Typography
                  variant="subtitle2"
                  fontWeight="bold"
                  color="black"
                  sx={{
                    textOverflow: "ellipsis",
                    overflow: "hidden",
                    whiteSpace: "noWrap",
                  }}
                >
                  {title}
                </Typography>
                <Typography
                  variant="body2"
                  color="#757575"
                  sx={{
                    textOverflow: "ellipsis",
                    overflow: "hidden",
                    whiteSpace: "noWrap",
                  }}
                >
                  {description}
                </Typography>
              </Stack>
            </Grid>
            <Grid item xs={1} mt={2.5}>

              <ArrowForwardIosIcon
              sx={{
              color: "#2462c3",
              fontSize: "16px",
              
             '&:hover':{
                transform: 'translate(4px)',transition: '0.5s', cursor: "pointer" ,fontSize:'16.5px'
              }
            }}
           
                
                onClick={handleOpen}
              />
            </Grid>
          </Grid>
        </Box>
      )}
    </>
  );
};

export default VideoCard;
