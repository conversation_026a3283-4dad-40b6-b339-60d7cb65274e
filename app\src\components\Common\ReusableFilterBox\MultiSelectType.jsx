import {
    Autocomplete,
    Checkbox,
    Chip,
    FormControlLabel,
    FormGroup,
    Grid,
    Popover,
    Stack,
    TextField,
    Typography,
    Box,
  } from "@mui/material";
  import { useDispatch, useSelector } from "react-redux";
  import { useEffect, useState, useRef } from "react";
  import { setMultipleMaterialPayloadKey, updateMaterialData } from "../../../app/payloadslice";
  import { colors } from "../../../constant/colors"
import { useLocation } from "react-router-dom";
  
  export default function MultiSelectType(props) {
    const dispatch = useDispatch();
    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const RequestId = queryParams.get('RequestId');
  
    const dropDownData = useSelector((state) => state.AllDropDown?.dropDown || {});
    const errorFields = useSelector((state) => state.payload?.errorFields || []);
    const changeFields = useSelector((state) => state.tabsData.changeFieldsDT);
    const initialData = useSelector((state) => state.payload.payloadData);
    const dynamicData = useSelector((state) => state.payload.dynamicKeyValues)
    const fieldSelectivity = changeFields?.["Field Selectivity"];
  
    const [selectedValues, setSelectedValues] = useState([]);
    const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
    const [popoverContent, setPopoverContent] = useState("");
    const [isPopoverVisible, setIsPopoverVisible] = useState(false);
    const popoverRef = useRef(null);
  
    const options = dropDownData?.[props?.keyName] || [];
    let allOptions = options.map((option) => option?.code || "");
  
    useEffect(() => {
      if (fieldSelectivity === "Disabled") {
        setSelectedValues(allOptions);
        dispatchUpdate(allOptions);
      } else {
        if(RequestId) {
          setSelectedValues(dynamicData?.requestHeaderData?.FieldName?.split("$^$") || [])
          return;
        }
        setSelectedValues([]);
      }

    }, [fieldSelectivity, initialData?.TemplateName, options]);
  
    const handlePopoverOpen = (event, content) => {
        setPopoverAnchorEl(event.currentTarget);
        setPopoverContent(content);
        setIsPopoverVisible(true);
      };
     
      const handlePopoverClose = () => {
        setIsPopoverVisible(false);
      };
     
      const handleMouseEnterPopover = () => {
        setIsPopoverVisible(true);
      };
     
      const handleMouseLeavePopover = () => {
        setIsPopoverVisible(false);
      };
     
      const popoverOpen = Boolean(popoverAnchorEl);
      const popoverId = popoverOpen ? "custom-popover" : undefined;
  
    const handleSelectAllReqType = () => {
      if (selectedValues.length === allOptions.length) {
        setSelectedValues([]);
        dispatchUpdate([]);
      } else {
        setSelectedValues(allOptions);
        dispatchUpdate(allOptions);
      }
    };
  
    const dispatchUpdate = (values) => {
      dispatch(
        updateMaterialData({
          materialID: props?.materialID || "",
          keyName: props?.keyName || "",
          data: values || [],
          viewID: props?.viewName,
          itemID: props?.plantData,
        })
      );
    };
  
    const isReqTypeSelected = (option) => selectedValues.includes(option);
    const isOptionDisabled = fieldSelectivity === "Disabled";
  
    return (
      <Grid item md={2} sx={{ marginBottom: "12px !important" }}>
        {props?.details?.visibility === "Hidden" ? null : (
          <Stack>
            <Typography
              variant="body2"
              color={colors.secondary.grey}
              sx={{ whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis", maxWidth: "100%" }}
              title={props?.details?.fieldName}
            >
              {props?.details?.fieldName || "Field Name"}
              {(props?.details?.visibility === "Mandatory" || props?.details?.visibility === "0") && (
                <span style={{ color: "red" }}>*</span>
              )}
            </Typography>
            <Autocomplete
                multiple
                fullWidth
                disableCloseOnSelect
                disabled={props?.disabled}
                size="small"
                value={selectedValues}
                onChange={(e, value, reason) => {
                    if(!isOptionDisabled) {
                        if (reason === "clear" || value?.length === 0) {
                        setSelectedValues([]);
                        dispatchUpdate([]);
                        return;
                    }

                    if (value.length > 0 && value[value.length - 1] === "Select All") {
                        handleSelectAllReqType();
                    } else {
                        setSelectedValues(value);
                        dispatchUpdate(value);
                    }
                  }
                }}
                options={allOptions.length ? ["Select All", ...allOptions] : []}
                getOptionLabel={(option) => `${option}` || ""}
                renderOption={(props, option, { selected }) => (
                    <li {...props} style={{ pointerEvents: isOptionDisabled ? "none" : "auto" }}>
                        <FormGroup>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        disabled={isOptionDisabled}
                                        checked={
                                            isReqTypeSelected(option) ||
                                            (option === "Select All" && selectedValues.length === allOptions.length)
                                        }
                                    />
                                }
                                label={<Typography style={{ fontSize: 12 }}><strong>{option}</strong></Typography>}
                            />
                        </FormGroup>
                    </li>
                )}
                renderTags={(selected, getTagProps) => {
                    // if (isOptionDisabled) return null; // No tags or chips if disabled

                    const selectedOptionsText = selected.join("<br />");
                    return selected.length > 1 ? (
                        <>
                            <Chip
                                sx={{
                                    height: 25,
                                    fontSize: "0.85rem",
                                    ".MuiChip-label": { padding: "0 6px" },
                                    "&.Mui-disabled": {
                                        color: colors?.text?.primary,
                                        opacity: 1
                                    }
                                }}
                                label={`${selected[0]}`}
                                {...getTagProps({ index: 0 })}
                                
                            />
                            <Chip
                                sx={{
                                    height: 25,
                                    fontSize: "0.85rem",
                                    ".MuiChip-label": { padding: "0 6px" },
                                }}
                                label={`+${selected.length - 1}`}
                                onMouseEnter={(event) => handlePopoverOpen(event, selectedOptionsText)}
                                onMouseLeave={handlePopoverClose}
                                
                            />
                            <Popover
                                    id={popoverId}
                                    open={isPopoverVisible}
                                    anchorEl={popoverAnchorEl}
                                    onClose={handlePopoverClose}
                                    anchorOrigin={{
                                      vertical: "bottom",
                                      horizontal: "center",
                                    }}
                                    transformOrigin={{
                                      vertical: "top",
                                      horizontal: "center",
                                    }}
                                    onMouseEnter={handleMouseEnterPopover}
                                    onMouseLeave={handleMouseLeavePopover}
                                    ref={popoverRef}
                                    sx={{
                                      "& .MuiPopover-paper": {
                                        backgroundColor: "#f5f5f5",
                                        boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                                        borderRadius: "8px",
                                        padding: "10px",
                                        fontSize: "0.875rem",
                                        color: "#4791db",
                                        border: "1px solid #ddd",
                                      },
                                    }}
                                  >
                                    <Box
                                      sx={{
                                        maxHeight: "270px",
                                        overflowY: "auto",
                                        padding: "5px",
                                      }}
                                      dangerouslySetInnerHTML={{ __html: popoverContent }}
                                    />
                                  </Popover>
                        </>
                    ) : (
                        selected.map((option, index) => (
                            <Chip
                                sx={{
                                    height: 25,
                                    fontSize: "0.85rem",
                                    ".MuiChip-label": { padding: "0 6px" },
                                }}
                                label={`${option}`}
                                {...getTagProps({ index })}
                                
                            />
                        ))
                    );
                }}
                renderInput={(params) => (
                    <TextField
                        {...params}
                        variant="outlined"
                        placeholder={
                            selectedValues?.length === 0 
                            ? `Select ${props?.details?.fieldName}`
                            : ""
                        }
                        error={errorFields.includes(props?.keyName || "")}
                        InputProps={{
                            ...params.InputProps,
                            endAdornment: isOptionDisabled
                                ? null // Removes clear icon when disabled
                                : params.InputProps.endAdornment,
                        }}
                        sx={{
                            fontSize: "12px !important",
                            "& .MuiOutlinedInput-root": { height: 35 },
                            "& .MuiInputBase-input": { padding: "10px 14px" },
                        }}
                    />
                )}
            />
          </Stack>
        )}
      </Grid>
    );
  }
  