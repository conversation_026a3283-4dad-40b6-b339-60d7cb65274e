import "react-dropzone-uploader/dist/styles.css";
import Dropzone from "react-dropzone-uploader";
import { Box, Button, Grid, Paper, Stack, Typography } from "@mui/material";
import { useState, useEffect } from "react";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import { useDispatch, useSelector } from "react-redux";

const Layout = ({
  input,
  previews,
  submitButton,
  dropzoneProps,
  files,
  extra: { maxFiles },
}) => {
  const [DragView, setDragView] = useState([]);
  const dispatch = useDispatch();
  
  // Initialize DragView when previews change
  useEffect(() => {
    if (previews && Array.isArray(previews)) {
      setDragView(previews);
    }
  }, [previews]);

  function handleOnDragEnd(result) {
    if (!result.destination) return;
    
    const items = Array.from(DragView);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    setDragView(items);

    // Optional: Update sequence in Redux store
    // dispatch(commonFilterUpdate({
    //   module: "Broadcast",
    //   filterData: {
    //     sequence: items.map((i) => i.props?.meta?.name || ''),
    //   },
    // }));
  }

  return (
    <Grid container spacing={2}>
      <Grid item xs={6}>
        <Stack 
          sx={{ 
            border: "1px dashed #ccc", 
            borderRadius: "8px",
            padding: 2,
            minHeight: "300px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "#fafafa"
          }} 
          {...dropzoneProps}
        >
          <Box textAlign="center">
            <Typography
              sx={{
                color: "#3B30C8",
                fontSize: "16px",
                fontWeight: "bold",
                mb: 2,
              }}
            >
              Drag and Drop to Upload File
            </Typography>
            <Typography
              sx={{ fontSize: "14px", mb: 2 }}
              variant="body2"
              fontWeight="bold"
              color="black"
            >
              Or
            </Typography>
            <Box mb={2}>{input}</Box>
            <Typography color="#757575" variant="body2">
              Supported Files: PNG, JPG or SVG (Max {maxFiles} files)
            </Typography>
          </Box>
        </Stack>
      </Grid>
      
      <Grid item xs={6}>
        <Stack spacing={2}>
          <Box>
            <Typography color="black" fontWeight="bold" fontSize="16px">
              Uploaded Banners ({DragView.length})
            </Typography>
            <Typography color="#757575" fontSize="12px">
              Drag to rearrange position of picture in Carousel
            </Typography>
          </Box>
          
          <Box sx={{ maxHeight: "250px", overflowY: "auto" }}>
            {DragView.length > 0 ? (
              <DragDropContext onDragEnd={handleOnDragEnd}>
                <Droppable droppableId="droppable">
                  {(provided) => (
                    <Stack 
                      {...provided.droppableProps} 
                      ref={provided.innerRef}
                      spacing={1}
                    >
                      {DragView.map((item, index) => {
                        const draggableId = item.key || `draggable-${index}`;
                        return (
                          <Draggable
                            key={draggableId}
                            index={index}
                            draggableId={draggableId}
                          >
                            {(provided, snapshot) => (
                              <Box
                                sx={{ 
                                  position: "relative",
                                  backgroundColor: snapshot.isDragging ? "#f0f0f0" : "transparent",
                                  borderRadius: "4px",
                                  padding: "4px"
                                }}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                ref={provided.innerRef}
                              >
                                {item}
                                <Box
                                  sx={{
                                    position: "absolute",
                                    left: 120,
                                    top: "50%",
                                    transform: "translateY(-50%)",
                                  }}
                                >
                                  <Typography variant="body2" color="black">
                                    {item.props?.meta?.name || `Image ${index + 1}`}
                                  </Typography>
                                </Box>
                              </Box>
                            )}
                          </Draggable>
                        );
                      })}
                      {provided.placeholder}
                    </Stack>
                  )}
                </Droppable>
              </DragDropContext>
            ) : (
              <Typography variant="body2" color="#757575" textAlign="center">
                No banners uploaded yet
              </Typography>
            )}
          </Box>
          
          {files.length > 0 && (
            <Box sx={{ mt: 2 }}>
              {submitButton}
            </Box>
          )}
        </Stack>
      </Grid>
    </Grid>
  );
};

const FileDropZone = (props) => {
  const [initialFiles, setInitialFiles] = useState([]);
  
  // Convert uploaded files to File objects for dropzone
  const convertUploadedFiles = async () => {
    if (!props?.uploadedFiles || props.uploadedFiles.length === 0) {
      setInitialFiles([]);
      return;
    }

    try {
      const filePromises = props.uploadedFiles.map(async (item, index) => {
        if (!item.url || !item.name) return null;
        
        try {
          const response = await fetch(item.url);
          const blob = await response.blob();
          
          return new File([blob], item.name, {
            type: item.type || 'image/jpeg',
          });
        } catch (error) {
          console.error(`Error converting file ${item.name}:`, error);
          return null;
        }
      });

      const convertedFiles = await Promise.all(filePromises);
      const validFiles = convertedFiles.filter(file => file !== null);
      setInitialFiles(validFiles);
    } catch (error) {
      console.error('Error converting uploaded files:', error);
      setInitialFiles([]);
    }
  };

  useEffect(() => {
    convertUploadedFiles();
  }, [props?.uploadedFiles]);

  const handleChangeStatus = ({ meta, file, xhr }, status) => {
    console.log('File status changed:', { meta: meta.name, status });
  };

  return (
    <div id="imageUpload" className="dropzone mt-2">
      <Dropzone
        onSubmit={props?.handleAttachmentsSubmit}
        onChangeStatus={handleChangeStatus}
        styles={{
          submitButton: {
            background: "#3B30C8",
            color: "white",
            border: "none",
            borderRadius: "4px",
            padding: "8px 16px",
            cursor: "pointer",
            fontWeight: "normal",
          },
          dropzone: {
            border: "none",
            padding: 0,
            overflow: "hidden",
            minHeight: "350px",
            backgroundColor: "transparent",
          },
          submitButtonContainer: { 
            textAlign: "right",
            marginTop: "16px",
          },
          previewImage: {
            borderRadius: "4px",
            width: "100px",
            height: "85px",
            objectFit: "cover",
          },
          preview: { 
            padding: "8px",
            border: "1px solid #ddd",
            borderRadius: "4px",
            marginBottom: "8px",
            backgroundColor: "white",
          },
          inputLabel: {
            textTransform: "none",
            height: "36px",
            backgroundColor: "#3B30C8",
            color: "white",
            fontWeight: "normal",
            padding: "8px 16px",
            borderRadius: "4px",
            border: "none",
            cursor: "pointer",
            fontSize: "14px",
          },
          inputLabelWithFiles: {
            textTransform: "none",
            backgroundColor: "#3B30C8",
            color: "white",
            fontWeight: "normal",
            padding: "8px 16px",
            borderRadius: "4px",
            border: "none",
            cursor: "pointer",
          },
        }}
        LayoutComponent={Layout}
        inputContent="Browse Files"
        inputWithFilesContent="Add More Files"
        maxFiles={4}
        initialFiles={initialFiles}
        submitButtonContent="Save Banners"
        accept="image/*"
      />
    </div>
  );
};

export default FileDropZone;