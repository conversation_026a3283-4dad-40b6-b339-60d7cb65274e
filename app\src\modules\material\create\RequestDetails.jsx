import { useState, useEffect, Fragment, useRef } from "react";
import { Ta<PERSON>, Tab, <PERSON>, Button, Checkbox, IconButton, Chip, Stack, Paper, Dialog, FormControl, TextField, DialogActions, DialogTitle, Typography, DialogContent, Tooltip, Autocomplete, Grid, TableContainer, TableHead, TableRow, TableCell, TableBody, Table, FormLabel, RadioGroup, FormControlLabel, Radio, Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import DescriptionIcon from "@mui/icons-material/Description";
import TaskAltIcon from "@mui/icons-material/TaskAlt";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { useSelector, useDispatch } from "react-redux";
import GenericTabs from "@components/MasterDataCockpit/GenericTabs";
import { setMaterialRows } from "@slice/requestDataSlice";
import BottomNav from "../BottomNav";
import { v4 as uuidv4 } from "uuid";
import { useLocation } from "react-router-dom";
import { pushMaterialDisplayData, removeMaterialRow, resetOnMatTypeChange, setMultipleMaterialHeader, setMultipleMaterialHeaderKey, setUOmData, updateMaterialData, setPayload, setOrgElementDefaultValues } from "../../../app/payloadSlice";
import { updatePage } from "@slice/paginationSlice";
import CloseIcon from "@mui/icons-material/Close";
import ReusableDialog from "@components/Common/ReusableDialog";
import { doAjax } from "@components/Common/fetchService";
import { destination_IDM, destination_MaterialMgmt } from "../../../destinationVariables";
import AdditionalData from "../AdditionalData/AdditionalData";
import { API_CODE, CHANGE_KEYS, DELETE_MODAL_BUTTONS_NAME, DIALOUGE_BOX_MESSAGES, ENABLE_STATUSES, ERROR_MESSAGES, MATERIAL_TABLE, MATERIAL_VIEWS, REQUEST_TYPE, VALIDATION_STATUS, LOADER_MESSAGES, DEFAULT_VALUES, HEADINGS, REGION_CODE, MATERIAL_TYPE_DRODOWN, EXCLUDED_VIEWS, RELATION_DROPDOWN, LOCAL_STORAGE_KEYS, CATEGORY_DROPDOWN, BUTTON_NAME, VAR_ORD_UNIT, UI_HIDDEN_VIEWS, DT_TABLES, DOC_SNACKBAR_MESSAGES, ORG_FIELDS, MODULE_MAP } from "../../../constant/enum";
import { makeStyles } from "@mui/styles";
import useLogger from "@hooks/useLogger";
import { setOrgData, updateAllTabsData } from "@slice/tabsDetailsSlice";
import useMaterialFieldConfig from "@hooks/useMaterialFieldConfig";
import { checkCommonView, checkIncludedAndValidated, getKeysWithOnlyNumbers, transformMaterialNumberKeys, getValidationStatus, addMissingViews, sortByCode, getKeysValue, addViewForWithRefrence, filterButtonsBasedOnTab, fetchClassByType, getTranformedOrgDefault } from "@helper/helper";
import { orgOptionShow } from "@constant/showOrgData";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import useValidation from "@hooks/useValidation";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { button_Outlined, button_Primary } from "@components/Common/commonStyles";
import { END_POINTS } from "@constant/apiEndPoints";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { setCreatePayloadCopyForChangeLog } from "@app/changeLogReducer";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import useFetchDropdownAndDispatch from "@hooks/useFetchDropdownAndDispatch";
import useDisplayCall from "@hooks/useDisplayCall";
import { transformResponseForCreateRef } from "../../../functions";
import { colors } from "@constant/colors";
import useValuationClass from "@hooks/useValuationClass";
import FileCopyIcon from "@mui/icons-material/FileCopy";
import OrgDataCopyModal from "../extend/OrgDataCopyModal";
import useFetchAccordianFieldsOptions from "@hooks/useFetchAccordianFieldsOptions";
import { ToastContainer } from "react-toastify";
import useCountryBasedOnPlant from "@hooks/useCountryBasedOnPlant";
import CropFreeIcon from '@mui/icons-material/CropFree';
import CloseFullscreenIcon from '@mui/icons-material/CloseFullscreen';
import Swal from 'sweetalert2';
import {useCreateDynamicButtons} from "@hooks/useCreateDynamicButtons";
import useDynamicWorkflowDT from "@hooks/useDynamicWorkflowDT";
import { setDropDown } from "@app/dropDownDataSlice";
import useLang from "@hooks/useLang";
import { BUTTONS_ACTION_TYPE } from "@constant/buttonPriority";
import useCustomDtCall from "@hooks/useCustomDtCall";
import LibraryAddIcon from '@mui/icons-material/LibraryAdd';
import { useSnackbar } from "@hooks/useSnackbar";
const useStyles = makeStyles(() => ({
  customTabs: {
    "& .MuiTabs-scroller": {
      overflowX: "auto !important",
      overflowY: "hidden !important",
    },
  },
}));

const RequestDetails = (props) => {
  const classes = useStyles();
  const { customError } = useLogger();
  const dispatch = useDispatch();
  const { getDynamicWorkflowDT } = useDynamicWorkflowDT();
  const { fetchMaterialFieldConfig } = useMaterialFieldConfig();
  const { getNextDisplayDataForCreate } = useDisplayCall();
  const { fetchValuationClassData } = useValuationClass();
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const requestType = initialPayload?.RequestType;
  const regionBasedSalesOrgData = useSelector((state) => state.request.salesOrgDTData);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const paginationData = useSelector((state) => state.paginationData);
  const singlePayloadData = useSelector((state) => state.payload);
  const requestDetails = useSelector((state) => state.request.requestHeader);
  const storedRows = useSelector((state) => state.request.materialRows); // Retrieve stored rows from Redux
  const payloadFields = useSelector((state) => state.payload.payloadData);
  const allDropDownData = useSelector((state) => state.AllDropDown?.dropDown || {});
  const allTabsData = useSelector((state) => state.tabsData.allTabsData);
  let userData = useSelector((state) => state.userManagement.userData);
  let userRoles = useSelector((state) => state.userManagement.roles);
  let taskData = useSelector((state) => state.userManagement.taskData);
  const allMaterialFieldConfigDT = useSelector((state) => state.tabsData.allMaterialFieldConfigDT);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  const isrequestId = queryParams.get("RequestId");
  const [isGridZoomed, setIsGridZoomed] = useState(false);
  const [isTabsZoomed, setIsTabsZoomed] = useState(false);
  const [page, setPage] = useState(0);
  const [timerId, setTimerId] = useState(null);
  const [expandedAccordion, setExpandedAccordion] = useState(null); // State to track expanded accordion
  const fixedOption = "Basic Data";
  const [selectedViews, setSelectedViews] = useState([fixedOption]);
  const [isDeleteDialogVisible, setIsDeleteDialogVisible] = useState({ data: {}, isVisible: false });
  const [rows, setRows] = useState(storedRows || []); // Initialize state with stored rows or an empty array
  const selectedSections = useSelector((state) => state.selectedSections.selectedSections);
  const [addButtonDisabled, setAddButtonDisabled] = useState(rows?.length ? true : false); // Initialize state with stored rows or an empty array
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const { fetchTabSpecificData } = useFetchAccordianFieldsOptions();
  const [materialOptions, setMaterialOptions] = useState([]);
  const [skip, setSkip] = useState(0); // Skip value for API call
  const [inputState, setInputState] = useState(null); // Skip value for API call
  const [isLoading, setIsLoading] = useState(false); // Loading state
  const [submitForApprovalDisabled, setSubmitForApprovalDisabled] = useState(true);
  const [counter, setCounter] = useState(rows.length + 1); // Counter for unique row IDs
  const [activeTab, setActiveTab] = useState(0); // State for active tab
  const [rowsAdded, setRowsAdded] = useState(storedRows.length > 0); // To track if rows have been added
  const [basicData, setBasicData] = useState({});
  const [dropDownData, setDropDownData] = useState({});
  const [lineNumberCounter, setLineNumberCounter] = useState(0);
  const [materialNumberValidationData, setMaterialNumberValidationData] = useState([]);
  const [currentRowData, setCurrentRowData] = useState({});
  const [allViews, setAllViews] = useState([]);
  const [openViews, setOpenViews] = useState(false);
  const [rowId, setRowId] = useState("");
  const [activeViewTab, setActiveViewTab] = useState("Basic Data");
  const [openOrgData, setOpenOrgData] = useState(false);
  let orgRowFields = { id: 0, salesOrg: null, dc: { value: null, options: [] }, plant: { value: null, options: [] }, sloc: { value: null, options: [] }, warehouse: { value: null, options: [] }, mrpProfile: null };
  const [orgRow, setOrgRow] = useState([orgRowFields]);
  const [openAddMatPopup, setOpenAddMatPopup] = useState(false);
  const [selectedMaterials, setSelectedMaterials] = useState(null);
  const [withReference, setWithReference] = useState("yes");
  const [selectedMatLines, setSelectedMatLines] = useState([]);
  const [selectedMaterialID, setSelectedMaterialID] = useState(null);
  const selectedMaterialHeaderPayload = singlePayloadData?.[selectedMaterialID]?.headerData;
  const [alertType, setAlertType] = useState("success");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [missingValidationPlant, setMissingValidationPlant] = useState([]);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [orgDTData, setOrgDTData] = useState("");
  const [selectedMaterialNumber, setSelectedMaterialNumber] = useState("");
  const matViews = useSelector((state) => state.tabsData.matViews);
  const { checkValidation } = useValidation(singlePayloadData, allMaterialFieldConfigDT, selectedViews, setSelectedMaterialID, currentRowData);
  const { t } = useLang();
  const { getDtCall, dtData } = useCustomDtCall();
  const [mandatoryFailedView, setMandatoryFailedView] = useState(null);

  const templates = [
    { region: "US", temp: "MIDDLE EAST HUB" },
    { region: "US", temp: "SOUTHERN HUB" },
    { region: "EUR", temp: "NORTH HUB" },
    { region: "EUR", temp: "CENTRAL HUB" },
    { region: "EUR", temp: "WEST HUB" },
  ];

  const [selectedTemp, setSelectedTemp] = useState(null);
  const [blurLoading, setBlurLoading] = useState("");
  const [loaderMessage, setLoaderMessage] = useState("");
  const latestOrgToUse = useRef(orgRow);
  const [isOrgFilled, setIsOrgFilled] = useState(false);
  const selectedMaterialPayload = singlePayloadData?.[selectedMaterialID]?.payloadData;
  const { fetchDataAndDispatch } = useFetchDropdownAndDispatch();
  const withRefParams = ["Sales Org","Plant", "Distribution Channel", "Storage Location", "Warehouse"];
  const [withRefValues, setWithRefValues] = useState({});
  const [openOrgCopyModal, setOpenOrgCopyModal] = useState(false);
  const [lengthOfOrgRow, setLengthOfOrgRow] = useState(0);
  const [isDropdownLoading, setIsDropdownLoading] = useState({
      "Material No" : false,
    });
  const { getContryBasedOnPlant } = useCountryBasedOnPlant({
    doAjax,
    customError,
    fetchDataAndDispatch,
    destination_MaterialMgmt
  });
  const [wfLevels,setWfLevels] = useState([]);
  const {filteredButtons,showWfLevels} = useCreateDynamicButtons(taskData,applicationConfig,destination_IDM,BUTTON_NAME);
  const requestDetailsButton = filterButtonsBasedOnTab(filteredButtons,[BUTTONS_ACTION_TYPE.HANDLE_SUBMIT_FOR_APPROVAL,BUTTONS_ACTION_TYPE.HANDLE_SAP_SYNDICATION,BUTTONS_ACTION_TYPE.HANDLE_SUBMIT_FOR_REVIEW]);
  const { showSnackbar } = useSnackbar();
  const descriptionMaxLength = 40;
  const matNumberMaxLength = 18;
  // Sync rows to Redux when rows state changes
  useEffect(() => {
    setRows(storedRows);
    setRowsAdded(storedRows?.length > 0);
    if (storedRows?.length > 0 && isrequestId) {
      setSelectedMaterialID(storedRows?.[0]?.id);
      setSelectedMaterialNumber(storedRows?.[0]?.materialNumber);
      handleMaterialType(storedRows?.[0]?.materialType?.code);
      setActiveTab(0);
      setActiveViewTab(MATERIAL_VIEWS.BASIC_DATA);
      setSelectedViews(storedRows?.[0]?.views?.length ? storedRows?.[0]?.views : [fixedOption]);
      const allMaterials = getKeysWithOnlyNumbers(singlePayloadData);
      const updatedMaterials = transformMaterialNumberKeys(allMaterials);
      let payloadDataForCopy = JSON.parse(JSON.stringify(updatedMaterials));
      dispatch(setCreatePayloadCopyForChangeLog(payloadDataForCopy));
      dispatch(setPayload({keyName:"selectedMaterialID",data:storedRows?.[0]?.id}))
      if(singlePayloadData?.[storedRows?.[0]?.id]?.Tochildrequestheaderdata?.ChildRequestId){
        dispatch(setPayload({ keyName: "childRequestId", data: singlePayloadData?.[storedRows?.[0]?.id]?.Tochildrequestheaderdata?.ChildRequestId }))
      } 
    }
  }, [storedRows]);
  useEffect(() => {
    if (storedRows?.[0]?.materialType) {
      getMaterialNumValidation(storedRows?.[0]?.materialType);
      handleRowSelection({ row: storedRows[0] });
      const allRowsValidated = checkIncludedAndValidated(storedRows);
      if (allRowsValidated) {
        setSubmitForApprovalDisabled(false);
        setAddButtonDisabled(false);
      }
    }
    if (storedRows?.length) {
      setLineNumberCounter(storedRows?.at(-1)?.lineNumber);
    }
    dispatch(setDropDown({ keyName: 'VarOrdUn', data: VAR_ORD_UNIT }))
    if(!storedRows?.length && !isrequestId){
      getOrgElementDefaultVal();
    }
  }, []);
  useEffect(() => {
    if(dtData && dtData?.customParam?.dt === DT_TABLES.MDG_ORG_ELEMENT_DEFAULT_VALUE){
      const transformedOrgDefault = getTranformedOrgDefault(dtData?.data?.result?.[0]?.MDG_ORG_ELEMENT_DEFAULT_VALUE_ACTION_TYPE);
      dispatch(setOrgElementDefaultValues({ data: transformedOrgDefault }));
    }
  }, [dtData]);
  const getOrgElementDefaultVal = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DT_TABLES.MDG_ORG_ELEMENT_DEFAULT_VALUE,
      version: "v1",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_REGION": initialPayload?.Region,
          "MDG_CONDITIONS.MDG_SCENARIO" : initialPayload?.RequestType,
        },
      ],
    };
    getDtCall(payload,{dt: DT_TABLES.MDG_ORG_ELEMENT_DEFAULT_VALUE});
  };
  useEffect(() => {
    const fetchWorkflowLevels = async () => {
      try {
        const workflowLevelsDtData = await getDynamicWorkflowDT(
          requestType,
          initialPayload?.Region,
          '',
          singlePayloadData[selectedMaterialID]?.Tochildrequestheaderdata?.MaterialGroupType,
          taskData?.ATTRIBUTE_3,
          'v4', 'MDG_MAT_DYNAMIC_WF_DT', MODULE_MAP.MATERIAL
        );
        setWfLevels(workflowLevelsDtData);
      } catch (err) {
        customError(err);
      }
    };

    if (requestType && initialPayload?.Region && selectedMaterialID && taskData?.ATTRIBUTE_3) {
      fetchWorkflowLevels();
    }
  }, [requestType, initialPayload?.Region, selectedMaterialID,taskData?.ATTRIBUTE_3]);

  useEffect(() =>{
    if(withReference === "no"){
      setWithRefValues({});
      setSelectedMaterials(null);
      setInputState(null);
    }
  },[withReference])
  useEffect(() => {
    if (selectedMaterialID && allTabsData?.[MATERIAL_VIEWS.BASIC_DATA]) {
      if ((singlePayloadData[selectedMaterialID]?.headerData.refMaterialData || singlePayloadData?.OrgElementDefaultValues) && !singlePayloadData[selectedMaterialID]?.payloadData?.["Basic Data"]?.basic && !isrequestId) {
        setBasicAndAdditionalFromRef(singlePayloadData[selectedMaterialID]?.headerData?.refMaterialData);
      }
      if(singlePayloadData[selectedMaterialID]?.payloadData?.[MATERIAL_VIEWS.CLASSIFICATION]?.basic?.Classtype){
        fetchClassByType(singlePayloadData[selectedMaterialID]?.payloadData?.[MATERIAL_VIEWS.CLASSIFICATION]?.basic?.Classtype,dispatch);
      }
    }
  }, [selectedMaterialID,allTabsData]);

  useEffect(() => {
    if (rows?.length === 0) setAddButtonDisabled(false);
  }, [rows,openAddMatPopup]);
  useEffect(() => {
    if(selectedMaterials?.code){
      getSalesOrg(selectedMaterials?.code, "extended");
    } 
    else setDropDownData((prev) => ({ ...prev, ["Sales Org"]: [] }));
    setWithRefValues((prev) => ({ ...prev, [CHANGE_KEYS.SALES_ORG]: null }));
  }, [selectedMaterials]);
  useEffect(() => {
    if(withRefValues?.["Material Type"]?.code){
      getMaterialNo();
      setSelectedMaterials(null);
      setInputState(null);
    } 
  }, [withRefValues?.["Material Type"]?.code]);

  useEffect(() => {
    const dependents = ["Distribution Channel", "Plant"];
    dependents.forEach((depKey) => {
      setWithRefValues((prev) => ({ ...prev, [depKey]: "" }));
      if (dropDownData[depKey]) {
        setDropDownData((prev) => ({ ...prev, [depKey]: [] }));
      }
    });
  },[withRefValues?.["Sales Org"]?.code])
  useEffect(() => {
    const dependents = ["Storage Location", "Warehouse"];
    dependents.forEach((depKey) => {
      setWithRefValues((prev) => ({ ...prev, [depKey]: "" }));
      if (dropDownData[depKey]) {
        setDropDownData((prev) => ({ ...prev, [depKey]: [] }));
      }
    });
  },[withRefValues?.["Plant"]?.code]);
  useEffect(() => {
    if (selectedMaterialID && singlePayloadData[selectedMaterialID]?.headerData?.materialType) {
      let headerData = singlePayloadData[selectedMaterialID]?.headerData;
      if (matViews && matViews[headerData?.materialType?.code] && headerData?.views?.length < 2) {
        const views = initialPayload?.Region === "EUR" ? matViews[headerData?.materialType?.code]?.filter((view) => view !== MATERIAL_VIEWS.WAREHOUSE) || [] : matViews[headerData?.materialType?.code] || [];
        setAllViews(views);
        setSelectedViews(views);
        handleCellEdit({ id: selectedMaterialID, field: "views", value: views });
      }
    }
  }, [matViews, selectedMaterialID, singlePayloadData[selectedMaterialID]?.headerData?.materialType]);
  useEffect(() => {
    if(withRefValues[CHANGE_KEYS.SALES_ORG]){
      fetchDistChnlLookupData();
      setWithRefValues((prev) => ({ ...prev, [CHANGE_KEYS.DIST_CHNL]: null, [CHANGE_KEYS.PLANT]: null }));
    }
  }, [withRefValues[CHANGE_KEYS.SALES_ORG]]);
  const setBasicAndAdditionalFromRef = (referenceData) => {
    const basicViewData = referenceData?.copyPayload?.payloadData?.['Basic Data']?.['basic'] || {};
    const globalDefaults = singlePayloadData?.OrgElementDefaultValues?.[MATERIAL_VIEWS.BASIC_DATA]?.[MATERIAL_VIEWS.BASIC_DATA] || {};
    // Merge both sources, with referenceData taking priority
    const mergedKeys = new Set([
      ...Object.keys(globalDefaults),
      ...Object.keys(basicViewData),
    ]);

    mergedKeys.forEach((key) => {
      const valueFromReference = basicViewData?.[key] || "";
      const orgElementDefault = globalDefaults?.[key] || "";
      const keyValue = (key === 'Division')
        ? initialPayload?.Division
        : getKeysValue(key, valueFromReference, allTabsData['Basic Data'], orgElementDefault);

      dispatch(updateMaterialData({
        materialID: selectedMaterialID,
        viewID: 'Basic Data',
        itemID: 'basic',
        keyName: key,
        data: keyValue,
      }));
    });

    // dispatch(setMultipleMaterialPayloadKey({ materialID: selectedMaterialID, data: referenceData?.copyPayload?.payloadData?.["Basic Data"], keyName: "Basic Data" }));
    let uomData = referenceData?.copyPayload?.unitsOfMeasureData;
    if (uomData?.length) {
      let uomArr = [];
      uomData?.forEach((item) => {
        uomArr.push({ ...item, id: item?.id || uomArr.length + 1 });
      });
      dispatch(setUOmData({ materialID: selectedMaterialID, data: uomArr }));
    }
  };

  
  const getSalesOrg = (materialNo, type) => {
    const hSuccess = (data) => {
      setIsDropdownLoading(prev => ({ ...prev, "Sales Org": false }));
      if(data?.statusCode === API_CODE.STATUS_200){
        type === "notExtended" ? setDropDownData((prev) => ({ ...prev, ["Sales Org"]: data.body })) : setDropDownData((prev) => ({ ...prev, ["Sales Org"]: data?.body.length > 0 ? data.body : []  }));
      }
    };
    const hError = () => {
      setIsDropdownLoading(prev => ({ ...prev, "Sales Org": false }));
    };
    setIsDropdownLoading(prev => ({ ...prev, "Sales Org": true }));
    doAjax(`/${destination_MaterialMgmt}/data/${type === "notExtended" ? "getSalesOrgNotExtended" : "getSalesOrgExtended"}?materialNo=${materialNo}&region=${initialPayload?.Region}`, "get", hSuccess, hError);
  };

  const getPlant = (materialNo, type, salesOrg) => {
    setIsDropdownLoading(prev => ({ ...prev, "Plant": true }));
    const hSuccess = (data) => {
      setIsDropdownLoading(prev => ({ ...prev, "Plant": false }));
      if(data?.statusCode === API_CODE.STATUS_200){
        type === "notExtended" ? setDropDownData((prev) => ({ ...prev, ["Plant"]: data.body })) : setDropDownData((prev) => ({ ...prev, ["Plant"]: data?.body.length > 0 ? data.body : [] }));
          
      }
    };
    const hError = () => {
      setIsDropdownLoading(prev => ({ ...prev, "Plant": false }));
    };
    const salesOrgParam = salesOrg ? `&salesOrg=${salesOrg.code}` : '';
    doAjax(`/${destination_MaterialMgmt}/data/${type === "notExtended" ? "getPlantNotExtended" : "getPlantExtended"}?materialNo=${materialNo}&region=${initialPayload?.Region}${salesOrgParam}`, "get", hSuccess, hError);
  };
  const getWarehouse = (materialNo, type, plant) => {
    setIsDropdownLoading(prev => ({ ...prev, "Warehouse": true }));
    const hSuccess = (data) => {
      setIsDropdownLoading(prev => ({ ...prev, "Warehouse": false }));
      if(data?.statusCode === API_CODE.STATUS_200){
        type === "notExtended" ? setDropDownData((prev) => ({ ...prev, ["Warehouse"]: data.body })) : setDropDownData((prev) => ({ ...prev, ["Warehouse"]: data?.body.length > 0 ? data.body : [] }));
      }
    };
    const hError = () => {
      setIsDropdownLoading(prev => ({ ...prev, "Warehouse": false }));
    };
    const plantParam = plant ? `&plant=${plant.code}` : '';
    doAjax(`/${destination_MaterialMgmt}/data/${type === "notExtended" ? "getWarehouseNotExtended" : "getWarehouseExtended"}?materialNo=${materialNo}&region=${initialPayload?.Region}${plantParam}`, "get", hSuccess, hError);
  };

  const fetchStoreLocLookupData = (materialNo,pant,salesOrg) => {
    setIsDropdownLoading(prev => ({ ...prev, "Storage Location": true }));
    const successHandler = (data) => {
      setIsDropdownLoading(prev => ({ ...prev, "Storage Location": false }));
      if(data?.statusCode === API_CODE.STATUS_200){
      setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.STORAGE_LOC]: data.body || [] }));
      }
    };
    const errorHandler = (error) => {
      customError(error);
      setIsDropdownLoading(prev => ({ ...prev, "Storage Location": false }));
    };
    doAjax(`/${destination_MaterialMgmt}/data/getStorageLocationExtended?plant=${pant?.code}&materialNo=${materialNo}&region=${initialPayload?.Region}&salesOrg=${salesOrg?.code}`, "get", successHandler, errorHandler);
  };

  const fetchDistChnlLookupData = () => {
    setIsDropdownLoading(prev => ({ ...prev, "Distribution Channel": true }));
    const successHandler = (data) => {
      setIsDropdownLoading(prev => ({ ...prev, "Distribution Channel": false }));
      if(data?.statusCode === API_CODE.STATUS_200){
        setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.DIST_CHNL]: data.body && data?.body }));
      }
    };
    const errorHandler = (error) => {
      customError(error);
      setIsDropdownLoading(prev => ({ ...prev, "Distribution Channel": false }));};

    doAjax(`/${destination_MaterialMgmt}/data/getDistributionChannelExtended?materialNo=${selectedMaterials?.code}&salesOrg=${withRefValues[CHANGE_KEYS.SALES_ORG]?.code}`, "get", successHandler, errorHandler);
  };

  useEffect(() => {
    //["Plant", "Sales Organization", "Mrp Profile"].forEach(fetchOrgLookupData); #NOTE
    ["Mrp Profile"].forEach(fetchOrgLookupData);
    if (storedRows?.length === 0 && (requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD)) {
      setOpenAddMatPopup(true);
    }
    getSalesOrgForOrgRow();
    getPlantForOrgRow();
  }, []);
  
  useEffect(() => {
    latestOrgToUse.current = orgRow;
    const hasSalesOrgWithoutDc = orgRow.some(row => 
      row?.salesOrg?.code && !row?.dc?.value?.code
    );
    
    if (hasSalesOrgWithoutDc) {
      setIsOrgFilled(false);
    } else if (((orgRow[0]?.salesOrg?.code && orgRow[0]?.dc?.value?.code) || !singlePayloadData[selectedMaterialID]?.headerData?.views?.includes(MATERIAL_VIEWS?.SALES)) && orgRow[0]?.plant?.value?.code &&
       (orgRow[0]?.sloc?.value?.code || !singlePayloadData[selectedMaterialID]?.headerData?.views?.includes(MATERIAL_VIEWS?.STORAGE)) && (orgRow[0]?.warehouse?.value?.code || (initialPayload?.Region === "EUR" || !singlePayloadData[selectedMaterialID]?.headerData?.views?.includes(MATERIAL_VIEWS?.WAREHOUSE)))) {
      setIsOrgFilled(true);
    } else {
      setIsOrgFilled(false);
    }
  }, [orgRow]);
  useEffect(() => {
    setAddButtonDisabled(true); // Disable submit and add new if any field changes.
    setSubmitForApprovalDisabled(true);
  }, [singlePayloadData[selectedMaterialID]?.headerData, singlePayloadData[selectedMaterialID]?.payloadData]);
  const getSalesOrgForOrgRow = () => {
    if(initialPayload?.Region){
      const successHandler = (data) => {
        if(data?.statusCode === API_CODE.STATUS_200){
          setDropDownData((prev) => ({ ...prev, ['Sales Organization']: data.body ? data?.body : [] }));
        }
      };
      const errorHandler = (error) => {
        customError(error);
      };

      doAjax(`/${destination_MaterialMgmt}${END_POINTS.DATA.GET_SALES_ORG}?region=${initialPayload?.Region}`, "get", successHandler, errorHandler);
    }
  }
  const getPlantForOrgRow = () => {
    if(initialPayload?.Region){
      const successHandler = (data) => {
        if(data?.statusCode === API_CODE.STATUS_200){
          let rowOption = latestOrgToUse.current ? JSON.parse(JSON.stringify(latestOrgToUse.current)) : JSON.parse(JSON.stringify(orgRow));
          setDropDownData((prev) => ({ ...prev, ['PlantNotExtended']: data.body ? data?.body : [] }));
          setOrgRow(rowOption);
          latestOrgToUse.current = rowOption;
        }
      };
      const errorHandler = (error) => {
        customError(error);
      };

      doAjax(`/${destination_MaterialMgmt}${END_POINTS.DATA.GET_PLANT}?region=${initialPayload?.Region}`, "get", successHandler, errorHandler);
    }
  }
  const getslocForOrgRow = (plantValue,index) => {
    if(plantValue){
      setIsDropdownLoading(prev => ({ ...prev, [ORG_FIELDS.STORAGE_LOCATION]: { ...prev[ORG_FIELDS.STORAGE_LOCATION], [index]: true } }));
      const successHandler = (data) => {
        setIsDropdownLoading(prev => ({ ...prev, [ORG_FIELDS.STORAGE_LOCATION]: { ...prev[ORG_FIELDS.STORAGE_LOCATION], [index]: false } }));
        if(data?.statusCode === API_CODE.STATUS_200){
          let rowOption = latestOrgToUse.current ? JSON.parse(JSON.stringify(latestOrgToUse.current)) : JSON.parse(JSON.stringify(orgRow));
          if(index !== -1) rowOption[index].sloc.options = sortByCode(data.body);
          setOrgRow(rowOption);
          latestOrgToUse.current = rowOption;
        }
      };
      const errorHandler = (error) => {
        customError(error);
        setIsDropdownLoading(prev => ({ ...prev, [ORG_FIELDS.STORAGE_LOCATION]: { ...prev[ORG_FIELDS.STORAGE_LOCATION], [index]: false } }));
      };

      doAjax(`/${destination_MaterialMgmt}${END_POINTS.DATA.GET_STORAGE_LOCATION}?region=${initialPayload?.Region}&plant=${plantValue?.code}`, "get", successHandler, errorHandler);
    }
  }
  const getWarehouseForOrgRow = (plantValue,slocValue,index) => {
    if(plantValue){
      setIsDropdownLoading(prev => ({ ...prev, [ORG_FIELDS.WAREHOUSE]: { ...prev[ORG_FIELDS.WAREHOUSE], [index]: true } }));
      const successHandler = (data) => {
        setIsDropdownLoading(prev => ({ ...prev, [ORG_FIELDS.WAREHOUSE]: { ...prev[ORG_FIELDS.WAREHOUSE], [index]: false } }));
        if(data?.statusCode === API_CODE.STATUS_200){
          let rowOption = latestOrgToUse.current ? JSON.parse(JSON.stringify(latestOrgToUse.current)) : JSON.parse(JSON.stringify(orgRow));
          if(index !== -1) rowOption[index].warehouse.options = sortByCode(data.body);
          setOrgRow(rowOption);
          latestOrgToUse.current = rowOption;
        }
      };
      const errorHandler = (error) => {
        customError(error);
        setIsDropdownLoading(prev => ({ ...prev, [ORG_FIELDS.WAREHOUSE]: { ...prev[ORG_FIELDS.WAREHOUSE], [index]: false } }));
      };
      doAjax(`/${destination_MaterialMgmt}${END_POINTS.DATA.GET_WAREHOUSE_NO}?region=${initialPayload?.Region}&plant=${plantValue?.code}&storageLocation=${slocValue?.code}`, "get", successHandler, errorHandler);
    }
  }
  const checkDuplicateMatNo = (matNo, requestId = "",matDescription) => {
    return new Promise((resolve, reject) => {
      const payload = [
        {
          materialNo: matNo,
          requestNo: requestId || requestDetails?.requestId,
          materialDesc : matDescription || "",
        },
      ];
      const successHandler = (data) => {
        if (data?.body?.length) {
          showSnackbar(`Duplicate material number ${data.body[0].split("$^$")[0]}` + ` (${data.body[0].split("$^$")[1]})`, "error");
          resolve(true);
        } else {
          resolve(false);
        }
      };
      const errorHandler = (error) => {
        customError(error);
        resolve(false);
      };
      let count = 0;
      Object.keys(singlePayloadData).forEach((key, idx) => {
        if (key.includes("-") || /\d/.test(key)) {
          if (singlePayloadData[key]?.headerData?.materialNumber === matNo) count++;
        }
      });
       let DescriptionCount = 0;
        Object.keys(singlePayloadData).forEach((key) => {
          if (key.includes("-") || /\d/.test(key)) {
            if (singlePayloadData[key]?.headerData?.globalMaterialDescription === matDescription) DescriptionCount++;
          }
        });
        if (count > 1) {
          showSnackbar(`${ERROR_MESSAGES.DUPLICATE_MATERIAL}${matNo}`, "error");
          resolve(true);
        }
        else if(DescriptionCount > 1){
          showSnackbar(`${ERROR_MESSAGES.DUPLICATE_MATERIAL_DESCRIPTION}${matDescription}`, "error");
          resolve(true);
        }
       else {
        doAjax(`/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION?.MAT_NO_DUPLICATE_CHECK}`, "post", successHandler, errorHandler, payload);
      }
    });
  };

  const validateMaterials = async () => {
    let updatedRows = [...rows];
    let allValid = true;
    setBlurLoading(true);
    setLoaderMessage(LOADER_MESSAGES.VALIDATING_MATS);
    return new Promise(async (resolve, reject) => {
      for (let i = 0; i < rows?.length; i++) {
        const ele = rows[i];
        const { missingFields, viewType, isValid, plant = [] } = checkValidation(ele.id, ele?.orgData || [], false, false, false);
        setMissingValidationPlant(plant);
        setMandatoryFailedView(viewType);
        setActiveTab(viewType ? selectedViews?.indexOf(viewType) : 0 || 0);
        setActiveViewTab(viewType || MATERIAL_VIEWS.BASIC_DATA);
        if (!isValid) {
          allValid = false;
          updatedRows = updatedRows.map((row) => (row.id === ele.id ? { ...row, validated: false } : row));
          dispatch(setMaterialRows(updatedRows));
          if (missingFields) {
            setSelectedMaterialID(ele.id);
            setSelectedMaterialNumber(ele.materialNumber);
            if (typeof missingFields === 'object' && !Array.isArray(missingFields)) {
              const combinationMessages = Object.entries(missingFields).map(([combination, fields]) => {
                return `Combination ${combination}: ${fields.join(", ")}`;
              });
              showSnackbar(`Line No ${ele.lineNumber} : Please fill all the Mandatory fields in ${viewType ? viewType : ""}: ${combinationMessages.join(" | ")}`, "error",10000);
            } else {
              showSnackbar(`Line No ${ele.lineNumber} : Please fill all the Mandatory fields in ${viewType ? viewType : ""}: ${missingFields.join(", ")}`, "error",10000);
            }
          }
          break;
        } else {
          let isDuplicateMatNo = false;
          if (isValid && (!isrequestId || ele?.isMatNoChanged)) {
            isDuplicateMatNo = await checkDuplicateMatNo(ele.materialNumber, isrequestId, ele?.globalMaterialDescription);
          }
          if (isDuplicateMatNo) allValid = false;
          updatedRows = updatedRows?.map((row) => (row.id === ele.id ? { ...row, validated: isDuplicateMatNo ? false : true } : row));
          dispatch(setMaterialRows(updatedRows));
        }
      }
      allValid ? resolve(true) : reject();
      setBlurLoading(false);
      const allRowsValidated = checkIncludedAndValidated(updatedRows);
      setAddButtonDisabled(!allRowsValidated); // Enable Add button if all rows validated
      setSubmitForApprovalDisabled(!allRowsValidated);
      if(allValid){
        showSnackbar("Validation successful for all materials.", "success");
      }
    });
  };

  const getMrpDefaultValues = (orgSet) => {
    if (orgSet) {
      let defaultApiMrpCodes = JSON.parse(JSON.stringify(singlePayloadData?.[selectedMaterialID]?.headerData?.calledMrpCodes || [])) || [];
      orgSet.forEach((org, index) => {
        if (org?.mrpProfile?.code && !singlePayloadData?.[selectedMaterialID]?.headerData?.calledMrpCodes?.includes(org?.mrpProfile?.code)){
          getMrpProfileValues(org?.plant?.value?.code, org?.mrpProfile?.code, index);
          defaultApiMrpCodes.push(org?.mrpProfile?.code);
        }
      });
      dispatch(setMultipleMaterialHeaderKey({ materialID: selectedMaterialID, keyName: "calledMrpCodes", data: defaultApiMrpCodes }));
      const updatedRows = rows?.map((row) => (row.id === selectedMaterialID ? { ...row, "calledMrpCodes": defaultApiMrpCodes} : row));
      dispatch(setMaterialRows(updatedRows));
    }
  };

  const getMrpProfileValues = (comb, mrpProf, index) => {
    const payload = {
      mrpProfile: mrpProf,
    };
    const hSuccess = (data) => {
      if (data.body[0]) {
        let nonEmptyKeys = Object.keys(data?.body[0]).filter((key) => data?.body[0][key]);
        nonEmptyKeys.forEach((elem) => {
          setFieldValues(comb, elem, data?.body[0][elem], MATERIAL_VIEWS.MRP);
        });
      }
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(`/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION?.MRP_DEFAULT_VALUES}`, "post", hSuccess, hError, payload);
  };
  const setFieldValues = (comb, fieldName, fieldValue, tab) => {
    dispatch(
      updateMaterialData({
        materialID: selectedMaterialID || "",
        keyName: fieldName || "",
        data: fieldValue ?? null,
        viewID: tab,
        itemID: comb,
      })
    );
  };

  useEffect(() => {
    const allRowsValidated = checkIncludedAndValidated(storedRows);
    if ((allRowsValidated && storedRows?.length) || isreqBench || isrequestId) {
      props.setCompleted([true, true]);
      props?.setIsAttachmentTabEnabled(true);
    } else {
      props.setCompleted([true, false]);
      props?.setIsAttachmentTabEnabled(false);
    }
  }, [storedRows]);

  // Function to generate a random 4-digit number
  const nextLineNumber = lineNumberCounter + 10;
  // Function to add a new row with a random 4-digit line number
  const addRow = () => {
    const id = uuidv4();
    const newRow = {
      id, // Unique id for DataGrid rows
      included: true,
      lineNumber: nextLineNumber, // Generate random 4-digit line number
      industrySector: DEFAULT_VALUES?.DEFAULT_IND_SECTOR,
      materialType: payloadFields?.MatlType?.code ?? "",
      materialNumber: selectedMaterials?.code || "",
      globalMaterialDescription: "",
      views: [],
      orgData: "",
      validated: VALIDATION_STATUS.default,
      withReference: withReference,
    };
    dispatch(setMultipleMaterialHeader({ materialID: id, data: newRow }));
    dispatch(setMaterialRows([...rows, newRow]));
    setCounter(counter + 1); // Increment the counter for unique IDs
    setLineNumberCounter(nextLineNumber);
    setRowsAdded(true); // Set rowsAdded to true once a row is added
    setAddButtonDisabled(true);
    setSubmitForApprovalDisabled(true);
    setSelectedViews([fixedOption]);
    setSelectedMaterialID(id);
    handleMaterialType("");
  };

  const handleAddRow = () => {
    setOpenAddMatPopup(false);
    if (initialPayload?.RequestType === "Create") {
      addRow();
    } else if (initialPayload?.RequestType === "Change") {
      setOpenMessageDialog(true);
    }
  };

  const handleOk = () => {
    addRow();
  };

  const handleOkDialog = () => {
    handleDialogClose();
  };

  const getMaterialNo = (value = "", reset = true) => {
    const payload = {
      materialNo: value ?? "",
      salesOrg: regionBasedSalesOrgData?.uniqueSalesOrgList?.map((item) => item.code).join("$^$") || "",
      top: 500,
      skip: reset ? 0 : skip,
      matlType: withRefValues?.["Material Type"]?.code ?? "",
    };
    setIsDropdownLoading(prev => ({ ...prev, "Material No": true }));
    const hSuccess = (data) => {
      if(data?.statusCode === API_CODE.STATUS_200 && data?.body){
        if (reset) setMaterialOptions(data?.body);
        else setMaterialOptions((prevOptions) => [...prevOptions, ...data?.body]);
      }
      setIsLoading(false);
      setIsDropdownLoading(prev => ({ ...prev, "Material No": false }));
    };

    const hError = () => {
      setIsLoading(false);
      setIsDropdownLoading(prev => ({ ...prev, "Material No": false }));
    };

    setIsLoading(true);
    doAjax(`/${destination_MaterialMgmt}/data/getSearchParamsMaterialNo`, "post", hSuccess, hError, payload);
  };

  const getMaterialNumValidation = (materialTypeValue) => {
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        setMaterialNumberValidationData(data?.body);
      }
    };
    const hError = (error) => {
      console.error(error, "while fetching the validation data of material number");
    };
    doAjax(`/${destination_MaterialMgmt}/data/getNumberRangeForMaterialType?materialType=${materialTypeValue?.code}`, "get", hSuccess, hError);
  };

  const isInternalRangeAllowed = materialNumberValidationData?.[0]?.External === "X";
  const isExternalRangeAllowed = materialNumberValidationData?.[1]?.External === "X" ? true : false;
  const isExtWOCheckAllowed = materialNumberValidationData?.some((item) => item.ExtNAwock === "X");

  const generateUniqueMessages = (materialNumberValidationData) => {
    const uniqueMessages = new Set();
    let notAllowedMessage = null;

    materialNumberValidationData?.forEach((item) => {
      if (item.External === "X" && item.ExtNAwock === "X") {
        uniqueMessages.add(`External Number Range: Allowed (${item.FromNumber}-${item.ToNumber})`);
        uniqueMessages.add(`Ext W/O Check: Allowed`);
      } else if (item.External !== "X" && item.ExtNAwock === "X") {
        uniqueMessages.add(`Internal Number Range: Allowed`);
        uniqueMessages.add(`Ext W/O Check: Allowed`);
      } else if (item.External === "X" && item.ExtNAwock !== "X") {
        uniqueMessages.add(`External Number Range: Allowed (${item.FromNumber}-${item.ToNumber})`);
        notAllowedMessage = `Ext W/O Check: Not Allowed`;
      } else if (item.External !== "X" && item.ExtNAwock !== "X") {
        uniqueMessages.add(`Internal Number Range: Allowed`);
        notAllowedMessage = `Ext W/O Check: Not Allowed`;
      }
    });

    const messageArray = Array.from(uniqueMessages);
    if (notAllowedMessage) {
      messageArray.push(notAllowedMessage);
    }


    return messageArray.map((message, index) => (
      <div key={index}>
        <Typography>{message}</Typography>
      </div>
    ));
  };
  const tooltipContent = generateUniqueMessages(materialNumberValidationData);

  function handleMaterialType(materialType) {
    const region = initialPayload?.Region || REGION_CODE.US;
    const materialTypeExists = allMaterialFieldConfigDT.some(obj => 
      obj[region] && obj[region][materialType]
    );
    if (!materialTypeExists && materialType) {
      fetchMaterialFieldConfig(materialType,region);
    } else {
      if (!materialType) {
        dispatch(updateAllTabsData({}));
      } else {
        const gotMatConfigFieldFromRedux = allMaterialFieldConfigDT?.find(
          (materialTypes) => materialTypes?.[region] && materialTypes?.[region][materialType]
        );
        gotMatConfigFieldFromRedux && dispatch(updateAllTabsData(gotMatConfigFieldFromRedux[region][materialType]?.allfields));
      }
    }

    if (materialType) {
      fetchValuationClassData(materialType);
    }
  }

  // Function to handle cell edits
  const handleCellEdit = (params) => {
    const { id, field, value } = params;
    let updatedRows = rows.map((row) => (row.id === id ? { ...row, [field]: value } : row));
    setCurrentRowData({
      ...currentRowData,
      [field]: value,
    });
    if (field === MATERIAL_TABLE.MATERIALTYPE) {
      getMaterialNumValidation(value);
      setSelectedViews([fixedOption]);
      setOrgData([orgRowFields]);
      dispatch(setMultipleMaterialHeaderKey({ materialID: id, keyName: "views", data: [fixedOption] }));
      dispatch(setMultipleMaterialHeaderKey({ materialID: id, keyName: "orgData", data: "" }));
      updatedRows = updatedRows.map((row) => (row.id === id ? { ...row, "orgData": ''} : row));
      handleMaterialType(value?.code);
    }
    if (field === MATERIAL_TABLE.INCLUDED) {
      if (checkIncludedAndValidated(updatedRows)) {
        setAddButtonDisabled(false);
        setSubmitForApprovalDisabled(false);
      } else {
        setAddButtonDisabled(true);
        setSubmitForApprovalDisabled(true);
      }
    }
    if (field === MATERIAL_TABLE.VIEWS) {
      setAddButtonDisabled(true);
      setSubmitForApprovalDisabled(true);
    }
    setRows(updatedRows);
    dispatch(setMultipleMaterialHeaderKey({ materialID: id, keyName: field, data: value }));
    dispatch(setMaterialRows(updatedRows));
  };

  const handleRowSelection = (params) => {
    setSelectedMaterialID(params.row.id);
    setSelectedMaterialNumber(params.row.materialNumber);
    handleMaterialType(params?.row?.materialType?.code);
    setAllViews(initialPayload?.Region === "EUR" ? (matViews[params?.row?.materialType?.code]?.filter(view => view !== MATERIAL_VIEWS.WAREHOUSE) || []) : matViews[params?.row?.materialType?.code] || []);
    
    setSelectedViews(params?.row?.views?.length ? params.row?.views : [fixedOption]);
    setOrgRow(params?.row?.orgData?.length ? params.row?.orgData : [orgRowFields]);
    setActiveTab(0);
    setActiveViewTab("Basic Data");
  };

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setInputState({ code: "", desc: "" });
    setOpenMessageDialog(false);
  };

  const handleDialogClickOpen = () => {
    setOpenDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
  };

  const viewsClose = (event, reason) => {
    if (reason === "backdropClick" || reason === "escapeKeyDown") {
      return;
    }
    setOpenOrgData(false);
  };
  const handleSelectAll = () => setSelectedViews(allViews || [fixedOption]);
  const AddCopiedMaterial = () => {
    setOpenAddMatPopup(false);
    if (withReference === "yes") {
      if (selectedMatLines?.length) {
        let newRows = [...rows];
        selectedMatLines?.forEach((mat) => {
          const id = uuidv4();
          let newRow = JSON.parse(JSON.stringify(mat));
          if(newRow?.refMaterialData){
            delete newRow.refMaterialData;
          }
          let payloadData = JSON.parse(JSON.stringify(singlePayloadData?.[mat.id]?.payloadData));
          newRow.id = id;
          newRow.lineNumber = nextLineNumber;
          newRow.globalMaterialDescription = "";
          newRow.materialNumber = "";
          newRow.validated = VALIDATION_STATUS.default;
          dispatch(setMultipleMaterialHeader({ materialID: id, data: newRow, payloadData: payloadData }));
          newRows.push(newRow);
          setRows(newRows);
          dispatch(setMaterialRows(newRows));
          setCounter(counter + 1); // Increment the counter for unique IDs
          setLineNumberCounter(nextLineNumber);
          setRowsAdded(true); // Set rowsAdded to true once a row is added
          setAddButtonDisabled(true);
          setSubmitForApprovalDisabled(true);
          let uomData = singlePayloadData?.[mat.id]?.unitsOfMeasureData;
          if (uomData?.length) {
            let uomArr = [];
            uomData?.forEach((item) => {
              uomArr.push({ ...item, eanUpc: "", eanCategory: "",length:'',width:'',height:'',
                volume:'',grossWeight:'',netWeight:'',
                eanCategory : initialPayload?.Region ===  REGION_CODE?.US ? item?.EanCat : '',
                eanUpc : ((item?.EanCat === 'MB') && initialPayload?.Region === REGION_CODE?.US) ? "" : initialPayload?.Region === REGION_CODE?.EUR ? '' : item?.EanUpc,
                id: item?.id || uomArr.length + 1 });
            });
            dispatch(setUOmData({ materialID: id, data: uomArr }));
          }
        });
        setSelectedMatLines([]);
      } else if (selectedMaterials) {
        // handleAddRow();
        getCopiedMaterial();
      }
    } else {
      handleAddRow();
    }
  };
  const getCopiedMaterial = () => {
    setBlurLoading(true)
    let payload = {
      "material": selectedMaterials?.code,
      "wareHouseNumber": withRefValues?.Warehouse?.code,
      "storageLocation": withRefValues?.['Storage Location']?.code,
      "salesOrg": withRefValues?.['Sales Org']?.code,
      "distributionChannel": withRefValues?.['Distribution Channel']?.code,
      "valArea": withRefValues?.Plant?.code,
      "plant": withRefValues?.Plant?.code
    }
    const successHandler = (data) => {
      setBlurLoading(false);
      setWithRefValues({});
      if(data?.body[0]){
        transformResponseForCreateRef(data?.body,initialPayload);
        let newRows = [...rows];
        const id = uuidv4();
        let newRow = {};
        newRow.id = id;
        newRow.included = true;
        newRow.lineNumber = nextLineNumber;
        newRow.globalMaterialDescription = "";
        newRow.materialType = {code : data.body[0]?.MatlType || "", desc: allDropDownData?.["MatlType"]?.find(item => item.code === data.body[0]?.MatlType)?.desc || ""};
        newRow.industrySector = {code : data.body[0]?.IndSector || "", desc:allDropDownData?.["IndSector"]?.find(item => item.code === data.body[0]?.IndSector)?.desc || ""};
        newRow.materialNumber = "";
        (newRow.views = (data.body[0]?.Views || "")
          .split(",")
          .map(view => view.trim() === 'Storage' ? MATERIAL_VIEWS.STORAGE : view.trim())
          ?.filter((view) => !EXCLUDED_VIEWS.includes(view)));
        if(initialPayload?.Region === REGION_CODE?.EUR){
          newRow.views = newRow?.views?.filter(view => view !== MATERIAL_VIEWS.WAREHOUSE) || [];
        }
        newRow.validated = VALIDATION_STATUS.default;
        newRow.withReference = withReference;
        newRow.refMaterialData = transformResponseForCreateRef(data.body,initialPayload);
        dispatch(setMultipleMaterialHeader({ materialID: id, data: newRow, payloadData: {} }));
        newRows.push(newRow);
        setRows(newRows);
        dispatch(setMaterialRows(newRows));
        setSelectedViews(newRow?.views);
        setCounter(counter + 1); // Increment the counter for unique IDs
        setLineNumberCounter(nextLineNumber);
        setRowsAdded(true); // Set rowsAdded to true once a row is added
        setAddButtonDisabled(true);
        setSubmitForApprovalDisabled(true);
        handleMaterialType(data.body[0]?.MatlType);
        setSelectedMaterialID(id);
      } else {
        setBlurLoading(false);
        showSnackbar(ERROR_MESSAGES.NO_MATERIAL_FOUND, "warning");
        setOpenAddMatPopup(true);
      }
    };
    const errorHandler = (error) => {
      customError(error);
      setBlurLoading(false);
      setOpenAddMatPopup(true);
    };
    setWithRefValues({});
    setSelectedMaterials(null);
    setInputState(null);
    doAjax(`/${destination_MaterialMgmt}${END_POINTS.DATA?.GET_COPY_MATERIAL}`, "post", successHandler, errorHandler, payload);
  };

  const disableCheck = !ENABLE_STATUSES.includes(props?.requestStatus) || (isrequestId && !isreqBench);

  const checkUserRoles = (userRoles) => ({
    hasFertRole: userRoles.includes("CA-MDG-MRKTNG-FERT-EUR"),
    hasSalesRole: userRoles.includes("CA-MDG-MRKTNG-SALES-EUR"),
  });

  const isMaterialTypeDisabled = (option, rows, currentRowId) => {
    const { hasFertRole, hasSalesRole } = checkUserRoles(userRoles);
    if (hasFertRole && !hasSalesRole && initialPayload?.Region === REGION_CODE.EUR) {
      if (option?.code === "FERT") return false;
      else return true;
    }

    if (!hasFertRole && hasSalesRole && initialPayload?.Region ===  REGION_CODE.EUR) {
      if (option?.code === "FERT") return true;
      else return false;
    }

    if (hasFertRole && hasSalesRole && initialPayload?.Region ===  REGION_CODE.EUR) {
      const firstRow = rows[0];
      if (currentRowId === firstRow?.id) {
        return false;
      }

      const firstRowMaterialType = firstRow?.materialType?.code;
      if (firstRowMaterialType === "FERT") {
        if (option?.code === "FERT") return false;
        else return true;
      } else if (firstRowMaterialType) {
        if (option?.code === "FERT") return true;
        else return false;
      }
    }

    return false;
  };
  const warnUser = (newValue,paramId) =>{
    Swal.fire({
      title: t('Are you sure?'),
      text: t("Changing the material type will reset all the field values entered!"),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: colors.primary?.main,  // OK button
      cancelButtonColor: colors.error.red,      // Cancel button
      confirmButtonText: t('Yes, do it!'),
      cancelButtonText: t('Cancel'),
      reverseButtons: true
    }).then((result) => {
      if (result.isConfirmed) {
        setSelectedViews([]);
        handleCellEdit({ id: rowId, field: MATERIAL_TABLE.VIEWS, value: newValue });
        dispatch(resetOnMatTypeChange({materialId:paramId}));
        handleCellEdit({
          id: paramId,
          field: "materialType",
          value: newValue,
        });
        // setOrgData(orgData)
      }
    });
  }

  const columns = [
    {
      field: "included",
      headerName: t("Included"),
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => <Checkbox checked={params.row.included} disabled={disableCheck} onChange={(e) => handleCellEdit({ id: params.row.id, field: "included", value: e.target.checked })} />,
    },
    { field: "lineNumber", headerName: t("Line Number"), flex:0.5, editable: true, align: "center", headerAlign: "center" },
    {
      field: "industrySector",
      headerName: t("Industry Sector"),
      flex:0.7,
      align: "center",
      headerAlign: "center",
      ...(requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD
        ? {
            renderCell: (params) => {
              return (
                <SingleSelectDropdown
                  options={allDropDownData?.["IndSector"] || []}
                  value={params.row.industrySector || DEFAULT_VALUES?.DEFAULT_IND_SECTOR}
                  onChange={(newValue) =>
                    handleCellEdit({
                      id: params.row.id,
                      field: "industrySector",
                      value: newValue,
                    })
                  }
                  placeholder={t("Select Industry Sector")}
                  disabled={disableCheck}
                  minWidth="90%"
                  listWidth={235}
                />
              );
            },
          }
        : {
            editable: false,
            renderCell: (params) => {
              return singlePayloadData?.[params.row.id]?.headerData?.industrySector || "";
            },
          }), // Default configuration for non-Create requestType
    },
    {
      field: "materialType",
      headerName: t("Material Type"),
      flex:0.7,
      align: "center",
      headerAlign: "center",
      renderHeader: () => (
        <span>
          {t("Material Type")}<span style={{ color: "red" }}>*</span>
        </span>
      ),
      ...(requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD
        ? {
            renderCell: (params) => {
              return (
                <SingleSelectDropdown
                  options={MATERIAL_TYPE_DRODOWN|| []}
                  value={params.row.materialType}
                  onChange={(newValue) => {
                    if(params.row.materialType){
                      warnUser(newValue,params.row.id);
                    }
                    else{
                      handleCellEdit({
                        id: params.row.id,
                        field: "materialType",
                        value: newValue,
                      });
                    }
                    
                  }}
                  placeholder={t("Select Material Type")}
                  disabled={disableCheck}
                  minWidth="90%"
                  listWidth={235}
                  isOptionDisabled={(option) => isMaterialTypeDisabled(option, rows, params.row.id)}
                />
              );
            },
          }
        : {
            editable: false,
            renderCell: (params) => {
              return singlePayloadData?.[params.row.id]?.headerData?.materialType || "";
            },
          }),
    },
    {
      field: "materialNumber",
      headerName: t("Material Number"),
      flex:0.7,
      editable: false,
      align: "center",
      headerAlign: "center",
      renderHeader: () => (
        <span>
          {t("Material Number")}<span style={{ color: "red" }}>*</span>
        </span>
      ),
      renderCell: (params) => {
        const [localValues, setLocalValues] = useState({ [params?.row?.id]: params.row.materialNumber });
        const rowId = params.row.id;
        let debounceSnackbarTimeout = null;
        const handleChangeValueInTable = (event) => {
          const inputValue = event.target.value.toUpperCase();
          if(inputValue?.length >= matNumberMaxLength) {
            if (!debounceSnackbarTimeout) {
              debounceSnackbarTimeout = setTimeout(() => {
                showSnackbar(`Material Number cannot exceed ${matNumberMaxLength} characters`, "error");
                debounceSnackbarTimeout = null;
              }, 500); // show after 500ms once
            }
          }
          const cleanedValue = inputValue.replace(/[^A-Z0-9-]/g, "").slice(0, matNumberMaxLength); // ✅ Only allow A-Z, 0-9 (no special characters) with max length of 18.
          setLocalValues((prev) => ({
            ...prev,
            [rowId]: cleanedValue,
          }));
          handleCellEdit({
            id: params.row.id,
            field: "materialNumber",
            value: cleanedValue,
          });
          const updatedRows = rows.map((row) => (row.id === params.row.id ? { ...row, isMatNoChanged: true, materialNumber: cleanedValue } : row));
          dispatch(setMaterialRows(updatedRows));
        };
        const isMaxLengthReached = (localValues[rowId]?.length || 0) === matNumberMaxLength;
        return (
          <Tooltip title={tooltipContent} arrow>
            {initialPayload?.RequestType === REQUEST_TYPE.CREATE || initialPayload?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ? (
              <TextField
                fullWidth
                placeholder={t("ENTER MATERIAL NUMBER")}
                variant="outlined"
                size="small"
                name="material number"
                value={localValues[rowId] || ""}
                onChange={(event) => {
                  handleChangeValueInTable(event);
                }}
                error={isMaxLengthReached}
                sx={{
                  "& .MuiInputBase-root.Mui-disabled": {
                    "& > input": {
                      WebkitTextFillColor: colors.black.dark,
                      color: colors.black.dark,
                    },
                  },
                }}
                disabled={disableCheck}
              />
            ) : (
              params.row.materialNumber
            )}
          </Tooltip>
        );
      },
    },
    {
      field: "globalMaterialDescription",
      flex: 0.7,
      headerName: t("Material Description"),
      renderHeader: () => (
        <span>
          {t("Material Description")}<span style={{ color: "red" }}>*</span>
        </span>
      ),
      renderCell: (params) => {
        const [localValues, setLocalValues] = useState({ [params?.row?.id]: params.row.globalMaterialDescription });
        const rowId = params.row.id;
        let debounceSnackbarTimeout = null;
        const handleChangeValueInTable = (event) => {
          const inputValue = event.target.value.toUpperCase();
          if(inputValue?.length > descriptionMaxLength) {
            if (!debounceSnackbarTimeout) {
              debounceSnackbarTimeout = setTimeout(() => {
                showSnackbar(`Material Description cannot exceed ${descriptionMaxLength} characters`, "error");
                debounceSnackbarTimeout = null;
              }, 500); // show after 500ms once
            }
          }
          const cleanedValue = inputValue.replace(/[^A-Z0-9-]/g, "").slice(0, descriptionMaxLength); // Only allow A-Z, 0-9, and hyphen
          
          setLocalValues((prev) => ({
            ...prev,
            [rowId]: cleanedValue,
          }));
          
          handleCellEdit({
            id: params.row.id,
            field: "globalMaterialDescription",
            value: cleanedValue,
          });
        };
        
        const isMaxLengthReached = (localValues[rowId]?.length || 0) === descriptionMaxLength;
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <Tooltip title={localValues[rowId] || ""} arrow placement="top">
              <TextField
                fullWidth
                variant="outlined"
                size="small"
                placeholder={t("ENTER MATERIAL DESCRIPTION")}
                value={localValues[rowId] || ""}
                onChange={handleChangeValueInTable}
                // inputProps={{ maxLength: 40 }}
                error={isMaxLengthReached}
                sx={{ 
                  flexGrow: 1,
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: isMaxLengthReached ? colors.error.dark : undefined,
                    },
                    '&:hover fieldset': {
                      borderColor: isMaxLengthReached ? colors.error.dark : undefined,
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: isMaxLengthReached ? colors.error.dark : undefined,
                    },
                  },
                }}
              />
            </Tooltip>
          </Box>
        );
      },
      align: "center",
      headerAlign: "center",
      editable: false,
    },
    {
      ...(requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD
        ? {
            field: "views",
            headerName: "", // No column heading
            flex:0.6,
            align: "center", // Center-align cell content
            headerAlign: "center", // Center-align header
            renderCell: (params) => (
              <>
                <Button
                  variant="contained"
                  size="small"
                  disabled={!params?.row?.materialType}
                  onClick={() => {
                    setOpenViews(true), setRowId(params.row.id), setSelectedViews(params?.row?.views?.length ? params.row?.views : [fixedOption]);
                  }}
                >
                  {t("Views")}
                </Button>
                <Button
                  variant="contained"
                  disabled={!(params?.row?.views?.length > 1)}
                  size="small"
                  sx={{ marginLeft: "4px" }}
                  onClick={() => {
                    setOpenOrgData(true), setRowId(params.row.id), setOrgRow(params?.row?.orgData?.length ? params.row?.orgData : [orgRowFields]);
                  }}
                >
                  {t("ORG Data")}
                </Button>
              </>
            ),
          }
        : {}),
    },
    {
      field: "action",
      headerName: t("Action"),
      flex:0.5,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        let validateStatus = getValidationStatus(params?.row);
        const handleValidate = async (e) => {
          e.stopPropagation();
          if(params?.row?.views?.length > 1 && (!params?.row?.orgData || params?.row?.orgData?.length === 0)){
            showSnackbar(t(DOC_SNACKBAR_MESSAGES?.FILES?.MISSING_ORG_DATA), "error", 10000);
            return;
          }
          const { missingFields, viewType, isValid, plant = [] } = checkValidation(params.row.id, params?.row?.orgData || [], isInternalRangeAllowed, isExternalRangeAllowed, isExtWOCheckAllowed);
          setMissingValidationPlant(plant);
          setMandatoryFailedView(viewType);
          setActiveTab(viewType ? selectedViews?.indexOf(viewType) : 0 || 0);
          setActiveViewTab(viewType || MATERIAL_VIEWS.BASIC_DATA);
          if (missingFields) {
            if (typeof missingFields === 'object' && !Array.isArray(missingFields)) {
              const combinationMessages = Object.entries(missingFields).map(([combination, fields]) => {
                return `Combination ${combination}: ${fields.join(", ")}`;
              });
              showSnackbar(`${t("Line No")} ${params.row.lineNumber} : ${t("Please fill all the Mandatory fields in")} ${viewType ? viewType : ""}: ${combinationMessages.join(" | ")}`, "error",10000);
            } else {
              showSnackbar(`${t("Line No")} ${params.row.lineNumber} : ${t("Please fill all the Mandatory fields in")} ${viewType ? viewType : ""}: ${missingFields.join(", ")}`, "error",10000);
            }
          }
          let isDuplicateMatNo = false;
          if (isValid && (!isrequestId || params.row?.isMatNoChanged)) {
            isDuplicateMatNo = await checkDuplicateMatNo(params.row.materialNumber, isrequestId, params?.row?.globalMaterialDescription);
          }
          validateStatus = isValid && !isDuplicateMatNo ? "success" : "error";
          if(validateStatus === "success"){
            showSnackbar("Validation successful", "success");
          }
          const updatedRows = rows.map((row) => (row.id === params.row.id ? { ...row, validated: isValid && !isDuplicateMatNo } : row));
          dispatch(setMaterialRows(updatedRows));
          const allRowsValidated = checkIncludedAndValidated(updatedRows);
          setAddButtonDisabled(!allRowsValidated); // Enable Add button if all rows validated
          setSubmitForApprovalDisabled(!allRowsValidated);
        };
        return (
          <Stack direction="row" alignItems="center" sx={{ marginLeft: "0.5rem", magrinRight: "0.5rem" }} spacing={0.5}>
            {/* {!disableCheck && ( */}
            <Tooltip title={validateStatus === "success" ? "Validated Successfully" : validateStatus === "error" ? t("Validation Failed") : t("Click to Validate")}>
              <IconButton onClick={handleValidate} 
              color={validateStatus === "success" ? "success" : validateStatus === "error" ? "error" : "default"}>
                {validateStatus === "error" ? <CancelOutlinedIcon /> : <TaskAltIcon />}
              </IconButton>
            </Tooltip>
            {/* )} */}
            {!disableCheck && (
              <Tooltip title={t("Delete Row")}>
                <IconButton
                  onClick={() => {
                    setIsDeleteDialogVisible({ ...isDeleteDialogVisible, data: params, isVisible: true });
                  }}
                  color="error"
                >
                  <DeleteOutlineOutlinedIcon />
                </IconButton>
              </Tooltip>
            )}
          </Stack>
        );
      },
    },
  ];

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    setActiveViewTab(event?.target?.id === "AdditionalKey" ? "Additional Data" : selectedViews?.[newValue]);
  };

  const fetchOrgLookupData = (field) => {
    const endpoints = {
      "Sales Org": "/getSalesOrg",
      "Mrp Profile": "/getMRPProfile",
    };
    const successHandler = (data) => {
      const sortedData = sortByCode(data.body);
      setDropDownData((prev) => ({ ...prev, [field]: sortedData }));
    };
    const errorHandler = (error) => console.error(error);

    doAjax(`/${destination_MaterialMgmt}/data${endpoints[field]}`, "get", successHandler, errorHandler);
  };

  const addOtherViews = (plantIds) => {
    addMissingViews(plantIds, selectedViews, selectedMaterialPayload, selectedMaterialID, orgRow, dispatch, pushMaterialDisplayData);
  };
  const addViewForRference = (plantIds) => {
    addViewForWithRefrence(plantIds, selectedViews, selectedMaterialPayload, selectedMaterialID, orgRow, dispatch, pushMaterialDisplayData, MATERIAL_VIEWS);
  }
  const handleAccordionChange = (accordionIndex, accData, view) => (event, isExpanded) => {
    let payload = {};
    let url = "";
    let plantData = "";
    if (view === "Purchasing" || view === "Costing") {
      payload = {
        materialNo: accData?.Material,
        plant: accData?.Plant,
      };
      plantData = accData?.Plant;
      url = `/${destination_MaterialMgmt}/data/displayLimitedPlantData`;
    } else if (view === "Accounting") {
      payload = {
        materialNo: accData?.Material,
        valArea: accData?.ValArea,
      };
      plantData = accData?.ValArea;
      url = `/${destination_MaterialMgmt}/data/displayLimitedAccountingData`;
    } else if (view === "Sales") {
      payload = {
        materialNo: accData?.Material,
        salesOrg: accData?.SalesOrg,
        distChnl: accData?.DistrChan,
      };
      plantData = `${accData?.SalesOrg}-${accData?.DistrChan}`;
      url = `/${destination_MaterialMgmt}/data/displayLimitedSalesData`;
    }

    const hSuccess = (data) => {
      if (view === "Purchasing" || view === "Costing") {
        dispatch(
          pushMaterialDisplayData({
            materialID: selectedMaterialID,
            viewID: view,
            itemID: accData?.Plant,
            data: data?.body?.SpecificPlantDataViewDto[0],
          })
        );
      } else if (view === "Accounting") {
        dispatch(
          pushMaterialDisplayData({
            materialID: selectedMaterialID,
            viewID: view,
            itemID: accData?.ValArea,
            data: data?.body?.SpecificAccountingDataViewDto[0],
          })
        );
      } else if (view === "Sales") {
        dispatch(
          pushMaterialDisplayData({
            materialID: selectedMaterialID,
            viewID: view,
            itemID: `${accData?.SalesOrg}-${accData?.DistrChan}`,
            data: data?.body?.SpecificSalesDataViewDto[0],
          })
        );
      }
    };

    const hError = () => {};

    const shouldCallApi = !singlePayloadData?.[selectedMaterialID]?.payloadData?.[view]?.[plantData];

    if (shouldCallApi) {
      doAjax(url, "post", hSuccess, hError, payload);
    }

    setExpandedAccordion(isExpanded ? accordionIndex : null); // Toggle accordion expansion
  };

  const renderTabContent = () => {
    if (allTabsData && activeViewTab && (allTabsData[activeViewTab] || activeViewTab === "Additional Data")) {
      if (activeViewTab === "Additional Data") {
        return [<AdditionalData disableCheck={isreqBench && !ENABLE_STATUSES.includes(props?.requestStatus)} materialID={selectedMaterialID} selectedMaterialNumber={selectedMaterialNumber} />];
      }
      return [<GenericTabs disabled={isreqBench && !ENABLE_STATUSES.includes(props?.requestStatus)} selectedMaterialNumber={selectedMaterialNumber} materialID={selectedMaterialID} basicData={basicData} setBasicData={setBasicData} dropDownData={dropDownData} basicDataTabDetails={allTabsData[activeViewTab]} allTabsData={allTabsData} activeViewTab={activeViewTab} selectedViews={selectedViews} handleAccordionClick={handleAccordionChange} missingValidationPlant={missingValidationPlant} isDisplay={isrequestId || isreqBench} mandatoryFailedView={mandatoryFailedView} />];
    } else {
      return <></>;
    }
  };

  const handleMatInputChange = (e) => {
    const inputValue = e?.target?.value?.toUpperCase() || "";
    setInputState(null);
    setSkip(0);
    if (timerId) {
      clearTimeout(timerId);
    }

    const newTimerId = setTimeout(() => {
      getMaterialNo(inputValue, true);
    }, 500);

    setTimerId(newTimerId);
  };

  const handleSalesOrgWithREF = (key, newValue) => {
    const materialNo = selectedMaterials?.code;
    const type = withReference === "yes" ? "extended" : "notExtended";
    setWithRefValues((prev) => ({ ...prev, [key]: newValue }));
    if (key === "Sales Org" && newValue) {
      getPlant(materialNo,type, newValue);
    }
    else if (key === "Plant" && newValue) {
      getWarehouse(materialNo, type, newValue);
      fetchStoreLocLookupData(materialNo,newValue,withRefValues["Sales Org"]);
    }    
  }

  // Update form data based on field selection
  const handleSalesOrgSel = (fieldName, selectedValue, index) => {
    //setFormData((prev) => ({ ...prev, [fieldName]: selectedValue }));

    // If Sales Organization changes, update dependent dropdowns
    if (fieldName === "Sales Organization") {
      if (selectedValue) {
        setOrgRow((prev) => prev.map((row, i) => (i === index ? { ...row, salesOrg: selectedValue } : row)));
        salesOrgDependentOrgElement(selectedValue, index).then((resp) => {
          // filterOptions(fieldName, selectedValue, index);
        });
      }
      else{
        setOrgRow((prev) => prev.map((row, i) => (i === index ? { ...row, salesOrg: null } : row)));
      }
    }
  };
  const salesOrgDependentOrgElement = (salesOrg, index, org = "", row = "") => {
    return new Promise((resolve, reject) => {
      // if (displayedFields.includes("Distribution Channel")) {
      //let rowOption = orgRow;
      setIsDropdownLoading(prev => ({ ...prev, "Distribution Channel": { ...prev["Distribution Channel"], [index]: true } }));
      let payload = {
        salesOrg: salesOrg?.code,
      };
      const hSuccess = (data) => {
        setIsDropdownLoading(prev => ({ ...prev, "Distribution Channel": { ...prev["Distribution Channel"], [index]: false } }));
        let rowOption = org ? JSON.parse(JSON.stringify(org)) : JSON.parse(JSON.stringify(latestOrgToUse.current));
        //rowOption[index].salesOrg = salesOrg;
        rowOption[index].dc.options = sortByCode(data.body);
        setOrgRow(rowOption);
        latestOrgToUse.current = rowOption;
        if (row) {
          dispatch(setMultipleMaterialHeaderKey({ materialID: row?.id, keyName: "orgData", data: rowOption }));
          let matRow = rows?.length || [JSON.parse(JSON.stringify(row))];
          let matIndex = matRow.findIndex((ele) => ele.id === row?.id);
          matRow[matIndex].orgData = rowOption;
          dispatch(setMaterialRows(matRow));
          resolve({ org: rowOption, material: matRow[matIndex] });
        } else {
          resolve("");
          setIsDropdownLoading(prev => ({ ...prev, "Distribution Channel": { ...prev["Distribution Channel"], [index]: false } }));
        }
      };
      const hError = (error) => {
        console.error(error)
        setIsDropdownLoading(prev => ({ ...prev, "Distribution Channel": { ...prev["Distribution Channel"], [index]: false } }));
      };

      doAjax(`/${destination_MaterialMgmt}/data/getDistrChan`, "post", hSuccess, hError, payload);
      // }
    });
  };

  const setDc = (newValue, index) => {
    let org = JSON.parse(JSON.stringify(orgRow));
    org[index].dc.value = newValue;
    setOrgRow(org);
  };
  const removeOrgRow = (index) => {
    let org = JSON.parse(JSON.stringify(orgRow));
    org.splice(index, 1);
    setOrgRow(org);
  };
  const setPlant = (value, index) => {
    let org = JSON.parse(JSON.stringify(orgRow));
    org[index].plant.value = value;
    org[index].sloc.value = {};
    org[index].sloc.options = [];
    org[index].warehouse.value = {};
    org[index].warehouse.options = [];
    setOrgRow(org);
    getslocForOrgRow(value, index);
    latestOrgToUse.current = org;
  };
  const setSloc = (value, index) => {
    let org = JSON.parse(JSON.stringify(orgRow));
    org[index].sloc.value = value;
    org[index].warehouse.value = {};
    org[index].warehouse.options = [];
    setOrgRow(org);
    latestOrgToUse.current = org;
    getWarehouseForOrgRow(org[index]?.plant?.value,value,index);
  };
  const setMrpDefault = (value, index) => {
    let org = JSON.parse(JSON.stringify(orgRow));
    org[index].mrpProfile = value;
    setOrgRow(org);
  };
  const setWarehouse = (value, index) => {
    let org = JSON.parse(JSON.stringify(orgRow));
    org[index].warehouse.value = value;
    setOrgRow(org);
  };
  const AddAnotherOrg = () => {
    let org = JSON.parse(JSON.stringify(orgRow));
    org.push(orgRowFields);
    setOrgRow(org);
  };
  const getOrgFromDtTemplate = (val) => {
    if (!val?.temp || val?.temp === selectedTemp?.temp) return;
    setBlurLoading(true);
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_MAT_ORGDATA_TEMPLATE_CONFIG",
      version: "v2",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": initialPayload?.Region || REGION_CODE.US,
          "MDG_CONDITIONS.MDG_MAT_TEMPLATE": val.temp || "",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      if (data.statusCode === API_CODE.STATUS_200) {
        setBlurLoading(false);
        let responseData = data?.data?.result[0]?.MDG_MAT_ORGDATA_TEMPLATE_CONFIG;
        let org = [];
        responseData?.forEach((resp, index) => {
          let orgObj = JSON.parse(JSON.stringify(orgRowFields));
          orgObj.salesOrg = {};
          orgObj.salesOrg["code"] = resp.MDG_MAT_SALES_ORG;
          orgObj.salesOrg["desc"] = resp.MDG_MAT_SALES_ORG_DESC;
          orgObj.plant.value = {};
          orgObj.plant.value["code"] = resp.MDG_MAT_PLANT;
          orgObj.plant.value["desc"] = resp.MDG_MAT_PLANT_DESC;
          let filteredPlant = orgDTData
            ?.filter((val) => val.MDG_MAT_SALES_ORG === resp.MDG_MAT_SALES_ORG)
            ?.map((item) => {
              return {
                code: item.MDG_MAT_PLANT,
                desc: item.MDG_MAT_PLANT_DESC,
              };
            });
          filteredPlant = filteredPlant?.filter((item, index, self) => index === self.findIndex((obj) => obj["code"] === item["code"]));
          orgObj.plant.options = filteredPlant?.sort((a, b) => a.code - b.code);
          let filteredData = orgDTData?.filter((val) => val.MDG_MAT_SALES_ORG === resp.MDG_MAT_SALES_ORG && val.MDG_MAT_PLANT === resp.MDG_MAT_PLANT);
          let storageLoc = filteredData?.map((item) => {
            return {
              code: item.MDG_MAT_STORAGE_LOCATION,
              desc: item.MDG_MAT_STORE_LOC_DESC,
            };
          });
          let wareHouse = filteredData
            ?.map((item) => {
              if (!item.MDG_MAT_WAREHOUSE) return null;
              return {
                code: item.MDG_MAT_WAREHOUSE,
                desc: item.MDG_MAT_WAREHOUSE_DESC,
              };
            })
            .filter(Boolean);
          if (resp.MDG_MAT_STORAGE_LOCATION) {
            orgObj.sloc.value = {};
            orgObj.sloc.value["code"] = resp.MDG_MAT_STORAGE_LOCATION;
            orgObj.sloc.value["desc"] = resp.MDG_MAT_STORE_LOC_DESC;
          }
          orgObj.sloc.options = storageLoc;
          if (resp.MDG_MAT_WAREHOUSE) {
            orgObj.warehouse.value = {};
            orgObj.warehouse.value["code"] = resp.MDG_MAT_WAREHOUSE || "";
            orgObj.warehouse.value["desc"] = resp.MDG_MAT_WAREHOUSE_DESC || "";
          }
          orgObj.warehouse.options = wareHouse;
          org.push(orgObj);
        });
        latestOrgToUse.current = org;
        setOrgRow(org);
        setDistributionChanOptions(org, 0);
      }
      else{
        customError('Something went wrong');
        setBlurLoading(false);
        showSnackbar('No Org data found', "error");
      }
    };

    const hError = (error) => {
      customError('Something went wrong');
      setBlurLoading(false);
      showSnackbar('No Org data found', "error");
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(`/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}`, "post", hSuccess, hError, payload);
    } else {
      doAjax(`/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`, "post", hSuccess, hError, payload);
    }
  };
  const setDistributionChanOptions = async (org, ind) => {
    if (ind < org?.length) {
      await salesOrgDependentOrgElement(org[ind].salesOrg, ind);
      ind++;
      setDistributionChanOptions(org, ind);
    }
  };

  const handleDelete = () => {
    const params = isDeleteDialogVisible?.data;
    setRows(rows?.filter((row) => row.id !== params?.row?.id));
    dispatch(removeMaterialRow(params?.row.id));
    handleMaterialType('');
    dispatch(setMaterialRows(rows?.filter((row) => row.id !== params?.row?.id)));
    if (!rows?.length){
      setAddButtonDisabled(false);
    }
    else{
      if(rows.filter(row => row.params?.id !== params?.row?.id).every(ele => ele.validated)){
        setAddButtonDisabled(false)
      }
    }
    setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false });
  };
  useEffect(() => {
    const hasSales = selectedViews?.includes(MATERIAL_VIEWS?.SALES);
    const hasSalesPlant = selectedViews?.includes(MATERIAL_VIEWS?.SALES_PLANT);
    const hasStorage = selectedViews?.includes(MATERIAL_VIEWS?.STORAGE);
    const hasStoragePlant = selectedViews?.includes(MATERIAL_VIEWS?.STORAGE_PLANT);
    const hasPlantInOrg = orgRow?.some(row => 
      row?.plant?.value?.code
    );
    if (hasSales && !hasSalesPlant && hasPlantInOrg) {
      setSelectedViews((prev) => {
        const newViews = [...prev];
        const salesIndex = newViews.indexOf(MATERIAL_VIEWS?.SALES);
        newViews.splice(salesIndex + 1, 0, MATERIAL_VIEWS?.SALES_PLANT);
        return newViews;
      });
    }
    if(hasStorage && !hasStoragePlant) {
      setSelectedViews((prev) => {
        const newViews = [...prev];
        const storageIndex = newViews.indexOf(MATERIAL_VIEWS?.STORAGE);
        newViews.splice(storageIndex + 1, 0, MATERIAL_VIEWS?.STORAGE_PLANT);
        return newViews;
      });
    }
  }, [selectedViews,orgRow]);
  
  const fetchOrgSpecificData = (orgData) => {
    if (!orgData || !Array.isArray(orgData)) return;
    orgData.forEach(row => {
      if (row.plant?.value?.code) {
        fetchTabSpecificData(row.plant?.value?.code, MATERIAL_VIEWS.PLANT);
        if (row.salesOrg?.code || row.dc?.value?.code) {
          const salesCombination = `${row.salesOrg?.code || ""}-${row.dc?.value?.code || ""}`;
          fetchTabSpecificData(salesCombination, MATERIAL_VIEWS.SALES);
        }
        if (row.warehouse?.value?.code) {
          fetchTabSpecificData(row.warehouse?.value?.code, MATERIAL_VIEWS.WAREHOUSE);
        }
        getContryBasedOnPlant(row.plant?.value?.code)
      }
    });
  };

  useEffect(() => {
    if (isrequestId) {
      const reduxOrgData = selectedMaterialHeaderPayload?.orgData;
      if (reduxOrgData?.length > 0 && reduxOrgData.some((row) => row.plant?.value?.code && (row.salesOrg?.code || row.dc?.value?.code))) {
        fetchOrgSpecificData(reduxOrgData);
      }
    }
  }, [selectedMaterialHeaderPayload?.orgData]);

  const handlePageChange = (newPage) => {
    dispatch(updatePage(newPage));
    setPage(newPage);
  };
  useEffect(() => {
    if (paginationData?.page !== 0 && (requestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD || requestType === REQUEST_TYPE?.CREATE)) {
      getNextDisplayDataForCreate();
    }
    setPage(paginationData?.page || 0);
  }, [paginationData?.page]);

  const toggleGridZoom = () => {
    setIsGridZoomed(!isGridZoomed);
    if (isTabsZoomed) setIsTabsZoomed(false); // Ensure only one section is zoomed at a time
  };
  
  const toggleTabsZoom = () => {
    setIsTabsZoomed(!isTabsZoomed);
    if (isGridZoomed) setIsGridZoomed(false); // Ensure only one section is zoomed at a time
  };

  return (
    <div>
      <div style={{ padding: "0", width: "100%", margin: "0", marginTop: "20px" }}>
        <Box
          sx={{
            position: isGridZoomed ? 'fixed' : 'relative',
            top: isGridZoomed ? 0 : 'auto',
            left: isGridZoomed ? 0 : 'auto',
            right: isGridZoomed ? 0 : 'auto',
            bottom: isGridZoomed ? 0 : 'auto',
            width: isGridZoomed ? '100vw' : '100%',
            height: isGridZoomed ? '100vh' : 'auto',
            zIndex: isGridZoomed ? 1004 : undefined,
            backgroundColor: isGridZoomed ? 'white' : 'transparent',
            padding: isGridZoomed ? '20px' : '0',
            display: 'flex',
            flexDirection: 'column',
            boxShadow: isGridZoomed ? '0px 0px 15px rgba(0, 0, 0, 0.2)' : 'none',
            transition: 'all 0.3s ease',
            borderRadius: '8px',
            border: '1px solid #e0e0e0',
          }}
        >
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            padding: '8px 16px',
            backgroundColor: '#f5f5f5',
            borderRadius: '8px 8px 0 0'
          }}>
            <Typography variant="h6">{t("Material Data")}</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Button
                variant="contained"
                color="primary"
                size="small"
                onClick={() => {
                  if (requestType === REQUEST_TYPE.CREATE) {
                    setOpenAddMatPopup(true);
                    setSelectedMatLines([]);
                    setSelectedMaterials(null);
                    setWithRefValues({});
                    setMaterialOptions([]);
                  }
                }}
                disabled={addButtonDisabled || disableCheck}
              >
                + {t("Add")}
              </Button>
              <Tooltip title={isGridZoomed ? t("Exit Zoom") : t("Zoom In")} sx={{zIndex:'1009'}}>
                <IconButton 
                  onClick={toggleGridZoom} 
                  color="primary"
                  sx={{ 
                    backgroundColor: 'rgba(0, 0, 0, 0.05)',
                    '&:hover': {
                      backgroundColor: 'rgba(0, 0, 0, 0.1)',
                    }
                  }}
                >
                  {isGridZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          
          {isrequestId && rows && rows?.length > 0 ? (
            <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
              <div style={{ height: "100%"}}>
                <DataGrid
                  rows={rows}
                  columns={columns}
                  pageSize={50}
                  autoHeight={false}
                  page={page}
                  rowCount={paginationData?.totalElements || 0}
                  rowsPerPageOptions={[50]}
                  onRowClick={handleRowSelection}
                  onCellEditCommit={handleCellEdit}
                  onPageChange={(newPage) => handlePageChange(newPage)}
                  pagination
                  disableSelectionOnClick
                  getRowClassName={(params) => (params.id === selectedMaterialID ? "selected-row" : "")}
                  style={{
                    border: "1px solid #ccc",
                    borderRadius: "8px",
                    width: "100%",
                     height: isGridZoomed ? "calc(100vh - 150px)" : `${Math.min(rows.length * 50 + 130, 300)}px`,
                      overflow: "auto"
                  }}
                  sx={{
                    "& .selected-row": {
                      backgroundColor: "rgb(234 233 255)",
                    },
                  }}
                />
              </div>
            </div>
          ) : (
            <>
              <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
                <div style={{ height: "100%"}}>
                  <DataGrid
                    autoHeight={false}
                    rows={rows}
                    columns={columns}
                    pageSize={50}
                    page={page}
                    rowsPerPageOptions={[50]}
                    onRowClick={handleRowSelection}
                    onCellEditCommit={handleCellEdit}
                    onPageChange={(newPage) => handlePageChange(newPage)}
                    disableSelectionOnClick
                    getRowClassName={(params) => (params.id === selectedMaterialID ? "selected-row" : "")}
                    style={{
                      border: "1px solid #ccc",
                      borderRadius: "8px",
                      width: "100%",
                      height: isGridZoomed ? "calc(100vh - 150px)" : `${Math.min(rows.length * 50 + 130, 300)}px`,
                      overflow: "auto"
                    }}
                    sx={{
                      "& .selected-row": {
                        backgroundColor: "rgb(234 233 255)",
                      },
                    }}
                  />
                </div>
              </div>
            </>
          )}
        </Box>
      </div>

      {/* Zoomable Tabs Section */}
      {requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || taskData?.ATTRIBUTE_1 ? (
        selectedMaterialID &&
        rowsAdded &&
        rows?.length > 0 &&
        selectedSections?.length > 0 &&
        allTabsData &&
        Object.getOwnPropertyNames(allTabsData)?.length > 0 && (
          <Box
            sx={{
              position: isTabsZoomed ? 'fixed' : 'relative',
              top: isTabsZoomed ? 0 : 'auto',
              left: isTabsZoomed ? 0 : 'auto',
              right: isTabsZoomed ? 0 : 'auto',
              bottom: isTabsZoomed ? 0 : 'auto',
              width: isTabsZoomed ? '100vw' : '100%',
              height: isTabsZoomed ? '100vh' : 'auto',
              zIndex: isTabsZoomed ? 1004 : undefined,
              backgroundColor: isTabsZoomed ? 'white' : 'transparent',
              padding: isTabsZoomed ? '20px' : '0',
              marginTop: "20px",
              display: 'flex',
              flexDirection: 'column',
              boxShadow: isTabsZoomed ? '0px 0px 15px rgba(0, 0, 0, 0.2)' : 'none',
              transition: 'all 0.3s ease',
              borderRadius: '8px',
              border: '1px solid #e0e0e0',
            }}
          >
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              padding: '8px 16px',
              borderRadius: '8px 8px 0 0'
            }}>
              <Typography variant="h6">{t("View Details")}</Typography>
              <Tooltip title={isTabsZoomed ? t("Exit Zoom") : t("Zoom In")} sx={{zIndex:'1009'}}>
                <IconButton 
                  onClick={toggleTabsZoom} 
                  color="primary"
                  sx={{ 
                    backgroundColor: 'rgba(0, 0, 0, 0.05)',
                    '&:hover': {
                      backgroundColor: 'rgba(0, 0, 0, 0.1)',
                    }
                  }}
                >
                  {isTabsZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
            
            <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                className={classes.customTabs}
                aria-label="material tabs"
                sx={{
                  top: 0,
                  position: "sticky",
                  zIndex: 1000,
                  backgroundColor: colors.background.container,
                  borderBottom: `1px solid ${colors.border.light}`,
                  "& .MuiTab-root": {
                    minHeight: "48px",
                    textTransform: "none",
                    fontSize: "14px",
                    fontWeight: 600,
                    color: colors.black.graphite,
                    "&.Mui-selected": {
                      color: colors.primary.main,
                      fontWeight: 700,
                    },
                    "&:hover": {
                      color: colors.primary.main,
                      opacity: 0.8,
                    },
                  },
                  "& .MuiTabs-indicator": {
                    backgroundColor: colors.primary.main,
                    height: "3px",
                  },
                }}
              >
                {selectedViews && orgRow.length > 0 && selectedViews?.length > 0 ? selectedViews?.map((view, index) => <Tab key={index} label={t(view)} />) : <></>}
                <Tab label={t("Additional Data")} key="Additional data" id="AdditionalKey" />
              </Tabs>
              
              <Box sx={{ 
                padding: 2, 
                marginTop: 2,
                flexGrow: 1,
                overflow: 'auto',
                height: isTabsZoomed ? 'calc(100vh - 180px)' : 'auto'
              }}>
                {rows?.length > 0 && renderTabContent()}
              </Box>
              
              {(!disableCheck || (isrequestId && !isreqBench) || (isreqBench && ENABLE_STATUSES.includes(props?.requestStatus))) && 
                <Box sx={{ 
                  borderTop: '1px solid #e0e0e0', 
                  padding: '16px'
                }}>
                  <BottomNav 
                    activeTab={activeTab} 
                    submitForApprovalDisabled={!checkIncludedAndValidated(storedRows)} 
                    filteredButtons={requestDetailsButton} 
                    validateMaterials={validateMaterials} 
                    workFlowLevels={wfLevels}
                    showWfLevels = {showWfLevels}
                    childRequestHeaderData={singlePayloadData?.[selectedMaterialID]?.Tochildrequestheaderdata}
                  />
                </Box>
              }
            </Box>
          </Box>
        )
      ) : (
        <></>
      )}
      
      <ReusableDialog dialogState={openDialog} openReusableDialog={handleDialogClickOpen} closeReusableDialog={handleDialogClose} dialogTitle="Warning" dialogMessage={dialogMessage} showCancelButton={false} handleOk={handleOkDialog} handleDialogConfirm={handleDialogClose} dialogOkText={"OK"} dialogSeverity="danger" />
      {openViews && (
        <Dialog fullWidth maxWidth={false} open={true} onClose={viewsClose} sx={{ display: "flex", justifyContent: "center" }} disableEscapeKeyDown>
          <Box sx={{ width: "600px !important" }}>
            <DialogTitle sx={{ backgroundColor: "#EAE9FF", marginBottom: ".5rem" }}>
              <DescriptionIcon
                style={{
                  height: "20px",
                  width: "20px",
                  marginBottom: "-5px",
                }}
              />
              <span>{t("Select Views")}</span>
            </DialogTitle>
            <DialogContent sx={{ paddingBottom: ".5rem" }}>
              <Box display="flex" alignItems="center" sx={{ flex: 1, padding: "22px 0px", gap: "5px" }}>
                <Autocomplete
                  size="small"
                  multiple
                  fullWidth
                  options={allViews || []}
                  disabled={disableCheck}
                  disableCloseOnSelect
                  value={selectedViews?.filter((option) => !UI_HIDDEN_VIEWS.includes(option))}
                  onChange={(event, newValue) => {
                    if (!taskData?.requestId) {
                      setSelectedViews([fixedOption, ...newValue.filter((option) => option !== fixedOption)]);
                      handleCellEdit({ id: rowId, field: MATERIAL_TABLE.VIEWS, value: newValue });
                    }
                  }}
                  getOptionDisabled={(option) => option === fixedOption}
                  renderOption={(props, option, { selected }) => (
                    <li {...props}>
                      <Checkbox checked={selected} sx={{ marginRight: 1 }} />
                      {option}
                    </li>
                  )}
                  renderTags={(tagValue, getTagProps) =>
                    tagValue.map((option, index) => {
                      const { key, ...tagProps } = getTagProps({ index });
                      return <Chip key={key} label={option} {...tagProps} disabled={option === fixedOption || disableCheck} />;
                    })
                  }
                  renderInput={(params) => <TextField {...params} label={t("Select Views")} />}
                />
                <Button variant="contained" size="small" onClick={() => handleSelectAll()} disabled={disableCheck}>
                  {t("Select all")}
                </Button>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => {
                  setOpenViews(false), handleCellEdit({ id: rowId, field: "views", value: selectedViews });
                }}
                variant="contained"
              >
                {t("Ok")}
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      )}

      {openOrgData && (
        <Dialog
          fullWidth
          maxWidth="xl"
          open
          onClose={viewsClose}
          disableEscapeKeyDown
          sx={{
            "& .MuiDialog-paper": {
              padding: 2,
              borderRadius: 2,
            },
          }}
        >
          <DialogTitle
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              backgroundColor: "#EAE9FF",
            }}
          >
            <DescriptionIcon fontSize="small" />
            <span>{t("Select Org Data")}</span>
            <Box sx={{ position: "absolute", right: "7%", width: "15%" }}>
              <Autocomplete
                options={templates.filter((temp) => temp.region === initialPayload?.Region)}
                value={selectedTemp}
                size="small"
                disabled={disableCheck}
                isOptionEqualToValue={(option, value) => option.region === value.region}
                onChange={(event, newValue) => {
                  setSelectedTemp(newValue), getOrgFromDtTemplate(newValue);
                }}
                getOptionLabel={(option) => option?.temp}
                renderInput={(params) => <TextField {...params} label={t("Select Template")} sx={{ minWidth: 165 }} />}
                sx={{
                  "& .MuiAutocomplete-popper": {
                    minWidth: 250,
                  },
                }}
              />
            </Box>
            <IconButton onClick={viewsClose} sx={{ position: "absolute", right: 15 }}>
              <CloseIcon />
            </IconButton>
          </DialogTitle>

          <DialogContent sx={{ padding: 0 }}>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell align="center">{t("S NO.")}</TableCell>
                    <TableCell align="center">{t("Sales Org")}</TableCell>
                    <TableCell align="center">{t("Distribution Channel")}</TableCell>
                    <TableCell align="center">{t("Plant")}</TableCell>
                    <TableCell align="center">{t("Storage Location")}</TableCell>
                    {initialPayload?.Region !== "EUR" && <TableCell align="center">{t("Warehouse")}</TableCell>}
                    <TableCell align="center">{t("MRP Profile")}</TableCell>
                    {orgRow.length > 1 && <TableCell align="center">{t("Action")}</TableCell>}
                  </TableRow>
                </TableHead>

                <TableBody>
                  {orgRow.map((row, index) => {
                    return (
                      <TableRow key={index} sx={{ padding: "12px", opacity: disableCheck ? 0.5 : 1, pointerEvents: disableCheck ? "none" : "auto" }}>
                        <TableCell>
                          <Typography variant="body2">{index + 1}</Typography>
                        </TableCell>
                        <TableCell>
                          <SingleSelectDropdown
                            options={dropDownData["Sales Organization"]}
                            value={row.salesOrg}
                            onChange={(newValue) => handleSalesOrgSel("Sales Organization", newValue, index)}
                            placeholder={t("Select Sales Org")}
                            // disabled={!checkCommonView(orgOptionShow.salesOrg, selectedViews)}
                            minWidth={165}
                            listWidth={215}
                            title={row?.salesOrg?.code + ` - ${row?.salesOrg?.desc}`}
                            disabled={!checkCommonView(orgOptionShow.salesOrg, selectedViews)}
                          />
                        </TableCell>
                        <TableCell>
                          <SingleSelectDropdown options={row.dc?.options || []} isLoading={isDropdownLoading["Distribution Channel"]?.[index] || false} value={row.dc?.value} onChange={(newValue) => setDc(newValue, index)} placeholder={t("Select DC")} disabled={!checkCommonView(orgOptionShow.distributionChannel, selectedViews)} minWidth={165} listWidth={215} title={row?.dc?.value?.code + ` - ${row?.dc?.value?.desc}`} />
                        </TableCell>
                        <TableCell>
                          <SingleSelectDropdown options={dropDownData["PlantNotExtended"] || []} value={row.plant?.value} onChange={(newValue) => setPlant(newValue, index)} placeholder={t("Select Plant")} disabled={!checkCommonView(orgOptionShow.plant, selectedViews)} minWidth={165} listWidth={215} title={row?.plant?.value?.code + ` - ${row?.plant?.value?.desc}`} />
                        </TableCell>
                        <TableCell>
                          <SingleSelectDropdown options={row?.sloc?.options} value={row?.sloc?.value} isLoading={isDropdownLoading["Storage Location"]?.[index] || false} onChange={(newValue) => setSloc(newValue, index)} placeholder={t("Select Sloc")} disabled={!checkCommonView(orgOptionShow.storage, selectedViews)} minWidth={165} listWidth={215} title={row?.sloc?.value?.code + ` - ${row?.sloc?.value?.desc}`} />
                        </TableCell>
                        {initialPayload?.Region !== "EUR" && (
                          <TableCell>
                            <SingleSelectDropdown options={row?.warehouse?.options || []} value={row?.warehouse?.value} isLoading={isDropdownLoading["Warehouse"]?.[index] || false} onChange={(newValue) => setWarehouse(newValue, index)} disabled={!checkCommonView(orgOptionShow.warehouse, selectedViews)} placeholder={t("Select Warehouse")} minWidth={165} listWidth={215} title={row?.warehouse?.value?.code + ` - ${row?.warehouse?.value?.desc}`} />
                          </TableCell>
                        )}
                        <TableCell>
                          <SingleSelectDropdown 
                            options={dropDownData["Mrp Profile"] || []} 
                            value={row.mrpProfile} 
                            onChange={(newValue) => setMrpDefault(newValue, index)} 
                            placeholder={t("Select MRP Profile")} 
                            disabled={!checkCommonView(orgOptionShow.mrpProfile, selectedViews)}
                            isOptionDisabled={(option) => {
                              if (index === 0) return false; 
                              const currentPlant = orgRow[index].plant?.value?.code;
                              if (!currentPlant) return false; 
                              const previousRow = orgRow.slice(0, index).find(row => 
                                row.plant?.value?.code === currentPlant
                              );
                              if (previousRow && previousRow.mrpProfile) {
                                return option.code !== previousRow.mrpProfile.code;
                              }

                              return false;
                            }}
                            minWidth={165} 
                            listWidth={215} 
                            title={row?.mrpProfile?.code + ` - ${row?.mrpProfile?.desc}`}
                          />
                        </TableCell>
                        {orgRow.length > 1 && (
                          <TableCell align="right">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => {
                                setOpenOrgCopyModal(true);
                                setLengthOfOrgRow({
                                  orgRowLength: orgRow.length,
                                  copyFor: index,
                                });
                                const plantIds = orgRow.filter((row) => row.plant?.value?.code).map((row) => row.plant?.value?.code);
                                if (withReference === "yes") {
                                  addViewForRference(plantIds)
                                }
                              }}
                              style={{ display: index === 0 ? "none" : "inline-flex" }}
                            >
                              <FileCopyIcon />
                            </IconButton>
                            <IconButton size="small" color="error" onClick={() => removeOrgRow(index)}>
                              <DeleteOutlineOutlinedIcon />
                            </IconButton>
                          </TableCell>
                        )}
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </DialogContent>

          <DialogActions sx={{ justifyContent: "flex-end", gap: 0.5 }}>
            <Button onClick={AddAnotherOrg} variant="contained" disabled={!isOrgFilled || disableCheck}>
              + {t("Add")}
            </Button>
            <Tooltip title={isOrgFilled ? "" : t("Please fill all the fields of first row at least")} arrow>
              <span>
                <Button
                  onClick={() => {
                    setOpenOrgData(false);
                    if (orgRow[0].plant?.value?.code) {
                      fetchOrgSpecificData(orgRow);
                      handleCellEdit({ id: rowId, field: "orgData", value: orgRow });
                      getMrpDefaultValues(orgRow);
                      const updatedRows = rows?.map((row) => (row.id === selectedMaterialID ? { ...row, orgData: orgRow} : row));
                      dispatch(setMaterialRows(updatedRows));
                      if (withReference === "no") {
                        const plantIds = orgRow.filter((row) => row.plant?.value?.code).map((row) => row.plant?.value?.code);
                        if (plantIds.length > 0) {
                          addOtherViews(plantIds);
                        }
                      }
                      setSelectedTemp(null);
                    }
                  }}
                  variant="contained"
                  disabled={!isOrgFilled || disableCheck}
                  tooltip={!isOrgFilled ? t("Please fill all the fields of first row at least") : ""}
                >
                  {t("Ok")}
                </Button>
              </span>
            </Tooltip>
          </DialogActions>
        </Dialog>
      )}
      {openAddMatPopup && (
        <Dialog
          fullWidth
          open
          maxWidth="lg"
          //onClose={handleDialogClose}
          sx={{
            "&::webkit-scrollbar": {
              width: "1px",
            },
          }}
        >
          <DialogTitle
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              padding: "0.75rem 1rem",
              backgroundColor: "#EAE9FF",
              borderBottom: "1px solid #d6d6f0",
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <LibraryAddIcon sx={{ mr: 1, color: "#3C3C66" }} />
              <Typography variant="h6" sx={{ fontWeight: 600, color: "#3C3C66" }}>
                {t("Add New Material")}
              </Typography>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ padding: ".5rem 1rem", alignItems: "center", justifyContent: "center", margin: "0px 25px" }}>
            <FormControl component="fieldset" sx={{ paddingBottom: "2%" }}>
              <FormLabel component="legend" sx={{ padding: "15px 0px", fontWeight: "600", fontSize: "15px" }}>
                {t("How would you like to proceed?")}
              </FormLabel>
              <RadioGroup row aria-label="profit-center-number" name="profit-center-number" value={withReference} onChange={(event) => setWithReference(event.target.value)}>
                <FormControlLabel value="yes" control={<Radio />} label={t("With Reference")} />
                <FormControlLabel value="no" control={<Radio />} label={t("Without Reference")} />
              </RadioGroup>
            </FormControl>
            <Grid container spacing={2}>
              
              {/* First row: 4 dropdowns */}
              <Grid item xs={12}>
                <Grid container spacing={2}>
                  <Grid item xs={3}>
                    <SingleSelectDropdown
                      options={MATERIAL_TYPE_DRODOWN || []}
                      value={withRefValues[CHANGE_KEYS.MATERIAL_TYPE] || ""}
                      onChange={(newValue) => {
                        setWithRefValues((prev) => ({ ...prev, [CHANGE_KEYS.MATERIAL_TYPE]: newValue }));
                      }}
                      placeholder={t("Select Material Type")}
                      minWidth={180}
                      listWidth={266}
                      disabled={selectedMatLines?.length || withReference === "no"}
                      getOptionLabel={(option) => (option?.desc ? `${option.code} - ${option.desc}` : option?.code || "")}
                      renderOption={(props, option) => (
                        <li {...props}>
                          <strong>{option?.code}</strong>
                          {option?.desc ? ` - ${option?.desc}` : ""}
                        </li>
                      )}
                    />
                  </Grid>
                  <Grid item xs={3}>
                    <SingleSelectDropdown
                      options={materialOptions}
                      value={inputState || selectedMaterials}
                      onChange={(newValue) => {
                        setSelectedMaterials(newValue);
                        setInputState(newValue);
                        if (!newValue) handleMatInputChange(newValue);
                      }}
                      minWidth={180}
                      listWidth={266}
                      placeholder={t("Select Material")}
                      disabled={selectedMatLines?.length || withReference === "no"}
                      getOptionLabel={(option) => (option?.desc ? `${option.code} - ${option.desc}` : option?.code || "")}
                      renderOption={(props, option) => (
                        <li {...props}>
                          <strong>{option?.code}</strong>
                          {option?.desc ? ` - ${option?.desc}` : ""}
                        </li>
                      )}
                      handleInputChange={handleMatInputChange}
                      sx={{
                        minWidth: 270,
                        "& .MuiAutocomplete-popper": { minWidth: 306 },
                      }}
                      isLoading={isDropdownLoading["Material No"]}
                    />
                  </Grid>
                  {withRefParams?.slice(0, 2).map((key) => (
                    <Grid item xs={3} key={key}>
                      <SingleSelectDropdown
                        options={dropDownData?.[key] || []}
                        value={withRefValues[key] || ""}
                        onChange={(newValue) => {
                          handleSalesOrgWithREF(key, newValue);
                        }}
                        placeholder={t(`Select ${key}`)}
                        minWidth={180}
                        listWidth={306}
                        sx={{
                          minWidth: 270,
                          "& .MuiAutocomplete-popper": { minWidth: 306 },
                        }}
                        disabled={selectedMatLines?.length || withReference === "no"}
                        isLoading={isDropdownLoading[key]}
                      />
                    </Grid>
                  ))}
                </Grid>
              </Grid>

              {/* Second row: 4 dropdowns + "OR" section */}
              <Grid item xs={12}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={3}>
                    <SingleSelectDropdown
                      options={dropDownData?.[withRefParams[2]] || []}
                      value={withRefValues[withRefParams[2]] || ""}
                      onChange={(newValue) => {
                        setWithRefValues((prev) => ({ ...prev, [withRefParams[2]]: newValue }));
                      }}
                      placeholder={t(`Select ${withRefParams[2]}`)}
                      minWidth={180}
                      listWidth={306}
                      sx={{
                        minWidth: 270,
                        "& .MuiAutocomplete-popper": { minWidth: 306 },
                      }}
                      disabled={selectedMatLines?.length || withReference === "no"}
                      isLoading={isDropdownLoading['Distribution Channel'] === true}
                    />
                  </Grid>
                  {withRefParams?.slice(3).map((key) => (
                    <Grid item xs={3} key={key}>
                      <SingleSelectDropdown
                        options={dropDownData?.[key] || []}
                        value={withRefValues[key] || ""}
                        onChange={(newValue) => {
                          setWithRefValues((prev) => ({ ...prev, [key]: newValue }));
                        }}
                        placeholder={t(`Select ${key}`)}
                        minWidth={180}
                        listWidth={306}
                        sx={{
                          minWidth: 270,
                          "& .MuiAutocomplete-popper": { minWidth: 306 },
                        }}
                        disabled={selectedMatLines?.length || withReference === "no"}
                        isLoading={isDropdownLoading[key] === true}
                      />
                    </Grid>
                  ))}
                  {/* OR Dropdown (kept in the same row) */}
                  {storedRows?.length > 0 && (
                    <>
                      <Grid item xs={1} sx={{ textAlign: "center" }}>
                        <Typography variant="body1" sx={{ fontWeight: "bold", color: "gray" }}>
                          OR
                        </Typography>
                      </Grid>
                      <Grid item xs={3}>
                        <SingleSelectDropdown
                          options={storedRows.map((opt) => ({ ...opt, code: opt.lineNumber, desc: "" }))}
                          value={selectedMatLines[0]}
                          onChange={(newValue) => {
                            setSelectedMatLines(newValue ? [newValue] : []);
                            setWithRefValues({});
                            setSelectedMaterials(null);
                            setInputState(null);
                          }}
                          minWidth={180}
                          listWidth={266}
                          placeholder={t("Select Material Line Number")}
                          disabled={selectedMaterials?.code || withReference === "no"}
                          getOptionLabel={(option) => (option?.desc ? `${option.code} - ${option.desc}` : option?.code || "")}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <strong>{option?.code}</strong>
                              {option?.desc ? ` - ${option?.desc}` : ""}
                            </li>
                          )}
                          sx={{
                            minWidth: 270,
                            "& .MuiAutocomplete-popper": { minWidth: 306 },
                          }}
                        />
                      </Grid>
                    </>
                  )}
                </Grid>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{
                width: "max-content",
                textTransform: "capitalize",
              }}
              onClick={() => setOpenAddMatPopup(false)}
              variant="outlined"
            >
              {t("Cancel")}
            </Button>
            <Button className="button_primary--normal" type="save" disabled={!(selectedMatLines?.length || selectedMaterials?.code) && withReference === "yes"} onClick={AddCopiedMaterial} variant="contained">
              {t("Proceed")}
            </Button>
          </DialogActions>
        </Dialog>
      )}
      {isDeleteDialogVisible?.isVisible && (
        <CustomDialog isOpen={isDeleteDialogVisible?.isVisible} titleIcon={<DeleteOutlineOutlinedIcon size="small" color="error" sx={{ fontSize: "20px" }} />} Title={t("Delete Row")+"!"} handleClose={() => setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false })}>
          <DialogContent sx={{ mt: 2 }}>{t(DIALOUGE_BOX_MESSAGES.DELETE_MESSAGE)}</DialogContent>
          <DialogActions>
            <Button variant="outlined" size="small" sx={{ ...button_Outlined }} onClick={() => setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false })}>
              {t(DELETE_MODAL_BUTTONS_NAME.CANCEL)}
            </Button>
            <Button variant="contained" size="small" sx={{ ...button_Primary }} onClick={handleDelete}>
              {t(DELETE_MODAL_BUTTONS_NAME.DELETE)}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
      {openOrgCopyModal && <OrgDataCopyModal open={openOrgCopyModal} onClose={() => setOpenOrgCopyModal(false)} title={HEADINGS.COPY_ORG_DATA_VALES_HEADING} selectedMaterialPayload={selectedMaterialPayload} lengthOfOrgRow={lengthOfOrgRow} materialID={selectedMaterialID} orgRows={orgRow}/>}
      {messageDialogMessage && <ReusableSnackBar openSnackBar={openSnackbar} alertMsg={messageDialogMessage} alertType={alertType} handleSnackBarClose={() => setOpenSnackbar(false)} />}
      <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
      <ToastContainer />
    </div>
  );
};

export default RequestDetails;
