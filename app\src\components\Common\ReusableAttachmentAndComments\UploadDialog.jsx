import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  Typography,
  Stack,
  Button,
  IconButton,
  Box,
  CircularProgress,
  LinearProgress,
} from "@mui/material";
import { CloudUpload } from "@mui/icons-material";
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import React, { useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import CloseIcon from "@mui/icons-material/Close";
import DeleteIcon from '@mui/icons-material/Delete';
import { showToast } from "../../../functions";
import utilityImages from "../../../utilityImages";
import { ERROR_MESSAGES } from "@constant/enum";

const UploadDialog = ({
  onFilesAdded = () => {},
  maxFiles = 5,
  maxFileSize = 500, // MB
  acceptedTypes = ".jpeg, .jpg, .xls, .xlsx, .docx, .pdf",
  currentFileCount = 0,
  loading = false
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [docList, setDocList] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [errors, setErrors] = useState([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [fileSizeError, setFileSizeError] = useState(false);

  const handleOpenDialog = () => {
    setDialogOpen(true);
    setDocList([]);
    setErrors([]);
    setUploadProgress(0);
  };

  const handleCloseDialog = () => {
    if (isUploading) {
      // Prevent closing during upload
      return;
    }
    setDialogOpen(false);
    setDocList([]);
    setErrors([]);
    setUploadProgress(0);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = Array.from(e.dataTransfer.files);
    if (docList.length + files.length > 5) {
      showToast(ERROR_MESSAGES?.DOC_UPLOAD_FILES_LIMIT, "error");
      return;
    }
    handleFileSelection(files);
  };

  const handleFileSelection = (files) => {
    let filesWithId = [];
    files.forEach((item) => {
      item.id = uuidv4();
      filesWithId.push(item);
    });
    setDocList((prev) => [...prev, ...filesWithId]);
  };

  const addFile = () => {
    if (isUploading) return; // Prevent file selection during upload
    document.getElementById("fileButton").click();
  };

  const deleteFile = (id) => {
    if (isUploading) return; // Prevent file deletion during upload
    setDocList(prev => prev.filter(file => file.id !== id));
  };

  const handleUpload = async () => {
    if (docList.length === 0) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90; // Keep at 90% until actual upload completes
          }
          return prev + 10;
        });
      }, 200);

      const result = await onFilesAdded(docList);
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      setTimeout(() => {
        handleCloseDialog();
      }, 500);

    } catch (error) {
      setErrors(['Upload failed. Please try again.']);
      setUploadProgress(0);
    } finally {
      setIsUploading(false);
    }
  };

  let checkFileSize = () => {
    let error = false;
    let size = 0;
    docList.forEach((i) => {
      size += i.length;
    });
    if (size > 5000000000) {
      showToast(ERROR_MESSAGES?.DOC_UPLOAD_SIZE_LIMIT, "error");
      setFileSizeError(true);
    } else {
      setFileSizeError(false);
    }
  };
  
  useEffect(() => {
    checkFileSize();
  }, [docList]);

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <>
      <Button
        variant="contained"
        onClick={handleOpenDialog}
        startIcon={<CloudUpload />}
        disabled={loading}
        sx={{
          backgroundColor: "#1976d2",
          color: "#fff",
          textTransform: "capitalize",
          borderRadius: "5px",
          padding: "10px 20px",
          "&:hover": {
            backgroundColor: "#1565c0",
          },
          "&:disabled": {
            backgroundColor: "#ccc",
          },
          boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.2)",
        }}
      >
        {"Upload Files"}
      </Button>

      <Dialog
        fullWidth
        maxWidth="sm"
        open={dialogOpen}
        onClose={handleCloseDialog}
        disableEscapeKeyDown={isUploading}
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: '12px',
            padding: '1rem',
          },
        }}
      >
        <DialogTitle sx={{ padding: '1rem 1.5rem' }}>
          <Typography variant="h6" sx={{ fontWeight: 500 }}>
            Upload Files
          </Typography>
          <IconButton
            aria-label="close"
            onClick={handleCloseDialog}
            disabled={isUploading}
            sx={{
              position: 'absolute',
              right: 12,
              top: 10,
              color: isUploading ? '#ccc' : '#666',
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        
        <DialogContent sx={{ padding: '1.5rem' }}>
          {/* Upload Progress */}
          {isUploading && (
            <Box sx={{ mb: 2 }}>
              <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
                <Typography variant="body2">Uploading files...</Typography>
                <Typography variant="body2">{uploadProgress}%</Typography>
              </Stack>
              <LinearProgress 
                variant="determinate" 
                value={uploadProgress} 
                sx={{ 
                  height: 8, 
                  borderRadius: 4,
                  backgroundColor: '#e0e0e0',
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 4,
                  }
                }} 
              />
            </Box>
          )}

          {/* Error Messages */}
          {errors.length > 0 && (
            <Box sx={{ mb: 2 }}>
              {errors.map((error, index) => (
                <Typography key={index} variant="body2" color="error" sx={{ mb: 0.5 }}>
                  • {error}
                </Typography>
              ))}
            </Box>
          )}

          {/* Drop Zone */}
          <Box
            sx={{
              width: '100%',
              border: `2px dashed ${isDragOver ? '#3b30c8' : '#d0d5dd'}`,
              borderRadius: '8px',
              padding: '2rem',
              backgroundColor: isDragOver ? '#f8f9ff' : '#fafbff',
              transition: 'all 0.3s ease',
              cursor: isUploading ? 'not-allowed' : 'pointer',
              minHeight: '200px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              opacity: isUploading ? 0.6 : 1,
            }}
            onDragOver={isUploading ? undefined : handleDragOver}
            onDragLeave={isUploading ? undefined : handleDragLeave}
            onDrop={isUploading ? undefined : handleDrop}
            onClick={isUploading ? undefined : addFile}
          >
            <Stack alignItems="center" spacing={1}>
              {isUploading ? (
                <CircularProgress sx={{ color: '#3b30c8' }} />
              ) : (
                <CloudUploadIcon sx={{ fontSize: 48, color: '#3b30c8' }} />
              )}
              <Typography variant="body1" sx={{ color: isUploading ? '#999' : '#344054' }}>
                {isUploading ? 'Uploading...' : 'Drag and drop files here'}
              </Typography>
              {!isUploading && (
                <>
                  <Typography
                    variant="body2"
                    color="primary"
                    sx={{ 
                      cursor: 'pointer', 
                      textDecoration: 'underline',
                      '&:hover': { color: '#3b30c8' }
                    }}
                  >
                    or click to browse
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    Max {maxFiles} files, {maxFileSize}MB each
                  </Typography>
                </>
              )}
            </Stack>
            
            <input
              id="fileButton"
              multiple
              accept={acceptedTypes}
              type="file"
              onChange={(e) => {
                const files = Array.from(e.target.files);
                if (docList.length + files.length > 5) {
                  showToast(ERROR_MESSAGES?.DOC_UPLOAD_FILES_LIMIT, "error");
                  return;
                }
                handleFileSelection(files);
                e.target.value = '';
              }}
              style={{ display: "none" }}
              disabled={isUploading}
            />
          </Box>

          {/* Selected Files List */}
          {docList.length > 0 && (
            <Box
              sx={{
                maxHeight: '200px',
                overflowY: 'auto',
                marginTop: '1.5rem',
                padding: '1rem',
                backgroundColor: '#fff',
                borderRadius: '8px',
                border: '1px solid #eee',
              }}
            >
              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 500 }}>
                Selected Files ({docList.length})
              </Typography>
              <Stack spacing={1}>
                {docList?.map((file) => {
                  const extension = file?.name?.split('.').pop()?.toLowerCase();
                  return (
                    <Box
                      key={file.id}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        padding: '0.75rem',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '6px',
                        border: '1px solid #e9ecef',
                      }}
                    >
                      <img
                          style={{ width: '24px', height: '24px', marginRight: '0.75rem' }}
                          src={utilityImages[file.name?.split(".")[1]]}
                        />
                        <Typography variant="body1" sx={{ flexGrow: 1 }}>
                          {file.name}
                        </Typography>
          
                      <Box sx={{ flex: 1, minWidth: 0 }}>
                        <Typography variant="caption" color="textSecondary">
                          {formatFileSize(file.size)}
                        </Typography>
                      </Box>
                      <IconButton
                        size="small"
                        onClick={() => deleteFile(file.id)}
                        disabled={isUploading}
                        sx={{
                          color: isUploading ? '#ccc' : '#666',
                          '&:hover': {
                            color: isUploading ? '#ccc' : '#d32f2f',
                            backgroundColor: isUploading ? 'transparent' : 'rgba(211, 47, 47, 0.04)',
                          },
                        }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  );
                })}
              </Stack>
            </Box>
          )}

          {/* Action Buttons */}
          <Stack
            direction="row"
            spacing={2}
            justifyContent="flex-end"
            sx={{ mt: 3 }}
          >
            <Button
              variant="outlined"
              onClick={handleCloseDialog}
              disabled={isUploading}
              sx={{
                color: isUploading ? '#ccc' : '#666',
                borderColor: isUploading ? '#ccc' : '#ddd',
              }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={handleUpload}
              disabled={docList.length === 0 || isUploading}
              startIcon={isUploading ? <CircularProgress size={16} color="inherit" /> : undefined}
              sx={{
                backgroundColor: '#1976d2',
                '&:hover': {
                  backgroundColor: '#1565c0',
                },
                '&:disabled': {
                  backgroundColor: '#ccc',
                },
              }}
            >
              {isUploading ? `Uploading... ${uploadProgress}%` : `Upload ${docList.length} file${docList.length !== 1 ? 's' : ''}`}
            </Button>
          </Stack>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UploadDialog;