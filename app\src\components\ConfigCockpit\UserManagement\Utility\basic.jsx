export const getUserById = (userId, users) => {
  return users?.find((user) => user.empId === userId);
};

export const getCurrentLoggedInUser = (userEmail, users) => {
  return users?.find((user) => user.emailId === userEmail);
};

export const findUserById = (emailId, users) => {
  return users?.find((user) => user?.emailId === emailId) || null;
};

export const findIdpUserByEmailId = (emailId, idpUsers) => {
  return idpUsers?.find((idpUser) => idpUser?.emailId === emailId) || null;
};

export const findApplicationById = (id, applications) => {
  return applications?.find((application) => application?.id === id) || null;
};

export const findRoleById = (id, roles) => {
  return roles?.find((role) => role?.id === id) || null;
};

export const findEntityById = (id, entities) => {
  return entities?.find((entitie) => entitie?.id === id) || null;
};

export const findActivityById = (id, activities) => {
  return activities?.find((activity) => activity?.id === id) || null;
};

export const findApiByid = (id, apis) => {
  return apis?.find((api) => Number(api?.id) === Number(id)) || null;
};
