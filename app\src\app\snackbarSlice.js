import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  snackbars: [],
};

export const snackbarSlice = createSlice({
  name: 'snackbar',
  initialState,
  reducers: {
    addSnackbar: (state, action) => {
      const newSnackbar = {
        id: Date.now() + Math.random(),
        message: action.payload.message || '',
        type: action.payload.type || 'success',
        autoHideDuration: action.payload.autoHideDuration || 3000,
        open: true,
      };
      state.snackbars.push(newSnackbar);
    },
    removeSnackbar: (state, action) => {
      state.snackbars = state.snackbars.filter(
        (snackbar) => snackbar.id !== action.payload
      );
    },
    closeSnackbar: (state, action) => {
      const snackbar = state.snackbars.find(
        (snackbar) => snackbar.id === action.payload
      );
      if (snackbar) {
        snackbar.open = false;
      }
    },
    resetSnackbars: (state) => {
      state.snackbars = [];
    }
  },
});

export const {
  addSnackbar,
  removeSnackbar,
  closeSnackbar,
  resetSnackbars
} = snackbarSlice.actions;

export const snackBarReducer = snackbarSlice.reducer;