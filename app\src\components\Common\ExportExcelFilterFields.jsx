import { useState, forwardRef, useRef, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Slide,
} from "@mui/material";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import { useSelector } from "react-redux";
import ReusableBackDrop from "./ReusableBackDrop";
import FilterChangeDropdown from "./ui/dropdown/FilterChangeDropdown";
import { colors } from "@constant/colors";
import { ERROR_MESSAGES, EXPORT_EXCEL_KEYS } from "@constant/enum";
import useMaterialFieldConfig from "@hooks/useMaterialFieldConfig";
import { doAjax } from "./fetchService";
import { END_POINTS } from "../../constant/apiEndPoints";
import { destination_MaterialMgmt } from "../../destinationVariables";
import { sortByCode } from "../../helper/helper";
import { API_CODE } from "../../constant/enum";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});

const ExportExcelFilterFields = ({
  open,
  onClose,
  parameters,
  templateName,
  allDropDownData,
  onSearch,
  buttonName = "Search"
}) => {
  const [selectedValues, setSelectedValues] = useState({});
  const [errors, setErrors] = useState({});
  const [dropDownData, setDropDownData] = useState({
    [EXPORT_EXCEL_KEYS.MATERIAL_NUMBER]: [],
    [EXPORT_EXCEL_KEYS.SALES_ORG]: [],
    [EXPORT_EXCEL_KEYS.DISTRIBUTION_CHANNEL]: [],
    [EXPORT_EXCEL_KEYS.PLANT]: [],
    [EXPORT_EXCEL_KEYS.WAREHOUSE]: [],
    [EXPORT_EXCEL_KEYS.STORAGE_LOCATION]: [],
  });
  const [searchResults, setSearchResults] = useState([]);
  const [hasSearched, setHasSearched] = useState(false);
  const [inputState, setInputState] = useState({ code: "", desc: "" });
  const [timerId, setTimerId] = useState(null);
  const [isLoading, setIsLoading] = useState({
    [EXPORT_EXCEL_KEYS.MATERIAL_NUMBER]: false,
    [EXPORT_EXCEL_KEYS.SALES_ORG]: false,
    [EXPORT_EXCEL_KEYS.DISTRIBUTION_CHANNEL]: false,
    [EXPORT_EXCEL_KEYS.PLANT]: false,
    [EXPORT_EXCEL_KEYS.WAREHOUSE]: false,
    [EXPORT_EXCEL_KEYS.STORAGE_LOCATION]: false,
  });
  const dropdownRef = useRef(null);
  const regionBasedSalesOrgData = useSelector((state) => state.request.salesOrgDTData);
  const { fetchOrgData } = useMaterialFieldConfig();
  
  // Popover states
  const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
  const [popoverContent, setPopoverContent] = useState("");
  const [isPopoverVisible, setIsPopoverVisible] = useState(false);
  const popoverRef = useRef(null);

  const handleSelectionChange = (key, newValue) => {
    setSelectedValues((prev) => ({
      ...prev,
      [key]: newValue,
    }));
    if (newValue.length > 0) {
      setErrors((prev) => ({
        ...prev,
        [key]: "",
      }));
    }
  };

  const handlePopoverOpen = (event, content) => {
    setPopoverAnchorEl(event.currentTarget);
    setPopoverContent(content);
    setIsPopoverVisible(true);
  };

  const handlePopoverClose = () => {
    setIsPopoverVisible(false);
  };

  const handleMouseEnterPopover = () => {
    setIsPopoverVisible(true);
  };

  const handleMouseLeavePopover = () => {
    setIsPopoverVisible(false);
  };

  const handleSelectAll = (key, options) => {
    setSelectedValues((prev) => ({
      ...prev,
      [key]: options,
    }));
  };

  const handleMatInputChange = (e) => {
    const inputValue = e.target.value?.toUpperCase();
    setInputState({ code: inputValue, desc: "" });
    if (timerId) {
      clearTimeout(timerId);
    }

    const newTimerId = setTimeout(() => {
      if(selectedValues?.[EXPORT_EXCEL_KEYS.MATERIAL_TYPE]?.length){
        getMaterialNo(inputValue, true, selectedValues?.[EXPORT_EXCEL_KEYS.MATERIAL_TYPE][0]);
      }
    }, 500);

    setTimerId(newTimerId);
  };

  const formatOptionLabel = (option) => {
    if (option.code && option.desc) {
      return `${option.code} - ${option.desc}`;
    }
    return option.code || "";
  };

  const getMaterialNo = (value = "", reset = false, materialType) => {
    setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.MATERIAL_NUMBER]: true }));
    const payload = {
      matlType: materialType?.code ?? "",
      materialNo: value ?? "",
      top: 500,
      skip: reset ? 0 : skip,
      salesOrg:
        regionBasedSalesOrgData?.uniqueSalesOrgList
          ?.map((item) => item.code)
          ?.join("$^$") || "",
    };
    const hSuccess = (data) => {
      if(data?.statusCode === API_CODE.STATUS_200) {
        if (reset) {
          setDropDownData((prev) => ({
            ...prev,
            [EXPORT_EXCEL_KEYS.MATERIAL_NUMBER]: data.body,
          }));
        } else {
          setDropDownData((prev) => ({
            ...prev,
            [EXPORT_EXCEL_KEYS.MATERIAL_NUMBER]: [
              ...(prev[EXPORT_EXCEL_KEYS.MATERIAL_NUMBER] || []),
              ...data.body,
            ],
          }));
        }
        setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.MATERIAL_NUMBER]: false }));
      }
    };
    const hError = (error) => {
      customError(error);
      setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.MATERIAL_NUMBER]: false }));
    };
    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS?.DATA?.GET_SEARCH_PARAMS_MATERIAL_NO}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getSalesOrg = (materialNo, region) => {
    setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.SALES_ORG]: true }));
    
    const hSuccess = (data) => {
      if(data?.statusCode === API_CODE.STATUS_200){
        let salesOrgData = data.body.length > 0 ? sortByCode(data.body) : [];
        setDropDownData((prev) => ({ ...prev, [EXPORT_EXCEL_KEYS.SALES_ORG]: salesOrgData }));
      }
      setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.SALES_ORG]: false }));
    }
    
    const hError = () => { 
      setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.SALES_ORG]: false }));
    }
    
    doAjax(`/${destination_MaterialMgmt}${END_POINTS.DATA.GET_SALES_ORG_EXTENDED}?materialNo=${materialNo}&region=${region}`, "get", hSuccess, hError);
  }
  const getPlant = (materialNo, salesOrg, region) => {
    setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.PLANT]: true }));
    
    const hSuccess = (data) => {
      if(data?.statusCode === API_CODE.STATUS_200){
        let plantData = data.body.length > 0 ? sortByCode(data.body || []) : [];
        setDropDownData((prev) => ({ ...prev, [EXPORT_EXCEL_KEYS.PLANT]: plantData }));
      }
      setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.PLANT]: false }));
    }
    
    const hError = () => { 
      setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.PLANT]: false }));
    }

    doAjax(`/${destination_MaterialMgmt}${END_POINTS.DATA.GET_PLANT_EXTENDED}?materialNo=${materialNo}&region=${region}&salesOrg=${salesOrg}`, "get", hSuccess, hError);
  }

  const getDistrChan = (materialNo, salesOrg) => {
    setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.DISTRIBUTION_CHANNEL]: true }));
    
    const hSuccess = (data) => {
      if(data?.statusCode === API_CODE.STATUS_200){
        let plantData = data.body.length > 0 ? sortByCode(data.body || []) : [];
        setDropDownData((prev) => ({ ...prev, [EXPORT_EXCEL_KEYS.DISTRIBUTION_CHANNEL]: plantData }));
      }
      setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.DISTRIBUTION_CHANNEL]: false }));
    }
    
    const hError = () => { 
      setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.DISTRIBUTION_CHANNEL]: false }));
    }

    doAjax(`/${destination_MaterialMgmt}${END_POINTS.DATA.GET_DISTR_CHAN_EXTENDED}?materialNo=${materialNo}&salesOrg=${salesOrg}`, "get", hSuccess, hError);
  }

  const getWarehouse = (materialNo, plant, region) => {
    setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.WAREHOUSE]: true }));
    
    const hSuccess = (data) => {
      if(data?.statusCode === API_CODE.STATUS_200){
        let warehouseData = data.body.length > 0 ? sortByCode(data.body || []): []
        setDropDownData((prev) => ({ ...prev, [EXPORT_EXCEL_KEYS.WAREHOUSE]: warehouseData }));
      }
      setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.WAREHOUSE]: false }));
    }
    
    const hError = () => { 
      setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.WAREHOUSE]: false }));
    }
    
    doAjax(`/${destination_MaterialMgmt}${END_POINTS.DATA.GET_WAREHOUSE_EXTENDED}?materialNo=${materialNo}&region=${region}&plant=${plant}`, "get", hSuccess, hError);
  }

  const getStorLoc = (materialNo, plant, region, salesOrg) => {
    setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.STORAGE_LOCATION]: true }));
    
    const hSuccess = (data) => {
      if(data?.statusCode === API_CODE.STATUS_200){
        let warehouseData = data.body.length > 0 ? sortByCode(data.body || []): []
        setDropDownData((prev) => ({ ...prev, [EXPORT_EXCEL_KEYS.STORAGE_LOCATION]: warehouseData }));
      }
      setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.STORAGE_LOCATION]: false }));
    }
    
    const hError = () => { 
      setIsLoading(prev => ({ ...prev, [EXPORT_EXCEL_KEYS.STORAGE_LOCATION]: false }));
    }
    
    doAjax(`/${destination_MaterialMgmt}${END_POINTS.DATA.GET_STOR_LOC_EXTENDED}?materialNo=${materialNo}&region=${region}&plant=${plant}&salesOrg=${salesOrg}`, "get", hSuccess, hError);
  }

  useEffect(() => {
    if (selectedValues[EXPORT_EXCEL_KEYS?.REGION]?.length) {
      fetchOrgData(selectedValues[EXPORT_EXCEL_KEYS?.REGION][0]);
    }
  }, [selectedValues[EXPORT_EXCEL_KEYS?.REGION]]);

  useEffect(() => {
    if (selectedValues[EXPORT_EXCEL_KEYS?.MATERIAL_TYPE]?.length) {
      getMaterialNo("", true, selectedValues[EXPORT_EXCEL_KEYS?.MATERIAL_TYPE][0]);
    }
  }, [selectedValues[EXPORT_EXCEL_KEYS?.MATERIAL_TYPE]]);

  useEffect(() => {
    if (selectedValues[EXPORT_EXCEL_KEYS?.MATERIAL_NUMBER]?.length) {
      getSalesOrg(
        selectedValues[EXPORT_EXCEL_KEYS?.MATERIAL_NUMBER][0]?.code, 
        selectedValues[EXPORT_EXCEL_KEYS?.REGION][0]?.code
      );
      return;
    }
    if(selectedValues[EXPORT_EXCEL_KEYS?.MATERIAL_TYPE]?.length && !selectedValues[EXPORT_EXCEL_KEYS?.MATERIAL_NUMBER]?.length) {
      getMaterialNo("", true, selectedValues[EXPORT_EXCEL_KEYS?.MATERIAL_TYPE][0]);
    }
  }, [selectedValues[EXPORT_EXCEL_KEYS?.MATERIAL_NUMBER]]);

  useEffect(() => {
    if (selectedValues[EXPORT_EXCEL_KEYS?.SALES_ORG]?.length) {
      getPlant(
        selectedValues[EXPORT_EXCEL_KEYS?.MATERIAL_NUMBER][0]?.code, 
        selectedValues[EXPORT_EXCEL_KEYS?.SALES_ORG][0]?.code, 
        selectedValues[EXPORT_EXCEL_KEYS?.REGION][0]?.code
      );
      getDistrChan(
        selectedValues[EXPORT_EXCEL_KEYS?.MATERIAL_NUMBER][0]?.code, 
        selectedValues[EXPORT_EXCEL_KEYS?.SALES_ORG][0]?.code, 
      );
    }
  }, [selectedValues[EXPORT_EXCEL_KEYS?.SALES_ORG]]);

  useEffect(() => {
    if (selectedValues[EXPORT_EXCEL_KEYS?.PLANT]?.length) {
      getWarehouse(
        selectedValues[EXPORT_EXCEL_KEYS?.MATERIAL_NUMBER][0]?.code, 
        selectedValues[EXPORT_EXCEL_KEYS?.PLANT][0]?.code, 
        selectedValues[EXPORT_EXCEL_KEYS?.REGION][0]?.code
      );
      getStorLoc(
        selectedValues[EXPORT_EXCEL_KEYS?.MATERIAL_NUMBER][0]?.code,
        selectedValues[EXPORT_EXCEL_KEYS?.PLANT][0]?.code, 
        selectedValues[EXPORT_EXCEL_KEYS?.REGION][0]?.code, 
        selectedValues[EXPORT_EXCEL_KEYS?.SALES_ORG][0]?.code, 
      );
    }
  }, [selectedValues[EXPORT_EXCEL_KEYS?.PLANT]]);

  useEffect(() => {
    if (Object.keys(selectedValues).length === 0) {
      setDropDownData(prev => ({
        ...prev,
        [EXPORT_EXCEL_KEYS.MATERIAL_NUMBER]: [],
        [EXPORT_EXCEL_KEYS.SALES_ORG]: [],
        [EXPORT_EXCEL_KEYS.DISTRIBUTION_CHANNEL]: [],
        [EXPORT_EXCEL_KEYS.PLANT]: [],
        [EXPORT_EXCEL_KEYS.WAREHOUSE]: [],
        [EXPORT_EXCEL_KEYS.STORAGE_LOCATION]: []
      }));
    }
  }, [selectedValues]);

  const prevRegionRef = useRef();
  const prevMaterialTypeRef = useRef();
  const prevMaterialNumberRef = useRef();
  const prevSalesOrgRef = useRef();
  const prevPlantRef = useRef();

  useEffect(() => {
    const currentRegion = selectedValues[EXPORT_EXCEL_KEYS.REGION]?.[0]?.code;
    if (!selectedValues[EXPORT_EXCEL_KEYS.REGION]?.length || currentRegion !== prevRegionRef.current) {
      setDropDownData(prev => ({
        ...prev,
        [EXPORT_EXCEL_KEYS.MATERIAL_TYPE]: [],
      }));
      setSelectedValues(prev => ({
        ...prev,
        [EXPORT_EXCEL_KEYS.MATERIAL_TYPE]: [],
      }));
    }
    prevRegionRef.current = currentRegion;
  }, [selectedValues[EXPORT_EXCEL_KEYS.REGION]]);

  useEffect(() => {
    const currentMaterialType = selectedValues[EXPORT_EXCEL_KEYS.MATERIAL_TYPE]?.[0]?.code;
    if (!selectedValues[EXPORT_EXCEL_KEYS.MATERIAL_TYPE]?.length || currentMaterialType !== prevMaterialTypeRef.current) {
      setDropDownData(prev => ({
        ...prev,
        [EXPORT_EXCEL_KEYS.MATERIAL_NUMBER]: []
      }));
      setSelectedValues(prev => ({
        ...prev,
        [EXPORT_EXCEL_KEYS.MATERIAL_NUMBER]: [],
      }));
    }
    prevMaterialTypeRef.current = currentMaterialType;
  }, [selectedValues[EXPORT_EXCEL_KEYS.MATERIAL_TYPE]]);

  useEffect(() => {
    const currentMaterialNumber = selectedValues[EXPORT_EXCEL_KEYS.MATERIAL_NUMBER]?.[0]?.code;
    if (!selectedValues[EXPORT_EXCEL_KEYS.MATERIAL_NUMBER]?.length || currentMaterialNumber !== prevMaterialNumberRef.current) {
      setDropDownData(prev => ({
        ...prev,
        [EXPORT_EXCEL_KEYS.SALES_ORG]: [],
      }));
      setSelectedValues(prev => ({
        ...prev,
        [EXPORT_EXCEL_KEYS.SALES_ORG]: [],
      }));
    }
    prevMaterialNumberRef.current = currentMaterialNumber;
  }, [selectedValues[EXPORT_EXCEL_KEYS.MATERIAL_NUMBER]]);

  useEffect(() => {
    const currentSalesOrg = selectedValues[EXPORT_EXCEL_KEYS.SALES_ORG]?.[0]?.code;
    if (!selectedValues[EXPORT_EXCEL_KEYS.SALES_ORG]?.length || currentSalesOrg !== prevSalesOrgRef.current) {
      setDropDownData(prev => ({
        ...prev,
        [EXPORT_EXCEL_KEYS.DISTRIBUTION_CHANNEL]: [],
        [EXPORT_EXCEL_KEYS.PLANT]: [],
      }));
      setSelectedValues(prev => ({
        ...prev,
        [EXPORT_EXCEL_KEYS.DISTRIBUTION_CHANNEL]: [],
        [EXPORT_EXCEL_KEYS.PLANT]: [],
      }));
    }
    prevSalesOrgRef.current = currentSalesOrg;
  }, [selectedValues[EXPORT_EXCEL_KEYS.SALES_ORG]]);

  useEffect(() => {
    const currentPlant = selectedValues[EXPORT_EXCEL_KEYS.PLANT]?.[0]?.code;
    if (!selectedValues[EXPORT_EXCEL_KEYS.PLANT]?.length || currentPlant !== prevPlantRef.current) {
      setDropDownData(prev => ({
        ...prev,
        [EXPORT_EXCEL_KEYS.WAREHOUSE]: [],
        [EXPORT_EXCEL_KEYS.STORAGE_LOCATION]: []
      }));
      setSelectedValues(prev => ({
        ...prev,
        [EXPORT_EXCEL_KEYS.WAREHOUSE]: [],
        [EXPORT_EXCEL_KEYS.STORAGE_LOCATION]: []
      }));
    }
    prevPlantRef.current = currentPlant;
  }, [selectedValues[EXPORT_EXCEL_KEYS.PLANT]]);

  const renderAutocomplete = (param) => {
    if(param?.key === EXPORT_EXCEL_KEYS?.MATERIAL_NUMBER) {
      return (
        <FilterChangeDropdown
          param={param}
          dropDownData={dropDownData}
          allDropDownData={allDropDownData}
          selectedValues={selectedValues}
          inputState={inputState}
          handleSelectAll={handleSelectAll}
          handleSelectionChange={handleSelectionChange}
          handleMatInputChange={handleMatInputChange}
          dropdownRef={dropdownRef}
          errors={errors}
          formatOptionLabel={formatOptionLabel}
          handlePopoverOpen={handlePopoverOpen}
          handlePopoverClose={handlePopoverClose}
          handleMouseEnterPopover={handleMouseEnterPopover}
          handleMouseLeavePopover={handleMouseLeavePopover}
          isPopoverVisible={isPopoverVisible}
          popoverId={popoverOpen ? "custom-popover" : undefined}
          popoverAnchorEl={popoverAnchorEl}
          popoverRef={popoverRef}
          popoverContent={popoverContent}
          isLoading={isLoading[param.key]}
          singleSelect={param.singleSelect}
        />
      );
    } 
    else if(param?.key === EXPORT_EXCEL_KEYS?.REGION || param?.key === EXPORT_EXCEL_KEYS?.MATERIAL_TYPE) {
      return (
        <FilterChangeDropdown
          param={param}
          dropDownData={{ [param.key]: param.options }}
          allDropDownData={allDropDownData}
          selectedValues={selectedValues}
          inputState={inputState}
          handleSelectAll={handleSelectAll}
          handleSelectionChange={handleSelectionChange}
          dropdownRef={dropdownRef}
          errors={errors}
          formatOptionLabel={formatOptionLabel}
          handlePopoverOpen={handlePopoverOpen}
          handlePopoverClose={handlePopoverClose}
          handleMouseEnterPopover={handleMouseEnterPopover}
          handleMouseLeavePopover={handleMouseLeavePopover}
          isPopoverVisible={isPopoverVisible}
          popoverId={popoverOpen ? "custom-popover" : undefined}
          popoverAnchorEl={popoverAnchorEl}
          popoverRef={popoverRef}
          popoverContent={popoverContent}
          isLoading={isLoading[param.key]}
          singleSelect={param.singleSelect}
        />
      );
    } 
    else {
      return (
        <FilterChangeDropdown
          param={param}
          dropDownData={dropDownData}
          allDropDownData={allDropDownData}
          selectedValues={selectedValues}
          inputState={inputState}
          handleSelectAll={handleSelectAll}
          handleSelectionChange={handleSelectionChange}
          dropdownRef={dropdownRef}
          errors={errors}
          formatOptionLabel={formatOptionLabel}
          handlePopoverOpen={handlePopoverOpen}
          handlePopoverClose={handlePopoverClose}
          handleMouseEnterPopover={handleMouseEnterPopover}
          handleMouseLeavePopover={handleMouseLeavePopover}
          isPopoverVisible={isPopoverVisible}
          popoverId={popoverOpen ? "custom-popover" : undefined}
          popoverAnchorEl={popoverAnchorEl}
          popoverRef={popoverRef}
          popoverContent={popoverContent}
          isLoading={isLoading[param.key]}
          singleSelect={param.singleSelect}
        />
      );
    }
  };

  const handleOkClick = () => {
    setHasSearched(true);
    onSearch(selectedValues)
    setSelectedValues({})
  };

  const isAnyValueSelected = () => {
    return Object.values(selectedValues).some(
      (value) => Array.isArray(value) && value.length > 0
    );
  };

  const popoverOpen = Boolean(popoverAnchorEl);

  return (
    <>
      <Dialog
        open={open}
        TransitionComponent={Transition}
        keepMounted
        onClose={onClose}
        maxWidth="lg"
        fullWidth
      >
        <Box
          sx={{
            backgroundColor: "#e3f2fd",
            padding: "1rem 1.5rem",
            display: "flex",
            alignItems: "center",
          }}
        >
          <FeedOutlinedIcon color="primary" sx={{ marginRight: "0.5rem" }} />
          <Typography variant="h6" component="div" color="primary">
            {templateName} Search Filter(s)
          </Typography>
        </Box>

        <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: "repeat(3, 1fr)",
              gap: 2,
            }}
          >
            {parameters?.map((param) => (
              <Box key={param.key} sx={{ marginBottom: "1rem" }}>
                {renderAutocomplete(param)}
              </Box>
            ))}
          </Box>
        </DialogContent>

        <DialogActions
          sx={{
            padding: "0.5rem 1.5rem",
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          <div>
          </div>

          <div style={{ display: "flex", gap: "8px" }}>
            <Button
              onClick={() =>{
                setSelectedValues({});
              }}
              color="warning"
              variant="outlined"
              sx={{
                height: 36,
                minWidth: "3.5rem",
                textTransform: "none",
                borderColor: "#cc3300",
                fontWeight: 500,
              }}
            >
              Clear
            </Button>
            <Button
              onClick={() =>{
                setSelectedValues({});
                onClose()
              }}
              color="error"
              variant="outlined"
              sx={{
                height: 36,
                minWidth: "3.5rem",
                textTransform: "none",
                borderColor: "#cc3300",
                fontWeight: 500,
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleOkClick}
              variant="contained"
              disabled={!isAnyValueSelected()}
              sx={{
                height: 36,
                minWidth: "3.5rem",
                backgroundColor: "#3B30C8",
                textTransform: "none",
                fontWeight: 500,
                "&:hover": {
                  backgroundColor: "#2c278f",
                },
              }}
            >
              {buttonName}
            </Button>
          </div>
        </DialogActions>
      </Dialog>
      <ReusableBackDrop blurLoading={false} />
    </>
  );
};

export default ExportExcelFilterFields;
