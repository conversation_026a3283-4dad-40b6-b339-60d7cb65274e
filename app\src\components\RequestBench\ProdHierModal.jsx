import { DT_TABLES } from "@constant/enum";
import useGenericDtCall from "@hooks/useGenericDtCall";
import { useSelector } from "react-redux";
import { useEffect, useMemo, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import { Autocomplete, Box, Button, Dialog, DialogActions, DialogContent, DialogTitle, Grid, IconButton, TextField, Typography } from "@mui/material";

const ProdHierModal = (props) =>{
    const payloadState = useSelector((state) => state.payload);
    const { getDtCall, dtData } = useGenericDtCall();
    const [prodHierLevels,setProdHierLevels] = useState([{name:'Level 1',options:[],value:''},{name:'Level 2',options:[],value:''},
        {name:'Level 3',options:[],value:''},{name:'Level 4',options:[],value:{code:'000',desc:'Not related'}},
        {name:'Level 5',options:[],value:{code:'000',desc:'Not related'}},{name:'Level 6',options:[],value:{code:'000',desc:'Not related'}},
        {name:'Level 7',options:[],value:{code:'0',desc:'Not related'}}]);
    const [productHierData, setProductHierData] = useState([]);
    useEffect(() =>{
        getProdHierarchy();
    },[])
    useEffect(() =>{
        if(dtData){
            filterOptions(dtData.result?.[0]?.MDG_MAT_PRODUCT_HIERARCHY,0);
            setProductHierData(dtData.result?.[0]?.MDG_MAT_PRODUCT_HIERARCHY)
        }
    },[dtData])
    const getProdHierarchy = ()=>{
        let payload = {
            decisionTableId: null,
            decisionTableName: DT_TABLES.MDG_MAT_PRODUCT_HIERARCHY,
            version: "v1",
            conditions: [
            {
                "MDG_CONDITIONS.MDG_MAT_REGION": payloadState?.payloadData?.Region,
                "MDG_CONDITIONS.MDG_MAT_DIVISION": payloadState?.payloadData?.Division,
            },
            ],
        };
        getDtCall(payload);
    }
    const filterOptions = (optArr,index) =>{
        let prodHier = prodHierLevels;
        let options = optArr?.map(opt =>{
            return {
                code : opt.MDG_MAT_BRAND,
                desc : opt.MDG_MAT_BRAND_DESC
            }
        })
        prodHier[index].options = options?.filter((opt,index,self) =>index === self.findIndex(item => item.code === opt.code)) || [];
        setProdHierLevels(prodHier)
    }
    const filterLevelOptions = (ind, val) => {
        let prodHier = JSON.parse(JSON.stringify(prodHierLevels)); // Deep copy
        const keys = [
            "MDG_MAT_BRAND",
            "MDG_MAT_SUB_BRAND",
            "MDG_MAT_CATEGORY",
            "MDG_MAT_PRODUCT_FAMILY",
            "MDG_MAT_PRODUCT_TYPE",
            "MDG_MAT_LEVEL6",
            "MDG_MAT_BUSINESS_CATEGORY"
        ];
        // Set the selected value at the current index
        prodHier[ind].value = val;
    
        // Reset all levels above the current index
        for (let i = ind + 1; i < prodHier?.length; i++) {
            //prodHier[i].value = null;
            prodHier[i].options = [];
        }
    
        // If the current index is within the valid range
        if (ind < keys.length - 1) {
            let options = productHierData
                .filter(prod =>
                    keys.slice(0, ind).every((key, i) => prod[key] === prodHier[i]?.value?.code) &&
                    prod[keys[ind]] === val?.code
                )
                .map(op => ({
                    code: op[keys[ind + 1]], 
                    desc: op[keys[ind + 1] + "_DESC"]
                }))
                .filter((opt, index, self) =>
                    index === self.findIndex(item => item.code === opt.code) // duplicate remove
                )?.sort((a,b) => a.code - b.code);
    
            prodHier[ind + 1].options = options;
        }
        // Update state
        setProdHierLevels(prodHier);
    };
    
    const handleClose = () =>{
        props?.setIsClicked(false)
    }
    const extractProductHierVal = () =>{
        let val = prodHierLevels.map(obj => obj?.value?.code || "").join("");
        props.setProdHierVal(val);
        handleClose();
    }
    return (
        <>
            {props?.isClicked && prodHierLevels?.[0]?.options?.length > 0 && (<Dialog open={true} sx={{ display: "flex", justifyContent: "center" }} fullWidth
                maxWidth="xl">
            <Box>
                <DialogTitle sx={{ backgroundColor: "#EAE9FF", marginBottom: ".5rem" }}>
                    <Box sx={{display:'flex',alignItems:'center'}}><span>Select Product Hierarchy</span>
                        <IconButton onClick={handleClose} sx={{ position: "absolute", right: 15 }}>
                            <CloseIcon />
                        </IconButton>
                    </Box>
                </DialogTitle>
                <DialogContent >
                    <Grid container spacing={2} wrap="nowrap">
                        {prodHierLevels?.map((prod, index) => (
                            <Grid item key={index} sx={{ minWidth: 165 }}>
                                <Autocomplete
                                    fullWidth
                                    // disabled={props?.disabled || props.details?.visibility === VISIBILITY_TYPE.DISPLAY}
                                    size="small"
                                    value={prod?.value}
                                    onChange={(event, newValue) => filterLevelOptions(index,newValue)}
                                    options={prod?.options || []}
                                    getOptionLabel={(option) => (option?.desc ? `${option?.code || ""} - ${option?.desc || ""}` : `${option?.code || ""}`)}
                                    renderOption={(props, option) => (
                                        <li {...props}>
                                        <Typography style={{ fontSize: 12 }}>
                                            <strong>{option?.code}</strong>
                                            {option?.desc ? ` - ${option?.desc}` : ""}
                                        </Typography>
                                        </li>
                                    )}
                                    renderInput={(params) => 
                                        <TextField {...params} 
                                        variant="outlined" 
                                        placeholder={`Select ${prod?.name || "Field Name"}`} 
                                        // error={errorFields.includes(props?.keyName || "")} 
                                        // InputProps={{
                                        //     ...params.InputProps,
                                        // }}
                                        sx={{ minWidth: 165 }}
                                        />
                                    }
                                />
                            </Grid>)
                        )}
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <Button variant="contained" onClick={() =>extractProductHierVal()}>
                        Ok
                    </Button>
                </DialogActions>
            </Box>
            </Dialog>)}
        </>)
}
export default ProdHierModal;