// import { destination_ServiceRequest } from "../destinationVariables";
// //   import { startDate, endDate } from "../components/common/DateRangeByQuarter";
// import store from "../app/store";
// import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

// /* Setting Default Dates */
// const presentDate = new Date();
// const backDate = new Date();
// // const appSettings=useSelector((state)=> state.appSettings["Format"])

// backDate.setDate(backDate.getDate() - 3650);

// let initialState = {
//   Dashboard: {
//     vendorNo: "",
//     companyCode: "",
//     dashboardDate: [presentDate, backDate],
//     selectedusersId:[],
//     selectedRequestType :[],
//     selectedRequestStatus:[],
//     selectedRequestTypeSLA:["Create"],
//     selectedRequestTypeInGroup:["Create"],
//     selectedRequestTypeSLATable:["Create"],
//     selectedRequestTypeRole:["Create"],
//     tab: 0,
//   },
//   MaterialMaster: {
//     plant: {
//       code: "",
//       desc: "",
//     },
//     group: {
//       code: "",
//       desc: "",
//     },
//     "Lab/Office": {
//       code: "",
//       desc: "",
//     },
//     "Transportation Group": {
//       code: "",
//       desc: "",
//     },
//     "Product Hierarchy": "",
//     createdBy: "",
//     changedBy: "",
//     division:"",
//     oldMaterialNumber: "",
//     createdOn: [backDate, presentDate],
//     number: "",
//     type: {
//       code: "",
//       desc: "",
//     },
//     salesOrg: {
//       code: "",
//       desc: "",
//     },
//     description: "",
//     distributionChannel: {
//       code: "",
//       desc: ""
//     }
//   },
//   DuplicateDesc: {
//     group: {
//       code: "",
//       desc: "",
//     },
//     baseUnit: {
//       code: "",
//       desc: "",
//     },
//     packagingMaterials: {
//       code: "",
//       desc: "",
//     },
//     prefix: "",
//   },
//   NewMaterial: {
//     selectViewsValue: [],
//     materialNo: "",
//     sector: {
//       code: "",
//       desc: "",
//     },
//     type: {
//       code: "",
//       desc: "",
//     },
//     description: "",
//     description1: "",
//     copyMaterial: "",
//     selectedViews: "Basic data",
//     text: "",
//     // plant: { selectedPlantNo },
//     // salesOrg: { selectedSalesOrg },
//     // distributionChannel: { selectedDistributionChannel },
//     // storageLocation: { selectedStorageLocation },
//     // mrpProfile: { selectedMRPProfile },
//     // forecastProfile: { selectedForecastProfile },
//     // warehouseNo: { selectedWarehouseNo },
//     // storageType: { selectedStorageType },
//   },
//   DocumentManagement: {
//     docName: "",
//     transactionId: "",
//     transactionType: [],
//     docType: "",
//     uploadedBy: "",
//     uploadedDate: [backDate, presentDate],
//   },
//   Userswb: {
//     // taskId:'',
//     assignedTo: "",
//     date: [backDate, presentDate],
//     company: "",
//     supplier: "",
//     taskName: [],
//     taskStatus: [],
//     type: [],
//     createdBy: "",
//   },
//   RequestBench: {
//     requestId: "",
//     requestType: "",
//     createdBy: "",
//     createdOn: [backDate, presentDate],
//     reqPriority:[],
//     reqStatus: [],
//     userId: "",
//     distChnl: "",
//     plantOrg: "",
//     salesOrg: "",
//     controllingArea:"",
//     costCenters:""
//   },
//   CostCenter: {
//     costCenterName: "",
//     costCenter:"",
//     number:"",
//     controllingArea: {
//       code: "",
//       desc: "",
//     },
//     companyCode: {
//       code: "",
//       desc: "",
//     },
//     profitCenter: {
//       code: "",
//       desc: "",
//     },
//     hierarchyArea: {
//       code: "",
//       desc: "",
//     },
//     costCenterCategory: {
//       code: "",
//       desc: "",
//     },
//   },
//   ProfitCenter: {
//     controllingArea:"",
//     profitCenter:"",
//     profitCenterName:"",
//     createdBy:"",
//     segment:"",
//     profitCenterGroup:"",
//   },
//   BankKey: {
//     bankCtrRegion:  {
//       code: "",
//       desc: "",
//     },
//     bankKey:"",
//     bankName:"",
//     bankBranch:"",
//     swiftBic:"",
//     bankNumber:"",
//     createdBy:"",
//     changedBy:"",
//     createdOn:""
//   },
//   GeneralLedger :{
//     chartOfAccount:{
//       code:"",
//       desc:""
//     },
//     companyCode :{
//       code:"",
//       desc:""
//     },
//     glAccount:{
//       code:"",
//       desc:""
//     },
//     glAccountType:{
//       code:"",
//       desc:""
//     },
//     accountGroup:{
//       code:"",
//       desc:""
//     },
//     shortText:"",
//     createdBy: "",
//     glAccountText: {
//       code:"",
//       desc:""
//     },
//     groupAccountNumber :{
//       code:"",
//       desc:""
//     },
//     accountCurrency:{
//       code:"",
//       desc:""
//     },
//     taxCategory:{
//       code:"",
//       desc:""
//     }

//   }
// };

// // Payload Format :
// // {
// //     module:"PurchaseOrder",
// //     filterData:{
// // company:"",
// // supplier:"",
// // date:[]
// //     }
// // }

// const commonFilterSlice = createSlice({
//   name: "commonFilterSlice",
//   initialState,
//   reducers: {
//     defaultFilterDataSetup(state, action) {
//       initialState = action.payload;
//       state = action.payload;
//       return state;
//     },
//     commonFilterUpdate(state, action) {
//       state[action.payload["module"]] = {
//         ...state[action.payload["module"]],
//         ...action.payload["filterData"],
//       };
//       return state;
//     },
//     commonFilterClear(state, action) {
//       const { module, days } = action.payload;
//       console.log("days",action.payload);
//       var date = new Date();
//       var backDate = new Date();
//       var presentDate = new Date();
//       backDate.setDate(backDate.getDate() - 3650);

//       // const backDate = new Date();
//       // const presentDate=new Date();
//       // if(days==100)
//       // {
//       //   backDate=new Date(date.getFullYear(), date.getMonth(), 1)
//       //   presentDate=new Date(date.getFullYear(), date.getMonth()+1, 0)
//       // }
//       // else if(days==50)
//       // {
//       //   backDate=new Date(date.getFullYear(), date.getMonth()-1, 1)
//       //   presentDate=new Date(date.getFullYear(), date.getMonth(), 0)
//       // }
//       // else if(days==150)
//       // {
//       //   backDate=startDate
//       //   presentDate=endDate
//       // }
//       // else if(days==200)
//       // {
//       //   backDate=new Date(date.getFullYear(), 0, 1)
//       //   presentDate=new Date()
//       // }
//       // else{
//       // backDate.setDate(backDate.getDate() - days);
//       // }
//       const updatedState = {
//         ...state,
//         [module]: {
//           ...initialState[module],
//         },
//       };

//       if (module) {
//         updatedState[module] = {
//           ...updatedState[module],
//           ...(module === "Dashboard"
//             ? { dashboardDate: [backDate, presentDate] }
//             : {}),
//           ...(module === "MaterialMaster"
//             ? { createdOn: [backDate, presentDate] }
//             : {}),
//           ...(module === "NewMaterial"
//             ? { createdOn: [backDate, presentDate] }
//             : {}),
//           ...(module === "DuplicateDesc"
//             ? { createdOn: [backDate, presentDate] }
//             : {}),
//             ...(module === "CostCenter"
//             ? { createdOn: [backDate, presentDate] }
//             : {}),
//             ...(module === "GeneralLedger"
//             ? { createdOn: [backDate, presentDate] }
//             : {}),
//         };
//       }

//       return updatedState;
//     },
//     selectedUserIdClear(state, action) {
//       const { module, days } = action.payload;
//       //alert("coming")
//       const updatedState = {
//         ...state,
//         [module]: {
//           ...initialState[module],
//         },
//       };

//       if (module) {
//         updatedState[module] = {
//           ...updatedState[module],
//           ...(module === "Dashboard"
//             ? { selectedusersId: [] }
//             : {}),

//         };
//       }

//       return updatedState;
//     },
//     setErrorFields: (state, action) => {
//       state.errorFields = action.payload;
//       return state;
//     },
//     clearHierarchyGroup(state, action) {
//       const { module, groupName } = action.payload;
//       if (state[module] && state[module][groupName]) {
//         state[module][groupName] = {
//           code: "",
//           desc: "",
//         };
//       }
//       return state;
//     },
//   },
//   extraReducers: (builder) => {
//     builder.addCase(fetchPresetsByModule.fulfilled, (state, action) => {
//       state[action.payload["module"].preset] = action.payload.data;
//       return state;
//     });
//     builder.addCase(fetchAllPresets.fulfilled, (state, action) => {
//       console.log(action.payload);
//     });
//     builder.addCase(savePreset.fulfilled, (state, action) => {
//       console.log(action.payload);
//     });
//   },
// });

// export const {
//   commonFilterUpdate,
//   commonFilterClear,
//   defaultFilterDataSetup,
//   setErrorFields,
//   clearHierarchyGroup,
//   selectedUserIdClear,
// } = commonFilterSlice.actions;

// export default commonFilterSlice.reducer;

// //Preset Filter Thunks

// export const fetchPresetsByModule = createAsyncThunk(
//   "preset/fetchByModule",
//   async (data) => {
//     const module = data;
//     return fetch(
//       `/${destination_ServiceRequest}/presetFilter/listOfFilters/${module}`
//     )
//       .then((res) => res.json())
//       .then((data) => {
//         return {
//           module,
//           data,
//         };
//       });
//   }
// );

// export const savePreset = createAsyncThunk(
//   "preset/savePreset",
//   async (data) => {
//     return fetch(
//       `/${destination_ServiceRequest}/presetFilter/savePresetFilter`,
//       {
//         method: "POST",
//         body: data,
//       }
//     )
//       .then((res) => res.json())
//       .then((data) => {
//         return data;
//       });
//   }
// );

// export const fetchAllPresets = createAsyncThunk("preset/fetchAll", async () => {
//   return fetch(`/${destination_ServiceRequest}/presetFilter/allListOfFilters`)
//     .then((res) => res.json())
//     .then((data) => {
//       return data;
//     });
// });

// // export function fetchPresets(){
// //   return async function fetchPresetThunk(dispatch, getState){
// //     try{
// //       const res = await fetch(`/${destination_ServiceRequest}/presetFilter/listOfFilters/{module}`);
// //       const data = await res.json()
// //       const state = getState()
// //       let tempFilterData = { ...state[], poStatus };
// //       dispatch(commonFilterUpdate({module:"PurchaseOrder",filterData:tempFilterData}));commonFilterUpdate
// //     }catch(err){

// //     }
// //   }
// // }
import { destination_ServiceRequest } from "../destinationVariables";
//   import { startDate, endDate } from "../components/common/DateRangeByQuarter";
import store from "../app/store";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

/* Setting Default Dates */
const presentDate = new Date();
const backDate = new Date();
// const appSettings=useSelector((state)=> state.appSettings["Format"])

backDate.setDate(backDate.getDate() - 3650);
const DashboardBackDate = new Date()
DashboardBackDate.setDate(DashboardBackDate.getDate() - 9000);
/* Setting Default Dates For Scheduler*/
let presentDateScheduler = new Date();
let backDateScheduler = new Date();
presentDateScheduler.setDate(presentDateScheduler.getDate() + 1);
backDateScheduler.setDate(backDateScheduler.getDate() - 7); //chnages Date ta 15/10/2024
let initialState = {
  Dashboard: {
    vendorNo: "",
    companyCode: "",
    dashboardDate: [DashboardBackDate, presentDate],
    selectedusersId: [],
    selectedRegion: "",
    selectedRequestType: [],
    selectedRequestStatus: [],
    selectedRequestTypeSLA: ["Create"],
    selectedRequestTypeInGroup: ["Create"],
    selectedRequestTypeSLATable: ["Create"],
    selectedRequestTypeRole: ["Create"],
    tab: 0,
  },
  Reports: {
    reportType: "",
    reportDate: [backDate, presentDate],
    selectedUsers: [],
    selectedStatus: [],
  },
  MaterialMaster: {
    plant: "",
    group: {
      code: "",
      desc: "",
    },
    createdBy: "",
    changedBy: "",
    createdOn: [backDate, presentDate],
    number: "",
    type: {
      code: "",
      desc: "",
    },
    salesOrg: {
      code: "",
      desc: "",
    },
    description: "",
    distributionChannel: {
      code: "",
      desc: "",
    },
  },
  ArticleMaster: {
    plant: "",
    group: {
      code: "",
      desc: "",
    },
    createdBy: "",
    changedBy: "",
    createdOn: [backDate, presentDate],
    number: "",
    type: {
      code: "",
      desc: "",
    },
    salesOrg: {
      code: "",
      desc: "",
    },
    description: "",
    distributionChannel: {
      code: "",
      desc: "",
    },
  },
  DuplicateDesc: {
    group: {
      code: "",
      desc: "",
    },
    baseUnit: {
      code: "",
      desc: "",
    },
    packagingMaterials: {
      code: "",
      desc: "",
    },
    prefix: "",
  },
  NewMaterial: {
    selectViewsValue: [],
    materialNo: "",
    sector: {
      code: "",
      desc: "",
    },
    type: {
      code: "",
      desc: "",
    },
    description: "",
    description1: "",
    copyMaterial: "",
    selectedViews: "Basic data",
    text: "",
    // plant: { selectedPlantNo },
    // salesOrg: { selectedSalesOrg },
    // distributionChannel: { selectedDistributionChannel },
    // storageLocation: { selectedStorageLocation },
    // mrpProfile: { selectedMRPProfile },
    // forecastProfile: { selectedForecastProfile },
    // warehouseNo: { selectedWarehouseNo },
    // storageType: { selectedStorageType },
  },
  DocumentManagement: {
    docName: "",
    transactionId: "",
    requestType: "",
    transactionType: [],
    docType: "",
    uploadedBy: "",
    uploadedDate: [backDate, presentDate],
  },
  Userswb: {
    // taskId:'',
    assignedTo: "",
    date: [backDate, presentDate],
    company: "",
    supplier: "",
    taskName: [],
    taskStatus: [],
    type: [],
    createdBy: "",
  },
  RequestBench: {
    requestId: "",
    childRequestId: "",
    requestType: "",
    createdBy: "",
    createdOn: [backDate, presentDate],
    reqPriority: [],
    reqStatus: [],
    tempName: [],
    userId: "",
    distChnl: "",
    plantOrg: "",
    salesOrg: "",
    controllingArea: "",
    costCenters: "",
  },
  SchedulerManager: {
    createdOnScheduler: [backDateScheduler, presentDateScheduler],
  },
  CostCenter: {
    costCenterName: "",
    costCenter: "",
    number: "",
    description: "",
    blockingStatus: [],
    street: "",
    location: "",
    personResponsible: "",
    createdBy: "",
    createdOn: [backDate, presentDate],
    userResponsible: {
      code: "",
      desc: "",
    },
    // "Profit Center":{
    //   code: "",
    //   desc: "",
    // },
    // "Functional Area": {
    //   code: "",
    //   desc: "",
    // },
    // "FERC Indicator": {
    //   code: "",
    //   desc: "",
    // },
    // "Country/Reg": {
    //   code: "",
    //   desc: "",
    // },
    // "Region": {
    //   code: "",
    //   desc: "",
    // },
    controllingArea: {
      code: "ETCA",
      desc: "ET FAMILY CO AREA",
    },
    companyCode: {
      code: "",
      desc: "",
    },
    profitCenter: {
      code: "",
      desc: "",
    },
    hierarchyArea: {
      code: "",
      desc: "",
    },
    costCenterCategory: {
      code: "",
      desc: "",
    },
  },
  CostCenterSunoco: {
    costCenterName: "",
    costCenter: "",
    number: "",
    description: "",
    blockingStatus: [],
    street: "",
    location: "",
    city: "",
    personResponsible: "",
    createdBy: "",
    createdOn: [backDate, presentDate],
    userResponsible: {
      code: "",
      desc: "",
    },
    functionalArea: {
      code: "",
      desc: "",
    },
    fercIndicator: {
      code: "",
      desc: "",
    },
    countryReg: {
      code: "",
      desc: "",
    },
    region: {
      code: "",
      desc: "",
    },
    controllingArea: {
      code: "ETCA",
      desc: "ET FAMILY CO AREA",
    },
    companyCode: {
      code: "",
      desc: "",
    },
    profitCenter: {
      code: "",
      desc: "",
    },
    hierarchyArea: {
      code: "",
      desc: "",
    },
    costCenterCategory: {
      code: "",
      desc: "",
    },
    "ProfitCenter": {
      code: "",
      desc: "",
    },
    "HierarchyArea": {
      code: "",
      desc: "",
    },
    "FunctionalArea": {
      code: "",
      desc: "",
    },
    "FERCIndicator": {
      code: "",
      desc: "",
    },
    // "CountryReg": {
    //   code: "",
    //   desc: "",
    // },
    // "Region": {
    //   code: "",
    //   desc: "",
    // },
    "Location": {
      code: "",
      desc: "",
    },
  },
  ProfitCenter: {
    controllingArea: {
      code: "ETCA",
      desc: "ET FAMILY CO AREA",
    },
    profitCenter: "",
    profitCenterName: "",
    createdBy: "",
    segment: {
      code: "",
      desc: "",
    },
    blockingStatus: "",
    profitCenterGroup: "",
    personResponsible: "",
    createdOn: [backDate, presentDate],
    street: "",
    city: "",
    region: "",
    Region: "",
    longText: "",
    companyCode: {
      code: "",
      desc: "",
    },
    country: "",
  },
  ProfitCenterSunoco: {
    controllingArea: {
      code: "ETCA",
      desc: "ET FAMILY CO AREA",
    },
    profitCenter: "",
    profitCenterName: "",
    createdBy: "",
    segment: "",
    blockingStatus: "",
    profitCenterGroup: "",
    personResponsible: "",
    createdOn: [backDate, presentDate],
    street: "",
    city: "",
    region: {
      code: "",
      desc: "",
    },
    longText: "",
    companyCode: {
      code: "",
      desc: "",
    },
    country: {
      code: "",
      desc: "",
    },
  },
  BankKey: {
    bankCtrRegion: {
      code: "",
      desc: "",
    },
    bankKey: "",
    bankName: "",
    bankBranch: "",
    swiftBic: "",
    bankNumber: "",
    createdBy: "",
    changedBy: "",
    createdOn: "",
  },
  GeneralLedger: {
    chartOfAccount: {
      code: "ETCN",
      desc: "ET NATURAL / OPERATIONAL CHART OF ACCOUNTS",
    },
    companyCode: {
      code: "",
      desc: "",
    },
    glAccount: {
      code: "",
      desc: "",
    },
    glAccountType: {
      code: "",
      desc: "",
    },
    accountGroup: {
      code: "",
      desc: "",
    },
    fieldStatusGroup: {
      code: "",
      desc: "",
    },
    //openItemMgmtbyLedgerGroup: "",
    openItemMgmtByLedgerGroup: "",
    openItemManagement: "",
    reconAccountforAcctType: {
      code: "",
      desc: "",
    },
    shortText: "",
    createdBy: "",
    glAcctLongText: "",
    postingWithoutTaxAllowed: "",
    blockedForPostingInCOA: "",
    blockedForPostingInCompany: "",
    glAccountText: {
      code: "",
      desc: "",
    },
    groupAccountNumber: {
      code: "",
      desc: "",
    },
    accountCurrency: {
      code: "",
      desc: "",
    },
    taxCategory: {
      code: "",
      desc: "",
    },
    "Tax Category": {
      code: "",
      desc: "",
    },
    // "Account Group": {
    //   code: "",
    //   desc: "",
    // },
    "Short Text": {
      code: "",
      desc: "",
    },
    // "G/L Account Type": {
    //   code: "",
    //   desc: "",
    // },
    // "Recon Account for Acct Type": {
    //   code: "",
    //   desc: "",
    // },
    createdOn: [backDate, presentDate],
  },
  SunocoCCPC: {
    costCenterNameCC: "",
    costCenterCC: "",
    numberCC: "",
    descriptionCC: "",
    blockingStatusCC: {
      code: "",
    },
    streetCC: "",
    locationCC: "",
    personResponsibleCC: "",
    createdOnCC: [backDate, presentDate],
    userResponsibleCC: {
      code: "",
      desc: "",
    },
    functionalArea: {
      code: "",
      desc: "",
    },
    fercIndicator: {
      code: "",
      desc: "",
    },
    countryReg: {
      code: "",
      desc: "",
    },
    region: {
      code: "",
      desc: "",
    },
    controllingAreaCC: {
      code: "ETCA",
      desc: "ET FAMILY CO AREA",
    },
    companyCodeCC: {
      code: "",
      desc: "",
    },
    profitCenter: {
      code: "",
      desc: "",
    },
    ProfitCenter: {
      code: "",
      desc: "",
    },
    hierarchyAreaCC: {
      code: "",
      desc: "",
    },
    costCenterCategoryCC: {
      code: "",
      desc: "",
    },
    "Cost Center Name": {
      code: "",
      desc: "",
    },
    // "Hierarchy Area": {
    //   code: "",
    //   desc: "",
    // },
    // "Functional Area": {
    //   code: "",
    //   desc: "",
    // },
    // "FERC Indicator": {
    //   code: "",
    //   desc: "",
    // },
    // "Country/Reg": {
    //   code: "",
    //   desc: "",
    // },
    // "Region": {
    //   code: "",
    //   desc: "",
    // },
    // "Location": {
    //   code: "",
    //   desc: "",
    // },
    createdBy: "",
    controllingAreaPC: {
      code: "ETCA",
      desc: "ET FAMILY CO AREA",
    },
    profitCenterPC: "",
    profitCenterNamePC: "",
    createdByPC: "",
    segmentPC: "",
    profitCenterGroupPC: "",
    personResponsiblePC: "",
    createdOnPC: [backDate, presentDate],
    streetPC: "",
    cityPC: "",
    regionPC: {
      code: "",
      desc: "",
    },
    longTextPC: "",
    companyCodePC: {
      code: "",
      desc: "",
    },
    countryRegionPC: {
      code: "",
      desc: "",
    },
  },

  HierarchyNodeProfitCenter: {
    controllingArea: {
      code: "ETCA",
      desc: "ET FAMILY CO AREA",
    },
    profitCenterGroup: {
      code: "",
      desc: "",
    },
  },
  HierarchyNodeCostCenter: {
    controllingArea: {
      code: "ETCA",
      desc: "ET FAMILY CO AREA",
    },
    costCenterGroup: {
      code: "",
      desc: "",
    },
  },
  HierarchyNodeGeneralLedger: {
    chartOfAccount: {
      code: "ETCN",
      desc: "ET NATURAL / OPERATIONAL CHART OF ACCOUNTS",
    },
    controllingArea: {
      code: "ETCA",
      desc: "ET FAMILY CO AREA",
    },
    costElementGroup: {
      code: "",
      desc: "",
    },
  },
};

// Payload Format :
// {
//     module:"PurchaseOrder",
//     filterData:{
// company:"",
// supplier:"",
// date:[]
//     }
// }

const commonFilterSlice = createSlice({
  name: "commonFilterSlice",
  initialState,
  reducers: {
    defaultFilterDataSetup(state, action) {
      initialState = action.payload;
      state = action.payload;
      return state;
    },
    commonFilterUpdate(state, action) {
      state[action.payload["module"]] = {
        ...state[action.payload["module"]],
        ...action.payload["filterData"],
      };
      return state;
    },
    commonFilterClear(state, action) {
      const { module, days } = action.payload;
      var date = new Date();
      const backDate = new Date();
      const presentDate = new Date();
      backDate.setDate(backDate.getDate() - 3650);

      const updatedState = {
        ...state,
        [module]: {
          ...initialState[module],
        },
      };

      if (module) {
        updatedState[module] = {
          ...updatedState[module],
          ...(module === "Dashboard"
            ? { dashboardDate: [backDate, presentDate] }
            : {}),
          ...(module === "Reports"
            ? { reportDate: [backDate, presentDate] }
            : {}),
          ...(module === "MaterialMaster"
            ? { createdOn: [backDate, presentDate] }
            : {}),
          ...(module === "ArticleMaster"
            ? { createdOn: [backDate, presentDate] }
            : {}),
          ...(module === "NewMaterial"
            ? { createdOn: [backDate, presentDate] }
            : {}),
          ...(module === "DuplicateDesc"
            ? { createdOn: [backDate, presentDate] }
            : {}),
          ...(module === "CostCenter"
            ? { createdOn: [backDate, presentDate] }
            : {}),
          ...(module === "SchedulerManager"
            ? { createdOn: [backDateScheduler, presentDateScheduler] }
            : {}),
          ...(module === "GeneralLedger"
            ? { createdOn: [backDate, presentDate] }
            : {}),
        };
      }

      return updatedState;
    },
    selectedUserIdClear(state, action) {
      const { module, days } = action.payload;
      const updatedState = {
        ...state,
        [module]: {
          ...initialState[module],
        },
      };

      if (module) {
        updatedState[module] = {
          ...updatedState[module],
          ...(module === "Dashboard"
            ? { selectedusersId: [] }
            : {}),
        };
      }

      return updatedState;
    },
    setErrorFields: (state, action) => {
      state.errorFields = action.payload;
      return state;
    },
    clearHierarchyGroup(state, action) {
      const { module, groupName } = action.payload;
      if (state[module] && state[module][groupName]) {
        state[module][groupName] = {
          code: "",
          desc: "",
        };
      }
      return state;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchPresetsByModule.fulfilled, (state, action) => {
      state[action.payload["module"].preset] = action.payload.data;
      return state;
    });
    builder.addCase(fetchAllPresets.fulfilled, (state, action) => {
      console.log(action.payload);
    });
    builder.addCase(savePreset.fulfilled, (state, action) => {
      console.log(action.payload);
    });
  },
});

export const {
  commonFilterUpdate,
  commonFilterClear,
  defaultFilterDataSetup,
  setErrorFields,
  selectedUserIdClear,
  clearHierarchyGroup,
} = commonFilterSlice.actions;

export default commonFilterSlice.reducer;

//Preset Filter Thunks

export const fetchPresetsByModule = createAsyncThunk(
  "preset/fetchByModule",
  async (data) => {
    const module = data;
    return fetch(
      `/${destination_ServiceRequest}/presetFilter/listOfFilters/${module}`
    )
      .then((res) => res.json())
      .then((data) => {
        return {
          module,
          data,
        };
      });
  }
);

export const savePreset = createAsyncThunk(
  "preset/savePreset",
  async (data) => {
    return fetch(
      `/${destination_ServiceRequest}/presetFilter/savePresetFilter`,
      {
        method: "POST",
        body: data,
      }
    )
      .then((res) => res.json())
      .then((data) => {
        return data;
      });
  }
);

export const fetchAllPresets = createAsyncThunk("preset/fetchAll", async () => {
  return fetch(`/${destination_ServiceRequest}/presetFilter/allListOfFilters`)
    .then((res) => res.json())
    .then((data) => {
      return data;
    });
});

// export function fetchPresets(){
//   return async function fetchPresetThunk(dispatch, getState){
//     try{
//       const res = await fetch(`/${destination_ServiceRequest}/presetFilter/listOfFilters/{module}`);
//       const data = await res.json()
//       const state = getState()
//       let tempFilterData = { ...state[], poStatus };
//       dispatch(commonFilterUpdate({module:"PurchaseOrder",filterData:tempFilterData}));commonFilterUpdate
//     }catch(err){

//     }
//   }
// }
