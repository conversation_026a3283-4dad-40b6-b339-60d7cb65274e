import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  destination_ProfitCenter_Mass,
} from "../../../destinationVariables";
import { v4 as uuidv4 } from "uuid";
import { doAjax } from "../../Common/fetchService";
import useProfitCenterFieldConfig from "@hooks/UseProfitCenterFieldConfig";
import useButtonDTConfig from "@hooks/useButtonDTConfig";
import ReusableBackDrop from "../../Common/ReusableBackDrop";
import { DataGrid } from "@mui/x-data-grid";
import ReusableDataTable from "../../Common/ReusableTable"
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { useLocation, useNavigate } from "react-router-dom";
import {
  TextField,
  IconButton,
  Box,
  Typography,
  Paper,
  Button,
  Tabs,
  Tab,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Radio,
  RadioGroup,
  FormControlLabel,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Snackbar,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Grid,
  Checkbox
} from "@mui/material";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";

import { Add } from "@mui/icons-material";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import TaskAltIcon from "@mui/icons-material/TaskAlt";
import CountryDropdown from "../../Common/CountryDropdown";
import {
  setProfitCenterRows,
  setProfitCenterTabs,
  setSelectedRowId,
} from "../../../App/redux/profitCenterTabSlice";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import GenericTabsGlobal from "../../MasterDataCockpit/GenericTabsGlobal";
import { colors } from "@constant/colors";
import { setPCRows, updateModuleFieldData } from "@app/profitCenterTabsSlice";
import { createPayloadForPC } from "../../../functions";
import BottomNavGlobal from "./BottomNavGlobal";
import { setDependentDropdown } from "@app/dropDownDataSlice";

const RequestHeaderDetails = ({ reqBench, apiResponses }) => {
  // console.log("apiresponses",apiResponses)
  const dispatch = useDispatch();
  const navigate = useNavigate();

  let task = useSelector((state) => state?.userManagement.taskData);


  const { selectedRowId, tabs } = useSelector((state) => state.profitCenterTab);

  const profitCenterTabs = useSelector((state) => {
    const tabs = state.profitCenter.profitCenterTabs || [];
    return tabs.filter((tab) => tab.tab !== "Initial Screen");
  });
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  const isrequestId = queryParams.get("RequestId");

  const requestHeaderSlice = useSelector((state) => state.request.requestHeader)
  const allDropDownData = useSelector((state) => state.AllDropDown?.dropDown || {});

  const requestHeaderData = useSelector((state) => state.requestHeader);
  const { loading, error, fetchProfitCenterFieldConfig } =
    useProfitCenterFieldConfig();
  const pcRows = useSelector((state) => state.profitCenter.payload.rowsHeaderData);
  const [rows, setRows] = useState(pcRows || []);

  const reduxPayload = useSelector((state) => state.profitCenter.payload);
  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons)

  const [selectedRow, setSelectedRow] = useState(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [rowTabData, setRowTabData] = useState({});
  const [dropdownDataCompany, setDropdownDataCompany] = useState([]);
  const [dropdownDataTaxJur, setDropdownDataTaxJur] = useState([]);
  const [dropdownDataProfitCtrGrp, setDropdownDataProfitCtrGrp] = useState([]);
  const [dropdownDataFormPlanning, setDropdownDataFormPlanning] = useState([]);
  const [dropdownDataCOA, setDropdownDataCOA] = useState([]);
  const [dropdownDataCountry, setDropdownDataCountry] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState("");
  const [dropdownDataRegion, setDropdownDataRegion] = useState([]);
  const [dropdownDataSegment, setDropdownDataSegment] = useState([]);
  const [dropdownDataLanguage, setDropdownDataLanguage] = useState([]);
  const [missingFieldsDialogOpen, setMissingFieldsDialogOpen] = useState(false);
  const [missingFields, setMissingFields] = useState([]);
  const [isAddRowEnabled, setIsAddRowEnabled] = useState(false);
  const [isSaveAsDraftEnabled, setIsSaveAsDraftEnabled] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [openSnackBar, setOpenSnackBar] = useState(false);
  const [alertMsg, setAlertMsg] = useState('');
  const [alertType, setAlertType] = useState('success'); // 'success' or 'error'
  const [isLoading, setIsLoading] = useState(false);
  const [validatedRows, setValidatedRows] = useState({});
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const { getButtonsDisplayGlobal } = useButtonDTConfig();

  useEffect(() => {
    if(!profitCenterTabs?.length) {
      fetchProfitCenterFieldConfig();
    }
  }, []);

    useEffect(() => {
      if((task?.ATTRIBUTE_1 || isrequestId)) {
        getButtonsDisplayGlobal("ET PC", "MDG_DYN_BTN_DT", "v2");
      }
    }, [task]);

  const columns = [
    {
      field: "included",
      headerName: "Included",
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => 
        <Checkbox 
          checked={params.row.included} 
          disabled={false} 
          onChange={(e) =>
            handleRowInputChange(e.target.checked, params.row.id, "included")
          } 
          />,
    },
    {
      field: "lineNumber",
      headerName: "Line Number",
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        const rowIndex = pcRows.findIndex(row => row.id === params.row.id);
        return <div>{rowIndex + 1}</div>;
      }
    },
    {
      field: "controllingArea",
      headerName: "Controlling Area",
      flex: 1,
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.["COArea"] || []}
            value={params.row.controllingArea}
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "controllingArea")
            }
            placeholder="Select Controlling Area"
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "companyCode",
      headerName: "Company Code",
      flex: 1,
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={dropdownDataCompany || []}
            value={params.row.companyCode}
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "companyCode")
            }
            placeholder="Select Company Code"
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "profitCenterNumber",
      headerName: "Profit Center Number",
      flex: 1,
      renderCell: (params) => {
        const value = params.row.profitCenterNumber || "";
        const isInvalid = value.length > 0 && value.length < 10;
    
        const handleKeyDown = (e) => {
          // Allow navigation & control keys
          if (
            ["Backspace", "Delete", "Tab", "Escape", "Enter", "ArrowLeft", "ArrowRight"].includes(
              e.key
            )
          ) {
            return;
          }
    
          // Block if not a digit
          if (!/^\d$/.test(e.key)) {
            e.preventDefault();
          }
        };
    
        const handlePaste = (e) => {
          const paste = e.clipboardData.getData("text");
          if (!/^\d+$/.test(paste)) {
            e.preventDefault(); // block paste if not all digits
          }
        };
    
        return (
          <TextField
            value={value}
            onChange={(e) => {
              const numericValue = e.target.value.slice(0, 10); // only digits allowed by keyboard/paste
              handleRowInputChange(numericValue, params.row.id, "profitCenterNumber");
            }}
            onKeyDown={handleKeyDown}
            onPaste={handlePaste}
            variant="outlined"
            size="small"
            placeholder="Enter Profit Center Number"
            fullWidth
            error={isInvalid}
            helperText={isInvalid ? "Number should be 10 digits" : ""}
            inputProps={{
              inputMode: "numeric",
              maxLength: 10,
            }}
            sx={{
              "& .MuiInputBase-root.Mui-disabled": {
                "& > input": {
                  WebkitTextFillColor: colors.black.dark,
                  color: colors.black.dark,
                },
              },
            }}
          />
        );
      },
    },
    {
      field: "longDescription",
      headerName: "Long Description",
      flex: 1,
      renderCell: (params) => {
        const value = params.row.longDescription || "";
    
        return (
          <TextField
            value={value}
            onChange={(e) => {
              handleRowInputChange(e.target.value.toUpperCase(), params.row.id, "longDescription");
            }}
            variant="outlined"
            size="small"
            placeholder="Enter Long Description"
            fullWidth
            sx={{
              "& .MuiInputBase-root.Mui-disabled": {
                "& > input": {
                  WebkitTextFillColor: colors.black.dark,
                  color: colors.black.dark,
                },
              },
            }}
          />
        );
      },
    },
    {
      field: "businessSegment",
      headerName: "Business Segment",
      flex: 1,
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={[{code: "CRUDE", desc: ""}, {code: "INTERSTATE", desc: ""}] || []}
            value={params.row.businessSegment}
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "businessSegment")
            }
            placeholder="Select Business Segment"
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      width: 150,
      renderHeader: () => (
        <span style={{ fontWeight: "bold" }}>Action</span>
      ),
      renderCell: (params) => (
        <Box>
          <IconButton
            onClick={() => handleValidate(params.row)}
            color={validatedRows[params.row.id] ? "success" : "default"}
            size="small"
            sx={{ mr: 1 }}
          >
            <TaskAltIcon />
          </IconButton>
          <IconButton
            onClick={() => handleDelete(params.row.id)}
            color="error"
            size="small"
          >
            <DeleteOutlineOutlinedIcon />
          </IconButton>
        </Box>
      ),
    },
  ];

  const mandatoryFields = ["controllingArea", "profitCenterNumber"];

  const handleSnackBarClose = () => {
    setOpenSnackBar(false);
  };

  useEffect(() => {
    const allRowsValidated = pcRows.length > 0 && 
      pcRows.every(row => validatedRows[row.id] === true);
    setIsAddRowEnabled(allRowsValidated);
    setIsSaveAsDraftEnabled(allRowsValidated);
  }, [pcRows, validatedRows]);

  const checkDuplicateCheckProfitCenter = (row) => {
    console.log(row, "roedata");
  
    setIsLoading(true);

    const hSuccess = (data) => {
      setIsLoading(false);
      if (data?.body?.length > 0) {
        setAlertType('error');
        setAlertMsg('Duplicate Profit Center found.');
        // Mark this row as not validated
        setValidatedRows(prev => ({...prev, [row.id]: false}));
      } else {
        setAlertType('success');
        setAlertMsg('Validation Successful');
        // Mark this row as validated
        setValidatedRows(prev => ({...prev, [row.id]: true}));
      }
      setOpenSnackBar(true);
    };

    const hError = (error) => {
      setIsLoading(false);
      console.error(error);
      setAlertType('error');
      setAlertMsg('An error occurred during validation.');
      setOpenSnackBar(true);
      // Mark this row as not validated on error
      setValidatedRows(prev => ({...prev, [row.id]: false}));
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${row?.controllingArea?.code}$$${row?.profitCenterNumber}`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleValidate = (row) => {
    const missing = [];
    mandatoryFields.forEach((field) => {
      if (!row[field]) {
        missing.push(field);
      }
    });

    if (missing.length > 0) {
      setMissingFields(missing);
      setMissingFieldsDialogOpen(true);
      setValidatedRows(prev => ({...prev, [row.id]: false}));
    } else {
      checkDuplicateCheckProfitCenter(row);
    }
  };

  const handleMassValidate = (row) => {
    const missing = [];
    mandatoryFields.forEach((field) => {
      if (!row[field]) {
        missing.push(field);
      }
    });

    if (missing.length > 0) {
      setMissingFields(missing);
      setMissingFieldsDialogOpen(true);
      setValidatedRows(prev => ({...prev, [row.id]: false}));
      return false;
    } else {
      return new Promise((resolve) => {
        const hSuccess = (data) => {
          if (data?.body?.length > 0) {
            setValidatedRows(prev => ({...prev, [row.id]: false}));
            resolve(false);
          } else {
            setValidatedRows(prev => ({...prev, [row.id]: true}));
            resolve(true);
          }
        };

        const hError = () => {
          setValidatedRows(prev => ({...prev, [row.id]: false}));
          resolve(false);
        };

        doAjax(
          `/${destination_ProfitCenter_Mass}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${row?.controllingArea?.code}$$${row?.profitCenterNumber}`,
          "get",
          hSuccess,
          hError
        );
      });
    }
  };

  const validateAllRows = () => {
    setLoaderMessage("Validating all rows...");
    setBlurLoading(true);
    
    Promise.all(pcRows?.map(row => handleMassValidate(row)))
      .then(results => {
        setBlurLoading(false);
        if (results.every(result => result === true)) {
          setAlertType('success');
          setAlertMsg('Validation Successful');
        } else {
          setAlertType('error');
          setAlertMsg('Validation Failed');
        }
        setOpenSnackBar(true);
      });
  };

  const handleCloseDialog = () => {
    setMissingFieldsDialogOpen(false);
  };
  console.log(rowTabData, "rowTabsData");

  const handleDelete = (id) => {
    // setRows(pcRows.filter((row) => row.id !== id));
    const updatedRows = pcRows.filter((row) => row.id !== id);
    dispatch(setPCRows(updatedRows))
    if (selectedRow?.id === id) {
      setSelectedRow(null);
    }
    const updatedRowTabData = { ...rowTabData };
    delete updatedRowTabData[id];
    setRowTabData(updatedRowTabData);
    
    setValidatedRows(prev => {
      const updated = {...prev};
      delete updated[id];
      return updated;
    });
  };

  const handleRowInputChange = (value, id, field) => {
    if(field === "controllingArea") {
      getProfitCenterGrp(value?.code);
    }
    if(field === "companyCode") {
      dispatch(
        updateModuleFieldData({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "CompanyCode",
          data: value?.code,
          viewID: "Comp Codes"
        })
      );
    }
    if(field === "longDescription") {
      dispatch(
        updateModuleFieldData({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "Description",
          data: value,
          viewID: "Basic Data"
        })
      );
    }
    const updatedRows = 
    pcRows.map((row) => (row.id === id ? { ...row, [field]: value } : row));
    dispatch(setPCRows(updatedRows))

  };

  const handleRowClick = (params) => {
    const clickedRow = params.row;
    setSelectedRow(clickedRow);
    dispatch(setSelectedRowId(clickedRow?.ProfitCenterID));
  };

  const handleAddRow = () => {
    const id = uuidv4();
    const newRow = {
      id,
      controllingArea: "",
      profitCenterNumber: "",
      longDescription: "",
      businessSegment: "",
      companyCode: "",
      included: true, 
      isNew: true
    };
    dispatch(setPCRows([...pcRows, newRow]))
    setValidatedRows(prev => ({...prev, [newRow.id]: false}));
  };

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  useEffect(() => {
    setSelectedRow(pcRows[0])
    getCompanyCode();
  }, []);

  const getCompanyCode = () => {
    const hSuccess = (data) => {
      setDropdownDataCompany(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "CompanyCode", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCompCode?rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };

  const getProfitCenterGrp = (coa) => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "PrctrHierGrp",
          data: data?.body || [],
          keyName2 : selectedRowId || selectedRow?.id,
        })
      )
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCtrGroup?controllingArea=${coa}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getFormPlanningFrp = () => {
    const hSuccess = (data) => {
      setDropdownDataFormPlanning(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Template", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getFormPlanningTemp`,
      "get",
      hSuccess,
      hError
    );
  };

  const getControllingArea = () => {
    const hSuccess = (data) => {
      setDropdownDataCOA(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "COArea", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getControllingArea`,
      "get",
      hSuccess,
      hError
    );
  };

  const getTaxJurisdiction = () => {
    const hSuccess = (data) => {
      setDropdownDataTaxJur(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "TaxJurisdiction", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getJurisdiction`,
      "get",
      hSuccess,
      hError
    );
  };

  const getBusSeg = () => {
    const hSuccess = (data) => {
      setDropdownDataCOA(data.body);
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getBusinessSegment`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    getCountryData();
    getProfitCenterGrp();
    getFormPlanningFrp();
    getControllingArea();
    getTaxJurisdiction();
    getBusSeg()
  }, []);

  const getCountryData = () => {
    const hSuccess = (data) => {
      setDropdownDataCountry(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Country", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCountryOrReg`,
      "get",
      hSuccess,
      hError
    );
  };
  const handleCountryChange = (e, fieldData) => {
    const selectedCountryCode = e.target.value;
    setSelectedCountry(selectedCountryCode);
    const rowId = selectedRow?.id;
    if (!rowId) return;
    getRegionBasedOnCountry(selectedCountryCode, fieldData, rowId);
  };

  const [rowRegionData, setRowRegionData] = useState({});

  const getRegionBasedOnCountry = (countryCode, fieldData, rowId) => {
    const hSuccess = (data) => {
      // Store region data for this specific row
      setRowRegionData(prev => ({
        ...prev,
        [rowId]: data.body
      }));
      
      // Also update the general dropdown data for UI rendering
      setDropdownDataRegion(data.body);
      
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Region", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getRegionBasedOnCountry?country=${countryCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getSegment = () => {
    const hSuccess = (data) => {
      setDropdownDataSegment(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Segment", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getSegment`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getSegment();
  }, []);

  const getLanguage = () => {
    const hSuccess = (data) => {
      setDropdownDataLanguage(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Language", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getLanguage();
  }, []);

  const handleSaveAsDraft = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForPC(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("Profit Centers Submission saved as draft.");
      setOpenSnackBar(true);
      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
      console.error("Error saving draft:", error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );

  };

  const handleSubmitForReview = () => {
    setLoaderMessage("");
    setBlurLoading(true);
    const finalPayload = createPayloadForPC(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(
        "Profit Centers submission for save as draft initiated"
      );
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
      console.error("Error saving draft:", error);
    };

    // 🔄 API call
    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersSubmitForReview`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );

    console.log("finalPayload", finalPayload);
  };

  const handleValidateAndSyndicate= (type) => {
    setBlurLoading(true);
    const finalPayload = createPayloadForPC(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      if(type === "validate")
      setAlertMsg("Profit Centers Validation initiated");
      else if(type === "syndicate")
      setAlertMsg("Profit Centers Syndication initiated");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while validating the request");
      console.error("Error saving draft:", error);
    };

    doAjax(
      type === "validate" ? `/${destination_ProfitCenter_Mass}/massAction/validateMassProfitCenter` : `/${destination_ProfitCenter_Mass}/massAction/createProfitCentersApproved`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );

    console.log("finalPayload", finalPayload);
  };

  const handleSubmitForApprove= () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForPC(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(
        "Profit Centers submission for Approve initiated"
      );
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
      console.error("Error saving draft:", error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersApprovalSubmit`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );

    console.log("finalPayload", finalPayload);
  };

  const handleSendBack = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForPC(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Profit Centers submission for Approve initiated");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
      console.error("Error saving draft:", error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersSendForCorrection`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const handleCorrection = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForPC(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(data?.message ?? "Profit Centers Sent for Correction !");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while sending for correction");
      console.error("Error saving draft:", error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersSendForReview`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const handleRejectAndCancel = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForPC(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(data?.message ?? "Profit Centers Rejected !");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while rejecting the request");
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersRejected`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      <ReusableSnackBar
        openSnackBar={openSnackBar}
        alertMsg={alertMsg}
        handleSnackBarClose={handleSnackBarClose}
        alertType={alertType}
        isLoading={isLoading}
      />

      {error && <Typography color="error">Error loading data</Typography>}

      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
      <Typography gutterBottom sx={{ fontWeight: "bold", fontSize: "16px", mb: -2 }}>
        List of Profit Centers
      </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<Add />}
          onClick={handleAddRow}
          disabled={!isAddRowEnabled}
          sx={{ mb: 1}}
        >
          Add
        </Button>
      </Box>

      <div>
        <ReusableDataTable
          isLoading={loading}
          rows={pcRows}
          columns={columns}
          pageSize={10}
          tempheight={'50vh'}
          getRowIdValue={"id"}
          status_onRowSingleClick={true}
          callback_onRowSingleClick={handleRowClick}
          getRowClassName={(params) =>
            selectedRow?.id === params.row.id ? "Mui-selected" : ""
          }
        />
      </div>
    

      <Dialog
        open={missingFieldsDialogOpen}
        onClose={handleCloseDialog}
        aria-labelledby="missing-fields-dialog-title"
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          id="missing-fields-dialog-title"
          sx={{
            backgroundColor: "#fff3e0",
            color: "#e65100",
            display: "flex",
            alignItems: "center",
            gap: 1,
            fontWeight: "bold"
          }}
        >
          <WarningAmberIcon fontSize="medium" />
          Missing Mandatory Fields
        </DialogTitle>

        <DialogContent sx={{ pt: 2 }}>
          <Typography variant="body1" gutterBottom>
            Please complete the following mandatory fields:
          </Typography>
          <List dense>
            {missingFields.map((field, index) => (
              <ListItem key={index} disablePadding>
                <ListItemIcon sx={{ minWidth: 30 }}>
                  <WarningAmberIcon fontSize="small" color="warning" />
                </ListItemIcon>
                <ListItemText primary={field} />
              </ListItem>
            ))}
          </List>
        </DialogContent>

        <DialogActions sx={{ pr: 3, pb: 2 }}>
          <Button
            onClick={handleCloseDialog}
            variant="contained"
            color="warning"
            sx={{ textTransform: "none", fontWeight: 500 }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {selectedRow &&
        ((reqBench === "true" && selectedRowId) ? (
          <Box sx={{ mt: 3 }}>
            <Tabs
              value={selectedTab}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              aria-label="Request tabs"
              variant="scrollable"
              scrollButtons="auto"
              sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
            >
              {profitCenterTabs.map((tab, index) => (
                <Tab key={index} label={tab.tab} />
              ))}
            </Tabs>
            <Paper elevation={2} sx={{ p: 3, borderRadius: 4 }}>
              {profitCenterTabs[selectedTab] && (
                <GenericTabsGlobal
                  disabled={false}
                  basicDataTabDetails={profitCenterTabs[selectedTab].data}
                  dropDownData={{
                    "CompanyCode": dropdownDataCompany,
                    "Country": dropdownDataCountry,
                    "Segment": dropdownDataSegment,
                    "Language": dropdownDataLanguage,
                    "Template": dropdownDataFormPlanning,
                    "COArea": dropdownDataCOA,
                    "TaxJurisdiction": dropdownDataTaxJur,
                  }}
                  activeViewTab={profitCenterTabs[selectedTab].tab}
                  uniqueId={selectedRow?.id || selectedRowId || pcRows[0]?.id}
                  selectedRow={selectedRow || {}}
                />
              )}
            </Paper>
          </Box>
        ) : (
          <Box sx={{ mt: 3 }}>
            <Tabs
              value={selectedTab}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              aria-label="Request tabs"
              variant="scrollable"
              scrollButtons="auto"
              sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
            >
              {profitCenterTabs.map((tab, index) => (
                <Tab key={index} label={tab.tab} />
              ))}
            </Tabs>
            <Paper elevation={2} sx={{ p: 3, borderRadius: 4 }}>
              {profitCenterTabs[selectedTab] && (
                <GenericTabsGlobal
                  disabled={false}
                  basicDataTabDetails={profitCenterTabs[selectedTab].data}
                  dropDownData={{
                    "CompanyCode": dropdownDataCompany,
                    "Country": dropdownDataCountry,
                    "Segment": dropdownDataSegment,
                    "Language": dropdownDataLanguage,
                    "Template": dropdownDataFormPlanning,
                    "COArea": dropdownDataCOA,
                    "TaxJurisdiction": dropdownDataTaxJur,
                  }}
                  activeViewTab={profitCenterTabs[selectedTab].tab}
                  uniqueId={selectedRow?.id || selectedRowId || pcRows[0]?.id}
                  selectedRow={selectedRow || {}}
                />
              )}
            </Paper>
          </Box>
        ))
      }

      <BottomNavGlobal 
        handleSaveAsDraft={handleSaveAsDraft}
        handleSubmitForReview={handleSubmitForReview}
        handleSubmitForApprove={handleSubmitForApprove}
        handleSendBack={handleSendBack}
        handleCorrection={handleCorrection}
        handleRejectAndCancel={handleRejectAndCancel}
        handleValidateAndSyndicate={handleValidateAndSyndicate}
        validateAllRows={validateAllRows}
        isSaveAsDraftEnabled={isSaveAsDraftEnabled}
        filteredButtons={filteredButtons}
      />
      <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
    </Box>
  );
};

export default RequestHeaderDetails;
