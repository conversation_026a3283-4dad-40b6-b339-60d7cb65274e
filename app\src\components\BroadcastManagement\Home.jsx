import React, { useState, useEffect, useRef, useCallback } from 'react';
import YouTube from 'react-youtube';
import {
  Box,
  Card,
  CardContent,
  CardMedia,
  Grid,
  Chip,
  Avatar,
  IconButton,
  Button,
  Container,
  Paper,
  Typography,
  Fab,
  TextField,
  InputAdornment,
  Drawer,
  List,
  ListItem,
  Divider,
  Badge,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Slider,
  colors,
} from '@mui/material';
import {
  ChevronLeft,
  ChevronRight,
  CalendarToday,
  PlayArrow,
  Visibility,
  Star,
  TrendingUp,
  EmojiEvents,
  Business,
  VideoLibrary,
  Assessment,
  Security,
  Search,
  Close,
  FilterList,
  Schedule,
  Update,
  VolumeUp,
  VolumeOff,
  VolumeDown,
  Pause,
  Settings,
  Fullscreen,
  FullscreenExit
} from '@mui/icons-material';
import { baseUrl_Admin } from '@data/baseUrl';
import useLogger from '@hooks/useLogger';
import { HOME_CAROUSEL_BANNER } from '@constant/enum';
import AnnouncementDialog from "./AnnouncementDialog"
import useLang from "@hooks/useLang";
import noDataFound from "../../utilityImages/nonotificationfound.png";

const Home = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [hoveredAnnouncement, setHoveredAnnouncement] = useState(null);
  const [videoDrawerOpen, setVideoDrawerOpen] = useState(false);
  const [videoSearchQuery, setVideoSearchQuery] = useState('');
  const [videoCarouselIndex, setVideoCarouselIndex] = useState(0);
  const [hoveredVideo, setHoveredVideo] = useState(null);
  const [mediaPlayerOpen, setMediaPlayerOpen] = useState(false);
  const [currentVideo, setCurrentVideo] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(50);
  const [isMuted, setIsMuted] = useState(false);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [showControls, setShowControls] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [allVideos, setAllVideos] = useState([]);
  const [allAnnouncements, setAllAnnouncements] = useState([]);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState({});
  const [dialogOpen, setDialogOpen] = useState(false);
  
  const playerRef = useRef(null);
  const controlsTimeoutRef = useRef(null);
  const progressUpdateRef = useRef(null);
  const {customError, warn} = useLogger()
  const { t } = useLang();

  // Carousel data
  const carouselSlides = HOME_CAROUSEL_BANNER

  useEffect(() => {
    if (allVideos.length === 0) {
      fetchAllVideos();
    }
  }, []);

  useEffect(() => {
    if (allAnnouncements.length === 0) {
      fetchAllAnnouncements();
    }
  }, []);



  const fetchAllVideos = async () => {
    try {
      const response = await fetch(`${baseUrl_Admin}/api/videos/all`);
      if (!response.ok) throw new Error("Failed to fetch videos");
      const videos = await response.json();
      setAllVideos(videos);
    } catch (error) {
      setAllVideos([]);
    }
  };

  const fetchAllAnnouncements = async () => {
    try {
      const response = await fetch(`${baseUrl_Admin}/broadcastManagement/getAll/category/Announcements`);
      if (!response.ok) throw new Error("Failed to fetch announcements");
      const anns = await response.json();
      setAllAnnouncements(anns?.broadcastDetailsDtoList);
    } catch (error) {
      setAllAnnouncements([]);
    }
  };

  // Filter videos for carousel (first 4) and search
  const carouselVideos = allVideos.slice(0, 4);
  const filteredVideos = allVideos.filter(video =>
    video.title.toLowerCase().includes(videoSearchQuery.toLowerCase()) ||
    video.description.toLowerCase().includes(videoSearchQuery.toLowerCase()) ||
    video.category.toLowerCase().includes(videoSearchQuery.toLowerCase())
  );

  // YouTube player options
  const opts = {
    height: '100%',
    width: '100%',
    playerVars: {
      autoplay: 1,
      controls: 0,
      disablekb: 1,
      modestbranding: 1,
      rel: 0,
      showinfo: 0,
      iv_load_policy: 3,
      cc_load_policy: 0,
      fs: 0,
      playsinline: 1,
      origin: window.location.origin,
    },
  };

  // YouTube player event handlers
  const onPlayerReady = (event) => {
    playerRef.current = event.target;
    event.target.setVolume(volume);
    if (isMuted) {
      event.target.mute();
    }
    startProgressUpdate();
  };

  const onPlayerStateChange = (event) => {
    const { YT } = window;
    if (!YT) return;

    switch (event.data) {
      case YT.PlayerState.PLAYING:
        setIsPlaying(true);
        startProgressUpdate();
        break;
      case YT.PlayerState.PAUSED:
        setIsPlaying(false);
        stopProgressUpdate();
        break;
      case YT.PlayerState.ENDED:
        setIsPlaying(false);
        stopProgressUpdate();
        break;
      default:
        break;
    }
  };

  const onPlayerError = (event) => {
    customError('YouTube Player Error:', event.data);
  };

  // Progress update functions
  const startProgressUpdate = useCallback(() => {
    if (progressUpdateRef.current) {
      cancelAnimationFrame(progressUpdateRef.current);
    }

    const update = () => {
      if (playerRef.current && typeof playerRef.current.getCurrentTime === 'function') {
        try {
          const current = playerRef.current.getCurrentTime();
          const total = playerRef.current.getDuration();
          
          if (!isNaN(current) && !isNaN(total)) {
            setCurrentTime(current);
            setDuration(total);
          }
        } catch (error) {
          warn('Error updating progress:', error);
        }
      }

      if (isPlaying && playerRef.current) {
        progressUpdateRef.current = requestAnimationFrame(update);
      }
    };

    progressUpdateRef.current = requestAnimationFrame(update);
  }, [isPlaying]);

  const stopProgressUpdate = useCallback(() => {
    if (progressUpdateRef.current) {
      cancelAnimationFrame(progressUpdateRef.current);
    }
  }, []);

  // Control functions
  const handlePlayPause = () => {
    if (playerRef.current) {
      try {
        if (isPlaying) {
          playerRef.current.pauseVideo();
        } else {
          playerRef.current.playVideo();
        }
      } catch (error) {
        warn('Error toggling play:', error);
      }
    }
  };

  const handleSeek = (e) => {
    if (playerRef.current) {
      try {
        const progressBar = e.currentTarget;
        const rect = progressBar.getBoundingClientRect();
        const pos = (e.clientX - rect.left) / rect.width;
        const seekTime = pos * duration;
        
        if (!isNaN(seekTime) && duration > 0) {
          playerRef.current.seekTo(seekTime, true);
        }
      } catch (error) {
        warn('Error seeking:', error);
      }
    }
  };

  const handleVolumeChange = (event, newValue) => {
    setVolume(newValue);
    
    if (playerRef.current) {
      try {
        playerRef.current.setVolume(newValue);
        if (newValue > 0 && isMuted) {
          playerRef.current.unMute();
          setIsMuted(false);
        }
      } catch (error) {
        warn('Error setting volume:', error);
      }
    }
  };

  const handleMuteToggle = () => {
    if (playerRef.current) {
      try {
        if (isMuted) {
          playerRef.current.unMute();
          setIsMuted(false);
        } else {
          playerRef.current.mute();
          setIsMuted(true);
        }
      } catch (error) {
        warn('Error toggling mute:', error);
      }
    }
  };

  const handleSpeedChange = (rate) => {
    setPlaybackRate(rate);
    if (playerRef.current) {
      try {
        playerRef.current.setPlaybackRate(rate);
      } catch (error) {
        warn('Error setting playback rate:', error);
      }
    }
  };

  const handleFullscreenToggle = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Show/hide controls
  const showControlsTemporarily = () => {
    setShowControls(true);
    
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    
    controlsTimeoutRef.current = setTimeout(() => {
      setShowControls(false);
    }, 3000);
  };

  // Format time helper
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle video click
  const handleVideoClick = (video) => {
    setCurrentVideo(video);
    setMediaPlayerOpen(true);
    setIsPlaying(false);
    setCurrentTime(0);
    setDuration(0);
  };

  // Handle dialog close
  const handleClosePlayer = () => {
    if (playerRef.current) {
      try {
        playerRef.current.pauseVideo();
      } catch (error) {
        warn('Error pausing video:', error);
      }
    }
    
    setMediaPlayerOpen(false);
    setCurrentVideo(null);
    setIsPlaying(false);
    stopProgressUpdate();
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % carouselSlides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + carouselSlides.length) % carouselSlides.length);
  };

   useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % carouselSlides.length);
    }, 5000);
    return () => clearInterval(timer);
  }, [carouselSlides.length]);

  // Carousel navigation
  const nextVideoCarousel = () => {
    if (videoCarouselIndex < carouselVideos.length - 2) {
      setVideoCarouselIndex(prev => prev + 1);
    }
  };

  const prevVideoCarousel = () => {
    if (videoCarouselIndex > 0) {
      setVideoCarouselIndex(prev => prev - 1);
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopProgressUpdate();
    };
  }, [stopProgressUpdate]);

  const getVolumeIcon = () => {
    if (isMuted || volume === 0) return VolumeOff;
    if (volume < 50) return VolumeDown;
    return VolumeUp;
  };

  const VolumeIcon = getVolumeIcon();

  return (
    <Grid maxHeight={"100%"}>
      {/* Carousel Section */}
      <Box sx={{ position: 'relative', height: 300, overflow: 'hidden' }}>
        <Box
          sx={{
            display: 'flex',
            transition: 'transform 0.7s cubic-bezier(0.4, 0.0, 0.2, 1)',
            transform: `translateX(-${currentSlide * 100}%)`,
            height: '100%'
          }}
        >
          {carouselSlides.map((slide) => (
            <Box key={slide.id} sx={{ minWidth: '100%', position: 'relative' }}>
              <Box
                sx={{
                  position: 'absolute',
                  inset: 0,
                  background: slide.gradient,
                  opacity: 0.85
                }}
              />
              <Box
                sx={{
                  position: 'absolute',
                  inset: 0,
                  backgroundImage: `url(${slide.image})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  mixBlendMode: 'overlay'
                }}
              />
              <Container
                maxWidth="lg"
                sx={{
                  position: 'relative',
                  zIndex: 1,
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                <Box sx={{ maxWidth: '60%', color: 'white' }}>
                  <Typography
                    variant="h2"
                    component="h2"
                    sx={{
                      fontWeight: 800,
                      mb: 2,
                      fontSize: { xs: '2.5rem', md: '3.5rem' },
                      lineHeight: 1.2,
                      textShadow: '0 2px 10px rgba(0,0,0,0.3)'
                    }}
                  >
                    {t(slide.title)}
                  </Typography>
                  <Typography
                    variant="h5"
                    sx={{
                      mb: 3,
                      opacity: 0.95,
                      fontWeight: 400,
                      textShadow: '0 1px 5px rgba(0,0,0,0.3)'
                    }}
                  >
                    {t(slide.subtitle)}
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      mb: 4,
                      opacity: 0.9,
                      fontSize: '1.1rem',
                      lineHeight: 1.6,
                      textShadow: '0 1px 3px rgba(0,0,0,0.3)'
                    }}
                  >
                    {t(slide.description)}
                  </Typography>
                </Box>
              </Container>
            </Box>
          ))}
        </Box>

        {/* Carousel Controls */}
        <Fab
          size="medium"
          onClick={prevSlide}
          sx={{
            zIndex: 0,
            position: 'absolute',
            left: 20,
            top: '50%',
            transform: 'translateY(-50%)',
            background: 'rgba(255,255,255,0.2)',
            backdropFilter: 'blur(10px)',
            color: 'white',
            '&:hover': {
              background: 'rgba(255,255,255,0.3)',
              transform: 'translateY(-50%) scale(1.1)'
            },
            transition: 'all 0.3s ease'
          }}
        >
          <ChevronLeft />
        </Fab>
        <Fab
          size="medium"
          onClick={nextSlide}
          sx={{
            zIndex: 0,
            position: 'absolute',
            right: 20,
            top: '50%',
            transform: 'translateY(-50%)',
            background: 'rgba(255,255,255,0.2)',
            backdropFilter: 'blur(10px)',
            color: 'white',
            '&:hover': {
              background: 'rgba(255,255,255,0.3)',
              transform: 'translateY(-50%) scale(1.1)'
            },
            transition: 'all 0.3s ease'
          }}
        >
          <ChevronRight />
        </Fab>

        {/* Carousel Indicators */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 20,
            left: '50%',
            transform: 'translateX(-50%)',
            display: 'flex',
            gap: 1
          }}
        >
          {carouselSlides.map((_, index) => (
            <Box
              key={index}
              onClick={() => setCurrentSlide(index)}
              sx={{
                width: 12,
                height: 12,
                borderRadius: '50%',
                background: index === currentSlide ? 'white' : 'rgba(255,255,255,0.5)',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                transform: index === currentSlide ? 'scale(1.3)' : 'scale(1)',
                '&:hover': {
                  background: 'rgba(255,255,255,0.8)'
                }
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Main Content */}
        <Grid container spacing={4}  sx={{ p: 4 }}>
          
          {/* Left Section - Announcements */}
          <Grid item xs={12} md={6} xl={6} lg={6}>
            <Paper
              elevation={8}
              sx={{
                borderRadius: 4,
                overflow: 'hidden',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                mb: 3
              }}
            >
              <Box sx={{ p: 3, color: 'white' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color:"white" }}>
                      {t("Announcements")}
                    </Typography>
                    <Typography variant="body1" sx={{ opacity: 0.9 }}>
                      {t("Stay updated with latest news")}
                    </Typography>
                  </Box>
                    <Avatar
                      sx={{
                        background: 'rgba(255,255,255,0.2)',
                        backdropFilter: 'blur(10px)',
                        width: 56,
                        height: 56
                      }}
                    >
                      <Update sx={{ fontSize: 28 }} />
                    </Avatar>
                </Box>
              </Box>
            </Paper>
            
            {/* Scrollable Announcements Container */}
            <Box
              sx={{
                height: 500,
                overflowY: 'auto',
                pr: 1,
                '&::-webkit-scrollbar': {
                  width: '8px'
                },
                '&::-webkit-scrollbar-track': {
                  background: 'rgba(0,0,0,0.1)',
                  borderRadius: '4px'
                },
                '&::-webkit-scrollbar-thumb': {
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  borderRadius: '4px'
                }
              }}
            >
              { 
              allAnnouncements?.length ? (
                allAnnouncements?.map((announcement) => (
                <Card
                  key={announcement.broadcastId}
                  onMouseEnter={() => setHoveredAnnouncement(announcement.broadcastId)}
                  onMouseLeave={() => setHoveredAnnouncement(null)}
                  onClick={() => {
                    setSelectedAnnouncement(announcement)
                    setDialogOpen(true)
                  }}
                  sx={{
                    mb: 2,
                    borderRadius: 3,
                    background: hoveredAnnouncement === announcement.broadcastId ? 
                      'linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%)' : 
                      'white',
                    border: hoveredAnnouncement === announcement.broadcastId ? '2px solid #667eea' : '2px solid transparent',
                    cursor: 'pointer'
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 700,
                        color: hoveredAnnouncement === announcement.broadcastId ? '#667eea' : 'text.primary',
                        transition: 'color 0.3s ease',
                        fontSize: '1.2rem',
                        mb: 2,
                        lineHeight: 1.3
                      }}
                    >
                      {announcement.broadcastTitle}
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mb: 2, lineHeight: 1.6, fontSize: '0.95rem' }}
                    >
                      {announcement.description}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, justifyContent: 'space-between' }}>
                      <Box sx={{ display: "flex", alignItems: "center", gap: 0.5}}>
                        <CalendarToday sx={{ fontSize: 16, color: 'text.secondary' }} />
                        <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 500 }}>
                          {t("Updated on")} {announcement.startDate}
                        </Typography>
                      </Box>
                      <Chip 
                          label={announcement.module} 
                          size="small" 
                          sx={{ 
                            background: '#f5576c', 
                            color: 'white',
                            fontSize: '0.7rem',
                          }} 
                        />
                    </Box>
                  </CardContent>
                </Card>
              ))
              )
              : (
                <Box display="flex" flexDirection="column" alignItems="center" pl={5} pt={0.5}>
                  <img
                    src={noDataFound}
                    alt="No Notifications"
                    style={{ width: "500px", height: "auto", marginBottom: "16px", opacity: 0.7, borderRadius: "50px" }}
                  />
                  <Typography variant="body1" align="center" sx={{ color: colors?.secondary?.grey }}>
                    {t("No Announcements available")}
                  </Typography>
                </Box>
              )
              }

              <AnnouncementDialog
                open={dialogOpen}
                onClose={() => setDialogOpen(false)}
                announcement={selectedAnnouncement}
              />

            </Box>
          </Grid>

          {/* Right Section - Training Videos */}
          <Grid item xs={12} md={6}>
            <Paper
              elevation={8}
              sx={{
                borderRadius: 4,
                overflow: 'hidden',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                mb: 3
              }}
            >
              <Box sx={{ p: 3, color: 'white' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color:"white" }}>
                      {t("Videos")}
                    </Typography>
                    <Typography variant="body1" sx={{ opacity: 0.9 }}>
                      {t("Learn with our video library")}
                    </Typography>
                  </Box>
                  <Avatar
                    sx={{
                      background: 'rgba(255,255,255,0.2)',
                      backdropFilter: 'blur(10px)',
                      width: 56,
                      height: 56
                    }}
                  >
                    <VideoLibrary sx={{ fontSize: 28 }} />
                  </Avatar>
                </Box>
              </Box>
            </Paper>

            {/* View All Videos Button */}
            <Button
              variant="contained"
              startIcon={<VideoLibrary />}
              onClick={() => setVideoDrawerOpen(true)}
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                py: 1.5,
                borderRadius: 3,
                fontWeight: 600,
                alignItems: 'center',
                marginBottom: '20px',
                fontSize: '1rem',
                '&:hover': {
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 25px rgba(240, 147, 251, 0.4)'
                },
                transition: 'all 0.3s ease'
              }}
            >
              {t("View All Videos")} ({allVideos.length})
            </Button>
            
            {/* Carousel Container */}
            <Box sx={{ position: 'relative', mb: -5, height: 320, overflow: 'hidden' }}>
              <Box
                sx={{
                  display: 'flex',
                  transition: 'transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                  transform: `translateX(-${videoCarouselIndex * 50}%)`,
                  gap: 2,
                  height: '100%',
                  width: 'fit-content'
                }}
              >
                {carouselVideos.map((video) => (
                  <Card
                    key={video.id}
                    elevation={hoveredVideo === video.id ? 20 : 8}
                    onMouseEnter={() => setHoveredVideo(video.id)}
                    onMouseLeave={() => setHoveredVideo(null)}
                    onClick={() => handleVideoClick(video)}
                    sx={{
                      minWidth: '48%',
                      maxWidth: '48%',
                      transition: 'all 0.4s cubic-bezier(0.4, 0.0, 0.2, 1)',
                      borderRadius: 3,
                      cursor: 'pointer',
                      background: hoveredVideo === video.id ? 
                        'linear-gradient(135deg, #fff8f8 0%, #fce4ec 100%)' : 
                        'white',
                      border: hoveredVideo === video.id ? '3px solid #f5576c' : '3px solid transparent',
                      height: 'fit-content',
                      boxShadow: hoveredVideo === video.id ? 
                        '0 20px 40px rgba(245, 87, 108, 0.3)' : 
                        '0 8px 20px rgba(0,0,0,0.1)'
                    }}
                  >
                    <Box sx={{ position: 'relative' }}>
                      <CardMedia
                        component="img"
                        height="160"
                        image={`https://img.youtube.com/vi/${video.videoId}/mqdefault.jpg`}
                        alt={video.title}
                        sx={{ 
                          transition: 'all 0.3s ease',
                          filter: hoveredVideo === video.id ? 'brightness(1.1)' : 'brightness(1)'
                        }}
                      />
                      <Box
                        sx={{
                          position: 'absolute',
                          inset: 0,
                          background: hoveredVideo === video.id ? 
                            'rgba(0,0,0,0.4)' : 
                            'rgba(0,0,0,0.2)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          transition: 'all 0.3s ease',
                          opacity: hoveredVideo === video.id ? 1 : 0
                        }}
                      >
                        <Fab
                          size="large"
                          style={{
                            zIndex: "0 !important",
                            background: 'rgba(255,255,255,0.95)',
                            color: '#f5576c',
                            transform: hoveredVideo === video.id ? 'scale(1.1)' : 'scale(1)',
                            '&:hover': {
                              transform: 'scale(1.2)',
                              background: 'white'
                            }
                          }}
                        >
                          <PlayArrow sx={{ fontSize: 36 }} />
                        </Fab>
                      </Box>
                      {/* <Chip
                        label={video.duration}
                        size="small"
                        sx={{
                          position: 'absolute',
                          bottom: 8,
                          right: 8,
                          background: 'rgba(0,0,0,0.8)',
                          color: 'white',
                          fontSize: '0.7rem'
                        }}
                      /> */}
                    </Box>
                    <CardContent sx={{ p: 2 }}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          fontSize: '1rem',
                          mb: 1,
                          color: hoveredVideo === video.id ? '#f5576c' : 'text.primary',
                          transition: 'color 0.3s ease',
                          lineHeight: 1.3,
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden'
                        }}
                      >
                        {video.title}
                      </Typography>
                      {/* <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <Chip 
                          label={video.category} 
                          size="small" 
                          sx={{ 
                            background: '#f5576c', 
                            color: 'white',
                            fontSize: '0.7rem'
                          }} 
                        />
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          <Visibility sx={{ fontSize: 14, color: 'text.secondary' }} />
                          <Typography variant="caption" color="text.secondary">
                            {video.views}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          <Star sx={{ fontSize: 14, color: '#ffc107' }} />
                          <Typography variant="caption" color="text.secondary">
                            {video.rating}
                          </Typography>
                        </Box>
                      </Box> */}
                    </CardContent>
                  </Card>
                ))}
              </Box>

              {/* Carousel Navigation Buttons */}
              <IconButton
                onClick={prevVideoCarousel}
                disabled={videoCarouselIndex === 0}
                sx={{
                  position: 'absolute',
                  left: 0,
                  top: '40%',
                  transform: 'translateY(-50%)',
                  background: 'white',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                  '&:hover': {
                    background: '#f5f5f5'
                  },
                  '&:disabled': {
                    opacity: 0.3
                  }
                }}
              >
                <ChevronLeft />
              </IconButton>
              <IconButton
                onClick={nextVideoCarousel}
                disabled={videoCarouselIndex >= carouselVideos.length - 2}
                sx={{
                  position: 'absolute',
                  right: 0,
                  top: '40%',
                  transform: 'translateY(-50%)',
                  background: 'white',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                  '&:hover': {
                    background: '#f5f5f5'
                  },
                  '&:disabled': {
                    opacity: 0.3
                  }
                }}
              >
                <ChevronRight />
              </IconButton>
            </Box>
          </Grid>
        </Grid>


      {/* Video Library Drawer */}
      <Drawer
        anchor="right"
        open={videoDrawerOpen}
        onClose={() => setVideoDrawerOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: { xs: '100%', sm: 500 },
            background: 'linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%)'
          }
        }}
      >
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" sx={{ fontWeight: 700, color: '#667eea' }}>
              {t("Videos")}
            </Typography>
            <IconButton onClick={() => setVideoDrawerOpen(false)}>
              <Close />
            </IconButton>
          </Box>
          
          <TextField
            fullWidth
            variant="outlined"
            placeholder={t("Search videos")+"..."}
            value={videoSearchQuery}
            onChange={(e) => setVideoSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              )
            }}
            sx={{ mb: 3 }}
          />
          
          <List sx={{ maxHeight: 'calc(100vh - 200px)', overflow: 'auto' }}>
            {filteredVideos.map((video, index) => (
              <React.Fragment key={video.id}>
                <ListItem
                  sx={{
                    borderRadius: 2,
                    mb: 1,
                    cursor: 'pointer',
                    '&:hover': {
                      background: 'rgba(102, 126, 234, 0.1)'
                    }
                  }}
                  onClick={() => {
                    handleVideoClick(video);
                    setVideoDrawerOpen(false);
                  }}
                >
                  <Box sx={{ display: 'flex', gap: 2, width: '100%' }}>
                    <Box sx={{ position: 'relative' }}>
                      <CardMedia
                        component="img"
                        sx={{ width: 120, height: 90, borderRadius: 1 }}
                        image={`https://img.youtube.com/vi/${video.videoId}/mqdefault.jpg`}
                        alt={video.title}
                      />
                      {/* <Chip
                        label={video.duration}
                        size="small"
                        sx={{
                          position: 'absolute',
                          bottom: 4,
                          right: 4,
                          background: 'rgba(0,0,0,0.8)',
                          color: 'white',
                          fontSize: '0.6rem'
                        }}
                      /> */}
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 0.5 }}>
                        {video.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {video.description}
                      </Typography>
                      {/* <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                        <Chip label={video.category} size="small" />
                      </Box> */}
                    </Box>
                  </Box>
                </ListItem>
                {index < filteredVideos.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </Box>
      </Drawer>

      {/* Media Player Dialog */}
      <Dialog
        open={mediaPlayerOpen}
        onClose={handleClosePlayer}
        maxWidth="lg"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 3,
            background: '#000',
            overflow: 'hidden'
          }
        }}
      >
        {currentVideo && (
          <>
            <DialogTitle sx={{ color: 'white', p: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  {currentVideo.title}
                </Typography>
                <IconButton onClick={handleClosePlayer} sx={{ color: 'white' }}>
                  <Close />
                </IconButton>
              </Box>
            </DialogTitle>
            
            <DialogContent sx={{ p: 0, position: 'relative', aspectRatio: '16/9' }}>
              <Box
                sx={{ 
                  width: '100%', 
                  height: '100%',
                  position: 'relative'
                }}
                onMouseMove={showControlsTemporarily}
                onMouseEnter={() => setShowControls(true)}
                onMouseLeave={() => setShowControls(false)}
              >
                <YouTube
                  videoId={currentVideo.videoId}
                  opts={opts}
                  onReady={onPlayerReady}
                  onStateChange={onPlayerStateChange}
                  onError={onPlayerError}
                  style={{ width: '100%', height: '100%' }}
                />
                
                {/* Custom Controls */}
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    background: 'linear-gradient(transparent, rgba(0,0,0,0.8))',
                    color: 'white',
                    p: 2,
                    transform: showControls || !isPlaying ? 'translateY(0)' : 'translateY(100%)',
                    transition: 'transform 0.3s ease',
                    zIndex: 10
                  }}
                >
                  {/* Progress Bar */}
                  <Box
                    sx={{
                      height: 6,
                      background: 'rgba(255,255,255,0.3)',
                      borderRadius: 3,
                      mb: 2,
                      cursor: 'pointer',
                      overflow: 'hidden'
                    }}
                    onClick={handleSeek}
                  >
                    <Box
                      sx={{
                        height: '100%',
                        background: '#f5576c',
                        borderRadius: 3,
                        width: duration > 0 ? `${(currentTime / duration) * 100}%` : '0%',
                        transition: 'width 0.1s ease'
                      }}
                    />
                  </Box>
                  
                  {/* Control Buttons */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <IconButton onClick={handlePlayPause} sx={{ color: 'white' }}>
                      {isPlaying ? <Pause /> : <PlayArrow />}
                    </IconButton>
                    
                    <Typography variant="caption">
                      {formatTime(currentTime)} / {formatTime(duration)}
                    </Typography>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, ml: 'auto' }}>
                      <IconButton onClick={handleMuteToggle} sx={{ color: 'white' }}>
                        <VolumeIcon />
                      </IconButton>
                      
                      <Slider
                        value={volume}
                        onChange={handleVolumeChange}
                        sx={{
                          width: 80,
                          color: '#f5576c',
                          '& .MuiSlider-thumb': {
                            width: 16,
                            height: 16
                          }
                        }}
                      />
                      
                      <Button
                        size="small"
                        onClick={() => handleSpeedChange(playbackRate === 1 ? 1.5 : 1)}
                        sx={{ color: 'white', minWidth: 40 }}
                      >
                        {playbackRate}x
                      </Button>
                      
                      <IconButton onClick={handleFullscreenToggle} sx={{ color: 'white' }}>
                        {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
                      </IconButton>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </DialogContent>
          </>
        )}
      </Dialog>
    </Grid>
  );
};

export default Home;