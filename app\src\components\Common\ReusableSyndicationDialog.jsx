import {
    CheckCircleOutline,
    Close,
    CloseFullscreen,
    DangerousOutlined,
    InfoOutlined,
    WarningAmberOutlined,
  } from "@mui/icons-material";
  import {
    Box,
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    FormControlLabel,
    FormLabel,
    Grid,
    IconButton,
    Radio,
    RadioGroup,
    TextField,
    Typography,
  } from "@mui/material";
  import { Stack } from "@mui/system";
  import React, { useState } from "react";
  import CloseIcon from "@mui/icons-material/Close";
  import ReusableSnackBar from "./ReusableSnackBar";
  import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { doAjax } from "./fetchService";
import { destination_ServiceRequest } from "../../destinationVariables";
import AutoAwesomeOutlinedIcon from '@mui/icons-material/AutoAwesomeOutlined';
import { useDispatch, useSelector } from "react-redux";
import { setSyndicationType } from "../../app/costCenterTabsSlice";
  
  function ReusableSyndicationDialog({
    poId,
    roleName,
    SR_REDIRECT,
    handleOk = null,
    dialogPlaceholder,
    dialogState,
    setDialogState,
    openReusableDialog,
    closeReusableDialog,
    handleCancle,
    dialogTitle,
    dialogMessage,
    showInputText,
    inputText,
    setInputText=()=>{},
    handleDialogConfirm=()=>{},
    handleDialogReject=()=>{},
    showCancelButton,
    dialogCancelText,
    showOkButton,
    dialogOkText,
    dialogSeverity,
    requestId,
    commentArtifactType,
    user,
    loadingStatus,
    handleExtraButton,
    handleExtraText,
    showExtraButton,
    onClose,
    alertMsg,
    errorMessage
  }) {
    console.log(showOkButton,"showOkButton======")
    const [openSnackBar, setOpenSnackBar] = useState(false);
    // const [syndicationType, setSyndicationType] = useState("manualSyndication");
    const syndicationType = useSelector((state)=>state.costCenter.syndicationType);
    const dispatch = useDispatch();
    const handleSnackBarClick = () => {
      setOpenSnackBar(true);
    };
  
    const handleSnackBarClose = () => {
      setOpenSnackBar(false);
    };
  
    const handleCommentClick = () => {
      if (inputText) {
        const formData = new FormData();
        var comments = JSON.stringify({
          requestId: requestId,
          comment: inputText,
          commentArtifactType: commentArtifactType,
          createdBy: user,
          roleName: roleName,
          poNumber: poId
        });
        formData.append("comments", comments);
        let hSuccess = (data) => {
          // setInputText("");
        }
        let hError = (error) => {
          console.log(error);
        }
        doAjax(`/${destination_ServiceRequest}/comment/saveCommentWithoutFile`, 'postformdata', hSuccess, hError, formData)
      }
    };

    const handleRemarks = (e,value) => {
      const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setInputText(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue;
      console.log("remarksss", remarksUpperCase);
      setInputText(remarksUpperCase);
    }
    }
    const handleChange = (event) => {
        // setSyndicationType(event.target.value);
        dispatch(setSyndicationType(event.target.value))
      };
  
    return (
      <>
        <Dialog
          hideBackdrop={false}
          elevation={2}
          PaperProps={{
            sx: { boxShadow: "none",marginBottom: "8px", borderRadius: "8px" },
          }}
          open={dialogState}
          onClose={onClose ? onClose : closeReusableDialog}
        >
          <Grid
            container
            sx={{ display: "flex", justifyContent: "space-between",backgroundColor: "#EAE9FF", }}
          >
            <Grid item>
              <DialogTitle
                id="alert-dialog-title"
                sx={{

                justifyContent: "space-between",
                // alignItems: "center",
                height: "max-content",
                // padding: ".5rem",
                paddingLeft: "1rem",
                display: "flex",
                // marginBottom: "8px"
                alignItems: "flex-start",
                }}
              >
                {dialogSeverity && (
                  <span style={{ display: "flex", alignItems: "center" }}>
                    {dialogSeverity === "success" && (
                      <CheckCircleOutline sx={{ color: "green" }} />
                    )}
                    {dialogSeverity === "warning" && (
                      <WarningAmberOutlined sx={{ color: "orange" }} />
                    )}
                    {dialogSeverity === "redirectionWarning" && (
                      <WarningAmberOutlined sx={{ color: "orange" }} />
                    )}
                    {dialogSeverity === "danger" && (
                      <CancelOutlinedIcon sx={{ color: "red" }} />
                    )}
                    {dialogSeverity === "info" && (
                      <InfoOutlined sx={{ color: "grey" }} />
                    )}
                    &nbsp;&nbsp;
                  </span>
                )}
                <Grid sx={{
                  display: "flex",
                  flexDirection: "column",
                }}>
                <Typography>Select Syndication Preference</Typography>
                </Grid>
              </DialogTitle>
            </Grid>
            {/* <Grid item sx={{ padding: "12px" }}>
              {(dialogSeverity !== "danger" || showInputText) && (
                <IconButton
                  onClick={(e) => {
                    e.stopPropagation();
                    closeReusableDialog();
                  }}
                >
                  <CloseIcon />
                </IconButton>
              )}
            </Grid> */}
          </Grid>
  
          <DialogContent>
            <Stack>
              <Grid container>
                <Grid
                  item
                  md={12}
                  sx={{
                   
                    textAlign: "left",
                  }}
                >
                <Grid>
                <FormControl>
      {/* <FormLabel id="demo-controlled-radio-buttons-group">Gender</FormLabel> */}
      <RadioGroup
       row
        aria-labelledby="demo-row-radio-buttons-group-label"
        name="row-radio-buttons-group"
        value={syndicationType}
        onChange={handleChange}
      >
        <FormControlLabel value="manualSyndication" control={<Radio />} label="Immediate Syndication" />
        <FormControlLabel value="scheduleSyndication" control={<Radio />} label="Schedule Syndication" />
      </RadioGroup>
    </FormControl>
                </Grid>
                {/* {dialogTitle?.split('\n')?.map((line, index) => (
                  <Typography key={index}>
                    {line}
                  </Typography >
                ))} */}
                  <Typography className={loadingStatus ? "loading" : ""}>
                    {dialogMessage}
                  </Typography>
                  {errorMessage &&
                  <Typography className={loadingStatus ? "loading" : ""} sx={{fontSize: "12px", paddingTop:"0.5rem"}}>
                    Error Message: {errorMessage}
                  </Typography>}
                </Grid>
              </Grid>
              <Box sx={{ minWidth: 400 }}>
                {showInputText && (
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      value={inputText}
                      onChange={handleRemarks}
                      inputProps={{ maxLength: "200" }}
                      multiline
                      placeholder={`${dialogTitle}...`}
                    ></TextField>
                  </FormControl>
                )}
              </Box>
            </Stack>
           
          </DialogContent>
           
          <DialogActions
            sx={{
              padding: "1rem 1.5rem",
            }}
          >
             <Grid item sx={{display:"flex", justifyContent:"flex-start"}}>
                   <Typography  variant="h6">
                    
                   </Typography>
                   <Typography variant="body2" color="#777">
                   <strong >Note:</strong> The scheduler will commence after office hours. Requests will be addressed according to the priority set.
                   </Typography>
            </Grid>
            {(dialogSeverity === "warning" || showCancelButton) && (
              <Button
                variant="outlined"
                color="error"
                sx={{
                  height: 40,
                  minWidth: "4rem",
                  textTransform: "none",
                  borderColor: "#cc3300",
                  // backgroundColor:"#cc3300",
                  // color: "white",
                }}
                onClick={(e) => { e.stopPropagation();
                   handleDialogReject();
                  closeReusableDialog();

                 }}
              >
                Cancel
              </Button>
            )}
            {(dialogSeverity === "redirectionWarning") && (
              <Button
                variant="outlined"
                color="error"
                sx={{
                  height: 40,
                  minWidth: "4rem",
                  textTransform: "none",
                  borderColor: "#cc3300",
                  // backgroundColor:"#cc3300",
                  // color: "white",
                }}
                onClick={(e) => { e.stopPropagation();
                   handleDialogReject();
                  closeReusableDialog();

                 }}
              >
                No
              </Button>
            )}
            {showOkButton && (
              <Button
                variant="contained"
                style={{
                  height: 40,
                  minWidth: "4rem",
                  backgroundColor: "#3B30C8",
                  textTransform: "none",
                }}
                onClick={() => {
                  handleOk ? handleOk() : closeReusableDialog();
                  handleDialogConfirm();
                  // handleCommentClick();
                  handleDialogReject();
                  handleSnackBarClick();
                }}
              >
                {dialogOkText}
              </Button>
            )}
            {/* {(dialogSeverity === "warning" || dialogSeverity === "danger") && ( */}
              <Button
                variant="contained"
                style={{
                  height: 40,
                  minWidth: "4rem",
                  backgroundColor: "#3B30C8",
                  textTransform: "none",
                }}
                onClick={() => {
                  handleOk ? handleOk() : closeReusableDialog();
                  handleDialogConfirm();
                  // handleCommentClick();
                  handleDialogReject();
                  // handleSnackBarClick();
                }}
              >
                {dialogOkText}
              </Button>
            {/* )} */}
            {showExtraButton && (
              <Button
                variant="contained"
                style={{
                  height: 40,
                  minWidth: "4rem",
                  backgroundColor: "#3B30C8",
                  textTransform: "none",
                }}
                onClick={handleExtraButton}
              >
                {handleExtraText}
              </Button>
            )}
          </DialogActions>
        </Dialog>
      </>
    );
  }
  
  export default ReusableSyndicationDialog;
  