import { <PERSON>, Card, CardContent, Stack, Typography } from "@mui/material";
import React from "react";
import PieChartIcon from "@mui/icons-material/PieChart";
const NoDataPie = ({
  header,
  margintop = 13,
  ht = "365px",
  marginleft = 0,
}) => {
  return (
    <>
      <Card sx={{ borderRadius: "10px", boxShadow: "4", minHeight: `${ht}` }}>
        <CardContent>
          <Stack sx={{alignItems:"center" ,justifyContents:'center'}}>
            <Typography variant="subtitle2" color="black">
              <strong>{header}</strong>
            </Typography>
            <Box
              // justifyContent="center"
              // className="usd"
              mt={margintop}
              // alignItems="center"
            >
              <Typography
                variant="subtitle2"
                fontWeight="normal"
                // ml={marginleft}
                color="#757575"
                fontSize="14px"
                sx={{ fontWeight: 500 }}
              >
                No data is available for the selected filter criteria{" "}
                <PieChartIcon sx={{ fontSize: "20px" }} />
              </Typography>
            </Box>
          </Stack>
        </CardContent>
      </Card>
    </>
  );
};

export default NoDataPie;
