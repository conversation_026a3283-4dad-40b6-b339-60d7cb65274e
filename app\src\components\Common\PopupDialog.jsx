import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  IconButton,
  Box,
  Grid,
  Stack,
  FormControl,
} from "@mui/material";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import CloseIcon from "@mui/icons-material/Close";
import { WarningAmberOutlined } from "@mui/icons-material";

const PopupDialog = ({ open, onClose, message }) => {
  return (
    // <Dialog open={open} onClose={onClose} maxWidth="xs" fullWidth>
    //   <DialogTitle>
    //     <Box display="flex" alignItems="center">
    //       <WarningAmberIcon color="warning" sx={{ mr: 1 }} />
    //       <Typography variant="h6">Warning</Typography>
    //       <IconButton aria-label="close" onClick={onClose} sx={{ marginLeft: "auto" }}>
    //         <CloseIcon />
    //       </IconButton>
    //     </Box>
    //   </DialogTitle>
    //   <DialogContent dividers>
    //     <Typography>{message}</Typography>
    //   </DialogContent>
    //   <DialogActions>
    //     <Button variant="contained" color="primary" onClick={onClose}>
    //       OK
    //     </Button>
    //   </DialogActions>
    // </Dialog>
    <Dialog
      hideBackdrop={false}
      elevation={2}
      PaperProps={{
        sx: { boxShadow: "none", marginBottom: "8px", borderRadius: "8px" },
      }}
      open={open}
      onClose={onClose}
    >
      <Grid
        container
        sx={{
          display: "flex",
          justifyContent: "space-between",
          backgroundColor: "#EAE9FF",
        }}
      >
        <Grid item>
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              height: "max-content",
              paddingLeft: "1rem",
              display: "flex",
              alignItems: "flex-start",
            }}
          >
            <span style={{ display: "flex", alignItems: "center" }}>
              <WarningAmberOutlined sx={{ color: "orange" }} />
            </span>
            <Grid
              sx={{
                display: "flex",
                flexDirection: "column",
              }}
            >
              <Typography variant="h6">Warning</Typography>
            </Grid>
          </DialogTitle>
        </Grid>
      </Grid>
      <DialogContent>
        <Stack>
          <Grid container>
            <Grid
              item
              md={12}
              sx={{
                textAlign: "left",
              }}
            >
              <Box sx={{ minWidth: 400 }}>
                <FormControl sx={{ height: "auto" }} fullWidth>
                  <Typography variant="subtitle2" fontWeight={"bold"}>
                    {message}
                  </Typography>
                </FormControl>
              </Box>
            </Grid>
          </Grid>
        </Stack>
      </DialogContent>
      <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
        <Button
          className="button_primary--normal"
          variant="contained"
          // sx={{ width: "max-content", textTransform: "capitalize" }}
          onClick={onClose}
        >
          OK
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PopupDialog;
