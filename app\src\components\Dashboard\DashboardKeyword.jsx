const dashboardKeyWord = {
  Open: "Open",
  PendingConfirmation: "PendingConfirmation",
  Confirmed: "Confirmed",
  Delivered: "Delivered",
  Blocked: "Blocked",
  ProductionInProgress: "ProductionInProgress",
  ProductionCompleted: "ProductionCompleted",
  PendingASN: "PendingASN",
  OverdueShipment: "OverdueShipment",
  PendingReturns: "PendingReturns",
  OnTimeDelivery: "OnTimeDelivery",
  OrderFillRate: "OrderFillRate",
  ReadyToINV: "ReadyToINV",
  ReadyToPostINV: "ReadyToPostINV",
  PostedINV: "PostedINV",
  PaidINV: "PaidINV",
  UnpaidINV: "UnpaidINV",
  RejectedINV: "RejectedINV",
  MaterialQuantity: "MaterialQuantity",
  MaterialValue: "MaterialValue",
  POStatus: "POStatus",
  INVStatus: "INVStatus",
  PaymentStatus: "PaymentStatus",
  ProductionStatus: "ProductionStatus",
  SRStatus: "SRStatus",
  SRPriority: "SRPriority",
  DeliveryDelay: "DeliveryDelay",
  PlanningTask:"PlanningTask",
  PendingAck:"PendingAck",
  PendingConsumption:"PendingConsumption",
  PendingPlanning:"PendingPlanning",
  SubmiitedAck:"SubmiitedAck",
  ConfirmationSubmitted:"ConfirmationSubmitted",
  SubmittedASN:"SubmittedASN",
  SubmittedConsumption:"SubmittedConsumption",
  ConsumptionSummary:"ConsumptionSummary",
  ConsumptionByPO:"ConsumptionByPO",
  ConsumptionByASN:"ConsumptionByASN",
  ConsumptionByMaterial:"ConsumptionByMaterial",
  MaterialGroup:"MaterialGroup",
  PlanningTable:"PlanningTable",
  Change:"Change",
  Create: "Create",
  Extend:"Extend",
  MassExtend: "MassExtend",
  MassChange:"MassChange",
  MassCreate: "MassCreate",
  pieStatus:"pieStatus",
  ExtendTable:"ExtendTable",
  ExtendTableHeader:"ExtendTableHeader",
  OnboardBar:"OnboardBar",
  FormToSupplier : "FormToSupplier",
  FinanceReview :"FinanceReview",
  ProcurementLeadReview:"ProcurementLeadReview",
  BuyerReview:"BuyerReview",
  ComplianceReview:"ComplianceReview",
  Completed : "Completed",
  dashboardDate:"dashboardDate",
  CycleTime:"CycleTime",
  BottleNeck:"BottleNeck",
  BottleNeckTable : "BottleNeckTable",
  BottleNeckTable2 : "BottleNeckTable2",
  BottleNeckGraph : "BottleNeckGraph",
  ReviewPending:"ReviewPending",
  Approved:"Approved",
  CorrectionPending:"CorrectionPending",
  ApprovalPending:"ApprovalPending",
  PlanningTaskContent:"PlanningTaskContent",
  PlanningTaskLength:"PlanningTaskLength",
  PlanningTaskHeader:"PlanningTaskHeader",
  requestItemLength:"requestItemLength",
  requestTypeGraph:"requestTypeGraph",
  BasedOnGroupGraph:"BasedOnGroupGraph",
  topFiveSlaBreached:"topFiveSlaBreached",
  slaRequestType:"slaRequestType",
  slaRequestTypeContent:"slaRequestTypeContent",
  selectedRequestTypeSLATable:"selectedRequestTypeSLATable",
  selectedRequestTypeRole:"selectedRequestTypeRole"
  

};
export default dashboardKeyWord;
