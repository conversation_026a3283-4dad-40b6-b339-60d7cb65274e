import { createSlice } from "@reduxjs/toolkit";
import appConfigsJson from '../data/applicationConfig';

let initialState = {
    environment: window.location.hostname,
    token:'',
    iwaToken:"",
    SERVICE_BASE_URL_MAP:{},
    importedModules:{},
    logoutUserWarning:false,
    idmToken:'',
    langTranslation:{},
}
appConfigsJson.url.forEach(i=>{
    initialState.SERVICE_BASE_URL_MAP[i.name]= i.url;
})
let applicationConfigReducer = createSlice({
    name: 'applicationConfigSlice',
    initialState,
    reducers:{
        setToken:(state, action)=>{
            state.token = action.payload.token
            return state
        },
        setImportModules:(state, action)=>{
            state.importedModules = action.payload
            return state
        },
        handleLogoutWarningScreen:(state,action)=>{
            state.logoutUserWarning = action.payload
            return state
        },
        setIdmToken:(state, action)=>{
            state.idmToken = action.payload.idmToken
            return state
        },
        setIwaToken:(state, action)=>{
            state.iwaToken = action.payload.iwaToken
            return state
        },
        setLangTranslation:(state, action)=>{
            state.langTranslation = action.payload
            return state
        },
    }
})

export const {setToken,setImportModules,handleLogoutWarningScreen,setIdmToken,setUtilityToken,setLangTranslation,setIwaToken} = applicationConfigReducer.actions
export default applicationConfigReducer.reducer