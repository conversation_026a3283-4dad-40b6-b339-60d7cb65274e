import { Box, Card, CardContent, Grid, Skeleton, Stack, Typography } from '@mui/material'
import React from 'react'
import ArrowForwardIosRoundedIcon from "@mui/icons-material/ArrowForwardIosRounded";
const POtileSkeleton = ({header}) => {
  return (
    <> 
     <Grid item md={ 2}>
    <Card
    sx={{
      borderRadius: "10px",
      boxShadow: "4",
      height:'79px',
      "&:hover": { boxShadow: "6" },
      
    }}
  >
    <CardContent sx={{backgroundColor:'#F5F5F5'}}>
      <Box sx={{ position: "relative" }} p={0}>
        <Box sx={{ position: "absolute", right: "0%", top: "35%" }}>
          <Typography variant="caption" color="#777">
          
            <ArrowForwardIosRoundedIcon
              sx={{ fontSize: "14px", cursor: "pointer" }}
             
            />
          </Typography>
        </Box>
        <Stack sx={{ textAlign: "center" ,alignItems:"center", justifyContent:"center"}}>
          <Typography variant="caption" color="#777" fontSize="0.70rem">
            {header}
          </Typography>

          <Typography variant="h5" color="#3B30C8" >
            {/* <CircularProgress size={15}/> */}
            <Skeleton variant='text' fontSize='1rem' width={50} />
          </Typography>
        </Stack>
      </Box>
    </CardContent>
  </Card>
  </Grid></>
  )
}

export default POtileSkeleton