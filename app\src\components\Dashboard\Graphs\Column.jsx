import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { useDroppable } from "@dnd-kit/core";
import { SortableCard } from "./SortableCard";
export function Column({ column, cards }) {
  const { setNodeRef } = useDroppable({ id: column.id });
  return (
    <div style={{ width: "33%" }}>
      <div ref={setNodeRef} style={{ display: "flex", flexDirection: "column", gap: "16px", minHeight: "100px" }}>
        <SortableContext
          id={column.id}
          items={cards.map((card) => card.id)}
          strategy={verticalListSortingStrategy}
        >
          {cards.map((card) => (
            <SortableCard key={card.id} card={card} />
          ))}
        </SortableContext>
      </div>
    </div>
  );
}