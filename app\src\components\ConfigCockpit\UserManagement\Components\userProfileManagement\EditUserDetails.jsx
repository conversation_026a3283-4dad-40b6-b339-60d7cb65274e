import {
  Backdrop,
  BottomNavigation,
  Box,
  Card,
  CardContent,
  CircularProgress,
  FormControl,
  FormControlLabel,
  Grid,
  Modal,
  OutlinedInput,
  Paper,
  TextField,
  Tooltip,
  Typography,
  IconButton,
  Button,
  StepButton,
  Step,
  Stepper,
  Stack,
  MenuItem,
  Select,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import { useState, useEffect } from "react";
import {
  iconButton_SpacingSmall,
  outermostContainer_Information,
  font_Small,
  button_Primary,
  button_Outlined,
  button_MarginRight
} from "../../../../common/commonStyles";
import ReusableIcon from "../../../../common/ReusableIcon";
import ReusableTable from "../../../../common/ReusableTable";
import AddIcon from "@mui/icons-material/Add";
import { v4 as uuidv4 } from "uuid";
import { destination_Admin, destination_DocumentManagement, destination_ManageAccount, destination_Po, destination_PR } from "../../../../../destinationVariables";
import axios from "axios";
import { useSelector } from "react-redux";
import { json, Navigate, useNavigate } from "react-router-dom";
import moment from "moment";
import ReusableSnackBar from "../../../../common/ReusableSnackBar";
import ReusableDialog from "../../../../common/ReusableDialog";
import { DesktopDatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { ASNidGenerator, formValidator } from "../../../../../functions";
import { doAjax } from "../../../../common/fetchService";

const EditUserDetails = ({ editState, seteditState }) => {
  const [isLoading, setisLoading] = useState(true);
  const userData = useSelector((state) => state.userManagement.userData);
  const navigate = useNavigate();
  //<--Varibles for user data fields
  const [supplierDetails_Data, setsupplierDetails_Data] = useState({});
  const [companyDetails_Data, setcompanyDetails_Data] = useState({});
  const [addressAndContact_Data, setaddressAndContact_Data] = useState({});

  const [bankInfo_Data, setbankInfo_Data] = useState([]);
  const [registration_Data, setregistration_Data] = useState([]);
  const [additionalContact_Data, setadditionalContact_Data] = useState([]);
  const [documentDetails_Data, setdocumentDetails_Data] = useState([]);
  const [docList, setDocList] = useState([]);
  const [reusableDialog_Ref, setreusableDialog_Ref] = useState('')
  const [countriesList, setcountriesList] = useState([])
  const [regionList, setregionList] = useState([])
  
    //<--Functions And Variables For Validations-->
    const [validationErrorItems, setvalidationErrorItems] = useState([])
    let companyCodes
    let userDetails
    let countryList
    let temp_countryList
    let temp_regionList
  let fetchCountries = async()=>{
   doAjax(`/${destination_PR}/Odata/Country`,'get',(res)=> {
    setcountriesList(Object.keys(res).map(item=> `${item} - ${res[item]}`))
    countryList = Object.keys(res).map(item=> `${item} - ${res[item]}`)
    temp_countryList = res
  }
    )
    console.log(countryList,temp_countryList)
  }
  const fetchVendorDetails = async () => {
  await doAjax(
      `/${destination_ManageAccount}/userManagement/getUserDetailsFromOdata/vendorId/${userData?.supplierId}`,'get',(res)=>{
        userDetails = res
      }
    );
    let data = userDetails.d.results[0]
     await doAjax(`/${destination_Po}/Odata/populateCompanyCodeDetails`,'get',(res)=>companyCodes= res)
     await doAjax(`/${destination_PR}/Odata/Region/${data?.Country}`,`GET`,(res)=>{
       temp_regionList = res
      })
      setregionList(Object.keys(temp_regionList).map(item=> `${item} - ${temp_regionList[item]}`))
    let profileData = {
      supplierDetails: {
        supplierName: data?.Name,
        supplierType: "",
        partnerType: "",
        supplierEmail: data?.ToEmail?.results[0]?.EMailAddress,
        supplierNumber: userData?.supplierId,
      },
      bankInfo: {
        id: uuidv4(),
        bankName: data?.ToBank?.results[0]?.BankAccount,
        accountNumber:
          data?.ToBank?.results[0]?.AccountNumber ??
          data?.ToBank?.results[0]?.IBAN,
        accountHolderName: data?.ToBank?.results[0]?.Accountholder,
        swiftKey: data?.ToBank?.results[0]?.Banknumber,
        currency: "",
      },
      registration: {
        id: uuidv4(),
        country:`${data?.Country} - ${temp_countryList[data?.Country]}`,
        taxRegistrationId: data?.TaxNumber,
        type: data?.taxtype,
        ifOther: "",
      },
      companyDetails: {
        id: Math.ceil(Math.random() * 9999999),
        companyName: `${data?.ToCompanyData?.results[0]?.CompanyCode} - ${
          companyCodes[data?.ToCompanyData?.results[0]?.CompanyCode]
        }`,
        areaOfBusiness: "",
        currency: "",
        registeredCapital: "",
        totalEmployee: "",
      },
      addressAndContact: {
        id: Math.ceil(Math.random() * 9999999),
        street1: data?.Street,
        street2: data?.Street2,
        street3: data?.Street3,
        street4: data?.Street4,
        street5: data?.Street5,
        city: data?.City,
        country:`${data?.Country} - ${temp_countryList[data?.Country]}`,
        fax: data?.ToFax?.results[0]?.Fax,
        companyPhone: data?.ToPhone?.results[0]?.Telephoneno,
        companyWebsite: "",
        region:  `${data?.Region} - ${temp_regionList[data?.Region]}`,
        pinCode: data?.PostalCode,
        productCategory: "",
        certifications: "",
        groupOfCompany: "",
      },
      additionalContacts: [
      ],
    };
    setbankInfo_Data([profileData.bankInfo]);
    setcompanyDetails_Data(profileData.companyDetails);
    setregistration_Data([profileData.registration]);
    setsupplierDetails_Data(profileData.supplierDetails);
    setaddressAndContact_Data(profileData.addressAndContact);
    setadditionalContact_Data([...profileData.additionalContacts]);
    setisLoading(false);
  };
  // console.log(bankInfo_Data,'bankinfo')
  const [selectedRows_additionalContact, setselectedRows_additionalContact] = useState([]);
  const [selectedRows_documentDetails, setselectedRows_documentDetails] = useState([]);
  const [selectedRows_bankDetails, setselectedRows_bankDetails] = useState([]);
  const [selectedRows_RegistrationDetails, setselectedRows_RegistrationDetails] = useState([]);

//<-- Functions and Variables for textfields components -->
   const handleOnChange_SupplierDetails = (e) => {
    setsupplierDetails_Data((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };
  const handleOnChange_CompanyDetails = (e) => {
    setcompanyDetails_Data((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };
  const handleOnChange_addressAndContact = (e) => {
    setaddressAndContact_Data((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };
  const supplierDetails_FieldData = [
    {
      name: "supplierName",
      placeholder: "Supplier Name",
      label: "Supplier Name",
      value: supplierDetails_Data,
      onchange: handleOnChange_SupplierDetails,
      disabled: true,
      errorItems:validationErrorItems

    },
    {
      name: "supplierType",
      placeholder: "Supplier Type",
      label: "Supplier Type",
      value: supplierDetails_Data,
      onchange: handleOnChange_SupplierDetails,
      disabled: true,
      errorItems:validationErrorItems

    },
    {
      name: "supplierNumber",
      placeholder: "Supplier Number",
      label: "Supplier Number",
      value: supplierDetails_Data,
      onchange: handleOnChange_SupplierDetails,
      disabled: true,
      errorItems:validationErrorItems

    },
    {
      name: "partnerType",
      placeholder: "Partner Type",
      label: "Partner Type",
      value: supplierDetails_Data,
      onchange: handleOnChange_SupplierDetails,
      disabled: true,
      errorItems:validationErrorItems

    },
    {
      name: "supplierEmail",
      isRequired: true,
      label: "Email ID",
      value: supplierDetails_Data,
      disabled: false,
      errorItems:validationErrorItems

    },
  ];
  const companyDetails_FieldData = [
    {
      name: "companyName",
      label: "Company Name",
      value: companyDetails_Data,
      onchange: handleOnChange_CompanyDetails,
      disabled: true,
      errorItems:validationErrorItems
    },
    {
      name: "areaOfBusiness",
      label: "Area Of Business",
      value: companyDetails_Data,
      onchange: handleOnChange_CompanyDetails,
      disabled: false,
      errorItems:validationErrorItems

    },
    {
      name: "registeredCapital",
      label: "Registered Capital",
      value: companyDetails_Data,
      onchange: handleOnChange_CompanyDetails,
      disabled: false,
      errorItems:validationErrorItems

    },
    {
      name: "currency",
      label: "Currency",
      value: companyDetails_Data,
      onchange: handleOnChange_CompanyDetails,
      disabled: false,
      errorItems:validationErrorItems

    },
    {
      name: "totalEmployee",
      label: "Total Employees",
      value: companyDetails_Data,
      onchange: handleOnChange_CompanyDetails,
      disabled: false,
      errorItems:validationErrorItems

    },
    {
      name: "productCategory",
      label: "Product Category",
      value: companyDetails_Data,
      onchange: handleOnChange_CompanyDetails,
      disabled: false,
      isRequired: true,
      multiSelect: true,
      option: ["IT Services"],
      errorItems:validationErrorItems

    },
    {
      name: "certifications",
      label: "Accreditions & Certifications",
      value: companyDetails_Data,
      onchange: handleOnChange_CompanyDetails,
      disabled: false,
      errorItems:validationErrorItems

    },
    {
      name: "groupOfCompany",
      label: "Group Of Company",
      value: companyDetails_Data,
      onchange: handleOnChange_CompanyDetails,
      disabled: false,
      errorItems:validationErrorItems

    },
  ];
  const addressAndContact_fieldData = [
    {
      name: "street1",
      label: "Street 1",
      isRequired: true,
      value: addressAndContact_Data,
      onchange: handleOnChange_addressAndContact,
      disabled: false,
      errorItems:validationErrorItems

    },
    {
      name: "street2",
      label: "Street 2",
      isRequired: false,
      value: addressAndContact_Data,
      onchange: handleOnChange_addressAndContact,
      disabled: false,
      errorItems:validationErrorItems

    },
    {
      name: "street3",
      label: "Street 3",
      isRequired: false,
      value: addressAndContact_Data,
      onchange: handleOnChange_addressAndContact,
      disabled: false,
      errorItems:validationErrorItems

    },
    {
      name: "street4",
      label: "Street 4",
      isRequired: false,
      value: addressAndContact_Data,
      onchange: handleOnChange_addressAndContact,
      disabled: false,
      errorItems:validationErrorItems

    },
    {
      name: "street5",
      label: "Street 5",
      isRequired: false,
      value: addressAndContact_Data,
      onchange: handleOnChange_addressAndContact,
      disabled: false,
      errorItems:validationErrorItems

    },
    {
      name: "city",
      label: "City",
      isRequired: true,
      value: addressAndContact_Data,
      onchange: handleOnChange_addressAndContact,
      disabled: false,
      errorItems:validationErrorItems

    },
    {
      name: "pinCode",
      label: "Pin Code",
      isRequired: true,
      value: addressAndContact_Data,
      onchange: handleOnChange_addressAndContact,
      disabled: false,
      errorItems:validationErrorItems

    },
    {
      name: "country",
      label: "Country",
      isRequired: true,
      value: addressAndContact_Data,
      onchange: handleOnChange_addressAndContact,
      disabled: false,
      errorItems:validationErrorItems

    },
    {
      name: "companyPhone",
      label: "Company Phone",
      isRequired: true,
      value: addressAndContact_Data,
      onchange: handleOnChange_addressAndContact,
      disabled: false,
      errorItems:validationErrorItems

    },
    {
      name: "fax",
      label: "Fax",
      isRequired: false,
      value: addressAndContact_Data,
      onchange: handleOnChange_addressAndContact,
      disabled: false,
      errorItems:validationErrorItems

    },
    {
      name: "companyWebsite",
      label: "Website",
      isRequired: true,
      value: addressAndContact_Data,
      onchange: handleOnChange_addressAndContact,
      disabled: false,
      errorItems:validationErrorItems

    },
    ,
    {
      name: "region",
      label: "Region",
      isRequired: true,
      value: addressAndContact_Data,
      onchange: handleOnChange_addressAndContact,
      disabled: false,
      errorItems:validationErrorItems

    },
  ];
  const getTextFields = (data) => {
    //returns textfields
    return (
      <>
        <Card sx={{ width: "98%" }}>
          <CardContent>
            <Grid
              columns={12}
              container
              rowSpacing={1}
              sx={{ marginBottom: "0.5rem" }}
              spacing={2}
              justifyContent="start"
              alignItems="center"
            >
              {data?.map((item) => {
                return (
                  <Grid item xs={1} md={3}>
                    <Box sx={{ minWidth: 120 }}>
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: "row",
                          justifyContent: "start",
                          alignItems: "self-start",
                        }}
                      >
                        <Typography
                          sx={{ ...font_Small, display: "inline-block" }}
                        >
                          {item.label}
                        </Typography>
                        {item.isRequired && (
                          <Typography
                            sx={{
                              ...font_Small,
                              display: "inline-block",
                              color: "red",
                            }}
                          >
                            *
                          </Typography>
                        )}
                      </Box>

                      <>
                        {!item.multiSelect && (
                          <TextField
                            fullWidth
                            size="small"
                            placeholder={item.label}
                            name={item.name}
                            value={item.value[item.name]}
                            onChange={item.onchange}
                            required={item.isRequired}
                            error={item.errorItems.includes(item.name)}
                            InputProps={{
                              readOnly: item.disabled,
                            }}
                          ></TextField>
                        )}
                        {item.multiSelect && (
                          <FormControl fullWidth size="small">
                            <Select
                              sx={font_Small}
                              select
                              size="small"
                              placeholder={item.label}
                              name={item.name}
                              disabled={item.disabled}
                              value={item.value[item.name]}
                              onChange={item.onchange}
                              displayEmpty={true}
                            >
                              <MenuItem value={""}>
                                <Typography
                                  className="placeholderstyle"
                                  style={{ color: "#C1C1C1" }}
                                >
                                  {item.label}
                                </Typography>
                              </MenuItem>
                              {item.option.map((com) => (
                                <MenuItem value={com}>{com}</MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        )}
                      </>
                    </Box>
                  </Grid>
                );
              })}
            </Grid>
          </CardContent>
        </Card>
      </>
    );
  };

//<-- Functions and variables for reusabletable components
  const handleRowSelect = (ids, ref) => {
    switch (ref) {
      case "ADDITIONALCONTACTS":
        setselectedRows_additionalContact(ids);
        break;
      case "DOCUMENTS":
        setselectedRows_documentDetails(ids);
        break;
      case "TAXID/REGISTRATION":
        setselectedRows_RegistrationDetails(ids);
        break;
      case "BANKDETAILS":
        setselectedRows_bankDetails(ids);
        break;
    }
  };
  const handleEdit = (params, ref) => {
    switch (ref) {
      case "ADDITIONALCONTACTS":
        setadditionalContact_Data(
          additionalContact_Data.map((ItemData) => {
            if (ItemData.id === params.id) {
              ItemData[params.field] = params.props.value;
            }
            return ItemData;
          })
        );
        break;
      case "DOCUMENTS":
        setdocumentDetails_Data(
          documentDetails_Data.map((ItemData) => {
            if (ItemData.id === params.id) {
              // console.log(ItemData[params.field], params.props.value);
              // ItemData[params.field] = params.props.value;
            }
            return ItemData;
          })
        );
        break;
      case "TAXID/REGISTRATION":
        setregistration_Data(
          registration_Data.map((ItemData) => {
            if (ItemData.id === params.id) {
              ItemData[params.field] = params.props.value;
            }
            return ItemData;
          })
        );
        break;
      case "BANKDETAILS":
        setbankInfo_Data(
          bankInfo_Data.map((ItemData) => {
            if (ItemData.id === params.id) {
              ItemData[params.field] = params.props.value;
            }
            return ItemData;
          })
        );
        break;
      default:
        return null;
    }
  };
  const addRow = (ref) => {
    try {
      switch (ref) {
        case "ADDITIONALCONTACTS":
          setadditionalContact_Data([
            ...additionalContact_Data,
            { id: uuidv4() },
          ]);
          break;
        case "DOCUMENTS":
          setdocumentDetails_Data((prev) => [...prev, { id: uuidv4() }]);
          break;
        case "TAXID/REGISTRATION":
          setregistration_Data((prev) => [...prev, { id: uuidv4() }]);
          break;
        case "BANKDETAILS":
          setbankInfo_Data((prev) => [...prev, { id: uuidv4() }]);
          break;
      }
    } catch (e) {
      console.log(e, "error in add");
    }
  };
  const addFile = () => {
    let addFileButton = document.getElementById("fileButton");
    addFileButton.click();
  };
  const handleOnChangeFile = (e) => {
    let files = [...e.target.files];
    let filesWithId = [];
    files.forEach((item) => {
      item.id = uuidv4();
      filesWithId.push(item);
    });
    // console.log(files, "files");
    setDocList((prev)=> [...prev,...filesWithId]);
    // console.log(filesWithId,'filesWithId')
    let tDocDetailsData = filesWithId.map((item, index) => {
      return {
        id: item.id,
        documentType: item.type,
        documentName: item.name,
        updatedDate: item.lastModifiedDate,
      };
    });
    setdocumentDetails_Data((prev) =>[...prev,...tDocDetailsData] );
  };
  const deleteData = (ref) => {
    switch (ref) {
      case "ADDITIONALCONTACTS":
        setadditionalContact_Data(
          additionalContact_Data.filter(
            (ItemData) => !selectedRows_additionalContact.includes(ItemData.id)
          )
        );
        break;
      case "DOCUMENTS":
        setDocList((prev) => {
          return docList.filter(
            (ItemData) => !selectedRows_documentDetails.includes(ItemData.id)
          );
        });
        setdocumentDetails_Data(
          documentDetails_Data.filter(
            (ItemData) => !selectedRows_documentDetails.includes(ItemData.id)
          )
        );
        break;
      case "TAXID/REGISTRATION":
        setregistration_Data(
          registration_Data.filter(
            (ItemData) =>
              !selectedRows_RegistrationDetails.includes(ItemData.id)
          )
        );
        break;
      case "BANKDETAILS":
        setbankInfo_Data(
          bankInfo_Data.filter(
            (ItemData) => !selectedRows_bankDetails.includes(ItemData.id)
          )
        );
        break;
      default:
        return null;
    }
  };

  const getActionButtons = (ref) => { //return delete add buttons
    return (
      <Stack direction={"row"}>
        <IconButton onClick={() => addRow(ref)}>
          <AddIcon />
        </IconButton>
        <IconButton onClick={() => deleteData(ref)}>
          <DeleteIcon />
        </IconButton>
      </Stack>
    );
  };

  let bankInfo_Column = [
    {
      field: "id",
      headerName: "id",
      editable: false,
      hide: true,
      width: 200,
    },
    {
      field: "bankName",
      headerName: "Bank Name",
      editable: true,
      flex: 1,
      isRequired: true,
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>{"Enter " + params.colDef.headerName}</i>
        );
      },
    },
    {
      field: "accountNumber",
      headerName: "Bank Account Number/IBAN Code",
      isRequired: true,
      editable: true,
      flex: 1,
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>{"Enter " + params.colDef.headerName}</i>
        );
      },
    },
    {
      field: "accountHolderName",
      headerName: "Bank Holder Name",
      editable: true,
      flex: 1,
      isRequired: true,
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>{"Enter " + params.colDef.headerName}</i>
        );
      },
    },
    {
      field: "currency",
      headerName: "Currency",
      editable: true,
      flex: 1,
      isRequired: true,
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>{"Enter " + params.colDef.headerName}</i>
        );
      },
    },
    {
      field: "swiftKey",
      headerName: "Swift Code/Bank Key",
      editable: true,
      flex: 1,
      isRequired: true,
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>{"Enter " + params.colDef.headerName}</i>
        );
      },
    },
    {
      field: "bankCountry",
      headerName: "Country",
      editable:true,
      valueOptions: countriesList,
      type: 'singleSelect',
      flex: 1,
      isRequired: true,
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>{"Select " + params.colDef.headerName}</i>
        );
      },
    },
  ];
  let documentColumn = [
    {
      field: "id",
      headerName: "id",
      editable: false,
      hide: true,
      flex: 1,
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>{"Enter " + params.colDef.headerName}</i>
        );
      },
    },
    {
      field: "documentType",
      headerName: "Document Type",
      editable: false,
      flex: 1,
      type: "singleSelect",
      valueOptions: [],
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>
            {"Select a " + params.colDef.headerName}
          </i>
        );
      },
    },
    {
      field: "documentName",
      headerName: "Document Name",
      editable: false,
      flex: 1,
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>{"Enter " + params.colDef.headerName}</i>
        );
      },
    },
    {
      field: "updatedDate",
      headerName: "Updated On",
      editable: false,
      flex: 1,
      renderCell: function (params) {
        return params.row.updatedDate ? (
          <>{moment(params.row.updatedDate).format("YYYY-MM-DD")}</>
        ) : (
          <i style={{ color: "grey" }}>{"Enter " + params.colDef.headerName}</i>
        );
      },
    },
    {
      field: "expiryDate",
      headerName: "ExpiryDate",
      flex: 1,
      renderCell: function (params) {
        return (
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DesktopDatePicker
              inputFormat="dd MMM yyyy"
              value={params.row.expiryDate}
              name="expiryDate"
              onChange={(date) => {
                setdocumentDetails_Data(
                  documentDetails_Data.map((ItemData) => {
                    if (ItemData.id === params.id) {
                      ItemData[params.field] = date;
                    }
                    return ItemData;
                  })
                );
              }}
              renderInput={(param) => (
                <TextField
                  size="small"
                  {...param}
                  sx={{
                    width: "16.7rem",
                    backgroundColor: "#FAFAFA",
                  }}
                />
              )}
            />
          </LocalizationProvider>
        );
      },
    },
  ];
  let additionalContactColumn = [
    {
      field: "id",
      headerName: "id",
      editable: false,
      hide: true,
      width: 200,
    },
    {
      field: "function",
      headerName: "Function",
      editable: true,
      flex: 1,
      type: "singleSelect",
      valueOptions: ["Finance", "Marketing"],
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>
            {"Select a " + params.colDef.headerName}
          </i>
        );
      },
    },
    {
      field: "contactName",
      headerName: "Contact Name",
      editable: true,
      flex: 1,
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>{"Enter " + params.colDef.headerName}</i>
        );
      },
    },
    {
      field: "position",
      headerName: "Position",
      editable: true,
      flex: 1,
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>{"Enter " + params.colDef.headerName}</i>
        );
      },
    },
    {
      field: "contactPhone",
      headerName: "Contact Phone",
      editable: true,
      flex: 1,
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>{"Enter " + params.colDef.headerName}</i>
        );
      },
    },
    {
      field: "email",
      headerName: "Email",
      editable: true,
      flex: 1,
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>{"Enter Swift Code/Bank Key"}</i>
        );
      },
    },
  ];
  let registration_Column = [
    {
      field: "id",
      headerName: "id",
      editable: false,
      hide: true,
      width: 200,
    },
    {
      field: "country",
      headerName: "Country",
      editable: true,
      flex: 1,
      type: "singleSelect",
      valueOptions: countriesList,
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>
            {"Select " + params.colDef.headerName}
          </i>
        );
      },
    },
    {
      field: "type",
      headerName: "Type",
      editable: true,
      width: 200,
      type: "singleSelect",
      valueOptions: [],
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>
            {"Select " + params.colDef.headerName}
          </i>
        );
      },
    },
    {
      field: "taxRegistrationId",
      headerName: "Tax Registration ID",
      editable: true,
      flex: 1,
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>{"Enter " + params.colDef.headerName}</i>
        );
      },
    },
    {
      field: "ifOther",
      headerName: "If Other",
      editable: true,
      flex: 1,
      renderCell: function (params) {
        return params.value ? (
          <>{params.value}</>
        ) : (
          <i style={{ color: "grey" }}>{"Enter " + params.colDef.headerName}</i>
        );
      },
    },
  ];



  //<--Functions and variables for steps -->
  const steps = [
    "Company Details",
    "Bank & Tax Information",
    "Contact & Documentation",
  ];
  const [activeStep, setActiveStep] = useState(0);
  const [completed, setCompleted] = useState({});
  let functions_Steps = {
    totalSteps: () => {
      return steps.length;
    },
    completedSteps: () => {
      return Object.keys(completed).length;
    },
    isLastStep: () => {
      return activeStep === functions_Steps.totalSteps() - 1;
    },

    allStepsCompleted: () => {
      return functions_Steps.completedSteps() === functions_Steps.totalSteps();
    },

    handleNext: () => {
      let error = false
     
      switch(activeStep){
        case 0:
          error = formValidator({...companyDetails_Data,...addressAndContact_Data},['street1', 'companyName', 'country','pinCode','companyPhone','companyWebsite','region'],setvalidationErrorItems)
          break;
        // case 1:
        //   error = formValidator({...companyDetails_Data,...addressAndContact_Data},['street1', 'companyName', 'country','pinCode'],setvalidationErrorItems)
        //   break;
        // case 2:
        //   error = formValidator({...companyDetails_Data,...addressAndContact_Data},['street1', 'companyName', 'country','pinCode'],setvalidationErrorItems)
        //   break;
        default:
          error = true
        
      }
      if(error){

      const newActiveStep =
        functions_Steps.isLastStep() && !functions_Steps.allStepsCompleted()
          ? // It's the last step, but not all steps have been completed,
            // find the first step that has been completed
            steps.findIndex((step, i) => !(i in completed))
          : activeStep + 1;
      setActiveStep(newActiveStep);
    }

    },

    handleBack: () => {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
    },

    handleStep: (step) => () => {
      setActiveStep(step);
    },

    handleComplete: () => {
      const newCompleted = completed;
      newCompleted[activeStep] = true;
      setCompleted(newCompleted);
      functions_Steps.handleNext();
    },

    handleReset: () => {
      setActiveStep(0);
      setCompleted({});
    },
  };

  //<-- Functions and variables for reusable dialog box -->
  const [warning_Notification, setwarning_Notification] = useState({
    currentNotification: "",
    success: true,
    open: false,
    title: "",
    severity: "",
  });
  const [Success_Notification, setSuccess_Notification] = useState({
    currentNotification: "",
    success: true,
    open: false,
    title: "",
    severity: "",
  }); 
  const functions_ReusableDialogBox = {
    MessageDialogCancel: () => {
      setwarning_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
    },
    MessageDialogClickOpen: () => {
      setwarning_Notification((prev) => ({ ...prev, open: true }));
      // setOpenMessageDialog(true);
    },

    MessageDialogClose: () => {
      setwarning_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));

      setSuccess_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
    },
    messageDialogCloseAndRedirect: () => {
      seteditState(false);
      setwarning_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
      setSuccess_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
      // navigate('/manageAccount')
    },
    getHandleOkFunction:()=>{
      switch(reusableDialog_Ref){
        case 'CONFIRMSUBMIT':
          setwarning_Notification((prev) => ({
            open: false,
            currentNotification: "",
            success: "",
            title: "",
            severity: "",
          }));
          handleSubmit()
          break;
        case 'ERROR':
          functions_ReusableDialogBox.MessageDialogClose()
          break;
        case 'CONFIRMCANCEL':
          functions_ReusableDialogBox.MessageDialogClose()
          seteditState(false)
        default :
        functions_ReusableDialogBox.MessageDialogClose()
      }
    },
    viewOkButton:()=>{
      // console.log(reusableDialog_Ref,'ref')
      switch(reusableDialog_Ref){
        case 'CONFIRMSUBMIT':
          return true
          break;
        case 'ERROR':
          return false
          break;
        case 'CONFIRMCANCEL':
          return false
        default :
       return false
      }
    },
    viewCancelButton: ()=>{
      switch(reusableDialog_Ref){
        case 'CONFIRMSUBMIT':
          return true
          break;
        case 'ERROR':
          return false
          break;
          case 'CONFIRMCANCEL':
         return true
        default :
       return false
      }
    },
    getOkButtonText:()=>{
      switch(reusableDialog_Ref){
        case 'CONFIRMSUBMIT':
          return 'Submit'
          break;
        case 'ERROR':
          return 'OK'
        case 'CONFIRMCANCEL':
           return 'OK'
        default :
       return ''
      }
    },
    getHandleCancleFunction:()=>{
      switch(reusableDialog_Ref){
        case 'CONFIRMSUBMIT':
          return functions_ReusableDialogBox.MessageDialogClose()
        case 'ERROR':
          return ()=>{}
        case 'CONFIRMCANCEL':
           return functions_ReusableDialogBox.MessageDialogClose()
        default :
       return ()=>{}
      }
    }
  };

//<-- Functions for submit and confirm submit -->
  const handleSubmitConfirmation=()=>{
    setreusableDialog_Ref('CONFIRMSUBMIT')

    setwarning_Notification((prev) => ({
      ...prev,
      currentNotification: `Proceed with submission of profile updation`,
      success: false,
      open: true,
      title: "Account Updation",
      severity: "success",
    }));
  }
  const handleSubmit = () => {
    try{
    let deleteIdFromPayload = (data) => {
      let modifiedData = data?.map((item) => {
        let itemClone = {...item}
        delete itemClone.id;
        itemClone.mapId = "";
       return itemClone;
      });
      return modifiedData;
    };

    let payload = {
      createdBy: userData?.emailId,
      companyInformationDo: {
        ...supplierDetails_Data,
        ...companyDetails_Data,
        ...addressAndContact_Data,
        companyName: companyDetails_Data.companyName.split(' - ')[0],
        mapId: ASNidGenerator('POF'),
      },
      bankDo: deleteIdFromPayload(bankInfo_Data),
      taxDo: deleteIdFromPayload(registration_Data),
      contactDo: deleteIdFromPayload(additionalContact_Data)
    };
    payload.bankDo.forEach(item=>{
      item.bankCountry = item.bankCountry?.split(' - ')[0]
    })
    payload.taxDo.forEach(item=>{
      item.country = item.country?.split(' - ')[0]
    })
    // payload.taxDo.country = payload.taxDo.country?.split('-')[0]
    payload.companyInformationDo.country = payload.companyInformationDo.country?.split(' - ')[0]
    payload.companyInformationDo.region = payload.companyInformationDo.region?.split(' - ')[0]

    delete payload.companyInformationDo.id;
    let formData_Payload = new FormData();
    formData_Payload.append("userDetailsDto", JSON.stringify(payload));

    //payload for saving documents
    let docPayload= new FormData()
    docList.forEach((doc)=>{
      delete doc.id
      docPayload.append('files',doc)
    })
    docPayload.append('doc',JSON.stringify({
      artifactId:payload.companyInformationDo.mapId,
      createdBy:userData.emailId,
      artifactType:'User Management'
    }))

    console.log(payload,'finalPayload')

    // console.log(JSON.stringify({
    //   artifactId:payload.companyInformationDo.mapId,
    //   createdBy:userData.emailId,
    //   artifactType:'user management'
    // }), docPayload.getAll('files'),docPayload.get('doc'),'docPayload')

    let hSuccess = (res) => {
      
      if (res.code === '0') {
        setSuccess_Notification((prev) => ({
          ...prev,
          currentNotification: `Updated supplier details sent for approval successfully.`,
          success: true,
          open: true,
          title: "Success",
          severity: "success",
        }));
        doAjax(`/${destination_DocumentManagement}/documentManagement/uploadDocument`,'postformdata',(res)=>{console.log(res,'docres')},()=>{},docPayload)
        
      } else {
        setreusableDialog_Ref('ERROR')
        setwarning_Notification((prev) => ({
          ...prev,
          currentNotification: `Update Failed`,
          success: false,
          open: true,
          title: "Error",
          severity: "danger",
        }));
      }
    }
    let hError = e=>{
      // setreusableDialog_Ref('ERROR')
      // setwarning_Notification((prev) => ({
      //   ...prev,
      //   currentNotification: `Update Failed due to error`,
      //   success: false,
      //   open: true,
      //   title: "Error",
      //   severity: "danger",
      // }));
    }
    doAjax(
        `/${destination_ManageAccount}/userManagement/saveDetails`,'postformdata',hSuccess,hError,
        formData_Payload
      )
    }catch(e){
      console.log(e)
    }
  };
 
  useEffect(() => {
    fetchVendorDetails();
    fetchCountries()
  }, []);
  useEffect(() => {
    // console.log(reusableDialog_Ref, bankInfo_Data, registration_Data,'test')
  },[bankInfo_Data]);

  // useEffect(() => {
  //   // console.log(
  //   //   addressAndContact_Data,
  //   //   companyDetails_Data,
  //   //   "addressAndContact_Data"
  //   // );
  // }, [addressAndContact_Data]);

  return (
    <>
      {/* Loader */}

      <Backdrop className="backdrop" open={isLoading}>
        <CircularProgress color="primary" />
      </Backdrop>
      <ReusableDialog
        dialogState={warning_Notification.open}
        openReusableDialog={functions_ReusableDialogBox.MessageDialogClickOpen}
        closeReusableDialog={functions_ReusableDialogBox.MessageDialogCancel}
        dialogTitle={warning_Notification.title}
        dialogMessage={warning_Notification.currentNotification}
        handleOk={functions_ReusableDialogBox.getHandleOkFunction }
        dialogOkText={functions_ReusableDialogBox.getOkButtonText()}
        showOkButton={functions_ReusableDialogBox.viewOkButton() }
        showCancelButton= {functions_ReusableDialogBox.viewCancelButton()}
        dialogSeverity={warning_Notification.severity}
        handleDialogReject={functions_ReusableDialogBox.getHandleCancleFunction}
      />
      <ReusableSnackBar
        openSnackBar={Success_Notification.open}
        alertMsg={Success_Notification.currentNotification}
        handleSnackBarClose={
          functions_ReusableDialogBox.messageDialogCloseAndRedirect
        }
      />
      <Stack sx={{ padding: "0 1rem 0 1rem" }}>
        <Grid container sx={outermostContainer_Information}>
          <Grid container>
            <Grid
              item
              md={5}
              style={{ padding: "16px", paddingLeft: "", display: "flex" }}
            >
              {/* Information */}
              <Grid container sx={outermostContainer_Information}>
                <Grid item xs>
                  <Typography variant="h3">
                    <strong>Edit Account</strong>
                  </Typography>
                  <Typography variant="body2" color="#777">
                    This view displays the User Profile to supplier and allows
                    them to edit it
                  </Typography>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        <Grid container>
         
          <Box sx={{ width: "100%" }}>
            <Stepper
              nonLinear
              activeStep={activeStep}
              sx={{ width: "70%", margin: "0 auto" }}
            >
              {steps.map((label, index) => (
                <Step key={label} completed={completed[index]}>
                  <StepButton
                    color="inherit"
                    onClick={functions_Steps.handleStep(index)}
                  >
                    {label}
                  </StepButton>
                </Step>
              ))}
            </Stepper>
            <div>
              {activeStep === 0 && (
                <Stack spacing={1}>
                  {/*supplier details */}
                  <Stack
                    direction={"column"}
                    sx={{ width: "100%", padding: ".5rem" }}
                  >
                    <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                      Supplier Details
                    </Typography>
                    {getTextFields(supplierDetails_FieldData)}
                  </Stack>
                  {/*Company Details*/}
                  <Stack
                    direction={"column"}
                    sx={{ width: "100%", padding: ".5rem" }}
                  >
                    <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                      Company Details
                    </Typography>
                    {getTextFields(companyDetails_FieldData)}
                  </Stack>
                  {/*Company Address and COntacts*/}
                  <Stack
                    direction={"column"}
                    sx={{ width: "100%", padding: ".5rem" }}
                  >
                    <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                      Company Address and Contacts
                    </Typography>
                    {getTextFields(addressAndContact_fieldData)}
                  </Stack>
                </Stack>
              )}
              {activeStep === 1 && (
                <>
                  {/*Bank info*/}
                  <Stack
                    direction={"column"}
                    sx={{ width: "100%", padding: ".5rem" }}
                  >
                    <Stack direction={"row"}>
                      <Typography
                        variant="h6"
                        sx={{ margin: ".5rem 0px", marginRight: "auto" }}
                      >
                        Mention your company Bank Details
                      </Typography>
                      {getActionButtons("BANKDETAILS")}
                    </Stack>

                    <ReusableTable
                      width="100%"
                      status_onRowDoubleClick={false}
                      rows={bankInfo_Data}
                      columns={bankInfo_Column}
                      getRowIdValue={"id"}
                      hideFooter={true}
                      checkboxSelection={true}
                      disableSelectionOnClick={true}
                      isLoading={false}
                      onRowsSelectionHandler={(ids) =>
                        handleRowSelect(ids, "BANKDETAILS")
                      }
                      onEditCellPropsChange={(params) =>
                        handleEdit(params, "BANKDETAILS")
                      }
                    />
                  </Stack>
                  {/*Bank info*/}
                  <Stack
                    direction={"column"}
                    sx={{ width: "100%", padding: ".5rem" }}
                  >
                    <Stack direction={"row"}>
                      <Typography
                        variant="h6"
                        sx={{ margin: ".5rem 0px", marginRight: "auto" }}
                      >
                        Mention your Site Tax ID or Company Registration Number
                      </Typography>
                      {getActionButtons("TAXID/REGISTRATION")}
                    </Stack>

                    <ReusableTable
                      width="100%"
                      status_onRowDoubleClick={false}
                      rows={registration_Data}
                      columns={registration_Column}
                      getRowIdValue={"id"}
                      hideFooter={true}
                      checkboxSelection={true}
                      disableSelectionOnClick={true}
                      isLoading={false}
                      onRowsSelectionHandler={(ids) =>
                        handleRowSelect(ids, "TAXID/REGISTRATION")
                      }
                      onEditCellPropsChange={(params) =>
                        handleEdit(params, "TAXID/REGISTRATION")
                      }
                    />
                  </Stack>
                </>
              )}
              {activeStep === 2 && (
                <>
                  {/*additional contacts and documents*/}
                  <Stack
                    direction={"column"}
                    sx={{ width: "100%", padding: ".5rem" }}
                  >
                    <Stack direction={"row"}>
                      <Typography
                        variant="h6"
                        sx={{ margin: ".5rem 0px", marginRight: "auto" }}
                      >
                        Mention the additional contacts from the company
                      </Typography>
                      {getActionButtons("ADDITIONALCONTACTS")}
                    </Stack>

                    <ReusableTable
                      width="100%"
                      status_onRowDoubleClick={false}
                      rows={additionalContact_Data}
                      columns={additionalContactColumn}
                      getRowIdValue={"id"}
                      hideFooter={true}
                      checkboxSelection={true}
                      disableSelectionOnClick={true}
                      isLoading={false}
                      onRowsSelectionHandler={(ids) =>
                        handleRowSelect(ids, "ADDITIONALCONTACTS")
                      }
                      onEditCellPropsChange={(params) =>
                        handleEdit(params, "ADDITIONALCONTACTS")
                      }
                    />
                  </Stack>

                  {/*Documents info*/}
                  <Typography>
                    
                  </Typography>
                  <Stack
                    direction={"column"}
                    sx={{ width: "100%", padding: ".5rem" }}
                  >
                    <Stack direction={"row"}>
                      <Typography
                        variant="h6"
                        sx={{ margin: ".5rem 0px", marginRight: "auto" }}
                      >
                        Upload the supporting documents
                      </Typography>
                      <Stack direction={"row"}>
                        <IconButton onClick={() => addFile("DOCUMENTS")}>
                          <AddIcon />
                        </IconButton>
                        <input
                          id="fileButton"
                          multiple
                          accept=".jpeg, .jpg, .xls, .xlsx, .docx, .pdf"
                          type="file"
                          name="files"
                          onChange={(e) => handleOnChangeFile(e, "DOCUMENTS")}
                          style={{ marginTop: ".5rem", display: "none" }}
                        />
                        <IconButton onClick={() => deleteData("DOCUMENTS")}>
                          <DeleteIcon />
                        </IconButton>
                      </Stack>
                    </Stack>

                    <ReusableTable
                      width="100%"
                      status_onRowDoubleClick={false}
                      rows={documentDetails_Data}
                      columns={documentColumn}
                      getRowIdValue={"id"}
                      hideFooter={true}
                      checkboxSelection={true}
                      disableSelectionOnClick={true}
                      isLoading={false}
                      onRowsSelectionHandler={(ids) =>
                        handleRowSelect(ids, "DOCUMENTS")
                      }
                      onEditCellPropsChange={(params) =>
                        handleEdit(params, "DOCUMENTS")
                      }
                    />
                  </Stack>
                </>
              )}
            </div>
          </Box>
        </Grid>
      </Stack>
      <Paper
        sx={{ position: "fixed", bottom: 0, left: 0, right: 0,zIndex:1 }}
        elevation={2}
      >
        <BottomNavigation showLabels    className="container_BottomNav">
          <Grid
            container
            sx={{
              display: "flex",
              justifyContent: "end",
              alignContent: "center",
              width: "100%",
            }}
          >
            <Grid
              item
              md={8}
              minWidth="8rem"
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                height: "2rem",
                marginLeft: "auto",
              }}
            >
              {activeStep === 0 && (
                <Button
                  size="small"
                  variant="outlined"
                  className='btn-mr'
                  onClick={() => {
                    setreusableDialog_Ref('CONFIRMCANCEL')

    setwarning_Notification((prev) => ({
      ...prev,
      currentNotification: `Are you sure you want to proceed with Cancel? The entered data will be lost`,
      success: false,
      open: true,
      title: "Warning",
      severity: "warning",
    }));
                  }}
                >
                  Cancel
                </Button>
              )}
              {activeStep !== 0 && (
                <Button
                  size="small"
                  variant="outlined"
                  className='btn-mr'
                  disabled={activeStep === 0}
                  onClick={functions_Steps.handleBack}
                >
                  Back
                </Button>
              )}
              <Button
                size="small"
                variant="contained"
               
                onClick={
                  activeStep === steps.length - 1
                    ? handleSubmitConfirmation
                    : functions_Steps.handleNext
                }
              >

                {activeStep === steps.length - 1 ? "Submit" : "Next"}
              </Button>
              <Typography>
            {bankInfo_Data.id}
          </Typography>
            </Grid>
          </Grid>
        </BottomNavigation>
      </Paper>
    </>
  );
};

export default EditUserDetails;
