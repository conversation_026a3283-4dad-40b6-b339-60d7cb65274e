import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON>rid, Stack, TextField, Typography } from "@mui/material";
import { updateMaterialData } from "../../../app/payloadslice";
import { useChangeLogUpdate } from '@hooks/useChangeLogUpdate';
import { useLocation } from 'react-router-dom';
import ProdHierModal from "@components/RequestBench/ProdHierModal";
import { colors } from "@constant/colors";
import SkeletonWithFallback from "@components/Common/ui/SkeletonWithFallback";
import { CHANGE_LOG_STATUSES, VISIBILITY_TYPE } from "@constant/enum";
import  useLang  from "@hooks/useLang";

const InputType = ({ details, materialID, keyName, disabled, ...props }) => {
  const dispatch = useDispatch();
  const { updateChangeLog } = useChangeLogUpdate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get("RequestId");
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const SAPview = location.pathname.includes("DisplayMaterialSAPView");
  const { t } = useLang();

  // Fetch Redux state value
  const valueFromPayload = useSelector((state) => state.payload);
  const errorFields = useSelector((state) => state.payload.errorFields);

  // Determine the initial value for the field
  const initialFieldValue =
    valueFromPayload?.[materialID]?.payloadData?.[props?.viewName]?.[props?.plantData]?.[keyName] ?? valueFromPayload?.payloadData?.[keyName] ?? props?.details?.value ?? "";

  // Local state to manage input value
  const [localValue, setLocalValue] = useState(initialFieldValue);
  const [isFocused, setIsFocused] = useState(false);
  const [charCount, setCharCount] = useState({});
  const [isClicked,setIsClicked] = useState(false);

  // Sync local state with Redux state on mount or Redux updates
  useEffect(() => {
    setLocalValue(initialFieldValue);
    setCharCount((prevCount) => ({
      ...prevCount,
      [keyName]: initialFieldValue?.length || 0,
    }));
  }, [initialFieldValue]);

  // Handle input change
  const handleInputChange = (e) => {
    const updatedValue = e.target.value
        .replace(/[^a-zA-Z0-9\-&()#,. ]/g, "")      // Added dot (.) to the allowed characters
        .replace(/\s{2,}/g, " ")
        .replace(/\s*([-&()#,.])\s*/g, "$1")        // Remove spaces around special characters
        .trimStart();

    setCharCount((prevCount) => ({
      ...prevCount,
      [keyName]: updatedValue.length,
    }));
    
    setLocalValue(updatedValue); // Update local state for immediate UI feedback
    
    dispatch(
      updateMaterialData({
        materialID,
        keyName,
        data: updatedValue,
        viewID: props?.viewName,
        itemID: props?.plantData,
      })
    );

    {requestId && !CHANGE_LOG_STATUSES.includes(initialPayload?.RequestStatus) && updateChangeLog({
      materialID: props?.selectedMaterialNumber,
      viewName: props?.viewName,
      plantData: props?.plantData,
      fieldName: details?.fieldName,
      jsonName: details?.jsonName,
      currentValue: updatedValue,
      requestId: initialPayload?.RequestId,
        childRequestId:requestId
    })}
  };

  // Return null if the field is hidden
  if (details.visibility === "Hidden") {
    return null;
  }
  const setProdHierVal = (val) =>{
    dispatch(
      updateMaterialData({
        materialID,
        keyName,
        data: val,
        viewID: props?.viewName,
        itemID: props?.plantData,
      })
    );
  }

  return (
    <>
      <Grid item md={2}>
        <Stack>
        {SAPview ? <div style={{
        padding: '16px',
        backgroundColor: colors.primary.white,
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        margin: '16px 0',
        transition: 'all 0.3s ease'
      }}>
        <Typography
            variant="body1"
            style={{
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              maxWidth: "100%",
              fontWeight: 600,
              fontSize: "12px",
              marginBottom: '4px',
           
              display: 'flex',
              alignItems: 'center',
            }}
          title={t(details?.fieldName)}
        >
          {t(details?.fieldName) || "Field Name"}
          {(details?.visibility === "Required" ||
            details?.visibility === "MANDATORY") && (
            <span style={{ color: colors.error.darkRed, marginLeft: '2px' }}>*</span>
          )}
        </Typography>
       
        <div     style={{
        fontSize: '0.8rem',
          color: colors.black.dark,
          marginTop: '4px'
      }}>
          <span
        style={{
          fontWeight: 500,
          color: colors.secondary.grey,
          letterSpacing: '0.5px', 
          wordSpacing: '1px',
        }}
      >
          {localValue}
          {!localValue &&  <SkeletonWithFallback fallback="--" />}
 
      </span>
       
        </div>
      </div>:(<>
          {/* Field Label */}
          <Typography variant="body2" color="#777" sx={{whiteSpace: 'nowrap',overflow: 'hidden',textOverflow: 'ellipsis',maxWidth: '100%'}} title={t(details?.fieldName)}>
            {t(details.fieldName)}
            {(details.visibility === "Mandatory" || details.visibility === "0") && (
              <span style={{ color: "red" }}>*</span>
            )}
          </Typography>
          <TextField
            size="small"
            type={details.dataType === "QUAN" ? "number" : "text"}
            placeholder={disabled ? "" : t(`Enter ${details.fieldName}`)}
            error={errorFields?.includes(keyName)}
            value={localValue}
            title={localValue}
            onBlur={(e) => {
              setIsFocused(false)
            }
            }
            inputProps={{
              style: { textTransform: "uppercase" },
              maxLength: details.maxLength, // This handles the transformation directly
            }}
            onFocus={() => {setIsFocused(true)}}
            onClick={() =>{setIsClicked(true)}}
            helperText={
                  isFocused &&
                  ( charCount[keyName] === details.maxLength
                    ? t("Max Length Reached")
                    : `${charCount[keyName]}/${details.maxLength}`
                  )
                }
            FormHelperTextProps={{
              sx: {
                color:
                  isFocused &&
                  charCount[keyName] === details.maxLength
                    ? "red"
                    : "blue",
                position: "absolute",
                bottom: "-20px",
              },
            }}
            sx={{
              "& .MuiInputBase-root.Mui-disabled": {
                "& > input": {
                  WebkitTextFillColor: colors.black.dark,
                  color: colors.black.dark,
                },
                backgroundColor: colors.hover.light,
              },
              "& .MuiInputBase-root": {
                height: "34px",
              },
              "& .MuiOutlinedInput-root": {
                "&.Mui-focused fieldset": {
                  borderColor: 
                  isFocused && charCount[keyName] >= details.maxLength
                    ? "red"
                    : "",
                },
                "& fieldset": {
                  borderColor: 
                  isFocused && charCount[keyName] >= details.maxLength
                    ? "red"
                    : "",
                },
              },
            }}
            onChange={handleInputChange}
            disabled={disabled || details?.visibility === VISIBILITY_TYPE.DISPLAY}
            required={details.visibility === "Mandatory" || details.visibility === "0"}
          /></>)}
        </Stack>
      </Grid>
      {details?.fieldName.trim() === 'Product Hierarchy' && <ProdHierModal setProdHierVal = {setProdHierVal} isClicked = {isClicked} setIsClicked ={setIsClicked}/>}
    </>
    
  );
};

export default InputType;
