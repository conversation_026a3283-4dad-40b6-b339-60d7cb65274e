// redux/requestHeaderSlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  requestHeader: {
    requestId: '',
    reqCreatedBy: '',
    reqCreatedOn: '',
    reqUpdatedOn: '',
    requestType: '',
    requestDesc: '',
    requestStatus: 'DRAFT',
    requestPriority: '',
    fieldName: '',
    templateName: '',
    division: '',
    region: '',
    leadingCat: '',
    firstProd: '',
    launchDate: '',
    isBifurcated: false,
    screenName: '',
    isHeaderFinalized: false,
  },
  loading: false,
  error: null,
  successMsg: '',
};

const costCenterRequestHeaderSlice = createSlice({
  name: 'requestHeader',
  initialState,
  reducers: {
    setRequestHeaderData(state, action) {
      state.requestHeader = { ...state.requestHeader, ...action.payload };
      state.successMsg = `Request Header Created Successfully! Request ID: ${action.payload.requestId}`;
      state.error = null;
    },
    updateHeaderField: (state, action) => {
      const { field, value } = action.payload;
      state.requestHeader[field] = value;
    },
    setHeaderFinalized: (state, action) => {
      state.isHeaderFinalized = action.payload;
    },
    setLoading(state, action) {
      state.loading = action.payload;
    },
    setError(state, action) {
      state.error = action.payload;
      state.successMsg = '';
    },
    clearStatus(state) {
      state.error = null;
      state.successMsg = '';
    }
  },
});

export const { setRequestHeaderData,updateHeaderField,setHeaderFinalized, setLoading, setError, clearStatus } = costCenterRequestHeaderSlice.actions;
export default costCenterRequestHeaderSlice.reducer;
