import React, { useEffect,useState } from 'react'
import applicationIsDown from '../utilityImages/applicationIsDown.jpg'
import { Stack,Typography } from '@mui/material'
const ApplicationDownScreen = () => {
    let [count,setCount] = useState(5)

    useEffect(()=>{
        setTimeout(() => {
            window.location.href = "/do/logout"

        },5000); 
    },[])
    // useEffect(()=>{
    //  if(count===0){
    //  }
    // },[count])
  return (
    <Stack sx={{
        backgroundColor:'',
        display:'flex',
        justifyContent:'center',
        alignItems:'center',
        width:'100vw',
        height:'100vh',
        flexDirection: "column",
        opacity: ".5"
    }}>
        <img
        src={applicationIsDown}
        style={{
            height: '29rem',
            width:'40rem'
        }}
        >
        </img>
        <Typography
                  sx={{
                    color: "grey",
                    fontWeight: "600",
                    fontSize: "16px",
                    marginTop: "1rem",
                  }}
                >
                 {`Sorry, There was a problem loading the application. Please try after sometimes.`}
                </Typography>
    </Stack>
  )
}

export default ApplicationDownScreen