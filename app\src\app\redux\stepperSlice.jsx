import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  activeStep: 0,
};

const stepperSlice = createSlice({
  name: 'stepper',
  initialState,
  reducers: {
    setActiveStep: (state, action) => {
      state.activeStep = action.payload;
    },
    resetStepper: (state) => {
      state.activeStep = 0;
    },
  },
});

export const { setActiveStep, resetStepper } = stepperSlice.actions;
export default stepperSlice.reducer;
