import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Grid,
  Autocomplete,
  IconButton,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SaveIcon from "@mui/icons-material/Save";
import { DateRangePicker } from "rsuite";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { setPayload } from "../../app/editPayloadSlice";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { setEditMultipleMaterial } from "../../app/initialDataSlice";
import dayjs from "dayjs";

function transformApiData(apiValue, dropDownData) {
  if (Array.isArray(dropDownData)) {
    const matchingOption = dropDownData.find(
      (option) => option.code === apiValue
    );
    return matchingOption || "";
  } else {
    return "";
  }
}
const EditableField = ({
  label,
  value,
  units,
  onSave,
  isEditMode,
  isExtendMode,
  options = [],
  type,
}) => {
  const [editedValue, setEditedValue] = useState(value);
  const [changeStatus, setChangeStatus] = useState(false);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  const dispatch = useDispatch();
  const transformedValue = transformApiData(editedValue, dropDownData);
  console.log("dropdownData", editedValue);
  console.log("value e", value);
  console.log("label", label);
  console.log("units", units);
  console.log("transformedValue", transformedValue);

  const editField = useSelector((state) => state.edit.payload);
  let errorFields = useSelector((state) => state.payload.errorFields);

  console.log("editField", editField);
  const fieldData = {
    label,
    value: editedValue,
    units,
    type,
  };
  console.log("fieldData", fieldData);
  const handleSave = () => {
    onSave(fieldData);
  };
  let key = label
    .replaceAll("(", "")
    .replaceAll(")", "")
    .replaceAll("/", "")
    .replaceAll("-", "")
    .replaceAll(".", "")
    .split(" ")
    .join("");
  useEffect(() => {
    setEditedValue(value);
  }, [value]);

  const onEdit = (newValue) => {
    console.log('checkonedit')
    dispatch(
      setPayload({
        keyname: key
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: newValue,
      })
    );
    // dispatch(setEditMultipleMaterial(newValue))
  };
  useEffect(() => {
    console.log("lkey", key);
    console.log("data", value);
    dispatch(
      setPayload({
        keyname: key
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: value ? value : "",
      })
    );
  }, []);

  const today = dayjs();

  console.log("editedValue[key] ", dropDownData[key]);
  console.log("editedValue[key] ", editedValue);
  return (
    <Grid item>
      <Stack>
        {isEditMode || isExtendMode ? (
          <>
            <Typography variant="body2" color="#777">
              {label}
            </Typography>
            {type === "Drop Down" ? (
              <Autocomplete
                options={dropDownData[key] ?? []}
                value={
                  (editedValue &&
                    dropDownData[key]?.filter((x) => x.code === editedValue)) ||
                  ""
                }
             
                onChange={(event, newValue) => {
                  onEdit(newValue.code);
                  console.log("newValue", newValue);
                  setEditedValue(newValue.code);
                  setChangeStatus(true);
                  console.log("keys", key);
                }}
                getOptionLabel={(option) => {
                  console.log("optionoptionoption", option);
                  return option === ""
                    ? ""
                    : `${option && option[0]?.code} - ${
                        option && option[0]?.desc
                      }`;
                }}
                // isOptionEqualToValue={(a,b)=>{ return a.code===b.code}}
                renderOption={(props, option) => {
                  console.log("option vakue", option);
                  return (
                    <li {...props}>
                      <Typography style={{ fontSize: 12 }}>
                        {`${option?.code} - ${option?.desc}`}
                      </Typography>
                    </li>
                  );
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    size="small"
                    label={null}
                    placeholder={`Select ${label}`}
                  />
                )}
              />
            ) : type === "Input" ? (
              <TextField
                variant="outlined"
                size="small"
                value={editedValue}
                onChange={(event) => {
                  const newValue = event.target.value;
                  onEdit(newValue);
                  setEditedValue(newValue);
                }}
                placeholder={`Enter ${label}`}
              />
            ) : type === "Calendar" ? (
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  slotProps={{ textField: { size: "small" } }}
                  value={dropDownData[key]
                  ?dropDownData[key]
                  :""}
                  defaultValue={today}
                  placeholder="Select Date Range"
                  onChange={(newValue) =>
                    dispatch(
                      setPayload({
                        keyname: key,
                        data: "/Date(" + Date.parse(newValue) + ")/",
                      })
                    )
                  }
                  onError={errorFields.includes(key)}
                  // required={
                  //   props.details.visibility === "0" ||
                  //   props.details.visibility === "Required"
                  // }
                />
              </LocalizationProvider>
            ) : type === "Radio Button" ? (
              <Checkbox
                  sx={{ padding: 0 }}
                  checked={editedValue}
                  onChange={(event, newValue) => {
                    onEdit(newValue);
                    setEditedValue(newValue);
                  }}
                />
            ) : (
              ""
            )}
          </>
        ) : (
          <>
            <>
              <Typography variant="body2" color="#777">
                {label}
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {editedValue} 
                {/* {type === "Radio Button" ? (
                  <Checkbox
                    sx={{ padding: 0 }}
                    checked={editedValue==="true"?true:false}
                    disabled
                  />
                ) : (
                  ""
                )} */}
              </Typography>
            </>
          </>
        )}
      </Stack>
    </Grid>
  );
};

export default EditableField;
