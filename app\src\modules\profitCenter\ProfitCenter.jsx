import React, { useMemo } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ReusableBackDrop from "../../components/Common/ReusableBackDrop";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { doAjax } from "../../components/Common/fetchService";
import {
  Button,
  Checkbox,
  Grid,
  Paper,
  IconButton,
  Typography,
  TextField,
  Box,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Popper,
  BottomNavigation,
  ListItemText,
  tooltipClasses,
  Autocomplete,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  ButtonGroup,
  ClickAwayListener,
  MenuList,
  Divider,
  FormControlLabel,
  FormGroup,
  Backdrop,
  CircularProgress,
  ToggleButtonGroup,
  ToggleButton,
  FormLabel,
  Radio,
  RadioGroup,
} from "@mui/material";

import moment from "moment/moment";
import { Stack } from "@mui/system";
import Select from "@mui/material/Select";
import { FormControl, MenuItem } from "@mui/material";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import ReusableDialog from "../../components/Common/ReusableDialog";
import { useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import styled from "@emotion/styled";
import { commonFilterClear, commonFilterUpdate } from "@app/commonFilterSlice";
import { v4 as uuidv4 } from "uuid";
import DateRange from "../../components/Common/DateRangePicker";
import useGenericDtCall from "@hooks/useGenericDtCall";
import { API_CODE, DECISION_TABLE_NAME, ERROR_MESSAGES, PAGESIZE, SEARCH_FIELD_TYPES, VISIBILITY_TYPE } from "@constant/enum";
import {
  button_Marginleft,
  button_Outlined,
  button_Primary,
  container_filter,
  container_table,
  font_Small,
  iconButton_SpacingSmall,
  outerContainer_Information,
  outermostContainer,
  outermostContainer_Information,
} from "../../components/Common/commonStyles";
import {
  destination_IDM,
  destination_ProfitCenter_Mass,
} from "../../destinationVariables";

import ClearIcon from "@mui/icons-material/Clear";
import ReusableTable from "../../components/Common/ReusableTable";
import { setDropDown } from "@app/dropDownDataSlice";
import { LocalizationProvider } from "@mui/x-date-pickers";
import {
  setProfitCenterAddressTab,
  setProfitCenterBasicDataTab,
  setProfitCenterCommunicationTab,
  setProfitCenterHistoryTab,
  setProfitCenterCompCodesTab,
  setProfitCenterIndicatorsTab,
  setHandleMassMode,
  clearProfitCenter,
  setSingleProfitCenterPayload,
  setSelectedHeader,
  setSelectedHeaderTab,
} from "@app/profitCenterTabsSlice";
import AttachmentUploadDialog from "../../components/Common/AttachmentUploadDialog";
import ReusableIcon from "../../components/common/ReusableIcon";
import {  saveExcel } from "../../functions";
import { clearTaskData, setTaskData } from "@app/userManagementSlice";
import WarningIcon from "@mui/icons-material/Warning";

import ReusablePreset from "../../components/Common/ReusablePresetFilter";
import {
  clearPayload,
} from "@app/editPayloadSlice";

import {
  clearArtifactId,
  setAttachmentType,
} from "@app/initialDataSlice";
import { colors } from "@constant/colors";
import FilterListIcon from "@mui/icons-material/FilterList";
import SearchIcon from "@mui/icons-material/Search";
import useLang from "@hooks/useLang";
const HtmlTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "#f5f5f9",
    color: "rgba(0, 0, 0, 0.87)",
    maxWidth: 250,
    border: "1px solid #dadde9",
  },
}));
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

// const exportAsPicture = () => {
//   const html = document.getElementsByTagName("HTML")[0];
//   const body = document.getElementsByTagName("BODY")[0];
//   let htmlWidth = html.clientWidth;
//   let bodyWidth = body.clientWidth;

//   const data = document.getElementById("e-invoice-export"); //CHANGE THIS ID WITH ID OF OUTERMOST DIV CONTAINER
//   const newWidth = data.scrollWidth - data.clientWidth;

//   if (newWidth > data.clientWidth) {
//     htmlWidth += newWidth;
//     bodyWidth += newWidth;
//   }

//   html.style.width = htmlWidth + "px";
//   body.style.width = bodyWidth + "px";

//   html2canvas(data)
//     .then((canvas) => {
//       return canvas.toDataURL("image/png", 1.0);
//     })
//     .then((image) => {
//       saveAs(image, "E-InvoiceReport.png"); //CHANGE THE NAME OF THE FILE
//       html.style.width = null;
//       body.style.width = null;
//     });
// };

const saveAs = (blob, fileName) => {
  const elem = window.document.createElement("a");
  elem.href = blob;
  elem.download = fileName;
  (document.body || document.documentElement).appendChild(elem);
  if (typeof elem.click === "function") {
    elem.click();
  } else {
    elem.target = "_blank";
    elem.dispatchEvent(
      new MouseEvent("click", {
        view: window,
        bubbles: true,
        cancelable: true,
      })
    );
  }
  URL.revokeObjectURL(elem.href);
  elem.remove();
};
const ProfitCenter = () => {
  const [snackbar, setSnackbar] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [isDropDownLoading, setIsDropDownLoading] = useState(false);
  const [pcInputValue, setPcInputValue] = useState("");
  const [pcLongDesc, setPcLongDesc] = useState("");
  const [pcPerson, setPcPerson] = useState("");
  const [pcCity, setPcCity] = useState("");
  const [pcShortDesc, setPcShortDesc] = useState("");
  const [pcStreet, setPcStreet] = useState("");
  const [pcCreatedBy, setPcCreatedBy] = useState("");
  const [selectedComanyCode, setselectedComanyCode] = useState([]);
  const [selectedProfitCenter, setselectedProfitCenter] = useState([]);
  const [selectedLongText, setselectedLongText] = useState([]);
  const [selectedPersonResponsible, setselectedPersonResponsible] = useState(
    []
  );
  const [selectedCountry, setselectedCountry] = useState([]);
  const [selectedCreatedBy, setselectedCreatedBy] = useState([]);
  const [selectedProfitCenterName, setselectedProfitCenterName] = useState([]);
  const [selectedStreet, setselectedStreet] = useState([]);
  const [selectedCity, setselectedCity] = useState([]);
  const [selectedRegion, setselectedRegion] = useState([]);
  const [searchInput, setSearchInput] = useState("");

  const [selectedPresetComanyCode, setselectedPresetComanyCode] = useState([]);
  const [selectedPresetProfitCenter, setselectedPresetProfitCenter] = useState(
    []
  );
  const [selectedPresetLongText, setselectedPresetLongText] = useState([]);
  const [selectedPresetPersonResponsible, setselectedPresetPersonResponsible] =
    useState([]);
  const [selectedPresetCountry, setselectedPresetCountry] = useState([]);
  const [selectedPresetCreatedBy, setselectedPresetCreatedBy] = useState([]);
  const [selectedPresetProfitCenterName, setselectedPresetProfitCenterName] =
    useState([]);
  const [selectedPresetStreet, setselectedPresetStreet] = useState([]);
  const [selectedPresetCity, setselectedPresetCity] = useState([]);
  const [selectedPresetRegion, setselectedPresetRegion] = useState([]);
  const [selectedPresetValues, setSelectedPresetValues] = useState({});
  const [selectedDateRange, setSelectedDateRange] = useState([]);
  const [requiredArrayDetailsMass, setRequiredArrayDetailsMass] = useState([]);
  const { t } = useLang();
  const [items,setItem] = useState(); 

  let demoData = {
    statusCode: 200,
    body: {
      list: [
        {
          CreatedBy: "SSAMANTAR1",
          Description: "LG DATAS 03601",
          ProfitCenter: "P202153601",
          PersonResponsible: "VTJENNINGS",
          ProfitCenterGroup: "ET_PCA",
          Segment: "",
          Role: "Z3S:ETP:STP:PU:COMMON_UPDATE",
          LockIndicator: "",
          Country: "US",
          Region: "TX",
          Street: "7220 JW PEAVY DR",
          CreatedOn: "/Date(1725321600000)/",
          ProfitCenterName: "DATA TESTS 3601",
          CompanyCode: "2021",
          ControllingArea: "ETCA",
          Location: "HOUSTON",
        },
        {
          CreatedBy: "SSAMANTAR1",
          Description: "LG DATAS 03600WSWSWSDDEED",
          ProfitCenter: "P202153600",
          PersonResponsible: "VTJENNINGS",
          ProfitCenterGroup: "ET_PCA",
          Segment: "",
          Role: "Z3S:ETP:STP:PU:COMMON_UPDATE",
          LockIndicator: "",
          Country: "US",
          Region: "TX",
          Street: "7220 JW PEAVY DR",
          CreatedOn: "/Date(1725321600000)/",
          ProfitCenterName: "DATA TESTS 3600",
          CompanyCode: "2021",
          ControllingArea: "ETCA",
          Location: "HOUSTON",
        },
        {
          CreatedBy: "SSAMANTAR1",
          Description: "LG DATAS 03596",
          ProfitCenter: "P202153596",
          PersonResponsible: "VTJENNINGS",
          ProfitCenterGroup: "ET_PCA",
          Segment: "",
          Role: "Z3S:ETP:STP:PU:COMMON_UPDATE",
          LockIndicator: "",
          Country: "US",
          Region: "TX",
          Street: "7220 JW PEAVY DR",
          CreatedOn: "/Date(1725321600000)/",
          ProfitCenterName: "DATA TESTS 3596",
          CompanyCode: "2021",
          ControllingArea: "ETCA",
          Location: "HOUSTON",
        },
      ],
    },
  };

  const StyledAccordion = styled(Accordion)(({ theme }) => ({
    marginTop: "0px !important",
    border: `1px solid ${colors.primary.border}`,
    borderRadius: "8px",
    boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
    "&:not(:last-child)": {
      borderBottom: 0,
    },
    "&:before": {
      display: "none",
    },
  }));

  const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
    minHeight: "2rem !important",
    margin: "0px !important",
    backgroundColor: colors.primary.ultraLight,
    borderRadius: "8px 8px 0 0",
    transition: "all 0.2s ease-in-out",
    "&:hover": {
      backgroundColor: `${colors.primary.light}20`,
    },
  }));

  const LabelTypography = styled(Typography)({
    fontSize: "0.75rem",
    color: colors.primary.dark,
    marginBottom: "0.25rem",
    fontWeight: 500,
  });
  const ActionButton = styled(Button)({
    borderRadius: "4px",
    padding: "4px 12px",
    textTransform: "none",
    fontSize: "0.875rem",
  });
  const ButtonContainer = styled(Grid)({
    display: "flex",
    justifyContent: "flex-end",
    paddingRight: "0.75rem",
    paddingBottom: "0.75rem",
    paddingTop: "0rem",
    gap: "0.5rem",
  });

  const memoizedCompCodeValue = useMemo(() => {
    if (selectedComanyCode.length > 0) return selectedComanyCode;
    else if (selectedPresetComanyCode.length > 0)
      return selectedPresetComanyCode;
    else return [];
  }, [selectedComanyCode, selectedPresetComanyCode]);

  const memoizedPCValue = useMemo(() => {
    if (selectedProfitCenter.length > 0) return selectedProfitCenter;
    else if (selectedPresetProfitCenter.length > 0)
      return selectedPresetProfitCenter;
    else return [];
  }, [selectedProfitCenter, selectedPresetProfitCenter]);

  const memoizedLDValue = useMemo(() => {
    if (selectedLongText.length > 0) return selectedLongText;
    else if (selectedPresetLongText.length > 0) return selectedPresetLongText;
    else return [];
  }, [selectedLongText, selectedPresetLongText]);

  const memoizedSDValue = useMemo(() => {
    if (selectedProfitCenterName.length > 0) return selectedProfitCenterName;
    else if (selectedPresetProfitCenterName.length > 0)
      return selectedPresetProfitCenterName;
    else return [];
  }, [selectedProfitCenterName, selectedPresetProfitCenterName]);

  const memoizedPRValue = useMemo(() => {
    if (selectedPersonResponsible.length > 0) return selectedPersonResponsible;
    else if (selectedPresetPersonResponsible.length > 0)
      return selectedPresetPersonResponsible;
    else return [];
  }, [selectedPersonResponsible, selectedPresetPersonResponsible]);

  const memoizedStreetValue = useMemo(() => {
    if (selectedStreet.length > 0) return selectedStreet;
    else if (selectedPresetStreet.length > 0) return selectedPresetStreet;
    else return [];
  }, [selectedStreet, selectedPresetStreet]);

  const memoizedCityValue = useMemo(() => {
    if (selectedCity.length > 0) return selectedCity;
    else if (selectedPresetCity.length > 0) return selectedPresetCity;
    else return [];
  }, [selectedCity, selectedPresetCity]);

  const memoizedCreatedByValue = useMemo(() => {
    if (selectedCreatedBy.length > 0) return selectedCreatedBy;
    else if (selectedPresetCreatedBy.length > 0) return selectedPresetCreatedBy;
    else return [];
  }, [selectedCreatedBy, selectedPresetCreatedBy]);

  const [openSearchDialog, setOpenSearchDialog] = useState(false);
  const [searchDialogTitle, setSearchDialogTitle] = useState("");
  const [searchDialogMessage, setSearchDialogMessage] = useState();

  // const [controllingArea, setControllingArea] = useState();
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const appSettings = useSelector((state) => state.appSettings["Format"]);

  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [downloadType, setDownloadType] = useState("systemGenerated");

  // let userData = useSelector((state) => state?.userManagement?.userData);
  // let iwaAccessData = useSelector(
  //   (state) => state?.userManagement?.entitiesAndActivities?.["Return Order"]
  // );
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;

  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };

  const handleOnClick = (materialNumber) => {
    navigate(
      "/masterDataCockpit/materialMaster/displayMaterialDetail/" +
        materialNumber
    );
  };

  const HtmlTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: "#f5f5f9",

      color: "rgba(0, 0, 0, 0.87)",

      maxWidth: 250,

      border: "1px solid #dadde9",
    },
  }));
  const [Status_ServiceReqForm, setStatus_ServiceReqForm] = useState(false);
  const [duplicateFieldsData, setDuplicateFieldsData] = useState([]);
  const [showTableInDialog, setShowTableInDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [timerId, setTimerId] = useState(null);
  const [tableLoading, setTableLoading] = useState(false);
  const [value, setValue] = useState(null);
  const ariaLabel = { "aria-label": "description" };
  const [rmDataRows, setRmDataRows] = useState([]);
  const [tableData, setTableData] = useState([...rmDataRows]);
  const [UserName, setUserName] = React.useState("");
  const [openSnackBaraccept, setOpenSnackBaraccept] = useState(false);
  const [confirmingid, setConfirmingid] = useState("");
  const [materialNumber, setMaterialNumber] = useState("");
  const [confirmStatus, setConfirmStatus] = useState(true);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [companyCodeSet, setCompanyCodeSet] = useState([]);
  const [plantCodeSet, setPlantCodeSet] = useState([]);
  const [vendorDetailsSet, setVendorDetailsSet] = useState([]);
  const [taskstatusSet, setTasksttusSet] = useState([]);
  const [disableButton, setDisableButton] = useState(true);
  const [selectedRow, setSelectedRow] = useState([]);
  const [selectedDetails, setSelectedDetails] = useState([]);
  const [downloadError, setdownloadError] = useState(false);
  const [selectedValues, setSelectedValues] = useState({});
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [displayedFields, setDisplayedFields] = useState([]);
  const [filterFieldData, setFilterFieldData] = useState({});
  const [matType, setMatType] = useState([]);
  const [matGroup, setMatGroup] = useState([]);
  const [viewDetailpage, setViewDetailpage] = useState(false);
  const [matNumber, setMatNumber] = useState([]);
  const [dynamicOptions, setDynamicOptions] = useState([]);
  const [dynamicColumns, setDynamicColumns] = useState([]);
  const {getDtCall: getMasterDataColumn, dtData:masterDataDtResponse} = useGenericDtCall();
  const {getDtCall:getSearchParams, dtData:dtSearchParamsResponse } = useGenericDtCall();
  const [searchParameters, setSearchParameters] = useState([]);
  const [plantForWarehouse, setPlantForWarehouse] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogOpenCreate, setDialogOpenCreate] = useState(false);
  const [fullWidth, setFullWidth] = useState(true);
  const [maxWidth, setMaxWidth] = useState("sm");
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [successMsg, setsuccessMsg] = useState(false);
  const [newControllingArea, setNewControllingArea] = useState("");
  const [newProfitCenter, setNewProfitCenter] = useState("");
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [skip, setSkip] = useState(0);
  const [confirmation, setconfirmation] = useState([]);
  const [confirmationText, setConfirmationText] = useState(null);
  const [poHeader, setPoHeader] = useState(null);
  const [roCount, setroCount] = useState(0);
  const [count, setCount] = useState(0);
  const [showBtmNav, setShowBtmNav] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [openDialogIDM, setOpenDialogIDM] = useState(false);
  const [openSnackbarDialog, setOpenSnackbarDialog] = useState(false);
  const [opendialog2, setOpendialog2] = useState(false);
  const [opendialog3, setOpendialog3] = useState(false);
  const [openforwarddialog, setOpenforwarddialog] = useState(false);
  const [rejectInputText, setRejectInputText] = useState("");
  const [acceptInputText, setAcceptInputText] = useState("");
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [profitCenterValid, setProfitCenterValid] = useState(false);
  const [controllingAreaValid, setControllingAreaValid] = useState(false);
  const [anchorEl_Preset, setAnchorEl] = useState(null);
  const anchorRef = React.useRef(null);
  const [openButton, setOpenButton] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [openButtonChange, setOpenButtonChange] = useState(false);
  const anchorRefChange = React.useRef(null);
  const anchorRefCreate = React.useRef(null);
  const [newCompanyCode, setNewComapnyCode] = useState("");
  const [newCompanyCodeCopy, setNewComapnyCodeCopy] = useState("");
  const [newProfitCenterName, setNewProfitCenterName] = useState("");
  // const [handleMassMode, setHandleMassMode] = useState("");
  const [selectedIndexCreate, setSelectedIndexCreate] = useState(0);
  const [openButtonCreate, setOpenButtonCreate] = useState(false);
  const [newControllingAreaCopyFrom, setNewControllingAreaCopyFrom] =
    useState("");
  const [selectedIndexChange, setSelectedIndexChange] = useState(0);
  const [selectedMassChangeRowData, setSelectedMassChangeRowData] = useState(
    []
  );
  const [isValidationError, setIsValidationError] = useState(false);
  const [isValidationErrorwithCopy, setIsValidationErrorwithCopy] =
    useState(false);
  const [newProgfitCenterValid, setNewProfitCenterValid] = useState(false);
  const [newPofitCenterValidWithCopy, setNewProfitCenterValidWithCopy] =
    useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [isCheckboxSelected, setIsCheckboxSelected] = useState(true);
  const [tableRows, setTableRows] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [downloadMultiple, setDownloadMultiple] = useState(false);
  const [ruleData, setRuleData] = useState([]);
  const [profitCenterLength, setProfitCenterLength] = useState("");
  const [openSelectColumnDialog, setOpenSelectColumnDialog] = useState(false);
  const [selectedTemplateData, setSelectedTemplateData] = useState("");
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const [blurLoading, setBlurLoading] = useState(false);
  const [isAnyFieldEmpty, setIsAnyFieldEmpty] = useState(false);

  const [dataListAllOtherChanges, setDataListAllOtherChanges] = useState([]);
  const [dataListBlockNames, setDataListBlockNames] = useState([]);
  const [dataListProfitCenterChange, setDataListProfitCenterChange] = useState(
    []
  );
  const [fieldSelectionFromIdm, setFieldselectionFromIdm] = useState([]);
  const [dataListAllOtherChangesSelected, setDataListAllOtherChangesSelected] =
    useState([]);
  const [selectedTab, setSelectedTab] = useState("ALL OTHER CHANGES");
  const [dataListBlockNamesSelected, setDataListBlockNamesSelected] = useState(
    []
  );
  const [
    dataListTemporaryBlockNamesSelected,
    setDataListTemporaryBlockNamesSelected,
  ] = useState([]);
  const [dataListTemporaryBlockNames, setDataListTemporaryBlockNames] =
    useState([]);
  const [
    dataListProfitCenterChangeSelected,
    setDataListProfitCenterChangeSelected,
  ] = useState([]);
  const [filteredRuleData, setFilteredRuleData] = useState(false);
  const [filteredRuleDataMass, setFilteredRuleDataMass] = useState(false);

  const [alignment, setAlignment] = useState("ALL OTHER CHANGES");
  const [dialogTitle, setDialogTitle] = useState("");
  const [inputValue, setInputValue] = useState("");
  const [loaderMessage, setLoaderMessage] = useState("");

  const [dialogOkText, setDialogOkText] = useState("");

  const [radioValue, setRadioValue] = useState("yes");
  const [openDownloadChangeDialog, setOpenDownloadChangeDialog] =
    useState(false);

  const NoMaxWidthTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))({
    [`& .${tooltipClasses.tooltip}`]: {
      maxWidth: "none",
    },
  });

  const handleDownloadDialogClose = () => {
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
  };
  const handleDownloadChangeDialogClose = () => {
    setOpenDownloadChangeDialog(false);
    setDownloadType("systemGenerated");
  };
  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };
  const handleMultipleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };
  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownloadCreate();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };

  const onMultipleDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleApply();
      handleDownloadChangeDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleApplyEmail();
      handleDownloadChangeDialogClose();
    }
  };
  //const [CompCodeDataPC, setCompCodeDataPC] = useState([]);

  const handleRadioChange = (event) => {
    setRadioValue(event.target.value);
  };

  const initialScreenData = useSelector(
    (state) => state.profitCenter.singlePCPayload
  );

  const duplicateFieldsColumns = [
    {
      field: "profitCenter",
      headerName: "Profit Center",
      editable: false,
      flex: 1,
      width: 150,
    },
    {
      field: "reqId",
      headerName: "Req Id",
      editable: false,
      flex: 1,
      width: 200,
    },
    {
      field: "requestedBy",
      headerName: "Requested By",
      editable: false,
      flex: 1,
      width: 250,
    },
  ];

  const [dataList, setDataList] = useState([
    { name: "Name", id: 1 },
    { name: "Long Description", id: 2 },
    { name: "Person Respons.", id: 3 },
    { name: "Segment", id: 4 },
    { name: "Lock indicator", id: 5 },
    { name: "Name 1", id: 6 },
    { name: "Name 2", id: 7 },
    { name: "Name 3", id: 8 },
    { name: "Name 4", id: 9 },
    { name: "Street", id: 10 },
    { name: "City", id: 11 },
    { name: "Country/Reg.", id: 12 },
    { name: "Postal Code", id: 13 },
    { name: "Region", id: 14 },
    { name: "Blocking Status", id: 15 },

    // Add more hardcoded values as needed
  ]);
  const [selectedListItems, setSelectedListItems] = useState([]);
  const options = ["Create Multiple", "Upload Template ", "Download Template "];
  const optionsChange = [
    "Change Multiple",
    "Upload Template ",
    "Download Template ",
  ];
  const optionsCreateSingle = ["Create Single", "With Copy", "Without Copy"];

  const openAnchor = Boolean(anchorEl_Preset);

  let iwaAccessData = useSelector(
    (state) =>
      state?.userManagement?.entitiesAndActivities?.["Display Material"]
  );
  let userData = useSelector((state) => state?.userManagement?.userData);
  const [checkValidationProfitCenter, setCheckValidationProfitCenter] =
    useState(false);
  const pcSearchForm = useSelector(
    (state) => state.commonFilter["ProfitCenter"]
  );

  const formcontroller_SearchBar = useSelector(
    (state) => state.commonSearchBar["ProfitCenter"]
  );
  const handleMassModePC = useSelector(
    (state) => state.profitCenter.handleMassMode
  );
  const buttonsIDM = useSelector((state) => state?.profitCenter?.buttonsIDM);

  const handleApply = () => {
    let filterDataForLock = [];
    if (alignment === "ALL OTHER CHANGES") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
      );
      dataListAllOtherChangesSelected.forEach((input) => {
        filteredData?.map((item, index) => {
          if (item.MDG_SELECT_OPTION == input.name) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item?.MDG_FIELD_NAME;
            ///fieldSelectionCompanyCode.push(COHash)
            filterDataForLock.push(COHash);
          }
        });
      });
    } else if (alignment === "BLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
      );

      dataListBlockNamesSelected.forEach((input) => {
        filteredData?.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let blockHash = {};
            blockHash["id"] = index;
            blockHash["name"] = item?.MDG_FIELD_NAME;
            filterDataForLock.push(blockHash);
          }
        });
      });
    } else if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
      );
      dataListTemporaryBlockNamesSelected.forEach((input) => {
        filteredData?.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let blockHash = {};
            blockHash["id"] = index;
            blockHash["name"] = item?.MDG_FIELD_NAME;
            ///fieldSelectionCompanyCode.push(COHash)
            filterDataForLock.push(blockHash);
          }
        });
      });
    }

    let changedFieldsToCheck = filterDataForLock
      ?.map((item) => item?.name)
      ?.join(",");

    if (downloadMultiple === true && selectedRows.length === 0) {
      if (changedFieldsToCheck !== "") {
        handleChangeDownloadEmpty();
        setDownloadMultiple(false);
        setOpenDialogIDM(false);
        setSelectedListItems([]);
        setDataListAllOtherChangesSelected([]);
        return;
      } else {
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please Select Any Field To Proceed?");
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
        return;
      }
    }

    let payload = [];
    if (downloadMultiple === true && selectedRows.length > 0) {
      if (changedFieldsToCheck === "") {
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please Select Any Field To Proceed?");
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
        return;
      }
      payload = selectedMassChangeRowData.map((row) => ({
        coArea: row.controllingArea,
        profitCenter: row.profitCenter,
        changedFieldsToCheck: changedFieldsToCheck,
      }));
    } else {
      if (changedFieldsToCheck === "") {
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please Select Any Field To Proceed?");
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
        return;
      }
      payload = [
        {
          coArea: tableRows?.controllingArea,
          profitCenter: tableRows?.profitCenter,
          changedFieldsToCheck: changedFieldsToCheck,
        },
      ];
    }

    const hSuccess = (response) => {
      const hasError = response.some((item) => item.statusCode !== 200);
      if (!hasError) {
        if (downloadMultiple === true) {
          // mass change case
          if (selectedRows.length > 0) {
            handleChangeDownload();
            setDownloadMultiple(false);
            setOpenDialog(false);
            setOpenDialogIDM(false);
            setSelectedListItems([]);
            setDataListAllOtherChangesSelected([]);
          } else if (selectedRows.length === 0) {
            handleChangeDownloadEmpty();
            setDownloadMultiple(false);
            setOpenDialog(false);
            setOpenDialogIDM(false);
            setSelectedListItems([]);
            setDataListAllOtherChangesSelected([]);
          }
        } else {
          //single change case
          dispatch(setSelectedHeader(dataListAllOtherChangesSelected));
          dispatch(setSelectedHeaderTab(selectedTab));
          if (alignment === "ALL OTHER CHANGES") {
            let filterDataWithSelectedData = [];
            const filteredData = fieldSelectionFromIdm.filter(
              (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
            );
            dataListAllOtherChangesSelected.forEach((input) => {
              filteredData?.map((item, index) => {
                if (item.MDG_SELECT_OPTION == input.name) {
                  let COHash = {};
                  COHash["id"] = index;
                  COHash["name"] = item?.MDG_FIELD_NAME;
                  ///fieldSelectionCompanyCode.push(COHash)
                  filterDataWithSelectedData.push(COHash);
                }
              });
            });
            let newFilterData = Object?.values(
              filterDataWithSelectedData?.reduce((acc, item) => {
                if (!acc[item?.name]) acc[item?.name] = item;
                return acc;
              }, {})
            );
            dispatch(setFields(newFilterData));

            //dispatch(setFields(selectedListItems))
          } else if (alignment === "BLOCK") {
            const filteredData = fieldSelectionFromIdm.filter(
              (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
            );
            let filterDataWithSelectedData = [];
            dataListBlockNamesSelected.forEach((input) => {
              filteredData?.map((item, index) => {
                if (item.MDG_SELECT_OPTION === input.name) {
                  let blockHash = {};
                  blockHash["id"] = index;
                  blockHash["name"] = item?.MDG_FIELD_NAME;
                  ///fieldSelectionCompanyCode.push(COHash)
                  filterDataWithSelectedData.push(blockHash);
                }
              });
            });
            let newFilterData = Object?.values(
              filterDataWithSelectedData?.reduce((acc, item) => {
                if (!acc[item?.name]) acc[item?.name] = item;
                return acc;
              }, {})
            );
            dispatch(setFields(newFilterData));
            // dispatch(setFields(filterDataWithSelectedData))
          } else if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
            const filteredData = fieldSelectionFromIdm.filter(
              (item) =>
                item.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
            );
            let filterDataWithSelectedData = [];
            dataListTemporaryBlockNamesSelected.forEach((input) => {
              filteredData?.map((item, index) => {
                if (item.MDG_SELECT_OPTION === input.name) {
                  let blockHash = {};
                  blockHash["id"] = index;
                  blockHash["name"] = item?.MDG_FIELD_NAME;
                  ///fieldSelectionCompanyCode.push(COHash)
                  filterDataWithSelectedData.push(blockHash);
                }
              });
            });
            let newFilterData = Object?.values(
              filterDataWithSelectedData?.reduce((acc, item) => {
                if (!acc[item?.name]) acc[item?.name] = item;
                return acc;
              }, {})
            );
            dispatch(setFields(newFilterData));

            // dispatch(setFields(filterDataWithSelectedData))
          }
          //dispatch(setFields(selectedListItems));
          // dispatch(clearProfitCenter())
          // dispatch(clearProfitCenterPayloadGI())
          if (selectedTab === "TEMPORARY BLOCK/UNBLOCK") {
            navigate(
              "/masterDataCockpit/profitCenterNew/changeETPCTemporaryBlock",
              {
                state: tableRows,
              }
            );
          } else {
            navigate("/masterDataCockpit/profitCenter/changeETPC", {
              state: tableRows,
            });
          }
        }
      } else {
        const filteredData = response.filter((item) => item.statusCode === 400);

        let duplicateFieldsArr = [];

        filteredData?.map((item, index) => {
          let dataHash = {};
          const profitCenter = item?.message
            ?.split("Profit Center: ")[1]
            ?.split(",")[0];
          dataHash["id"] = index;
          dataHash["profitCenter"] = profitCenter;
          dataHash["reqId"] =
            item?.body?.EditIds?.[0] || item?.body?.MassEditIds?.[0];
          dataHash["requestedBy"] = item?.body?.RequestCreatedBy?.[0];

          duplicateFieldsArr.push(dataHash);
        });

        setDuplicateFieldsData(duplicateFieldsArr);

        setShowTableInDialog(true);
      }
    };
    const hError = (error) => {
      console.log("Failure");
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/alter/checkDuplicatePCRequest`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleApplyEmail = () => {
    let filterDataForLock = [];
    if (alignment === "ALL OTHER CHANGES") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
      );
      dataListAllOtherChangesSelected.forEach((input) => {
        filteredData?.map((item, index) => {
          if (item.MDG_SELECT_OPTION == input.name) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item?.MDG_FIELD_NAME;
            ///fieldSelectionCompanyCode.push(COHash)
            filterDataForLock.push(COHash);
          }
        });
      });
    } else if (alignment === "BLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
      );

      dataListBlockNamesSelected.forEach((input) => {
        filteredData?.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let blockHash = {};
            blockHash["id"] = index;
            blockHash["name"] = item?.MDG_FIELD_NAME;
            filterDataForLock.push(blockHash);
          }
        });
      });
    } else if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
      );
      dataListTemporaryBlockNamesSelected.forEach((input) => {
        filteredData?.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let blockHash = {};
            blockHash["id"] = index;
            blockHash["name"] = item?.MDG_FIELD_NAME;
            ///fieldSelectionCompanyCode.push(COHash)
            filterDataForLock.push(blockHash);
          }
        });
      });
    }

    let changedFieldsToCheck = filterDataForLock
      ?.map((item) => item?.name)
      ?.join(",");

    if (downloadMultiple === true && selectedRows.length === 0) {
      if (changedFieldsToCheck !== "") {
        handleChangeDownloadEmptyEmail();
        setDownloadMultiple(false);
        setOpenDialogIDM(false);
        setSelectedListItems([]);
        setDataListAllOtherChangesSelected([]);
        return;
      } else {
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please Select Any Field To Proceed?");
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
        return;
      }
    }

    let payload = [];
    if (downloadMultiple === true && selectedRows.length > 0) {
      if (changedFieldsToCheck === "") {
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please Select Any Field To Proceed?");
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
        return;
      }
      payload = selectedMassChangeRowData.map((row) => ({
        coArea: row.controllingArea,
        profitCenter: row.profitCenter,
        changedFieldsToCheck: changedFieldsToCheck,
      }));
    } else {
      if (changedFieldsToCheck === "") {
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please Select Any Field To Proceed?");
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
        return;
      }
      payload = [
        {
          coArea: tableRows?.controllingArea,
          profitCenter: tableRows?.profitCenter,
          changedFieldsToCheck: changedFieldsToCheck,
        },
      ];
    }

    const hSuccess = (response) => {
      const hasError = response.some((item) => item.statusCode !== 200);
      if (!hasError) {
        if (downloadMultiple === true) {
          // mass change case
          if (selectedRows.length > 0) {
            handleChangeDownloadEmail();
            setDownloadMultiple(false);
            setOpenDialog(false);
            setOpenDialogIDM(false);
            setSelectedListItems([]);
            setDataListAllOtherChangesSelected([]);
          } else if (selectedRows.length === 0) {
            handleChangeDownloadEmptyEmail();
            setDownloadMultiple(false);
            setOpenDialog(false);
            setOpenDialogIDM(false);
            setSelectedListItems([]);
            setDataListAllOtherChangesSelected([]);
          }
        } else {
          //single change case
          dispatch(setSelectedHeader(dataListAllOtherChangesSelected));
          dispatch(setSelectedHeaderTab(selectedTab));
          if (alignment === "ALL OTHER CHANGES") {
            let filterDataWithSelectedData = [];
            const filteredData = fieldSelectionFromIdm.filter(
              (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
            );
            dataListAllOtherChangesSelected.forEach((input) => {
              filteredData?.map((item, index) => {
                if (item.MDG_SELECT_OPTION == input.name) {
                  let COHash = {};
                  COHash["id"] = index;
                  COHash["name"] = item?.MDG_FIELD_NAME;
                  ///fieldSelectionCompanyCode.push(COHash)
                  filterDataWithSelectedData.push(COHash);
                }
              });
            });
            let newFilterData = Object?.values(
              filterDataWithSelectedData?.reduce((acc, item) => {
                if (!acc[item?.name]) acc[item?.name] = item;
                return acc;
              }, {})
            );
            dispatch(setFields(newFilterData));

            //dispatch(setFields(selectedListItems))
          } else if (alignment === "BLOCK") {
            const filteredData = fieldSelectionFromIdm.filter(
              (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
            );
            let filterDataWithSelectedData = [];
            dataListBlockNamesSelected.forEach((input) => {
              filteredData?.map((item, index) => {
                if (item.MDG_SELECT_OPTION === input.name) {
                  let blockHash = {};
                  blockHash["id"] = index;
                  blockHash["name"] = item?.MDG_FIELD_NAME;
                  ///fieldSelectionCompanyCode.push(COHash)
                  filterDataWithSelectedData.push(blockHash);
                }
              });
            });
            let newFilterData = Object?.values(
              filterDataWithSelectedData?.reduce((acc, item) => {
                if (!acc[item?.name]) acc[item?.name] = item;
                return acc;
              }, {})
            );
            dispatch(setFields(newFilterData));
            // dispatch(setFields(filterDataWithSelectedData))
          } else if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
            const filteredData = fieldSelectionFromIdm.filter(
              (item) =>
                item.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
            );
            let filterDataWithSelectedData = [];
            dataListTemporaryBlockNamesSelected.forEach((input) => {
              filteredData?.map((item, index) => {
                if (item.MDG_SELECT_OPTION === input.name) {
                  let blockHash = {};
                  blockHash["id"] = index;
                  blockHash["name"] = item?.MDG_FIELD_NAME;
                  ///fieldSelectionCompanyCode.push(COHash)
                  filterDataWithSelectedData.push(blockHash);
                }
              });
            });
            let newFilterData = Object?.values(
              filterDataWithSelectedData?.reduce((acc, item) => {
                if (!acc[item?.name]) acc[item?.name] = item;
                return acc;
              }, {})
            );
            dispatch(setFields(newFilterData));

            // dispatch(setFields(filterDataWithSelectedData))
          }
          //dispatch(setFields(selectedListItems));
          // dispatch(clearProfitCenter())
          // dispatch(clearProfitCenterPayloadGI())
          if (selectedTab === "TEMPORARY BLOCK/UNBLOCK") {
            navigate(
              "/masterDataCockpit/profitCenter/changeETPCTemporaryBlock",
              {
                state: tableRows,
              }
            );
          } else {
            navigate("/masterDataCockpit/profitCenter/changeETPC", {
              state: tableRows,
            });
          }
        }
      } else {
        const filteredData = response.filter((item) => item.statusCode === 400);

        let duplicateFieldsArr = [];

        filteredData?.map((item, index) => {
          let dataHash = {};
          const profitCenter = item?.message
            ?.split("Profit Center: ")[1]
            ?.split(",")[0];
          dataHash["id"] = index;
          dataHash["profitCenter"] = profitCenter;
          dataHash["reqId"] =
            item?.body?.EditIds?.[0] || item?.body?.MassEditIds?.[0];
          dataHash["requestedBy"] = item?.body?.RequestCreatedBy?.[0];

          duplicateFieldsArr.push(dataHash);
        });

        setDuplicateFieldsData(duplicateFieldsArr);

        setShowTableInDialog(true);
      }
    };
    const hError = (error) => {
      console.log("Failure");
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/alter/checkDuplicatePCRequest`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  // const handleApply = () => {
  //   if (downloadMultiple === true) {
  //     handleChangeDownload();
  //     setOpenDialogIDM(false);
  //     setSelectedListItems([]);
  //   } else {
  //     dispatch(setFields(selectedListItems));
  //     navigate("/masterDataCockpit/ProfitCenterNew/ChangeMultiplePC", {
  //       state: tableRows,
  //     });
  //
  //   }
  // //   else{
  // //   dispatch(setFields(selectedListItems));
  // //   navigate("/masterDataCockpit/sunoco/profitCenterSunoco/ChangePCSunocoField", {
  // //     state: tableRows
  // //   });
  // //
  // // };
  // };
  const handleSelectedColumn = () => {
    dispatch(setFields(selectedFieldsforCCSunSlice));
    setIsLoading(true);
    setIsCheckboxSelected(false);
    handleChangeDownload();
  };
  const handleSelectColumnDialogClose = () => {
    setOpenSelectColumnDialog(false);
  };

  const getCompanyCodeBasedOnControllingArea = (data) => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "CompCodeBasedOnControllingArea",
          data: data.body?.map((x, idx) => {
            return {
              id: idx,
              companyCodes: x.code,
              companyName: x.desc,
              assigned: "X",
            };
          }),
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCompCodeBasedOnControllingArea?controllingArea=${data.ControllingArea}&rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCodeForProfitCenter = (value) => {
    dispatch(
      setDropDown({
        keyName: "CompCodeBasedOnControllingArea",
        data: [
          {
            id: 0,
            companyCodes: value?.code,
            companyName: value?.desc,
            assigned: "X",
          },
        ],
      })
    );
  };

  const handleSelectionListItem = (selectedItem) => {
    const { templateName } = selectedItem;

    // Find all items with the same templateName
    const itemsToSelect = ruleData.filter(
      (item) => item.templateName === templateName
    );

    // Update selectedListItems to include all items with this templateName
    setSelectedListItems((prevSelected) => {
      const newSelected = [...prevSelected];

      // Toggle selection for all items with the same templateName
      itemsToSelect.forEach((item) => {
        const itemIndex = newSelected.findIndex(
          (selectedItem) => selectedItem.id === item.id
        );
        if (itemIndex === -1) {
          newSelected.push(item);
        } else {
          newSelected.splice(itemIndex, 1); // Deselect if already selected
        }
      });

      return newSelected;
    });
  };

  // Group items by templateName to display unique templateName values
  const groupedItems = {};
  ruleData.forEach((item) => {
    if (!groupedItems[item.templateName]) {
      groupedItems[item.templateName] = item;
    }
  });

  const dropDownData = useSelector((state) => state?.AllDropDown?.dropDown);
  // const dropDownData = {
  //   PCSearchDataET: [
  //     {
  //       code: "1010",
  //     },
  //     {
  //       code: "2010",
  //     },
  //     {
  //       code: "2015",
  //     },
  //     {
  //       code: "2020",
  //     },
  //     {
  //       code: "2021",
  //     },
  //     {
  //       code: "2030",
  //     },
  //     {
  //       code: "2040",
  //     },
  //   ],
  //   LongTextSearchET: [
  //     {
  //       code: "1010",
  //     },
  //     {
  //       code: "2010",
  //     },
  //     {
  //       code: "2015",
  //     },
  //     {
  //       code: "2020",
  //     },
  //     {
  //       code: "2021",
  //     },
  //     {
  //       code: "2030",
  //     },
  //     {
  //       code: "2040",
  //     },
  //   ],
  //   PersonResponsibleSearchET: [
  //     {
  //       code: "1010",
  //     },
  //     {
  //       code: "2010",
  //     },
  //     {
  //       code: "2015",
  //     },
  //     {
  //       code: "2020",
  //     },
  //     {
  //       code: "2021",
  //     },
  //     {
  //       code: "2030",
  //     },
  //     {
  //       code: "2040",
  //     },
  //   ],
  //   CitySearchET: [
  //     {
  //       code: "1010",
  //     },
  //     {
  //       code: "2010",
  //     },
  //     {
  //       code: "2015",
  //     },
  //     {
  //       code: "2020",
  //     },
  //     {
  //       code: "2021",
  //     },
  //     {
  //       code: "2030",
  //     },
  //     {
  //       code: "2040",
  //     },
  //   ],
  //   StreetSearchET: [
  //     {
  //       code: "1010",
  //     },
  //     {
  //       code: "2010",
  //     },
  //     {
  //       code: "2015",
  //     },
  //     {
  //       code: "2020",
  //     },
  //     {
  //       code: "2021",
  //     },
  //     {
  //       code: "2030",
  //     },
  //     {
  //       code: "2040",
  //     },
  //   ],
  //   CreatedBySearchET: [
  //     {
  //       code: "1010",
  //     },
  //     {
  //       code: "2010",
  //     },
  //     {
  //       code: "2015",
  //     },
  //     {
  //       code: "2020",
  //     },
  //     {
  //       code: "2021",
  //     },
  //     {
  //       code: "2030",
  //     },
  //     {
  //       code: "2040",
  //     },
  //   ],
  // };
  const dropdownData = useSelector((state) => state.AllDropDown.dropDown);

  const sendNewProfitCenterData = {
    profitCenter: { newProfitCenter },
    companyCode: { newCompanyCode },
    companyCodeCopy: { newCompanyCodeCopy },
    profitCenterName: { newProfitCenterName },
    controllingArea: initialScreenData?.ControllingArea,
    controllingAreaDataCopy: { newControllingAreaCopyFrom },
    // validFromDate: { newValidFromDate },
    // validToDate: { newValidToDate },
  };

  const handleDialogClickOpen = () => {
    setDialogOpen(true);
  };
  const handleDialogClickOpenWithCopy = () => {
    setDialogOpenCreate(true);
  };
  const handleChange = (event, newAlignment) => {
    setAlignment(newAlignment);
    if (newAlignment === "ALL OTHER CHANGES") {
      setSelectedTab("ALL OTHER CHANGES");
    } else if (newAlignment === "BLOCK") {
      setSelectedTab("BLOCK");
    } else if (newAlignment === "TEMPORARY BLOCK/UNBLOCK") {
      setSelectedTab("TEMPORARY BLOCK/UNBLOCK");
    }
  };
  const handleSelectionAllOtherChanges = (item) => {
    //chiranjit

    const selectedIndex = dataListAllOtherChangesSelected.findIndex(
      (selectedItem) => selectedItem.id === item.id
    );

    let newSelected = [];
    if (selectedIndex === -1) {
      newSelected = [...dataListAllOtherChangesSelected, item];
    } else if (selectedIndex === 0) {
      newSelected = dataListAllOtherChangesSelected.slice(1);
    } else if (selectedIndex === dataListAllOtherChangesSelected.length - 1) {
      newSelected = dataListAllOtherChangesSelected.slice(0, -1);
    } else if (selectedIndex > 0) {
      newSelected = [
        ...dataListAllOtherChangesSelected.slice(0, selectedIndex),
        ...dataListAllOtherChangesSelected.slice(selectedIndex + 1),
      ];
    }

    setDataListAllOtherChangesSelected(newSelected);
  };
  const handleSelectionBlock = (item) => {
    //chiranjit

    const selectedIndex = dataListBlockNamesSelected.findIndex(
      (selectedItem) => selectedItem.id === item.id
    );

    let newSelected = [];
    if (selectedIndex === -1) {
      newSelected = [...dataListBlockNamesSelected, item];
    } else if (selectedIndex === 0) {
      newSelected = dataListBlockNamesSelected.slice(1);
    } else if (selectedIndex === dataListBlockNamesSelected.length - 1) {
      newSelected = dataListBlockNamesSelected.slice(0, -1);
    } else if (selectedIndex > 0) {
      newSelected = [
        ...dataListBlockNamesSelected.slice(0, selectedIndex),
        ...dataListBlockNamesSelected.slice(selectedIndex + 1),
      ];
    }

    setDataListBlockNamesSelected(newSelected);
  };
  const handleSelectionTemporaryBlockChange = (item) => {
    const selectedIndex = dataListTemporaryBlockNamesSelected.findIndex(
      (selectedItem) => selectedItem.id === item.id
    );

    let newSelected = [];
    if (selectedIndex === -1) {
      newSelected = [...dataListTemporaryBlockNamesSelected, item];
    } else if (selectedIndex === 0) {
      newSelected = dataListTemporaryBlockNamesSelected.slice(1);
    } else if (
      selectedIndex ===
      dataListTemporaryBlockNamesSelected.length - 1
    ) {
      newSelected = dataListTemporaryBlockNamesSelected.slice(0, -1);
    } else if (selectedIndex > 0) {
      newSelected = [
        ...dataListTemporaryBlockNamesSelected.slice(0, selectedIndex),
        ...dataListTemporaryBlockNamesSelected.slice(selectedIndex + 1),
      ];
    }

    setDataListTemporaryBlockNamesSelected(newSelected);
  };

  const handleDialogCloseCreate = () => {
    setDialogOpenCreate(false);
    setIsValidationErrorwithCopy(false);
    setNewProfitCenterValidWithCopy(false);
    setNewControllingArea("");
    setNewComapnyCode("");
    setNewProfitCenterName("");
    setNewProfitCenter("");
    setCheckValidationProfitCenter(false);
    //setNewCostCenterCopyFrom('')
    //setnewpro
  };
  const handleLookupChange = (newValue, option) => {
    if (true) {
      var tempOption = option
        .replaceAll("(", "")
        .replaceAll(")", "")
        .replaceAll("/", "")
        .replaceAll("-", "")
        .replaceAll(".", "")
        .split(" ")
        .join("");

      let tempFilterData = {
        ...pcSearchForm,
        [tempOption]: newValue,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  // const getCompanyCodeBasedOnControllingArea = (data) => {

  //   const hSuccess = (data) => {
  //     dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_ProfitCenter_Mass}/data/getCompCodeBasedOnControllingArea?controllingArea=${data?.ControllingArea}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  const getCompanyCodeForCreate = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${value?.code}&rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCodeForwoCopy = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCompCodeBasedOnControllingArea?controllingArea=${value?.ControllingArea}&rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCodeForSearch = (value = "ETCA") => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "CompanyCodeForSearch", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCompCodeBasedOnControllingArea?controllingArea=${value}&rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCodeBasedOnControllingAreaCopy = (value) => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "CompCode",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCompCodeBasedOnControllingArea?controllingArea=${value.code}&rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenterBasedOnControllingAreaCopy = (value) => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "ProfitCenter",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCenterAsPerControllingArea?controllingArea=${value.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  // const handleDialogProceedWithCopy = () => {
  //   if (newProfitCenter === "" || newControllingArea === null) {
  //     setProfitCenterValid(true);
  //     setControllingAreaValid(true);
  //   } else {
  //     setProfitCenterValid(false);
  //     setControllingAreaValid(false);
  //   }
  //   getProfitCenterGroup(newControllingArea);
  //   // getCompanyCodeBasedOnControllingArea();
  //   let selectedControllingArea = newControllingArea.code;
  //   let selectedCompanyCode = newCompanyCode.code;
  //   let selectedProfitCenterName = newProfitCenterName;
  //   let result = selectedControllingArea.concat("$$","P",selectedCompanyCode, selectedProfitCenterName);

  //   const hSuccess = (data) => {

  //     if (data.body.length > 0) {
  //       console.log('rakesj')
  //       setCheckValidationProfitCenter(true);
  //     } else {
  //       // navigate(`/masterDataCockpit/et/profitCenter/displayCopyProfitCenter/${selectedCompanyCode}${selectedProfitCenterName}`, {
  //       navigate(`/masterDataCockpit/et/profitCenter/displayCopyProfitCenter/TUK1_7899`, {
  //         state: sendNewProfitCenterData,
  //       });
  //     }
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };

  //   doAjax(
  //     `/${destination_ProfitCenter_Mass}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${result}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  // Preset Function Start
  const PresetMethod = () => {
    let tempFilterData = {
      controllingArea: pcSearchForm?.controllingArea?.code ?? "",
      profitCenter: pcSearchForm?.profitCenter ?? "",
      profitCenterName: pcSearchForm?.profitCenterName ?? "",
      createdBy: pcSearchForm?.createdBy ?? "",
      segment: pcSearchForm?.Segment?.code ?? "",
      profitCenterGroup: pcSearchForm?.profitCenterGroup?.code ?? "",
    };
    dispatch(
      commonFilterUpdate({
        module: "ProfitCenter",
        filterData: tempFilterData,
      })
    );
  };
  const PresetObj = [
    { name: "ProfitCenterName", value: "TZUS" },
    // { name: "company", value: planningSearchForm?.companyCode },
    // { name: "supplier", value: planningSearchForm?.vendorNo },
    // { name: "purchasingGroup", value: planningSearchForm?.purchGrp },
    // { name: "status", value: planningSearchForm?.planningSheetStatus },
    // {
    //   name: "toDate",
    //   value:
    //     moment(planningSearchForm?.taskCreationDate[1]).format("YYYY-MM-DD") +
    //     "T00:00:00",
    // },
    // {
    //   name: "fromDate",
    //   value:
    //     moment(planningSearchForm?.taskCreationDate[0]).format("YYYY-MM-DD") +
    //     "T00:00:00",
    // },
  ];
  // Preset Function End
  const handleDialogProceedWithCopy = () => {
    duplicateCheckWithCopy();
  };
  const duplicateCheckWithCopy = () => {
    if (
      newControllingArea?.code === undefined ||
      newControllingArea?.code === "" ||
      newCompanyCodeCopy?.code === undefined ||
      newCompanyCodeCopy?.code === "" ||
      newProfitCenterName === undefined ||
      newProfitCenterName === "" ||
      newControllingAreaCopyFrom?.code === undefined ||
      newControllingAreaCopyFrom?.code === "" ||
      newProfitCenter?.code === undefined ||
      newProfitCenter?.code === ""
    ) {
      setNewProfitCenterValidWithCopy(false);
      setIsValidationErrorwithCopy(true);
      return;
    } else {
      if (newProfitCenterName.length !== 5) {
        setNewProfitCenterValidWithCopy(true);
        setIsValidationErrorwithCopy(false);
        return;
        //duplicateCheck()
      } else {
        setNewProfitCenterValidWithCopy(false);
      }
      setIsValidationErrorwithCopy(false);
    }

    // let selectedControllingArea =
    //   sendNewProfitCenterData?.controllingArea?.newControllingArea?.code;
    let selectedControllingArea = sendNewProfitCenterData?.controllingArea;
    // let selectedCompanyCode =
    //   sendNewProfitCenterData?.companyCodeCopy?.newCompanyCodeCopy?.code;
    let selectedCompanyCode =
      sendNewProfitCenterData?.companyCodeCopy?.newCompanyCodeCopy?.code;
    let selectedProfitCenterName =
      sendNewProfitCenterData?.profitCenterName?.newProfitCenterName;
    let result = selectedControllingArea?.concat(
      "$$",
      "P",
      selectedCompanyCode,
      selectedProfitCenterName
    );

    // setIsLoading(true);
    setLoaderMessage(
      "We are validating the Profit Center Number to ensure it does not exist. Thank you for your patience"
    );
    setBlurLoading(true);
    const hSuccess = (data) => {
      setBlurLoading(false);
      setLoaderMessage("");

      if (data.body.length > 0) {
        setCheckValidationProfitCenter(true);
      } else {
        navigate(
          `/masterDataCockpit/profitCenter/displayCopyProfitCenter/${sendNewProfitCenterData?.profitCenterName?.newProfitCenterName}`,
          {
            state: sendNewProfitCenterData,
          }
        );
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${result}`,
      "get",
      hSuccess,
      hError
    );
  };
  // const handleDialogProceed = () => {
  //   if (radioValue === "yes") {
  //     duplicateCheck();
  //   } else {
  //     navigate("/masterDataCockpit/profitCenterNew/newSingleProfitCenter", { state: sendNewProfitCenterData });
  //   }
  // };

  const handleDialogProceed = () => {
    /*if (newProfitCenter === "" || newControllingArea === null) {
      setProfitCenterValid(true);
      setControllingAreaValid(true);
    } else {
      setProfitCenterValid(false);
      setControllingAreaValid(false);
    }*/
    duplicateCheck();
    // if (
    //   newControllingArea?.code === undefined ||
    //   newControllingArea?.code === "" ||
    //   newCompanyCode?.code === undefined ||
    //   newCompanyCode?.code === "" ||
    //   newProfitCenterName === undefined ||
    //   newProfitCenterName === ""
    // ) {
    //   setNewProfitCenterValid(false);
    //   setIsValidationError(true);
    //   return;
    // } else {
    //   if (newProfitCenterName.length !== 5) {
    //     setNewProfitCenterValid(true);
    //     setIsValidationError(false);
    //     return;
    //   } else {
    //     setNewProfitCenterValid(false);
    //   }
    //   setIsValidationError(false);
    // }

    // // getProfitCenterGroup(newControllingArea);
    // getCompanyCodeBasedOnControllingArea(newControllingArea);
    // let selectedControllingArea = newControllingArea.code;
    // let selectedCompanyCode = newCompanyCode.code;
    // let selectedProfitCenterName = newProfitCenterName;
    // let result = selectedControllingArea.concat(
    //   "$$",
    //   "P",
    //   selectedCompanyCode,
    //   selectedProfitCenterName
    // );
    // setIsLoading(true);
    // const hSuccess = (data) => {
    //   setIsLoading(false);
    //   if (data.body.length > 0) {
    //     setCheckValidationProfitCenter(true);
    //   } else {
    //     navigate("/masterDataCockpit/profitCenterNew/newSingleProfitCenter", {
    //       state: sendNewProfitCenterData,
    //     });
    //   }
    // };
    // const hError = (error) => {
    //   console.log(error);
    // };

    // doAjax(
    //   `/${destination_ProfitCenter_Mass}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${result}`,
    //   "get",
    //   hSuccess,
    //   hError
    // );
  };
  const duplicateCheck = () => {
    let selectedCompanyCode =
      sendNewProfitCenterData?.companyCode?.newCompanyCode?.code;
    let selectedProfitCenterName =
      sendNewProfitCenterData?.profitCenterName?.newProfitCenterName;
    if (radioValue === "no") {
      if (newCompanyCode?.code === undefined || newCompanyCode?.code === "") {
        setNewProfitCenterValid(false);
        setIsValidationError(true);
        setIsAnyFieldEmpty(true);

        return;
      } else {
        setIsValidationError(false);
      }
      navigate("/masterDataCockpit/profitCenter/newSingleProfitCenter", {
        state: sendNewProfitCenterData,
      });
    } else {
      if (
        newCompanyCode?.code === undefined ||
        newCompanyCode?.code === "" ||
        newProfitCenterName === undefined ||
        newProfitCenterName === ""
      ) {
        setNewProfitCenterValid(false);
        setIsValidationError(true);
        setIsAnyFieldEmpty(true);

        return;
      } else {
        if (newProfitCenterName.length !== 5) {
          setNewProfitCenterValid(true);
          setIsValidationError(false);
          return;
        } else {
          setNewProfitCenterValid(false);
        }
        setIsValidationError(false);
      }

      let result = initialScreenData?.ControllingArea.concat(
        "$$",
        "P",
        selectedCompanyCode,
        selectedProfitCenterName
      );
      setLoaderMessage(
        "We are validating the Profit Center Number to ensure it does not exist. Thank you for your patience"
      );
      setBlurLoading(true);
      const hSuccess = (data) => {
        setBlurLoading(false);
        setLoaderMessage("");

        if (data.body.length > 0) {
          setCheckValidationProfitCenter(true);
        } else {
          navigate("/masterDataCockpit/profitCenter/newSingleProfitCenter", {
            state: sendNewProfitCenterData,
          });
        }
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_ProfitCenter_Mass}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${result}`,
        "get",
        hSuccess,
        hError
      );
    }
  };
  const handleCloseButtonCreate = (event) => {
    if (
      anchorRefCreate.current &&
      anchorRefCreate.current.contains(event.target)
    ) {
      return;
    }
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };
  const handleDialogClose = () => {
    setProfitCenterValid(false);
    setControllingAreaValid(false);
    setDialogOpen(false);
    setIsValidationError(false);
    setNewProfitCenterValid(false);
    setNewControllingArea("");
    setNewComapnyCode("");
    setNewProfitCenterName("");
    setCheckValidationProfitCenter(false);
  };
  const handleProfitCenter = (e) => {
    if (e.target.value !== null) {
      var tempProfitCenter = e.target.value;

      let tempFilterData = {
        ...pcSearchForm,
        profitCenter: tempProfitCenter,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleProfitCenterName = (e, value) => {
    if (true) {
      var tempProfitCenterName = value;

      let tempFilterData = {
        ...pcSearchForm,
        profitCenterName: tempProfitCenterName,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleStreet = (e, value) => {
    if (true) {
      var tempStreet = value;

      let tempFilterData = {
        ...pcSearchForm,
        street: tempStreet,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleCreatedOn = (e) => {
    if (e.target.value !== null) {
      var tempCreatedOn = e.target.value;

      let tempFilterData = {
        ...pcSearchForm,
        street: tempCreatedOn,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleLongText = (e, value) => {
    if (true) {
      var tempLongText = value;

      let tempFilterData = {
        ...pcSearchForm,
        longText: tempLongText,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handlePersonResponsible = (e, value) => {
    if (true) {
      var tempPersonResponsible = value;

      let tempFilterData = {
        ...pcSearchForm,
        personResponsible: tempPersonResponsible,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };

  const handleControllingArea = (e, value) => {
    if (true) {
      var tempControllingArea = value;

      let tempFilterData = {
        ...pcSearchForm,
        controllingArea: tempControllingArea,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
      getHierarchyArea(tempFilterData);
      getCompanyCodeForSearch(pcSearchForm?.controllingArea?.code);
      // getProfitCenter(tempFilterData);
      // getCompanyCode(tempFilterData);
      // getCostCenterCategory(tempFilterData);
    }
  };

  const isLongTextSelected = (option) => {
    return selectedLongText.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isPersonResponsibleSelected = (option) => {
    return selectedPersonResponsible.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isCreatedBySelected = (option) => {
    return selectedCreatedBy.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isStreetSelected = (option) => {
    return selectedStreet.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isProfitCenterSelected = (option) => {
    return selectedProfitCenter.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isProfitCenterNameSelected = (option) => {
    return selectedProfitCenterName.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isCitySelected = (option) => {
    return selectedCity.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isRegionSelected = (option) => {
    return selectedRegion.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isCompanyCodeSelected = (option) => {
    return selectedComanyCode.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isCountrySelected = (option) => {
    return selectedCountry.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isOptionSelected = (option, dropdownOption) => {
    return selectedValues[option]?.some(
      (selectedOption) => selectedOption?.code === dropdownOption?.code
    );
  };

  const handleInputCompanyChange = (event, newInputValue) => {
    setSearchInput(newInputValue);
  };

  const handleSelectAllOptions = (option) => {
    if (selectedValues[option]?.length === dynamicOptions[option]?.length) {
      setSelectedValues((prev) => ({
        ...prev,
        [option]: [],
      }));
      setSelectedPresetValues((prev) => ({
        ...prev,
        [option]: [],
      }));
    } else {
      setSelectedValues((prev) => ({
        ...prev,
        [option]: dynamicOptions[option] ?? [],
      }));
    }
  };

  const handleSelectAllCompanyCodes = () => {
    //alert("coming")

    if (selectedComanyCode.length === dropDownData.CompanyCode.length) {
      //alert(1)
      setselectedComanyCode([]);
      setselectedPresetComanyCode([]);
    } else {
      //alert(2)
      setselectedComanyCode(dropDownData?.CompanyCode);
    }
  };
  const handleSelectAllLongTexts = () => {
    //alert("coming")

    if (selectedLongText.length === dropDownData?.LongTextSearchET.length) {
      //alert(1)
      setselectedLongText([]);
      setselectedPresetLongText([]);
    } else {
      //alert(2)
      setselectedLongText(dropDownData?.LongTextSearchET);
    }
  };

  const handleSelectAllPersonResponsible = () => {
    if (
      selectedPersonResponsible.length ===
      dropDownData?.PersonResponsibleSearchET?.length
    ) {
      setselectedPersonResponsible([]);
      setselectedPresetPersonResponsible([]);
    } else {
      setselectedPersonResponsible(dropDownData?.PersonResponsibleSearchET);
    }
  };

  const handleSelectAllCountry = () => {
    if (selectedCountry.length === dropDownData?.country?.length) {
      setselectedCountry([]);
      setselectedPresetCountry([]);
    } else {
      setselectedCountry(dropDownData?.country);
    }
  };

  const handleSelectAllCreatedBy = () => {
    if (selectedCreatedBy.length === dropDownData?.CreatedBySearchET?.length) {
      setselectedCreatedBy([]);
      setselectedPresetCreatedBy([]);
    } else {
      setselectedCreatedBy(dropDownData?.CreatedBySearchET);
    }
  };

  const handleSelectAllStreet = () => {
    if (selectedStreet.length === dropDownData?.StreetSearchET?.length) {
      setselectedStreet([]);
      setselectedPresetStreet([]);
    } else {
      setselectedStreet(dropDownData?.StreetSearchET);
    }
  };

  const handleSelectAllProfitCenter = () => {
    if (selectedProfitCenter.length === dropDownData?.PCSearchDataET?.length) {
      setselectedProfitCenter([]);
      setselectedPresetProfitCenter([]);
    } else {
      setselectedProfitCenter(dropDownData?.PCSearchDataET);
    }
  };

  const handleSelectAllProfitCenterName = () => {
    if (
      selectedProfitCenterName.length === dropDownData?.PCNameSearchET?.length
    ) {
      setselectedProfitCenterName([]);
      setselectedPresetProfitCenterName([]);
    } else {
      setselectedProfitCenterName(dropDownData?.PCNameSearchET);
    }
  };

  const handleSelectAllCity = () => {
    if (selectedCity.length === dropDownData?.CitySearchET.length) {
      setselectedCity([]);
      setselectedPresetCity([]);
    } else {
      setselectedCity(dropDownData?.CitySearchET);
    }
  };

  const handleSelectAllRegion = () => {
    if (selectedRegion.length === dropDownData?.region.length) {
      setselectedRegion([]);
      setselectedPresetRegion([]);
    } else {
      setselectedRegion(dropDownData?.region);
    }
  };

  const handleCompanyCode = (event, value) => {
    if (reason === "clear") {
      setselectedComanyCode([]);
      return;
    }

    if (value.length > 0 && value[value.length - 1]?.code === "Select All") {
      handleSelectAllRequestStatusName();
    } else {
      setselectedComanyCode((prevCompNames) => {
        const isValuePresent = prevCompNames.some(
          (item) => item.code === value[value.length - 1]?.code
        );

        if (isValuePresent) {
          return prevCompNames.filter(
            (item) => item.code !== value[value.length - 1]?.code
          );
        } else {
          return [...prevCompNames, value[value.length - 1]];
        }
      });
    }

    const tempCompanyCode = value.map((item) => item.desc).join("$^$");
    const tempFilterData = {
      ...pcSearchForm,
      companyCode: tempCompanyCode,
    };

    dispatch(
      commonFilterUpdate({
        module: "ProfitCenter",
        filterData: tempFilterData,
      })
    );
  };
  const handleInputChange = (e) => {
    const value = e.target.value;
    setInputValue(value);

    let tempFilterData = {
      ...pcSearchForm,
      profitCenterString: value,
    };

    dispatch(
      commonFilterUpdate({
        module: "ProfitCenter",
        filterData: tempFilterData,
      })
    );
  };
  const handleControllingAreaChange = (e, value) => {
    if (true) {
      var tempControllingArea = value;

      let tempFilterData = {
        ...pcSearchForm,
        controllingArea: tempControllingArea,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
      getProfitCenterGroupForSearch(tempFilterData);
    }
  };
  const handleCity = (e, value) => {
    if (true) {
      var tempCity = value;

      let tempFilterData = {
        ...pcSearchForm,
        city: tempCity,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
      // getProfitCenterGroupForSearch(tempFilterData);
    }
  };
  const handleCountry = (e, value) => {
    if (true) {
      var tempCountry = value;

      let tempFilterData = {
        ...pcSearchForm,
        country: tempCountry,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
      getRegion(tempCountry);
    }
  };
  const handleRegion = (e, value) => {
    if (true) {
      var tempRegion = value;

      let tempFilterData = {
        ...pcSearchForm,
        region: tempRegion,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
      // getProfitCenterGroupForSearch(tempFilterData);
    }
  };

  const handleLongTextInputChange = (e) => {
    const inputValue = e.target.value;
    setPcLongDesc(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    // Check if input length is at least 4 characters
    if (inputValue.length >= 4) {
      // Set a new timer to execute API call after 500ms
      const newTimerId = setTimeout(() => {
        getLongText(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
    // if (timerId) {
    //   clearTimeout(timerId);
    // }

    // // Set a new timer to execute handleDQM after 1 second
    // const newTimerId = setTimeout(() => {
    //   getLongText(e.target.value);
    // }, 500);

    // // Store the new timer ID
    // setTimerId(newTimerId);
  };
  const handlePersonResponsibleInputChange = (e) => {
    const inputValue = e.target.value;
    setPcPerson(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    // Set a new timer to execute handleDQM after 1 second
    if (inputValue.length >= 4) {
      // Set a new timer to execute API call after 500ms
      const newTimerId = setTimeout(() => {
        getPersonResponsible(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };
  const handleCityInputChange = (e) => {
    const inputValue = e.target.value;
    setPcCity(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    // Set a new timer to execute handleDQM after 1 second
    if (inputValue.length >= 4) {
      // Set a new timer to execute API call after 500ms
      const newTimerId = setTimeout(() => {
        getCity(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };
  const handlePCNameInputChange = (e) => {
    const inputValue = e.target.value;
    setPcShortDesc(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    // Set a new timer to execute handleDQM after 1 second
    if (inputValue.length >= 4) {
      // Set a new timer to execute API call after 500ms
      const newTimerId = setTimeout(() => {
        getPCName(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };
  const handleStreetInputChange = (e) => {
    const inputValue = e.target.value;
    setPcStreet(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    // Set a new timer to execute handleDQM after 1 second
    if (inputValue.length >= 4) {
      // Set a new timer to execute API call after 500ms
      const newTimerId = setTimeout(() => {
        getStreet(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };
  const handlePCInputChange = (e) => {
    const inputValue = e.target.value;
    setPcInputValue(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      // Set a new timer to execute handleDQM after 1 second
      const newTimerId = setTimeout(() => {
        getProfitCenterSearch(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };

  const handleCreatedByInputChange = (e) => {
    const inputValue = e.target.value;
    setPcCreatedBy(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    // Set a new timer to execute handleDQM after 1 second
    if (inputValue.length >= 4) {
      // Set a new timer to execute handleDQM after 1 second
      const newTimerId = setTimeout(() => {
        getCreatedBy(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };
  const handleCountryInputChange = (e) => {
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    // Set a new timer to execute handleDQM after 1 second
    const newTimerId = setTimeout(() => {
      getCountryorRegion(e.target.value);
    }, 500);

    // Store the new timer ID
    setTimerId(newTimerId);
  };
  const handleRegionInputChange = (e) => {
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    // Set a new timer to execute handleDQM after 1 second
    const newTimerId = setTimeout(() => {
      getRegion(e.target.value);
    }, 500);

    // Store the new timer ID
    setTimerId(newTimerId);
  };
  const handleCreateSingleWithCopy = () => {
    handleDialogClickOpenWithCopy();
  };
  const handleCreateSingleWithoutCopy = () => {
    handleDialogClickOpen();
  };
  // useEffect(() => {
  //   getProfitCenterGroupForSearch(pcSearchForm?.controllingArea?.code);
  // }, [pcSearchForm?.controllingArea?.code]);

  const handleGroup = (e, value) => {
    if (true) {
      var tempGroup = value;

      let tempFilterData = {
        ...pcSearchForm,
        profitCenterGroup: tempGroup,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleBlockingStatus = (e) => {
    if (e.target.value !== null) {
      var tempBlockingStatus = e.target.value;

      let tempFilterData = {
        ...pcSearchForm,
        blockingStatus: tempBlockingStatus,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleSegment = (e, value) => {
    if (true) {
      var tempSegment = value;

      let tempFilterData = {
        ...pcSearchForm,
        Segment: tempSegment,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleCreatedBy = (e, value) => {
    if (true) {
      var tempCreatedBy = value;

      let tempFilterData = {
        ...pcSearchForm,
        createdBy: tempCreatedBy,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleChangedBy = (e, value) => {
    if (true) {
      var tempChangedBy = value;

      let tempFilterData = {
        ...pcSearchForm,
        changedBy: tempChangedBy,
      };
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    }
  };
  let dynamicDataApis = {
    Segment: `/${destination_ProfitCenter_Mass}/data/getSearchParamsSegment`,
    // "Country/Region": `/${destination_ProfitCenter_Mass}/data/getCountryOrReg`,
    // "Business Area ": `/${destination_MaterialMgmt}/data/getDivision`,
    // "Functional Area": `/${destination_MaterialMgmt}/data/getLaboratoryDesignOffice`,
  };

  const handleSelection = (event) => {
    const selectedItems = event.target.value;
    setSelectedOptions(selectedItems);
    setDisplayedFields([]);

    selectedItems.forEach(async (selectedItem) => {
      const apiEndpoint = dynamicDataApis[selectedItem];
      fetchOptionsForDynamicFilter(apiEndpoint, selectedItem);
    });
  };
  const handleAddFields = () => {
    const numSelected = selectedOptions.length;
    const newFields = Array.from({ length: numSelected }, (_, index) => ({
      id: index,
      value: "",
    }));
    setDisplayedFields(newFields);
  };
  const handleFieldChange = (fieldId, value) => {
    setDisplayedFields(
      (selectedOptions) => selectedOptions?.map((option) => option)
      // prevFields?.map((field) => (field.id === fieldId ? { ...field, value } : field))
    );
  };
  const lookupData = {
    "Task ID": [{ label: "Type A" }, { label: "Type B" }],
    SalesOrg: [{ label: "Type A" }, { label: "Type B" }],
    "Warehouse Number": [{ label: "Desc X" }, { label: "Desc Y" }],
    "Storage Location": [{ label: "Desc X" }, { label: "Desc Y" }],
    Division: [{ label: "Desc X" }, { label: "Desc Y" }],
    "Old Material Number": [{ label: "Desc X" }, { label: "Desc Y" }],
    "Lab/Office": [{ label: "Desc X" }, { label: "Desc Y" }],
    "Transportation Group": [{ label: "Desc X" }, { label: "Desc Y" }],
    // "Batch management": [{ label: "Desc X" }, { label: "Desc Y" }],
    // ... other options
  };
  // const items = [
  //   { title: "Short Description" },
  //   { title: "Segment" },
  //   { title: "Street" },
  //   { title: "Country/Region" },
  //   { title: "Created On" },
  //   { title: "Created By" },
  //   // Add more options as needed
  // ];
  const titleToFieldMapping = {
    "Short Description": "ProfitCenterName",
    Segment: "Segment",
    Street: "street",
    "Country/Region": "country",
    "Created On": "createdOn",
    "Created By": "CreatedBy",
    // Add more mappings as needed
  };
  const names = ["Blocked", "Unblocked", ""];
  // let [pcSearchForm, setpcSearchForm] = useState({
  //   companyCode: "",
  //   vendorNo: "",
  //   paymentStatus: "",
  // });
  //Checked PO rows
  const getProfitCenter = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCenter`,
      "get",
      hSuccess,
      hError
    );
  };
  const getControllingArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ControllingArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getControllingArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenterGroupForSearch = (CA) => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "ProfitCtrGroupSearch", data: data.body })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCtrGroup?controllingArea=${CA?.controllingArea?.code}`,
      "get",
      hSuccess,
      hError
    );
  };

  // const getCompanyCodeBasedOnCA = (value) => {
  //   const hSuccess = (data) => {
  //     dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_ProfitCenter_Mass}/data/getCompCodeBasedOnControllingArea?controllingArea=${value.code}`,
  //     "get",
  //     hSuccess,
  //     hError
  //   );
  // };
  const getCompanyCode = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCompCode?rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenterGroup = (newControllingArea) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCtrGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCtrGroup?controllingArea=${newControllingArea.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getSegment = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Segment", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getSearchParamsSegment`,
      "get",
      hSuccess,
      hError
    );
  };
  const getLanguageKey = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Language", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenterBasicDetails = () => {
    let viewName = "Basic Data";
    const hSuccess = (data) => {
      dispatch(setProfitCenterBasicDataTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getIndicatorsProfitCenter = () => {
    let viewName = "Indicators";
    const hSuccess = (data) => {
      dispatch(setProfitCenterIndicatorsTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCompCodesProfitCenter = () => {
    let viewName = "Comp Codes";
    const hSuccess = (data) => {
      dispatch(setProfitCenterCompCodesTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAddressProfitCenter = () => {
    let viewName = "Address";
    const hSuccess = (data) => {
      dispatch(setProfitCenterAddressTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCommunicationProfitCenter = () => {
    let viewName = "Communication";
    const hSuccess = (data) => {
      dispatch(setProfitCenterCommunicationTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getHistoryProfitCenter = () => {
    let viewName = "History";
    const hSuccess = (data) => {
      dispatch(setProfitCenterHistoryTab(data.body));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getUserResponsible = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "UserResponsible", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getUserResponsible`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFormPlanningTemp = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FormPlanningTemp", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getFormPlanningTemp`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRegion = (region) => {
    setIsDropDownLoading(true);
    let payload = {
      controllingArea: pcSearchForm?.controllingArea?.code
        ? pcSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : pcSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "region", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_ProfitCenter_Mass}/data/getRegionBasedOnCountry?country=${filterData?.code}`,
      `/${destination_ProfitCenter_Mass}/data/getSearchParamsRegion`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const getCountryorRegion = () => {
    setIsDropDownLoading(true);
    let payload = {
      rolePrefix: "ETP",
      top: 200,
      skip: 0,
      controllingArea: pcSearchForm?.controllingArea?.code
        ? pcSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : pcSearchForm?.controllingArea?.code
        : "ETCA",
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "country", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getSearchParamsCountryReg`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const getJurisdiction = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "TaxJur", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getJurisdiction`,
      "get",
      hSuccess,
      hError
    );
  };
  const getLongText = (longText) => {
    setIsDropDownLoading(true);
    let payload = {
      controllingArea: pcSearchForm?.controllingArea?.code
        ? pcSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : pcSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      description: longText,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "LongTextSearchET", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getSearchParamsLongText`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getPersonResponsible = (perResp) => {
    // let payload = {
    //   "description": perResp,
    // }
    setIsDropDownLoading(true);
    let payload = {
      controllingArea: pcSearchForm?.controllingArea?.code
        ? pcSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : pcSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      personRespons: perResp,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "PersonResponsibleSearchET", data: data.body })
      );
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getSearchParamsPersonRespons`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getPCName = (pcName) => {
    setIsDropDownLoading(true);
    let payload = {
      pcName: pcName,
      controllingArea: pcSearchForm?.controllingArea?.code
        ? pcSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : pcSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "PCNameSearchET", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getSearchParamsPCName`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getCity = (city) => {
    setIsDropDownLoading(true);
    let payload = {
      city: city,
      rolePrefix: "ETP",
      controllingArea: pcSearchForm?.controllingArea?.code
        ? pcSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : pcSearchForm?.controllingArea?.code
        : "ETCA",
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CitySearchET", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getSearchParamsCity`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getStreet = (street) => {
    setIsDropDownLoading(true);
    let payload = {
      street: street,
      controllingArea: pcSearchForm?.controllingArea?.code
        ? pcSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : pcSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "StreetSearchET", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getSearchParamsStreet`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getProfitCenterSearch = (pc) => {
    // var HA = "TZUS";
    setIsDropDownLoading(true);
    let payload = {
      profitCenter: pc,
      controllingArea: pcSearchForm?.controllingArea?.code
        ? pcSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : pcSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "PCSearchDataET", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_ProfitCenter_Mass}/data/getCompCode?contrllingArea=${CA?.controllingArea?.code}`,
      `/${destination_ProfitCenter_Mass}/data/getSearchParamsProfitCenter`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getCreatedBy = (createdBy) => {
    setIsDropDownLoading(true);
    let payload = {
      createdBy: createdBy,
      rolePrefix: "ETP",
      top: 200,
      skip: 0,
      controllingArea: pcSearchForm?.controllingArea?.code
        ? pcSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : pcSearchForm?.controllingArea?.code
        : "ETCA",
    };

    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CreatedBySearchET", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getSearchParamsCreatedBy`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getSearchControllingArea = () => {
    setIsDropDownLoading(true);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "ControllingAreaForSearchET", data: data.body })
      );
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getSearchParamsConArea`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const uploadExcel = (file) => {
    // console.log(file);

    setBlurLoading(true);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    formData.append("dtName", "MDG_PC_FIELD_CONFIG");
    formData.append("version", "v1");
    formData.append("IsSunoco", "false");
    if (handleMassModePC === "Change") {
      var uploadUrl = `/${destination_ProfitCenter_Mass}/massAction/getAllProfitCenterFromExcelForMassChange`;
      const hSuccess = (data) => {
        setIsLoading(false);
        if (data.statusCode === 200) {
          setEnableDocumentUpload(false);
          // dispatch(setControllingArea(data?.body?.controllingArea));
          // dispatch(setMultipleProfitCenterData(data?.body));

          setMessageDialogTitle("Create");
          // const dialogMessage = `

          //   2. Mass Upload Process has Started in the background. As soon as the request ID is generated, you will receive a notification and mail for it containing the new request ID number.
          //   3. Then you can visit the Request Bench Tab and search for that request ID and do further actions on it.
          //   4. Note - All request IDs generated in the background would initially have the status Draft.
          //   5. Ok button
          // `.split('\n')?.map(line => line.trim()).join('\n');
          const content = (
            <Typography component="div">
              <ul>
                <li>
                  Mass Upload Process has Started in the background. As soon as
                  the request ID is generated, you will receive a notification
                  and mail for it containing the new request ID number.
                </li>
                <li>
                  Then you can visit the Request Bench Tab and search for that
                  request ID and do further actions on it.
                </li>
                <li>
                  Note - All request IDs generated in the background would
                  initially have the status Draft.
                </li>
              </ul>
            </Typography>
          );
          setBlurLoading(false);
          setLoaderMessage("");
          handleMessageDialogClickOpen();
          setMessageDialogTitle("Header - Information");
          setMessageDialogMessage(content);
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          setMessageDialogExtra(true);
          handleSnackBarOpen();
          setIsLoading(false);
        } else if (data.statusCode === 429) {
          // Handling status code 429 (Too Many Requests)
          setBlurLoading(false);
          setLoaderMessage("");
          setEnableDocumentUpload(false);
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          // Map the body.message from the response to display it in the UI
          setMessageDialogMessage(data?.message || "Please try again.");
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setIsLoading(false);
        } else {
          setEnableDocumentUpload(false);
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Upload failed. Incorrect template tab name, please recheck upload file"
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          setBlurLoading(false);
          setLoaderMessage("");
          handleMessageDialogClickOpen();

          setIsLoading(false);
        }
        handleClose();
      };
      const hError = (error) => {
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`${error?.message}`);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        setDialogOkText("OK");
        setBlurLoading(false);
        setLoaderMessage("");
        handleMessageDialogClickOpen();
      };
      doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
    } else {
      var uploadUrl = `/${destination_ProfitCenter_Mass}/massAction/getAllProfitCenterFromExcel`;
      const hSuccess = (data) => {
        if (data.statusCode === 200) {
          setEnableDocumentUpload(false);

          setMessageDialogTitle("Create");
          const content = (
            <Typography component="div">
              <ul>
                <li>
                  Mass Upload Process has Started in the background. As soon as
                  the request ID is generated, you will receive a notification
                  and mail for it containing the new request ID number.
                </li>
                <li>
                  Then you can visit the Request Bench Tab and search for that
                  request ID and do further actions on it.
                </li>
                <li>
                  Note - All request IDs generated in the background would
                  initially have the status Draft.
                </li>
              </ul>
            </Typography>
          );
          setBlurLoading(false);
          setLoaderMessage("");
          handleMessageDialogClickOpen();
          setMessageDialogTitle("Header - Information");
          setMessageDialogMessage(content);
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          setMessageDialogExtra(true);
          handleSnackBarOpen();
          setIsLoading(false);
        } else if (data.statusCode === 429) {
          // Handling status code 429 (Too Many Requests)
          setBlurLoading(false);
          setLoaderMessage("");
          setEnableDocumentUpload(false);
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          // Map the body.message from the response to display it in the UI
          setMessageDialogMessage(data?.message || "Please try again.");
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setIsLoading(false);
        } else {
          setBlurLoading(false);
          setLoaderMessage("");
          setEnableDocumentUpload(false);
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Upload failed. Incorrect template tab name, please recheck upload file"
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setIsLoading(false);
        }
        handleClose();
      };
      const hError = (error) => {
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`${error?.message}`);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        setDialogOkText("OK");
        setBlurLoading(false);
        setLoaderMessage("");
        handleMessageDialogClickOpen();
      };
      doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
    }
  };
console.log('rndatarows', dynamicColumns)
  const handleSelectionModelChange = (newSelection) => {
    // handlesna;
    setSelectedRows(newSelection);
    let filterValueColumns = columns?.map((t) => t.field);
    const selectedRowsDetails = rmDataRows.filter((row) =>
      newSelection.includes(row.id)
    );
    let requiredArrayDetails = [];
    selectedRowsDetails?.map((s) => {
      let requiredObject = {};
      filterValueColumns.forEach((y) => {
        if (s[y] !== null) {
          requiredObject[y] = s[y] || "";
        }
      });
      requiredObject["controllingArea"] = s["controllingArea"];
      requiredArrayDetails.push(requiredObject);
      setSelectedMassChangeRowData(requiredArrayDetails);
      setRequiredArrayDetailsMass(requiredArrayDetails);
    });
  };
  const getChangeTemplate = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ET_CHNG_FIELD_SELECTION_DT",
      version: "v4",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "ET PROFIT CENTER",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData =
          data?.data?.result[0]?.MDG_ET_CHNG_FIELD_SELECTION_ACTION_TYPE;

        // setButtonsIDM(responseData);

        setFieldselectionFromIdm(responseData);
        let fieldSelectionAllOtherChanges = [];
        let fieldSelectionBlock = [];
        let fieldSelectionTemporaryBlock = [];
        let fieldSelectionProfitCenterChange = [];

        responseData?.map((element, index) => {
          if (element.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES") {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = element.MDG_SELECT_OPTION;
            fieldSelectionAllOtherChanges.push(COHash);
          } else if (element.MDG_FIELD_SELECTION_LVL === "BLOCK") {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = element.MDG_SELECT_OPTION;
            fieldSelectionBlock.push(COHash);
          } else if (
            element.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
          ) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = element.MDG_SELECT_OPTION;
            fieldSelectionTemporaryBlock.push(COHash);
          }
          // } else {
          //   let COAHash = {};
          //   COAHash["id"] = index;
          //   COAHash["name"] = element.MDG_SELECT_OPTION;
          //   fieldSelectionProfitCenterChange.push(COAHash);
          // }
        });

        //const distinctNames = [];
        const uniqueAllOtherChanges = new Set();
        const distinctAllOtherChangesData =
          fieldSelectionAllOtherChanges.filter((obj) => {
            if (!uniqueAllOtherChanges.has(obj.name)) {
              uniqueAllOtherChanges.add(obj.name);
              return true;
            }
            return false;
          });
        const uniqueBlockNames = new Set();
        const distinctBlockNames = fieldSelectionBlock.filter((obj) => {
          if (!uniqueBlockNames.has(obj.name)) {
            uniqueBlockNames.add(obj.name);
            return true;
          }
          return false;
        });
        const uniqueTemporaryBlockNames = new Set();
        const distinctTemporaryBlockNames = fieldSelectionTemporaryBlock.filter(
          (obj) => {
            if (!uniqueTemporaryBlockNames.has(obj.name)) {
              uniqueTemporaryBlockNames.add(obj.name);
              return true;
            }
            return false;
          }
        );
        //const uniqueProfitCenterChange = new Set();
        // const distinctProfitCenterChange = fieldSelectionProfitCenterChange.filter((obj) => {
        //   if (!uniqueProfitCenterChange.has(obj.name)) {
        //     uniqueProfitCenterChange.add(obj.name);
        //     return true;
        //   }
        //   return false;
        // });

        //setRuleData(templateData);
        setDataListBlockNamesSelected(distinctBlockNames);
        setDataListAllOtherChanges(distinctAllOtherChangesData);
        setDataListTemporaryBlockNames(distinctTemporaryBlockNames);
        setDataListTemporaryBlockNamesSelected(distinctTemporaryBlockNames);
        setDataListBlockNames(distinctBlockNames);
        //setDataListProfitCenterChange(distinctProfitCenterChange);
        //setDataListProfitCenterChangeSelected(distinctProfitCenterChange);
        // let templateData = [];
        // responseData.map((element, index) => {
        //   console.log("element", element);

        //   var tempRow = {
        //     id: index,
        //     templateName: element?.MDG_SELECT_OPTION || "",
        //     templateData: element?.MDG_FEILD_NAME || "",
        //   };
        //   templateData.push(tempRow);
        // });
        // console.log("templateData", templateData);
        // setRuleData(templateData);
      } else {
        // setMessageDialogTitle("Create");
        // setsuccessMsg(false);
        // setMessageDialogMessage("Creation Failed");
        // setHandleExtrabutton(false);
        // setMessageDialogSeverity("danger");
        // setMessageDialogOK(false);
        // setMessageDialogExtra(true);
        // handleMessageDialogClickOpen();
        // handleCreateDialogClose();
        // setIsLoading(false);
      }
      handleClose();
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };
  // const getChangeTemplate = () => {
  //   let payload = {
  //     decisionTableId: null,
  //     decisionTableName: "MDG_ET_CHNG_FIELD_SELECTION_DT",
  //     version: "v2",
  //     rulePolicy: null,
  //     validityDate: null,
  //     conditions: [
  //       {
  //         "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "PROFIT CENTER",
  //       },
  //     ],
  //     systemFilters: null,
  //     systemOrders: null,
  //     filterString: null,
  //   };
  //   setIsLoading(true);
  //   // const formData = new FormData();
  //   const hSuccess = (data) => {
  //     setIsLoading(false);
  //     if (data.statusCode === 200) {
  //       let responseData =
  //         data?.data?.result[0]?.MDG_ET_CHNG_FIELD_SELECTION_ACTION_TYPE;
  //       let templateData = [];
  //       responseData.map((element, index) => {
  //         console.log("element", element);

  //         var tempRow = {
  //           id: index,
  //           templateName: element?.MDG_SELECT_OPTION||"",
  //           templateData: element?.MDG_FEILD_NAME||"",
  //         };
  //         templateData.push(tempRow);
  //       });
  //       setRuleData(templateData);
  //     } else {
  //       // setMessageDialogTitle("Create");
  //       // setsuccessMsg(false);
  //       // setMessageDialogMessage("Creation Failed");
  //       // setHandleExtrabutton(false);
  //       // setMessageDialogSeverity("danger");
  //       // setMessageDialogOK(false);
  //       // setMessageDialogExtra(true);
  //       // handleMessageDialogClickOpen();
  //       // handleCreateDialogClose();
  //       // setIsLoading(false);
  //     }
  //     handleClose();
  //   };

  //   const hError = (error) => {
  //     console.log(error);
  //   };

  //   if (applicationConfig.environment === "localhost") {
  //     doAjax(
  //       `/${destination_IDM}/rest/v1/invoke-rules`,
  //       "post",
  //       hSuccess,
  //       hError,
  //       payload
  //     );
  //   } else {
  //     doAjax(
  //       `/${destination_IDM}/v1/invoke-rules`,
  //       "post",
  //       hSuccess,
  //       hError,
  //       payload
  //     );
  //   }
  // };
  const setInitialScreenDataInRedux = (data) => {
    data?.map((item) => {
      dispatch(
        setSingleProfitCenterPayload({
          keyName: item?.MDG_PC_UI_FIELD_NAME.replaceAll("(", "")
            .replaceAll(")", "")
            .replaceAll("/", "")
            .replaceAll("-", "")
            .replaceAll(".", "")
            .replaceAll("%", "")
            .split(" ")
            .join(""),
          data: item?.MDG_PC_DEFAULT_VALUE,
        })
      );
    });
  };
  function groupBy(array, key) {
    return array.reduce((result, currentValue) => {
      (result[currentValue[key]] = result[currentValue[key]] || []).push(
        currentValue
      );
      return result;
    }, {});
  }

  const handleSearchDialogClickOpen = () => {
    setOpenSearchDialog(true);
  };

  const handleSearchDialogClose = () => {
    setOpenSearchDialog(false);
  };

  const getCreateTemplate = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_PC_FIELD_CONFIG",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_PC_SCENARIO": "Create",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true);
    // const formData = new FormData();
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData =
          data?.data?.result[0]?.MDG_PC_FIELD_DETAILS_ACTION_TYPE;
        let sortedData = responseData.sort(
          (a, b) => a.MDG_PC_VIEW_SEQUENCE - b.MDG_PC_VIEW_SEQUENCE
        );
        const groupedFields = groupBy(sortedData, "MDG_PC_VIEW_NAME");
        groupedFields?.["Initial Screen"]?.map((item) => {
          if (item?.MDG_PC_UI_FIELD_NAME === "Profit Center") {
            setProfitCenterLength(item?.MDG_PC_MAX_LENGTH);
          }
        });
        setInitialScreenDataInRedux(groupedFields?.["Initial Screen"]);
      }
    };
    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };
  useEffect(() => {
    getCompanyCodeForSearch();
    getControllingArea();
    getSearchControllingArea();
    getRegion();
    getCountryorRegion();
    getCreateTemplate();
    getCompanyCode();
    getSegment();
    getProfitCenterBasicDetails();
    //getIndicatorsProfitCenter();
    getCompCodesProfitCenter();
    getAddressProfitCenter();
    getHistoryProfitCenter();
    getCommunicationProfitCenter();
    dispatch(setTaskData({}));
    getChangeTemplate();
    dispatch(clearProfitCenter());
    // dispatch(clearProfitCenterSunocoPayloadGI());
    dispatch(clearArtifactId());
    // dispatch(clearProfitCenterPayloadGI());
    dispatch(clearPayload({}));
    dispatch(clearTaskData());
  }, []);

  // const fetchCompCode = (data) => {

  //   const hSuccess = (response) => {
  //     if (response.body && response.body.length > 0) {
  //       const updatedCompCodeDataPC = response.body;
  //       setCompCodeDataPC(updatedCompCodeDataPC);

  //       getFilter(skip+1000,response.body[0])
  //   };
  // }

  //   const hError = (error) => {
  //     console.error(error);

  //   };

  //   doAjax(
  //     `/${destination_ProfitCenter_Mass}/data/getUserIdFromCompCodePC?controllingArea=${data?.ControllingArea}`,
  //     'get',
  //     hSuccess,
  //     hError
  //   );
  // };

  useEffect(() => {
    if (pcSearchForm?.["Profit Center"]) {
      const costCenterArray = pcSearchForm?.["Profit Center"].split("$^$");
      const formattedCostCenterArray = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setselectedPresetProfitCenter(formattedCostCenterArray);
    }

    if (pcSearchForm?.profitCenterName) {
      const costCenterArray = pcSearchForm?.profitCenterName.split("$^$");
      const formattedCostCenterArray = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setselectedPresetProfitCenterName(formattedCostCenterArray);
    }

    if (pcSearchForm?.["Company Code"]) {
      const costCenterArray = pcSearchForm?.["Company Code"].split("$^$");
      const formattedCostCenterArray = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setselectedPresetComanyCode(formattedCostCenterArray);
    }

    if (pcSearchForm?.createdBy) {
      const costCenterArray = pcSearchForm?.createdBy.split("$^$");
      const formattedCostCenterArray = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setselectedPresetCreatedBy(formattedCostCenterArray);
    }

    if (pcSearchForm?.personResponsible) {
      const costCenterArray = pcSearchForm?.personResponsible.split("$^$");
      const formattedCostCenterArray = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setselectedPresetPersonResponsible(formattedCostCenterArray);
    }

    if (pcSearchForm?.longText) {
      const costCenterArray = pcSearchForm?.longText.split("$^$");
      const formattedCostCenterArray = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setselectedPresetLongText(formattedCostCenterArray);
    }

    if (pcSearchForm?.street) {
      const costCenterArray = pcSearchForm?.street.split("$^$");
      const formattedCostCenterArray = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setselectedPresetStreet(formattedCostCenterArray);
    }

    if (pcSearchForm?.city) {
      const costCenterArray = pcSearchForm?.city.split("$^$");
      const formattedCostCenterArray = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setselectedPresetCity(formattedCostCenterArray);
    }

    if (pcSearchForm?.country) {
      const costCenterArray = pcSearchForm?.country.split("$^$");
      const formattedCostCenterArray = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setselectedPresetCountry(formattedCostCenterArray);
    }

    if (pcSearchForm?.createdOn) {
      const presentDate = new Date(pcSearchForm?.createdOn[0]);
      const backDate = new Date(pcSearchForm?.createdOn[1]);
      setSelectedDateRange([presentDate, backDate]);
    }

    if (pcSearchForm?.Segment) {
      const costCenterArray = pcSearchForm?.Segment.split("$^$");
      const valuesSelected = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setSelectedPresetValues((prev) => ({
        ...prev,
        Segment: valuesSelected ?? [], // Use option as the key in the state
      }));
    }

    if (pcSearchForm?.Region) {
      const costCenterArray = pcSearchForm?.Region.split("$^$");
      const valuesSelected = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setselectedPresetRegion(valuesSelected);
    }
  }, [pcSearchForm]);

  useEffect(() => {
    Object.keys(selectedValues).forEach((option) => {
      const tempSelected = selectedValues[option]
        ?.map((item) => item?.code)
        .join("$^$");
      let tempFilterData = {
        ...pcSearchForm,
        [option]: tempSelected,
      };

      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: tempFilterData,
        })
      );
    });
  }, [selectedValues]);

  useEffect(() => {
    let tempCompanyCode = selectedComanyCode
      .map((item) => item?.code)
      .join("$^$");
    let tempFilterData = {
      ...pcSearchForm,
      "Company Code": tempCompanyCode,
    };
    dispatch(
      commonFilterUpdate({
        module: "ProfitCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedComanyCode]);

  useEffect(() => {
    let tempPersonResponsible = selectedPersonResponsible
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...pcSearchForm,
      personResponsible: tempPersonResponsible,
    };
    dispatch(
      commonFilterUpdate({
        module: "ProfitCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedPersonResponsible]);

  useEffect(() => {
    let tempLongText = selectedLongText.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...pcSearchForm,
      longText: tempLongText,
    };

    dispatch(
      commonFilterUpdate({
        module: "ProfitCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedLongText]);

  useEffect(() => {
    var tempCity = selectedCity.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...pcSearchForm,
      city: tempCity,
    };
    dispatch(
      commonFilterUpdate({
        module: "ProfitCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedCity]);

  useEffect(() => {
    var tempRegion = selectedRegion.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...pcSearchForm,
      Region: tempRegion,
    };
    dispatch(
      commonFilterUpdate({
        module: "ProfitCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedRegion]);

  useEffect(() => {
    var tempProfitCenterName = selectedProfitCenterName
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...pcSearchForm,
      profitCenterName: tempProfitCenterName,
    };
    dispatch(
      commonFilterUpdate({
        module: "ProfitCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedProfitCenterName]);

  useEffect(() => {
    var tempStreet = selectedStreet.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...pcSearchForm,
      street: tempStreet,
    };
    dispatch(
      commonFilterUpdate({
        module: "ProfitCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedStreet]);

  useEffect(() => {
    var tempPC = selectedProfitCenter.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...pcSearchForm,
      "Profit Center": tempPC,
    };
    dispatch(
      commonFilterUpdate({
        module: "ProfitCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedProfitCenter]);

  useEffect(() => {
    var tempCreatedBy = selectedCreatedBy.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...pcSearchForm,
      createdBy: tempCreatedBy,
    };
    dispatch(
      commonFilterUpdate({
        module: "ProfitCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedCreatedBy]);

  useEffect(() => {
    var tempCountry = selectedCountry.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...pcSearchForm,
      country: tempCountry,
    };
    dispatch(
      commonFilterUpdate({
        module: "ProfitCenter",
        filterData: tempFilterData,
      })
    );
    getRegion(tempCountry);
  }, [selectedCountry]);

  useEffect(() => {
    if (tableRows?.blockingStatus === "Blocked") {
      // const filteredData = dataListBlockNamesSelected.filter(item => item.templateName !== "Block");
      setFilteredRuleData(true);
    } else {
      setFilteredRuleData(false);
    }
  }, [tableRows]);
  useEffect(() => {
    const isAnyBlocked = requiredArrayDetailsMass?.some(
      (row) => row.blockingStatus === "Blocked"
    );

    if (isAnyBlocked) {
      setFilteredRuleDataMass(true);
    } else {
      setFilteredRuleDataMass(false);
    }
  }, [requiredArrayDetailsMass]);
  useEffect(() => {
    getCompanyCodeBasedOnControllingArea(initialScreenData);
    getCompanyCodeForwoCopy(initialScreenData);
  }, [initialScreenData?.ControllingArea]);

  useEffect(() => {
    setNewComapnyCode("");
    setNewProfitCenterName("");
  }, [radioValue]);
  // const fetchOptionsForDynamicFilter = (apiEndpoint) => {
  //   const hSuccess = (data) => {
  //     setDynamicOptions({
  //       ...dynamicOptions,
  //       [apiEndpoint]: data.body,

  //     });
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(apiEndpoint, "get", hSuccess, hError);
  // };

  const fetchOptionsForDynamicFilter = (apiEndpoint, selectedItem) => {
    setIsDropDownLoading(true);
    let payload;
    if (selectedItem === "Segment") {
      payload = {
        segment: "",
        controllingArea: pcSearchForm?.controllingArea?.code
          ? pcSearchForm?.controllingArea?.code === ""
            ? "ETCA"
            : pcSearchForm?.controllingArea?.code
          : "ETCA",
        rolePrefix: "ETP",
        top: 200,
        skip: 0,
      };
    } else {
      payload = {
        controllingArea: pcSearchForm?.controllingArea?.code
          ? pcSearchForm?.controllingArea?.code === ""
            ? "ETCA"
            : pcSearchForm?.controllingArea?.code
          : "ETCA",
        rolePrefix: "ETP",
        top: 200,
        skip: 0,
      };
    }

    const hSuccess = (data) => {
      const newOptions = data.body;
      // Merge the new options with the existing dynamicOptions
      setDynamicOptions((prev) => ({ ...prev, [selectedItem]: newOptions }));
      setIsDropDownLoading(false);
    };

    const hError = (error) => {
      console.log(error);
    };
    doAjax(apiEndpoint, "post", hSuccess, hError, payload);
  };

  const clearSearchBar = () => {
    setMaterialNumber("");
  };

  const getMaterialNoGlobalSearch = (fetchSkip) => {
    setTableLoading(true);
    if (!fetchSkip) {
      setPage(0);
      setPageSize(10);
      setSkip(0);
    }
    let payload = {
      companyCode: pcSearchForm?.companyCode?.code ?? "",
      longText: pcSearchForm?.longText ?? "",
      personResponsible: pcSearchForm?.personResponsible ?? "",
      city: pcSearchForm?.city ?? "",
      region: pcSearchForm?.region?.code ?? "",
      street: pcSearchForm?.street ?? "",
      country: pcSearchForm?.country?.code ?? "",
      fromDate:
        moment(pcSearchForm?.createdOn[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate:
        moment(pcSearchForm?.createdOn[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      controllingArea: "",
      profitCenter: formcontroller_SearchBar?.number ?? "",
      profitCenterName: pcSearchForm?.profitCenterName ?? "",
      createdBy: pcSearchForm?.createdBy ?? "",
      //userId: CompCodeDataPC.length > 0 ? CompCodeDataPC[0].UserId : "",
      segment: pcSearchForm?.Segment?.code ?? "",
      profitCenterGroup: pcSearchForm?.profitCenterGroup?.code ?? "",
      blockingStatus:
        pcSearchForm?.blockingStatus === "Blocked"
          ? "X"
          : pcSearchForm?.blockingStatus === "Unblocked"
          ? "Y"
          : "",
      top: "500",
      skip: fetchSkip ?? 0,
    };

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        var rows = [];
        for (let index = 0; index < data?.body?.list?.length; index++) {
          var tempObj = data?.body.list[index];

          // if (tempObj["MaterialNo"]) {
          var tempRow = {
            id: uuidv4(),
            longText: tempObj?.Description,
            personResponsible: tempObj?.PersonResponsible,
            city: tempObj?.Location,
            region: tempObj?.Region,
            street: tempObj?.Street,
            country: tempObj?.Country,
            createdOn:
              tempObj["CreatedOn"] !== ""
                ? `${moment(tempObj["CreatedOn"]).format("DD MMM YYYY")}`
                : "Not Available",
            controllingArea: tempObj.ControllingArea,
            companyCode: tempObj.CompanyCode,
            profitCenter: tempObj.ProfitCenter,
            profitCenterGroup: tempObj.ProfitCenterGroup,
            ProfitCenterName: tempObj.ProfitCenterName,
            CreatedBy:
              tempObj["CreatedBy"] !== ""
                ? `${tempObj["CreatedBy"]}`
                : "Not Available",
            Segment: tempObj.Segment,
            blockingStatus:
              tempObj["LockIndicator"] === "X" ? "Blocked" : "Unblocked",
          };
          rows.push(tempRow);
          // }
        }
        // rows.sort(
        //   (a, b) =>
        //     moment(a.createdOn, "DD MMM YYYY HH:mm") -
        //     moment(b.createdOn, "DD MMM YYYY HH:mm")
        // );

        setRmDataRows(rows.reverse());
        setTableLoading(false);
        setroCount(rows.length);
        setCount(data?.body?.count);
      } else if (data.statusCode === 400) {
        setSearchDialogTitle("Warning");
        setSearchDialogMessage(
          "Please Select Lesser Fields as the URL is getting too long !!"
        );
        handleSearchDialogClickOpen();
      }
    };
    let hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCentersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  /* Setting Default Dates */
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 15);

  const [date, setDate] = useState([backDate, presentDate]);
  const [date1, setDate1] = useState([backDate, presentDate]);

  const handleDate = (e) => {
    if (e !== null) {
      dispatch(
        commonFilterUpdate({
          module: "ProfitCenter",
          filterData: {
            ...pcSearchForm,
            createdOn: e,
          },
        })
      );
    }
  };

  const handleDate1 = (e) => {
    if (e !== null) setDate1(e.reverse());
  };

  const handleSnackBarClickaccept = () => {
    setOpenSnackBaraccept(true);
  };

  const handleSnackBarCloseaccept = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }

    setOpenSnackBaraccept(false);
  };

  const handleUserName = (e) => {
    setUserName(e.target.value);
  };

  useEffect(() => {
    getFilter();
  }, []);
  // Get Filter Data

  const getFilter = (fetchSkip) => {
    setTableLoading(true);
    // if (!fetchSkip) {
    setPage(0);
    //   setPageSize(10);
    //   setSkip(0);
    // }
    let payload = {
      companyCode: pcSearchForm?.["Company Code"] ?? "",
      longText: pcSearchForm?.longText ?? "",
      personResponsible: pcSearchForm?.personResponsible ?? "",
      city: pcSearchForm?.city ?? "",
      region: pcSearchForm?.Region ?? "",
      street: pcSearchForm?.street ?? "",
      country: pcSearchForm?.country ?? "",
      fromDate:
        moment(pcSearchForm?.createdOn[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate:
        moment(pcSearchForm?.createdOn[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      controllingArea: pcSearchForm?.controllingArea?.code
        ? pcSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : pcSearchForm?.controllingArea?.code
        : "ETCA",
      //userId: userId?.UserId || CompCodeDataPC[0]?.UserId || "",
      rolePrefix: "ETP",
      profitCenter: pcSearchForm?.["Profit Center"] ?? "",
      profitCenterName: pcSearchForm?.profitCenterName ?? "",
      createdBy: pcSearchForm?.createdBy ?? "",
      segment: pcSearchForm?.Segment ?? "",
      profitCenterGroup: pcSearchForm?.profitCenterGroup?.code ?? "",
      blockingStatus:
        pcSearchForm?.blockingStatus === "Blocked"
          ? "X"
          : pcSearchForm?.blockingStatus === "Unblocked"
          ? "Y"
          : "",
      top: 100,
      skip: 0,
    };
    const hSuccess = (data) => {
      // let data = demoData;

      if (data.statusCode === 200) {
        var rows = [];
        for (let index = 0; index < data?.body?.list?.length; index++) {
          var tempObj = data?.body.list[index];

          // if (tempObj["MaterialNo"]) {
          var tempRow = {
            id: uuidv4(),
            description: tempObj?.Description,
            personResponsible: tempObj?.PersonResponsible,
            Location: tempObj?.Location,
            Region: tempObj?.Region,
            street: tempObj?.Street,
            country: tempObj?.Country,
            createdOn:
              tempObj["CreatedOn"] !== ""
                ? `${moment(tempObj["CreatedOn"]).format("DD MMM YYYY")}`
                : "Not Available",
            controllingArea: tempObj.ControllingArea,
            companyCode: tempObj.CompanyCode,
            profitCenter: tempObj.ProfitCenter,
            profitCenterGroup: tempObj.ProfitCenterGroup,
            ProfitCenterName: tempObj.ProfitCenterName,
            CreatedBy:
              tempObj["CreatedBy"] !== ""
                ? `${tempObj["CreatedBy"]}`
                : "Not Available",
            Segment: tempObj.Segment,
            blockingStatus:
              tempObj["LockIndicator"] === "X" ? "Blocked" : "Unblocked",
          };
          rows.push(tempRow);
          // }
        }
        // rows.sort(
        //   (a, b) =>
        //     moment(a.createdOn, "DD MMM YYYY HH:mm") -
        //     moment(b.createdOn, "DD MMM YYYY HH:mm")
        // );
        setRmDataRows(rows);
        setTableLoading(false);
        setroCount(rows.length);
        setCount(data?.body?.count);
      } else if (data.statusCode === 400) {
        setSearchDialogTitle("Warning");
        setSearchDialogMessage(
          "Please Select Lesser Fields as the URL is getting too long !!"
        );
        handleSearchDialogClickOpen();
      }
    };
    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCentersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getFilterBasedOnPagination = (fetchSkip) => {
    setTableLoading(true);
    // if (!fetchSkip) {
    //   setPage(0);
    //   setPageSize(10);
    //   setSkip(0);
    // }

    let payload = {
      companyCode: pcSearchForm?.["Company Code"] ?? "",
      longText: pcSearchForm?.longText ?? "",
      personResponsible: pcSearchForm?.personResponsible ?? "",
      city: pcSearchForm?.city ?? "",
      region: pcSearchForm?.Region ?? "",
      street: pcSearchForm?.street ?? "",
      country: pcSearchForm?.country ?? "",
      fromDate:
        moment(pcSearchForm?.createdOn[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate:
        moment(pcSearchForm?.createdOn[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      controllingArea: pcSearchForm?.controllingArea?.code
        ? pcSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : pcSearchForm?.controllingArea?.code
        : "ETCA",
      //userId: userId?.UserId || CompCodeDataPC[0]?.UserId || "",
      rolePrefix: "ETP",
      profitCenter: pcSearchForm?.["Profit Center"] ?? "",
      profitCenterName: pcSearchForm?.profitCenterName ?? "",
      createdBy: pcSearchForm?.createdBy?.code ?? "",
      segment: pcSearchForm?.Segment ?? "",
      profitCenterGroup: pcSearchForm?.profitCenterGroup?.code ?? "",
      blockingStatus:
        pcSearchForm?.blockingStatus === "Blocked"
          ? "X"
          : pcSearchForm?.blockingStatus === "Unblocked"
          ? "Y"
          : "",
      top: 100,
      // skip: 100 * page ?? 0,
      skip: rmDataRows?.length ?? 0,
    };
    const hSuccess = (data) => {
      var rows = [];
      for (let index = 0; index < data?.body?.list?.length; index++) {
        var tempObj = data?.body.list[index];

        // if (tempObj["MaterialNo"]) {
        var tempRow = {
          id: uuidv4(),
          description: tempObj?.Description,
          personResponsible: tempObj?.PersonResponsible,
          Location: tempObj?.Location,
          Region: tempObj?.Region,
          street: tempObj?.Street,
          country: tempObj?.Country,
          createdOn:
            tempObj["CreatedOn"] !== ""
              ? `${moment(tempObj["CreatedOn"]).format("DD MMM YYYY")}`
              : "Not Available",
          controllingArea: tempObj.ControllingArea,
          companyCode: tempObj.CompanyCode,
          profitCenter: tempObj.ProfitCenter,
          profitCenterGroup: tempObj.ProfitCenterGroup,
          ProfitCenterName: tempObj.ProfitCenterName,
          CreatedBy:
            tempObj["CreatedBy"] !== ""
              ? `${tempObj["CreatedBy"]}`
              : "Not Available",
          Segment: tempObj.Segment,
          blockingStatus:
            tempObj["LockIndicator"] === "X" ? "Blocked" : "Unblocked",
        };
        rows.push(tempRow);
        // }
      }
      // rows.sort(
      //   (a, b) =>
      //     moment(a.createdOn, "DD MMM YYYY HH:mm") -
      //     moment(b.createdOn, "DD MMM YYYY HH:mm")
      // );
      setRmDataRows((prevRows) => [...prevRows, ...rows]);
      setTableLoading(false);
      setroCount(rows.length);
      setCount(data?.body?.count);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCentersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  useEffect(() => {
    // if(page!==0) {
    //   if ((parseInt(page) + 1) * parseInt(pageSize) >= parseInt(skip) + 100) {
    //     getFilterBasedOnPagination();
    //     // setSkip((prev) => prev + 500);
    //   }
    // }
    if (page * pageSize >= rmDataRows?.length) {
      getFilterBasedOnPagination();
      // setSkip((prev) => prev + 500);
    }
  }, [page, pageSize]);

  const getButtons = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_DYN_BTN_DT",
      version: "v2",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_DYN_BTN_MODULE_NAME": "ET PC",
          "MDG_CONDITIONS.MDG_DYN_BTN_REQUEST_TYPE": "Create",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    // setIsLoading(true);
    // const formData = new FormData();

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        let responseData = data?.data?.result[0]?.MDG_DYN_BTN_ACTION_TYPE;
        // let lookupKeyName= data?.data?.result[0]?.conditions[0]?.["MDG_CONDITIONS.MDG_FIELD_NAME"]
        //setButtonsIDM(responseData);
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };
  useEffect(() => {
    //getFilter();
    getButtons();
    // functions_PresetFilter.getFilterPresets();
  }, []);

  const [userList, setUserList] = useState([]);

  const handleSnackbarClose = () => {
    setopenSnackbar(false);
  };
  const handleReject = () => {
    setMessageDialogTitle("Success");
    setMessageDialogMessage("Comment Posted");
    setMessageDialogSeverity("success");
    handleMessageDialogClickOpen();
  };
  const handleAccept = () => {
    setMessageDialogTitle("Success");
    setMessageDialogMessage("Comment Posted");
    setMessageDialogSeverity("success");
    handleMessageDialogClickOpen();
  };
  // const handleSelectionListItem = (item) => {
  //   let updatedSelectedListItems;
  //   if (selectedListItems.some((selectedItem) => selectedItem.id === item.id)) {
  //     updatedSelectedListItems = selectedListItems.filter(
  //       (selectedItem) => selectedItem.id !== item.id
  //     );
  //   } else {
  //     updatedSelectedListItems = [...selectedListItems, item];
  //   }
  //   setSelectedListItems(updatedSelectedListItems);
  //   setSelectAll(updatedSelectedListItems.length === ruleData.length);
  // };
  // const handleSelectAll = () => {
  //   const selectAllState = !selectAll;
  //   setSelectAll(selectAllState);
  //   setSelectedListItems(selectAllState ? [...ruleData] : []);
  // };
  const handleSelectAll = () => {
    if (!selectAll && alignment === "ALL OTHER CHANGES") {
      setDataListAllOtherChangesSelected(dataListAllOtherChanges);
    } else if (selectAll && alignment === "ALL OTHER CHANGES") {
      setDataListAllOtherChangesSelected([]);
    } else if (!selectAll && alignment === "BLOCK") {
      setDataListBlockNamesSelected(dataListBlockNames);
    } else if (!selectAll && alignment === "TEMPORARY BLOCK/UNBLOCK") {
      setDataListTemporaryBlockNamesSelected(dataListTemporaryBlockNames);
    } else if (selectAll && alignment === "TEMPORARY BLOCK/UNBLOCK") {
      setDataListTemporaryBlockNamesSelected([]);
    } else if (!selectAll && alignment === "BLOCK") {
      setDataListBlockNamesSelected(dataListBlockNames);
    } else {
      setDataListBlockNamesSelected([]);
    }
    setSelectAll(!selectAll);
  };
  const handleOpenDialogIDM = (rows) => {
    setOpenDialog(true);
    setTableRows(rows);
    // getListofData(); // Fetch data when the dialog is opened
  };

  // const handleCloseDialogIDM = () => {
  //   setDataListAllOtherChangesSelected([]);
  //   setOpenDialogIDM(false);
  //   //setSelectAll(!selectAll);
  //   setSelectAll(false);
  //   setSelectedListItems([]);

  // };
  const handleCloseDialogIDM = () => {
    setSelectedRows([]);
    setSelectAll(false);
    setSelectedListItems([]);
    setSelectedMassChangeRowData([]);
    setRequiredArrayDetailsMass([]);

    // Step 3: Close the dialog
    setOpenDialogIDM(false);
  };

  const handleCloseDialog = () => {
    setDataListAllOtherChangesSelected([]);
    setSelectAll(false);
    setOpenDialog(false);
  };
  const handleOpendialog2 = (id) => {
    setID(id);
    fetchPOHeader(id);
    setOpendialog2(true);
  };
  const handleClosedialog2 = () => {
    setOpendialog2(false);
  };
  const handleOpendialog3 = (id) => {
    setOpendialog3(true);
    setConfirmingid(id);
    fetchPOHeader(id);
  };
  const handleClosedialog3 = () => {
    setOpendialog3(false);
  };
  const handleOpenforwarddialog = () => {
    setOpenforwarddialog(true);
  };
  const handleCloseforwarddialog = () => {
    setOpenforwarddialog(false);
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  const handleClose_Preset = () => {
    setPresetName("");
    setAnchorEl(null);
  };
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };
  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
    setSkip(0);
  };

  const [presets, setPresets] = useState(null);
  const [presetName, setPresetName] = useState(null);

  const handleClear = () => {
    dispatch(commonFilterClear({ module: "ProfitCenter" }));

    setselectedComanyCode([]);
    setselectedLongText([]);
    setselectedPersonResponsible([]);
    setselectedCountry([]);
    setselectedCreatedBy([]);
    setselectedProfitCenterName([]);
    setselectedStreet([]);
    setselectedProfitCenter([]);
    setselectedCity([]);
    setselectedRegion([]);
    setSelectedValues({});

    setselectedPresetComanyCode([]);
    setselectedPresetLongText([]);
    setselectedPresetPersonResponsible([]);
    setselectedPresetCountry([]);
    setselectedPresetCreatedBy([]);
    setselectedPresetProfitCenterName([]);
    setselectedPresetStreet([]);
    setselectedPresetProfitCenter([]);
    setselectedPresetCity([]);
    setselectedPresetRegion([]);
    setSelectedPresetValues({});

    setFilterFieldData({});
  };
  const onRowsSelectionHandler = (ids) => {
    //Selected Columns stored here
    const selectedRowsData = ids?.map((id) =>
      rmDataRows.find((row) => row.id === id)
    );
    var compCodes = selectedRowsData?.map((row) => row.company);
    var companySet = new Set(compCodes);
    var vendors = selectedRowsData?.map((row) => row.vendor);
    var vendorSet = new Set(vendors);
    var paymentTerms = selectedRowsData?.map((row) => row.paymentTerm);
    var paymentTermsSet = new Set(paymentTerms);
    if (selectedRowsData.length > 0) {
      if (companySet.size === 1) {
        if (vendorSet.size === 1) {
          if (paymentTermsSet.size !== 1) {
            setDisableButton(true);
            setMessageDialogTitle("Error");
            setMessageDialogMessage(
              "Invoice cannot be generated for vendors with different payment terms"
            );
            setMessageDialogSeverity("danger");
            handleMessageDialogClickOpen();
          } else setDisableButton(false);
        } else {
          setDisableButton(true);
          setMessageDialogTitle("Error");
          setMessageDialogMessage(
            "Invoice cannot be generated for multiple suppliers"
          );
          setMessageDialogSeverity("danger");
          handleMessageDialogClickOpen();
        }
      } else {
        setDisableButton(true);
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          "Invoice cannot be generated for multiple companies"
        );
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
      }
    } else {
      setDisableButton(true); //Enable the Create E-Invoice button when at least one row is selected and no two companys or vendors are same
    }
    setSelectedRow(ids); //Setting the ids(PO Numbers) of selected rows
    setSelectedDetails(selectedRowsData); //Setting the entire data of a selected row
  };

  function refreshPage() {
    getFilter();
  }

  const [company, setCompany] = useState([]);
  const [Companyid, setCompanyid] = useState([]);

  const [open, setOpen] = useState(false);
  const [matAnchorEl, setMatAnchorEl] = useState(null);
  const [materialDetails, setMaterialDetails] = useState(null);
  const [itemDataRows, setItemDataRows] = useState([]);

  const handlePODetailsClick = (event) => {
    setOpendialog3(true);
  };

  const matOpen = Boolean(matAnchorEl);
  const popperId = matOpen ? "simple-popper" : undefined;

  const handleClickOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };

  const [poNum, setPONum] = useState(null);
  const createMultiValueCell = (fieldName, displayName) => ({
      field: fieldName,
      headerName: displayName,
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const values = params.value ? params.value.split(",").map(m => m.trim()) : [];
        const displayCount = values.length - 1;
  
        if (values.length === 0) return "-";
  
        const formatText = (text) => {
          const [code, ...rest] = text.split('-');
          return (
            <>
              <strong>{code}</strong>{rest.length ? ` - ${rest.join('-')}` : ''}
            </>
          );
        };
  
        return (
          <Box sx={{ 
            display: "flex", 
            alignItems: "center",
            width: "100%",
            minWidth: 0
          }}>
            <Tooltip 
              title={values[0]}
              placement="top"
              arrow
            >
              <Typography 
                variant="body2" 
                sx={{ 
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  flex: 1,
                  minWidth: 0,
                }}
              >
                {formatText(values[0])}
              </Typography>
            </Tooltip>
            {displayCount > 0 && (
              <Box sx={{ 
                display: "flex",
                alignItems: "center",
                ml: 1,
                flexShrink: 0 
              }}>
                <Tooltip
                  arrow
                  placement="right"
                  title={
                    <Box sx={{ p: 1, maxHeight: 200, overflowY: "auto" }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                        Additional {displayName}s ({displayCount})
                      </Typography>
                      {values.slice(1).map((value, idx) => (
                        <Typography key={idx} variant="body2" sx={{ mb: 0.5 }}>
                          {formatText(value)}
                        </Typography>
                      ))}
                    </Box>
                  }
                >
                  <Box sx={{ 
                    display: "flex", 
                    alignItems: "center",
                    cursor: "pointer"
                  }}>
                    <InfoIcon 
                      sx={{ 
                        fontSize: "1rem",
                        color: "primary.main",
                        "&:hover": { color: "primary.dark" }
                      }} 
                    />
                    <Typography 
                      variant="caption" 
                      sx={{ 
                        ml: 0.5,
                        color: "primary.main",
                        fontSize: "11px"
                      }}
                    >
                      +{displayCount}
                    </Typography>
                  </Box>
                </Tooltip>
              </Box>
            )}
          </Box>
        );
      }
    });
    const  createSingleValueCell = (fieldName, displayName) => ({
      field: fieldName,
      headerName: displayName,
      editable: false,
      flex: 1,
      renderCell: (params) => {
          const [firstPart, ...rest] = params.value?.split(" - ") || [];
          return (
            <span style={{ flex: 1, wordBreak: 'break-word', whiteSpace: 'normal' }}>
              <strong>{firstPart}</strong> {rest.length ? `- ${rest.join(" - ")}` : ""}
            </span>
          );
        },
    });
  const fetchMasterDataColumns = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_COLUMN,
      version: "v1",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": "US",
          "MDG_CONDITIONS.MDG_MODULE":"Profit Center"||"",
          "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE": "Master Data",
        },
      ],
    };
    getMasterDataColumn(payload);
  };
  const fetchSearchParameterFromDt = () => {
    let payload = {
          decisionTableId: null,
          decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_PARAMETER,
          version: "v1",
          conditions: [
            {
              "MDG_CONDITIONS.MDG_MAT_REGION":"US",
              "MDG_CONDITIONS.MDG_MODULE":"ProfitCenter",
              "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data",
            },
          ],
        };
        getSearchParams(payload);
  }
  const fetchPOHeader = (id) => {
    var formData = new FormData();
    if (id) formData.append("extReturnId", id);
    const hSuccess = (data) => {
      if (data) {
        setPoHeader(data);
        setPONum(data[0]["poNumber"] ?? "");
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_Returns}/returnsHeader/getReturnsPreview`,
      "postformdata",
      hSuccess,
      hError,
      formData
    );
  };

  const [id, setID] = useState("");
  const columns = [
    {
      field: "companyCode",
      headerName: "Company Code",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "profitCenter",
      headerName: "Profit Center",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "longText",
      headerName: "Long Description",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "personResponsible",
      headerName: "Person Responsible",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "city",
      headerName: "City",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "region",
      headerName: "Region",
      editable: false, //dd
      flex: 1,
    },
    // {
    //   field: "profitCenterName",
    //   headerName: "Profit Center Name",
    //   editable: false, //text
    //   flex: 1,
    // },
    // {
    //   field: "controllingArea",
    //   headerName: "Controlling Area",
    //   editable: false, //dd
    //   flex: 1,
    // },
    // {
    //   field: "profitCenterGroup",
    //   headerName: "Profit Center Group", //dd
    //   editable: false,
    //   flex: 1,
    // },
    // {
    //   field: "createdBy",
    //   headerName: "Created By",
    //   editable: false,
    //   flex: 1,
    // },
    {
      field: "blockingStatus",
      headerName: "Blocking Status", //dd
      editable: false,
      flex: 1,
    },
  ];
  // const actionColumn = [
  //   {
  //     field: "actions",
  //     align: "center",
  //     flex: 1, // Use flex for responsive width
  //     headerAlign: "center",
  //     headerName: "Actions",
  //     sortable: false,
  //     renderCell: (params) => (
  //       <div>
  //         <Tooltip title="Change">
  //           <IconButton
  //             aria-label="View Metadata"
  //             onClick={() => handleOpenDialogIDM(params.row)} // Open dialog when edit button is clicked
  //           >
  //             <EditOutlinedIcon />
  //           </IconButton>
  //         </Tooltip>
  //       </div>
  //     ),
  //   },
  // ];

  const dynamicFilterColumns = selectedOptions
    ?.map((option) => {
      const field = titleToFieldMapping[option]; // Get the corresponding field from the mapping
      if (!field) {
        return null; // Handle the case when the field doesn't exist in the mapping
      }
      return {
        field: field, // Use the field name from the mapping
        headerName: option,
        editable: false,
        flex: 1,
      };
    })
    .filter((column) => column !== null); // Remove any null columns

  const allColumns = [...columns, ...dynamicFilterColumns];

  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      allColumns.forEach((item) => {
        if (item.headerName.toLowerCase() !== "actions" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `Profit Center Data-${moment(presentDate).format(
          "DD-MMM-YYYY"
        )}`,
        columns: excelColumns,
        rows: rmDataRows,
      });
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };

  const functions_ExportAsExcelErrorProcessNotCompleate = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      duplicateFieldsColumns.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `Duplicate Requests -${moment(presentDate).format(
          "DD-MMM-YYYY"
        )}`,
        columns: excelColumns,
        rows: duplicateFieldsData,
      });
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() =>
            functions_ExportAsExcelErrorProcessNotCompleate.convertJsonToExcel()
          }
        >
          Download
        </Button>
      );
    },
  };

  const capitalize = (str) => {
    const arr = str.split(" ");
    for (var i = 0; i < arr.length; i++) {
      arr[i] = arr[i].charAt(0) + arr[i].slice(1).toLowerCase();
    }

    const str2 = arr.join(" ");
    return str2;
    //  })
  };
  const createMasterDataColums = (data) => {
           const columns = [];
           let sortedData = data?.sort(
           (a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO
         )|| [];
           if (sortedData) {
             sortedData?.forEach((item) => {
               if(item?.MDG_MAT_VISIBILITY===VISIBILITY_TYPE.DISPLAY){
                 
               if (item?.MDG_MAT_UI_FIELD_NAME) {
                 const fieldName = item.MDG_MAT_JSON_FIELD_NAME;
                 const headerName = item.MDG_MAT_UI_FIELD_NAME;
                 if(fieldName==="DataValidation"){
                   columns.push(displayCell());
                 }
                 else if (item.MDG_MAT_FIELD_TYPE === "Multiple") {
                   columns.push(createMultiValueCell(fieldName, headerName));
                 } 
                 else if (item.MDG_MAT_FIELD_TYPE === "Single") {
                   columns.push(createSingleValueCell(fieldName, headerName));
                 }
               }
             }
           });
           }
           return columns;
         }
    useEffect(() => {
         if (masterDataDtResponse) {
           const columnsGlobal = createMasterDataColums(masterDataDtResponse?.result?.[0]?.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);
           setDynamicColumns(columnsGlobal);
         }
         if(dtSearchParamsResponse){
      const response = dtSearchParamsResponse?.result?.[0]?.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE;
      const additionalData = response?.filter((item) => {
        return item.MDG_MAT_FILTER_TYPE === "Additional";
      }).map((item) => {
        return { title: item.MDG_MAT_UI_FIELD_NAME };
      });
      setSearchParameters(response);
      setItem(additionalData);
    }
       }, [masterDataDtResponse,dtSearchParamsResponse]); 
    
    useEffect(() => {
          fetchMasterDataColumns();
          fetchSearchParameterFromDt();
    },[]);

  let ref_elementForExport = useRef(null);
  // let exportAsPicture = () => {
  //   setTimeout(() => {
  //     captureScreenShot("Material-Single");
  //   }, 100);
  // };
  const handleDownloadTemplate = async () => {
    var downloadPayload = selectedMassChangeRowData?.map((x) => {
      return {
        profitCenter: x.profitCenter,
        controllingArea: x.controllingArea,
      };
    });

    let hSuccess = (response) => {
      handleCloseDialogIDM();
      setIsLoading(false);
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `Profit Center Mass Change.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
      setMessageDialogMessage(
        `Profit Center Mass Change.xlsx has been downloaded successfully`
      );
      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/excel/downloadExcelWithData`,
      "postandgetblob",
      hSuccess,
      hError,
      downloadPayload
    );
  };

  const handleChangeDownload = () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);

    const downloadPayload = selectedMassChangeRowData?.map((x) => ({
      profitCenter: x.profitCenter,
      controllingArea: x.controllingArea,
    }));

    // Create headers
    const selectedItemArr = [
      "Profit Center",
      "Controlling Area",
      "Business Segment",
    ];
    let filterDataWithSelectedData = [];
    let selectedOptionsForDownload = new Set(); // Ensure this is initialized as a Set

    const addUniqueFields = (filteredData, selectedList) => {
      const uniqueFields = new Set();
      selectedList.forEach((input) => {
        filteredData?.forEach((item, index) => {
          if (
            item.MDG_SELECT_OPTION === input.name &&
            !uniqueFields.has(item.MDG_FIELD_NAME)
          ) {
            uniqueFields.add(item.MDG_FIELD_NAME);
            filterDataWithSelectedData.push({
              id: index,
              name: item.MDG_FIELD_NAME,
            });
            selectedOptionsForDownload.add(item.MDG_SELECT_OPTION); // Add the selected option to the set
          }
        });
      });
    };

    if (alignment === "ALL OTHER CHANGES") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
      );
      addUniqueFields(filteredData, dataListAllOtherChangesSelected);
    } else if (alignment === "BLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
      );
      addUniqueFields(filteredData, dataListBlockNamesSelected);
    } else if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
      );
      addUniqueFields(filteredData, dataListTemporaryBlockNamesSelected);
    }

    filterDataWithSelectedData.forEach((selectedElement) => {
      selectedItemArr.push(selectedElement.name);
    });

    const downloadPayloadHash = {
      coAreaPCs: downloadPayload,
      headers: selectedItemArr,
      dtName: "MDG_PC_FIELD_CONFIG",
      version: "v1",
      templateName: Array.from(selectedOptionsForDownload).join(", "), // Join all unique selected options
      templateHeaders: "",
      IsSunoco: "false",
    };
    const downloadPayloadHashTemporaryBlock = {
      coAreaPCs: downloadPayload,
      isSunoco: "false",
    };

    const hSuccess = (response) => {
      if (response?.size !== 0 || response?.type !== "") {
        setSelectAll(false);

        const href = URL.createObjectURL(response);
        const link = document.createElement("a");
        link.href = href;
        link.setAttribute("download", `Profit Center Mass Change.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Success");
        setMessageDialogMessage(
          `Profit Center Mass Change.xlsx has been downloaded successfully`
        );
        setMessageDialogSeverity("success");
        setBlurLoading(false);
        setLoaderMessage("");
      } else {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        // setMessageDialogMessage(`${error.message}`);
        setMessageDialogMessage("Please try again.");
        setMessageDialogSeverity("danger");
        setBlurLoading(false);
        setLoaderMessage("");
      }
    };

    const hError = (error) => {
      setBlurLoading(false);
      setLoaderMessage("");
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          `Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`
        );
        setMessageDialogSeverity("danger");
        setBlurLoading(false);
      }
    };
    if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      doAjax(
        `/${destination_ProfitCenter_Mass}/excel/downloadExcelWithDataForTempBlock`,
        "postandgetblob",
        hSuccess,
        hError,
        downloadPayloadHashTemporaryBlock
      );
    } else {
      doAjax(
        `/${destination_ProfitCenter_Mass}/excel/downloadExcelWithData`,
        "postandgetblob",
        hSuccess,
        hError,
        downloadPayloadHash
      );
    }
  };
  const handleChangeDownloadEmail = () => {
    // setLoaderMessage(
    //   "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    // );
    setBlurLoading(true);

    const downloadPayload = selectedMassChangeRowData?.map((x) => ({
      profitCenter: x.profitCenter,
      controllingArea: x.controllingArea,
    }));

    // Create headers
    const selectedItemArr = [
      "Profit Center",
      "Controlling Area",
      "Business Segment",
    ];
    let filterDataWithSelectedData = [];
    let selectedOptionsForDownload = new Set(); // Ensure this is initialized as a Set

    const addUniqueFields = (filteredData, selectedList) => {
      const uniqueFields = new Set();
      selectedList.forEach((input) => {
        filteredData?.forEach((item, index) => {
          if (
            item.MDG_SELECT_OPTION === input.name &&
            !uniqueFields.has(item.MDG_FIELD_NAME)
          ) {
            uniqueFields.add(item.MDG_FIELD_NAME);
            filterDataWithSelectedData.push({
              id: index,
              name: item.MDG_FIELD_NAME,
            });
            selectedOptionsForDownload.add(item.MDG_SELECT_OPTION); // Add the selected option to the set
          }
        });
      });
    };

    if (alignment === "ALL OTHER CHANGES") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
      );
      addUniqueFields(filteredData, dataListAllOtherChangesSelected);
    } else if (alignment === "BLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
      );
      addUniqueFields(filteredData, dataListBlockNamesSelected);
    } else if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
      );
      addUniqueFields(filteredData, dataListTemporaryBlockNamesSelected);
    }

    filterDataWithSelectedData.forEach((selectedElement) => {
      selectedItemArr.push(selectedElement.name);
    });

    const downloadPayloadHash = {
      coAreaPCs: downloadPayload,
      headers: selectedItemArr,
      dtName: "MDG_PC_FIELD_CONFIG",
      version: "v1",
      templateName: Array.from(selectedOptionsForDownload).join(", "), // Join all unique selected options
      templateHeaders: "",
      IsSunoco: "false",
    };
    const downloadPayloadHashTemporaryBlock = {
      coAreaPCs: downloadPayload,
      isSunoco: "false",
    };

    const hSuccess = (response) => {
      setSelectAll(false);
      setBlurLoading(false);
      setLoaderMessage("");
      // const href = URL.createObjectURL(response);
      // const link = document.createElement("a");
      // link.href = href;
      // link.setAttribute("download", `Profit Center Mass Change.xlsx`);
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);
      // URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
      setMessageDialogMessage(
        `Download has been started. You will get the Excel file via email`
      );
      setMessageDialogSeverity("success");
    };

    const hError = (error) => {
      setBlurLoading(false);
      setLoaderMessage("");
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          `Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`
        );
        setMessageDialogSeverity("danger");
        setBlurLoading(false);
      }
    };
    if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      doAjax(
        `/${destination_ProfitCenter_Mass}/excel/downloadExcelWithDataForTempBlockInMail`,
        "post",
        hSuccess,
        hError,
        downloadPayloadHashTemporaryBlock
      );
    } else {
      doAjax(
        `/${destination_ProfitCenter_Mass}/excel/downloadExcelWithDataInMail`,
        "post",
        hSuccess,
        hError,
        downloadPayloadHash
      );
    }
  };
  const handleChangeDownloadEmpty = () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);

    // const downloadPayload = selectedMassChangeRowData?.map((x) => ({
    //   profitCenter: x.profitCenter,
    //   controllingArea: x.controllingArea,
    // }));

    // Create headers
    const selectedItemArr = [
      "Profit Center",
      "Controlling Area",
      "Business Segment",
    ];
    let filterDataWithSelectedData = [];
    let selectedOptionsForDownload = new Set(); // Ensure this is initialized as a Set

    const addUniqueFields = (filteredData, selectedList) => {
      const uniqueFields = new Set();
      selectedList.forEach((input) => {
        filteredData?.forEach((item, index) => {
          if (
            item.MDG_SELECT_OPTION === input.name &&
            !uniqueFields.has(item.MDG_FIELD_NAME)
          ) {
            uniqueFields.add(item.MDG_FIELD_NAME);
            filterDataWithSelectedData.push({
              id: index,
              name: item.MDG_FIELD_NAME,
            });
            selectedOptionsForDownload.add(item.MDG_SELECT_OPTION); // Add the selected option to the set
          }
        });
      });
    };

    if (alignment === "ALL OTHER CHANGES") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
      );
      addUniqueFields(filteredData, dataListAllOtherChangesSelected);
    } else if (alignment === "BLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
      );
      addUniqueFields(filteredData, dataListBlockNamesSelected);
    } else if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
      );
      addUniqueFields(filteredData, dataListTemporaryBlockNamesSelected);
    }

    filterDataWithSelectedData.forEach((selectedElement) => {
      selectedItemArr.push(selectedElement.name);
    });

    const downloadPayloadHash = {
      coAreaPCs: [],
      headers: selectedItemArr,
      dtName: "MDG_PC_FIELD_CONFIG",
      version: "v1",
      templateName: Array.from(selectedOptionsForDownload).join(", "), // Join all unique selected options
      templateHeaders: "",
      IsSunoco: "false",
    };
    const downloadPayloadHashTemporaryBlock = {
      coAreaPCs: [],
      isSunoco: "false",
    };

    const hSuccess = (response) => {
      setLoaderMessage(
        "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
      );

      setSelectAll(false);
      if (response?.size !== 0 || response?.type !== "") {
        const href = URL.createObjectURL(response);
        const link = document.createElement("a");
        link.href = href;
        link.setAttribute("download", `Profit Center Mass Change.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Success");
        setMessageDialogMessage(
          `Profit Center Mass Change.xlsx has been downloaded successfully`
        );
        setMessageDialogSeverity("success");
        setBlurLoading(false);
        setLoaderMessage("");
      } else {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        // setMessageDialogMessage(`${error.message}`);
        setMessageDialogMessage("Please try again.");
        setMessageDialogSeverity("danger");
        setBlurLoading(false);
        setLoaderMessage("");
      }
    };

    const hError = (error) => {
      setBlurLoading(false);
      setLoaderMessage("");
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          `Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`
        );
        setMessageDialogSeverity("danger");
      }
    };
    if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      doAjax(
        `/${destination_ProfitCenter_Mass}/excel/downloadExcelWithDataForTempBlock`,
        "postandgetblob",
        hSuccess,
        hError,
        downloadPayloadHashTemporaryBlock
      );
    } else {
      doAjax(
        `/${destination_ProfitCenter_Mass}/excel/downloadExcelWithData`,
        "postandgetblob",
        hSuccess,
        hError,
        downloadPayloadHash
      );
    }
  };
  const handleChangeDownloadEmptyEmail = () => {
    // setLoaderMessage(
    //   "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    // );
    setBlurLoading(true);

    // const downloadPayload = selectedMassChangeRowData?.map((x) => ({
    //   profitCenter: x.profitCenter,
    //   controllingArea: x.controllingArea,
    // }));

    // Create headers
    const selectedItemArr = [
      "Profit Center",
      "Controlling Area",
      "Business Segment",
    ];
    let filterDataWithSelectedData = [];
    let selectedOptionsForDownload = new Set(); // Ensure this is initialized as a Set

    const addUniqueFields = (filteredData, selectedList) => {
      const uniqueFields = new Set();
      selectedList.forEach((input) => {
        filteredData?.forEach((item, index) => {
          if (
            item.MDG_SELECT_OPTION === input.name &&
            !uniqueFields.has(item.MDG_FIELD_NAME)
          ) {
            uniqueFields.add(item.MDG_FIELD_NAME);
            filterDataWithSelectedData.push({
              id: index,
              name: item.MDG_FIELD_NAME,
            });
            selectedOptionsForDownload.add(item.MDG_SELECT_OPTION); // Add the selected option to the set
          }
        });
      });
    };

    if (alignment === "ALL OTHER CHANGES") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "ALL OTHER CHANGES"
      );
      addUniqueFields(filteredData, dataListAllOtherChangesSelected);
    } else if (alignment === "BLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "BLOCK"
      );
      addUniqueFields(filteredData, dataListBlockNamesSelected);
    } else if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      const filteredData = fieldSelectionFromIdm.filter(
        (item) => item.MDG_FIELD_SELECTION_LVL === "TEMPORARY BLOCK/UNBLOCK"
      );
      addUniqueFields(filteredData, dataListTemporaryBlockNamesSelected);
    }

    filterDataWithSelectedData.forEach((selectedElement) => {
      selectedItemArr.push(selectedElement.name);
    });

    const downloadPayloadHash = {
      coAreaPCs: [],
      headers: selectedItemArr,
      dtName: "MDG_PC_FIELD_CONFIG",
      version: "v1",
      templateName: Array.from(selectedOptionsForDownload).join(", "), // Join all unique selected options
      templateHeaders: "",
      IsSunoco: "false",
    };
    const downloadPayloadHashTemporaryBlock = {
      coAreaPCs: [],
      isSunoco: "false",
    };

    const hSuccess = (response) => {
      // setLoaderMessage(
      //   "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
      // );
      setBlurLoading(false);
      setLoaderMessage("");
      setSelectAll(false);
      // const href = URL.createObjectURL(response);
      // const link = document.createElement("a");
      // link.href = href;
      // link.setAttribute("download", `Profit Center Mass Change.xlsx`);
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);
      // URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
      setMessageDialogMessage(
        `Download has been started. You will get the Excel file via email`
      );
      setMessageDialogSeverity("success");
    };

    const hError = (error) => {
      setBlurLoading(false);
      setLoaderMessage("");
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          `Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`
        );
        setMessageDialogSeverity("danger");
      }
    };
    if (alignment === "TEMPORARY BLOCK/UNBLOCK") {
      doAjax(
        `/${destination_ProfitCenter_Mass}/excel/downloadExcelWithDataForTempBlockInMail`,
        "post",
        hSuccess,
        hError,
        downloadPayloadHashTemporaryBlock
      );
    } else {
      doAjax(
        `/${destination_ProfitCenter_Mass}/excel/downloadExcelWithDataInMail`,
        "post",
        hSuccess,
        hError,
        downloadPayloadHash
      );
    }
  };

  const handleEmailDownload = async () => {
    // setLoaderMessage(
    //   "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    // );
    setBlurLoading(true);
    var downloadPayload = selectedMassChangeRowData?.map((x) => {
      return {
        profitCenter: x.profitCenter,
        controllingArea: x.controllingArea,
      };
    });
    const params = new URLSearchParams({
      dtName: "MDG_PC_FIELD_CONFIG",
      version: "v1",
    });

    let hSuccess = (response) => {
      setBlurLoading(false);
      setLoaderMessage("");
      // const href = URL.createObjectURL(response);
      // const link = document.createElement("a");
      // link.href = href;
      // link.setAttribute("download", `Profit Center Mass Create.xlsx`);
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);
      // URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
      setMessageDialogMessage(
        `Download has been started. You will get the Excel file via email`
      );
      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      setBlurLoading(false);
      setLoaderMessage("");
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          `Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`
        );
        setMessageDialogSeverity("danger");
        setBlurLoading(false);
      }
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/excel/downloadExcelInMail?${params.toString()}&rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
      // downloadPayload
    );
  };
  const handleDownloadCreate = async () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    var downloadPayload = selectedMassChangeRowData?.map((x) => {
      return {
        profitCenter: x.profitCenter,
        controllingArea: x.controllingArea,
      };
    });
    const params = new URLSearchParams({
      dtName: "MDG_PC_FIELD_CONFIG",
      version: "v1",
    });

    let hSuccess = (response) => {
      if (response?.size !== 0 || response?.type !== "") {
        // Check if response status is 429
        // if (response?.status === 429) {
        //   handleMessageDialogClickOpen();
        //   setMessageDialogTitle("Error");
        //   setMessageDialogMessage(
        //     `Please try again.`
        //   );
        //   setMessageDialogSeverity("warning");
        //   return;
        // }

        // Proceed with download if response is successful
        const href = URL.createObjectURL(response);
        const link = document.createElement("a");
        link.href = href;
        link.setAttribute("download", `Profit Center Mass Create.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);

        handleMessageDialogClickOpen();
        setMessageDialogTitle("Success");
        setMessageDialogMessage(
          `Profit Center Mass Create.xlsx has been downloaded successfully`
        );
        setMessageDialogSeverity("success");
        setBlurLoading(false);
        setLoaderMessage("");
      } else {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        // setMessageDialogMessage(`${error.message}`);
        setMessageDialogMessage("Please try again.");
        setMessageDialogSeverity("danger");
        setBlurLoading(false);
        setLoaderMessage("");
      }
    };
    let hError = (error) => {
      console.log("error data", error);
      if (error.status === 429) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`Please try again.`);
        setMessageDialogSeverity("warning");
        return;
      }
      setBlurLoading(false);
      setLoaderMessage("");
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          `Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`
        );
        setMessageDialogSeverity("danger");
        setBlurLoading(false);
      }
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/excel/downloadExcel?${params.toString()}&rolePrefix=ETP`,
      "getblobfile",
      hSuccess,
      hError
      // downloadPayload
    );
  };
  const handleClickCreate = (option, index) => {
    // dispatch(setHandleMassMode("Change"));
    if (index !== 0) {
      setSelectedIndexCreate(index);
      setOpenButtonCreate(false);
      if (index === 1) {
        handleCreateSingleWithCopy();
      } else if (index === 2) {
        handleCreateSingleWithoutCopy();
      }
    }
  };
  const handleCreateMultiple = () => {
    setEnableDocumentUpload(true);
    dispatch(setHandleMassMode("Create"));
  };
  const handleToggle = () => {
    setOpenButton((prevOpen) => !prevOpen);
  };
  const handleCloseButton = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    // setOpenButton(false);
    setOpenButton((prevOpen) => !prevOpen);
  };

  const handleToggleChange = () => {
    setOpenButtonChange((prevOpen) => !prevOpen);
  };
  const handleToggleCreate = () => {
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };

  const handleCloseButtonChange = (event) => {
    if (
      anchorRefChange.current &&
      anchorRefChange.current.contains(event.target)
    ) {
      return;
    }
    //setOpenButtonChange(false);
    setOpenButtonChange((prevOpen) => !prevOpen);
  };
  const handleCloseButtonCeateMultiple = (event) => {
    if (
      anchorRefChange.current &&
      anchorRefChange.current.contains(event.target)
    ) {
      return;
    }
    //setOpenButtonChange(false);
    setOpenButton((prevOpen) => !prevOpen);
  };
  const handleClick = (option, index) => {
    if (index !== 0) {
      setSelectedIndex(index);
      setOpenButton(false);
      if (index === 1) {
        handleCreateMultiple();
      } else if (index === 2) {
        // handleDownloadCreate();
        setOpenDownloadDialog(true);
      }
    }
  };
  const handleClickChange = (option, index) => {
    if (index !== 0) {
      setSelectedIndexChange(index);
      setOpenButtonChange(false);
      if (index === 1) {
        handleChangeMultiple();
      } else if (index === 2) {
        setDownloadMultiple(true);
        setOpenDialogIDM(true);
        // setIsLoading(true);
        // setIsCheckboxSelected(false);
        // handleDownloadTemplate();

        //handleDownloadTemplate();
      }
    }
  };
  const handleChangeMultiple = () => {
    setEnableDocumentUpload(true);
    dispatch(setHandleMassMode("Change"));
  };

  const handleSearchAction = (value) => {
    if (!value) {
      setTableData([...rmDataRows]);
      return;
    }
    const selected = rmDataRows.filter((row) => {
      let rowMatched = false;
      let keys = Object.keys(row);

      const displayedKeys = allColumns.map((col) => col.field);
      for (let k = 0; k < keys.length; k++) {
        if (displayedKeys.includes(keys[k])) {
          rowMatched = !row[keys[k]]
            ? false
            : row?.[keys?.[k]] &&
              row?.[keys?.[k]]
                .toString()
                .toLowerCase()
                ?.indexOf(value?.toLowerCase()) != -1;

          if (rowMatched) break;
        }
      }
      return rowMatched;
    });

    setTableData([...selected]);
    setRmDataRows([...selected]);
    setCount(rmDataRows?.length);
  };

  return (
    <>
      {/* {isLoading === true ? (
        <LoadingComponent />
      ) : ( */}
      <div ref={ref_elementForExport}>
        <ReusableDialog
          dialogState={openMessageDialog}
          openReusableDialog={handleMessageDialogClickOpen}
          closeReusableDialog={handleMessageDialogClose}
          dialogTitle={messageDialogTitle}
          dialogMessage={messageDialogMessage}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          // handleExtraButton={handleMessageDialogNavigate}
          dialogSeverity={messageDialogSeverity}
        />
        <ReusableDialog
          dialogState={openSearchDialog}
          openReusableDialog={handleSearchDialogClickOpen}
          closeReusableDialog={handleSearchDialogClose}
          dialogTitle={searchDialogTitle}
          dialogMessage={searchDialogMessage}
          handleDialogConfirm={handleSearchDialogClose}
          dialogSeverity={"danger"}
          showCancelButton={false}
          dialogOkText={"OK"}
        />
        <Dialog
          open={showTableInDialog}
          onClose={() => setShowTableInDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle
            sx={{ bgcolor: "#FFDAB9", color: "warning.contrastText" }}
          >
            <Typography
              variant="h6"
              gutterBottom
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <span>
                <WarningIcon sx={{ mr: 1 }} /> Duplicate Requests Alert
              </span>

              <Tooltip title="Export Table">
                <IconButton
                  sx={iconButton_SpacingSmall}
                  onClick={
                    functions_ExportAsExcelErrorProcessNotCompleate.convertJsonToExcel
                  }
                >
                  <ReusableIcon iconName={"IosShare"} />
                </IconButton>
              </Tooltip>
            </Typography>
          </DialogTitle>
          <DialogContent>
            <div style={{ marginTop: "20px" }}>
              <ReusableTable
                height={400}
                rows={duplicateFieldsData}
                columns={duplicateFieldsColumns}
                pageSize={duplicateFieldsData.length}
                getRowIdValue={"id"}
                hideFooter={true}
                checkboxSelection={false}
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
              />
            </div>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={() => {
                setShowTableInDialog(false);
                setOpenDialogIDM(false);
              }}
            >
              OK
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog open={openDownloadDialog} onClose={handleDownloadDialogClose}>
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              // borderBottom: "1px solid grey",
              display: "flex",
            }}
          >
            <Typography
              variant="h6"
              gutterBottom
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              Select Download Option
            </Typography>
          </DialogTitle>
          <DialogContent>
            <FormControl>
              <RadioGroup
                row
                aria-labelledby="demo-row-radio-buttons-group-label"
                name="row-radio-buttons-group"
                value={downloadType}
                onChange={handleDownloadTypeChange}
              >
                <NoMaxWidthTooltip
                  arrow
                  placement="bottom"
                  title={
                    <span
                      style={{
                        whiteSpace: "nowrap", // Prevents line break
                        fontSize: "12px",
                        // maxWidth: "400px", // Optional width constraint
                        overflow: "hidden",
                        textOverflow: "ellipsis", // Adds ellipsis if overflow
                      }}
                    >
                      Here Excel will be downloaded
                    </span>
                  }
                  // placement="right"
                >
                  <FormControlLabel
                    value="systemGenerated"
                    control={<Radio />}
                    label="System-Generated"
                  />
                </NoMaxWidthTooltip>

                <NoMaxWidthTooltip
                  arrow
                  placement="bottom"
                  title={
                    <span
                      style={{
                        whiteSpace: "nowrap", // Prevents line break
                        fontSize: "12px",
                        // maxWidth: "400px", // Optional width constraint
                        overflow: "hidden",
                        textOverflow: "ellipsis", // Adds ellipsis if overflow
                      }}
                    >
                      Here Excel will be sent to your email
                    </span>
                  }
                  // placement="right"
                >
                  <FormControlLabel
                    value="mailGenerated"
                    control={<Radio />}
                    label="Mail-Generated"
                  />
                </NoMaxWidthTooltip>
              </RadioGroup>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button variant="contained" onClick={onDownloadTypeChange}>
              OK
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
          open={openDownloadChangeDialog}
          onClose={handleDownloadChangeDialogClose}
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              // borderBottom: "1px solid grey",
              display: "flex",
            }}
          >
            <Typography
              variant="h6"
              gutterBottom
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              Select Download Option
            </Typography>
          </DialogTitle>
          <DialogContent>
            <FormControl>
              <RadioGroup
                row
                aria-labelledby="demo-row-radio-buttons-group-label"
                name="row-radio-buttons-group"
                value={downloadType}
                onChange={handleMultipleDownloadTypeChange}
              >
                <NoMaxWidthTooltip
                  arrow
                  placement="bottom"
                  title={
                    <span
                      style={{
                        whiteSpace: "nowrap", // Prevents line break
                        fontSize: "12px",
                        // maxWidth: "400px", // Optional width constraint
                        overflow: "hidden",
                        textOverflow: "ellipsis", // Adds ellipsis if overflow
                      }}
                    >
                      Here Excel will be downloaded
                    </span>
                  }
                  // placement="right"
                >
                  <FormControlLabel
                    value="systemGenerated"
                    control={<Radio />}
                    label="System-Generated"
                  />
                </NoMaxWidthTooltip>

                <NoMaxWidthTooltip
                  arrow
                  placement="bottom"
                  title={
                    <span
                      style={{
                        whiteSpace: "nowrap", // Prevents line break
                        fontSize: "12px",
                        // maxWidth: "400px", // Optional width constraint
                        overflow: "hidden",
                        textOverflow: "ellipsis", // Adds ellipsis if overflow
                      }}
                    >
                      Here Excel will be sent to your email
                    </span>
                  }
                  // placement="right"
                >
                  <FormControlLabel
                    value="mailGenerated"
                    control={<Radio />}
                    label="Mail-Generated"
                  />
                </NoMaxWidthTooltip>
              </RadioGroup>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button variant="contained" onClick={onMultipleDownloadTypeChange}>
              OK
            </Button>
          </DialogActions>
        </Dialog>

        <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
          <Stack spacing={1}>
            {/* Information */}
            <Grid container sx={outermostContainer_Information}>
              <Grid item md={5} sx={outerContainer_Information}>
                <Typography variant="h3">
                  <strong>{t("Profit Center")}</strong>
                </Typography>
                <Typography variant="body2" color="#777">
                  {t("This view displays the list of Profit Centers")}
                </Typography>
              </Grid>
            </Grid>
            <Grid container sx={container_filter}>
              <Grid item md={12}>
                <StyledAccordion defaultExpanded={true}>
                  <StyledAccordionSummary
                    expandIcon={
                      <ExpandMoreIcon
                        sx={{ fontSize: "1.25rem", color: colors.primary.main }}
                      />
                    }
                    aria-controls="panel1a-content"
                    id="panel1a-header"
                    // sx={{
                    //   minHeight: "2rem !important",
                    //   margin: "0px !important",
                    // }}
                  >
                    <FilterListIcon
                      sx={{
                        fontSize: "1.25rem",
                        marginRight: 1,
                        color: colors.primary.main,
                      }}
                    />
                    <Typography
                      sx={{
                        fontSize: "0.875rem",
                        fontWeight: 600,
                        color: colors.primary.dark,
                      }}
                    >
                      {t("Search Profit Center")}
                    </Typography>
                  </StyledAccordionSummary>
                  <AccordionDetails sx={{ padding: "1rem 1rem 0.5rem" }}>
                    <Grid
                      container
                      rowSpacing={1}
                      spacing={2}
                      justifyContent="space-between"
                      alignItems="center"
                      // sx={{ marginBottom: "0.5rem" }}
                    >
                      <Grid
                        container
                        spacing={1}
                        sx={{ padding: "0rem 1rem 0.5rem" }}
                      >
                        {searchParameters?.filter(item => item.MDG_MAT_VISIBILITY !== "Hidden")
                          .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
                          .map((item, index) => {
                            return (
                              <React.Fragment key={index}>
                                {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.CONTROLINGAREA &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                  </LabelTypography>
                                  <FormControl size="small" fullWidth>
                                    <Autocomplete
                                      sx={{ height: "31px" }}
                                      fullWidth
                                      size="small"
                                      value={pcSearchForm?.controllingArea}
                                      onChange={handleControllingArea}
                                      noOptionsText={
                                        isDropDownLoading ? (
                                          <Box
                                            sx={{
                                              display: "flex",
                                              justifyContent: "center",
                                              mt: 1,
                                              zIndex: 9999,
                                              top: "10px",
                                            }}
                                          >
                                            <CircularProgress size={20} />
                                          </Box>
                                        ) : (
                                          "No Data Available"
                                        )
                                      }
                                      options={
                                        dropDownData?.ControllingAreaForSearchET ?? []
                                      }
                                      getOptionLabel={(option) => {
                                        if (option?.code)
                                          return (
                                            `${option?.code} - ${option?.desc}` ?? ""
                                          );
                                        else return "";
                                      }}
                                      renderOption={(props, option) => (
                                        <li {...props}>
                                          <Typography style={{ fontSize: 12 }}>
                                            {option?.desc ? (
                                              <>
                                                <strong>{option.code}</strong> -{" "}
                                                {option.desc}
                                              </>
                                            ) : (
                                              <strong>{option.code}</strong>
                                            )}
                                          </Typography>
                                        </li>
                                      )}
                                      renderInput={(params) => (
                                        <TextField
                                          sx={{ fontSize: "12px !important" }}
                                          {...params}
                                          variant="outlined"
                                          placeholder="Select Controlling Area"
                                        />
                                      )}
                                    />
                                  </FormControl>
                                </Grid>}
                                {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.COMPANYCODE &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                  </LabelTypography>
                                  <FormControl
                                    fullWidth
                                    size="small"
                                    sx={{ paddingBottom: "0.7rem" }}
                                  >
                                    <Autocomplete
                                      size="small"
                                      multiple
                                      limitTags={1}
                                      id="checkboxes-tags-demo"
                                      options={[
                                        { code: "Select All", desc: "Select All" },
                                        ...(dropDownData?.CompanyCodeForSearch ?? []),
                                      ]}
                                      disableCloseOnSelect
                                      noOptionsText={
                                        isDropDownLoading ? (
                                          <Box
                                            sx={{
                                              display: "flex",
                                              justifyContent: "center",
                                              mt: 1,
                                              zIndex: 9999,
                                              top: "10px",
                                            }}
                                          >
                                            <CircularProgress size={20} />
                                          </Box>
                                        ) : (
                                          "No Data Available"
                                        )
                                      }
                                      getOptionLabel={(option) => {
                                        if (option?.code)
                                          return `${option?.code}` ?? "";
                                        else return "";
                                      }}
                                      onChange={(e, value, reason) => {
                                        if (reason === "clear" || value?.length === 0) {
                                          setselectedComanyCode([]);
                                          setselectedPresetComanyCode([]);
                                          return;
                                        }

                                        if (
                                          value.length > 0 &&
                                          value[value.length - 1]?.code === "Select All"
                                        ) {
                                          handleSelectAllCompanyCodes();
                                        } else {
                                          setselectedComanyCode(value);

                                          //   setselectedComanyCode(prevCompNames => {
                                          //     const isValuePresent = prevCompNames.some(item => item?.code === value[value.length - 1]?.code);

                                          //     if (isValuePresent) {
                                          //         return prevCompNames.filter(item => item?.code !== value[value.length - 1]?.code);
                                          //     } else {
                                          //         return [...prevCompNames, value[value.length - 1]];
                                          //     }
                                          // });
                                        }
                                      }}
                                      renderTags={(value, getTagProps) =>
                                        value.length > 0 ? (
                                          <>
                                            <Chip
                                              label={value[0].code}
                                              {...getTagProps({ index: 0 })}
                                              sx={{
                                                height: 20,
                                                fontSize: "0.75rem",
                                                ".MuiChip-label": { padding: "0 6px" },
                                              }}
                                            />
                                            {value.length > 1 && (
                                              <Chip
                                                label={`+${value.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                            )}
                                          </>
                                        ) : null
                                      }
                                      inputValue={searchInput}
                                      onInputChange={handleInputCompanyChange}
                                      value={memoizedCompCodeValue}
                                      renderInput={(params) => (
                                        <TextField
                                          sx={{
                                            fontSize: "12px !important",
                                            "& .MuiOutlinedInput-root": {
                                              height: 35,
                                            },
                                            "& .MuiInputBase-input": {
                                              padding: "10px 14px",
                                            },
                                          }}
                                          {...params}
                                          variant="outlined"
                                          placeholder="Select Company code"
                                        />
                                      )}
                                      renderOption={(props, option, { selected }) => (
                                        <li {...props}>
                                          <FormGroup>
                                            <FormControlLabel
                                              control={
                                                <Checkbox
                                                  checked={
                                                    isCompanyCodeSelected(option) ||
                                                    (option?.code === "Select All" &&
                                                      selectedComanyCode?.length ===
                                                      dropDownData
                                                        ?.CompanyCodeForSearch
                                                        ?.length)
                                                  }
                                                />
                                              }
                                              // label={`${option?.code} - ${option?.desc}`}
                                              label={
                                                <>
                                                  <strong>{option.code}</strong> -{" "}
                                                  {option.desc}
                                                </>
                                              }
                                            />
                                          </FormGroup>
                                        </li>
                                      )}
                                    />
                                  </FormControl>
                                </Grid>}
                                {/* <Grid item md={2}>
                          <LabelTypography sx={{ fontSize: '12px' }}>Profit Center</LabelTypography>
                          <FormControl size="small" fullWidth>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <TextField
                                 sx={{ 
                                  height: "31px",
                                  fontSize: '12px !important',
                                  flex: 1,
                                  '& .MuiOutlinedInput-root': {
                                    height: "100%", // Ensures TextField matches the height of Autocomplete
                                    borderRadius: '4px',
                                    '& fieldset': {
                                      border: '1px solid #ccc', // Adjust border to match the Autocomplete
                                    },
                                  },
                                  '& .MuiInputBase-input': {
                                    padding: '0 8px', // Adjust padding to fit with Autocomplete
                                    boxSizing: 'border-box', // Ensure padding does not affect height
                                  },
                                }}
                                variant="outlined"
                                placeholder="Search Profit Center"
                                size="small"
                                value={pcSearchForm?.profitCenterString ?? ""}
                                onChange={handleInputChange}
                              />
                            </Box>
                          </FormControl>
                        </Grid> */}
                        {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.PROFITCENTER &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                  </LabelTypography>
                                  <FormControl fullWidth size="small">
                                    <Autocomplete
                                      sx={{ height: "31px" }}
                                      fullWidth
                                      multiple
                                      disableCloseOnSelect
                                      size="small"
                                      value={memoizedPCValue}
                                      noOptionsText={
                                        isDropDownLoading ? (
                                          <Box
                                            sx={{
                                              display: "flex",
                                              justifyContent: "center",
                                              mt: 1,
                                              zIndex: 9999,
                                              top: "10px",
                                            }}
                                          >
                                            <CircularProgress size={20} />
                                          </Box>
                                        ) : (
                                          "No Data Available"
                                        )
                                      }
                                      onChange={(e, value, reason) => {
                                        if (reason === "clear" || value?.length === 0) {
                                          setselectedProfitCenter([]);
                                          setselectedPresetProfitCenter([]);
                                          return;
                                        }

                                        if (
                                          value.length > 0 &&
                                          value[value.length - 1]?.code === "Select All"
                                        ) {
                                          handleSelectAllProfitCenter();
                                        } else {
                                          setselectedProfitCenter(value);
                                        }
                                      }}
                                      renderTags={(value, getTagProps) =>
                                        value.length > 0 ? (
                                          <>
                                            <Chip
                                              label={value[0].code}
                                              {...getTagProps({ index: 0 })}
                                              sx={{
                                                height: 20,
                                                fontSize: "0.75rem",
                                                ".MuiChip-label": { padding: "0 6px" },
                                              }}
                                            />
                                            {value.length > 1 && (
                                              <Chip
                                                label={`+${value.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                            )}
                                          </>
                                        ) : null
                                      }
                                      limitTags={1}
                                      options={
                                        dropDownData?.PCSearchDataET?.length
                                          ? [
                                            { code: "Select All" },
                                            ...dropDownData?.PCSearchDataET,
                                          ]
                                          : dropDownData?.PCSearchDataET ?? []
                                      }
                                      getOptionLabel={(option) => {
                                        if (option?.code) return option?.code ?? "";
                                        else return "";
                                      }}
                                      renderOption={(props, option, { selected }) => (
                                        <li {...props}>
                                          <FormGroup>
                                            <FormControlLabel
                                              control={
                                                <Checkbox
                                                  checked={
                                                    isProfitCenterSelected(option) ||
                                                    (option?.code === "Select All" &&
                                                      selectedProfitCenter?.length ===
                                                      dropDownData?.PCSearchDataET
                                                        ?.length)
                                                  }
                                                />
                                              }
                                              label={
                                                <>
                                                  <strong>{option.code}</strong>
                                                </>
                                              }
                                            />
                                          </FormGroup>
                                        </li>
                                      )}
                                      renderInput={(params) => (
                                        <Tooltip
                                          title={
                                            pcInputValue.length < 4
                                              ? "Enter at least 4 characters"
                                              : ""
                                          }
                                          arrow
                                          placement="top"
                                          disableHoverListener={
                                            pcInputValue.length >= 4
                                          }
                                        >
                                          <TextField
                                            sx={{
                                              fontSize: "12px !important",
                                              "& .MuiOutlinedInput-root": {
                                                height: 35,
                                              },
                                              "& .MuiInputBase-input": {
                                                padding: "10px 14px",
                                              },
                                            }}
                                            {...params}
                                            variant="outlined"
                                            // helperText={
                                            //   pcInputValue.length < 4
                                            //     ? "Enter at least 4 characters"
                                            //     : ""
                                            // }
                                            placeholder="Select Profit Center"
                                            onChange={(e) => {
                                              handlePCInputChange(e);
                                            }}
                                          />
                                        </Tooltip>
                                      )}
                                    />
                                  </FormControl>
                                </Grid>}
                                {/* <Grid item md={2}>
                          <LabelTypography sx={font_Small}>Long Text</LabelTypography>
                          <FormControl size="small" fullWidth>
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              fullWidth
                              size="small"
                              value={pcSearchForm?.longText}
                              onChange={handleLongText}
                              placeholder="Enter Long Text"
                            />
                          </FormControl>
                        </Grid> */}
                        {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.LONGTEXT &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                  </LabelTypography>
                                  <FormControl size="small" fullWidth>
                                    <Autocomplete
                                      sx={{ height: "31px" }}
                                      fullWidth
                                      size="small"
                                      multiple
                                      disableCloseOnSelect
                                      noOptionsText={
                                        isDropDownLoading ? (
                                          <Box
                                            sx={{
                                              display: "flex",
                                              justifyContent: "center",
                                              mt: 1,
                                              zIndex: 9999,
                                              top: "10px",
                                            }}
                                          >
                                            <CircularProgress size={20} />
                                          </Box>
                                        ) : (
                                          "No Data Available"
                                        )
                                      }
                                      onChange={(e, value, reason) => {
                                        if (reason === "clear" || value?.length === 0) {
                                          setselectedLongText([]);
                                          setselectedPresetLongText([]);
                                          return;
                                        }

                                        if (
                                          value.length > 0 &&
                                          value[value.length - 1]?.code === "Select All"
                                        ) {
                                          handleSelectAllLongTexts();
                                        } else {
                                          setselectedLongText(value);
                                        }
                                      }}
                                      renderTags={(value, getTagProps) =>
                                        value.length > 0 ? (
                                          <>
                                            <Chip
                                              label={value[0].code}
                                              {...getTagProps({ index: 0 })}
                                              sx={{
                                                height: 20,
                                                fontSize: "0.75rem",
                                                ".MuiChip-label": { padding: "0 6px" },
                                              }}
                                            />
                                            {value.length > 1 && (
                                              <Chip
                                                label={`+${value.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                            )}
                                          </>
                                        ) : null
                                      }
                                      limitTags={1}
                                      value={memoizedLDValue}
                                      options={
                                        dropDownData?.LongTextSearchET?.length
                                          ? [
                                            { code: "Select All" },
                                            ...dropDownData?.LongTextSearchET,
                                          ]
                                          : dropDownData?.LongTextSearchET ?? []
                                      }
                                      //noOptionsText="Enter a query to search"
                                      getOptionLabel={(option) => {
                                        if (option?.code) return option?.code ?? "";
                                        else return "";
                                      }}
                                      renderOption={(props, option, { selected }) => (
                                        <li {...props}>
                                          <FormGroup>
                                            <FormControlLabel
                                              control={
                                                <Checkbox
                                                  checked={
                                                    isLongTextSelected(option) ||
                                                    (option?.code === "Select All" &&
                                                      selectedLongText?.length ===
                                                      dropDownData?.LongTextSearchET
                                                        ?.length)
                                                  }
                                                />
                                              }
                                              label={
                                                <>
                                                  <strong>{option.code}</strong>
                                                </>
                                              }
                                            />
                                          </FormGroup>
                                        </li>
                                      )}
                                      renderInput={(params) => (
                                        <Tooltip
                                          title={
                                            pcLongDesc.length < 4
                                              ? "Enter at least 4 characters"
                                              : ""
                                          }
                                          arrow
                                          placement="top"
                                          disableHoverListener={pcLongDesc.length >= 4}
                                        >
                                          <TextField
                                            sx={{
                                              fontSize: "12px !important",
                                              "& .MuiOutlinedInput-root": {
                                                height: 35, // Set a fixed height here
                                              },
                                              "& .MuiInputBase-input": {
                                                padding: "10px 14px",
                                              },
                                            }}
                                            {...params}
                                            variant="outlined"
                                            // helperText={inputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                            //error={inputValue.length < 4}
                                            placeholder="Search Long Description"
                                            onChange={(e) => {
                                              handleLongTextInputChange(e);
                                            }}
                                          />
                                        </Tooltip>
                                      )}
                                    />
                                  </FormControl>
                                </Grid>}
                                {/* <Grid item md={2}>
                          <LabelTypography sx={font_Small}>
                            Person Responsible
                          </LabelTypography>
                          <FormControl size="small" fullWidth>
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              fullWidth
                              size="small"
                              value={pcSearchForm?.personResponsible}
                              onChange={handlePersonResponsible}
                              placeholder="Enter Person Responsible"
                            />
                          </FormControl>
                        </Grid> */}
                        {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.PERSONRES &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                  </LabelTypography>
                                  <FormControl size="small" fullWidth>
                                    <Autocomplete
                                      sx={{ height: "31px" }}
                                      fullWidth
                                      size="small"
                                      multiple
                                      disableCloseOnSelect
                                      value={memoizedPRValue}
                                      noOptionsText={
                                        isDropDownLoading ? (
                                          <Box
                                            sx={{
                                              display: "flex",
                                              justifyContent: "center",
                                              mt: 1,
                                              zIndex: 9999,
                                              top: "10px",
                                            }}
                                          >
                                            <CircularProgress size={20} />
                                          </Box>
                                        ) : (
                                          "No Data Available"
                                        )
                                      }
                                      onChange={(e, value, reason) => {
                                        if (reason === "clear" || value?.length === 0) {
                                          setselectedPersonResponsible([]);
                                          setselectedPresetPersonResponsible([]);
                                          return;
                                        }

                                        if (
                                          value.length > 0 &&
                                          value[value.length - 1]?.code === "Select All"
                                        ) {
                                          handleSelectAllPersonResponsible();
                                        } else {
                                          setselectedPersonResponsible(value);
                                        }
                                      }}
                                      renderTags={(value, getTagProps) =>
                                        value.length > 0 ? (
                                          <>
                                            <Chip
                                              label={value[0].code}
                                              {...getTagProps({ index: 0 })}
                                              sx={{
                                                height: 20,
                                                fontSize: "0.75rem",
                                                ".MuiChip-label": { padding: "0 6px" },
                                              }}
                                            />
                                            {value.length > 1 && (
                                              <Chip
                                                label={`+${value.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                            )}
                                          </>
                                        ) : null
                                      }
                                      limitTags={1}
                                      options={
                                        dropDownData?.PersonResponsibleSearchET?.length
                                          ? [
                                            { code: "Select All" },
                                            ...dropDownData?.PersonResponsibleSearchET,
                                          ]
                                          : dropDownData?.PersonResponsibleSearchET ??
                                          []
                                      }
                                      //options={[{ code: 'Select All'}, ...(dropDownData?.PersonResponsibleSearchET ?? [])]}
                                      getOptionLabel={(option) => {
                                        if (option?.code) return option?.code ?? "";
                                        else return "";
                                      }}
                                      renderOption={(props, option, { selected }) => (
                                        <li {...props}>
                                          <FormGroup>
                                            <FormControlLabel
                                              control={
                                                <Checkbox
                                                  checked={
                                                    isPersonResponsibleSelected(
                                                      option
                                                    ) ||
                                                    (option?.code === "Select All" &&
                                                      selectedPersonResponsible?.length ===
                                                      dropDownData
                                                        ?.PersonResponsibleSearchET
                                                        ?.length)
                                                  }
                                                />
                                              }
                                              label={
                                                <>
                                                  <strong>{option.code}</strong>
                                                </>
                                              }
                                            />
                                          </FormGroup>
                                        </li>
                                      )}
                                      renderInput={(params) => (
                                        <Tooltip
                                          title={
                                            pcPerson.length < 4
                                              ? "Enter at least 4 characters"
                                              : ""
                                          }
                                          arrow
                                          placement="top"
                                          disableHoverListener={pcPerson.length >= 4}
                                        >
                                          <TextField
                                            sx={{
                                              fontSize: "12px !important",
                                              "& .MuiOutlinedInput-root": {
                                                height: 35, // Set a fixed height here
                                              },
                                              "& .MuiInputBase-input": {
                                                padding: "10px 14px",
                                              },
                                            }}
                                            {...params}
                                            variant="outlined"
                                            placeholder="Search Person Responsible"
                                            onChange={(e) => {
                                              handlePersonResponsibleInputChange(e);
                                            }}
                                          />
                                        </Tooltip>
                                      )}
                                    />
                                  </FormControl>
                                </Grid>}
                                {/* <Grid item md={2}>
                          <LabelTypography sx={font_Small}>City</LabelTypography>
                          <FormControl size="small" fullWidth>
                            <TextField
                              sx={{ fontSize: "12px !important" }}
                              fullWidth
                              size="small"
                              value={pcSearchForm?.city}
                              onChange={handleCity}
                              placeholder="Enter City"
                            />
                          </FormControl>
                        </Grid> */}
                        {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.CITY &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                  </LabelTypography>
                                  <FormControl size="small" fullWidth>
                                    <Autocomplete
                                      sx={{ height: "31px" }}
                                      fullWidth
                                      size="small"
                                      multiple
                                      disableCloseOnSelect
                                      value={memoizedCityValue}
                                      noOptionsText={
                                        isDropDownLoading ? (
                                          <Box
                                            sx={{
                                              display: "flex",
                                              justifyContent: "center",
                                              mt: 1,
                                              zIndex: 9999,
                                              top: "10px",
                                            }}
                                          >
                                            <CircularProgress size={20} />
                                          </Box>
                                        ) : (
                                          "No Data Available"
                                        )
                                      }
                                      onChange={(e, value, reason) => {
                                        if (reason === "clear" || value?.length === 0) {
                                          setselectedCity([]);
                                          setselectedPresetCity([]);
                                          return;
                                        }

                                        if (
                                          value.length > 0 &&
                                          value[value.length - 1]?.code === "Select All"
                                        ) {
                                          handleSelectAllCity();
                                        } else {
                                          setselectedCity(value);
                                        }
                                      }}
                                      renderTags={(value, getTagProps) =>
                                        value.length > 0 ? (
                                          <>
                                            <Chip
                                              label={value[0].code}
                                              {...getTagProps({ index: 0 })}
                                              sx={{
                                                height: 20,
                                                fontSize: "0.75rem",
                                                ".MuiChip-label": { padding: "0 6px" },
                                              }}
                                            />
                                            {value.length > 1 && (
                                              <Chip
                                                label={`+${value.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                            )}
                                          </>
                                        ) : null
                                      }
                                      limitTags={1}
                                      options={
                                        dropDownData?.CitySearchET?.length
                                          ? [
                                            { code: "Select All" },
                                            ...dropDownData?.CitySearchET,
                                          ]
                                          : dropDownData?.CitySearchET ?? []
                                      }
                                      getOptionLabel={(option) => {
                                        if (option?.code) return option?.code ?? "";
                                        else return "";
                                      }}
                                      renderOption={(props, option, { selected }) => (
                                        <li {...props}>
                                          <FormGroup>
                                            <FormControlLabel
                                              control={
                                                <Checkbox
                                                  checked={
                                                    isCitySelected(option) ||
                                                    (option?.code === "Select All" &&
                                                      selectedCity?.length ===
                                                      dropDownData?.CitySearchET
                                                        ?.length)
                                                  }
                                                />
                                              }
                                              label={
                                                <>
                                                  <strong>{option.code}</strong>
                                                </>
                                              }
                                            />
                                          </FormGroup>
                                        </li>
                                      )}
                                      renderInput={(params) => (
                                        <Tooltip
                                          title={
                                            pcCity.length < 4
                                              ? "Enter at least 4 characters"
                                              : ""
                                          }
                                          arrow
                                          placement="top"
                                          disableHoverListener={pcCity.length >= 4}
                                        >
                                          <TextField
                                            sx={{
                                              fontSize: "12px !important",
                                              "& .MuiOutlinedInput-root": {
                                                height: 35,
                                              },
                                              "& .MuiInputBase-input": {
                                                padding: "10px 14px",
                                              },
                                            }}
                                            {...params}
                                            variant="outlined"
                                            placeholder="Search City"
                                            onChange={(e) => {
                                              handleCityInputChange(e);
                                            }}
                                          />
                                        </Tooltip>
                                      )}
                                    />
                                  </FormControl>
                                </Grid>}
                                {/* <Grid item md={2}>
                            <LabelTypography sx={font_Small}>Country/Region</LabelTypography>
                            <FormControl size="small" fullWidth>
                              <Autocomplete
                                sx={{ height: "31px" }}
                                fullWidth
                                size="small"
                                value={pcSearchForm?.country}
                                onChange={handleCountry}
                                options={dropDownData?.country ?? []}
                                getOptionLabel={(option) => {
                                  if (option?.code)
                                    return (
                                      `${option?.code}-${option?.desc}` ?? ""
                                    );
                                  else return "";
                                }}
                                renderOption={(props, option) => (
                                  <li {...props}>
                                    <LabelTypography style={{ fontSize: 12 }}>
                                      {`${option?.code}-${option?.desc}`}
                                    </LabelTypography>
                                  </li>
                                )}
                                renderInput={(params) => (
                                  <TextField
                                    sx={{ fontSize: "12px !important" }}
                                    {...params}
                                    variant="outlined"
                                    placeholder="Select Country"
                                  />
                                )}
                              />
                            </FormControl>
                          </Grid> */}
                          {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.REGIONPC &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                  </LabelTypography>
                                  <FormControl size="small" fullWidth>
                                    <Autocomplete
                                      sx={{ height: "31px" }}
                                      fullWidth
                                      size="small"
                                      multiple
                                      disableCloseOnSelect
                                      value={
                                        selectedRegion.length > 0
                                          ? selectedRegion
                                          : selectedPresetRegion.length > 0
                                            ? selectedPresetRegion
                                            : []
                                      }
                                      noOptionsText={
                                        isDropDownLoading ? (
                                          <Box
                                            sx={{
                                              display: "flex",
                                              justifyContent: "center",
                                              mt: 1,
                                              zIndex: 9999,
                                              top: "10px",
                                            }}
                                          >
                                            <CircularProgress size={20} />
                                          </Box>
                                        ) : (
                                          "No Data Available"
                                        )
                                      }
                                      onChange={(e, value, reason) => {
                                        if (reason === "clear" || value?.length === 0) {
                                          setselectedRegion([]);
                                          setselectedPresetRegion([]);
                                          return;
                                        }

                                        if (
                                          value.length > 0 &&
                                          value[value.length - 1]?.code === "Select All"
                                        ) {
                                          handleSelectAllRegion();
                                        } else {
                                          setselectedRegion(value);
                                        }
                                      }}
                                      renderTags={(value, getTagProps) =>
                                        value.length > 0 ? (
                                          <>
                                            <Chip
                                              label={value[0].code}
                                              {...getTagProps({ index: 0 })}
                                              sx={{
                                                height: 20,
                                                fontSize: "0.75rem",
                                                ".MuiChip-label": { padding: "0 6px" },
                                              }}
                                            />
                                            {value.length > 1 && (
                                              <Chip
                                                label={`+${value.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                            )}
                                          </>
                                        ) : null
                                      }
                                      limitTags={1}
                                      options={
                                        dropDownData?.region?.length
                                          ? [
                                            { code: "Select All" },
                                            ...dropDownData?.region,
                                          ]
                                          : dropDownData?.region ?? []
                                      }
                                      getOptionLabel={(option) => {
                                        if (option?.code)
                                          return `${option?.code}` ?? "";
                                        else return "";
                                      }}
                                      renderOption={(props, option, { selected }) => (
                                        <li {...props}>
                                          <FormGroup>
                                            <FormControlLabel
                                              control={
                                                <Checkbox
                                                  checked={
                                                    isRegionSelected(option) ||
                                                    (option?.code === "Select All" &&
                                                      selectedRegion?.length ===
                                                      dropDownData?.region?.length)
                                                  }
                                                />
                                              }
                                              label={
                                                <>
                                                  <strong>{option.code}</strong>
                                                </>
                                              }
                                            />
                                          </FormGroup>
                                        </li>
                                      )}
                                      renderInput={(params) => (
                                        <TextField
                                          sx={{
                                            fontSize: "12px !important",
                                            "& .MuiOutlinedInput-root": {
                                              height: 35,
                                            },
                                            "& .MuiInputBase-input": {
                                              padding: "10px 14px",
                                            },
                                          }}
                                          {...params}
                                          variant="outlined"
                                          placeholder="Select Region"
                                        // onChange={(e) => {
                                        //   handleRegionInputChange(e);
                                        // }}
                                        />
                                      )}
                                    />
                                  </FormControl>
                                </Grid>}

                                {/* <Grid item md={2}>
                            <LabelTypography sx={font_Small}>
                              Controlling Area
                            </LabelTypography>
                            <FormControl size="small" fullWidth>
                              <Autocomplete
                                sx={{ height: "31px" }}
                                fullWidth
                                size="small"
                                value={pcSearchForm?.controllingArea}
                                onChange={handleControllingAreaChange}
                                options={dropDownData?.ControllingArea ?? []}
                                getOptionLabel={(option) => {
                                  if (option?.code)
                                    return (
                                      `${option?.code}-${option?.desc}` ?? ""
                                    );
                                  else return "";
                                }}
                                renderOption={(props, option) => (
                                  <li {...props}>
                                    <LabelTypography style={{ fontSize: 12 }}>
                                      {`${option?.code}-${option?.desc}`}
                                    </LabelTypography>
                                  </li>
                                )}
                                renderInput={(params) => (
                                  <TextField
                                    sx={{ fontSize: "12px !important" }}
                                    {...params}
                                    variant="outlined"
                                    placeholder="Select Controlling Area"
                                  />
                                )}
                              />
                            </FormControl>
                          </Grid> */}
                                {/* <Grid item md={2}>
                            <LabelTypography sx={font_Small}>Segment</LabelTypography>
                            <FormControl fullWidth size="small">
                              <Autocomplete
                                sx={{ height: "31px" }}
                                fullWidth
                                size="small"
                                onChange={handleSegment}
                                // onChange={(e) => handleMatTypeChange(e)}
                                options={dropDownData?.Segment ?? []}
                                getOptionLabel={(option) => {
                                  if (option?.code)
                                    return (
                                      `${option?.code} - ${option?.desc}` ?? ""
                                    );
                                  else return "";
                                }}
                                value={pcSearchForm?.segment}
                                renderOption={(props, option) => (
                                  <li {...props}>
                                    <LabelTypography style={{ fontSize: 12 }}>
                                      {`${option?.code} - ${option?.desc}`}
                                    </LabelTypography>
                                  </li>
                                )}
                                renderInput={(params) => (
                                  <TextField
                                    sx={{ fontSize: "12px !important" }}
                                    {...params}
                                    variant="outlined"
                                    placeholder="Select Segment"
                                  />
                                )}
                              />
                            </FormControl>
                          </Grid> */}
                                {/* <Grid item md={2}>
                            <LabelTypography sx={font_Small}>
                              Profit Center Group
                            </LabelTypography>
                            <FormControl fullWidth size="small">
                              <Autocomplete
                                sx={{ height: "31px" }}
                                fullWidth
                                size="small"
                                onChange={handleGroup}
                                value={pcSearchForm?.profitCenterGroup}
                                options={
                                  dropDownData?.ProfitCtrGroupSearch ?? []
                                }
                                getOptionLabel={(option) => {
                                  if (option?.code)
                                    return (
                                      `${option?.code} - ${option?.desc}` ?? ""
                                    );
                                  else return "";
                                }}
                                renderOption={(props, option) => (
                                  <li {...props}>
                                    <LabelTypography style={{ fontSize: 12 }}>
                                      {`${option?.code} - ${option?.desc}`}
                                    </LabelTypography>
                                  </li>
                                )}
                                renderInput={(params) => (
                                  <TextField
                                    sx={{ fontSize: "12px !important" }}
                                    {...params}
                                    variant="outlined"
                                    placeholder="Select Profit Center Group"
                                  />
                                )}
                              />
                            </FormControl>
                          </Grid> */}
                          {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.BLOCKINGSAT &&
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                  </LabelTypography>
                                  <FormControl fullWidth size="small">
                                    <Select
                                      placeholder={"Select Blocking Status"}
                                      sx={{ height: "36px" }}
                                      size="small"
                                      value={pcSearchForm?.blockingStatus}
                                      name="blockingStatus"
                                      onChange={(e) => handleBlockingStatus(e)}
                                      displayEmpty={true}
                                      // input={<OutlinedInput label="Tag" />}
                                      // renderValue={(selected) => selected}
                                      MenuProps={MenuProps}
                                    >
                                      {/* <MenuItem sx={font_Small} disabled value={""}>
                                <div
                                  style={{ color: "#C1C1C1", fontSize: "12px" }}
                                >
                                  Select Blocking Status
                                </div>
                              </MenuItem> */}
                                      {names?.map((name) => (
                                        <MenuItem
                                          sx={font_Small}
                                          key={name}
                                          value={name}
                                          style={{
                                            fontSize: "12px !important",
                                            height: "35px",
                                          }}
                                        >
                                          {/* <Checkbox
                                  checked={
                                    rbSearchForm?.reqStatus.indexOf(name) > -1
                                  }
                                /> */}
                                          <ListItemText
                                            sx={font_Small}
                                            primary={name}
                                            style={{ fontSize: "12px !important" }}
                                          />
                                        </MenuItem>
                                      ))}

                                      {/* <MenuItem sx={font_Small} value={""}>
                              <div style={{ color: "#C1C1C1" }}>
                                Select Request Status {}{" "}
                              </div>
                            </MenuItem>
                            <MenuItem value={"Draft"}>Draft</MenuItem>
                            <MenuItem value={"Approval pending"}>Approval pending</MenuItem>
                            <MenuItem value={"Review pending"}>Review pending</MenuItem>
                            <MenuItem value={"Approved"}>Approved</MenuItem>
                            <MenuItem value={"Data Entry pending"}>Data Entry pending</MenuItem>
                            <MenuItem value={"Correction Pending"}>Correction Pending</MenuItem>
                            <MenuItem value={"Approver SLA exceeded"}>Approver SLA exceeded</MenuItem>
                            <MenuItem value={"Data Owner SLA exceeded"}>Data Owner SLA exceeded</MenuItem>
                            <MenuItem value={"MDM Steward SLA exceeded"}>MDM Steward SLA exceeded</MenuItem> */}
                                    </Select>
                                  </FormControl>
                                </Grid>}
                              </React.Fragment>)
                          })}
                        <Grid item md={2}>
                          <LabelTypography sx={font_Small}>
                            {t("Add New Filters")}
                          </LabelTypography>
                          <FormControl fullWidth>
                            <Select
                              sx={{
                                font_Small,
                                height: "36px",
                                fontSize: "12px",
                              }}
                              // fullWidth
                              size="small"
                              multiple
                              limitTags={2}
                              value={selectedOptions}
                              onChange={handleSelection}
                              renderValue={(selected) => selected.join(", ")}
                              MenuProps={{
                                MenuProps,
                              }}
                              endAdornment={
                                selectedOptions.length > 0 && (
                                  <InputAdornment
                                    position="end"
                                    sx={{ marginRight: "15px" }}
                                  >
                                    <IconButton
                                      sx={{ height: "10px", width: "10px" }}
                                      size="small"
                                      onClick={() => setSelectedOptions([])}
                                      aria-label="Clear selections"
                                    >
                                      <ClearIcon />
                                    </IconButton>
                                  </InputAdornment>
                                )
                              }
                            >
                              {items?.map((option) => (
                                <MenuItem
                                  key={option.title}
                                  value={option.title}
                                >
                                  <Checkbox
                                    checked={
                                      selectedOptions.indexOf(option.title) > -1
                                    }
                                  />
                                  {option.title}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                          <Grid
                            style={{
                              display: "flex",
                              justifyContent: "space-around",
                            }}
                          ></Grid>
                        </Grid>
                      </Grid>
                      <Grid
                        container
                        rowSpacing={1}
                        spacing={2}
                        justifyContent="space-between"
                        alignItems="center"
                        sx={{ padding: "0.5rem 1rem 0.5rem" }}
                      // sx={{ marginBottom: "0.5rem" }}
                      >
                        <Grid
                          container
                          spacing={1}
                          sx={{ padding: "0rem 1rem 0.5rem" }}
                        >
                          {selectedOptions?.map((option, i) => {
                            if (option === "Short Description") {
                              return (
                                <>
                                  {/* <Grid item md={2}>
                                  <Typography sx={font_Small}>
                                    {option}
                                  </Typography>
                                  <FormControl size="small" fullWidth>
                                    <TextField
                                      sx={{ fontSize: "12px !important" }}
                                      fullWidth
                                      size="small"
                                      value={pcSearchForm?.profitCenterName}
                                      onChange={handleProfitCenterName}
                                      placeholder="Enter Profit Center Name"
                                    />
                                  </FormControl>
                                </Grid> */}
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                      {t("Short Description")}
                                    </LabelTypography>
                                    <FormControl size="small" fullWidth>
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        size="small"
                                        multiple
                                        disableCloseOnSelect
                                        value={memoizedSDValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        onChange={(e, value, reason) => {
                                          if (
                                            reason === "clear" ||
                                            value?.length === 0
                                          ) {
                                            setselectedProfitCenterName([]);
                                            setselectedPresetProfitCenterName(
                                              []
                                            );
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code ===
                                            "Select All"
                                          ) {
                                            handleSelectAllProfitCenterName();
                                          } else {
                                            setselectedProfitCenterName(value);
                                          }
                                        }}
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        limitTags={1}
                                        options={
                                          dropDownData?.PCNameSearchET?.length
                                            ? [
                                              { code: "Select All" },
                                              ...dropDownData?.PCNameSearchET,
                                            ]
                                            : dropDownData?.PCNameSearchET ?? []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return option?.code ?? "";
                                          else return "";
                                        }}
                                        renderOption={(
                                          props,
                                          option,
                                          { selected }
                                        ) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isProfitCenterNameSelected(
                                                        option
                                                      ) ||
                                                      (option?.code ===
                                                        "Select All" &&
                                                        selectedProfitCenterName?.length ===
                                                        dropDownData
                                                          ?.PCNameSearchET
                                                          ?.length)
                                                    }
                                                  />
                                                }
                                                label={
                                                  <>
                                                    <strong>
                                                      {option.code}
                                                    </strong>
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              pcShortDesc.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            placement="top"
                                            disableHoverListener={
                                              pcShortDesc.length >= 4
                                            }
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35, // Set a fixed height here
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              placeholder="Search Short Description"
                                              onChange={(e) => {
                                                handlePCNameInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>
                                </>
                              );
                            } else if (option === "Street") {
                              return (
                                <>
                                  {/* <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {option}
                                  </LabelTypography>
                                  <FormControl size="small" fullWidth>
                                    <TextField
                                      sx={{ fontSize: "12px !important" }}
                                      fullWidth
                                      size="small"
                                      value={pcSearchForm?.street}
                                      onChange={handleStreet}
                                      placeholder="Enter Street"
                                    />
                                  </FormControl>
                                </Grid> */}
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                      {t("Street")}
                                    </LabelTypography>
                                    <FormControl size="small" fullWidth>
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        size="small"
                                        multiple
                                        disableCloseOnSelect
                                        value={memoizedStreetValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        limitTags={1}
                                        onChange={(e, value, reason) => {
                                          if (
                                            reason === "clear" ||
                                            value?.length === 0
                                          ) {
                                            setselectedStreet([]);
                                            setselectedPresetStreet([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code ===
                                            "Select All"
                                          ) {
                                            handleSelectAllStreet();
                                          } else {
                                            setselectedStreet(value);
                                          }
                                        }}
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        options={
                                          dropDownData?.StreetSearchET?.length
                                            ? [
                                              { code: "Select All" },
                                              ...dropDownData?.StreetSearchET,
                                            ]
                                            : dropDownData?.StreetSearchET ?? []
                                        }
                                        //options={[{ code: 'Select All'}, ...(dropDownData?.StreetSearchET ?? [])]}
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return option?.code ?? "";
                                          else return "";
                                        }}
                                        renderOption={(
                                          props,
                                          option,
                                          { selected }
                                        ) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isStreetSelected(
                                                        option
                                                      ) ||
                                                      (option?.code ===
                                                        "Select All" &&
                                                        selectedStreet?.length ===
                                                        dropDownData
                                                          ?.StreetSearchET
                                                          ?.length)
                                                    }
                                                  />
                                                }
                                                label={
                                                  <>
                                                    <strong>
                                                      {option.code}
                                                    </strong>
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              pcStreet.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            placement="top"
                                            disableHoverListener={
                                              pcStreet.length >= 4
                                            }
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35, // Set a fixed height here
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              placeholder="Search Street"
                                              onChange={(e) => {
                                                handleStreetInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>
                                </>
                              );
                            } else if (option === "Created By") {
                              return (
                                <>
                                  {/* <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {option}
                                  </LabelTypography>
                                  <FormControl size="small" fullWidth>
                                    <TextField
                                      sx={{ fontSize: "12px !important" }}
                                      fullWidth
                                      size="small"
                                      value={pcSearchForm?.createdBy}
                                      onChange={handleCreatedBy}
                                      placeholder="Enter Created By"
                                    />
                                  </FormControl>
                                </Grid> */}
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                      {t("Created By")}
                                    </LabelTypography>
                                    <FormControl size="small" fullWidth>
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        multiple
                                        disableCloseOnSelect
                                        size="small"
                                        value={memoizedCreatedByValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        onChange={(e, value, reason) => {
                                          if (
                                            reason === "clear" ||
                                            value?.length === 0
                                          ) {
                                            setselectedCreatedBy([]);
                                            setselectedPresetCreatedBy([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code ===
                                            "Select All"
                                          ) {
                                            handleSelectAllCreatedBy();
                                          } else {
                                            setselectedCreatedBy(value);
                                          }
                                        }}
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        limitTags={1}
                                        options={
                                          dropDownData?.CreatedBySearchET
                                            ?.length
                                            ? [
                                              { code: "Select All" },
                                              ...dropDownData?.CreatedBySearchET,
                                            ]
                                            : dropDownData?.CreatedBySearchET ??
                                            []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return option?.code ?? "";
                                          else return "";
                                        }}
                                        renderOption={(
                                          props,
                                          option,
                                          { selected }
                                        ) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isCreatedBySelected(
                                                        option
                                                      ) ||
                                                      (option?.code ===
                                                        "Select All" &&
                                                        selectedCreatedBy?.length ===
                                                        dropDownData
                                                          ?.CreatedBySearchET
                                                          ?.length)
                                                    }
                                                  />
                                                }
                                                label={
                                                  <>
                                                    <strong>
                                                      {option.code}
                                                    </strong>
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              pcCreatedBy.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            placement="top"
                                            disableHoverListener={
                                              pcCreatedBy.length >= 4
                                            }
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              placeholder="Search Created By"
                                              onChange={(e) => {
                                                handleCreatedByInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>
                                </>
                              );
                            } else if (option === "Created On") {
                              return (
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {option}
                                  </LabelTypography>
                                  <FormControl size="small" fullWidth>
                                    <LocalizationProvider
                                      dateAdapter={AdapterDateFns}
                                    >
                                      <DateRange
                                        handleDate={handleDate}
                                        date={selectedDateRange}
                                      />
                                    </LocalizationProvider>
                                  </FormControl>
                                </Grid>
                              );
                            } else if (option === "Country/Region") {
                              return (
                                <>
                                  {/* <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {option}
                                  </LabelTypography>
                                  <FormControl size="small" fullWidth>
                                    <TextField
                                      sx={{ fontSize: "12px !important" }}
                                      fullWidth
                                      size="small"
                                      value={pcSearchForm?.createdBy}
                                      onChange={handleCreatedBy}
                                      placeholder="Enter Created By"
                                    />
                                  </FormControl>
                                </Grid> */}
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                      {t("Country/Region")}
                                    </LabelTypography>
                                    <FormControl size="small" fullWidth>
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        size="small"
                                        multiple
                                        disableCloseOnSelect
                                        value={
                                          selectedCountry.length > 0
                                            ? selectedCountry
                                            : selectedPresetCountry.length > 0
                                              ? selectedPresetCountry
                                              : []
                                        }
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        onChange={(e, value, reason) => {
                                          if (
                                            reason === "clear" ||
                                            value?.length === 0
                                          ) {
                                            setselectedCountry([]);
                                            setselectedPresetCountry([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code ===
                                            "Select All"
                                          ) {
                                            handleSelectAllCountry();
                                          } else {
                                            setselectedCountry(value);
                                          }
                                        }}
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        limitTags={1}
                                        options={
                                          dropDownData?.country?.length
                                            ? [
                                              {
                                                code: "Select All",
                                                desc: "Select All",
                                              },
                                              ...dropDownData?.country,
                                            ]
                                            : dropDownData?.country ?? []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return `${option?.code}` ?? "";
                                          else return "";
                                        }}
                                        renderOption={(
                                          props,
                                          option,
                                          { selected }
                                        ) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isCountrySelected(
                                                        option
                                                      ) ||
                                                      (option?.code ===
                                                        "Select All" &&
                                                        selectedCountry?.length ===
                                                        dropDownData?.country
                                                          ?.length)
                                                    }
                                                  />
                                                }
                                                label={
                                                  <>
                                                    <strong>
                                                      {option.code}
                                                    </strong>{" "}
                                                    - {option.desc}
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <TextField
                                            sx={{
                                              fontSize: "12px !important",
                                              "& .MuiOutlinedInput-root": {
                                                height: 35,
                                              },
                                              "& .MuiInputBase-input": {
                                                padding: "10px 14px",
                                              },
                                            }}
                                            {...params}
                                            variant="outlined"
                                            placeholder="Search Country"
                                          // onChange={(e) => {
                                          //   handleCountryInputChange(e);
                                          // }}
                                          />
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>
                                </>
                              );
                            } else {
                              return (
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {option}
                                  </LabelTypography>
                                  <FormControl fullWidth size="small">
                                    <Autocomplete
                                      sx={{ height: "31px" }}
                                      fullWidth
                                      multiple
                                      disableCloseOnSelect
                                      size="small"
                                      value={
                                        selectedValues[option]?.length > 0
                                          ? selectedValues[option]
                                          : selectedPresetValues[option]
                                            ?.length > 0
                                            ? selectedPresetValues[option]
                                            : []
                                      }
                                      noOptionsText={
                                        isDropDownLoading ? (
                                          <Box
                                            sx={{
                                              display: "flex",
                                              justifyContent: "center",
                                              mt: 1,
                                              zIndex: 9999,
                                              top: "10px",
                                            }}
                                          >
                                            <CircularProgress size={20} />
                                          </Box>
                                        ) : (
                                          "No Data Available"
                                        )
                                      }
                                      onChange={(e, value, reason) => {
                                        if (
                                          reason === "clear" ||
                                          value?.length === 0
                                        ) {
                                          setSelectedValues((prev) => ({
                                            ...prev,
                                            [option]: [],
                                          }));
                                          setSelectedPresetValues((prev) => ({
                                            ...prev,
                                            [option]: [],
                                          }));
                                          return;
                                        }

                                        if (
                                          value.length > 0 &&
                                          value[value.length - 1]?.code ===
                                          "Select All"
                                        ) {
                                          handleSelectAllOptions(option);
                                        } else {
                                          setSelectedValues((prev) => ({
                                            ...prev,
                                            [option]: value,
                                          }));
                                        }
                                      }}
                                      renderTags={(value, getTagProps) =>
                                        value.length > 0 ? (
                                          <>
                                            <Chip
                                              label={value[0].code}
                                              {...getTagProps({ index: 0 })}
                                              sx={{
                                                height: 20,
                                                fontSize: "0.75rem",
                                                ".MuiChip-label": {
                                                  padding: "0 6px",
                                                },
                                              }}
                                            />
                                            {value.length > 1 && (
                                              <Chip
                                                label={`+${value.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                            )}
                                          </>
                                        ) : null
                                      }
                                      limitTags={1}
                                      options={
                                        dynamicOptions?.[option]?.length
                                          ? [
                                            { code: "Select All" },
                                            ...dynamicOptions?.[option],
                                          ]
                                          : dynamicOptions?.[option] ?? []
                                      }
                                      getOptionLabel={(option) =>
                                        option?.code ? `${option.code}` : ""
                                      }
                                      renderOption={(
                                        props,
                                        dropdownOption,
                                        { selected }
                                      ) => (
                                        <li {...props}>
                                          <FormGroup>
                                            <FormControlLabel
                                              control={
                                                <Checkbox
                                                  checked={
                                                    isOptionSelected(
                                                      option,
                                                      dropdownOption
                                                    ) ||
                                                    (dropdownOption?.code ===
                                                      "Select All" &&
                                                      selectedValues[option]
                                                        ?.length ===
                                                      dynamicOptions?.[option]
                                                        ?.length)
                                                  }
                                                />
                                              }
                                              // label={`${dropdownOption?.code}`}
                                              label={
                                                <>
                                                  <strong>
                                                    {dropdownOption?.code}
                                                  </strong>
                                                </>
                                              }
                                            />
                                          </FormGroup>
                                        </li>
                                      )}
                                      renderInput={(params) => (
                                        <TextField
                                          sx={{
                                            fontSize: "12px !important",
                                            "& .MuiOutlinedInput-root": {
                                              height: 35,
                                            },
                                            "& .MuiInputBase-input": {
                                              padding: "10px 14px",
                                            },
                                          }}
                                          {...params}
                                          variant="outlined"
                                          placeholder={`Select ${option}`}
                                        />
                                      )}
                                    />
                                  </FormControl>
                                </Grid>
                              );
                            }
                          })}
                        </Grid>
                      </Grid>
                    </Grid>
                    <ButtonContainer>
                      <ActionButton
                        variant="outlined"
                        
                        sx={{
                          borderColor: colors.primary.main,
                          color: colors.primary.main,
                        }}
                        onClick={handleClear}
                      >
                        Clear
                      </ActionButton>

                      {/* MIGHT UNCOMMENT LATER */}
                      <Grid sx={{ ...button_Marginleft }}>
                        <ReusablePreset
                          moduleName={"ProfitCenter"}
                          PresetObj={PresetObj}
                          handleSearch={getFilter}
                          PresetMethod={PresetMethod}
                        />
                      </Grid>

                      <ActionButton
                        variant="contained"
                        startIcon={<SearchIcon sx={{ fontSize: "1rem" }} />}
                        sx={{ ...button_Primary, ...button_Marginleft }}
                        onClick={() => getFilter()}
                      >
                        Search
                      </ActionButton>
                     
                    </ButtonContainer>
                  </AccordionDetails>
                </StyledAccordion>
              </Grid>
            </Grid>

            <Grid item sx={{ position: "relative" }}>
              <Stack>
                <ReusableTable
                  isLoading={tableLoading}
                  module={"ProfitCenter"}
                  width="100%"
                  title="List of Profit Centers"
                  rows={rmDataRows}
                  columns={dynamicColumns ?? []}
                  showSearch={true}
                  page={page}
                  pageSize={pageSize}
                  showExport={true}
                  showRefresh={true}
                  rowCount={count ?? rmDataRows?.length ?? 0}
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  getRowIdValue={"id"}
                  hideFooter={true}
                  checkboxSelection={true}
                  disableSelectionOnClick={true}
                  status_onRowDoubleClick={true}
                  onRowsSelectionHandler={handleSelectionModelChange}
                  callback_onRowDoubleClick={(params) => {
                    const profitCenter = params.row.profitCenter; // Adjust this based on your data structure
                    navigate(
                      `/masterDataCockpit/profitCenter/displayProfitCenter/${profitCenter}`,
                      {
                        state: params.row,
                      }
                    );
                  }}
                  // setShowWork={setShowWork}
                  stopPropagation_Column={"action"}
                  // status_onRowDoubleClick={true}
                  showCustomNavigation={true}
                />
                {/* {viewDetailpage && <SingleMaterialDetail />} */}
              </Stack>
            </Grid>
            {/* {
            showBtmNav && */}
            {/* {(checkIwaAccess(iwaAccessData, "Profit Center", "CreatePC") &&
                userData?.role === "Super User") ||
              userData?.role === "Finance" ? ( */}
            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
                value={value}
                onChange={(newValue) => {
                  setValue(newValue);
                }}
              >
                {/* <ButtonGroup
                  variant="contained"
                  ref={anchorRefCreate}
                  aria-label="split button"
                >
                  <Button
                    size="small"
                    variant="contained"
                    onClick={() => handleClickCreate(optionsCreateSingle[0], 0)}
                    // onClick={handleDialogClickOpen}
                  >
                    {optionsCreateSingle[0]}
                  </Button>
                  <Button
                    size="small"
                    aria-controls={
                      openButtonCreate ? "split-button-menu" : undefined
                    }
                    aria-expanded={openButtonCreate ? "true" : undefined}
                    aria-label="select action"
                    aria-haspopup="menu"
                    onClick={handleToggleCreate}
                  >
                    <ReusableIcon
                      iconName={"ArrowDropUp"}
                      iconColor={"#FFFFFF"}
                    />
                  </Button>
                </ButtonGroup> */}
                <Popper
                  sx={{
                    zIndex: 1,
                  }}
                  open={openButtonCreate}
                  anchorEl={anchorRefCreate.current}
                  placement={"top-end"}
                >
                  <Paper
                    style={{ width: anchorRefCreate.current?.clientWidth }}
                  >
                    <ClickAwayListener onClickAway={handleCloseButtonCreate}>
                      <MenuList id="split-button-menu" autoFocusItem>
                        {optionsCreateSingle.slice(1)?.map((option, index) => (
                          <MenuItem
                            key={option}
                            selected={index === selectedIndexCreate - 1}
                            onClick={() => handleClickCreate(option, index + 1)}
                          >
                            {option}
                          </MenuItem>
                        ))}
                      </MenuList>
                    </ClickAwayListener>
                  </Paper>
                </Popper>

                {/* <Button
                  size="small"
                  variant="contained"
                  // onClick={handleClickOpenCreateInvoice}
                  onClick={handleDialogClickOpen}
                >
                  Create Single
                </Button> */}
                <Dialog
                  open={openDialog}
                  onClose={handleCloseDialog}
                  sx={{
                    "&::webkit-scrollbar": {
                      width: "1px",
                    },
                  }}
                >
                  <DialogTitle
                    sx={{
                      justifyContent: "space-between",
                      alignItems: "center",
                      height: "max-content",
                      padding: ".5rem",
                      paddingLeft: "1rem",
                      backgroundColor: "#EAE9FF40",

                      display: "flex",
                    }}
                  >
                    <Grid>
                      <Grid
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <ToggleButtonGroup
                          color="primary"
                          value={alignment}
                          exclusive
                          onChange={handleChange}
                          // aria-label="Platform"
                        >
                          <ToggleButton
                            value="ALL OTHER CHANGES"
                            //disabled={dataListAllOtherChanges?.length !== 0}
                          >
                            ALL OTHER CHANGES
                          </ToggleButton>
                          {!filteredRuleData && (
                            <ToggleButton value="BLOCK">BLOCK</ToggleButton>
                          )}
                          <ToggleButton
                            value="TEMPORARY BLOCK/UNBLOCK"
                            //disabled={dataListProfitCenterChange?.length !== 0}
                          >
                            TEMPORARY BLOCK/UNBLOCK
                          </ToggleButton>
                        </ToggleButtonGroup>
                      </Grid>
                      <Grid>
                        <Typography variant="h6">
                          Select the field(s) to be changed
                        </Typography>
                      </Grid>
                    </Grid>
                  </DialogTitle>
                  <DialogContent
                    sx={{
                      padding: ".5rem 1rem",
                      maxHeight: 400,
                      maxWidth: 400,
                      overflowY: "auto",
                    }}
                  >
                    <Grid container>
                      {alignment === "ALL OTHER CHANGES" ? (
                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                sx={{ height: "2vh" }}
                                onChange={handleSelectAll}
                                checked={selectAll}
                              />
                            }
                            label="SELECT ALL"
                          />
                        </Grid>
                      ) : (
                        ""
                      )}
                      {alignment === "ALL OTHER CHANGES"
                        ? dataListAllOtherChanges?.map((item) => (
                            <Grid item xs={12} key={item?.id}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    sx={{ height: "2vh" }}
                                    onChange={() =>
                                      handleSelectionAllOtherChanges(item)
                                    }
                                    checked={dataListAllOtherChangesSelected?.some(
                                      (selectedItem) =>
                                        selectedItem.id === item.id
                                    )}
                                  />
                                }
                                label={item.name}
                              />
                            </Grid>
                          ))
                        : alignment === "BLOCK"
                        ? dataListBlockNames?.map((item) => (
                            <Grid item xs={12} key={item?.id}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    sx={{ height: "2vh" }}
                                    onChange={() => handleSelectionBlock(item)}
                                    checked={dataListBlockNamesSelected?.some(
                                      (selectedItem) =>
                                        selectedItem.id === item.id
                                    )}
                                    disabled
                                  />
                                }
                                label={item.name}
                              />
                            </Grid>
                          ))
                        : alignment === "TEMPORARY BLOCK/UNBLOCK"
                        ? dataListTemporaryBlockNames?.map((item) => (
                            <Grid item xs={12} key={item?.id}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    sx={{ height: "2vh" }}
                                    onChange={() =>
                                      handleSelectionTemporaryBlockChange(item)
                                    }
                                    checked={dataListTemporaryBlockNamesSelected?.some(
                                      (selectedItem) =>
                                        selectedItem.id === item.id
                                    )}
                                    disabled
                                  />
                                }
                                label={item.name}
                              />
                            </Grid>
                          ))
                        : dataListProfitCenterChange?.map((item) => (
                            <Grid item xs={12} key={item.id}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    onChange={() =>
                                      handleSelectionProfitCenterChange(item)
                                    }
                                    checked={dataListProfitCenterChangeSelected?.some(
                                      (selectedItem) =>
                                        selectedItem.id === item.id
                                    )}
                                  />
                                }
                                label={item.name}
                              />
                            </Grid>
                          ))}
                    </Grid>
                  </DialogContent>
                  <DialogActions
                    sx={{ display: "flex", justifyContent: "end" }}
                  >
                    <Button
                      sx={{
                        width: "max-content",
                        textTransform: "capitalize",
                      }}
                      onClick={handleCloseDialog}
                    >
                      Cancel
                    </Button>
                    <Button
                      className="button_primary--normal"
                      type="save"
                      onClick={handleApply}
                      variant="contained"
                    >
                      Apply
                    </Button>
                  </DialogActions>
                </Dialog>
                <Dialog
                  open={openDialogIDM}
                  onClose={handleCloseDialogIDM}
                  sx={{
                    "&::webkit-scrollbar": {
                      width: "1px",
                    },
                  }}
                >
                  <DialogTitle
                    sx={{
                      justifyContent: "space-between",
                      alignItems: "center",
                      height: "max-content",
                      padding: ".5rem",
                      paddingLeft: "1rem",
                      backgroundColor: "#EAE9FF40",
                      // borderBottom: "1px solid grey",
                      display: "flex",
                    }}
                  >
                    <Grid>
                      <Grid
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <ToggleButtonGroup
                          color="primary"
                          value={alignment}
                          exclusive
                          onChange={handleChange}
                          // aria-label="Platform"
                        >
                          <ToggleButton
                            value="ALL OTHER CHANGES"
                            //disabled={dataListAllOtherChanges?.length !== 0}
                          >
                            ALL OTHER CHANGES
                          </ToggleButton>
                          {!filteredRuleDataMass && (
                            <ToggleButton value="BLOCK">BLOCK</ToggleButton>
                          )}
                          <ToggleButton
                            value="TEMPORARY BLOCK/UNBLOCK"
                            //disabled={dataListProfitCenterChange?.length !== 0}
                          >
                            TEMPORARY BLOCK/UNBLOCK
                          </ToggleButton>
                        </ToggleButtonGroup>
                        {/* <IconButton
                          sx={{ width: "max-content" }}
                          onClick={handleCloseDialogIDM}
                          children={<CloseIcon />}
                        /> */}
                      </Grid>
                      <Grid>
                        <Typography variant="h6">
                          Select the field(s) to be changed
                        </Typography>
                      </Grid>
                    </Grid>
                  </DialogTitle>
                  <DialogContent
                    sx={{
                      padding: ".5rem 1rem",
                      maxHeight: 400,
                      maxWidth: 400,
                      overflowY: "auto",
                    }}
                  >
                    <Grid container>
                      {alignment === "ALL OTHER CHANGES" ? (
                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                sx={{ height: "2vh" }}
                                onChange={handleSelectAll}
                                checked={selectAll}
                              />
                            }
                            label="SELECT ALL"
                          />
                        </Grid>
                      ) : (
                        ""
                      )}
                      {alignment === "ALL OTHER CHANGES"
                        ? dataListAllOtherChanges?.map((item) => (
                            <Grid item xs={12} key={item?.id}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    sx={{ height: "2vh" }}
                                    onChange={() =>
                                      handleSelectionAllOtherChanges(item)
                                    }
                                    checked={dataListAllOtherChangesSelected?.some(
                                      (selectedItem) =>
                                        selectedItem.id === item.id
                                    )}
                                  />
                                }
                                label={item.name}
                              />
                            </Grid>
                          ))
                        : alignment === "BLOCK"
                        ? dataListBlockNames?.map((item) => (
                            <Grid item xs={12} key={item?.id}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    sx={{ height: "2vh" }}
                                    onChange={() => handleSelectionBlock(item)}
                                    checked={dataListBlockNamesSelected?.some(
                                      (selectedItem) =>
                                        selectedItem.id === item.id
                                    )}
                                    disabled
                                  />
                                }
                                label={item.name}
                              />
                            </Grid>
                          ))
                        : alignment === "TEMPORARY BLOCK/UNBLOCK"
                        ? dataListTemporaryBlockNames?.map((item) => (
                            <Grid item xs={12} key={item?.id}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    sx={{ height: "2vh" }}
                                    onChange={() =>
                                      handleSelectionTemporaryBlockChange(item)
                                    }
                                    checked={dataListTemporaryBlockNamesSelected?.some(
                                      (selectedItem) =>
                                        selectedItem.id === item.id
                                    )}
                                    disabled
                                  />
                                }
                                label={item.name}
                              />
                            </Grid>
                          ))
                        : dataListProfitCenterChange?.map((item) => (
                            <Grid item xs={12} key={item.id}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    onChange={() =>
                                      handleSelectionProfitCenterChange(item)
                                    }
                                    checked={dataListProfitCenterChangeSelected?.some(
                                      (selectedItem) =>
                                        selectedItem.id === item.id
                                    )}
                                  />
                                }
                                label={item.name}
                              />
                            </Grid>
                          ))}
                    </Grid>
                  </DialogContent>
                  <DialogActions
                    sx={{ display: "flex", justifyContent: "end" }}
                  >
                    <Button
                      sx={{
                        width: "max-content",
                        textTransform: "capitalize",
                      }}
                      onClick={handleCloseDialogIDM}
                    >
                      Cancel
                    </Button>
                    <Button
                      className="button_primary--normal"
                      type="save"
                      onClick={() => setOpenDownloadChangeDialog(true)}
                      variant="contained"
                      // disabled={!buttonDisabledForSingleWithoutCopy}
                    >
                      Apply
                    </Button>
                  </DialogActions>
                </Dialog>
                {/* <Dialog
                  open={openSelectColumnDialog}
                  onClose={handleSelectColumnDialogClose}
                  sx={{
                    "&::webkit-scrollbar": {
                      width: "1px",
                    },
                  }}
                >
                  <DialogTitle
                    sx={{
                      justifyContent: "space-between",
                      alignItems: "center",
                      height: "max-content",
                      padding: ".5rem",
                      paddingLeft: "1rem",
                      backgroundColor: "#EAE9FF",
                      // borderBottom: "1px solid grey",
                      display: "flex",
                    }}
                  >
                    <Typography variant="h6">
                      Select the field(s) to be changed
                    </Typography>
                  </DialogTitle>
                  <DialogContent
                    sx={{
                      padding: ".5rem 1rem",
                      maxHeight: 400,
                      maxWidth: 400,
                      overflowY: "auto",
                    }}
                  >
                    <Grid container>
                      <Grid item xs={12}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              sx={{ height: "2vh" }}
                              onChange={handleSelectAll}
                              checked={selectAll}
                            />
                          }
                          label="SELECT ALL"
                        />
                      </Grid>

                      <>
                        {Object.values(groupedItems)?.map((uniqueItem) => (
                          <Grid item xs={12} key={uniqueItem.id}>
                            <FormControlLabel
                              value={uniqueItem.templateName}
                              control={
                                <Checkbox
                                  sx={{ height: "2vh" }}
                                  onChange={() =>
                                    handleSelectionListItem(uniqueItem)
                                  }
                                  checked={selectedListItems.some(
                                    (selectedItem) =>
                                      selectedItem.id === uniqueItem.id
                                  )}
                                />
                              }
                              label={uniqueItem.templateName}
                            />
                          </Grid>
                        ))}
                      </>
                    </Grid>
                  </DialogContent>
                  <DialogActions
                    sx={{ display: "flex", justifyContent: "end" }}
                  >
                    <Button
                      sx={{
                        width: "max-content",
                        textTransform: "capitalize",
                      }}
                      onClick={handleCloseDialogIDM}
                    >
                      Cancel
                    </Button>
                    <Button
                      className="button_primary--normal"
                      type="save"
                      // onClick={handleSelectedColumn}
                      onClick={()=>setOpenDownloadChangeDialog(true)}
                      variant="contained"
                      // disabled={!buttonDisabledForSingleWithoutCopy}
                    >
                      Apply
                    </Button>
                  </DialogActions>
                </Dialog> */}

                <Dialog
                  fullWidth
                  // maxWidth={"sm"}

                  open={dialogOpen}
                  onClose={handleDialogClose}
                  sx={{
                    "&::webkit-scrollbar": {
                      width: "1px",
                    },
                  }}
                >
                  <DialogTitle
                    sx={{
                      justifyContent: "space-between",
                      alignItems: "center",
                      height: "max-content",
                      padding: ".5rem",
                      paddingLeft: "1rem",
                      backgroundColor: "#EAE9FF",
                      // borderBottom: "1px solid grey",
                      display: "flex",
                    }}
                  >
                    <Typography variant="h6">New Profit Center</Typography>

                    {/* <IconButton
                      sx={{ width: "max-content" }}
                      onClick={handleDialogClose}
                      children={<CloseIcon />}
                    /> */}
                  </DialogTitle>
                  <DialogContent sx={{ padding: ".5rem 1rem" }}>
                    <FormControl component="fieldset">
                      <FormLabel component="legend">
                        Do You Know Profit Center Number?
                      </FormLabel>
                      <RadioGroup
                        row
                        aria-label="profit-center-number"
                        name="profit-center-number"
                        value={radioValue}
                        onChange={handleRadioChange}
                      >
                        <FormControlLabel
                          value="yes"
                          control={<Radio />}
                          label="Yes"
                        />
                        <FormControlLabel
                          value="no"
                          control={<Radio />}
                          label="No"
                        />
                      </RadioGroup>
                    </FormControl>
                    {radioValue === "yes" ? (
                      <Grid container spacing={1}>
                        {/* <Grid
                            item
                            md={6}
                            sx={{ width: "100%", marginTop: ".5rem" }}
                          >
                            <Typography>
                              Controlling Area
                              <span style={{ color: "red" }}>*</span>
                            </Typography>
                            <FormControl
                              fullWidth
                              sx={{ margin: ".5em 0px", minWidth: "250px" }}
                            >
                              <Autocomplete
                                sx={{ height: "31px" }}
                                fullWidth
                                size="small"
                                onChange={(e, value) => {
                                  setNewControllingArea(value);
                                
                                  getCompanyCodeBasedOnControllingArea(value);
                                  getProfitCenterGroup(value);
                                  getCompanyCodeBasedOnControllingAreaCopy(
                                    value
                                  );
                                }}
                                options={dropDownData?.ControllingArea ?? []}
                                getOptionLabel={(option) => {
                                  if (option?.code)
                                    return (
                                      `${option?.code}-${option?.desc}` ?? ""
                                    );
                                  else return "";
                                }}
                                value={pcSearchForm?.costCenterCategory}
                                renderOption={(props, option) => (
                                  <li {...props}>
                                    <Typography style={{ fontSize: 12 }}>
                                      {`${option?.code}-${option?.desc}`}
                                    </Typography>
                                  </li>
                                )}
                                renderInput={(params) => (
                                  <TextField
                                    sx={{ fontSize: "12px !important" }}
                                    {...params}
                                    variant="outlined"
                                    placeholder="SELECT CONTROLLING AREA"
                                    error={controllingAreaValid}
                                  />
                                )}
                              />
                            </FormControl>
                            {controllingAreaValid && (
                              <Typography variant="caption" color="error">
                                Please Select a Controlling Area.
                              </Typography>
                            )}
                          </Grid> */}
                        <Grid
                          item
                          // md={6}
                          sx={{
                            width: "100%",
                            marginTop: ".5rem",
                            // marginRight: "5rem",
                          }}
                        >
                          <Typography>
                            Profit Center
                            <span style={{ color: "red" }}>*</span>
                          </Typography>
                          <FormControl
                            fullWidth
                            sx={{
                              margin: ".5em 0px",
                              minWidth: "250px",
                              flexDirection: "row",
                            }}
                          >
                            <Grid md={1}>
                              <TextField
                                sx={{
                                  fontSize: "12px !important",
                                  height: "31px",
                                }}
                                value="P"
                                fullWidth
                                size="small"
                                editable={false}
                              />
                            </Grid>
                            <Grid md={6}>
                              <Autocomplete
                                sx={{ height: "42px" }}
                                required="true"
                                size="small"
                                onChange={(e, value) => {
                                  setNewComapnyCode(value);
                                  dispatch(
                                    setSingleProfitCenterPayload({
                                      keyName: "CompanyCode",
                                      data: value,
                                    })
                                  );
                                  getCompanyCodeForProfitCenter(value);
                                }}
                                options={dropDownData?.CompanyCode ?? []}
                                getOptionLabel={(option) =>
                                  `${option?.code}-${option?.desc}`
                                }
                                // value={pcSearchForm?.plant}
                                renderOption={(props, option) => (
                                  <li {...props}>
                                    <Typography style={{ fontSize: 12 }}>
                                      {`${option?.code}-${option?.desc}`}
                                    </Typography>
                                  </li>
                                )}
                                // error={newControllingArea === "" ? true : false}
                                renderInput={(params) => (
                                  <TextField
                                    sx={{ fontSize: "12px !important" }}
                                    {...params}
                                    variant="outlined"
                                    placeholder="SELECT COMPANY CODE"
                                  />
                                )}
                              />
                            </Grid>
                            <Grid md={5}>
                              <TextField
                                // className={classes.input}
                                sx={{
                                  fontSize: "12px !important",
                                  height: "31px",
                                }}
                                fullWidth
                                size="small"
                                //value={pcSearchForm?.changedBy}
                                value={newProfitCenterName}
                                onChange={(e) => {
                                  let newValue = e.target.value;
                                  if (/^\d*$/.test(newValue)) {
                                    if (
                                      newValue.length > 0 &&
                                      newValue[0] === " "
                                    ) {
                                      setNewProfitCenterName(
                                        newValue.trimStart()
                                      );
                                    } else {
                                      let profitCenterUpperCase =
                                        newValue.toUpperCase();
                                      setNewProfitCenterName(
                                        profitCenterUpperCase
                                      );
                                    }
                                  }
                                }}
                                inputProps={{
                                  length:
                                    profitCenterLength -
                                    newCompanyCode?.code?.length -
                                    1,
                                  maxLength:
                                    profitCenterLength -
                                    newCompanyCode?.code?.length -
                                    1,
                                  style: { textTransform: "uppercase" },
                                }}
                                placeholder="Enter Profit Center"
                                // error={newCostCenterName === "" ? true : false}
                                required={true}
                                //error={profitCenterValid}
                                error={newProgfitCenterValid}
                              />
                              {newProgfitCenterValid && (
                                <Typography variant="caption" color="error">
                                  Profit Center must be 10 digits
                                </Typography>
                              )}
                            </Grid>
                          </FormControl>
                          {profitCenterValid && (
                            <Typography variant="caption" color="error">
                              Please enter a Profit Center.
                            </Typography>
                          )}
                        </Grid>
                        {isValidationError && (
                          <Grid>
                            <Typography style={{ color: "red" }}>
                              Please Enter Mandatory Fields
                            </Typography>
                          </Grid>
                        )}

                        {checkValidationProfitCenter && (
                          <Grid>
                            <Typography style={{ color: "red" }}>
                              *Profit Center Number is already used - please
                              select a different number before submitting
                              request.
                            </Typography>
                          </Grid>
                        )}
                      </Grid>
                    ) : (
                      <Grid
                        container
                        spacing={1}
                        sx={{ display: "flex", flexDirection: "column" }}
                      >
                        <Grid
                          item
                          md={6}
                          sx={{
                            width: "100%",
                            marginTop: ".5rem",
                          }}
                        >
                          <Typography>
                            Select Company Code
                            <span style={{ color: "red" }}>*</span>
                          </Typography>
                          <FormControl
                            fullWidth
                            sx={{
                              margin: ".5em 0px",
                              minWidth: "250px",
                            }}
                          >
                            <Autocomplete
                              sx={{ height: "42px" }}
                              required="true"
                              size="small"
                              onChange={(e, value) => {
                                setNewComapnyCode(value);
                                dispatch(
                                  setSingleProfitCenterPayload({
                                    keyName: "CompanyCode",
                                    data: value,
                                  })
                                );
                                getCompanyCodeForProfitCenter(value);
                              }}
                              options={dropDownData?.CompanyCode ?? []}
                              getOptionLabel={(option) =>
                                `${option?.code}-${option?.desc}`
                              }
                              renderOption={(props, option) => (
                                <li {...props}>
                                  <Typography style={{ fontSize: 12 }}>
                                    {`${option?.code}-${option?.desc}`}
                                  </Typography>
                                </li>
                              )}
                              renderInput={(params) => (
                                <TextField
                                  sx={{ fontSize: "12px !important" }}
                                  {...params}
                                  variant="outlined"
                                  placeholder="SELECT COMPANY CODE"
                                />
                              )}
                            />
                          </FormControl>
                        </Grid>
                        <Grid item>
                          {isValidationError && (
                            <Grid>
                              <Typography style={{ color: "red" }}>
                                Please Enter Mandatory Fields
                              </Typography>
                            </Grid>
                          )}
                        </Grid>
                      </Grid>
                    )}
                  </DialogContent>

                  <DialogActions
                    sx={{ display: "flex", justifyContent: "end" }}
                  >
                    <Button
                      sx={{
                        width: "max-content",
                        textTransform: "capitalize",
                      }}
                      onClick={handleDialogClose}
                    >
                      Cancel
                    </Button>
                    <Button
                      className="button_primary--normal"
                      type="save"
                      onClick={handleDialogProceed}
                      variant="contained"
                    >
                      Proceed
                    </Button>
                  </DialogActions>
                </Dialog>

                <Dialog
                  // fullWidth={fullWidth}
                  // maxWidth={maxWidth}

                  open={dialogOpenCreate}
                  onClose={handleDialogCloseCreate}
                  sx={{
                    "&::webkit-scrollbar": {
                      width: "1px",
                    },
                  }}
                >
                  <DialogTitle
                    sx={{
                      justifyContent: "space-between",
                      alignItems: "center",
                      height: "max-content",
                      padding: ".5rem",
                      paddingLeft: "1rem",
                      backgroundColor: "#EAE9FF40",
                      // borderBottom: "1px solid grey",
                      display: "flex",
                    }}
                  >
                    <Typography variant="h6">New Profit Center</Typography>

                    {/* <IconButton
                      sx={{ width: "max-content" }}
                      onClick={handleDialogCloseCreate}
                      children={<CloseIcon />}
                    /> */}
                  </DialogTitle>
                  <DialogContent sx={{ padding: ".5rem 1rem" }}>
                    <Grid container spacing={1}>
                      <Grid
                        item
                        md={6}
                        sx={{ width: "100%", marginTop: ".5rem" }}
                      >
                        <Typography>
                          Controlling Area
                          <span style={{ color: "red" }}>*</span>
                        </Typography>
                        <FormControl
                          fullWidth
                          sx={{ margin: ".5em 0px", minWidth: "250px" }}
                        >
                          <Autocomplete
                            sx={{ height: "31px" }}
                            fullWidth
                            size="small"
                            onChange={(e, value) => {
                              setNewControllingArea(value);
                              getCompanyCodeBasedOnControllingArea(value);
                              getCompanyCodeForCreate(value);
                              getProfitCenterGroup(value);
                            }}
                            options={dropDownData?.ControllingArea ?? []}
                            getOptionLabel={(option) =>
                              `${option?.code}-${option?.desc}`
                            }
                            // value={pcSearchForm?.costCenterCategory}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code}-${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="SELECT CONTROLLING AREA"
                                error={controllingAreaValid}
                              />
                            )}
                          />
                        </FormControl>
                        {controllingAreaValid && (
                          <Typography variant="caption" color="error">
                            Please Select a Controlling Area.
                          </Typography>
                        )}
                      </Grid>
                      <Grid
                        item
                        md={6}
                        sx={{
                          width: "100%",
                          marginTop: ".5rem",
                          // marginRight: "5rem",
                        }}
                      >
                        <Typography>
                          Profit Center
                          <span style={{ color: "red" }}>*</span>
                        </Typography>
                        <FormControl
                          fullWidth
                          sx={{
                            margin: ".5em 0px",
                            minWidth: "250px",
                            flexDirection: "row",
                          }}
                        >
                          <Grid md={2}>
                            <TextField
                              sx={{
                                fontSize: "12px !important",
                                height: "31px",
                              }}
                              value="P"
                              fullWidth
                              size="small"
                              editable={false}
                            />
                          </Grid>
                          <Grid md={5}>
                            <Autocomplete
                              sx={{ height: "42px" }}
                              required="true"
                              size="small"
                              onChange={(e, value) => {
                                setNewComapnyCodeCopy(value);
                              }}
                              options={dropDownData?.CompanyCode ?? []}
                              getOptionLabel={(option) =>
                                `${option?.code}-${option.desc}`
                              }
                              // value={pcSearchForm?.plant}
                              renderOption={(props, option) => (
                                <li {...props}>
                                  <Typography style={{ fontSize: 12 }}>
                                    {`${option?.code}-${option.desc}`}
                                  </Typography>
                                </li>
                              )}
                              // error={newControllingArea === "" ? true : false}
                              renderInput={(params) => (
                                <TextField
                                  sx={{ fontSize: "12px !important" }}
                                  {...params}
                                  variant="outlined"
                                  placeholder="SELECT COMPANY CODE"
                                />
                              )}
                            />
                          </Grid>
                          <Grid md={5}>
                            <TextField
                              // className={classes.input}
                              sx={{
                                fontSize: "12px !important",
                                height: "31px",
                              }}
                              fullWidth
                              size="small"
                              value={newProfitCenterName}
                              onChange={(e) => {
                                let newValue = e.target.value;
                                if (
                                  newValue.length > 0 &&
                                  newValue[0] === " "
                                ) {
                                  setNewProfitCenterName(newValue.trimStart());
                                } else {
                                  let profitCenterUpperCase =
                                    newValue.toUpperCase();
                                  setNewProfitCenterName(profitCenterUpperCase);
                                }
                              }}
                              inputProps={{
                                length:
                                  profitCenterLength -
                                  newCompanyCode?.code?.length,
                                maxLength:
                                  profitCenterLength -
                                  newCompanyCode?.code?.length,
                                style: { textTransform: "uppercase" },
                              }}
                              placeholder="Enter Profit Center"
                              // error={newCostCenterName === "" ? true : false}
                              required={true}
                              error={newPofitCenterValidWithCopy}
                            />
                            {newPofitCenterValidWithCopy && (
                              <Typography variant="caption" color="error">
                                Profit Center must be 10 digits
                              </Typography>
                            )}
                          </Grid>
                        </FormControl>

                        {profitCenterValid && (
                          <Typography variant="caption" color="error">
                            Please enter a Profit Center.
                          </Typography>
                        )}
                      </Grid>

                      <Divider sx={{ width: "100%", marginLeft: "2%" }}>
                        <b>Copy From</b>
                      </Divider>
                      <Grid
                        item
                        md={6}
                        sx={{ width: "100%", marginTop: ".5rem" }}
                      >
                        <Typography>
                          Controlling Area
                          <span style={{ color: "red" }}>*</span>
                        </Typography>
                        <FormControl fullWidth sx={{ margin: ".5em 0px" }}>
                          <Autocomplete
                            sx={{ height: "42px" }}
                            required="true"
                            size="small"
                            onChange={(e, value) => {
                              setNewControllingAreaCopyFrom(value);
                              getProfitCenterBasedOnControllingAreaCopy(value);
                            }}
                            options={dropdownData?.ControllingArea ?? []}
                            getOptionLabel={(option) =>
                              `${option?.code}-${option?.desc}`
                            }
                            // value={pcSearchForm?.plant}
                            renderOption={(props, option) => (
                              <li {...props}>
                                <Typography style={{ fontSize: 12 }}>
                                  {`${option?.code}-${option?.desc}`}
                                </Typography>
                              </li>
                            )}
                            error={newControllingArea === "" ? true : false}
                            renderInput={(params) => (
                              <TextField
                                sx={{ fontSize: "12px !important" }}
                                {...params}
                                variant="outlined"
                                placeholder="SELECT CONTROLLING AREA"
                              />
                            )}
                          />
                        </FormControl>
                      </Grid>
                      <Grid
                        item
                        md={6}
                        sx={{
                          width: "100%",
                          marginTop: ".5rem",
                        }}
                      >
                        <Typography>
                          Profit Center
                          <span style={{ color: "red" }}>*</span>
                        </Typography>

                        <FormControl
                          fullWidth
                          sx={{
                            margin: ".5em 0px",
                            minWidth: "250px",
                            flexDirection: "row",
                          }}
                        >
                          <Grid md={12}>
                            <Autocomplete
                              sx={{ height: "42px" }}
                              required="true"
                              size="small"
                              onChange={(e, value) => {
                                setNewProfitCenter(value);
                              }}
                              options={dropdownData?.ProfitCenter ?? []}
                              getOptionLabel={(option) =>
                                `${option?.code}-${option?.desc}`
                              }
                              // value={pcSearchForm?.plant}
                              renderOption={(props, option) => (
                                <li {...props}>
                                  <Typography style={{ fontSize: 12 }}>
                                    {`${option?.code}-${option?.desc}`}
                                  </Typography>
                                </li>
                              )}
                              // error={newControllingArea === "" ? true : false}
                              renderInput={(params) => (
                                <TextField
                                  sx={{ fontSize: "12px !important" }}
                                  {...params}
                                  variant="outlined"
                                  placeholder="SELECT COMPANY CODE"
                                />
                              )}
                            />
                          </Grid>
                        </FormControl>
                      </Grid>

                      {checkValidationProfitCenter && (
                        <Grid>
                          <Typography style={{ color: "red" }}>
                            *The Profit Center with Controlling Area already
                            exists. Please enter different Profit Center or
                            Controlling Area
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                    {isValidationErrorwithCopy && (
                      <Grid>
                        <Typography style={{ color: "red" }}>
                          Please Enter Mandatory Fields
                        </Typography>
                      </Grid>
                    )}
                  </DialogContent>

                  <DialogActions
                    sx={{ display: "flex", justifyContent: "end" }}
                  >
                    <Button
                      sx={{
                        width: "max-content",
                        textTransform: "capitalize",
                      }}
                      onClick={handleDialogCloseCreate}
                    >
                      Cancel
                    </Button>
                    <Button
                      className="button_primary--normal"
                      type="save"
                      onClick={handleDialogProceedWithCopy}
                      variant="contained"
                    >
                      Proceed
                    </Button>
                  </DialogActions>
                </Dialog>
                {/* <ButtonGroup
                  variant="contained"
                  ref={anchorRef}
                  aria-label="split button"
                >
                  <Button
                    size="small"
                    variant="contained"
                    onClick={() => handleClick(options[0], 0)}
                    sx={{ cursor: "default" }}
                  >
                    {options[0]}
                  </Button>
                  <Button
                    size="small"
                    aria-controls={openButton ? "split-button-menu" : undefined}
                    aria-expanded={openButton ? "true" : undefined}
                    aria-label="select action"
                    aria-haspopup="menu"
                    onClick={handleToggle}
                  >
                    <ReusableIcon
                      iconName={"ArrowDropUp"}
                      iconColor={"#FFFFFF"}
                    />
                  </Button>
                </ButtonGroup> */}
                <Popper
                  sx={{
                    zIndex: 1,
                  }}
                  open={openButton}
                  anchorEl={anchorRef.current}
                  placement={"top-end"}
                >
                  <Paper style={{ width: anchorRef.current?.clientWidth }}>
                    <ClickAwayListener onClickAway={handleCloseButton}>
                      <MenuList id="split-button-menu" autoFocusItem>
                        {options.slice(1)?.map((option, index) => (
                          <MenuItem
                            key={option}
                            selected={index === selectedIndex - 1}
                            onClick={() => handleClick(option, index + 1)}
                          >
                            {option}
                          </MenuItem>
                        ))}
                      </MenuList>
                    </ClickAwayListener>
                  </Paper>
                </Popper>
                <ButtonGroup
                  variant="contained"
                  ref={anchorRefChange}
                  aria-label="split button"
                >
                  <Button
                    size="small"
                    onClick={() => {
                      navigate("/requestBench/ProfitCenterRequestTab", {
                        state: {
                          steaperData: [
                            "Request Header",
                            "Profit Center List",
                            "Attachments & Comments",
                          ],
                          moduleName: "ProfitCenter",
                        },
                      });
                      dispatch(setDisplayPayload({}));
                      dispatch(setRequestHeader({}));
                    }}
                    sx={{ cursor: "pointer" }}
                  >
                    Create Request
                  </Button>
                </ButtonGroup>
                {/* <ButtonGroup
                  variant="contained"
                  ref={anchorRefChange}
                  aria-label="split button"
                >
                  <Button
                    size="small"
                    onClick={() => handleClickChange(optionsChange[0], 0)}
                    sx={{ cursor: "default" }}
                  >
                    {optionsChange[0]}
                  </Button>
                  <Button
                    size="small"
                    aria-controls={
                      openButtonChange ? "split-button-menu" : undefined
                    }
                    aria-expanded={openButtonChange ? "true" : undefined}
                    aria-label="select action"
                    aria-haspopup="menu"
                    onClick={handleToggleChange}
                  >
                    <ReusableIcon
                      iconName={"ArrowDropUp"}
                      iconColor={"#FFFFFF"}
                    />
                  </Button>
                </ButtonGroup> */}
                <Popper
                  sx={{
                    zIndex: 1,
                  }}
                  open={openButtonChange}
                  anchorEl={anchorRefChange.current}
                  placement={"top-end"}
                >
                  <Paper
                    style={{ width: anchorRefChange.current?.clientWidth }}
                  >
                    <ClickAwayListener onClickAway={handleCloseButtonChange}>
                      <MenuList id="split-button-menu" autoFocusItem>
                        {optionsChange.slice(1)?.map((option, index) => (
                          <MenuItem
                            key={option}
                            selected={index === selectedIndexChange - 1}
                            onClick={() => handleClickChange(option, index + 1)}
                          >
                            {option}
                          </MenuItem>
                        ))}
                      </MenuList>
                    </ClickAwayListener>
                  </Paper>
                </Popper>

                {enableDocumentUpload && (
                  <AttachmentUploadDialog
                    artifactId=""
                    artifactName=""
                    setOpen={setEnableDocumentUpload}
                    handleUpload={uploadExcel}
                  />
                )}
              </BottomNavigation>
            </Paper>
            {/* ) : (
                ""
              )} */}
          </Stack>
        </div>
      </div>
      {/* )} */}
      {/* <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 9999 }}
        open={blurLoading}
      >
         <Box
      sx={{
        position: "absolute",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "rgba(255, 255, 255, 0.9)",
        zIndex: 10,
        backdropFilter: "blur(10px)", 
      }}
    >
      <Box sx={{ position: "relative", textAlign: "center" }}>
        <Box sx={{ position: "relative", display: "inline-flex" }}>
          <CircularProgress
            size={60}
            thickness={3}
            sx={{
              background: "rgba(0, 0, 0, 0)",
              animation: "pulse 1.5s infinite", 
              "@keyframes pulse": {
                "0%": { transform: "scale(1)" },
                "50%": { transform: "scale(1.1)" },
                "100%": { transform: "scale(1)" },
              },
            }}
          />
          <Box
            sx={{
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              position: "absolute",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              animation: "beep 1.5s infinite", 
              "@keyframes beep": {
                "0%": { transform: "scale(1)", opacity: 1 },
                "50%": { transform: "scale(1.1)", opacity: 0.7 },
                "100%": { transform: "scale(1)", opacity: 1 },
              },
            }}
          >
        
            <img src="favicon.ico" width="28px" alt="loading icon" />
          </Box>
        </Box>
        <Typography
          variant="h6"
          component="div"
          sx={{
            marginTop: 2,
            color: "gray",
          }}
        >
          Your request is being processed. Do not close or refresh the page.
        </Typography>
      </Box>
    </Box>
      </Backdrop> */}
      {/* <Backdrop
        sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 9999 }}
        open={blurLoading}
      >
      <Box sx={{ position: "relative", textAlign: "center" }}>
        <Box sx={{ position: "relative", display: "inline-flex" }}>
          <CircularProgress
            size={60}
            thickness={3}
            sx={{
              background: "rgba(0, 0, 0, 0)",
              animation: "pulse 1.5s infinite", 
              "@keyframes pulse": {
                "0%": { transform: "scale(1)" },
                "50%": { transform: "scale(1.1)" },
                "100%": { transform: "scale(1)" },
              },
            }}
          />
          <Box
            sx={{
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              position: "absolute",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              animation: "beep 1.5s infinite", 
              "@keyframes beep": {
                "0%": { transform: "scale(1)", opacity: 1 },
                "50%": { transform: "scale(1.1)", opacity: 0.7 },
                "100%": { transform: "scale(1)", opacity: 1 },
              },
            }}
          >
        
            <img src="favicon.ico" width="28px" alt="loading icon" />
          </Box>
        </Box>
        <Typography
          component="div"
          sx={{
            marginTop: 2,
            background:"#3B30C8",
            color: "#d7dae0",
          }}
        >
          {loaderMessage?loaderMessage:"Your request is being processed. Do not close or refresh the page."}
        </Typography>
      </Box>
        
      </Backdrop> */}
      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />
    </>
  );
};

export default ProfitCenter;
