import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Container,
  Grid,
  Paper,
  Skeleton,
  Typography,
  TextField,
  InputAdornment,
  Chip,
  LinearProgress
} from '@mui/material';
import { Search, Error } from '@mui/icons-material';

const DataCleanseSkeletonLoader = () => {
  return (
    <Container maxWidth="xl" sx={{ py: 3, bgcolor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Stats Cards Grid */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Type of Module Card */}
        <Grid item xs={12} sm={6} md={2.4}>
          <Card sx={{ textAlign: 'center', p: 2, bgcolor: '#fff5f5' }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <Skeleton variant="circular" width={48} height={48} />
              </Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                <Skeleton width={80} />
              </Typography>
              <Typography variant="h6" color="#dc3545" fontWeight="bold">
                <Skeleton width={60} />
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Number of Objects Card */}
        <Grid item xs={12} sm={6} md={2.4}>
          <Card sx={{ textAlign: 'center', p: 2, bgcolor: '#fff8f0' }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <Skeleton variant="circular" width={48} height={48} />
              </Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                <Skeleton width={100} />
              </Typography>
              <Typography variant="h6" color="#fd7e14" fontWeight="bold">
                <Skeleton width={40} />
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Total Business Rules Card */}
        <Grid item xs={12} sm={6} md={2.4}>
          <Card sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f0ff' }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <Skeleton variant="circular" width={48} height={48} />
              </Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                <Skeleton width={120} />
              </Typography>
              <Typography variant="h6" color="#6f42c1" fontWeight="bold">
                <Skeleton width={20} />
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Aggregate Score Card */}
        <Grid item xs={12} sm={6} md={2.4}>
          <Card sx={{ textAlign: 'center', p: 2, bgcolor: '#f0f8ff' }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <Skeleton variant="circular" width={48} height={48} />
              </Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                <Skeleton width={90} />
              </Typography>
              <Typography variant="h6" color="#007bff" fontWeight="bold">
                <Skeleton width={30} />
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Created By Card */}
        <Grid item xs={12} sm={6} md={2.4}>
          <Card sx={{ textAlign: 'center', p: 2, bgcolor: '#f0fff4' }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <Skeleton variant="circular" width={48} height={48} />
              </Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                <Skeleton width={70} />
              </Typography>
              <Typography variant="h6" color="#28a745" fontWeight="bold">
                <Skeleton width={180} />
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Left Panel - Material List */}
        <Grid item xs={12} md={4}>
          {/* Search Box */}
          <Box sx={{ mb: 2 }}>
            <TextField
              fullWidth
              placeholder="Search materials..."
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search color="action" />
                  </InputAdornment>
                ),
              }}
              disabled
            />
          </Box>

          {/* Material List */}
          <Paper sx={{ p: 2, maxHeight: 400, overflow: 'auto' }}>
            {[1, 2, 3, 4].map((item) => (
              <Box key={item} sx={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                p: 2,
                mb: 1,
                border: '1px solid #e0e0e0',
                borderRadius: 1,
                bgcolor: item === 1 ? '#e3f2fd' : 'white'
              }}>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    <Skeleton width={50} />
                  </Typography>
                  <Typography variant="h6">
                    <Skeleton width={60} />
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    <Skeleton width={40} />
                  </Typography>
                  <Chip 
                    label={<Skeleton width={20} />} 
                    size="small" 
                    sx={{ bgcolor: '#ffebee', color: '#d32f2f' }}
                  />
                </Box>
              </Box>
            ))}
          </Paper>
        </Grid>

        {/* Right Panel - Analysis Details */}
        <Grid item xs={12} md={8}>
          {/* Analysis Header */}
          <Paper sx={{ p: 3, mb: 3, bgcolor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
                  <Skeleton width={60} sx={{ bgcolor: 'rgba(255,255,255,0.3)' }} />
                </Typography>
                <Typography variant="body1" sx={{ color: 'white', opacity: 0.9 }}>
                  <Skeleton width={200} sx={{ bgcolor: 'rgba(255,255,255,0.3)' }} />
                </Typography>
              </Box>
              <Box sx={{ textAlign: 'right' }}>
                <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
                  <Skeleton width={40} sx={{ bgcolor: 'rgba(255,255,255,0.3)' }} />
                </Typography>
                <Typography variant="body1" sx={{ color: 'white', opacity: 0.9 }}>
                  <Skeleton width={80} sx={{ bgcolor: 'rgba(255,255,255,0.3)' }} />
                </Typography>
              </Box>
            </Box>
          </Paper>

          {/* Business Rule Card */}
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
              <Typography variant="h6" sx={{ color: '#1976d2' }}>
                <Skeleton width={100} />
              </Typography>
              <Skeleton width={200} />
              <Chip 
                label={<Skeleton width={20} />} 
                size="small" 
                sx={{ bgcolor: '#ffebee', color: '#d32f2f' }}
              />
            </Box>

            {/* Progress Bar */}
            <Box sx={{ mb: 3 }}>
              <Skeleton variant="rectangular" width="100%" height={8} sx={{ borderRadius: 1 }} />
            </Box>

            {/* Stats Row */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
              <Grid item xs={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h5" fontWeight="bold">
                    <Skeleton width={20} />
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <Skeleton width={40} />
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h5" fontWeight="bold" sx={{ color: '#4caf50' }}>
                    <Skeleton width={20} />
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <Skeleton width={30} />
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h5" fontWeight="bold" sx={{ color: '#f44336' }}>
                    <Skeleton width={20} />
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <Skeleton width={25} />
                  </Typography>
                </Box>
              </Grid>
            </Grid>

            {/* Field Details */}
            <Box sx={{ border: '1px solid #e0e0e0', borderRadius: 1, p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Error sx={{ color: '#f44336' }} />
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body1" fontWeight="bold">
                    <Skeleton width={150} />
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <Skeleton width={80} />
                  </Typography>
                </Box>
                <Box sx={{ textAlign: 'right' }}>
                  <Typography variant="body2" color="text.secondary">
                    <Skeleton width={100} />
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <Skeleton width={120} />
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default DataCleanseSkeletonLoader;