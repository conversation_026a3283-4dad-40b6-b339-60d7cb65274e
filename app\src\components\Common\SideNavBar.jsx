import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Typography,
  Box,
  Tooltip,
} from "@mui/material";
import "./SideNavbar.css";
import ReusableIcon from "./ReusableIcon";

function NavDrawer({
  navState,
  handleDrawer,
  drawerData,
  entitesAndActivities,
  handleMoreDrawer,
  mode,
  onClickNavDrawerItem,
}) {
  const onClickNavigateTo = (pathname) => {
    onClickNavDrawerItem(pathname);
    handleDrawer("close");
    handleMoreDrawer("close");
  };
  return (
    <Drawer
      sx={{
        "& .MuiDrawer-root": {
          position: "absolute",
        },
        "& .MuiPaper-root": {
          position: "absolute",
          background: mode === "dark" ? "#170E5E" : "#ffffff",
        },
        zIndex: 1000,
        left: "90px",
        top: "64px",
      }}
      anchor={"left"}
      open={navState}
      onClose={handleDrawer}
    >
      <Box sx={{ minWidth: "250px" }} role="presentation" color="#170E5E">
        <List>
          {drawerData?.subModule.map((data) => {
            if (
              data.isAccessible &&
              data.isSideOption &&
              entitesAndActivities[drawerData.module.iwaName].includes(
                data.iwaName
              )
            ) {
              return (
                <SubNavListItem
                  key={data.iwaName}
                  onClickNavigateTo={onClickNavigateTo}
                  option={data}
                  handleDrawer={handleDrawer}
                  mode={mode}
                />
              );
            }
          })}
        </List>
      </Box>
    </Drawer>
  );
}
function MoreNavDrawer({
  moreNavState,
  handleMoreDrawer,
  entitesAndActivities,
  handleDrawer,
  sideNavList,
  setDrawerData,
  updateSideNav,
  mode,
  onClickMoreNavDrawerItem,
}) {
  const navigate = useNavigate();
  const location = useLocation();
  const onClickNavigateTo = (pathname) => {
    onClickMoreNavDrawerItem(pathname);
    navigate(pathname);
    handleDrawer("close");
  };
  return (
    <Drawer
      sx={{
        "& .MuiDrawer-root": {
          position: "absolute",
        },
        "& .MuiPaper-root": {
          position: "absolute",
          background: mode === "dark" ? "#170E5E" : "#ffffff",
        },
        zIndex: 1000,
        left: "90px",
        top: "64px",
      }}
      anchor={"left"}
      open={moreNavState}
      onClose={() => handleMoreDrawer("close")}
    >
      <Box sx={{ minWidth: "250px" }} role="presentation" color="#170E5E">
        <List>
          {sideNavList?.data
            ?.slice(
              sideNavList.configuration.moreOptions,
              sideNavList.data.length
            )
            ?.map((option) => {
              if (
                option.isAccessible &&
                option.isSideOption &&
                entitesAndActivities[option.iwaName]
              ) {
                return (
                  <ListItem key={option.iwaName} disablePadding>
                    <ListItemButton
                      className="sideNavButton"
                      sx={{
                        borderBottom: "1px solid #efefef",
                        "&:hover": {
                          backgroundColor: "#EAE9FF",
                        },
                      }}
                      style={
                        location.pathname.includes(`/${option.routePath}`)
                          ? highlightSelected
                          : {}
                      }
                      onClick={() => {
                        if (
                          option.childItems.filter(
                            (item) => item.isAccessible && item.isSideOption
                          ).length
                        ) {
                          handleDrawer("open");
                          setDrawerData({
                            module: option,
                            subModule: option.childItems,
                          });
                        } else {
                          onClickNavigateTo(option.routePath);
                        }
                        updateSideNav(option);
                        handleMoreDrawer("close");
                      }}
                      key={option.id}
                    >
                      <ReusableIcon
                        isSelected={location.pathname.includes(
                          `/${option.routePath}`
                        )}
                        mode={mode}
                        iconName={option.icon}
                        iconSize={"20px !important"}
                      />
                      <ListItemText
                        primaryTypographyProps={{
                          fontSize: "12px !important",
                        }}
                        className="sideNavText"
                        sx={{
                          // "&:active": {
                          //   color: "#3730c7",
                          // },
                          // "&:hover": {
                          //  color: " #3730c7",
                          // },
                          fontSize: "0.1rem",
                          color: location.pathname.includes(
                            `/${option.routePath}`
                          )
                          ? "#3730c7"
                          : mode === "dark"
                          ? "#ffffff"
                          : "#000000",
                          display: "block",
                          textAlign: "left",
                          autofocus: "false",
                          textDecoration: "none",
                          paddingLeft: "1rem !important",
                          width: "max-content",
                        }}
                      >
                        {option.displayName}
                      </ListItemText>
                    </ListItemButton>
                  </ListItem>
                );
              }
            })}
        </List>
      </Box>
    </Drawer>
  );
}

const highlightSelected = {
  backgroundColor: "#EAE9FF",
  borderRadius: "4px",
};

const SubNavListItem = ({
  option,
  popupState,
  onClickNavigateTo,
  handleDrawer,
  mode,
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const isHighlighted =
    option.childItems.filter(
      (item) =>
        item.isAccessible &&
        item.isSideOption &&
        location.pathname.includes(`${item.routePath}`)
    ).length !== 0;
  const [expanded, setExpanded] = useState(isHighlighted);

  if (option.isAccessible && option.isSideOption) {
    if (
      option.childItems.filter((item) => item.isAccessible && item.isSideOption)
        .length === 0
    ) {
      return (
          <ListItem
          
            disablePadding
            sx={           
              location.pathname.includes(`${option.routePath}`)
                ? { ...highlightSelected }
                : {}
            }
          >
            <ListItemButton
              className="sideNavButton"
              sx={{
              width: "100%",
              maxWidth: "240px",
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-start",
              padding: "8px 16px !important",
              borderBottom: "1px solid #efefef",
              "&:hover .descriptionText": {
                opacity: 1,
                maxHeight: "100px",
                transform: "translateY(0px)",
              },
            }}
    onClick={() => {
      onClickNavigateTo(option.routePath);
    }}
    key={option.id}
  >
    <Box display="flex" flexDirection="column" >
    
      <Box display="flex" alignItems="center" >
        <ReusableIcon
          isSelected={location.pathname.includes(`${option.routePath}`)}
          iconName={option.icon}
          iconSize={"20px !important"}
          mode={mode}
        />
        <ListItemText
          primaryTypographyProps={{
            fontSize: "12px !important",
          }}
          className="sideNavText"
          sx={{
                color: location.pathname.includes(`${option.routePath}`)
                ? "#3730c7"
                : mode === "dark"
                ? "#ffffff"
                : "#000000",
                
            paddingLeft: "1rem !important",
            textAlign: "left",
             width: "80%",
          }}
        >
          {option.displayName}
        </ListItemText>
      </Box>
      <Typography
        className="descriptionText"
        fontSize="10px"
        color="text.secondary"
          sx={{
          paddingLeft: "2.2rem",
          opacity: 0,
          maxHeight: 0,
          transform: "translateY(-5px)",
          transition: "opacity 0.3s ease, transform 0.3s ease, max-height 0.3s ease",
          overflow: "hidden",
          whiteSpace: "normal",          
          wordBreak: "break-word",
          width: "80%",
          maxWidth: "100%",
          overflowWrap: "break-word",
        }}
            >
              {option?.description}
            </Typography>
          </Box>
        </ListItemButton>


          </ListItem>
      );
    } else {
  return (
    <ListItem disablePadding>
      <Accordion
        elevation={0}
        expanded={expanded}
        onClick={() => setExpanded(!expanded)}
        sx={{
          border: 0,
          "&::before": {
            backgroundColor: "none !important",
          },
          position: "relative !important",
          width: "100%",
          margin: "0px !important",
        }}
      >
        <AccordionSummary
          sx={{
            backgroundColor: isHighlighted ? "#EAE9FF !important" : "",
            borderBottom: "1px solid #efefef",
            borderRadius: "4px",
            "&:hover": {
              backgroundColor: "#EAE9FF",
            },
            margin: "0px 0px 0px 0px !important",
            paddingBottom: "4px !important",
            paddingTop: "4px !important",
          }}
          expandIcon={
            <ReusableIcon
              mode={mode}
              isSelected={isHighlighted}
              iconName="ExpandMore"
            />
          }
        >
          <ReusableIcon
            isSelected={isHighlighted}
            iconName={option.icon}
            iconSize={"20px !important"}
            mode={mode}
          />
          <ListItemText
        primaryTypographyProps={{
          fontSize: "12px !important",
        }}
        className="sideNavText"
        sx={{
          color: isHighlighted ? "#3730c7" : "#000000",
          textAlign: "left",
          textDecoration: "none",
          paddingLeft: "1rem !important",
          //paddingRight: "4rem !important",
          margin: "0px !important",
        }}
        primary={
          <Box>
            <Typography fontSize="12px" fontWeight={500}>
              {option.displayName}
            </Typography>
            {option?.description && (
              <Typography
                className="descriptionText"
                fontSize="10px"
                color="text.secondary"
                sx={{
                  opacity: 0,
                  maxHeight: 0,
                  transform: "translateY(-5px)",
                  transition: "opacity 0.3s ease, transform 0.3s ease, max-height 0.3s ease",
                  overflow: "hidden",
                  whiteSpace: "normal",
                  wordBreak: "break-word",
                  maxWidth: "80%",
                  ".MuiAccordionSummary-root:hover &": {
                    opacity: 1,
                    maxHeight: "100px",
                    maxWidth: "80%",
                    transform: "translateY(0)",
                  },
                }}
              >
                {option.description}
              </Typography>
            )}
          </Box>
        }
      />
        </AccordionSummary>
        <AccordionDetails sx={{ padding: "0px !important" }}>
          <List disablePadding>
            {option?.childItems.map((childOption) => (
              <div key={childOption}>
                <SubNavListItem
                  onClickNavigateTo={onClickNavigateTo}
                  option={childOption}
                  mode={mode}
                  handleDrawer={handleDrawer}
                />
              </div>
            ))}
          </List>
          <Typography
            className="descriptionText"
            fontSize="10px"
            color="text.secondary"
            sx={{
              paddingLeft: "2.2rem",
              opacity: 0,
              maxHeight: 0,
              transform: "translateY(-5px)",
              transition: "opacity 0.3s ease, transform 0.3s ease, max-height 0.3s ease",
              overflow: "hidden",
              whiteSpace: "normal",
              wordBreak: "break-word",
              width: "80%",
              maxWidth: "100%",
              overflowWrap: "break-word",
            }}
          >
            {option?.description}
          </Typography>
        </AccordionDetails>
      </Accordion>
    </ListItem>
  );
}

  }
};
const NavListItem = ({
  option,
  handleDrawer,
  updateSideNav,
  setDrawerData,
  handleMoreDrawer,
  mode,
  onClickNavigateNavListItem,
}) => {
  const location = useLocation();
  const onClickNavigateTo = (pathname) => {
    onClickNavigateNavListItem(pathname);
    handleDrawer("close");
    handleMoreDrawer("close");
  };

  const processLocation = (pathname) => {
    if (pathname === "/") {
      return location.pathname === pathname;
    }
    // if (location.pathname == "/" && pathname == '/bpOnboarding') {
    //   return true;
    // }
    else return location.pathname.includes(pathname);
  };

  const isSelected = processLocation(option.routePath);
  if (option.isAccessible && option.isSideOption) {
    if (
      option.childItems.filter((item) => item.isAccessible && item.isSideOption)
        .length !== 0
    )
      return (
        <ListItemButton
          className="sideNavButton"
          onClick={() => {
            handleDrawer();
            handleMoreDrawer("close");
            updateSideNav(option);
            setDrawerData({ module: option, subModule: option.childItems });
          }}
          sx={{
            padding: "0.1px 0 !important",
            marginBottom: "3px !important",
            borderRadius: isSelected ? "8px !important" : "",
            backgroundColor: isSelected ? "#EAE9FF !important" : "",
          }}
        >
            <Tooltip
          title={option?.description}
          arrow
          placement="right"
          componentsProps={{
            tooltip: {
              sx: {
                background: "linear-gradient(135deg, #e3f2fd, #ffffff)", 
                color: "#1a237e",                     
                fontSize: "13px",
                fontWeight: 500,
                border: "1px solid #3730c7",      
                borderRadius: "6px",
                padding: "6px 12px",
                boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
              },
              arrow: {
                sx: {
                  color: "#e3f2fd",
                },
              },
            },
          }}
        >
          <ListItem
            disablePadding
            sx={{
              color: "#000000",
              display: "block",
              justifyContent :"center !important",
              textAlign: "center",
              textDecoration: "none",
              padding:"0px",
            }}
            className="sideNavItem"
          >
            <ReusableIcon isSelected={isSelected} iconName={option.icon} mode={mode}/>
            <Typography
              display="block"
              className="sideNavText"
              
              sx={{
                fontSize: "11px !important",
                justifyContent: "center !important",
                flexGrow: 1,
                textAlign: "center !important",
                textDecoration: "none",
                color: isSelected ? "#3730c7"
                : mode === "dark"
                ? "#ffffff"
                : "#000000",
                fontWeight: isSelected ? "700 !important" : "500 !important",
                borderRadius: isSelected ? "8px !important" : "0px !important",
              }}
            >
              {option.displayName}
            </Typography>
          </ListItem>
        </Tooltip>



        </ListItemButton>
        
      );
    //Render Navigation options without any child option
    else
      return (
        <ListItemButton
          className="sideNavButton"
          key={option.id}
          selected={isSelected}
          style={isSelected ? highlightSelected : {}}
          onClick={() => {
            onClickNavigateTo(option.routePath);
          }}
          sx={{
            padding: "0",
            justifyContent: "center !important",
              marginBottom: "3px !important",
          }}
        >
        <Tooltip
            title={option?.description}
            arrow
            placement="right"
            componentsProps={{
              tooltip: {
                sx: {
                  background: "linear-gradient(135deg, #e3f2fd, #ffffff)", 
                  color: "#1a237e",                  
                  fontSize: "13px",
                  fontWeight: 500,
                  border: "1px solid #3730c7", 
                  borderRadius: "6px",
                  padding: "6px 12px",
                  boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
                },
                arrow: {
                  sx: {
                    color: "#e3f2fd",
                  },
                },
              },
            }}
          >
          <ListItem
            disablePadding
            sx={{
              color: "#000000",
              display: "block",
              justifyContent: "center !important",
              textAlign: "center",
              autofocus: "false",
              textDecoration: "none",
              padding: "0px",
            }}
            className="sideNavItem"
          >
            <ReusableIcon
              isSelected={isSelected}
              iconName={option.icon}
              mode={mode}
            />
            <Typography
              display="block"
              className="sideNavText"
              sx={{
                fontSize: "11px !important",
                justifyContent: "center !important",
                flexGrow: 1,
                textAlign: "center !important",
                textDecoration: "none",
                color: isSelected ? "#3730c7"
                : mode === "dark"
                ? "#ffffff"
                : "#000000",
                fontWeight: isSelected ? "700" : "500",
                borderRadius: isSelected ? "8px" : "0px",
              }}
            >
              {option.displayName}
            </Typography>
          </ListItem>
          </Tooltip>
        </ListItemButton>
      );
  }
};

function SideNavbar(props) {
  const {
    onClickNavigateNavListItem,
    onClickNavDrawerItem,
    onClickMoreNavDrawerItem,
  } = props;
  const [sideNavList, setSideNavList] = useState([]);
  const location = useLocation();
  useEffect(() => {
    if (props?.sideNavOptions) setSideNavList(props?.sideNavOptions);
  }, [props?.sideNavOptions]);
  //SideNav Reshuffling
  const updateSideNav = (option) => {
    var selectedSideNavOption = {};
    if (option) {
      selectedSideNavOption = [option];
    } else {
      selectedSideNavOption = sideNavList?.data?.filter((option) => {
        if (option.routePath !== "/") {
          return location.pathname.includes(option.routePath);
        }
      });
    }

    if (selectedSideNavOption) {
      if (
        selectedSideNavOption[0]?.id > sideNavList.configuration.moreOptions
      ) {
        var updatedSideNavOptions = [];
        var index = 1;
        sideNavList?.data?.map((option) => {
          var tempOption = {};
          if (option.id === selectedSideNavOption[0]?.id) {
            return;
          }
          if (option.id === sideNavList.configuration.moreOptions) {
            tempOption = { ...selectedSideNavOption[0], id: index };
            index = index + 1;
            updatedSideNavOptions.push(tempOption);
            tempOption = { ...option, id: index };
            updatedSideNavOptions.push(tempOption);
          } else {
            tempOption = { ...option, id: index };
            updatedSideNavOptions.push(tempOption);
          }
          index = index + 1;
        });
        updatedSideNavOptions.sort((a, b) => a.id - b.id);
        setSideNavList({ ...sideNavList, data: updatedSideNavOptions });
      }
    }
  };

  useEffect(() => {
    updateSideNav();
  }, [location]);

  const DrawerLocal = ({
    handleDrawer,
    setDrawerData,
    entitesAndActivities,
    handleMoreDrawer,
    mode,
  }) => {
    return (
      <>
        <List className="drawerList">
          {sideNavList &&
            sideNavList?.data
              ?.slice(0, sideNavList.configuration.moreOptions)
              .map((option) => {
                if (
                  option.isAccessible &&
                  option.isSideOption &&
                  entitesAndActivities[option.iwaName]
                ) {
                  return (
                    <NavListItem
                      key={option.iwaName}
                      handleDrawer={handleDrawer}
                      option={option}
                      updateSideNav={updateSideNav}
                      setDrawerData={setDrawerData}
                      handleMoreDrawer={handleMoreDrawer}
                      mode={mode}
                      onClickNavigateNavListItem={onClickNavigateNavListItem}
                    />
                  );
                }
              })}
          {sideNavList?.data?.length >
            sideNavList?.configuration?.moreOptions && (
            <ListItemButton
              className="sideNavButton"
              onClick={() => {
                handleDrawer("close");
                handleMoreDrawer("open");
              }}
              sx={{
                padding: "0 !important",
              }}
            >
              <ListItem
                disablePadding
                sx={{
                  color: "#000000",
                  display: "block",

                  textAlign: "center",
                  autofocus: "false",
                  textDecoration: "none",
                }}
                className="sideNavItem"
              >
                <ReusableIcon mode={mode} iconName="MoreHoriz" />
                <Typography
                  className="clsasaassd"
                  display="block"
                  sx={{
                    fontSize: 11,
                    justifyContent: "center",
                    flexGrow: 1,
                    textAlign: "center !important",
                    textDecoration: "none",
                    color:
                      (window.location.pathname === "/supplier/more" ||
                      mode === "dark") ? "#ffffff" : "#000000",
                    fontWeight:
                      window.location.pathname === "/supplier/more"
                        ? "700"
                        : "500",

                    // color: window.location.pathname  === '/purchaseOrder' ? "#3730c7":"#000000",
                    // fontWeight: window.location.pathname  === '/purchaseOrder' ? "700" : "500"
                  }}
                >
                  More
                </Typography>
              </ListItem>
            </ListItemButton>
          )}
        </List>
      </>
    );
  };

  const [navState, setNavState] = useState(false);
  const [moreNavState, setMoreNavState] = useState(false);
  const [drawerData, setDrawerData] = useState({ module: {}, subModule: [] });
  const [moreDrawerData, setMoreDrawerData] = useState({
    module: {},
    subModule: [],
  });
  const handleDrawer = (state) => {
    if (state === "open") setNavState(true);
    else if (state === "close") setNavState(false);
    else setNavState(!navState);
  };
  const handleMoreDrawer = (state) => {
    if (state === "open") setMoreNavState(true);
    else if (state === "close") setMoreNavState(false);
    else setMoreNavState(!navState);
  };

  return (
    <div className="sideNavbar">
      <Drawer className="drawerBase" variant="permanent"
       sx={{
        "& .MuiPaper-root": {
          background: props?.mode === "dark" ? "#170E5E" : "#ffffff",
        },
      }}
      >
        {props?.entitesAndActivities && (
          <DrawerLocal
            setDrawerData={setDrawerData}
            handleDrawer={handleDrawer}
            entitesAndActivities={props?.entitesAndActivities}
            setMoreDrawerData={setMoreDrawerData}
            handleMoreDrawer={handleMoreDrawer}
            mode={props?.mode}
          />
        )}
      </Drawer>
      {props?.entitesAndActivities && (
        <NavDrawer
          navState={navState}
          handleDrawer={handleDrawer}
          drawerData={drawerData}
          entitesAndActivities={props?.entitesAndActivities}
          handleMoreDrawer={handleMoreDrawer}
          mode={props?.mode}
          onClickNavDrawerItem={onClickNavDrawerItem}
        />
      )}
      {props?.entitesAndActivities && (
        <MoreNavDrawer
          moreNavState={moreNavState}
          handleMoreDrawer={handleMoreDrawer}
          entitesAndActivities={props?.entitesAndActivities}
          handleDrawer={handleDrawer}
          sideNavList={sideNavList}
          setDrawerData={setDrawerData}
          updateSideNav={updateSideNav}
          mode={props?.mode}
          onClickMoreNavDrawerItem={onClickMoreNavDrawerItem}
        />
      )}
      
    </div>
  );
}

export default SideNavbar;
