import {
  CheckCircleOutline,
  Close,
  CloseFullscreen,
  DangerousOutlined,
  InfoOutlined,
  WarningAmberOutlined,
} from "@mui/icons-material";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { useState } from "react";
import { useDispatch } from "react-redux";
import ReusableDataTable from "./ReusableTable";
import WarningIcon from "@mui/icons-material/Warning";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";

function ObjectLockDialog({ duplicateFieldsArr, moduleName, open, onClose }) {
  const renderWithTooltip = (value) => {
    const valuesArray = Array.isArray(value)
      ? value
      : value?.split(",").map((v) => v.trim()) || [];
    const displayValue = valuesArray[0] || "";

    return (
      <Box display="flex" alignItems="center">
        <Typography variant="body2" noWrap>
          {displayValue}
        </Typography>
        {valuesArray.length > 1 && (
          <Tooltip title={valuesArray.join(", ")} arrow>
            <InfoOutlinedIcon
              sx={{ ml: 0.5, fontSize: "18px", color: "#888" }}
            />
          </Tooltip>
        )}
      </Box>
    );
  };

  const duplicateFieldsColumns = [
    {
      field: "objectNo",
      headerName: moduleName,
      editable: false,
      flex: 1,
      width: 150,
    },
    {
      field: "reqId",
      headerName: "Req Id",
      editable: false,
      flex: 1,
      width: 200,
      renderCell: (params) => renderWithTooltip(params.row.reqId),
    },
    {
      field: "childReqId",
      headerName: "Child Req Id",
      editable: false,
      flex: 1,
      width: 200,
      renderCell: (params) => renderWithTooltip(params.row.childReqId),
    },
    {
      field: "requestedBy",
      headerName: "Requested By",
      editable: false,
      flex: 1,
      width: 250,
      renderCell: (params) => renderWithTooltip(params.row.requestedBy),
    },
  ];

  return (
    <>
      <Dialog open={open} onClose={() => onClose()} maxWidth="xl" fullWidth>
        <DialogTitle sx={{ bgcolor: "#FFDAB9", color: "warning.contrastText" }}>
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <span>
              <WarningIcon sx={{ mr: 1 }} /> Duplicate Requests Alert
            </span>
          </Typography>
        </DialogTitle>
        <DialogContent>
          <div style={{ marginTop: "20px" }}>
            <ReusableDataTable
              height={400}
              rows={duplicateFieldsArr}
              columns={duplicateFieldsColumns}
              rowCount={duplicateFieldsArr.length ?? 0}
              getRowIdValue={"id"}

              // Note: Will be used for pagination

              // page={pageForDuplicateFields}
              // pageSize={pageSizeForDuplicateFields}
              // onPageChange={handlePageChangeForDuplicateFields}
              // onPageSizeChange={handlePageSizeChangeForDuplicateFields}
              // showCustomNavigation={true}
              // hideFooter={true}
              // checkboxSelection={false}
              // disableSelectionOnClick={true}
              // status_onRowSingleClick={true}
              // stopPropagation_Column={"action"}
              // status_onRowDoubleClick={true}
              // getRowIdValue={(row) => row.id}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <Button variant="contained" onClick={onClose}>
            OK
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default ObjectLockDialog;
