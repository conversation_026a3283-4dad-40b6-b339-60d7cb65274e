import React from 'react';
import { Grid, Box, Paper, Skeleton, Stack } from '@mui/material';
const ProgressItemSkeleton = () => (
  <Box
    sx={{
      mb: 2,
      p: 1,
      border: '1px solid #E2E8F0',
      borderRadius: 2,
      bgcolor: '#F8FAFC'
    }}
  >
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
      <Box sx={{ mr: 1 }}>
        <Skeleton variant="circular" width={24} height={24} />
      </Box>
      <Box sx={{ width: '100%' }}>
        <Skeleton variant="text" width="40%" />
        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
          <Skeleton variant="rectangular" width={120} height={8} sx={{ mr: 1 }} />
          <Skeleton variant="text" width="20%" />
        </Box>
      </Box>
    </Box>
  </Box>
);
const TaskCardSkeleton = () => (
  <Paper
    elevation={1}
    sx={{
      p: 2,
      mb: 3,
      border: '1px solid #E2E8F0',
      borderRadius: 3,
      bgcolor: '#FFFFFF',
      boxShadow: '0 2px 6px rgba(0,0,0,0.03)'
    }}
  >
    <Stack spacing={2}>
      <Stack direction="row" alignItems="center" spacing={2}>
        <Skeleton variant="text" width="30%" />
        <Skeleton variant="rectangular" width={2} height={24} />
        <Stack direction="row" alignItems="center" spacing={1}>
          <Skeleton variant="circular" width={24} height={24} />
          <Skeleton variant="text" width={80} />
          <Skeleton variant="text" width={120} />
        </Stack>
      </Stack>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Skeleton variant="text" width="25%" />
        <Skeleton variant="rectangular" width={80} height={24} />
      </Box>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Skeleton variant="text" width="20%" />
          <Stack direction="row" spacing={1}>
            <Skeleton variant="rectangular" width={200} height={32} />
          </Stack>
        </Grid>
        <Grid item xs={12} md={6}>
          <Skeleton variant="text" width="20%" />
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Skeleton variant="circular" width={24} height={24} sx={{ mr: 1 }} />
            <Box>
              <Skeleton variant="text" width={150} />
              <Skeleton variant="text" width={100} />
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Stack>
  </Paper>
);
const RequestHistorySkeleton = () => (
  <Grid container spacing={2}>
    <Grid item xs={12}>
      <Grid container spacing={2}>
        {/* Left Progress Panel */}
        <Grid item xs={12} md={4} lg={3}>
          <Paper
            elevation={0}
            sx={{
              p: 2,
              border: '1px solid #E2E8F0',
              borderRadius: 3,
              bgcolor: '#F9FAFB'
            }}
          >
            {[...Array(5)].map((_, index) => (
              <ProgressItemSkeleton key={index} />
            ))}
          </Paper>
        </Grid>
        {/* Right Task Section */}
        <Grid item xs={12} md={8} lg={9}>
          <Paper
            elevation={0}
            sx={{
              p: 2,
              border: '1px solid #E2E8F0',
              borderRadius: 3,
              bgcolor: '#FFFFFF'
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Box>
                <Skeleton variant="text" width={200} />
                <Skeleton variant="text" width={100} />
              </Box>
              <Skeleton variant="rectangular" width={100} height={36} />
            </Box>
            {[...Array(1)].map((_, index) => (
              <TaskCardSkeleton key={index} />
            ))}
          </Paper>
        </Grid>
      </Grid>
    </Grid>
  </Grid>
);
export default RequestHistorySkeleton;