/* .css-12i7wg6-Mu<PERSON><PERSON><PERSON>-root-Mu<PERSON>Drawer-paper{
    box-shadow: 0 4px 8px 0 rgba(34, 34, 34, 0.049), 0 6px 20px 0 rgba(0, 0, 0, 0.148) !important;
  }
  .css-7bir1d-Mui<PERSON><PERSON>-root-MuiAppBar-root {
    box-shadow: 0 4px 8px 0 rgba(34, 34, 34, 0.049), 0 6px 20px 0 rgba(0, 0, 0, 0.148) !important;
  } */
  .logo {
    margin-left: 0.0vw;
    /* padding-top: 4px !important */
  }
  .headerCell {
    background-color: #f1f5fe !important;
    color:"black" !important ;
    font-weight: 700 !important;
  }
  
  .cell {
  text-align: center !important;
  color:"black" !important ;
  }

  .notifications	.MuiCardHeader-title{
    font-size: 1rem;
    font-weight: bold;
}
.css-1c32n2y-MuiBadge-root {
  color: #424242;
}

.css-12i7wg6-MuiP<PERSON>-root-<PERSON><PERSON><PERSON>rawer-paper {
  width: 5vw;
}
