export const treeTheme = {
  colors: {
    primary: "#1890ff",
    success: "#52c41a",
    warning: "#faad14",
    error: "#ff4d4f",
    info: "#13c2c2",
    parentNode: "#2c3e50",
    leafNode: "#34495e",
    tagNode: "#7f8c8d",
    primaryBg: "#f0f8ff",
    successBg: "#f6ffed",
    warningBg: "#fffbe6",
    errorBg: "#fff2f0",
    infoBg: "#e6fffb",
    primaryBorder: "#d9e8fc",
    successBorder: "#b7eb8f",
    warningBorder: "#ffe58f",
    errorBorder: "#ffccc7",
    infoBorder: "#87e8de",
    textPrimary: "#262626",
    textSecondary: "#595959",
    textDisabled: "#bfbfbf",
    hoverBg: "#f5f5f5",
    activeBg: "#e6f7ff",
    selectedBg: "#bae7ff",
  },
  spacing: {
    xs: "5px",
    sm: "8px",
    md: "12px",
    lg: "16px",
    xl: "24px",
    xxl: "32px",
  },
  borderRadius: {
    sm: "4px",
    md: "6px",
    lg: "8px",
  },
  shadows: {
    sm: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
    md: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
    lg: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
  },
  transitions: {
    default: "all 0.3s ease",
    fast: "all 0.15s ease",
    slow: "all 0.5s ease",
  },
};

export const treeStyles = {
  container: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    fontSize: "14px",
    lineHeight: 1.5,
    padding: "16px",
    backgroundColor: "#fafafa",
  },
  searchContainer: {
    display: "flex",
    marginBottom: treeTheme.spacing.lg,
    gap: treeTheme.spacing.sm,
    alignItems: "center",
  },
  actionButtonsContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: treeTheme.spacing.lg,
    padding: treeTheme.spacing.sm,
    backgroundColor: treeTheme.colors.primaryBg,
    borderRadius: treeTheme.borderRadius.md,
    border: `1px solid ${treeTheme.colors.primaryBorder}`,
  },
  actionButton: {
    borderRadius: treeTheme.borderRadius.md,
    boxShadow: treeTheme.shadows.sm,
    transition: treeTheme.transitions.default,
    fontWeight: 500,
  },
  treeContainer: {
    backgroundColor: "#fff",
    borderRadius: treeTheme.borderRadius.lg,
    border: `1px solid ${treeTheme.colors.primaryBorder}`,
    boxShadow: treeTheme.shadows.md,
    padding: "16px",
  },
  nodeTitle: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    padding: `${treeTheme.spacing.xs} ${treeTheme.spacing.sm}`,
    borderRadius: treeTheme.borderRadius.sm,
    transition: treeTheme.transitions.default,
  },
  nodeContent: {
    display: "flex",
    gap: treeTheme.spacing.md,
    alignItems: "center",
    flex: 1,
  },
  nodeIcon: {
    fontSize: "16px",
    minWidth: "20px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  nodeLabel: {
    fontWeight: 600,
    minWidth: "80px",
    color: treeTheme.colors.textPrimary,
    fontSize: "14px",
  },
  nodeDescription: {
    color: treeTheme.colors.textSecondary,
    fontSize: "13px",
    fontStyle: "italic",
    flex: 1,
  },
  tagTitle: {
    display: "flex",
    alignItems: "center",
    gap: treeTheme.spacing.sm,
    padding: `${treeTheme.spacing.xs} ${treeTheme.spacing.sm}`,
    borderRadius: treeTheme.borderRadius.sm,
    transition: treeTheme.transitions.default,
  },
  tagIcon: {
    fontSize: "14px",
    color: treeTheme.colors.info,
  },
  tagLabel: {
    color: treeTheme.colors.info,
    fontSize: "13px",
    fontWeight: 500,
  },
  nodeActionButtons: {
    display: "flex",
    gap: treeTheme.spacing.xs,
    alignItems: "center",
    opacity: 0,
    transition: treeTheme.transitions.default,
    paddingLeft: "20px",
  },
  actionIconButton: {
    border: "none",
    boxShadow: "none",
    borderRadius: "50%",
    width: "28px",
    height: "28px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    transition: treeTheme.transitions.fast,
  },
  searchHighlight: {
    backgroundColor: "#ffeb3b",
    color: "#d32f2f",
    fontWeight: 600,
    padding: "1px 2px",
    borderRadius: "2px",
  },
  moveAlert: {
    padding: treeTheme.spacing.lg,
    marginBottom: treeTheme.spacing.lg,
    backgroundColor: treeTheme.colors.warningBg,
    border: `1px solid ${treeTheme.colors.warningBorder}`,
    borderRadius: treeTheme.borderRadius.md,
    display: "flex",
    alignItems: "center",
    gap: treeTheme.spacing.sm,
    boxShadow: treeTheme.shadows.sm,
  },
};

export const globalTreeCSS = `
.hierarchy-tree-container .ant-tree-node-content-wrapper:hover .node-action-buttons {
  opacity: 1 !important;
}
.hierarchy-tree-container .parent-node > .ant-tree-node-content-wrapper {
  font-weight: 600;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
.hierarchy-tree-container .tag-node > .ant-tree-node-content-wrapper {
  background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%);
  border-left: 3px solid ${treeTheme.colors.info};
  margin-left: 8px;
}
.hierarchy-tree-container .leaf-node > .ant-tree-node-content-wrapper {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
}
.hierarchy-tree-container .ant-tree-switcher {
  background: none !important;
}
.hierarchy-tree-container .ant-tree-node-selected > .ant-tree-node-content-wrapper {
  background: ${treeTheme.colors.selectedBg} !important;
  border-radius: ${treeTheme.borderRadius.sm};
}
.hierarchy-tree-container .ant-tree-treenode {
  padding: 2px 0;
}
.hierarchy-tree-container .ant-tree-node-content-wrapper {
  border-radius: ${treeTheme.borderRadius.sm};
  transition: ${treeTheme.transitions.default};
  min-height: 32px;
  display: flex;
  align-items: center;
}
.hierarchy-tree-container .ant-tree-node-content-wrapper:hover {
  background: ${treeTheme.colors.hoverBg} !important;
}
`;