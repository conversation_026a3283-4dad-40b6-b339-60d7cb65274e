import React, { Component } from "react";
import MenuItem from "@mui/material/MenuItem";
import Grid from "@mui/material/Grid";
import InputLabel from "@mui/material/InputLabel";
import Select from "@mui/material/Select";
import FormControl from "@mui/material/FormControl";
import UIelements from "./component/CwUIELEMENT";
import Message from "./component/CwMessagetext";
import fetchWrapper from "./utility/fetchWrapper";
import BusyLoader from "./component/busyLoader";
class CwWorkText extends Component {
  constructor(props) {
    super(props);
    this.destinations = Array.isArray(props.destinations) ? props.destinations.map((ele) => ({ [ele.Name]: ele.URL })).reduce((acc, current) => ({ ...acc, ...current }), {}) : props.destinations ;
    this.state = {
      value: "UI Element",
      showUIElement: true,
      showmessage: false,
      application: [this.props.appName, "GLOBAL"],
      showTable: this.props.showPropList,
      properties: [],
      showproperty: !this.props.showPropList,
      showmap: false,
      showmessageDetails: false,
      languages: [],
      selectedlanguage: "EN",
      messagetextproperty: [],
      finalmessagetextproperty: [],
      finalproperties: [],
      configuration: "UI Element",
      applicationName: this.props.appName,
      propertyName: this.props.propertyName,
      propertyDescription: '',
      user_id:this.props.userDetails.user_id,
      authorization:this.props.userDetails.authorization
    };
  }
  changeDescription = (value) => {
    this.state = { ...this.state, propertyDescription: value }
  }
  tabChangeHandler = (event) => {
    if(this.props.showPropList){
    if (event === "UI Element") {
      fetchWrapper(
        "/CW_Worktext/translation/getPropertiesTranslations?applicationName=" +
        this.state.applicationName +
        "&elementName=UI&languageCode=EN",{}<this.destinations
      )
        .then((res) => res.json())
        .then((json) => {
          this.setState({
            ...this.state,
            isLoaded: true,
            properties: json.data,
            finalproperties: json.data,
            showUIElement: true,
            showmessage: false,
            configuration: event,
            showTable: true,
          });
        });
    } else {
      fetchWrapper(
        "/CW_Worktext/message/getMessagesTranslations?applicationName=" +
        this.state.applicationName +
        "&elementName=MESSAGE_TEXT&languageCode=EN",{},this.destinations
      )
        .then((res) => res.json())
        .then((json) => {
          this.setState({
            ...this.state,
            isLoaded: true,
            messagetextproperty: json.data,
            finalmessagetextproperty: json.data,
            showUIElement: false,
            showmessage: true,
            configuration: event,
          });
        });
    }}
  };

  handleChange = (event, newValue) => {
    this.setState({
      ...this.state,
      value: newValue,
    });
  };
  changeshowtable = () => {
    this.setState({
      ...this.state,
      showTable: !this.state.showTable,
      showproperty: this.state.showTable,
      showmessageDetails: this.state.showTable,
    });
  };
  onChangeSearchQuery = (evt) => {
    let tempproperties = this.state.finalproperties.filter((app) =>
      app.propertyName.includes(evt.target.value)
    );
    this.setState({
      ...this.state,
      properties: tempproperties,
    });
  };
  onChangeSearchQuerymessage = (evt) => {
    let tempmessage = this.state.finalmessagetextproperty.filter((app) =>
      app.usageArea.includes(evt.target.value)
    );
    this.setState({
      ...this.state,
      messagetextproperty: tempmessage,
    });
  };
  changeapplication = (option) => {
    fetchWrapper(
      "/CW_Worktext/message/getMessagesTranslations?applicationName=" +
      option +
      "&elementName=MESSAGE_TEXT&languageCode=EN",{},this.destinations
    )
      .then((res) => res.json())
      .then((json) => {
        this.setState({
          ...this.state,
          messagetextproperty: json.data,
          finalmessagetextproperty: json.data,
          selectedlanguage: "EN",
        });
      });
    fetchWrapper(
      "/CW_Worktext/translation/getPropertiesTranslations?applicationName=" +
      option +
      "&elementName=UI&languageCode=EN",{},this.destinations
    )
      .then((res) => res.json())
      .then((json) => {
        this.setState({
          ...this.state,
          isLoaded: true,
          properties: json.data,
          finalproperties: json.data,

          applicationName: option,
          selectedlanguage: "EN",
          showTable: true,
          showmessageDetails: false,
          showproperty: false,
          propertyDescription: "new"
        });
      });
  };
  showmapprops = () => {
    this.setState({
      ...this.state,
      showmap: !this.state.showmap,
      showTable: !this.state.showTable,
    });
  };
  editscreen = () => {
    this.setState({
      ...this.state,
      showmap: false,
      showTable: false,
      showproperty: true,
      showmessageDetails: true,
    });
  };
  componentDidMount() {
    let headers = {
      ContentType: "application/json",
      Accept: "application/json",
     
      Authorization:this.state.authorization
    };
    // fetch("CW_Worktext/application/getApplications")
    //   .then((res) => res.json())
    //   .then((json) => {
    //     this.setState({
    //       ...this.state,
    //       isLoaded: true,
    //       application: json.data,
    //     });
    //   });

    fetchWrapper("/CW_Worktext/language/getLanguages",{headers:headers},this.destinations)
      .then((res) => res.json())
      .then((json) => {
        this.setState({
          ...this.state,
          isLoaded: true,
          languages: json.data,
        });
      });
     
      if(this.props.showPropList){
        this.setState({
          ...this.state,
         
          showBusyLoader:true
        });
    fetchWrapper(
      "/CW_Worktext/translation/getPropertiesTranslations?applicationName=" +
      this.props.appName +
      "&elementName=UI&languageCode=EN",{headers:headers},this.destinations
    )
      .then((res) => res.json())
      .then((json) => {
        this.setState({
          ...this.state,
          isLoaded: true,
          properties: json.data,
          finalproperties: json.data,
          showUIElement: true,
          showmessage: false,
          applicationName: this.props.appName,
          configuration: "UI Element",
          propertyDescription: json.data && json.data[0] ? json.data[0].propertyDescription : '',
          showBusyLoader:false
        });
      });}
  }
  changelanguagemessage = (option) => {
    fetchWrapper(
      "/CW_Worktext/message/getMessagesTranslations?applicationName=" +
      this.state.applicationName +
      "&elementName=MESSAGE_TEXT&languageCode=" +
      option,{},this.destinations
    )
      .then((res) => res.json())
      .then((json) => {
        this.setState({
          ...this.state,
          messagetextproperty: json.data,
          finalmessagetextproperty: json.data,
        });
      });
  };
  changelanguage = (option) => {
    fetchWrapper(
      "/CW_Worktext/translation/getPropertiesTranslations?applicationName=" +
      this.state.applicationName +
      "&elementName=UI&languageCode=" +
      option,{},this.destinations
    )
      .then((res) => res.json())
      .then((json) => {
        this.setState({
          ...this.state,
          properties: json.data,
          finalproperties: json.data,
          selectedlanguage: option,
        });
      });
  };

  render() {
    const userAccess = this.props.userAccess;
    return (
      <div style={{ height: '100%' }}>
        <Grid container style={{ margin: 0 }}>
          {this.state.showTable && (
            <Grid item xs={8} sm={3} md={4}>
              <FormControl
                style={{ width: 180, marginTop: "1rem", marginLeft: "2rem" }}
                variant="outlined"
                size="small"
              >
                <InputLabel id="demo-simple-select-outlined-label" required>
                  Worktext configuration
                </InputLabel>
                <Select
                  labelId="demo-simple-select-outlined-label"
                  id="demo-simple-select-outlined"
                  //value={row.applicationId}
                  // size="small"
                  name="worktextconfig"
                  onChange={(evt) => this.tabChangeHandler(evt.target.value)}
                  value={this.state.configuration}
                >
                  <MenuItem value="UI Element" key="1">
                    UI Element
                  </MenuItem>
                  <MenuItem value="Messsage Text" key="2">
                    Message Text
                  </MenuItem>
                </Select>
              </FormControl>
            </Grid>
          )}


          {this.state.showTable && (
            <Grid>
              <FormControl
                style={{ width: 180, marginLeft: "1rem", marginTop: "0.5rem" }}
                variant="outlined"
                size="small"
              >
                <InputLabel id="demo-simple-select-outlined-label" required>
                  Application
                </InputLabel>
                <Select
                  labelId="demo-simple-select-outlined-label"
                  id="demo-simple-select-outlined"
                  //value={row.applicationId}
                  // size="small"
                  name="application"
                >
                  {this.state.application.map((option, index) => (
                    <MenuItem
                      value={option.applicationName}
                      onClick={() =>
                        this.changeapplication(option.applicationName)
                      }
                    >
                      {option.applicationDescription}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          )}
        </Grid>
        {this.state.showUIElement && (
          <UIelements
            {...this.props}
            authorization={this.state.authorization}
            applicationName={this.state.applicationName}
            showmapprops={() => this.showmapprops()}
            showmap={this.state.showmap}
            propertyName={this.state.propertyName}
            showproperty={this.state.showproperty}
            properties={this.state.properties}
            changelanguage={(option) => this.changelanguage(option)}
            languages={this.state.languages}
            selectedlanguage={this.state.selectedlanguage}
            editscreen={() => this.editscreen()}
            changeshowtable={() => this.changeshowtable()}
            changeapplication={(option) => this.changeapplication(option)}
            showtable={this.state.showTable}
            application={this.state.application}
            onChangeSearchQuery={(evt) => this.onChangeSearchQuery(evt)}
            propertyDescription={this.state.propertyDescription}
            changeDescription={(value)=>this.changeDescription(value)}
            destinations={this.destinations}
          />
        )}
        {this.state.showmessage && (
          <Message
            {...this.props}
            applicationName={this.state.applicationName}
            showmapprops={() => this.showmapprops()}
            showmap={this.state.showmap}
            showmessageDetails={this.state.showmessageDetails}
            messagetextproperty={this.state.messagetextproperty}
            changelanguage={(option) => this.changelanguagemessage(option)}
            selectedlanguage={this.state.selectedlanguage}
            languages={this.state.languages}
            editscreen={() => this.editscreen()}
            changeapplication={(option) => this.changeapplication(option)}
            changeshowtable={() => this.changeshowtable()}
            showtable={this.state.showTable}
            application={this.state.application}
            onChangeSearchQuerymessage={(evt) =>
              this.onChangeSearchQuerymessage(evt)
            }
            destinations={this.destinations}
          />
        )}
        {this.state.showBusyLoader && <BusyLoader show={this.state.showBusyLoader} />}
      </div>
    );
  }
}

export default CwWorkText;
