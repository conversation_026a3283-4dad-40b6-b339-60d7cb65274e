import { setCostCenterTabs,setCostCenterConfig,setCostCenterData } from "@app/costCenterTabsSlice";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { groupBy, removeHiddenAndEmptyObjects, transformStructureForAllTabsData } from "@helper/helper";
import { destination_IDM } from "../destinationVariables";
import { doAjax } from "@components/Common/fetchService";
import { API_CODE, REQUEST_TYPE, TASK_NAME, VISIBILITY_TYPE } from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "./useLogger";

const transformCostCenterFieldConfigData = (responseData) => {
    let mandatoryFields = {};
    let sortedData = responseData?.sort((a, b) => a.MDG_CC_SEQUENCE_NO - b.MDG_CC_SEQUENCE_NO);
    const groupedFields = groupBy(sortedData, "MDG_CC_VIEW_NAME");
  
    let view_data_array = [];
    Object.entries(groupedFields).forEach(([viewName, fields]) => {
      let groupedFieldsDataCardNameWise = groupBy(fields, "MDG_CC_CARD_NAME");
      let cards = [];
  
      Object.entries(groupedFieldsDataCardNameWise).forEach(([cardName, cardFields]) => {
        cardFields.sort((a, b) => a.MDG_CC_SEQUENCE_NO - b.MDG_CC_SEQUENCE_NO);
  
        let cardDetails = cardFields.map((item) => ({
          fieldName: item.MDG_CC_UI_FIELD_NAME,
          sequenceNo: item.MDG_CC_SEQUENCE_NO,
          fieldType: item.MDG_CC_FIELD_TYPE,
          maxLength: item.MDG_CC_MAX_LENGTH,
          dataType: item.MDG_CC_DATA_TYPE,
          viewName: item.MDG_CC_VIEW_NAME,
          cardName: item.MDG_CC_CARD_NAME,
          cardSeq: item.MDG_CC_CARD_SEQUENCE,
          value: item.MDG_CC_DEFAULT_VALUE,
          visibility: item.MDG_CC_VISIBILITY,
          jsonName: item.MDG_CC_JSON_FIELD_NAME,
        }));
  
        cards.push({
          cardName,
          cardSeq: cardFields[0].MDG_CC_CARD_SEQUENCE,
          cardDetails,
        });
      });
  
      cards.sort((a, b) => a.cardSeq - b.cardSeq);
      view_data_array.push({ viewName, cards });
    });
  
    let filteredData = removeHiddenAndEmptyObjects(view_data_array);
    let transformedData = {};
    filteredData.forEach((view) => {
      let cardData = {};
      view.cards.forEach((card) => {
        cardData[card.cardName] = card.cardDetails;
        if (view.viewName !== "Request Header") {
          card.cardDetails.forEach((detail) => {
            if (detail.visibility === VISIBILITY_TYPE.MANDATORY) {
              if (!mandatoryFields[detail.viewName]) {
                mandatoryFields[detail.viewName] = [];
              }
              mandatoryFields[detail.viewName].push({ jsonName: detail?.jsonName, fieldName: detail?.fieldName });
            }
          });
        }
      });
      transformedData[view.viewName] = cardData;
    });
  
    return { transformedData, mandatoryFields };
  };
  
  const useCostCenterFieldConfig = () => {
    const dispatch = useDispatch();
    const { customError } = useLogger();
    const initialPayload = useSelector((state) => state.payload?.payloadData);
    const applicationConfig = useSelector((state) => state.applicationConfig);
    const userData = useSelector((state) => state.userManagement.userData);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
  
    const getTemplateData = async () => {
      setLoading(true);
      const payload = {
        decisionTableId: null,
        decisionTableName: "MDG_CC_FIELD_CONFIG",
        version: "v3",
        rulePolicy: null,
        validityDate: null,
        conditions: [
          {
            "MDG_CONDITIONS.MDG_CC_SCENARIO": initialPayload?.RequestType || REQUEST_TYPE.CREATE,
            "MDG_CONDITIONS.MDG_CC_ROLE": TASK_NAME.REQ_INITIATE_FIN,
          },
        ],
        systemFilters: null,
        systemOrders: null,
        filterString: null,
      };
  
      const hSuccess = (data) => {
        if (data.statusCode === API_CODE.STATUS_200) {
          if (Array.isArray(data?.data?.result) && data?.data?.result.every(item => Object.keys(item).length !== 0)) {
            let responseData = data?.data?.result[0]?.MDG_CC_FIELD_DETAILS_ACTION_TYPE;
            const { transformedData, mandatoryFields } = transformCostCenterFieldConfigData(responseData);
            let ccTabsData = Object.keys(transformedData);  
            const allTabsData = ccTabsData.map((tab) => ({
                tab,
                data: transformedData[tab],
              }));
              
              dispatch(setCostCenterTabs(allTabsData));
  
            dispatch(setCostCenterConfig({ CostCenter: { allfields: transformStructureForAllTabsData(ccTabsData), mandatoryFields } }));
          } else {
            dispatch(setCostCenterData({ CostCenter: {} }));
          }
          setLoading(false);
        }
      };
  
      const hError = (error) => {
        customError(error);
        setError(error);
        setLoading(false);
      };
  
      const url =
        applicationConfig.environment === "localhost"
          ? `/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}`
          : `/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`;
  
      doAjax(url, "post", hSuccess, hError, payload);
    };
  
    const fetchCostCenterFieldConfig = () => {
      try {
        getTemplateData();
      } catch (err) {
        setError(err);
        setLoading(false);
      }
    };
  
    return { loading, error, fetchCostCenterFieldConfig };
  };
  
  export default useCostCenterFieldConfig;
  


