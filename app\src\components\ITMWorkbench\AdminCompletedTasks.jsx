// import configData from "../../data/configData";
// import destinationData from "../../data/destinationData";
// import { returnUserGroupMap } from "../../data/userGroupData";
// import { returnUserMap } from "../../data/userData";
// import { accessToken, userPermissions } from "../../data/propData";
// import Workspace from "@cw/cherrywork-itm";
import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
// import { destination_IWA } from "../../destinationVariables";
import WorkspaceComponent from "@cw/cherrywork-iwm-workspace/Workspace";
import configDataForCompletedTasks from "../../data/configDataForCompletedTasks";
import destinationData from "../../data/destinationData";
import { returnUserGroupMap } from "../../data/userGroupData";
import { returnUserMap } from "../../data/userData";
import { accessToken, userData, userPermissions } from "../../data/propData";

// Workflow imports
import Workflow from "@cw/cherrywork-iwm-workspace/Workflow";
// import destinationData from "../../data/destinationData";
// import { userRawData, returnUserMap } from "../../data/userData";
// import { accessToken, userData } from "../../data/propData";
// import { task } from "../../data/taskData";

// Activity Logs imports
// import ActivityLogs from "@cw/cherrywork-itm-workspace/ActivityLogs";
// import destinationData from "../../data/destinationData";
// import { userRawData, returnUserMap } from "../../data/userData";
// import { accessToken, userData } from "../../data/propData";
import { task } from "../../data/taskData";
import { useNavigate } from "react-router-dom";
import { setTaskData } from "../../app/userManagementSlice";
import { baseUrl_ITMJava } from "../../data/baseUrl";
import { doAjax } from "../Common/fetchService";
import { destination_IWA_NPI } from "../../destinationVariables";
import configData from "../../data/configData";

export default function AdminCompletedTasks() {
  let userData = useSelector((state) => state.userManagement?.userData);
  let groupData = useSelector((state) => state.userManagement?.groups);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  let dispatch = useDispatch();
  const navigate = useNavigate();
  const [userRawData, setUserRawData] = useState(null);
  const [userGroupRawData, setUserGroupRawData] = useState(null);
  const [userListBySystem, setUserListBySystem] = useState(null);

  const DestinationConfig = {
    APPLICATION_NAME: "1784",
    CRUD_API_ENV: "itm",
    DB_TYPE: "hana",
    SERVICE_BASE_URL: [
      {
        Description: "",
        Name: "ITMJavaServices",
        URL: "https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      // {
      //   Description: "",
      //   Name: "IWAServices",
      //   URL: "https://cw-mdg-authentication-dev.cfapps.eu10-004.hana.ondemand.com",
      // },
      {
        Description: "",
        Name: "ConfigServer",
        URL: "https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "WorkNetServices",
        URL: "https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "CrudApiServices",
        URL: "https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "WorkFormsServices",
        URL: "https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms",
      },
      {
        Description: "",
        Name: "NotificationServices",
        URL: "https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "ITMGraphServices",
        URL: "https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "Native Workflow Services",
        Name: "NativeWorkflowServices",
        URL: "https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "Native Workflow UI URL",
        Name: "NativeWorkflowUiUrl",
        URL: "https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui",
      },
      {
        Description: "",
        Name: "OnboardingServices",
        URL: "https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com",
      },
    ],
  };

  const userPrefData = {
    // "Language": {
    //   "value": "English",
    //   "key": "EN"
    // },
    // "AppConfiguration": {
    //   "title": "Intelligent Work Management"
    // },
    // "Theme": {
    //   "key": "blueTheme"
    // },
    DateTimeFormat: {
      dateTimeFormat: "DD MMM YYYY||HH:mm",
      timeZone: "Asia/Calcutta",
    },
  };

  const onTaskClick = (task) => {
    dispatch(setTaskData(task));
    var urls = {
      PRC: "/purchaseRequest/workbench/singleworkbench/",
      POC: "/purchaseOrder/confirmationTracker/taskDetail/",
      INC: "/invoices/workbench/singleInvoice/",
      RTC: "/ReturnManagement/SingleReturnWorkbench/",
      SEC: "/serviceSheet/workbench/singleServiceWorkbench/",
      PFC: "/planningManagement/singlePlanningTask/",
    };
    // var sampleTask = {
    //   systemId: "SCP",
    //   systemName: "SAP Workflow Engine",
    //   processName: "invoiceconfirmationworkflow",
    //   taskType: "usertask1",
    //   taskId: "e9759e0f-eb25-11ed-9fcd-eeee0a981469",
    //   processId: "e947d6d5-eb25-11ed-9fcd-eeee0a981469",
    //   requestId: "invoiceconfirmationworkflow",
    //   referenceId: "",
    //   taskDesc: "INC20230505091912674",
    //   requesType: "",
    //   businessStatus: "READY",
    //   technicalStatus: "READY",
    //   priority: "0",
    //   subject: "INC20230505091912674",
    //   createdBy:
    //     "sb-clone-111ca8c4-2306-446b-a595-c152a4b0fd9c!b19391|workflow!b10150",
    //   updatedBy:
    //     "sb-clone-111ca8c4-2306-446b-a595-c152a4b0fd9c!b19391|workflow!b10150",
    //   completedBy: "",
    //   formId: "6B90E928-E2C4-41BC-A88C-F80F95E7845C",
    //   itmStatus: "Open",
    //   isForwarded: 0,
    //   color: null,
    //   isPinned: null,
    //   compDeadline: 1697537094000,
    //   criticalDeadline: 1697537094000,
    //   createdOn: 1683278358000,
    //   updatedOn: null,
    //   completedAt: null,
    //   actions: null,
    //   taskNature: "Single-User",
    //   ownerType: "USER",
    //   ownerId: "<EMAIL>",
    //   createdByName:
    //     "sb-clone-111ca8c4-2306-446b-a595-c152a4b0fd9c!b19391|workflow!b10150",
    //   updatedByName: "",
    //   forwardedByName: "",
    //   isCritical: false,
    //   isBreached: false,
    //   timeLeftDisplayString: "On_Time",
    //   taskSla: "On_Time",
    //   timePercentCompleted: 0,
    // };
    if (urls[task?.taskDesc?.slice(0, 4)]) navigate(`${urls[task?.taskDesc?.slice(0, 4)]}${task?.taskDesc}`);
  };
  const fetchFilterViewList = () => {
    console.log("fetchFilterView");
  };
  const clearFilterView = () => {
    console.log("clearFilterView");
  };
  const fetchUserRawData = () => {
    doAjax(`/${destination_IWA_NPI}/api/v1/usersMDG/getUsersMDG`, "get", (resData) => {
      var tempData = resData.data;
      var tempUserData = tempData?.map((udata) => {
        return { ...udata, userId: udata?.emailId };
      });
      var finalData = { ...resData, data: tempUserData };
      setUserRawData(finalData);
    });
  };

  const fetchUserGroupRawData = () => {
    doAjax(`/${destination_IWA_NPI}/api/v1/groupsMDG/getAllGroupsMDG`, "get", (resData) => {
      var tempData = resData.data;
      var tempGroupData = tempData?.map((gData) => {
        return { ...gData, groupName: gData?.name };
      });
      var finalData = { ...resData, data: tempGroupData };
      setUserGroupRawData(finalData);
    });
  };

  const onActionComplete = (successFlag, taskPayload) => {
    console.log("Success flag.", successFlag);
    console.log("Task Payload.", taskPayload);
  };

  return (
    <div style={{ width: "calc(100vw - 105px)", height: "calc(100vh-130px)" }} className={"workspaceOverride"}>
      <>
        <WorkspaceComponent
          token={
            "eyJhbGciOiJSUzI1NiIsImprdSI6Imh0dHBzOi8vY2EtZ2JkLmF1dGhlbnRpY2F0aW9uLnVzMTAuaGFuYS5vbmRlbWFuZC5jb20vdG9rZW5fa2V5cyIsImtpZCI6ImRlZmF1bHQtand0LWtleS1kZjk5ODA5MzZhIiwidHlwIjoiSldUIiwiamlkIjogInVyQWRCVkJPN3VkV0FPMmFVaHp1QTRZU0V4aEE5TU96L05rTHF0UzkvR0E9In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TomSTo1o_CU1U8ExR7p5kJOZmgQirKjQSsoyUKgSBHcyMZ7ZoUB3DiZSDANEeMYnYOv__ZcyBwnY4JmIBSTvQDiWrasSnlcd2rwAx6oVREZCH3lanUM3qQd0CuAKnJMWghGmj6_XSaBnJc2Kulk8LAusknZ87EpK1EbBby1Ajrua6LafLahVkaIj4KnQkHlIa4cSXbAGVXnqAecvpX7rUI1wmwcnE1f4az3oCFoNWC7LK_pF74pZoie6yTBP4s7aQyoOwk6q_ayJdVgMjvodvE-6h01nbEw-3oRlu3YgwZOTSib5apz8upBrRJ6DdKIinfas5Q_VAXXywRjWq5wswQ"
          }
          configData={configData}
          destinationData={DestinationConfig}
          userData={{ ...userData, user_id: userData?.userName }}
          userPreferences={userPrefData} //Not needed check
          userPermissions={userPermissions}
          userList={{}}
          groupList={{}}
          userListBySystem={userListBySystem}
          useWorkAccess={applicationConfig.environment === "localhost" ? true : false} //change it "true" for local host
          useConfigServerDestination={applicationConfig.environment === "localhost" ? true : false} //change it "true" for local host
          // inboxTypeKey={task?.inboxTypeKey}
          // workspaceLabel={task?.workspaceLabel}
          inboxTypeKey={"ADMIN_COMPLETED_TASKS"}
          workspaceLabel={"Admin Completed Tasks"}
          workspaceFiltersByAPIDriven={true}
          subInboxTypeKey={"COMPLETED"}
          cachingBaseUrl={baseUrl_ITMJava}
          onTaskClick={onTaskClick}
          // onTaskLinkClick={onTaskLinkClick}  prev comment
          onActionComplete={onActionComplete}
          selectedFilterView={null}
          isFilterView={false}
          fetchFilterViewList={fetchFilterViewList}
          savedFilterViewData={[]}
          clearFilterView={clearFilterView}
          filterViewList={[]}
          selectedTabId={null}
          userProcess={[]}
        />
      </>
    </div>
  );
}
