import { useState, useEffect, useRef } from "react";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import Modal from "@mui/material/Modal";
import {
  DatePicker,
  DesktopDatePicker,
  LocalizationProvider,
  MobileDateTimePicker,
} from "@mui/x-date-pickers";
import CloseIcon from "@mui/icons-material/Close";
import {
  Autocomplete,
  Card,
  CardContent,
  CardHeader,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  Input,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  Skeleton,
  Stack,
  TextField,
  Tooltip,
} from "@mui/material";
import axios from "axios";
import ReusableDialog from "../../common/ReusableDialog";
import {
  destination_Admin,
  destination_Notification,
  destination_Po,
  destination_SLA_Mgmt,
} from "../../../destinationVariables";
import {
  button_Marginleft,
  button_Outlined,
  button_Primary,
  font_Small,
} from "../../common/commonStyles";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { doAjax } from "../../common/fetchService";
import { useSelector } from "react-redux";
import ReusableSnackBar from "../../common/ReusableSnackBar";
import DateRange from "../../common/DateRangePicker";
import { formValidator } from "../../../functions";
import moment from "moment/moment";
import ReusableIcon from "../../common/ReusableIcon";

const UpdateSLA = ({ handleClose, id, open, controller }) => {
  const [reusableDialog_Ref, setreusableDialog_Ref] = useState("");
  const [groupListMap, setGroupListMap] = useState({});
  const [serviceNameOptions, setServiceNameOptions] = useState([]);
  const [emailTemplateList, setemailTemplateList] = useState([]);
  const groupsList = useSelector((state) => state.userManagement.groups)?.map(
    (i) => i.name
  );
  const userData = useSelector((state) => state.userManagement.userData);
  let initialState = {
    processName: "",
    serviceName: "",
    serviceType: "",
    sla: "",
    slaType: "",
    startDate: null,
    endDate: null,
    slaFormat: "Days",
    date: [],
    slaReference: "PO Open Date",
    masterDataCategory: "",
    masterData: "",
    recipientGroup: "",
    emailTemplate: "",
    inAppNotification: false,
  };
  const [SLAFormController, setSLAFormController] = useState(initialState);
  const [SLATypeList, setSLATypeList] = useState([]);
  const [slaOptions, setSlaOptions] = useState([]);

  const [validationErrorItems, setvalidationErrorItems] = useState([]);
  let validationList = [
    "processName",
    "serviceName",
    "serviceType",
    "sla",
    "slaType",
    "slaFormat",
    "date",
    "slaReference",
  ];
  const [processNameList, setProcessNameList] = useState([]);
  const [serviceNameList, setServiceNameList] = useState([]);

  const [appReady, setAppReady] = useState(false);
  const [companysData, setcompanysData] = useState({});
  const [suppliersData, setsuppliersData] = useState({});
  const [masterDataOptions, setmasterDataOptions] = useState([]);
  const [masterDataOptionsRef, setmasterDataOptionsRef] = useState({});
  const [purchasingGroupsData, setpurchasingGroupsData] = useState({});
  let masterData = useSelector((state) => state.masterData);

  // const fetchCompanySupplierData = () => {
  //   doAjax(
  //     `/${destination_Po}/Odata/populateCompanyCodeDetails`,
  //     "get",
  //     (res) => {
  //       setcompanysData(res?.data);
  //     }
  //   );
  //   doAjax(`/${destination_Po}/Odata/getAllSuppliers`, "get", (res) => {
  //     setsuppliersData(res?.data);
  //   });
  //   doAjax(
  //     `/${destination_Po}/Odata/purchasingGroup`,
  //     "get",
  //     (res) => {
  //       setpurchasingGroupsData(res.data);
  //     },
  //     (err) => {}
  //   );
  //   //  setsuppliersData((prev)=>(masterData?.vendorCode));
  //   //  setcompanysData(masterData?.companyCode);
  // };

  let [elasticLoading, setElasticLoading] = useState(false);
  let timeOutId = useRef(null);

  //<--ELASTIC SEARCH FOR MASTERDATA OPTIONS FUNCTIONS AND VARIABLES
  let setDefaultDropdownData = (fieldName) => {
    if (fieldName === "purchasing group")
      setmasterDataOptions(masterData?.purchasingGroups);
    if (fieldName === "supplier") setmasterDataOptions(masterData?.vendorCode);
    if (fieldName === "company") setmasterDataOptions(masterData?.companyCode);
  };
  const fetchData = (name, newValue) => {
    let tName = name.toLowerCase();
    var url = "";
    if (tName === "purchasing group")
      url = `/${destination_Po}/Odata/getSearchDetails?name={##}&type=PurchasingGroup`;

    if (tName === "supplier")
      url = `/${destination_Po}/Odata/getSearchDetails?name={##}&type=vendor`;

    if (tName === "company")
      url = `/${destination_Po}/Odata/getSearchDetails?name={##}&type=company`;

    if (newValue) {
      const hSuccess = (data) => {
        // if(SLAFormController.masterData){
        //   if()

        // }
        var formatLocalOptions = Object.keys(data.data).map(
          (id) => `${data.data[id]} - ${id}`
        );
        setmasterDataOptions(formatLocalOptions);

        setElasticLoading(false);
        // if (tName === "purchasing group") {
        // }
        // if (tName === "supplier") {
        //   setmasterDataOptions(formatLocalOptions)
        //   setElasticLoading(false)

        // }
        // if (tName === "company") {
        //   setmasterDataOptions(formatLocalOptions)
        //   setElasticLoading(false)

        // }
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `${url}`.replace("{##}", newValue?.split(" - ")[0]),
        "get",
        hSuccess,
        hError
      );
    }
  };
  const onChangeFilter = (value) => {
    let name = SLAFormController.masterDataCategory;
    if (timeOutId) clearTimeout(timeOutId.current);
    if (value) {
      setElasticLoading(true);
      timeOutId.current = setTimeout(() => {
        fetchData(name, value);
      }, 1000);
    } else {
      setDefaultDropdownData(name);
    }
  };
  // useEffect(()=>{
  //   console.log(masterDataOptions)
  // },[masterDataOptions])

  let handleOnChangeMasterOptions = (fieldName, value) => {
    setSLAFormController((prev) => {
      return {
        ...prev,
        [fieldName]: value,
      };
    });
    onChangeFilter(value);
  };
  //---------------------------

  const setServiceNameDep = (serviceName) => {
    let tData = {};

    for (let i of slaOptions) {
      if (i?.["SLA Service Name"].toLowerCase() === serviceName.toLowerCase()) {
        setSLATypeList(i?.["SLA Type"]);
        setSLAFormController((prev) => ({
          ...prev,
          slaReference: i?.["SLA Reference"],
        }));
        break;
      }
    }
  };
  const setProcessNameDep = () => {
    let a = slaOptions;
    let o = [];
    let tempObj = {};
    //filter all services related to selected process name
    a.forEach((i) => {
      if (i["SLA Process Name"] === SLAFormController.processName) {
        o.push(i);
      }
    });
    //filter out all service names from filtered list o
    o.forEach((i) => {
      if (!tempObj[i["SLA Service Name"]]) {
        tempObj[i["SLA Service Name"]] = true;
      }
    });
    setServiceNameList(Object.keys(tempObj));
  };
  const handleOnChange = (e) => {
    setSLAFormController((prev) => {
      return {
        ...prev,
        [e.target.name]: e.target.value,
      };
    });
  };
  const handleOnChangeSlaType = (name) => {
    setSLAFormController((prev) => {
      return {
        ...prev,
        [name]: prev[name] === "Days" ? "Hours" : "Days",
      };
    });
  };
  const handleSubmit = () => {
    let payload = {
      ...SLAFormController,
      sla: parseInt(SLAFormController.sla),
      startDate: SLAFormController.date?.[0],
      endDate: SLAFormController.date?.[1],
      masterData: SLAFormController.masterData
        ?.split(" - ")
        .slice(-1)
        .toString(),
    };
    payload.emailTemplate =
      emailTemplateList[SLAFormController.emailTemplate] ?? "";
    payload.updatedBy = userData.emailId;
    delete payload.date;
    delete payload.createdAt;
    delete payload.createdBy;
    delete payload.ruleId;
    delete payload.updatedAt;

    let hSuccess = (res) => {
      controller("SUCCESS", SLAFormController.processName);
      handleClose_SLACrud();
    };
    let hError = (e) => {
      console.log(e);
    };
    !formValidator(
      SLAFormController,
      validationList,
      setvalidationErrorItems
    ) &&
      doAjax(
        `/${destination_SLA_Mgmt}/sla/update/${id}`,
        "patch",
        hSuccess,
        hError,
        payload
      );
  };

  const handleClose_SLACrud = () => {
    setSLAFormController(initialState);
    handleClose("UPDATE");
  };

  const fieldData = [
    {
      name: "processName",
      label: "Process Name",
      isRequired: true,
      value: SLAFormController,
      onchange: handleOnChange,
      disabled: false,
      errorItems: validationErrorItems,
      variant: "select",
      option: processNameList,
      placeholder: "Select Process Name",
      col: 5,
    },
    {
      name: "serviceName",
      label: "Service Name",
      isRequired: true,
      value: SLAFormController,
      onchange: handleOnChange,
      disabled: false,
      errorItems: validationErrorItems,
      variant: "select",
      option: serviceNameList,
      placeholder: "Select Service Name",
      col: 5,
    },
    {
      name: "slaType",
      label: "SLA Type",
      isRequired: true,
      value: SLAFormController,
      onchange: handleOnChange,
      disabled: false,
      errorItems: validationErrorItems,
      variant: "select",
      option: SLATypeList,
      placeholder: "Select SLA Type",
      col: 5,
    },

    {
      name: "date",
      label: "SLA Validity Range",
      isRequired: true,
      value: SLAFormController,
      onChange: (e) => {
        setSLAFormController((prev) => {
          return {
            ...prev,
            date: e,
          };
        });
      },
      disabled: false,
      errorItems: validationErrorItems,
      variant: "daterangepicker",
      format: "dd MMM yyyy",
      placeholder: "Enter SLA Validity",
      col: 5,
    },
    {
      name: "sla",
      label: "SLA",
      isRequired: true,
      value: SLAFormController,
      onchange: handleOnChange,
      disabled: false,
      errorItems: validationErrorItems,
      variant: "textWithAdornment",
      placeholder: "Enter SLA",
      col: 5,
      adornment: {
        name: "slaFormat",
        onChange: handleOnChangeSlaType,
        value: SLAFormController,
      },
    },
    {
      name: "slaReference",
      label: " ",
      isRequired: false,
      value: SLAFormController,
      variant: "custom",
      col: 5,
      component: (item) => {
        return (
          <>
            <Stack
              sx={{
                backgroundColor: "#EAE9FF",
                borderRadius: "10px",
                padding: ".5rem",
              }}
            >
              <Typography>
                {`SLA Reference: ${item.value[item.name] ?? ""}`}
              </Typography>
            </Stack>
          </>
        );
      },
    },
    {
      name: "masterDataCategory",
      label: "Master Data Category",
      isRequired: false,
      value: SLAFormController,
      onchange: handleOnChange,
      disabled: false,
      errorItems: validationErrorItems,
      variant: "select",
      option: ["Supplier", "Company", "Purchasing Group"],
      multiline: true,
      placeholder: "Select Master Data Category",
      col: 5,
    },
    {
      name: "masterData",
      label: "Master Data",
      isRequired: false,
      value: SLAFormController,
      onchange: handleOnChangeMasterOptions,
      disabled: false,
      elasticLoading: elasticLoading,
      errorItems: validationErrorItems,
      option: masterDataOptions,
      variant: "autocomplete",
      multiline: true,
      placeholder: "Select Master Data",
      col: 5,
    },
  ];

  const slaOutputFields = [
    {
      name: "recipientGroup",
      label: "Recipient Group",
      isRequired: false,
      value: SLAFormController,
      onchange: handleOnChange,
      disabled: false,
      errorItems: validationErrorItems,
      variant: "select",
      option: groupsList,
      placeholder: "Select Recipient Group",
      col: 5,
    },
    // {
    //   name: "emailTemplate",
    //   label: "Email Template",
    //   isRequired: true,
    //   value: SLAFormController,
    //   onchange: handleOnChange,
    //   disabled: false,
    //   errorItems: validationErrorItems,
    //   variant: "custom",
    //   option: Object.keys(emailTemplateList),
    //   placeholder: "Select Email Template",
    //   col: 5,
    //   component:(item)=>{
    //     return (
    //       <>
    //       <FormControl fullWidth size="small">
    //         <Select
    //           sx={font_Small}
    //           select
    //           fullWidth
    //           size="small"
    //           placeholder={item.placeholder}
    //           name={item.name}
    //           value={item.value[item.name]}
    //           onChange={item.onchange}
    //           displayEmpty={true}
    //           InputProps={{}}

    //         >
    //           <MenuItem value={""}>
    //             <Typography
    //               className="placeholderstyle"
    //             >
    //               {item.placeholder}
    //             </Typography>
    //           </MenuItem>
    //           {item.option?.map((com) => (
    //             <MenuItem value={com} >{com}</MenuItem>
    //           ))}
    //         </Select>
    //       </FormControl>
    //       </>

    //     );
    //   }
    // }
  ];

  const getTextFields = (data) => {
    //returns textfields
    const getTextField = (item) => {
      switch (item.variant) {
        case "text":
          return (
            <>
              {appReady ? (
                <TextField
                  fullWidth
                  size="small"
                  placeholder={item.placeholder}
                  name={item.name}
                  value={item.value?.[item.name]}
                  onChange={item.onchange}
                  required={item.isRequired}
                  error={item.errorItems?.includes(item.name)}
                  InputProps={{}}
                  multiline={item.multiline}
                ></TextField>
              ) : (
                <Skeleton variant="rectangular" height={"2rem"} />
              )}
            </>
          );
        case "select":
          return (
            <>
              {appReady ? (
                <FormControl fullWidth size="small">
                  <Select
                    sx={font_Small}
                    select
                    fullWidth
                    size="small"
                    placeholder={item.placeholder}
                    name={item.name}
                    value={item.value[item.name]}
                    onChange={item.onchange}
                    displayEmpty={true}
                    InputProps={{}}
                    // defaultValue={item.value[item.name]}
                  >
                    <MenuItem value={""}>
                      <Typography
                        className="placeholderstyle"
                        style={{ color: "#C1C1C1" }}
                      >
                        {item.placeholder}
                      </Typography>
                    </MenuItem>
                    {item.option?.map((com) => (
                      <MenuItem value={com?.toString()}>{`${com}`}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              ) : (
                <Skeleton variant="rectangular" height={"2rem"} />
              )}
            </>
          );
        case "datepicker":
          return (
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DesktopDatePicker
                inputFormat={item.format ?? ""}
                value={item.value[item.name]}
                name={item.name}
                onChange={item.onChange}
                InputProps={{}}
                renderInput={(params) => (
                  <TextField
                    fullWidth
                    size="small"
                    {...params}
                    placeholder={item.placeholder}
                    sx={{
                      backgroundColor: "white",
                      borderRadius: "6px",
                    }}
                  />
                )}
              />
            </LocalizationProvider>
          );
        case "textWithAdornment":
          return (
            <>
              {appReady ? (
                <Stack direction={"row"}>
                  <TextField
                    fullWidth
                    size="small"
                    placeholder={item.placeholder}
                    name={item.name}
                    value={item.value?.[item.name]}
                    onChange={item.onchange}
                    required={item.isRequired}
                    error={item.errorItems?.includes(item.name)}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment
                          sx={{ height: "100%", marginRight: 0 }}
                          position="end"
                        >
                          <IconButton
                            size="small"
                            onClick={() =>
                              item.adornment.onChange(item.adornment.name)
                            }
                            sx={{ fontSize: "14px" }}
                          >
                            {item.adornment.value[item.adornment.name]}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    multiline={item.multiline}
                  ></TextField>
                </Stack>
              ) : (
                <Skeleton variant="rectangular" height={"2rem"} />
              )}
            </>
          );
        case "daterangepicker":
          return (
            <>
              {appReady ? (
                <DateRange
                  className="dates"
                  size="md"
                  format="dd MMM yyyy"
                  placement="auto"
                  date={item.value[item.name]}
                  handleDate={item.onChange}
                  // handleClear={() => {}}
                />
              ) : (
                <Skeleton variant="rectangular" height={"2rem"} />
              )}
            </>
          );
        case "autocomplete":
          return (
            <FormControl
              fullWidth
              size="small"
              sx={{ maxHeight: "max-content" }}
            >
              <Autocomplete
                disablePortal
                placeholder={item.placeholder}
                id="combo-box-demo"
                options={item?.option}
                fullWidth
                size="small"
                freeSolo={true}
                loading={item?.elasticLoading ?? false}
                name={item.name}
                renderInput={(params) => (
                  <TextField placeholder={item.placeholder} {...params} />
                )}
                // onChange={}
                onInputChange={(e, newValue) =>
                  item.onchange(item.name, newValue)
                }
                value={item.value[item.name]}
              />
            </FormControl>
          );
        case "custom":
          return (
            <>
              {appReady ? (
                item.component(item)
              ) : (
                <Skeleton variant="rectangular" height={"2rem"} />
              )}
            </>
          );
        default:
          return <></>;
      }
    };
    return (
      <>
        {data.map((item) => {
          return (
            <Grid
              item
              xs={1}
              md={item.col}
              sx={{ height: "max-content", marginTop: "auto" }}
            >
              <Box
                sx={{
                  minWidth: 120,
                  display: "flex",
                  justifyContent: "end",
                  flexDirection: "column",
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "row",
                    justifyContent: "start",
                    alignItems: "self-start",
                    marginBottom: "auto",
                  }}
                >
                  <Typography sx={{ ...font_Small, display: "inline-block" }}>
                    {item.label}
                  </Typography>
                  {item.isRequired && (
                    <Typography
                      sx={{
                        ...font_Small,
                        display: "inline-block",
                        color: "red",
                      }}
                    >
                      *
                    </Typography>
                  )}
                </Box>

                <>{getTextField(item)}</>
              </Box>
            </Grid>
          );
        })}
      </>
    );
  };

  //<-- Functions and variables for reusable dialog box -->
  const [warning_Notification, setwarning_Notification] = useState({
    currentNotification: "",
    success: true,
    open: false,
    title: "",
    severity: "",
  });
  const [Success_Notification, setSuccess_Notification] = useState({
    currentNotification: "",
    success: true,
    open: false,
    title: "",
    severity: "",
  });
  const functions_ReusableDialogBox = {
    MessageDialogCancel: () => {
      setwarning_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
    },
    MessageDialogClickOpen: () => {
      setwarning_Notification((prev) => ({ ...prev, open: true }));
      // setOpenMessageDialog(true);
    },

    MessageDialogClose: () => {
      setwarning_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));

      setSuccess_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
      handleClose();
    },
    messageDialogCloseAndRedirect: () => {
      // seteditState(true);
      setwarning_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
      setSuccess_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
      // navigate('/manageAccount')
    },
    getHandleOkFunction: () => {
      switch (reusableDialog_Ref) {
        case "CONFIRMUPDATE":
          setwarning_Notification((prev) => ({
            open: false,
            currentNotification: "",
            success: "",
            title: "",
            severity: "",
          }));
          handleSubmit();
          break;
        case "CONFIRMCREATE":
          setwarning_Notification((prev) => ({
            open: false,
            currentNotification: "",
            success: "",
            title: "",
            severity: "",
          }));
          handleSubmit();
          break;
        case "ERROR":
          functions_ReusableDialogBox.MessageDialogClose();
          break;
        case "CONFIRMCANCEL":
          functions_ReusableDialogBox.MessageDialogClose();
        // seteditState(true)
        default:
          functions_ReusableDialogBox.MessageDialogClose();
      }
    },
    viewOkButton: () => {
      // console.log(reusableDialog_Ref,'ref')
      switch (reusableDialog_Ref) {
        case "CONFIRMUPDATE":
          return true;
          break;
        case "CONFIRMCREATE":
          return true;
          break;
        case "ERROR":
          return false;
          break;
        case "CONFIRMCANCEL":
          return false;
        default:
          return false;
      }
    },
    viewCancelButton: () => {
      switch (reusableDialog_Ref) {
        case "CONFIRMUPDATE":
          return true;
          break;
        case "CONFIRMCREATE":
          return true;
          break;
        case "ERROR":
          return false;
          break;
        case "CONFIRMCANCEL":
          return true;
        default:
          return false;
      }
    },
    getOkButtonText: () => {
      switch (reusableDialog_Ref) {
        case "CONFIRMUPDATE":
          return "Submit";
          break;
        case "CONFIRMCREATE":
          return "Submit";
          break;
        case "ERROR":
          return "OK";
        case "CONFIRMCANCEL":
          return "OK";
        default:
          return "OK";
      }
    },
    getHandleCancleFunction: () => {
      switch (reusableDialog_Ref) {
        case "CONFIRMUPDATE":
          return functions_ReusableDialogBox.MessageDialogClose();
        case "CONFIRMCREATE":
          return functions_ReusableDialogBox.MessageDialogClose();
        case "ERROR":
          return () => {};
        case "CONFIRMCANCEL":
          return functions_ReusableDialogBox.MessageDialogClose();
        default:
          return () => {};
      }
    },
  };
  let fetchSingleSLA = async () => {
    try {
      let hSuccess = (response) => {
        let res = response.data;
        let tData = {
          ...res,
          date: [
            moment(res.startDate?.split("T")[0], "YYYY-MM-DD").toDate(),
            moment(res.endDate?.split("T")[0], "YYYY-MM-DD").toDate(),
          ],
        };
        setSLAFormController(tData);
      };
      let hError = (e) => {};
      doAjax(
        `/${destination_SLA_Mgmt}/sla/getSingleSla/${id}`,
        "get",
        hSuccess,
        hError
      );
    } catch (e) {
      console.log(e);
    }
  };

  const 
  fetchSLAOptions = () => {
    doAjax(
      `/${destination_SLA_Mgmt}/sla/getSLAServiceDropdownValues`,
      "get",
      (res) => {
        setSlaOptions(
          res.slaDropdownList.map((i) => ({
            ...i,
            "SLA Type": i["SLA Type"]?.split(","),
          }))
        );
        let o = {};
        res.slaDropdownList.forEach((i) => {
          if (!o[i["SLA Process Name"]]) {
            o[i["SLA Process Name"]] = true;
          }
        });
        setProcessNameList(Object.keys(o));
      }
    );
  };
  let fetchServicesInOrder = async () => {
    await fetchSLAOptions();
    // await fetchCompanySupplierData();
    fetchSingleSLA();
    setAppReady(true);
  };
  useEffect(() => {
    setServiceNameDep(SLAFormController.serviceName);
  }, [SLAFormController.serviceName]);
  useEffect(() => {
    setProcessNameDep();
  }, [SLAFormController.processName]);

  useEffect(() => {
    setServiceNameDep(SLAFormController.serviceName);
  }, [SLAFormController.serviceName]);
  useEffect(() => {
    let tOptionMap = {};
    let tMasterdataValue = "";
    switch (SLAFormController.masterDataCategory.toLowerCase()) {
      case "supplier":
        tOptionMap = suppliersData;
        break;
      case "company":
        tOptionMap = companysData;
        break;
      case "purchasing group":
        tOptionMap = purchasingGroupsData;
        break;
    }
    if (tOptionMap[SLAFormController.masterData]) {
      console.log(tOptionMap);
      tMasterdataValue = `${SLAFormController.masterData} - ${
        tOptionMap[SLAFormController.masterData]
      }`;
      setSLAFormController((prev) => ({
        ...prev,
        masterData: tMasterdataValue,
      }));
    }
  }, [companysData, suppliersData, purchasingGroupsData]);
  useEffect(() => {
    fetchServicesInOrder();
  }, []);
  // useEffect(() => {
  //   let tList= []
  //   let tOptionRef={}
  //   if (SLAFormController.masterDataCategory.toLowerCase() === "company") {
  //     tList = Object.keys(companysData).map((i) => (i.toString()));
  //     setmasterDataOptionsRef(tOptionRef)
  //     setmasterDataOptions(tList);

  //   } else  if (SLAFormController.masterDataCategory.toLowerCase() === "supplier") {
  //     tList = Object.keys(suppliersData).map(
  //       (i) =>  (i.toString())
  //     );
  //     setmasterDataOptionsRef(tOptionRef)
  //     setmasterDataOptions(tList);
  //   }else{
  //     tList = Object.keys(purchasingGroupsData).map(
  //       (i) =>  (i.toString())
  //     );
  //     setmasterDataOptionsRef(tOptionRef)
  //     setmasterDataOptions(tList);
  //   }

  // }, [SLAFormController.masterDataCategory,suppliersData,companysData,purchasingGroupsData]);

  useEffect(() => {
    //handle dynamic options for masterdataoptions for supplier, companyand purchgrp
    let list;
    if (SLAFormController.masterDataCategory.toLowerCase() === "company") {
      list = Object.keys(companysData).map((i) => `${companysData[i]} - ${i}`);
    } else if (
      SLAFormController.masterDataCategory.toLowerCase() === "supplier"
    ) {
      list = Object.keys(suppliersData).map(
        (i) => `${suppliersData[i]} - ${i}`
      );
    } else {
      list = Object.keys(purchasingGroupsData).map(
        (i) => `${purchasingGroupsData[i]} -${i}`
      );
    }
    setmasterDataOptions(list);
  }, [SLAFormController.masterDataCategory]);

  useEffect(() => {
    // console.log(masterDataOptionsRef,'tList')
    // console.log(SLAFormController,'SLAFormController')
  }, [masterDataOptionsRef, masterDataOptionsRef]);
  return (
    <div>
      {/* REUSABLE DIALOG */}
      <ReusableDialog
        dialogState={warning_Notification.open}
        openReusableDialog={functions_ReusableDialogBox.MessageDialogClickOpen}
        closeReusableDialog={functions_ReusableDialogBox.MessageDialogCancel}
        dialogTitle={warning_Notification.title}
        dialogMessage={warning_Notification.currentNotification}
        handleOk={functions_ReusableDialogBox.getHandleOkFunction}
        dialogOkText={functions_ReusableDialogBox.getOkButtonText()}
        showOkButton={functions_ReusableDialogBox.viewOkButton()}
        showCancelButton={functions_ReusableDialogBox.viewCancelButton()}
        dialogSeverity={warning_Notification.severity}
        handleDialogReject={functions_ReusableDialogBox.getHandleCancleFunction}
      />
      <ReusableSnackBar
        openSnackBar={Success_Notification.open}
        alertMsg={Success_Notification.currentNotification}
        handleSnackBarClose={functions_ReusableDialogBox.MessageDialogClose}
      />

      <Dialog maxWidth="md" open={open} onClose={handleClose_SLACrud}>
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            display: "flex",
          }}
        >
          <Typography variant="h6">Update SLA</Typography>

          <IconButton
            sx={{ width: "max-content" }}
            onClick={handleClose_SLACrud}
            children={<ReusableIcon iconName={"Close"} />}
          />
        </DialogTitle>

        <DialogContent sx={{ padding: ".5rem 1rem" }}>
          <Grid
            container
            sx={{
              width: "600px",
              display: "flex",
              flexDirection: "row",
              justifyContent: "center",
              alignItems: "center",
              padding: "0.5rem 0rem"
            }}
            spacing={2}
            columns={10}
          >

            {getTextFields(fieldData)}
          </Grid>
        </DialogContent>

        <DialogActions
          sx={{ display: "flex", justifyContent: "end" }}
        >
          <Button
            variant="outlined"
            sx={{
              ...button_Outlined,
              marginLeft: "auto",
            }}
            onClick={handleClose_SLACrud}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            sx={{ ...button_Primary, ...button_Marginleft }}
            onClick={() => {
              setreusableDialog_Ref("CONFIRMCREATE");
              setwarning_Notification((prev) => ({
                ...prev,
                currentNotification: `Would you like to proceed with submission of the details?`,
                success: false,
                open: true,
                title: "Confirm Submit",
                severity: "success",
              }));
            }}
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default UpdateSLA;
