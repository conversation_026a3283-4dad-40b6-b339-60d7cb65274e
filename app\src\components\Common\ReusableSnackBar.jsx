import React, { forwardRef } from 'react';
import Stack from '@mui/material/Stack';
import Snackbar from '@mui/material/Snackbar';
import MuiAlert from '@mui/material/Alert';
import { Zoom, Slide, Fade } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import CircularProgress from '@mui/material/CircularProgress';
import { styled } from '@mui/material/styles';
import { colors } from '../../constant/colors';

const StyledAlert = styled(MuiAlert)(({ theme, severity, customcolor }) => ({
  borderRadius: '8px',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
  minWidth: '300px',
  padding: '12px 16px',
  alignItems: 'center',
  backgroundColor: customcolor || {
    success: colors.alert.success.background,
    error: colors.alert.error.background,
    warning: colors.alert.warning.background,
    info: colors.alert.info.background
  }[severity],
  color: {
    success: colors.alert.success.text,
    error: colors.alert.error.text,
    warning: colors.alert.warning.text,
    info: colors.alert.info.text
  }[severity],
  border: `1px solid ${customcolor ? 'rgba(0, 0, 0, 0.12)' : {
    success: colors.alert.success.border,
    error: colors.alert.error.border,
    warning: colors.alert.warning.border,
    info: colors.alert.info.border
  }[severity]}`,
  '& .MuiAlert-icon': {
    fontSize: '24px',
    opacity: 0.9,
    marginRight: '12px'
  },
  '& .MuiAlert-message': {
    fontSize: '14px',
    fontWeight: 500,
    padding: '4px 0'
  },
  '& .MuiAlert-action': {
    padding: '0 0 0 16px',
    marginRight: 0
  },
  transition: 'all 0.3s ease-in-out',
  animation: 'slideIn 0.5s ease-out',
  '@keyframes slideIn': {
    '0%': {
      transform: 'translateY(-100%)',
      opacity: 0
    },
    '100%': {
      transform: 'translateY(0)',
      opacity: 1
    }
  }
}));

const getIcon = (alertType, isLoading) => {
  if (isLoading) return <CircularProgress size={24} />;
  
  const icons = {
    success: <CheckCircleIcon />,
    error: <ErrorOutlineIcon />,
    warning: <WarningAmberIcon />,
    info: <InfoOutlinedIcon />
  };
  
  return icons[alertType] || icons.info;
};

const ReusableSnackBar = ({
  openSnackBar,
  alertMsg,
  handleSnackBarClose,
  alertType = 'success',
  transition = Slide,
  snackbarBgColor,
  textColor,
  isLoading = false,
  autoHideDuration = 3000,
  position = { vertical: 'top', horizontal: 'center' }
}) => {
  return (
    <Stack spacing={2} sx={{ width: '100%' }}>
      <Snackbar
        open={openSnackBar}
        autoHideDuration={autoHideDuration}
        onClose={handleSnackBarClose}
        anchorOrigin={position}
        TransitionComponent={transition}
      >
        <StyledAlert
          onClose={handleSnackBarClose}
          severity={alertType}
          icon={getIcon(alertType, isLoading)}
          customcolor={snackbarBgColor}
          sx={textColor ? { color: textColor } : {}}
        >
          {alertMsg}
        </StyledAlert>
      </Snackbar>
    </Stack>
  );
};

export default ReusableSnackBar;

