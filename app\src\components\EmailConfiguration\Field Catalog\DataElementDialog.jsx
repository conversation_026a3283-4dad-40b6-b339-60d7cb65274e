import React from "react";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import CloseIcon from "@mui/icons-material/Close";
import DialogTitle from "@mui/material/DialogTitle";
import DataElementDetails from "./CwMSDataElementDetails";
import { IconButton } from "@mui/material";

class valueHelpDialog extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      value: "DataElement",
      tempDataEle: this.props.dataElement,
    };
  }

  handleChange = (event, newValue) => {
    this.setState({
      ...this.state,
      value: newValue,
    });
  };

  render() {
    return (
      <Dialog
        fullScreen={false}
        fullWidth={true}
        maxWidth="lg"
        open={this.props.DAopen}

        //onClose={() => props.onClose('CANCEL')}
      >
        <DialogTitle style={{ padding: "0.5rem 0.5rem 0 0.5rem" }}>
          <div style={{ display: "flex", alignItems: "space", justifyContent: "space-between", borderBottom: "1px solid #d9d9d9" }}>
            <p>Create Data Element</p>
            <IconButton
              aria-label="close"
              onClick={() => {
                this.props.onClose("CANCEL");
              }}
            >
              <CloseIcon />
            </IconButton>
          </div>
        </DialogTitle>
        <DialogContent sx={{background: "#F1F5FE"}}>
          <DialogContentText style={{ width: "35rem" }}>
            <div>
              <DataElementDetails {...this.props} destinations={this.destinations}/>
            </div>
          </DialogContentText>
        </DialogContent>
        <DialogActions style={{ height: "3rem", borderTop: "1px solid #d9d9d9" }}>
          {/* {props.actions.map(action => <Button key={action} variant="contained" size="small" onClick={() => props.handleClose(action)}>{action}</Button>)} */}
          {/* <Button key={'CANCEL'} variant="contained" size="small" onClick={()=>{this.props.onClose("CANCEL")}}>Discard</Button>
                <Button key={'CANCEL'} variant="contained" size="small" onClick={()=>{this.props.onClose("CANCEL")}}>Save</Button> */}
        </DialogActions>
      </Dialog>
    );
  }
}

export default valueHelpDialog;
