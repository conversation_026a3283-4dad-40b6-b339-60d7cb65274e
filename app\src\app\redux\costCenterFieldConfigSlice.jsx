// src/store/slices/costCenterFieldConfigSlice.js
import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  tabs: [],
  config: {
    allfields: {},
    mandatoryFields: {},
  },
  error: null,
  loading: false,
};

const costCenterFieldConfigSlice = createSlice({
  name: "costCenterFieldConfig",
  initialState,
  reducers: {
    setCostCenterTabs: (state, action) => {
      state.tabs = action.payload;
    },
    setCostCenterConfig: (state, action) => {
      state.config = action.payload;
    },
    setCostCenterError: (state, action) => {
      state.error = action.payload;
    },
    setCostCenterLoading: (state, action) => {
      state.loading = action.payload;
    },
    resetCostCenterFieldConfig: () => initialState,
  },
});

export const {
  setCostCenterTabs,
  setCostCenterConfig,
  setCostCenterError,
  setCostCenterLoading,
  resetCostCenterFieldConfig,
} = costCenterFieldConfigSlice.actions;

export default costCenterFieldConfigSlice.reducer;
