import React, { useEffect, useState } from "react";
import { Snackbar, Alert } from "@mui/material";
import { ERROR_MESSAGES } from "@constant/enum";

const NetworkStatus = () => {
  const [isOffline, setIsOffline] = useState(!navigator.onLine);
  const [showOnlineMsg, setShowOnlineMsg] = useState(false);

  useEffect(() => {
    const handleOffline = () => {
      setIsOffline(true);
      setShowOnlineMsg(false);
    };

    const handleOnline = () => {
      setIsOffline(false);
      setShowOnlineMsg(true);

      // Auto-hide "Back online" after 3 seconds
      setTimeout(() => setShowOnlineMsg(false), 3000);
    };

    window.addEventListener("offline", handleOffline);
    window.addEventListener("online", handleOnline);

    return () => {
      window.removeEventListener("offline", handleOffline);
      window.removeEventListener("online", handleOnline);
    };
  }, []);

  return (
    <>
      <Snackbar
        open={isOffline}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert severity="warning" variant="filled">
          {ERROR_MESSAGES.NO_INTERNET_CONNECTION}
        </Alert>
      </Snackbar>

      <Snackbar
        open={showOnlineMsg}
        autoHideDuration={3000}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert severity="success" variant="filled">
          {ERROR_MESSAGES.BACK_ONLINE}
        </Alert>
      </Snackbar>
    </>
  );
};

export default NetworkStatus;
