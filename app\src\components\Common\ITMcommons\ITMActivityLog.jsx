import React, { useEffect, useState } from "react";
import {
  destination_Po,
  destination_IWA,
  destination_ITM,
  // destination_IWA_SCP,
  destination_IWA_NPI,
} from "../../../destinationVariables";
// Activity Logs imports
import ActivityLogs from "@cw/cherrywork-itm-workspace/ActivityLogs";
import { useNavigate } from "react-router-dom";
import { setTaskData } from "../../../app/userManagementSlice";
import { SwipeableDrawer } from "@mui/material";
import { useSelector } from "react-redux";
import { doAjax } from "../fetchService";
import CloseIcon from "@mui/icons-material/Close";
import Divider from "@mui/material/Divider";
import {
  Accordion,
  AccordionDetails,
  Box,
  Card,
  Grid,
  IconButton,
  Skeleton,
  styled,
  Typography,
} from "@mui/material";
import { Stack } from "@mui/system";
import { returnUserMap } from "../../../data/userData";
import configData from "../../../data/configData";

const ITMActivityLog = ({ id, handleClose, open }) => {
  const [taskData, settaskData] = useState(null);
  let userData = useSelector((state) => state.userManagement.userData);

  const navigate = useNavigate();
  const [userRawData, setUserRawData] = useState(null);
  const [userGroupRawData, setUserGroupRawData] = useState(null);
  const fetchUserRawData = () => {
    doAjax(
      `/${destination_IWA_NPI}/api/v1/usersMDG/getUsersMDG`,
      "get",
      (resData) => {
        var tempData = resData.data;
        var tempUserData = tempData?.map((udata) => {
          return { ...udata, userId: udata?.emailId };
        });
        var finalData = { ...resData, data: tempUserData };
        setUserRawData(finalData);
      }
    );
  };

  const fetchUserGroupRawData = () => {
    doAjax(`/${destination_IWA}/api/v1/groups`, "get", (resData) => {
      var tempData = resData.data;
      var tempGroupData = tempData?.map((gData) => {
        return { ...gData, groupName: gData?.name };
      });
      var finalData = { ...resData, data: tempGroupData };
      setUserGroupRawData(finalData);
    });
  };

  let fetchTaskId = (id) => {
    doAjax(
      `/${destination_Po}/task/workflow-task-Id/${id}`,
      "get",
      (data) => {
        console.log(data,"1st")
        if (data?.data) {
          doAjax(
            `/${destination_ITM}/v1/detailPage/dynamicDetails/${data?.data}`,
            "get",
            (res) => {
              console.log(res,"2nd")
              if (res.statusCode === 200) {
                settaskData(res?.data?.taskDetails?.[0]);
              }
            }
          );
        }
      }
    );
  };

  useEffect(() => {
    fetchTaskId(id);
    fetchUserRawData();
    fetchUserGroupRawData();
  }, []);
  return (
    <SwipeableDrawer
      anchor="right"
      open={open}
      onClose={() => {
        handleClose("ACTIVITYLOG");
      }}
      onOpen={() => {}}
      PaperProps={{
        elevation: 4,
        sx: {
          minWidth: "35vw",

          border: "none",
        },
      }}
    >
      <Stack spacing={1}>
        <Grid
          container
          sx={{
            padding: "0",
          }}
        >
          <Grid item md={10} style={{ padding: "16px" }}>
            <Typography
              variant="h5"
              sx={{
                color: "#1D1D1D",
                font: "Roboto",
                fontWeight: "400",
                fontSize: "24px",
              }}
            >
              Activity Log
            </Typography>
          </Grid>
          <Grid item md={2} style={{ margin: "auto" }}>
            <IconButton
              onClick={(e) => {
                e.stopPropagation();
                handleClose("ACTIVITYLOG");
              }}
            >
              <CloseIcon />
            </IconButton>
          </Grid>
        </Grid>
        <Divider />
        {taskData ? (
          <ActivityLogs
            task={taskData}
            useWorkAccess={false}
            token={""}
            destinationData={{}}
            userData={{ ...userData, user_id: userData?.emailId }}
            userList={returnUserMap(userRawData)}
            useConfigServerDestination={false}
            configData={configData}
          />
        ) : (
          <Typography
            variant="caption"
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            No Activity Found
          </Typography>
        )}
      </Stack>
    </SwipeableDrawer>
  );
};

export default ITMActivityLog;
