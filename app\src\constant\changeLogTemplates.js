import { TEMPLATE_KEYS } from "./changeTemplates";

export const CHANGE_LOG_TEMPS = {
    [TEMPLATE_KEYS?.LOGISTIC]: {
      "Logistic Data": {
        fieldName: ["Material", "AltUnit", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Alternative Unit of Measure", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
    },
  
    [TEMPLATE_KEYS?.MRP]: {
      "Basic Data": {
        fieldName: ["Material","FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "Plant Data": {
        fieldName: ["Material", "Plant", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Plant", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
    },
  
    [TEMPLATE_KEYS?.WARE_VIEW_2]: {
      "Warehouse Data": {
        fieldName: ["Material", "WhseNo",   "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Warehouse",  "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
    },
    
    [TEMPLATE_KEYS?.ITEM_CAT]: {
      "Item Cat Group": {
        fieldName: ["Material", "SalesOrg", "DistrChan", "FieldName", "SAPValue", "PreviousValue", "CurrentValue","ChangedBy", "ChangedOn"],
        headerName: ["Material", "Sales Org", "Distribution Channel", "Changed Fields", "SAP Value", "Old Value", "New Value","Changed by", "Updated on"]
      },
    },
  
    [TEMPLATE_KEYS?.SET_DNU]: {
      "Description": {
        fieldName: ["Material", "Langu", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Language","Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "Plant Data": {
        fieldName: ["Material", "Plant",  "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Plant",  "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "Basic Data": {
        fieldName: ["Material",  "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material",  "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "Sales Data": {
        fieldName: ["Material", "SalesOrg", "DistrChan", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Sales Org", "Distribution Channel", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
    },
    
  
    [TEMPLATE_KEYS?.UPD_DESC]: {
      "Update Descriptions": {
        fieldName: ["Material", "Langu", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Language","Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
    },
  
    [TEMPLATE_KEYS?.CHG_STAT]: {
      "Plant Data": {
        fieldName: ["Material", "Plant", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Plant", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "Basic Data": {
        fieldName: ["Material",  "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material",  "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
      "Sales Data": {
        fieldName: ["Material", "SalesOrg", "DistrChan", "FieldName", "SAPValue", "PreviousValue", "CurrentValue", "ChangedBy", "ChangedOn"],
        headerName: ["Material", "Sales Org", "Distribution Channel", "Changed Fields", "SAP Value", "Old Value", "New Value", "Changed by", "Updated on"]
      },
    }
    
  };
  