import { doAjax } from "@components/Common/fetchService";
import Store from "@app/store";
import useLogger from "@hooks/useLogger";
import { setDropDownDataIO } from "../slice/InternalOrderSlice";
const useIOFetchDropdownAndDispatch = () => {
  const { customError } = useLogger();
  const fetchDataAndDispatch = (url, keyName, method = "get", payload = {},isDependentDropDown = false) => {
    const hSuccess = (data) => {
      if(!isDependentDropDown){Store.dispatch(
          setDropDownDataIO({
            keyName,
            data: data.body,
          })
        );
        }
        // else{
        //   Store.dispatch(
        //     setDependentDropdown({
        //       keyName,
        //       data: data.body,
        //       keyName2 : payload?.plant || (`${payload.salesOrg}-${payload.distChnl}` || payload.salesOrg)
        //     })
        //   );
        // }
      }

    const hError = (error) => {
      customError(error);
    };
    
    doAjax(url, method.toLowerCase(), hSuccess, hError, payload);
  };

  return { fetchDataAndDispatch };
};

export default useIOFetchDropdownAndDispatch;