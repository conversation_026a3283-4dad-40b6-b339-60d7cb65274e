import React, { useEffect, useRef, useState } from "react";
import ReusableDialog from "./ReusableDialog";

const NoDataDialog = ({ DataRows }) => {
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [Display, setDisplay] = useState(false);
  const initialRender = useRef(true);
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  useEffect(() => {
    if (initialRender.current) {
      initialRender.current = false;
    } else {
      if (DataRows.length == 0) {
        setDisplay(true);
        handleMessageDialogClickOpen();
      }
    }
  }, [DataRows]);

  return (
    <>
      {Display && (
        <ReusableDialog
          dialogState={openMessageDialog}
          openReusableDialog={handleMessageDialogClickOpen}
          closeReusableDialog={handleMessageDialogClose}
          dialogTitle={"Error"}
          dialogMessage={`No Results to display for selected filters `}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          dialogSeverity={"danger"}
        />
      )}
    </>
  );
};

export default NoDataDialog;