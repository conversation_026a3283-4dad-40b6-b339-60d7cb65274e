import React from 'react';
import { 
  Box, 
  Typography,
  useTheme,
  useMediaQuery
} from '@mui/material';
import { 
  CalendarToday, 
  CheckCircle,
  AccessTimeOutlined,
  PendingOutlined,
  HourglassEmptyOutlined,
  TimelineOutlined
} from '@mui/icons-material';
import moment from 'moment';
import { colors } from '@constant/colors';

const TaskTimeline = ({ item, initiator = false }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  // Calculate task duration and completion percentage
  const startDate = moment(item.createdAt);
  const endDate = initiator ? moment(item.updatedAt) : moment(item.completedAt);
  const durationMinutes = endDate.diff(startDate, 'minutes');
  
  // Format dates for display
  const formattedStartDate = startDate.format("MMM D, YYYY");
  const formattedStartTime = startDate.format("h:mm A");
  const formattedEndDate = endDate.format("MMM D, YYYY");
  const formattedEndTime = endDate.format("h:mm A");
  
  const isCompleted = initiator ? item.updatedAt : item.completedAt;
  
  // Responsive sizes
  const iconSize = isMobile ? 16 : 20;
  const iconFontSize = isMobile ? 10 : 12;
  const marginLeft = isMobile ? 1.5 : 2;
  
  return (
    <>
    <Box
      sx={{
        px: 2,
        py: 1.5,
        borderBottom: "1px solid #E2E8F0",
        borderTop: { xs: "1px solid #E2E8F0", md: "none" },
        borderRadius: '8px',
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        bgcolor: colors?.chip?.background,
      }}
    >
      <Typography
        variant="subtitle2"
        sx={{
          fontWeight: 600,
          color: colors?.text.greyishBlue,
          display: "flex",
          alignItems: "center",
        }}
      >
        <TimelineOutlined sx={{ fontSize: 18, mr: 1 }} />
        Task Timeline
      </Typography>
    </Box>
    
    <Box sx={{ 
      position: 'relative', 
      ml: marginLeft, 
      pb: 2,
      width: '100%',
      maxWidth: '100%',
      overflow: 'hidden',
      pt:2
    }}>
      {/* Vertical timeline line - only between the points */}
      <Box sx={{ 
        position: 'absolute',
        left: iconSize / 2 - 1,
        top: iconSize + 4, // Start after the first icon
        height: isCompleted ? `calc(100% - ${iconSize * 2.5}px)` : `${iconSize * 4}px`, // End before the last icon
        width: 2,
        bgcolor: isCompleted ? '#16A34A' : '#F97316', // Green for completed, Orange for pending
        zIndex: 0
      }} />
      
      {/* Start Date */}
      <Box sx={{ position: 'relative', mb: 3, display: 'flex', alignItems: 'center' }}>
        <Box sx={{ 
          width: iconSize, 
          height: iconSize, 
          borderRadius: '50%',
          bgcolor: '#0284C7',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1,
          flexShrink: 0
        }}>
          <CalendarToday sx={{ color: '#FFFFFF', fontSize: iconFontSize }} />
        </Box>
        <Box sx={{ ml: 1.5, overflow: 'hidden' }}>
          <Typography variant={isMobile ? "caption" : "body2"} color="#0284C7" fontWeight={600} noWrap>
            Started
          </Typography>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', textOverflow: 'ellipsis' }}>
            {formattedStartDate} {formattedStartTime}
          </Typography>
        </Box>
      </Box>
      
      {/* Duration */}
      <Box sx={{ position: 'relative', mb: isCompleted ? 3 : 0, display: 'flex', alignItems: 'center' }}>
        <Box sx={{ 
          width: iconSize, 
          height: iconSize, 
          borderRadius: '50%',
          bgcolor: isCompleted ? '#059669' : '#F97316', // Green for completed, Orange for pending
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1,
          flexShrink: 0
        }}>
          {isCompleted ? (
            <AccessTimeOutlined sx={{ color: '#FFFFFF', fontSize: iconFontSize }} />
          ) : (
            <HourglassEmptyOutlined sx={{ color: '#FFFFFF', fontSize: iconFontSize }} />
          )}
        </Box>
        <Box sx={{ 
          ml: 1.5,
          display: 'flex', 
          alignItems: 'center',
          bgcolor: isCompleted ? "#F0FDF4" : "#FFF7ED", // Green bg for completed, Orange bg for pending
          borderRadius: 1.5, 
          px: 1, 
          py: 0.5, 
          border: isCompleted ? '1px solid #D1FAE5' : '1px solid #FFEDD5', // Green border for completed, Orange border for pending
          maxWidth: '100%'
        }}>
          <Typography variant="caption" sx={{ color: isCompleted ? "#059669" : "#EA580C", fontWeight: 600, whiteSpace: 'nowrap' }}>
            {!isCompleted ? 'Pending' : `${durationMinutes} min`}
          </Typography>
        </Box>
      </Box>
      
      {/* Completion (only shown if completed) */}
      {isCompleted && (
        <Box sx={{ position: 'relative', display: 'flex', alignItems: 'center' }}>
          <Box sx={{ 
            width: iconSize, 
            height: iconSize, 
            borderRadius: '50%',
            bgcolor: '#16A34A',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1,
            flexShrink: 0
          }}>
            <CheckCircle sx={{ color: '#FFFFFF', fontSize: iconFontSize }} />
          </Box>
          <Box sx={{ ml: 1.5, overflow: 'hidden' }}>
            <Typography variant={isMobile ? "caption" : "body2"} color="#16A34A" fontWeight={600} noWrap>
              Completed
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', textOverflow: 'ellipsis' }}>
              {formattedEndDate} {formattedEndTime}
            </Typography>
          </Box>
        </Box>
      )}
    </Box>
    </>
  );
};

export default TaskTimeline;