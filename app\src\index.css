* {

    box-sizing: border-box;
}
body {
	/* overflow-y: scroll !important; */
	margin: 0;
	/* overflow-y: scroll !important; */
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
		'Ubuntu', '<PERSON><PERSON><PERSON>', 'Fi<PERSON> Sans', 'Droid Sans', 'Helvetica Neue',
		sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

code {
	font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
		monospace;
}

/* Side Nav CSS */
.drawerPaper {
	padding: 0 0.5rem;
}

.drawerPaper::-webkit-scrollbar {
	display: none;
}


/* Reusable Table */
.reusable-table .MuiCardContent-root {
	padding-bottom: 0;
	padding: 10px;
}

.reusable-table .MuiDataGrid-columnHeaders {
	background: #F5F5F5 !important;
}

.reusable-table .MuiDataGrid-columnHeader {
	border: 0;
	width: 100%;
	font-weight: bold !important;
}

.reusable-table .MuiDataGrid-columnSeparator {
	opacity: 0 !important;
}

.reusable-table .MuiDataGrid-columnHeaderTitle {
	font-weight: bold !important;
	line-height: normal !important;
	white-space: pre-wrap;
	font-size: 12px;
}

.purchaseOrder .css-mhc70k-MuiGrid-root {
	width: 100%;
}

.css-15lovja-MuiButtonBase-root-MuiTab-root.Mui-selected {
	color: #1D1D1D !important;
}

.singlePO .Mui-selected {
	color: #3B30C8 !important;
	border-bottom: 2px solid #3B30C8 !important;
	/* z-index: -1 !important; */

}

.MuiStepConnector-lineVertical {
	min-height: 100px !important;
}

.purchaseOrder .css-6l1m71-MuiTimeline-root {
	padding: 6px 0;
}

.purchaseOrder .Mui-selected {
	color: #3B30C8 !important;
}


/* Filter Accordion */
.MuiAccordion-root {
	min-height: 1 rem;
	margin-top: 0px !important;
	border: 1px solid;
	border-color: #E0E0E0;
}

.MuiAccordionSummary-root {
	min-height: 2rem !important;
	margin: 0px !important;
}

.css-lqkwnf-MuiPopper-root-MuiTooltip-popper {
	z-index: 1 !important;
	pointer-events: auto;
}

.css-yem0l2-MuiPopper-root-MuiTooltip-popper {
	z-index: 1 !important;
	pointer-events: auto;
}

/* Expandable search */
.search-icon .button {
	display: inline-block;
	font-size: 24px;
	height: 50px;
	line-height: 50px;
	text-align: center;
	margin: auto;
	color: rgba(0, 0, 0, 0.54);
	text-decoration: none;
	cursor: pointer;
	-moz-user-select: none;
	-khtml-user-select: none;
	-webkit-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.search-icon .button:hover {
	transition-duration: 0.4s;
	-moz-transition-duration: 0.4s;
	-webkit-transition-duration: 0.4s;
	-o-transition-duration: 0.4s;
	background-color: white;
	color: black;
}

.search-icon .search-container {
	position: relative;
	display: inline-block;
	margin: 4px 2px;
	height: 50px;
	width: 50px;
	vertical-align: bottom;
}

.search-icon .mglass {
	display: inline-block;
	pointer-events: none;
}

.search-icon .searchbutton {
	position: absolute;
	font-size: 22px;
	width: 100%;
	margin: 0;
	padding: 0;
}

.search-icon .search:focus+.searchbutton {
	transition-duration: 0.4s;
	-moz-transition-duration: 0.4s;
	-webkit-transition-duration: 0.4s;
	-o-transition-duration: 0.4s;
	background-color: white;
	color: black;
}

.search-icon .search {
	position: absolute;
	left: 49px;
	/* Button width-1px (Not 50px/100% because that will sometimes show a 1px line between the search box and button) */
	background-color: white;
	border: 0;
	padding: 0;
	width: 0;
	height: 100%;
	z-index: 10;
	transition-duration: 0.4s;
	-moz-transition-duration: 0.4s;
	-webkit-transition-duration: 0.4s;
	-o-transition-duration: 0.4s;
}

.search-icon .search:focus {
	width: 320px;
	/* Bar width+1px */
	padding: 0 16px 0 0;
}

.search-icon .expandright {
	left: auto;
	right: 49px;
	/* Button width-1px */
}

.search-icon .expandright:focus {
	padding: 0 0 0 16px;
}

.workbenchstatus#text {
	margin: auto;
	padding: 2rem;
}

.dates {
	text-align: center;
}

.rs-picker-default .rs-picker-toggle.rs-btn {

	padding-bottom: 7px;
	width: 266px;
}

/* picked date align */
.rs-picker-menu {
	z-index: '1444 !important';
}

.rs-anim-fade.rs-anim-in {
	z-index: '1444 !important';
}

.rs-picker-default .rs-picker-toggle {
	z-index: 5;
	padding-right: 67px;
	display: inline-block;
	outline: none;
	cursor: pointer;
	color: #575757;
	border: 1px solid #cccaca !important;
	padding-left: 44px;
	border-radius: 4px !important;
}

.rs-picker-toggle .rs-picker-toggle-placeholder {
	color: #c7c6c6 !important;
	font-size: 1rem;
	font-family: Arial, Helvetica, sans-serif;
}

/* calander align */
/* .rs-picker-toggle-caret {
    display: inline-block;
    margin-right: 240px;
    position: absolute;
    top: 8px;
    right: 12px;
    font-weight: normal;
    color: #8e8e93;
} */
.rs-picker-daterange-menu {
	z-index: 10000000000;
	/*added a random number*/
	top: 100px !important;
}

.rs-picker-toggle-value {
	font-size: 14px;
}

.rs-picker-has-value .rs-btn .rs-picker-toggle-value,
.rs-picker-has-value .rs-picker-toggle .rs-picker-toggle-value {
	color: #1D1D1D !important;
}

.confirmOrder-lineItem {
	margin: 2rem 0;
}

.confirmOrder-lineItem .MuiDataGrid-cell--editable {
	height: 40px !important;
}

.confirmOrder-lineItem .MuiDataGrid-cell--editable .MuiDataGrid-cellContent {
	width: 100%;
	height: 30px !important;
	text-align: left;
	border-radius: 5px;
	border: 0.1px solid #c5c2c2;
	padding: 0.5rem;
}

.dprConfig-lineItem {
	margin: 2rem 0;
}

.dprConfig-lineItem .MuiDataGrid-cell--editable {
	height: 40px !important;
	background: #ffffff;
}

.dprConfig-lineItem .MuiDataGrid-cell--editable .MuiDataGrid-cellContent {
	width: 100%;

	padding: 1rem 0;
}


.asn-lineItem .MuiDataGrid-cell--editable {
	height: 40px !important;
	background: #ffffff;
}

.asn-lineItem .MuiDataGrid-cell--editable .MuiDataGrid-cellContent {
	width: 100%;
	text-align: right;
	border-radius: 5px;
	border: 0.2px solid #000000;
	padding: 1rem 0;
}



.createInvoice-invDetails {
	margin: 2rem 0;
}

.createInvoice-invDetails .grossAmount {
	font-weight: bold;
}

.createInvoice-invDetails .MuiDataGrid-cell--editable {
	height: 40px !important;
	background: #ffffff;
}

.createInvoice-invDetails .MuiDataGrid-cell--editable .MuiDataGrid-cellContent {
	width: 100%;
	text-align: right;
	border-radius: 5px;
	border: 0.2px solid #000000;
	padding: 0.5rem;
}

.disabledBtn {
	pointer-events: none;
}

.disabledBtn .wbActionIcon {
	color: #e6e2e2;
}

.custom-row--selectedStatus-true,
.custom-row--acceptreject-ACCEPT {
	border-left: 2px solid green;
}

.custom-row--selectedStatus-false,
.custom-row--acceptreject-REJECT {
	border-left: 2px solid red;
}

.css-1xvansi-MuiAutocomplete-root .MuiOutlinedInput-root {
	padding: 0px !important;
}

.css-1u4zpwo-MuiSvgIcon-root-MuiStepIcon-root.Mui-active {
	color: #0FC31D !important;
}

.css-1u4zpwo-MuiSvgIcon-root-MuiStepIcon-root.Mui-completed {
	color: #0FC31D !important;
}

.MuiDataGrid-cellContent {
	line-height: normal !important;
	white-space: pre-wrap;
	text-overflow: ellipsis !important;
	max-height: inherit;
}

/* .MuiDataGrid-cell--withRenderer {
  	line-height: normal !important;
  	white-space: pre-wrap !important;
} */
.backdrop {
	color: "#fff";
	z-index: 2;
}

.reusable-collapsible-table {
	margin: 1rem 0;
}

.reusable-collapsible-table .MuiTableCell-head {
	font-weight: 700;
	line-height: 1rem;
	letter-spacing: 0.01071em;
}

.reusable-collapsible-table .MuiTableRow-head {
	background: #F5F5F5 !important;
}

html, body,header {
	padding:0px !important;
}
/* #root{
	overflow: auto;
} */

.css-1n4twyu-MuiInputBase-input-MuiOutlinedInput-input{
    height: 1em !important;
}
input::placeholder{
    font-size: 12px;
}
