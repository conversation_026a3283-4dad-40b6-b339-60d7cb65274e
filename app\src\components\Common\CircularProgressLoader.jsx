import { colors } from "../../constant/colors"
const CircularProgressLoader = ({ percentage, id }) => {

const getProgressGradient = (percentage) => {
  if (percentage === 100) return colors?.progressColors.complete;
  if (percentage >= 75) return colors?.progressColors.high;
  if (percentage >= 50) return colors?.progressColors.medium;
  if (percentage >= 25) return colors?.progressColors.low;
  return colors?.progressColors.minimal;
};

  // Create segments similar to the image
  const totalSegments = 16;
  const filledSegments = Math.round((percentage / 100) * totalSegments);
  const centerX = 30;
  const centerY = 30;
  const outerRadius = 20;
  const innerRadius = 15;
  const segmentAngle = (2 * Math.PI) / totalSegments;

  const createSegmentPath = (index) => {
    const startAngle = index * segmentAngle - Math.PI / 2; // Start from top
    const endAngle = (index + 1) * segmentAngle - Math.PI / 2;
    
    const x1 = centerX + innerRadius * Math.cos(startAngle);
    const y1 = centerY + innerRadius * Math.sin(startAngle);
    const x2 = centerX + outerRadius * Math.cos(startAngle);
    const y2 = centerY + outerRadius * Math.sin(startAngle);
    const x3 = centerX + outerRadius * Math.cos(endAngle);
    const y3 = centerY + outerRadius * Math.sin(endAngle);
    const x4 = centerX + innerRadius * Math.cos(endAngle);
    const y4 = centerY + innerRadius * Math.sin(endAngle);

    return `M ${x1} ${y1} L ${x2} ${y2} A ${outerRadius} ${outerRadius} 0 0 1 ${x3} ${y3} L ${x4} ${y4} A ${innerRadius} ${innerRadius} 0 0 0 ${x1} ${y1} Z`;
  };

  const gradientColors = getProgressGradient(percentage);

  return (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      width: '100%',
      height: '100%',
      padding: '8px'
    }}>
      <div style={{ 
        position: 'relative', 
        width: '60px', 
        height: '60px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}>
        <svg
          width="60"
          height="60"
          viewBox="0 0 60 60"
          style={{ 
            position: 'absolute',
            top: 0,
            left: 0
          }}
        >
          <defs>
            <linearGradient id={`segmentGradient-${id}`} x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor={gradientColors.start} />
              <stop offset="100%" stopColor={gradientColors.end} />
            </linearGradient>
          </defs>
          
          {/* Render all segments */}
          {Array.from({ length: totalSegments }, (_, index) => (
            <path
              key={index}
              d={createSegmentPath(index)}
              fill={index < filledSegments ? `url(#segmentGradient-${id})` : colors?.progressColors.inactive.fill}
              stroke={colors?.progressColors.inactive.stroke}
              strokeWidth="0.5"
              style={{
                transition: `fill 0.6s cubic-bezier(0.4, 0, 0.2, 1) ${index * 0.05}s`,
                filter: index < filledSegments ? `drop-shadow(0 1px 2px ${colors?.progressColors.shadow.dropShadow})` : 'none'
              }}
            />
          ))}
        </svg>
        
        {/* Center content */}
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1
        }}>
          {/* Percentage text */}
          <div style={{
            fontSize: '12px',
            fontWeight: '700',
            color: gradientColors.end,
            letterSpacing: '-0.025em'
          }}>
            {percentage}%
          </div>
        </div>
        
        {/* Inner circle with subtle glow */}
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '32px',
          height: '32px',
          borderRadius: '50%',
          zIndex: 0
        }} />
      </div>
    </div>
  );
};

export default CircularProgressLoader