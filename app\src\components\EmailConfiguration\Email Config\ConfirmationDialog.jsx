import React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";

const ConfirmationDialog = (props) => {
  return (
    <Dialog open={props?.open}>
      <DialogTitle
        className="styleMessageBox"
        sx={{
          height: "3rem",
          display: "flex",
          alignItems: "center",
          fontWeight: "500",
          borderRadius: "7px",
          background: "#F1F5FE",
          fontSize: "1.5rem",
          color: "black ",
        }}
      >
        Confirmation
      </DialogTitle>
      <DialogContent
        sx={{ minWidth: "20rem", display: "flex", alignItems: "center" }}
      >
        <DialogContentText sx={{ display: "flex", alignItems: "center" }}>
          {props?.message}
        </DialogContentText>
      </DialogContent>
      <DialogActions sx={{ height: "3rem", borderTop: "2px solid #d9d9d9" }}>
        {props?.creationType !== "Timeout" && (
          <Button
            variant="outlined"
            size="small"
            sx={{ color: "red", borderColor: "red !important" }}
            onClick={() => {
              props?.onClose("Cancel");
            }}
          >
            Cancel
          </Button>
        )}
        <Button
          variant="contained"
          size="small"
          onClick={() => props?.onClose(props?.creationType)}
        >
          Ok
        </Button>
      </DialogActions>
    </Dialog>
  );
};
export default ConfirmationDialog;
