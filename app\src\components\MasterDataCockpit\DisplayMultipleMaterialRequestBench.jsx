import {
  Box,
  Grid,
  Icon<PERSON>utton,
  Tabs,
  Typo<PERSON>,
  Stack,
  Paper,
  BottomNavigation,
  Button,
  CardContent,
} from "@mui/material";
import Tab from "@mui/material/Tab";
import React, { useState, useEffect } from "react";
import {
  iconButton_SpacingSmall,
  outermostContainer,
  container_Padding,
  outerContainer_Information,
  button_Primary,
  outermostContainer_Information,
  button_Outlined,
} from "../common/commonStyles";
import { useSelector, useDispatch } from "react-redux";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { useNavigate, useLocation } from "react-router-dom";
// import EditableField from "./EditFieldForDisplay";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import {
  setEditMultipleMaterial,
  setMultipleMaterial,
  setMultipleMaterialRequestBench,
} from "../../app/initialDataSlice";
import EditableFieldForMassMaterial from "./EditFieldForMassMaterial";
import { doAjax } from "../Common/fetchService";
import { destination_MaterialMgmt } from "../../destinationVariables";
import EditableField from "./EditFieldForDisplay";

const DisplayMultipleMaterialRequestBench = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [dropDownData, setDropDownData] = useState({});
  const [value, setValue] = useState(0);
  const [tablevalue, setTableValue] = useState([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isDisplayMode, setIsDisplayMode] = useState(true);
  let [factorsArray, setFactorsArray] = useState([]);
  const [materialDetails, setMaterialDetails] = useState([]);
  const [activeTab, setActiveTab] = useState(0);
  const [IDs, setIDs] = useState();
  const allTabs = useSelector((state) => state.tabsData);
  const reference = {
    basicData: "Basic Data",
  };
  const location = useLocation();
  const ChangesInMaterialDetail = useSelector(
    (state) => state.initialData.EditMultipleMaterial
  );
  const MultipleMaterial = useSelector(
    (state) => state.initialData.MultipleMaterialRequestBench
  );
  let taskData = useSelector((state) => state?.initialData?.IWMMyTask);
  let task = useSelector((state) =>state.userManagement.taskData)
  const selectedRowData = location.state.rowData;
  console.log('rowData', selectedRowData)
  console.log('task', task)
  const requestNumber = location.state.requestNumber;
  const PayloadData = useSelector((state) => state.payload);
  const onEdit = () => {
    setIsEditMode(true);
    // setIsExtendMode(false);
    setEditMultipleMaterial();
    setIsDisplayMode(false);
  };
  // console.log("select", MultipleMaterial.filter((item)=>item.Description === selectedRowData.description)[0]);

  let activeRow = {};
  let activeIndex = -1;
  for (let index = 0; index < MultipleMaterial.length; index++) {
    if (MultipleMaterial[index].Description === selectedRowData.description) {
      activeRow = MultipleMaterial[index];
      activeIndex = index;
      break;
    }
  }
  const handleChange = (event, newValue) => {
    setActiveTab(newValue);
    // console.log("newValue", newValue);
  };


  const getMassMaterialTable = () => {
    let payload = {};

if (task?.processDesc === "Mass Change") {
  payload = {
    massCreationId: "",
    massChangeId: task?.subject,
    screenName: "Change",
  };
} else if (task?.processDesc === "Mass Create") {
  payload = {
    massCreationId: task?.subject,
    massChangeId: "",
    screenName: "Create",
  };
}

// Example: Logging the payload
console.log(payload);

    // {
    //   massCreationId: requestNumber,
    //   massChangeId: "",
    //   screenName: "Create",
    // }
    ;
    const hSuccess = (data) => {
      setTableValue(data?.body[0])

      const responseBody = data.body[0].viewData;
      // setHeaderData(data.body.headerData);
      // setAdditionalDisplayData(data.body.additionalData);
      setIDs(data.body[0].IDs);
      // const displayAdditionalData = data.body.additionalData;
      const categoryKeys = Object.keys(responseBody);
      console.log("categorry", categoryKeys);
      setFactorsArray(categoryKeys);
      const mappedData = categoryKeys.map((category) => ({
        category,
        data: responseBody[category],
      }));
      setMaterialDetails(mappedData);
      console.log("materialDetails", materialDetails);
      dispatch(setMultipleMaterialRequestBench(data?.body));
      
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_MaterialMgmt}/data/displayMassMaterial`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  console.log("factorsArray", factorsArray);

  useEffect(() => {
    getMassMaterialTable();
    dispatch(setMultipleMaterialRequestBench(tablevalue));

  }, []);

  const tabContents = factorsArray
    .map((item) => {
      const ddata = Object.entries(allTabs).filter((i) => {
        return reference[i[0]]?.split(" ")[0] == item?.split(" ")[0];
      })[0]?.[1];

      const mdata = materialDetails.filter(
        (ii) => ii.category?.split(" ")[0] == item?.split(" ")[0]
      );

      if (mdata.length !== 0) {
        return { category: item?.split(" ")[0], data: mdata[0].data };
      }

      return { category: item?.split(" ")[0], data: ddata };
    })
    .map((categoryData, index) => {
      console.log("categorydata", categoryData);
      if (categoryData?.category == "Basic") {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              mt: 1,
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                  // ...container_columnGap,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent
                    sx={{
                      padding: "0",
                      paddingBottom: "0 !important",
                      paddingTop: "10px !important",
                    }}
                  >
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => {
                        // console.log("fieldDatatttt", field);
                        return (
                          <EditableField
                            // key={field.fieldName}
                            label={field.fieldName}
                            value={field.value}
                            onSave={(newValue) =>
                              handleFieldSave(field.fieldName, newValue)
                            }
                            // isEditMode={isEditMode}
                            // isExtendMode={isExtendMode}
                            type={field.fieldType}
                            field={field} // Update the type as needed
                          />
                        );
                      })}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      }
      // Handle other categories if needed
      return null;
    });

  console.log(tabContents, "lololol");

  return (
    <div>
      <Grid
        container
        style={{
          ...outermostContainer_Information,
          backgroundColor: "#FAFCFF",
        }}
      >
        <Grid sx={{ width: "inherit" }}>
          <Grid item md={12} style={{ padding: "16px", display: "flex" }}>
            {/* <Grid  sx={{ display: "flex" }}> */}
            <Grid item style={{ display: "flex", justifyContent: "flex-end" }}>
              <IconButton
                // onClick={handleBacktoRO}
                color="primary"
                aria-label="upload picture"
                component="label"
                sx={iconButton_SpacingSmall}
              >
                <ArrowCircleLeftOutlinedIcon
                  style={{ height: "1em", width: "1em", color: "#000000" }}
                  onClick={() => {
                    navigate(
                      "/RequestBench"
                    );
                    // dispatch(clearPayload());
                    // dispatch(clearOrgData());
                  }}
                />
              </IconButton>
            </Grid>
            <Grid md={8}>
              <Typography variant="h3">
                <strong>
                  Multiple Material : {selectedRowData.description}{" "}
                </strong>
              </Typography>
              <Typography variant="body2" color="#777">
                This view displays details of uploaded material
              </Typography>
            </Grid>
          </Grid>
          <Grid container display="flex" flexDirection="row" flexWrap="nowrap">
            <Box width="70%" sx={{ marginLeft: "40px" }}>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Material
                    </Typography>
                  </div>
                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    justifyContent="flex-start"
                  >
                    : {selectedRowData.material}
                  </Typography>
                </Stack>
              </Grid>

              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Material Type
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    : {selectedRowData.materialType}
                    {/* {displayData?.materialDescription?.materialDescription} */}
                  </Typography>
                </Stack>
              </Grid>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Description
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    : {selectedRowData.description}
                  </Typography>
                </Stack>
              </Grid>
              <Grid item sx={{ paddingTop: "2px !important" }}>
                <Stack flexDirection="row">
                  <div style={{ width: "15%" }}>
                    <Typography variant="body2" color="#777">
                      Industry Sector
                    </Typography>
                  </div>
                  <Typography variant="body2" fontWeight="bold">
                    : {selectedRowData.industrySector}
                  </Typography>
                </Stack>
              </Grid>
            </Box>
            <Box width="30%" sx={{ marginLeft: "40px" }}>
              <Grid item>
                <Stack flexDirection="row">
                  <Typography
                    variant="body2"
                    color="#777"
                    style={{ width: "30%" }}
                  >
                    {/* {item.info ? item.desc : ""} */}
                  </Typography>

                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    sx={{ width: "8%", textAlign: "center" }}
                  >
                    {/* {item.info ? ":" : ""} */}
                  </Typography>

                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    justifyContent="flex-start"
                  >
                    {/* {item?.info?.code}
                                {item?.info?.code && item?.info?.desc
                                  ? " - "
                                  : null} */}
                    {/* {item?.info?.desc} */}
                  </Typography>
                </Stack>
              </Grid>
            </Box>
          </Grid>

          <Grid container style={{ padding: "16px" }}>
            <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
              <Tabs
                value={activeTab}
                onChange={handleChange}
                variant="scrollable"
                sx={{
                  background: "#FFF",
                  borderBottom: "1px solid #BDBDBD",
                  width: "100%",
                }}
                aria-label="mui tabs example"
              >
                {factorsArray.map((factor, index) => (
                  <Tab
                    sx={{ fontSize: "12px", fontWeight: "700" }}
                    key={index}
                    label={factor}
                  />
                ))}
              </Tabs>
            </Box>
            <Grid
              key={tabContents}
              container
              item
              md={12}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                // borderRadius: "8px",
                // border: "1px solid #E0E0E0",
                mt: 1,
                // boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                // padding: "10px",
                mb: 1,
              }}
            >         
              {tabContents &&
                tabContents[activeTab]?.map((cardContent, index) => (
                  <Box key={index} sx={{ mb: 2, width: "100%" }}>
                    <Typography variant="body2">{cardContent}</Typography>
                  </Box>
                ))}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {isEditMode ? (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
            }}
            value={value}
            onChange={(newValue) => {
              setValue(newValue);
            }}
          >
            <Button
              size="small"
              variant="contained"
              onClick={() => {
                navigate("/masterDataCockpit/materialMaster/massMaterialTable");
              }}
            >
              Save
            </Button>
          </BottomNavigation>
        </Paper>
      ) : (
        ""
      )}
    </div>
  );
};

export default DisplayMultipleMaterialRequestBench;
