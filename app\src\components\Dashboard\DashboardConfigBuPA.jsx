import dashboardKeyWord from "./DashboardKeyword.jsx";
import colorLib from '@kurkle/color';

const transparentize=(value, opacity)=> {
    var alpha = opacity === undefined ? 0.5 : 1 - opacity;
    return colorLib(value).alpha(alpha).rgbString();
  }

const dashboardConfigBupa = {
  BUPA: {
    product: true,

    TabPanel: [
      { uid: 0, name: "Status by Scenario", icon: "HowToReg", required: true },
      {
        uid: 1,
        name: "Bottleneck",
        icon: "RunningWithErrors",
        required: true,
      }
    
    ],
    Tiles: [
      // {
      //   uid: 4,
      //   name: "Form To Supplier",
      //   count: dashboardKeyWord.FormToSupplier,
      //   isComponent:true,
      //   required: true,
      //   width: 2,
      //   type: "RB",
      //   status: "0",
      // },
      /*{
        uid: 1,
        name: "Form To Supplier",
        count: dashboardKeyWord.FormToSupplier,
        required: true,
        width: 2,
        type: "RB",
        status: "0",
      },
      {
        uid: 1,
        name: "Buyer Review",
        count: dashboardKeyWord.BuyerReview,
        required: true,
        width: 2,
        type: "RB",
        status: "1",
      },
      {
        uid: 1,
        name: "Finance Review",
        count: dashboardKeyWord.FinanceReview,
        required: true,
        width: 2,
        type: "RB",
        status: "2",
      },
      {
        uid: 1,
        name: "Compliance Review",
        count: dashboardKeyWord.ComplianceReview,
        required: true,
        width: 2,
        type: "RB",
        status: "4",
      },
      {
        uid: 1,
        name: "Procurement Lead Review",
        count: dashboardKeyWord.ProcurementLeadReview,
        required: true,
        width: 2,
        type: "RB",
        status: "3",
      },
      
      

      {
        uid: 1,
        name: "Completed",
        count: dashboardKeyWord.Completed,
        required: true,
        width: 2,
        type: "RB",
        status: "5",
      }*/,
      {
        uid: 0,
        name: "Create",
        count: dashboardKeyWord.Create,
        required: true,
        width: 2,
        type: "RBEC",
        status: "Create",
        color: transparentize('#4dc9f6', 0.7),
        borderColor: '#4dc9f6'
      },
      {
        uid: 0,
        name: "Change",
        count: dashboardKeyWord.Change,
        required: true,
        width: 2,
        type: "RBEC",
        status: "Change",
        color:transparentize('#f6d55c', 0.7),
        borderColor: '#f6d55c'
      },
      {
        uid: 0,
        name: "Extend",
        count: dashboardKeyWord.Extend,
        required: true,
        width: 2,
        type: "RBEC",
        status: "Extend",
        color:transparentize('#537bc4', 0.7),
        borderColor: '#537bc4'
      },
      {
        uid: 0,
        name: "Create With Upload",
        count: dashboardKeyWord.MassCreate,
        required: true,
        width: 2,
        type: "RBEC",
        status: "Mass Create",
        color: transparentize('#00a950', 0.7),
        borderColor: '#00a950'
      },
      {
        uid: 0,
        name: "Change With Upload",
        count: dashboardKeyWord.MassChange,
        required: true,
        width: 2,
        type: "RBEC",
        status: "Mass Change",
        color: transparentize('#8549ba', 0.7),
        borderColor: '#8549ba'
      },
      {
        uid: 0,
        name: "Extend With Upload",
        count: dashboardKeyWord.MassExtend,
        required: true,
        width: 2,
        type: "RBEC",
        status: "Mass Extend",
        color: transparentize('#ff6384', 0.7),
        borderColor: '#ff6384'
      },

    ],
    Graphs: [
      // {
      //   uid: 1,
      //   id: 1,
      //   name: "Initiated",
      //   count: dashboardKeyWord.Open,
      //   required: true,
      //   xaxis: "poNumber",
      //   yaxis: "netPrice",
      //   type: "po",
      //   width: 6,
      // },
      {
        uid: 1,
        id: 1,
       // name: "Onboarding Progress by Status",
       name: "Time Log Based on Roles",
        count: dashboardKeyWord.OnboardBar,
        required: true,
        stackedBar: true,
        isStacked:true,
        xaxis: "status",
        yaxis: "statusCount",
        yaxisHeader:"Requests",

        type: "po",
        width: 12,
      },
      // {
      //   uid: 1,
      //   id: 3,
      //   name: "Drafts",
      //   count: dashboardKeyWord.Confirmed,
      //   required: true,
      //   xaxis: "poNumber",
      //   yaxis: "netPrice",
      //   type: "po",
      //   width: 6,
      // },
      // {
      //   uid: 1,
      //   id: 4,
      //   name: "Submitted",
      //   count: dashboardKeyWord.Delivered,
      //   required: true,
      //   xaxis: "poNumber",
      //   yaxis: "netPrice",
      //   type: "po",
      //   width: 6,
      // },
      {
        uid: 0,
        id: 1,
        name: "Status ",
        count: dashboardKeyWord.pieStatus,
        required: true,
        xaxis: "status",
        yaxis: "statusCount",
        type: "RBEC",
        isPie: true,
        width: 6,
      },
      {
        uid: 0,
        id: 0,
        name: "Extend Table",
        count: dashboardKeyWord.ExtendTable,
        required: true,
        width: 6,
        isTable: true,
      },
      {
        uid: 2,
        id: 1010,
        name: "Extend Table",
        count: dashboardKeyWord.BottleNeckTable,
        required: true,
        width: 6,
        isTable2: true,
        isTable: false,
      },
      {
        uid: 2,
        id: 1011,
        name: "Extend Table",
        count: dashboardKeyWord.BottleNeckTable2,
        required: true,
        width: 6,
        isTable3: true,
        isTable2: false,
        isTable: false,
      },
      // {
      //   uid: 2,
      //   id: 1012,
      //   name: "Bottleneck by Status",
      //   count: dashboardKeyWord.BottleNeck,
      //   required: true,
      //   xaxis: "name",
      //   yaxis: "days",
      //   yaxisHeader:"Days",
      //   type: "po",
      //   width: 12,
      // },
      {
        uid: 2,
        id: 1091,
        name: "",
        count: dashboardKeyWord.BottleNeckGraph,
        required: true,
        width: 12,
        isTable3: false,
        isTable2: false,
        isTable: false,
        isgroup:true

      },
      // {
      //   uid: 4,
      //   id: 1,
      //   name: "Status ",
      //   count: dashboardKeyWord.CycleTime,
      //   required: true,
      //   xaxis: "name",
      //   yaxis: "days",
      //   type: "po",
      //   width: 12,
      // },
    ],
  },
};
export default dashboardConfigBupa;
