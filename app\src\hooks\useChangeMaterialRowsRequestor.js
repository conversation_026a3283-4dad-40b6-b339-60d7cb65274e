import { doAjax } from '@components/Common/fetchService';
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';
import { setChangeFieldRows, setDataLoading, updateSelectedRows, setChangeFieldRowsDisplay, setWhseData, setMatlNoData, setPlantData } from '../app/payloadslice';
import { updateCurrentCount, updateNextButtonStatus, updateTotalCount } from "../app/paginationSlice"
import { setDropDown } from "../app/dropDownDataSlice";
import { v4 as uuidv4 } from "uuid";
import { destination_MaterialMgmt } from '../destinationVariables';
import { TEMPLATE_KEYS } from '@constant/changeTemplates';
import { API_CODE, CHANGE_KEYS, DIALOUGE_BOX_MESSAGES } from '@constant/enum';
import { END_POINTS } from '@constant/apiEndPoints';
import useLogger from './useLogger';

const useChangeMaterialRowsRequestor = () => {
    const paginationData = useSelector((state) => state.paginationData);
    const changeFieldRows = useSelector((state) => state.payload.changeFieldRows)
    const whseList = useSelector((state) => state.payload.whseList)
    const matNoList = useSelector((state) => state.payload.matNoList)
    const plantList = useSelector((state) => state.payload.plantList)
    const changeFieldRowsDisplay = useSelector((state) => state.payload.changeFieldRowsDisplay)
    const selectedRowData = useSelector((state) => state.payload.selectedRows)
    const dispatch = useDispatch();
    const { customError } = useLogger()
    const [errorState, setErrorState] = useState({
      errorText: false,
      errorTextMessage: "",
    });
      const fetchDisplayDataRequestor = async(templateName, convertedValues) => {
        dispatch(setDataLoading(true));
        let url, payload;
        if(templateName === TEMPLATE_KEYS?.LOGISTIC) {
          payload = {
            materialNo: convertedValues?.[CHANGE_KEYS?.MATERIAL_NUM] || "",
            division: convertedValues?.[CHANGE_KEYS?.DIVISION] || "",
            plant: convertedValues?.[CHANGE_KEYS?.PLANT] || "",
            page: paginationData?.page,
            size: paginationData?.size
          }
          url = `/${destination_MaterialMgmt}/${END_POINTS?.CHG_DISPLAY_REQUESTOR?.LOGISTIC}`
        }
        else if(templateName === TEMPLATE_KEYS?.ITEM_CAT) {
          payload = {
            materialNo: convertedValues?.[CHANGE_KEYS?.MATERIAL_NUM] || "",
            salesOrg: convertedValues?.[CHANGE_KEYS?.SALES_ORG] || "",
            distrChan: convertedValues?.[CHANGE_KEYS?.DIST_CHNL] || "",
            division: convertedValues?.[CHANGE_KEYS?.DIVISION] || "",
            page: paginationData?.page,
            size: paginationData?.size
          }
          url = `/${destination_MaterialMgmt}/${END_POINTS?.CHG_DISPLAY_REQUESTOR?.SALES}`
        }
        else if(templateName === TEMPLATE_KEYS?.MRP) {
          payload = {
            materialNo: convertedValues?.[CHANGE_KEYS?.MATERIAL_NUM] || "",
            mrpCtrler: convertedValues?.[CHANGE_KEYS?.MRP_CTRLER] || "",
            plant: convertedValues?.[CHANGE_KEYS?.PLANT] || "",
            division: convertedValues?.[CHANGE_KEYS?.DIVISION] || "",
            page: paginationData?.page,
            size: paginationData?.size
          }
          url = `/${destination_MaterialMgmt}/${END_POINTS?.CHG_DISPLAY_REQUESTOR?.MRP}`
        }
        else if(templateName === TEMPLATE_KEYS?.UPD_DESC) {
          payload = {
            materialNo: convertedValues?.[CHANGE_KEYS?.MATERIAL_NUM] || "",
            division: convertedValues?.[CHANGE_KEYS?.DIVISION] || "",
            plant: convertedValues?.[CHANGE_KEYS?.PLANT] || "",
            page: paginationData?.page,
            size: paginationData?.size
          }
          url = `/${destination_MaterialMgmt}/${END_POINTS?.CHG_DISPLAY_REQUESTOR?.DESC}`
        }
        else if(templateName === TEMPLATE_KEYS?.WARE_VIEW_2) {
          payload = {
            materialNo: convertedValues?.[CHANGE_KEYS?.MATERIAL_NUM] || "",
            whseNo: convertedValues?.[CHANGE_KEYS?.WAREHOUSE] || "",
            plant: convertedValues?.[CHANGE_KEYS?.PLANT] || "",
            division: convertedValues?.[CHANGE_KEYS?.DIVISION] || "",
            page: paginationData?.page,
            size: paginationData?.size
          }
          url = `/${destination_MaterialMgmt}/${END_POINTS?.CHG_DISPLAY_REQUESTOR?.WAREHOUSE}`
        }
        else if(templateName === TEMPLATE_KEYS?.CHG_STAT) {
          payload = {
            materialNo: convertedValues?.[CHANGE_KEYS?.MATERIAL_NUM] || "",
            salesOrg: convertedValues?.[CHANGE_KEYS?.SALES_ORG] || "",
            distrChan: convertedValues?.[CHANGE_KEYS?.DIST_CHNL] || "",
            division: convertedValues?.[CHANGE_KEYS?.DIVISION] || "",
            page: paginationData?.page,
            size: paginationData?.size
          }
          url = `/${destination_MaterialMgmt}/${END_POINTS?.CHG_DISPLAY_REQUESTOR?.CHG_STATUS}`
        }
        else if(templateName === TEMPLATE_KEYS?.SET_DNU) {
          payload = {
            materialNo: convertedValues?.[CHANGE_KEYS?.MATERIAL_NUM] || "",
            salesOrg: convertedValues?.[CHANGE_KEYS?.SALES_ORG] || "",
            distrChan: convertedValues?.[CHANGE_KEYS?.DIST_CHNL] || "",
            division: convertedValues?.[CHANGE_KEYS?.DIVISION] || "",
            plant: convertedValues?.[CHANGE_KEYS?.PLANT] || "",
            page: paginationData?.page,
            size: paginationData?.size
          }
          url = `/${destination_MaterialMgmt}/${END_POINTS?.CHG_DISPLAY_REQUESTOR?.SET_DNU}`
        }
        return new Promise((resolve, reject) => {
          const hSuccess = (data) => {
            dispatch(updateTotalCount(data?.totalElements))
            if(data?.totalPages === 1 || data?.currentPage+1 === data?.totalPages) {
            dispatch(updateCurrentCount(data?.totalElements))
            dispatch(updateNextButtonStatus(true))
            }
            else {
            dispatch(updateCurrentCount((data?.currentPage+1) * data?.pageSize))
            }
            const result =
              templateName === TEMPLATE_KEYS?.LOGISTIC ? processedLogisticData(data?.body) :
              templateName === TEMPLATE_KEYS?.ITEM_CAT ? processedItemCatData(data?.body) :
              templateName === TEMPLATE_KEYS?.MRP ? processedMRPData(data?.body) :
              templateName === TEMPLATE_KEYS?.UPD_DESC ? processedUpdDescData(data?.body) :
              templateName === TEMPLATE_KEYS?.WARE_VIEW_2 ? processedWarehouseData(data?.body) :
              templateName === TEMPLATE_KEYS?.CHG_STAT ? processedChangeStatusData(data?.body) :
              templateName === TEMPLATE_KEYS?.SET_DNU ? processedSetToDNUData(data?.body) : [];
            
            

            if (Array.isArray(result)) {
              dispatch(setChangeFieldRows([...changeFieldRows, ...result]));
              dispatch(setChangeFieldRowsDisplay({ ...changeFieldRowsDisplay, [paginationData?.page]: result }));
            } else if (typeof result === "object" && result !== null) {
              const updatedChangeFieldRows = { ...changeFieldRows };
            
              Object?.keys(result)?.forEach((key) => {
                updatedChangeFieldRows[key] = [
                  ...(updatedChangeFieldRows[key] || []),
                  ...result[key],
                ];
              });
            
              dispatch(setChangeFieldRows(updatedChangeFieldRows));
              dispatch(setChangeFieldRowsDisplay({ ...changeFieldRowsDisplay, [paginationData?.page]: result }));
            }
            dispatch(setDataLoading(false));
    
            let idsArray;
            if (Array.isArray(result)) {
              idsArray = result?.map((row) => row?.id);
              dispatch(updateSelectedRows([...selectedRowData, ...idsArray]));
            } else if (typeof result === "object" && result !== null) {
              idsArray = Object.keys(result).reduce((acc, key) => {
                acc[key] = result[key]?.map((row) => row?.id) || [];
                return acc;
              }, {});
              const updatedSelectedRows = { ...selectedRowData };
            
              Object?.keys(idsArray)?.forEach((key) => {
                updatedSelectedRows[key] = [
                  ...(updatedSelectedRows[key] || []),
                  ...idsArray[key],
                ];
              });
            
              dispatch(updateSelectedRows(updatedSelectedRows));
            }
    
            resolve(data?.body);
          };
    
          const hError = () => {
            dispatch(setDataLoading(false));
            reject(new Error(DIALOUGE_BOX_MESSAGES?.ERROR_MSG));
          };
    
          doAjax(url, "post", hSuccess, hError, payload);
        });
      }
      const processedLogisticData = (data) => {
        const result = [];
        let index = 1;
        const matNoSet = new Set();
      
        data.forEach((parent) => {
          parent.ToLogisticdata.forEach((child) => {
            matNoSet.add(child.Material);
            const transformedChild = {
              ...child,
              id: uuidv4(),
              slNo: index++,
              MatlType: parent?.MatlType || "",
            };
            result.push(transformedChild);
          });
        });
      
        dispatch(setMatlNoData([...matNoList, ...matNoSet]))
        return result;
      }
      const processedItemCatData = (data) => {
        const result = [];
        let index = 1;
        const matNoSet = new Set();
      
        data.forEach((parent) => {
          parent.Tosalesdata.forEach((child) => {
            matNoSet.add(child.Material);
            const transformedChild = {
              ...child,
              id: uuidv4(),
              slNo: index++,
              MatlType: parent?.MatlType || "",
            };
            result.push(transformedChild);
          });
        });
      
        dispatch(setMatlNoData([...matNoList, ...matNoSet]))
        return result;
      }
      const processedMRPData = (data) => {
        const result = {
          "Basic Data": [],
          "Plant Data": [],
        };
        let clientDataSlNo = 1;
        let plantDataSlNo = 1;
        const matNoSet = new Set();
        const plantSet = new Set();
      
        data.forEach((parent) => {
          const { Tomrpupdate, ToBasicdata, Material, MatlType } = parent;
          matNoSet.add(Material);

          result["Basic Data"].push({
            ...ToBasicdata,
            id: uuidv4(),
            slNo: clientDataSlNo++,
            type: "Basic Data",
            MatlType,
          });

          Tomrpupdate.forEach((plantData) => {
            plantSet.add(plantData?.Plant);
            result["Plant Data"].push({
              ...plantData,
              id: uuidv4(),
              slNo: plantDataSlNo++,
              type: "Plant Data",
            });
          });
        });
      
        dispatch(setMatlNoData([...matNoList, ...matNoSet]))
        dispatch(setPlantData([...plantList, ...plantSet]))
        return result;
      };
      const processedChangeStatusData = (data) => {
        const result = {
          "Basic Data": [],
          "Plant Data": [],
          "Sales Data": [],
        };
        let basicDataSlNo = 1;
        let plantDataSlNo = 1;
        let salesDataSlNo = 1;
        const matNoSet = new Set();
      
        data.forEach((parent) => {
          // Extract keys for Basic Data by excluding Tomrpupdate
          const { Tosalesdata, ToBasicdata, Toplantdata, Material, MatlType } = parent;
          matNoSet.add(Material);
      
          result["Basic Data"].push({
            ...ToBasicdata,
            id: uuidv4(),
            slNo: basicDataSlNo++,
            type: "Basic Data",
            MatlType,
          });

          Toplantdata?.forEach((plantData) => {
            result["Plant Data"].push({
              ...plantData,
              id: uuidv4(),
              slNo: plantDataSlNo++,
              type: "Plant Data",
            });
          });

          Tosalesdata?.forEach((salesData) => {
            result["Sales Data"].push({
              ...salesData,
              id: uuidv4(),
              slNo: salesDataSlNo++,
              type: "Sales Data",
            });
          });
        });
      
        dispatch(setMatlNoData([...matNoList, ...matNoSet]))
        return result;
      };
      const processedSetToDNUData = (data) => {
        const result = {
          "Basic Data": [],
          "Plant Data": [],
          "Sales Data": [],
          "Description": [],
        };
        let basicDataSlNo = 1;
        let plantDataSlNo = 1;
        let salesDataSlNo = 1;
        let descDataSlNo = 1;
        const matNoSet = new Set();
      
        data.forEach((parent) => {
          // Extract keys for Basic Data by excluding Tomrpupdate
          const { Tosalesdata, ToBasicdata, Toplantdata, Tomaterialdescription, Material, MatlType } = parent;
          matNoSet.add(Material);
      
          result["Basic Data"].push({
            ...ToBasicdata,
            id: uuidv4(),
            slNo: basicDataSlNo++,
            type: "Basic Data",
            MatlType,
          });

          Toplantdata?.forEach((plantData) => {
            result["Plant Data"].push({
              ...plantData,
              id: uuidv4(),
              slNo: plantDataSlNo++,
              type: "Plant Data",
            });
          });

          Tosalesdata?.forEach((salesData) => {
            result["Sales Data"].push({
              ...salesData,
              id: uuidv4(),
              slNo: salesDataSlNo++,
              type: "Sales Data",
            });
          });
          
          Tomaterialdescription?.forEach((descData) => {
            result["Description"].push({
              ...descData,
              id: uuidv4(),
              slNo: descDataSlNo++,
              type: "Description",
            });
          });
        });
      
        dispatch(setMatlNoData([...matNoList, ...matNoSet]))
        return result;
      };
      const processedWarehouseData = (data) => {
        const result = [];
        const whseNoSet = new Set();
        let index = 1;
        const matNoSet = new Set();
      
        data.forEach((parent) => {
          parent.ToWarehousedata.forEach((child) => {
            whseNoSet.add(child.WhseNo);
            matNoSet.add(child.Material);
            const transformedChild = {
              ...child,
              id: uuidv4(),
              slNo: index++,
              MatlType: parent?.MatlType || "",
            };
            result.push(transformedChild);
          });
        });

        const whseListArray = [...whseNoSet];
        dispatch(setWhseData(whseListArray))
        dispatch(setMatlNoData([...matNoList, ...matNoSet]))
        return result;
      }
      const processedUpdDescData = (data) => {
        const result = [];
        let index = 1;
        const matNoSet = new Set();
      
        data.forEach((parent) => {
          parent.Tomaterialdescription.forEach((child) => {
            matNoSet.add(child.Material);
            const transformedChild = {
              ...child,
              id: uuidv4(),
              slNo: index++,
              MatlType: parent?.MatlType || "",
            };
            result.push(transformedChild);
          });
        });
      
        dispatch(setMatlNoData([...matNoList, ...matNoSet]))
        return result;
      }
      useEffect(() => {
        const fetchData = async () => {
          if (whseList?.length > 0) {
            const whseOptions = await fetchWarehouseOptions(whseList);
            dispatch(setDropDown({ keyName: "Unittype1", data: whseOptions }));
          }
        };
      
        fetchData();
      }, [whseList]);
      
      const fetchWarehouseOptions = async (whseList) => {
        const whseOptions = {};
      
        for (const whseNo of whseList) {
          let payload = { whseNo };
      
          try {
            const data = await new Promise((resolve) => {
              doAjax(`/${destination_MaterialMgmt}${END_POINTS?.DEPENDENT_LOOKUPS?.UNITTYPE}`,
              "post",
              (response) => {
                if (response.statusCode === API_CODE?.STATUS_200) {
                  resolve(response?.body);
                } else {
                  customError("Failed to fetch data");
                  resolve([]);
                }
              },
              (error) => {
                customError(error);
                resolve([]);
              }, 
              payload);
            });
      
            whseOptions[whseNo] = data;
          } catch (error) {
            customError(error);
            whseOptions[whseNo] = [];
          }
        }
      
        return whseOptions;
      };

      useEffect(() => {
        const fetchData = async () => {
          if (plantList?.length > 0) {
            const plantOptions = await fetchPlantOptions(plantList);
            dispatch(setDropDown({ keyName: "Spproctype", data: plantOptions }));
            const mrpCtrlerOptions = await fetchMrpCtrlerOptions(plantList);
            dispatch(setDropDown({ keyName: "MrpCtrler", data: mrpCtrlerOptions }));
          }
        };
      
        fetchData();
      }, [plantList]);
      
      const fetchPlantOptions = async (plantList) => {
        const plantOptions = {};
      
        for (const plant of plantList) {
          let payload = { plant };
      
          try {
            const data = await new Promise((resolve) => {
              doAjax(`/${destination_MaterialMgmt}${END_POINTS?.DATA?.GET_SPPROC_TYPE}`,
              "post",
              (response) => {
                if (response.statusCode === API_CODE?.STATUS_200) {
                  resolve(response?.body);
                } else {
                  customError("Failed to fetch data");
                  resolve([]);
                }
              },
              (error) => {
                customError(error);
                resolve([]);
              }, 
              payload);
            });
      
            plantOptions[plant] = data;
          } catch (error) {
            customError(error);
            plantOptions[plant] = [];
          }
        }
      
        return plantOptions;
      };

      const fetchMrpCtrlerOptions = async (plantList) => {
        const plantOptions = {};
      
        for (const plant of plantList) {
          let payload = { plant };
      
          try {
            const data = await new Promise((resolve) => {
              doAjax(`/${destination_MaterialMgmt}${END_POINTS?.DATA?.GET_MRP_CONTROLLER_BASED_ON_PLANT}`,
              "post",
              (response) => {
                if (response.statusCode === API_CODE?.STATUS_200) {
                  resolve(response?.body);
                } else {
                  customError("Failed to fetch data");
                  resolve([]);
                }
              },
              (error) => {
                customError(error);
                resolve([]);
              }, 
              payload);
            });
      
            plantOptions[plant] = data;
          } catch (error) {
            customError(error);
            plantOptions[plant] = [];
          }
        }
      
        return plantOptions;
      };
      
    return { fetchDisplayDataRequestor, errorState };
}

export default useChangeMaterialRowsRequestor