import React, { useEffect, useMemo } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
  Typography,
  DialogActions,
  Button,
  Box,
  IconButton
} from "@mui/material";
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import GetAppIcon from '@mui/icons-material/GetApp';
import IosShareOutlinedIcon from '@mui/icons-material/IosShareOutlined';
import { styled } from "@mui/material/styles";
import { useSelector } from "react-redux";
import { saveExcel } from "../../../functions"
import { colors } from "@constant/colors";

const MaterialCell = styled(TableCell)(({ theme }) => ({
  padding: theme.spacing(2),
  border: 'none',
  backgroundColor: 'rgba(179, 236, 243, 0.5)',
}));

const MaterialBox = styled(Box)(({ theme }) => ({
  backgroundColor: colors?.primary?.whiteSmoke,
  padding: theme.spacing(1),
  border: '1px solid #E0E0E0',
  borderRadius: theme.shape.borderRadius,
  boxShadow: '0px 8px 15px rgba(0, 0, 0, 0.08), 0px 4px 6px rgba(115, 118, 122, 0.5)',
  minWidth: 120,
  textAlign: "center",
  fontWeight: "bold",
  color: theme.palette.text.primary,
}));

const HeaderBar = styled(Box)(({ theme }) => ({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  paddingLeft: theme.spacing(2),
  backgroundColor: theme.palette.grey[100],
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius,
  boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
}));

const ITEMS_PER_CELL = 4;

const MaterialConfirmDialog = ({ open = false, onClose = () => {}, handleOk = () => {}, message = "" }) => {
  const MATERIALS = useSelector((state) => state.payload.matNoList || []);
  const TOTAL_ITEMS = MATERIALS?.length || 0;

  // **Paginate Materials**
  const cells = useMemo(() => {
    const groupedCells = [];
    for (let i = 0; i < MATERIALS.length; i += ITEMS_PER_CELL) {
      groupedCells.push(MATERIALS.slice(i, i + ITEMS_PER_CELL));
    }
    return groupedCells;
  }, [MATERIALS]);

  const handleExportToExcel = () => {
    const materialRows = MATERIALS?.map((material, index) => ({
      id: index + 1,
      material: material,
    }));
    functions_ExportAsExcelMaterialData.convertJsonToExcel(materialRows);
  };

  const functions_ExportAsExcelMaterialData = {
      convertJsonToExcel: (materialRowsList) => {
        let excelColumns = [];
        excelColumns.push({ header: "Material", key: "material" });

        saveExcel({
          fileName: `Material List`,
          columns: excelColumns,
          rows: materialRowsList,
        })
      },
      
    };

  const handleClose = () => {
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      PaperProps={{
        sx: { borderRadius: 2, minWidth: "480px", maxHeight: "80vh" },
      }}
    >
      <DialogTitle sx={{ display: "flex", alignItems: "center", gap: 1, py: 2 }}>   
        <InfoOutlinedIcon fontSize="medium" sx={{ color: "0px 4px 6px rgba(115, 118, 122, 0.5)" }} />
        <Typography variant="h6" fontWeight="bold">
          Info: {message}
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <HeaderBar>
          <Typography variant="subtitle2" color="text.secondary" sx={{ marginLeft: "15px"}}>
            Total Materials: <strong>{TOTAL_ITEMS}</strong>
          </Typography>
          <IconButton onClick={handleExportToExcel} color="primary" sx={{ marginRight: "10px"}} title="Export Excel">
            <IosShareOutlinedIcon />
          </IconButton>
        </HeaderBar>

        <Box sx={{ pt: 0, pl: 2, pr: 2, pb: 0 }}>
          <StyledTableContainer component={Paper}>
            <Table>
              <TableBody>
                {cells.map((cell, index) => (
                  <TableRow key={index} sx={{ "&:last-child td": { borderBottom: 0 } }}>
                    <MaterialCell>
                      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 2, justifyContent: "center" }}>
                        {cell.map((material) => (
                          <MaterialBox key={material}>{material}</MaterialBox>
                        ))}
                      </Box>
                    </MaterialCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </StyledTableContainer>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 2, borderTop: 1, borderColor: "divider" }}>
        <Button
          onClick={handleClose}
          variant="outlined"
          color="warning"
          sx={{ minWidth: 100, textTransform: "none", fontWeight: "medium" }}
        >
          Close
        </Button>
        <Button
          onClick={handleOk}
          variant="contained"
          color="primary"
          sx={{ minWidth: 100, textTransform: "none", fontWeight: "medium" }}
        >
          Continue
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MaterialConfirmDialog;
