import React, { useState, useEffect, useMemo } from "react";
import { 
  Box, Button, FormControl, Grid, Select, MenuItem, 
  IconButton, Tooltip 
} from "@mui/material";
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import { useDispatch, useSelector } from "react-redux";
import { DataGrid } from "@mui/x-data-grid";
import { useLocation } from "react-router-dom";
import { showToast } from "../../../functions";
import { destination_ArticleMgmt } from "../../../destinationVariables";
import { button_Outlined } from "@components/Common/commonStyles";
import { doAjax } from "@components/Common/fetchService";
import { setAdditionalData } from "../../../app/payloadslice";
import { useChangeLogUpdate } from "@hooks/useChangeLogUpdate";
import { CHANGE_LOG_STATUSES, ERROR_MESSAGES, MATERIAL_VIEWS } from "@constant/enum";
import MaterialDescriptionColumn from "@components/Common/MaterialDescriptionColumn";
import { END_POINTS } from "../../../constant/apiEndPoints";
import { colors } from "@constant/colors";
import  useLang  from "@hooks/useLang";


const DescriptionTab = ({ materialID, selectedMaterialNumber,disabled }) => {
  const { t } = useLang();
  const dispatch = useDispatch();
  const location = useLocation();
  const { updateChangeLog } = useChangeLogUpdate();
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get("RequestId");
  const initialPayload = useSelector((state) => state.payload.payloadData);

  const payloadState = useSelector((state) => state.payload);
  const descriptionData = payloadState[materialID]?.additionalData || [];

  const [languages, setLanguages] = useState([]);

  useEffect(() => {
    fetchLanguage();
    updateGlobalMaterialDescription();
  }, [materialID]);

  const fetchLanguage = async () => {
    try {
      const responseHandler = (data) => setLanguages(data?.body || []);
      const errorHandler = () => showToast(ERROR_MESSAGES?.ERROR_FETCHING_LANGU, "error");
      doAjax(`/${destination_ArticleMgmt}${END_POINTS?.DATA?.GET_LANGUAGE}`, "get", responseHandler, errorHandler);
    } catch (error) {
      showToast(ERROR_MESSAGES?.ERROR_FETCHING_LANGU, "error");
    }
  };

  const updateGlobalMaterialDescription = () => {
    const globalMaterialDescription = payloadState[materialID]?.headerData?.globalMaterialDescription || payloadState[materialID]?.additionalData?.[0]?.materialDescription || "";
    let updatedData = [...descriptionData];
    
    if (!descriptionData.length) {
      updatedData = [{ id: 1, language: "EN", materialDescription: globalMaterialDescription }];
    } else {
      updatedData[0] = { ...updatedData[0], materialDescription: globalMaterialDescription };
    }

    dispatch(setAdditionalData({ materialID, data: updatedData }));
  };

  const handleLanguageChange = (event, params) => {
    const updatedData = descriptionData.map((row) =>
      row.id === params.id ? { ...row, language: event.target.value } : row
    );
    dispatch(setAdditionalData({ materialID, data: updatedData }));
  };

  const handleMaterialDescriptionChange = (id, value) => {
    const updatedData = descriptionData.map((row) =>
      row.id === id ? { ...row, materialDescription: value } : row
    );
    dispatch(setAdditionalData({ materialID, data: updatedData }));

    if (requestId && !CHANGE_LOG_STATUSES.includes(initialPayload?.RequestStatus)) {
      const selectedRow = descriptionData.find((row) => row.id === id);
      if (selectedRow) {
        updateChangeLog({
          materialID: selectedMaterialNumber,
          viewName: MATERIAL_VIEWS.DESCRIPTION,
          plantData: selectedRow.language,
          fieldName: "Material Description",
          jsonName: "materialDescription",
          currentValue: value,
          requestId: initialPayload?.RequestId,
          childRequestId:requestId,
          isDescriptionData: true,
          language: selectedRow.language,
        });
      }
    }
  };

  const handleAddNewRow = () => {
    const currentData = Array.isArray(descriptionData) ? descriptionData : [];

    const nextId = currentData.length > 0 
      ? Math.max(...currentData.map(row => row.id)) + 1 
      : 1;

    const newRow = {
      id: nextId, 
      language: "",
      materialDescription: "",
      isNew: true  
    };

    dispatch(setAdditionalData({ materialID, data: [...currentData, newRow] }));
  };

  const handleDeleteRow = (id) => {
    if (id === 1) return;

    const updatedData = descriptionData.filter(row => row.id !== id);
    dispatch(setAdditionalData({ materialID, data: updatedData }));
  };

  const columns = useMemo(
    () => [
      { field: "id", headerName: t("ID"), flex: 0.2, align: "center", headerAlign: "center" },
      {
        field: "language",
        headerName: t("Language"),
        flex: 0.4,
        type: "singleSelect",
        align: "center",
        headerAlign: "center",
        editable: true,
        renderCell: (params) => {
          const isFirstRow = params.id === 1;
          const selectedLanguages = descriptionData.filter((row) => row.id !== params.id).map((row) => row.language);

          return (
            <FormControl fullWidth>
              <Select
                sx={{ height: "31px", width: "100%" }}
                value={params.value || (isFirstRow ? "EN" : "")}
                onChange={(e) => !isFirstRow && handleLanguageChange(e, params)}
                displayEmpty
                renderValue={(selected) =>
                  selected ? 
                    `${selected} - ${languages.find((lang) => lang.code === selected)?.desc || ''}` : 
                    <span style={{ color: colors.primary.grey, fontSize: "12px" }}>Select Language</span>
                }
                disabled={isFirstRow || disabled}
              >
                {languages.map((lang) => (
                  <MenuItem key={lang.code} value={lang.code} disabled={selectedLanguages.includes(lang.code)}>
                    {`${lang.code} - ${lang.desc}`}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          );
        },
      },
      {
        field: "materialDescription",
        headerName: t("Material Description"),
        flex: 1,
        align: "center",
        headerAlign: "center",
        editable: false,
        renderCell: (params) => (
          <MaterialDescriptionColumn disabled={disabled} params={params} handleCellEdit={handleMaterialDescriptionChange} />
        ),
      },
      {
        field: "actions",
        headerName: t("Actions"),
        flex: 0.3,
        align: "center",
        headerAlign: "center",
        sortable: false,
        renderCell: (params) => {
          const isFirstRow = params.row.id === 1;
          return (
            <Tooltip title={params.row.isNew ? t("Delete row") : t("Cannot delete existing row")}>
              <span>
                <IconButton
                  onClick={() => handleDeleteRow(params.row.id)}
                  disabled={!params.row.isNew || disabled}
                  size="small"
                  color="error"
                >
                  <DeleteOutlineIcon fontSize="small" />
                </IconButton>
              </span>
            </Tooltip>
          );
        },
      },
    ],
    [descriptionData, languages, disabled]
  );

  return (
    <div>
      <Box sx={{ width: "50%", height: "50vh" }} className="confirmOrder-lineItem">
        <DataGrid
          rows={descriptionData ?? []}
          columns={columns}
          getRowId={(row) => row.id}
          hideFooter
          disableSelectionOnClick
          style={{ border: "1px solid #ccc", borderRadius: "8px", width: "100%" }}
        />
      </Box>
      <Grid>
        {!disabled && <Button variant="outlined" sx={{ ...button_Outlined, mt: 2 }} onClick={handleAddNewRow}>
          {t("Add Row")}
        </Button>}
      </Grid>
    </div>
  );
};

export default DescriptionTab;
