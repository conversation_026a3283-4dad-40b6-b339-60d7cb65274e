import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  destination_GeneralLedger_Mass,
  destination_ProfitCenter,
  destination_ProfitCenter_Mass,
} from "../../destinationVariables";
import { v4 as uuidv4 } from "uuid";
import { doAjax } from "../../components/Common/fetchService";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { useLocation, useNavigate } from "react-router-dom";
import DescriptionIcon from "@mui/icons-material/Description";
import CloseIcon from "@mui/icons-material/Close";
import { useChangeLogUpdateGl } from "@hooks/useChangeLogUpdateGl";
import useGeneralLedgerFieldConfig from "@hooks/useGeneralLedgerFieldConfig";
import {
  TextField,
  IconButton,
  Box,
  Typography,
  Paper,
  Button,
  Tabs,
  Tab,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Radio,
  RadioGroup,
  FormControlLabel,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Snackbar,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Grid,
  Checkbox,
  Tooltip,
  Autocomplete,
  TableContainer,
  Chip,
  FormLabel,
  TableRow,
  TableCell,
  TableBody,
  Table,
  TableHead
} from "@mui/material";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";

import { Add } from "@mui/icons-material";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import TaskAltIcon from "@mui/icons-material/TaskAlt";

import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import { colors } from "@constant/colors";
import { setGLRows, setSelectedRowIdGL, updateModuleFieldDataGL, setValidatedStatus, setdropdownDataForExtendedCode, setSelecteddropdownDataForExtendedCode, setGLPayload, setOpenDialog} from "@app/generalLedgerTabSlice";
import CloseFullscreenIcon from "@mui/icons-material/CloseFullscreen";
import CropFreeIcon from "@mui/icons-material/CropFree";
import useLang from "@hooks/useLang";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { CHANGE_LOG_STATUSES, DROP_DOWN_SELECT_OR_MAP, LOCAL_STORAGE_KEYS, MODULE_MAP, VALIDATION_STATUS } from "@constant/enum";
import ReusableBackDrop from "../../components/Common/ReusableBackDrop";
import ReusableDataTable from "../../components/Common/ReusableTable";
import GenericTabsGlobal from "../../components/MasterDataCockpit/GenericTabsGlobal";
import { cascadeNullify, checkDuplicateValidationGL, createPayloadForGL } from "../../functions";
import BottomNavGlobal from "../../components/RequestBench/RequestPages/BottomNavGlobal";
import { CHANGE_KEYS, MATERIAL_TYPE_DRODOWN, REQUEST_TYPE } from "../../constant/enum";
import moment from "moment/moment";
import { setDependentDropdown, setDropDown } from "./slice/generalLedgerDropDownSlice";
import { getLocalStorage } from "@helper/glhelper";
import useLogger from "@hooks/useLogger";
import useButtonDTConfig from "@hooks/useButtonDTConfig";



const RequestDetailsGL = ({ reqBench, apiResponses,setCompleted, setIsAttachmentTabEnabled,
  moduleName ,isDisabled}) => {


  const [selectedRow, setSelectedRow] = useState(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [rowTabData, setRowTabData] = useState({});
  const [dropdownDataCompany, setDropdownDataCompany] = useState([]);
  const [dropdownDataCompanyForExtend, setDropdownDataCompanyForExtend] = useState([]);
  const [dropdownDataAccountType, setDropdownDataAccountType] = useState([]);
  const [dropdownDataTaxJur, setDropdownDataTaxJur] = useState([]);
  const [dropdownDataAccountGroup, setDropdownDataAccountGroup] = useState([]);
  const [dropdownDataProfitCtrGrp, setDropdownDataProfitCtrGrp] = useState([]);
  const [dropdownDataFormPlanning, setDropdownDataFormPlanning] = useState([]);
  const [dropdownDataCOA, setDropdownDataCOA] = useState([]);
  const [dropdownDataCountry, setDropdownDataCountry] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState("");
  const [dropdownDataRegion, setDropdownDataRegion] = useState([]);
  const [dropdownDataSegment, setDropdownDataSegment] = useState([]);
  const [dropdownDataLanguage, setDropdownDataLanguage] = useState([]);
  const [missingFieldsDialogOpen, setMissingFieldsDialogOpen] = useState(false);
  const [missingFields, setMissingFields] = useState([]);
  const [isAddRowEnabled, setIsAddRowEnabled] = useState(false);
  const [isSaveAsDraftEnabled, setIsSaveAsDraftEnabled] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [openSnackBar, setOpenSnackBar] = useState(false);
  const [alertMsg, setAlertMsg] = useState('');
  const [alertType, setAlertType] = useState('success');
  const [isLoading, setIsLoading] = useState(false);
  const [validatedRows, setValidatedRows] = useState({});
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const { getButtonsDisplayGlobal } = useButtonDTConfig();
  const [originalRowData, setOriginalRowData] = useState({});
  const [originalTabData, setOriginalTabData] = useState({});
  const [isGridZoomed, setIsGridZoomed] = useState(false);
  const [isTabsZoomed, setIsTabsZoomed] = useState(false);
  const [openOrgData, setOpenOrgData] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [openAddMatPopup, setOpenAddMatPopup] = useState(false);
  const [withReference, setWithReference] = useState("yes");
  const [selectedMaterials, setSelectedMaterials] = useState(null);
  const [withRefValues, setWithRefValues] = useState({});
  const [dropDownData, setDropDownData] = useState({});
  const [selectedMatLines, setSelectedMatLines] = useState([]);
  const [isDropdownLoading, setIsDropdownLoading] = useState({
    "Material No": false,
  });
  const [lineNumberCounter, setLineNumberCounter] = useState(0);
  const [isFocused, setIsFocused] = useState(false);
  const [charCount, setCharCount] = useState({});
  const [validateEnabled, setValidateEnabled] = useState(true);
  const [duplicateTextDialogOpen, setDuplicateTextDialogOpen] = useState(false);
  const [duplicateTextDetails, setDuplicateTextDetails] = useState([]);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useLang();

  const initialPayload = useSelector((state) => state.payload.payloadData);
  const requestType = initialPayload?.RequestType;
  const isFieldMandatory = (fieldName) => mandatoryFields.includes(fieldName);
  let task = useSelector((state) => state?.userManagement.taskData);
  const { updateChangeLogGl } = useChangeLogUpdateGl();
  const validatedStatus = useSelector(
    (state) => state?.generalLedger?.validatedRowsStatus
  );
  const selectedExtendDropdownData = useSelector(
    (state) => state?.generalLedger?.selecteddropdownDataForExtendedCode
  );
  const fixedOption = "Basic Data";
  const [selectedViews, setSelectedViews] = useState([fixedOption]);
  const selectedRowId = useSelector((state) => state.generalLedger.selectedRowId);
  const companycodeExtendedTo = useSelector((state) => state.generalLedger.dropdownDataForExtendedCode);

  const generalLedgerTabs = useSelector((state) => {
    const tabs = state.generalLedger.generalLedgerTabs || [];
    return tabs.filter((tab) => tab.tab !== "Initial Screen");
  });

  const createPayloadCopyForChangeLog = useSelector((state) => state.changeLog.createChangeLogDataGL || []);
  const createChangeLogData = useSelector((state) => state.changeLog.createChangeLogDataGL);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const openDialog = useSelector((state) => state.generalLedger.isOpenDialog);
  const isreqBench = queryParams.get("reqBench");
  const isrequestId = queryParams.get("RequestId");
  const requestId = queryParams.get("RequestId");
  const selectedModule = getLocalStorage(LOCAL_STORAGE_KEYS.MODULE,true, {})
  const requestHeaderSlice = useSelector((state) => state.request.requestHeader)
  const selectedModuleSelector = DROP_DOWN_SELECT_OR_MAP[selectedModule] || (() => ({}));
  const allDropDownData = useSelector(selectedModuleSelector)
  const requestHeaderData = useSelector((state) => state.requestHeader);
  const { loading, error, fetchGeneralLedgerFieldConfig } =useGeneralLedgerFieldConfig(moduleName);
  const glRows = useSelector((state) => state.generalLedger.payload.rowsHeaderData);
  const [rows, setRows] = useState(glRows || []);
  const reduxPayload = useSelector((state) => state.generalLedger.payload);
  const rowsBodyData = useSelector(
    (state) => state.generalLedger.payload?.rowsBodyData || {}
  );
  let requestStatus = rowsBodyData?.[glRows[0]?.id]?.["Torequestheaderdata"]?.["RequestStatus"]
  const selectedCompanyCodeData = useSelector(
    (state) => state.generalLedger.payload?.rowsHeaderData[0]?.companyCode || {}
  );
  const { customError } = useLogger()  
  const firstrowData = useSelector(
    (state) => state.generalLedger.payload?.rowsHeaderData
  );
  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons)
  const rmSearchForm = useSelector(
    (state) => state.commonFilter["GeneralLedger"]
  );
  const selectAllOption = { code: "ALL", desc: "Select All" };
  const withRefParams = ["Chart Of Account", "Company Code", "Account Type", "Account Group", "GL Account"];
  const handleChangeExtendCompanycode = (event, newValue) => {
    const isSelectAllClicked = newValue?.some(
      (val) => val.code === selectAllOption.code
    );
    if (isSelectAllClicked) {
      const allWithoutSelectAll = companycodeExtendedTo?.[selectedRow?.id];
      setSelectedOptions(allWithoutSelectAll);
      dispatch(setSelecteddropdownDataForExtendedCode({
        uniqueId: selectedRow?.id,
        data: allWithoutSelectAll
      }));
    } else {
      dispatch(setSelecteddropdownDataForExtendedCode({
        uniqueId: selectedRow?.id,
        data: newValue
      }));
    }
  };
  const getOptionLabel = (option) =>
    option.code === "ALL" ? option.desc : `${option.code} - ${option.desc}`;
  const isOptionSelected = (option, value) =>
    value.some((val) => val.code === option.code);
  const toggleGridZoom = () => {
    setIsGridZoomed(!isGridZoomed);
    if (isTabsZoomed) setIsTabsZoomed(false);
  };
  const handleDialogClose = () => {
     dispatch(setOpenDialog(false));
  };
  const toggleTabsZoom = () => {
    setIsTabsZoomed(!isTabsZoomed);
    if (isGridZoomed) setIsGridZoomed(false);
  };
  useEffect(() => {
  if (glRows.length > 0 && !selectedRow) {
    setSelectedRow(glRows[0]);
  }
}, [glRows, selectedRow]);

   useEffect(() => {
    if (
      glRows.length >= 1 &&
      (glRows[0]?.chartOfAccount)
    ) {
       dispatch(setOpenDialog(false));
    }
  }, []);

    

  useEffect(() => {
    if (!generalLedgerTabs?.length && (withReference === "yes")) {
      fetchGeneralLedgerFieldConfig();
    }
  }, []);


  useEffect(() => {
    if (withReference === "no") {
      setWithRefValues({});
    }
    
  }, [withReference])

  useEffect(() => {
    const filtereddataforExtendedCompanyCode = dropdownDataCompany?.filter(
      (item) => item.code !== selectedCompanyCodeData
    );
    setDropdownDataCompanyForExtend(filtereddataforExtendedCompanyCode)
  }, [])

  useEffect(() => {
    if ((task?.ATTRIBUTE_1 || isrequestId)) {
      getButtonsDisplayGlobal("General Ledger", "MDG_DYN_BTN_DT", "v3");
    }
  }, [task]);


  useEffect(() => {
    if(glRows.length === 0){
      setIsAddRowEnabled(true);
    }
      if (!Array.isArray(glRows) ) {
        setIsAddRowEnabled(false);
        return;
      }

  
      const allRowsValidatedAndClean = glRows.every((row) => {
        const rowId = row?.id;
        if (!rowId) {
          return false;
        }
  
        const isValidated = validatedRows?.[String(rowId)] === true;
  
        const isClean = !isRowDirty(rowId);
  
        return isValidated && isClean;
      });
  
      setIsAddRowEnabled(allRowsValidatedAndClean);
    }, [glRows, rowsBodyData, validatedRows, originalRowData, originalTabData]);


  const getAllmandetoryjson = () => {
    const extractedFields = [];
    generalLedgerTabs.forEach((tabObj) => {
      const data = tabObj.data;
      Object.values(data).forEach((fieldArray) => {
        extractedFields.push(...fieldArray);
      });
    });
    return extractedFields
  }

  const columns = [
    {
      field: "included",
      headerName: "",
       width: 80,  
      minWidth: 80,
      align: "center",
      headerAlign: "center",
      sortable: false,
      disableColumnMenu: true,
      renderHeader: () => {
        const allChecked =
          glRows.length > 0 && glRows.every((row) => row.included);
        const someChecked = glRows.some((row) => row.included);
 
        return (
          <Checkbox
            indeterminate={!allChecked && someChecked}
            checked={allChecked}
            disabled={isDisabled}
            onChange={(e) => {
              const checked = e.target.checked;
              const updatedRows = glRows.map((row) => ({
                ...row,
                included: checked,
              }));
              dispatch(setGLRows(updatedRows));
            }}
          />
        );
      },
      renderCell: (params) => (
        <Checkbox
          checked={params.row.included}
          disabled={isDisabled}
          onChange={(e) =>
            handleRowInputChange(e.target.checked, params.row.id, "included")
          }
        />
      ),
    },
    {
      field: "lineNumber",
      headerName: "SL No,",
      width: 100,  
      minWidth: 100,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "chartOfAccount",
      headerName: "Chart Of Account",
      align: "center",
      headerAlign: "center",
      width: 250, 
      minWidth: 200,
      renderHeader: () => (
        <span>
          {t("Chart Of Account")}
          {isFieldMandatory("chartOfAccount") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.["COA"] || []}
            value={params.row.chartOfAccount}
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "chartOfAccount")
            }
            placeholder={t('Select Chart Of Account')}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "companyCode",
      headerName: "Company Code",
      align: "center",
      headerAlign: "center",
      width: 250,
      minWidth: 200,
      renderHeader: () => (
        <span>
          {t("Company Code")}
          {isFieldMandatory("companyCode") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.["CompanyCode"] || []}
            value={params.row.companyCode}
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "companyCode")
            }
            placeholder={t('Select Company Code')}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "accountType",
      headerName: "Account Type",
      width: 250, 
      minWidth: 200,
      renderHeader: () => (
        <span>
          {t("Account Type")}
          {isFieldMandatory("accountType") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.["accountType"] || []}
            value={params.row.accountType}
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "accountType")
            }
            placeholder={t('Select Account Type')}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "accountGroup",
      headerName: "Account Group",
      width: 250, 
      minWidth: 200,
       renderHeader: () => (
        <span>
          {t("Account Group")}
          {isFieldMandatory("accountGroup") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={allDropDownData?.["accountGroup"] || []}
            value={params.row.accountGroup}
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "accountGroup")
            }
            placeholder={t('Select Account Group')}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "glAccountNumber",
      headerName: "General Ledger Number",
      width: 250, 
      minWidth: 200,
      renderHeader: () => (
        <span>
          {t("General Ledger Number")}
          {isFieldMandatory("glAccountNumber") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        const accountGroupValue = params.row.accountGroup

        let finalAccountGroupValue;

        if (typeof accountGroupValue !== 'object') {
          finalAccountGroupValue = allDropDownData?.["accountGroup"]?.filter(
            (item) => item.code === accountGroupValue
          )[0];
        } else {
          finalAccountGroupValue = accountGroupValue;
        }
        const value = params.row.glAccountNumber || "";
        const isInvalid = value.length > 0 && value.length < 10;

        const handleKeyDown = (e) => {
          if (
            ["Backspace", "Delete", "Tab", "Escape", "Enter", "ArrowLeft", "ArrowRight"].includes(
              e.key
            )
          ) {
            return;
          }
          if (!/^\d$/.test(e.key)) {
            e.preventDefault();
          }
        };

        const handlePaste = (e) => {
          const paste = e.clipboardData.getData("text");
          if (!/^\d+$/.test(paste)) {
            e.preventDefault(); 
          }
        };
        return (
          <TextField
            value={value}
            onChange={(e) => {
              const numericValue = e.target.value.slice(0, 10);
              handleRowInputChange(numericValue, params.row.id, "glAccountNumber");
            }}
            onKeyDown={handleKeyDown}
            onPaste={handlePaste}
            variant="outlined"
            size="small"
            placeholder={finalAccountGroupValue?.FromAcct && finalAccountGroupValue?.ToAcct
              ? `${finalAccountGroupValue?.FromAcct} - ${finalAccountGroupValue?.ToAcct}`
              : `-`}
            fullWidth
            error={isInvalid}
            helperText={isInvalid ? "Number should be 10 digits" : ""}
            FormHelperTextProps={{
              sx: {
                marginTop: "30px",
                paddingLeft: "15px",
                position: "absolute",
                style: {
                  paddingBottom: "0px", 
                },
              },
            }}
            inputProps={{
              inputMode: "numeric",
              maxLength: 10,
            }}
            sx={{
              position: "relative",
              "& .MuiInputBase-root": {
                height: "45px",
                alignItems: "center",
              },
              "& .MuiFormHelperText-root": {
                marginLeft: "0px",
              },
              "& .MuiInputBase-root.Mui-disabled": {
                "& > input": {
                  WebkitTextFillColor: colors.black.dark,
                  color: colors.black.dark,
                },
              },
            }}
          />

        );
      }
    },
   
    {
      field: "shortDescription",
      headerName: "Short Description",
      width: 250,    
      minWidth: 200,
      renderCell: (params) => {
        const value = params.row.shortDescription || "";
        const maxFieldLength = 50;
        const [localValue, setLocalValue] = useState(value);

        const handleChangeValueInTable = (event) => {
          setLocalValue(event.target.value[0] === " " ? event.target.value.trimStart() : event.target.value?.replace(/[^a-zA-Z0-9-&()#.'/$%, ]/g, "")
          .replace(/\s{2,}/g, " ") 
          .replace(/\s*([-&()#.'/$%,])\s*/g, "$1")
          .replace(/([-&()#.'/$%,])\s+/g, "$1") 
          .trimStart()) 
        };

        const field = params?.row;
          let key = "longDescription"
         return (
          <TextField
            variant="outlined"
            size="small"
            fullWidth
            value={localValue}
            onFocus={() => setIsFocused(true)}
            
            placeholder={`Enter Short Description`}
            inputProps={{
              style: { textTransform: "uppercase" },
              maxLength: params?.row?.maxLength,
            }}
            
            onChange={(e) => {
              handleChangeValueInTable(e)
              let newValue = e.target.value
              .replace(/[^a-zA-Z0-9-&()#.'/$%, ]/g, "")
              .replace(/\s{2,}/g, " ") 
              .replace(/\s*([-&()#.'/$%,])\s*/g, "$1") 
              .replace(/([-&()#.'/$%,])\s+/g, "$1")
              .trimStart();
             
            }}
            onBlur={(event) => {
              event.stopPropagation();
              
              
              handleRowInputChange(event.target.value.toUpperCase(), params.row.id, "shortDescription");
              setIsFocused(false)
              
            }}
            onKeyDown={(event) => {
              event.stopPropagation();
            }}
          />
        );

       
      },
    },
     {
      field: "longDescription",
      headerName: "Long Description",
    
      width: 250,        
      minWidth: 200,
      renderCell: (params) => {
        const value = params.row.longDescription || "";
        const maxFieldLength = 50;
        const [localValue, setLocalValue] = useState(value);

        const handleChangeValueInTable = (event) => {
          setLocalValue(event.target.value[0] === " " ? event.target.value.trimStart() : event.target.value?.replace(/[^a-zA-Z0-9-&()#.'/$%, ]/g, "")
          .replace(/\s{2,}/g, " ") 
          .replace(/\s*([-&()#.'/$%,])\s*/g, "$1") 
          .replace(/([-&()#.'/$%,])\s+/g, "$1") 
          .trimStart()) 
        };

        const field = params?.row;
          let key = "longDescription"
         return (
          <TextField
            variant="outlined"
            size="small"
            fullWidth
            value={localValue}
            onFocus={() => setIsFocused(true)}
            
            placeholder={`Enter Long Description`}
            inputProps={{
              style: { textTransform: "uppercase" },
              maxLength: params?.row?.maxLength, 
            }}
            
            onChange={(e) => {
              handleChangeValueInTable(e)
              let newValue = e.target.value
              .replace(/[^a-zA-Z0-9-&()#.'/$%, ]/g, "")
              .replace(/\s{2,}/g, " ") 
              .replace(/\s*([-&()#.'/$%,])\s*/g, "$1")
              .replace(/([-&()#.'/$%,])\s+/g, "$1") 
              .trimStart();
              
            }}
            onBlur={(event) => {
              event.stopPropagation();
              
              
              handleRowInputChange(event.target.value.toUpperCase(), params.row.id, "longDescription");
              setIsFocused(false)
              
            }}
            onKeyDown={(event) => {
              event.stopPropagation();
            }}
          />
        );
      },
    },
    {
      field: "businessSegment",
      headerName: "Business Segment",
      width: 250,      
      minWidth: 200,
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={[{ code: "CRUDE", desc: "" }, { code: "INTERSTATE", desc: "" }, { code: "NA", desc: "" }] || []}
            value={params.row.businessSegment}
            onChange={(newValue) =>
              handleRowInputChange(newValue, params.row.id, "businessSegment")
            }
            placeholder={t('Select Business Segment')}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },

    {
      
      field: "CoCodeToExtend",
      headerName: "Company Code Extend To", 
      width: 250,     
      minWidth: 200,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <Button
            variant="contained"
            size="small"
            sx={{ marginLeft: "4px" }}
            onClick={() => {
              setOpenOrgData(true)
            }}
          >
            Company Code Extend To
          </Button>
        );
      },
      
    },
    {
      field: "action",
      headerName: "Action",
      width: 80,        
      minWidth: 80,
      headerAlign: "center",
      renderHeader: () => <span style={{ fontWeight: "bold" }}>{t("Action")}</span>,
      renderCell: (params) => {

        const rowId = params.row.id;
        const rowData = {
          id: rowId,
          ...rowsBodyData[rowId],
        };

        const validateStatus = getValidationStatus(rowId);

        let mandetoryFieldsData = getAllmandetoryjson()

        const minrangeOfGlAccount = parseInt(params.row.accountGroup?.FromAcct);
        const maxrangeOfGlAccount = parseInt(params.row.accountGroup?.ToAcct);
        const glValue = parseInt(params.row?.glAccountNumber);
        const isInRange =
          !isNaN(minrangeOfGlAccount) &&
          !isNaN(maxrangeOfGlAccount) &&
          glValue >= minrangeOfGlAccount &&
          glValue <= maxrangeOfGlAccount;



        const handleValidateClick = (e) => {
          e.stopPropagation();
          if (glValue == '' || glValue == NaN || glValue == undefined) {
            if (isInRange) {
              handleValidate(params.row, rowData, generalLedgerTabs);
            } else {
              setAlertType('error');
              setAlertMsg('GlAccount not In Range.');
              setOpenSnackBar(true);
            }
          } else {
            handleValidate(params.row, rowData, generalLedgerTabs);
          }

        };

        return (
          <Box>
            <Tooltip
              title={
                validateStatus === "success"
                  ? "Validated Successfully"
                  : validateStatus === "error"
                    ? "Validation Failed"
                    : "Click to Validate"
              }
            >
              <IconButton onClick={handleValidateClick} color={validateStatus}>
                {validateStatus === "error" ? (
                  <CancelOutlinedIcon />
                ) : (
                  <TaskAltIcon />
                )}
              </IconButton>
            </Tooltip>
           
          </Box>
        );
      },
    },
  ];
  const mandatoryFields =
    ["chartOfAccount", "companyCode", "accountType", "accountGroup", "glAccountNumber", "longDescription", "businessSegment"];

  const isEqual = (a, b) => JSON.stringify(a) === JSON.stringify(b);


  const handleSnackBarClose = () => {
    setOpenSnackBar(false);
  };

  const checkGLAccountDuplicateCheckGeneralLedger = (row) => {
    setIsLoading(true);
    var duplicateCheckDescription = {
      glAccount: row?.glAccountNumber,
      coa: row?.longDescription,
    };
    const hSuccess = (data) => {

      setIsLoading(false);
      if (data?.body?.length > 0) {
        setAlertType('error');
        setAlertMsg('Duplicate GL Account Number  found.');
        setValidatedRows(prev => ({ ...prev, [row.id]: false }));
      } else {
        checkDuplicateNameCheckGeneralLedger(row)
      }
      setOpenSnackBar(true);
    };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType('error');
      setAlertMsg('An error occurred during validation.');
      setOpenSnackBar(true);
      setValidatedRows(prev => ({ ...prev, [row.id]: false }));
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/alter/fetchGlAccountNCoaDupliChk`,
      "post",
      hSuccess,
      hError,
      duplicateCheckDescription
    )
  };

  const checkDuplicateNameCheckGeneralLedger = (row) => {
    setIsLoading(true);
    var duplicateCheckDescription = {
      coa: row?.chartOfAccount,
      glName: row?.shortDescription,
    };
    const hSuccess = (data) => {

      setIsLoading(false);
      if (data?.body?.length > 0) {
        setAlertType('error');
        setAlertMsg('Duplicate GL Name found.');
        setValidatedRows(prev => ({ ...prev, [row.id]: false }));
      } else {
        checkDuplicateDescCheckGeneralLedger(row)

      }
      setOpenSnackBar(true);
    };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType('error');
      setAlertMsg('An error occurred during validation.');
      setOpenSnackBar(true);
      setValidatedRows(prev => ({ ...prev, [row.id]: false }));
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/alter/fetchGlNameNCoaDupliChk`,
      "post",
      hSuccess,
      hError,
      duplicateCheckDescription
    )
  };


  const checkDuplicateDescCheckGeneralLedger = (row) => {
    setIsLoading(true);
    var duplicateCheckDescription = {
      coa: row?.chartOfAccount,
      glDesc: row?.chartOfAccount
    };
    const hSuccess = (data) => {

      setIsLoading(false);
      if (data?.body?.length > 0) {
        setAlertType('error');
        setAlertMsg('Duplicate Gl Long Text found.');
        setValidatedRows(prev => ({ ...prev, [row.id]: false }));
      } else {
        setAlertType('success');
        setAlertMsg('Validation Successful');
        setValidatedRows(prev => ({ ...prev, [row.id]: true }));
        setIsAttachmentTabEnabled(true);
      }
      setOpenSnackBar(true);
    };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType('error');
      setAlertMsg('An error occurred during validation.');
      setOpenSnackBar(true);
      setValidatedRows(prev => ({ ...prev, [row.id]: false }));
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/alter/fetchGlDescNCoaDupliChk`,
      "post",
      hSuccess,
      hError,
      duplicateCheckDescription
    )
  };

  const handleValidate = (row, tab, config) => {
    const missing = [];
     const lineNumber =
      row?.lineNumber || glRows.findIndex((r) => r.id === row.id) + 1;

    const allFields = config.flatMap(section => {
      return Object.values(section.data).flat(); 
    });

    allFields?.forEach((field) => {
      if (field.visibility === "Mandatory") {
        const value = tab[field.jsonName];
        if (
          value === null ||
          value === undefined ||
          (typeof value === "string" && value.trim() === "")
        ) {
          missing.push(field.fieldName);
        }
      }
    });
    const headerMap = {
      companyCode: "Company Code",
      glAccountNumber: "General Ledger Number",
      businessSegment: "Business Segment",
      controllingArea: "Controlling Area",
      longDescription: "Long Text",
      shortDescription: "Short Text",
      longDescription: "Long Text"
    };
    mandatoryFields.forEach((field) => {
      const value = row[field];
      const displayName = headerMap[field] || field;
      if (
        value === null ||
        value === undefined ||
        (typeof value === "string" && value.trim() === "")
      ) {
        missing.push(`Line ${lineNumber} - ${displayName}`);
      } else if (
        field === "glAccountNumber" &&
        (value.length !== 10 || !/^[a-zA-Z0-9]+$/.test(value))
      ) {
        missing.push(`Line ${lineNumber} - ${displayName}`);
      }
    });

    const status = missing.length > 0 ? "error" : "success";

    dispatch(setValidatedStatus({ rowId: row.id, status }));

    if (status === "error") {
      const uniqueMissing = [...new Set(missing)];
      setMissingFields(uniqueMissing);
      setMissingFieldsDialogOpen(true);
    } else {
      setOriginalRowData((prev) => ({
        ...prev,
        [row.id]: JSON.parse(JSON.stringify(row)),
      }));
      setOriginalTabData((prev) => {
        const { id, ...restTab } = tab;
        return {
          ...prev,
          [row.id]: JSON.parse(JSON.stringify(restTab)),
        };
      });
      setAlertType("success");
      setValidatedRows(prev => ({ ...prev, [row.id]: true }));
      setAlertMsg("Validated Successfully");
      setOpenSnackBar(true);
      setCompleted([true, false]);
      setIsAttachmentTabEnabled(true);
    }
  };

  const validateSingleRow = (row, tab, config) => {
    const missing = [];
    const lineNumber = row?.lineNumber || glRows.findIndex((r) => r.id === row.id) + 1;

	
	 const allFields = config.flatMap(section => {
      return Object.values(section.data).flat(); 
    });

    allFields?.forEach((field) => {
      if (field.visibility === "Mandatory") {
        const value = tab[field.jsonName];
        if (
          value === null ||
          value === undefined ||
          (typeof value === "string" && value.trim() === "")
        ) {
          missing.push(`Line ${lineNumber} - ${field.fieldName}`);
        }
      }
    });

    const headerMap = {
      companyCode: "Company Code",
      glAccountNumber: "General Ledger Number",
      businessSegment: "Business Segment",
      controllingArea: "Controlling Area",
      longDescription: "Long Text",
      shortDescription: "Short Text",
      longDescription: "Long Text"
    };

    mandatoryFields.forEach((field) => {
      const value = row[field];
      const displayName = headerMap[field] || field;

      if (
        value === null ||
        value === undefined ||
        (typeof value === "string" && value.trim() === "")
      ) {
        missing.push(`Line ${lineNumber} - ${displayName}`);
      } else if (
        field === "glAccountNumber" &&
        (value.length !== 10 || !/^[a-zA-Z0-9]+$/.test(value))
      ) {
        missing.push(`Line ${lineNumber} - ${displayName}`);
      }
    });

    return {
      missing,
      status: missing.length > 0 ? "error" : "success",
    };
  };

  const handleMassValidate = (row) => {
    const missing = [];
    mandatoryFields.forEach((field) => {
      if (!row[field]) {
        missing.push(field);
      }
    });

    if (missing.length > 0) {
      setMissingFields(missing);
      setMissingFieldsDialogOpen(true);
      setValidatedRows(prev => ({ ...prev, [row.id]: false }));
      return false;
    } else {
      return new Promise((resolve) => {
        const hSuccess = (data) => {
          if (data?.body?.length > 0) {
            setValidatedRows(prev => ({ ...prev, [row.id]: false }));
            resolve(false);
          } else {
            setValidatedRows(prev => ({ ...prev, [row.id]: true }));
            resolve(true);
          }
        };

        const hError = () => {
          setValidatedRows(prev => ({ ...prev, [row.id]: false }));
          resolve(false);
        };

        doAjax(
          `/${destination_ProfitCenter}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${row?.controllingArea?.code}$$${row?.profitCenterNumber}`,
          "get",
          hSuccess,
          hError
        );
      });
    }
  };

  const validateAllRowsafterAllcheck = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(
      reduxPayload,
      requestHeaderSlice,
      isrequestId,
      task,
      dynamicData,
      createChangeLogData,
      selectedExtendDropdownData
    );

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);

      setAlertMsg("General Ledgers Validation initiated");
      setIsSaveAsDraftEnabled(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 3000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while validating the request");
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/validateMassGeneralLedger`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );

  };

 
  const validateAllRows = () => {

    let allMissing = [];
    let duplicates=''

    glRows.forEach((row) => {
      const tab = rowsBodyData[row.id]; 
      const { missing, status } = validateSingleRow(row, tab, generalLedgerTabs);
       duplicates = checkDuplicateValidationGL(glRows, rowsBodyData);
      setValidatedRows(prev => ({ ...prev, [row.id]: status }));

      if (status === "error") {
        allMissing.push(...missing);
      } else {
        setOriginalRowData((prev) => ({
          ...prev,
          [row.id]: JSON.parse(JSON.stringify(row)),
        }));
        setOriginalTabData((prev) => {
          const { id, ...restTab } = tab;
          return {
            ...prev,
            [row.id]: JSON.parse(JSON.stringify(restTab)),
          };
        });
        setValidatedRows(prev => ({ ...prev, [row.id]: status }));
        
      }
    });

    if (allMissing.length > 0) {
      const uniqueMissing = [...new Set(allMissing)];
      setMissingFields(uniqueMissing);
      setMissingFieldsDialogOpen(true);
    } else {
      
        if (duplicates.length > 0) {
          setDuplicateTextDetails(duplicates);
          setDuplicateTextDialogOpen(true);
          return;
        }
        validateAllRowsafterAllcheck()
      setAlertType("success");
      setAlertMsg("All Rows Validated Successfully");

      setOpenSnackBar(true);
      setCompleted([true, false]);
      setIsAttachmentTabEnabled(true);
    }

  };
  const handleCloseDialog = () => {
    setMissingFieldsDialogOpen(false);
  };


  const handleRowInputChange = (value, id, field) => {
    if (field === "chartOfAccount") {
      getCompanyCode(value?.code, id)
      getAccountGroup(value?.code, id)
      getSortKey(id)
      getLanguage(id)

    }
    if (field === "companyCode") {
      getAccountCurrency(value?.code, id)
      getTaxCategory(value?.code, id)
      getHouseBank(value?.code, id)
      getFiledStatusGroup(value?.code, id)
      getreconAccountType(id)
      getPlanningLevel(id)

      let filteredCompanyCodeForExtend = dropdownDataCompany?.filter(
        (item) => item.code !== value?.code)

      dispatch(setdropdownDataForExtendedCode({
        uniqueId: id,
        data: filteredCompanyCodeForExtend
      }));


      dispatch(
        updateModuleFieldDataGL({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "CompanyCode",
          data: value?.code,
          viewID: "Comp Codes"
        })
      );
    }
    if (field === "accountType") {
      getCostElementCategory(value?.code)
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "Accounttype",
          data: value?.code,
          viewID: "Type/Description"
        })
      );
      {
        requestId && !CHANGE_LOG_STATUSES.includes(requestStatus) && updateChangeLogGl({
          uniqueId: selectedRowId || selectedRow?.id,
          viewName: "Type/Description",
          plantData: '',
          fieldName: 'Account Type',
          jsonName: "Accounttype",
          currentValue: value?.code,
          requestId: initialPayload?.RequestId,
          childRequestId: requestId
        });
      }
    }
    if (field === "accountGroup") {
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "AccountGroup",
          data: value?.code,
          viewID: "Type/Description"
        })
      );
      {
        requestId && !CHANGE_LOG_STATUSES.includes(requestStatus) && updateChangeLogGl({
          uniqueId: selectedRowId || selectedRow?.id,
          viewName: "Type/Description",
          plantData: '',
          fieldName: 'Account Group',
          jsonName: "AccountGroup",
          currentValue: value?.code,
          requestId: initialPayload?.RequestId,
          childRequestId: requestId
        });
      }


    }
    if (field === "longDescription") {
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "Description",
          data: value,
          viewID: "Basic Data"
        })
      );
      {
        requestId && !CHANGE_LOG_STATUSES.includes(requestStatus) && updateChangeLogGl({
          uniqueId: selectedRowId || selectedRow?.id,
          viewName: "Basic Data",
          plantData: '',
          fieldName: 'Long Text',
          jsonName: "Description",
          currentValue: value,
          requestId: initialPayload?.RequestId,
          childRequestId: requestId
        });
      }
    }
    if (field === "shortDescription") {
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: selectedRowId || selectedRow?.id,
          keyName: "GLname",
          data: value,
          viewID: "Basic Data"
        })
      );
      {
        requestId && !CHANGE_LOG_STATUSES.includes(requestStatus) && updateChangeLogGl({
          uniqueId: selectedRowId || selectedRow?.id,
          viewName: "Basic Data",
          plantData: '',
          fieldName: 'Short Text',
          jsonName: "GLname",
          currentValue: value,
          requestId: initialPayload?.RequestId,
          childRequestId: requestId
        });
      }
    }
    const updatedRows =
      glRows.map((row) => (row.id === id ? { ...row, [field]: value } : row));
    dispatch(setGLRows(updatedRows))

  };

  const handleRowClick = (params) => {
    const clickedRow = params.row;
    setSelectedRow(clickedRow);
    dispatch(setSelectedRowIdGL(clickedRow?.id));
  };

  const handleAddRow = () => {
    const id = uuidv4();
    const newRow = {
      id,
      chartOfAccount: '',
      lineNumber: glRows.length > 0 ? Math.max(...glRows.map(d => d.lineNumber)) + 10 : lineNumberCounter + 10,
      companyCode: '',
      accountType: '',
      accountGroup: '',
      glAccountNumber: '',
      businessSegment: '',
      included: true,
      isNew: true
    };
    setLineNumberCounter(glRows.length > 0 ? Math.max(...glRows.map(d => d.lineNumber)) + 10 : lineNumberCounter + 10)
    
    dispatch(setGLRows([...glRows, newRow]))
    setSelectedRow(newRow);
    dispatch(setSelectedRowIdGL(newRow?.id));
    setValidatedRows(prev => ({ ...prev, [newRow.id]: false }));
  };

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const fieldsSyncedFromRow = {
    Description: "longDescription",
    GLname: "shortDescription",
    AccountType: "accountType",
    AccountGroup: "accountGroup",
  };


  const cleanTabForComparison = (tab, row) => {
    const cleanedTab = { ...tab };
    delete cleanedTab.id;
    for (const [tabKey, rowPath] of Object.entries(fieldsSyncedFromRow)) {
      const rowValue = rowPath
        .split(".")
        .reduce((acc, key) => (acc ? acc[key] : undefined), row);
      if (cleanedTab[tabKey] === rowValue) {
        delete cleanedTab[tabKey];
      }
    }
    return cleanedTab;
  };


  const isRowDirty = (rowId) => {
    const originalRow = originalRowData[rowId];
    const originalTab = originalTabData[rowId];
    const currentRow = glRows.find((r) => r.id === rowId);
    const currentTab = rowsBodyData[rowId];
    if (!originalRow || !originalTab || !currentRow || !currentTab) return true;

    const cleanedCurrentTab = cleanTabForComparison(currentTab, currentRow);
    const cleanedOriginalTab = cleanTabForComparison(originalTab, originalRow);

    return (
      !isEqual(originalRow, currentRow) ||
      !isEqual(cleanedOriginalTab, cleanedCurrentTab)
    );
  };

  const handleSelectAll = () => setSelectedViews(dropdownDataCompany || [fixedOption]);

  const AddCopiedGL = () => {
     dispatch(setOpenDialog(false));
    if (withReference === "yes") {

      if (selectedMatLines?.length) {

        let matlines = selectedMatLines[0]
        const id = uuidv4();
        setLineNumberCounter(glRows.length > 0 ? Math.max(...glRows.map(d => d.lineNumber)) + 10 : lineNumberCounter + 10)
        const newRow = {
          id,
          chartOfAccount: matlines?.chartOfAccount,
          companyCode: matlines?.companyCode,
          lineNumber: glRows.length > 0 ? Math.max(...glRows.map(d => d.lineNumber)) + 10 : lineNumberCounter + 10,
          accountType: matlines?.accountType,
          accountGroup: matlines?.accountGroup,
          glAccountNumber: '0000',
          businessSegment: '',
          included: true,
          isNew: true
        };
        
        setSelectedRow(newRow);
        dispatch(setSelectedRowIdGL(newRow?.id));
        if (newRow?.chartOfAccount) {
          getCompanyCode(newRow?.chartOfAccount, id)
          getAccountGroup(newRow?.chartOfAccount, newRow?.id)
          getSortKey(newRow?.id)
          getLanguage(newRow?.id)

        }
        if (newRow?.companyCode) {
          getAccountCurrency(newRow?.companyCode, id)
          getTaxCategory(newRow?.companyCode, id)
          getHouseBank(newRow?.companyCode, id)
          getFiledStatusGroup(newRow?.companyCode, id)
          getreconAccountType(id)
          getPlanningLevel(id)
          let filteredCompanyCodeForExtend = dropdownDataCompany?.filter(
            (item) => item.code !== newRow?.companyCode)

          dispatch(setdropdownDataForExtendedCode({
            uniqueId: id,
            data: filteredCompanyCodeForExtend
          }));


          dispatch(
            updateModuleFieldDataGL({
              uniqueId: id,
              keyName: "CompanyCode",
              data: newRow?.companyCode,
              viewID: "Comp Codes"
            })
          );
        }
        if (newRow?.accountType) {
          getCostElementCategory(newRow?.accountType, id)
          dispatch(
            updateModuleFieldDataGL({
              uniqueId: id,
              keyName: "Accounttype",
              data: newRow?.accountType,
              viewID: "Type/Description"
            })
          );
          {
            requestId && !CHANGE_LOG_STATUSES.includes(requestStatus) && updateChangeLogGl({
              uniqueId: id,
              viewName: "Type/Description",
              plantData: '',
              fieldName: 'Account Type',
              jsonName: "Accounttype",
              currentValue: newRow?.accountType,
              requestId: initialPayload?.RequestId,
              childRequestId: requestId
            });
          }
        }
        if (newRow?.accountGroup) {
          dispatch(
            updateModuleFieldDataGL({
              uniqueId: id,
              keyName: "AccountGroup",
              data: newRow?.accountGroup,
              viewID: "Type/Description"
            })
          );
          {
            requestId && !CHANGE_LOG_STATUSES.includes(requestStatus) && updateChangeLogGl({
              uniqueId: id,
              viewName: "Type/Description",
              plantData: '',
              fieldName: 'Account Group',
              jsonName: "AccountGroup",
              currentValue: newRow?.accountGroup,
              requestId: initialPayload?.RequestId,
              childRequestId: requestId
            });
          }
        }


        const rawData = reduxPayload?.rowsBodyData[matlines?.id];

        const updatedDatarawData = rawData && typeof rawData === 'object'
          ? {
            ...Object.fromEntries(
              Object.entries(rawData).map(([key, value]) => [
                key,
                (key === "GLname" || key === "Description") ? "" : value
              ])
            ),
            CompanyCode: newRow?.companyCode 
          }
          : {};
        const requestHeaderData = reduxPayload?.requestHeaderData;
        let rowsbodydatares = reduxPayload?.rowsBodyData || {};

        let rowsBodyData = {
          ...rowsbodydatares,
          [newRow?.id]: {
            ...updatedDatarawData,
          },
        };
        const rowsHeaderDataapi = reduxPayload?.rowsHeaderData;

        const rowsHeaderData = [...rowsHeaderDataapi, newRow]


        const payload = {
          requestHeaderData,
          rowsBodyData,
          rowsHeaderData
        };


        dispatch(setGLPayload(payload));

      } else if (glRows?.length >= 0) {
        const id = uuidv4();
        setLineNumberCounter(glRows.length > 0 ? Math.max(...glRows.map(d => d.lineNumber)) + 10 : lineNumberCounter + 10)
        const newRow = {
          id,
          chartOfAccount: withRefValues?.["Chart Of Account"]?.["code"],
          companyCode: withRefValues?.["Company Code"]?.["code"],
          lineNumber: glRows.length > 0 ? Math.max(...glRows.map(d => d.lineNumber)) + 10 : lineNumberCounter + 10,
          accountType: withRefValues?.["Account Type"]?.["code"],
          accountGroup: withRefValues?.["Account Group"]?.["code"],
          glAccountNumber: withRefValues?.["GL Account"]?.["code"],
          businessSegment: '',
          included: true,
          isNew: true
        };

        
        setSelectedRow(newRow);
        dispatch(setSelectedRowIdGL(newRow?.id));
        if (newRow?.chartOfAccount) {
          getCompanyCode(newRow?.chartOfAccount, newRow?.id)
          getAccountGroup(newRow?.chartOfAccount, newRow?.id)
          getSortKey(newRow?.id)
          getLanguage(newRow?.id)

        }
        if (newRow?.companyCode) {
          getAccountCurrency(newRow?.companyCode, newRow?.id)
          getTaxCategory(newRow?.companyCode, newRow?.id)
          getHouseBank(newRow?.companyCode, newRow?.id)
          getFiledStatusGroup(newRow?.companyCode, newRow?.id)
          getreconAccountType(newRow?.id)
          getPlanningLevel(newRow?.id)
          let filteredCompanyCodeForExtend = allDropDownData?.CompanyCode?.filter(
            (item) => item.code !== newRow?.companyCode)
          

          dispatch(setdropdownDataForExtendedCode({
            uniqueId: id,
            data: filteredCompanyCodeForExtend
          }));


          dispatch(
            updateModuleFieldDataGL({
              uniqueId: id,
              keyName: "CompanyCode",
              data: newRow?.companyCode,
              viewID: "Comp Codes"
            })
          );
        }
        if (newRow?.accountType) {
          
          getCostElementCategory(newRow?.accountType, newRow?.id)
          dispatch(
            updateModuleFieldDataGL({
              uniqueId: id,
              keyName: "Accounttype",
              data: newRow?.accountType,
              viewID: "Type/Description"
            })
          );
          {
            requestId && !CHANGE_LOG_STATUSES.includes(requestStatus) && updateChangeLogGl({
              uniqueId: id,
              viewName: "Type/Description",
              plantData: '',
              fieldName: 'Account Type',
              jsonName: "Accounttype",
              currentValue: newRow?.accountType,
              requestId: initialPayload?.RequestId,
              childRequestId: requestId
            });
          }
        }
        if (newRow?.accountGroup) {
          dispatch(
            updateModuleFieldDataGL({
              uniqueId: id,
              keyName: "AccountGroup",
              data: newRow?.accountGroup,
              viewID: "Type/Description"
            })
          );
          {
            requestId && !CHANGE_LOG_STATUSES.includes(requestStatus) && updateChangeLogGl({
              uniqueId: id,
              viewName: "Type/Description",
              plantData: '',
              fieldName: 'Account Group',
              jsonName: "AccountGroup",
              currentValue: newRow?.accountGroup,
              requestId: initialPayload?.RequestId,
              childRequestId: requestId
            });
          }
        }
        dispatch(setGLRows([...glRows, newRow]))
        setValidatedRows(prev => ({ ...prev, [newRow.id]: false }));
        dispatch(
            updateModuleFieldDataGL({
              uniqueId: id,
              keyName: "AccountGroup",
              data: newRow?.accountGroup,
              viewID: "Type/Description"
            })
          )
        fetchGeneralLedgerFieldConfig();
        getCopiedGeneralLedgerData(newRow,id);
       
      }
    } else {
      handleAddRow();
    }
  };

  const getCopiedGeneralLedgerData = (newRow,id) => {

    const payload = {
      "glAccCOACoCode": [
        {
          "glAccount": newRow?.glAccountNumber,
          "chartOfAccount": newRow?.chartOfAccount,
          "companyCode": newRow?.companyCode
        },
      ]
    };
    const successHandler = (data) => {
      
      const rawData = data?.body || [];
       const response = rawData[0];

      if (!response) {
        setBlurLoading(false);
        dispatch(setOpenDialog(false))
        return;
      }
      const flatData = rawData.reduce((acc, obj) => ({
        ...acc,
        ...obj.typeNDescriptionViewDto,
        ...obj.controlDataViewDto,
        ...obj.createBankInterestViewDto,
        ...obj.keywordNTranslationViewDto,
        ...obj.informationViewDto
      }), {});

      const updatedDatarawData = flatData && typeof flatData === 'object'
        ? {
            ...Object.fromEntries(
              Object.entries(flatData).map(([key, value]) => [
                key,
                (key === "GLname" || key === "Description") ? "" : value
              ])
            ),
            CompanyCode: newRow?.companyCode
          }
        : {};

      const requestHeaderData = reduxPayload?.requestHeaderData;


      let rowsbodydatares = reduxPayload?.rowsBodyData || {};

      let rowsBodyData = {
        ...rowsbodydatares,
        [newRow?.id]: {
          ...updatedDatarawData,
        },
      };
      const rowsHeaderDataapi = reduxPayload?.rowsHeaderData;

      const rowsHeaderData = [...rowsHeaderDataapi, newRow]


      const payload = {
        requestHeaderData,
        rowsBodyData,
        rowsHeaderData
      };
        handleDialogClose();
       
      dispatch(setGLPayload(payload));

    };

    const errorHandler = (err) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGeneralLedgersData`,
      "post",
      successHandler,
      errorHandler,
      payload
    );

  }

  const getValidationStatus = (rowId) => {
    const status = validatedStatus[rowId];
    const dirty = isRowDirty(rowId);
    if (!status) return "default";
    return isRowDirty(rowId) ? "error" : status;
  };

  useEffect(() => {
    setSelectedRow(glRows[0])
  }, []);

  const getCompanyCode = (coa) => {
    const hSuccess = (data) => {
      setDropdownDataCompany(data.body)
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getCompanyCode?chartAccount=${coa}`,
      "get",
      hSuccess,
      hError
    );
  };


  const getCompanyCoderef = (coa) => {
    const hSuccess = (data) => {
      setDropDownData((prev) => ({ ...prev, ["Company Code"]: data.body }))
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getCompanyCode?chartAccount=${coa}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccountType = () => {
    const hSuccess = (data) => {
      setDropdownDataAccountType(data.body)
      setDropDownData((prev) => ({ ...prev, ["Account Type"]: data.body }))
      dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "Accounttype",
          data: data.body || [],
          keyName2: selectedRowId ?? selectedRow?.id,
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGLAccountType`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccountCurrency = (compCode, id = '') => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "AccountCurrency",
          data: data.body || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountCurrency?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getFiledStatusGroup = (compCode, id) => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "FieldStsGrp",
          data: data.body || [],
          keyName2: selectedRowId || selectedRow?.id, id,
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getFieldStatusGroup?fieldStatusVariant=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getTaxCategory = (compCode, id = '') => {
    const hSuccess = (data) => {

      dispatch(
        setDependentDropdown({
          keyName: "Taxcategory",
          data: data.body || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getTaxCategory?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getHouseBank = (compCode, id = '') => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "HouseBank",
          data: data.body || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getHouseBank?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccontId = (compCode,housebank,id ='') => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "AccountId",
          data: data.body || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountId?companyCode=${compCode}&houseBank=${housebank}`,
      "get",
      hSuccess,
      hError
    );
  };


  const getCostElementCategory = (accType, id = '') => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "CostEleCategory",
          data: data.body || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getCostElementCategory?accountType=${accType}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getreconAccountType = ( id = '') => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "ReconAcc",
          data: data.body || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getReconAccountForAccountType`,
      "get",
      hSuccess,
      hError
    );
  };

  const getPlanningLevel = ( id = '') => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "Planninglevel",
          data: data.body || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getPlanningLevel`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccountGroup = (coa, id = '') => {
    const hSuccess = (data) => {

      let accGrparr = []
      data?.body?.map((item) => {
        let hash = {}
        hash["code"] = item?.AccountGroup
        hash["desc"] = item?.Description
        hash["FromAcct"] = item?.FromAcct
        hash["ToAcct"] = item?.ToAcct
        accGrparr?.push(hash)
      })
      setDropdownDataAccountType(accGrparr)
      dispatch(
        setDependentDropdown({
          keyName: "AccountGroup",
          data: accGrparr || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )

      dispatch(setDropDown({ keyName: "accountGroup", data: accGrparr }));
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountGroup?chartAccount=${coa}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccountGroupref = (coa) => {
    const hSuccess = (data) => {

      let accGrparr = []
      data?.body?.map((item) => {
        let hash = {}
        hash["code"] = item?.AccountGroup
        hash["desc"] = item?.Description
        hash["FromAcct"] = item?.FromAcct
        hash["ToAcct"] = item?.ToAcct
        accGrparr?.push(hash)
      })

      setDropDownData((prev) => ({ ...prev, ["Account Group"]: accGrparr }))

    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountGroup?chartAccount=${coa}`,
      "get",
      hSuccess,
      hError
    );
  };


  const getFormPlanningFrp = () => {
    const hSuccess = (data) => {
      setDropdownDataFormPlanning(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Template", data: data.body },
      });
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_ProfitCenter}/data/getFormPlanningTemp`,
      "get",
      hSuccess,
      hError
    );
  };

  const getChartOfAccount = () => {
    const hSuccess = (data) => {
      setDropDownData((prev) => ({ ...prev, ["Chart Of Account"]: data.body }))
      dispatch(setDropDown({ keyName: "COA", data: data.body }));
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getChartOfAccounts`,
      "get",
      hSuccess,
      hError
    );
  };

  const getBusSeg = () => {
    const hSuccess = (data) => {
      setDropdownDataCOA(data.body);
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_ProfitCenter}/data/getBusinessSegment`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    getChartOfAccount()
    getAccountType()
    getBusSeg()
  }, []);


  const [rowRegionData, setRowRegionData] = useState({});


  const getSegment = () => {
    const hSuccess = (data) => {
      setDropdownDataSegment(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Segment", data: data.body },
      });
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_ProfitCenter}/data/getSegment`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getSegment();
  }, []);

  const getLanguage = (id = '') => {
    const hSuccess = (data) => {
      setDropdownDataLanguage(data.body);
      dispatch(
        setDependentDropdown({
          keyName: "Language",
          data: data.body || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };

  const getSortKey = (id = '') => {
    const hSuccess = (data) => {
      setDropdownDataLanguage(data.body);
      dispatch(
        setDependentDropdown({
          keyName: "Sortkey",
          data: data.body || [],
          keyName2: id?.trim() ? id : (selectedRowId && selectedRow?.id),
        })
      )
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getSortKey`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleSaveAsDraft = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData, createChangeLogData, selectedExtendDropdownData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("General Ledger Submission saved as draft.");
      setOpenSnackBar(true);
      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/generalLedgersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );

  };

  const handleSubmitForReview = () => {
    setLoaderMessage("");
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData, createChangeLogData, selectedExtendDropdownData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(
        "General Ledger submission for save as draft initiated"
      );
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/generalLedgersSubmitForReview`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );

  };

  const handleValidateAndSyndicate = (type) => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData, createChangeLogData, selectedExtendDropdownData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      if (type === "VALIDATE")
        setAlertMsg("General Ledger Validation initiated");
      else if (type === "SYNDICATE")
        setAlertMsg("General Ledger Syndication initiated");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while validating the request");
    };

    doAjax(
      type === "VALIDATE" ? `/${destination_GeneralLedger_Mass}/massAction/validateMassGeneralLedger` : `/${destination_GeneralLedger_Mass}/massAction/createGeneralLedgersApproved`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );

  };

  const handleSubmitForApprove = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData, createChangeLogData, selectedExtendDropdownData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(
        "General Ledger submission for Approve initiated"
      );
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/generalLedgersApprovalSubmit`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );

  };


  const viewsClose = (event, reason) => {
    setOpenOrgData(false);
  };

  const handleSendBack = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData, createChangeLogData, selectedExtendDropdownData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Profit Centers submission for Approve initiated");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while saving the draft");
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersSendForCorrection`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const handleCorrection = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData, createChangeLogData, selectedExtendDropdownData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(data?.message ?? "General Ledgers Sent for Correction !");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while sending for correction");
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersSendForReview`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  const getGlAcountDropdownDataFromSearchFilter = () => {

    let payload = {
      glAccount: "",
      chartOfAccount: withRefValues?.["Chart Of Account"]?.["code"],
      postAutoOnly: "",
      companyCode: withRefValues?.["Company Code"]?.["code"] ?? '',
      taxCategory: "",
      glAcctLongText: "",
      postingWithoutTaxAllowed: "",
      blockedForPostingInCOA: "",
      shortText: "",
      blockedForPostingInCompany: "",
      accountGroup: withRefValues?.["Account Group"]?.["code"] ?? '',
      glAccountType: withRefValues?.["Account Type"]?.["code"] ?? '',
      fieldStatusGroup: "",
      openItemMgmtbyLedgerGroup: "",
      openItemManagement: "",
      reconAccountforAcctType: "",
      fromDate:
        moment(rmSearchForm?.createdOn[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate:
        moment(rmSearchForm?.createdOn[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      createdBy: "",
      top: 100,
      skip: 0,
    };
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        let glAccountArr = [];

        data?.body?.list?.forEach((item) => {
            let glAccountHash = {};
            glAccountHash["code"] = item?.GLAccount;
            glAccountHash["desc"] = item?.GLname;
            glAccountArr.push(glAccountHash);
        });

        setDropDownData((prev) => ({ ...prev, ["GL Account"]: glAccountArr }))

      }
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGeneralLedgersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );


  }

  const handleSalesOrgWithREF = (key, newValue) => {
     const updated = {
    ...withRefValues,
    [key]: newValue,
  };

  const cascaded = cascadeNullify(updated, key);

  setWithRefValues(cascaded);
    if (key === "Chart Of Account" && newValue) {

      getCompanyCoderef(newValue?.code);
      getAccountGroupref(newValue?.code)
    }
    if (key === "Account Group") {
      getGlAcountDropdownDataFromSearchFilter()
    }  
  }



  
  const handleRejectAndCancel = () => {
    setBlurLoading(true);
    const finalPayload = createPayloadForGL(reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData)

    const hSuccess = (data) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg(data?.message ?? "General Ledgers Rejected !");
      setTimeout(() => {
        navigate("/requestbench");
      }, 1000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setSnackbarOpen(true);
      setAlertMsg("Error occurred while rejecting the request");
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/profitCentersRejected`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  return (
    <div>
      <ReusableSnackBar
        openSnackBar={openSnackBar}
        alertMsg={alertMsg}
        handleSnackBarClose={handleSnackBarClose}
        alertType={alertType}
        isLoading={isLoading}
      />

      {error && <Typography color="error">{t("Error loading data")}</Typography>}

      <Box
        sx={{
          position: isGridZoomed ? "fixed" : "relative",
          top: isGridZoomed ? 0 : "auto",
          left: isGridZoomed ? 0 : "auto",
          right: isGridZoomed ? 0 : "auto",
          bottom: isGridZoomed ? 0 : "auto",
          width: isGridZoomed ? "100vw" : "100%",
          height: isGridZoomed ? "100vh" : "auto",
          zIndex: isGridZoomed ? 1004 : 1,
          backgroundColor: isGridZoomed ? "white" : "transparent",
          padding: isGridZoomed ? "20px" : "0",
          display: "flex",
          flexDirection: "column",
          boxShadow: isGridZoomed
            ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
            : "none",
          transition: "all 0.3s ease",
          borderRadius: "8px",
          border: "1px solid #e0e0e0",
        }}
      >

        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "8px 16px",
            backgroundColor: "#f5f5f5",
            borderRadius: "8px 8px 0 0",
          }}
        >
          <Typography gutterBottom sx={{ fontWeight: "bold", fontSize: "16px", mb: -2 }}>
            {t("General Ledger Data")}
          </Typography>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<Add />}
              onClick={() => {

                dispatch(setOpenDialog(true));
                setSelectedMatLines([]);
                setSelectedMaterials(null);
                setWithRefValues({});
                setSelectedRow(null)
                dispatch(setSelectedRowIdGL([]));
                
              }}
              disabled={!isAddRowEnabled}
              sx={{ mb: 1 }}
            >
              {t("Add")}
            </Button>
            <Tooltip
              title={isGridZoomed ? "Exit Zoom" : "Zoom In"}
              sx={{ zIndex: "1009" }}
            >
              <IconButton
                onClick={toggleGridZoom}
                color="primary"
                sx={{
                  backgroundColor: "rgba(0, 0, 0, 0.05)",
                  "&:hover": {
                    backgroundColor: "rgba(0, 0, 0, 0.1)",
                  },
                }}
              >
                {isGridZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
            <div style={{ height: "100%" }}>
              <div>
                <ReusableDataTable
                  isLoading={loading}
                  rows={glRows}
                  columns={columns}
                  pageSize={10}
                  tempheight={'50vh'}
                  getRowIdValue={"id"}
                  status_onRowSingleClick={true}
                  callback_onRowSingleClick={handleRowClick}
                  getRowClassName={(params) =>
                    selectedRow?.id === params.row.id ? "Mui-selected" : ""
                  }
                />
              </div>
           </div>
        </div>
      </Box>

      {openDialog && (
        <Dialog
           fullWidth
          open={openDialog}
          maxWidth="lg"
          
          sx={{
            "&::webkit-scrollbar": {
              width: "1px",
            },
          }}
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF",
              display: "flex",
            }}
          >
            <Typography variant="h6">{t("Add New General Ledger")}</Typography>
          </DialogTitle>
          <DialogContent sx={{ padding: ".5rem 1rem", alignItems: "center", justifyContent: "center", margin: "0px 25px" }}>
            <FormControl component="fieldset" sx={{ paddingBottom: "2%" }}>
              <FormLabel component="legend" sx={{ padding: "15px 0px", fontWeight: "600", fontSize: "15px" }}>
                {t("How would you like to proceed?")}
              </FormLabel>
              <RadioGroup row aria-label="profit-center-number" name="profit-center-number" value={withReference} onChange={(event) => setWithReference(event.target.value)}>
                <FormControlLabel value="yes" control={<Radio />} label={t("With Reference")} />
                <FormControlLabel value="no" control={<Radio />} label={t("Without Reference")} />
              </RadioGroup>
            </FormControl>
            {withReference === 'yes' && (
            <Grid container spacing={2}>

              {/* First row: 4 dropdowns */}
              <Grid item xs={12}>
                <Grid container spacing={2}>

                  {withRefParams?.slice(0, 4).map((key) => (
                    <Grid item xs={3} key={key}>
                      <Typography variant="subtitle2" gutterBottom>
                        {key}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <SingleSelectDropdown
                        options={dropDownData?.[key] || []}
                        value={withRefValues[key] || ""}
                        onChange={(newValue) => {
                          handleSalesOrgWithREF(key, newValue);
                        }}
                        placeholder={t(`Select ${key}`)}
                        minWidth={180}
                        listWidth={306}
                        sx={{
                          minWidth: 270,
                          "& .MuiAutocomplete-popper": { minWidth: 306 },
                        }}
                        disabled={selectedMatLines?.length || withReference === "no"}
                        isLoading={isDropdownLoading[key]}
                      />
                    </Grid>
                  ))}
                </Grid>
              </Grid>

              {/* Second row: 4 dropdowns + "OR" section */}
              <Grid item xs={12}>
                <Grid container spacing={2} alignItems="center">
                  
                  {withRefParams?.slice(4).map((key) => (
                    <Grid item xs={3} key={key}>
                      <Typography variant="subtitle2" gutterBottom>
                        {key}
                        <span style={{ color: "red" }}>*</span>
                      </Typography>
                      <SingleSelectDropdown
                        options={dropDownData?.[key] || []}
                        value={withRefValues[key] || ""}
                        onChange={(newValue) => {
                          setWithRefValues((prev) => ({ ...prev, [key]: newValue }));
                        }}
                        placeholder={t(`Select ${key}`)}
                        minWidth={180}
                        listWidth={306}
                        sx={{
                          minWidth: 270,
                          "& .MuiAutocomplete-popper": { minWidth: 306 },
                        }}
                        disabled={selectedMatLines?.length || withReference === "no"}
                        isLoading={isDropdownLoading[key]}
                      />
                    </Grid>
                  ))}
                  {/* OR Dropdown (kept in the same row) */}
                  {glRows?.length > 0 && (
                    <>
                      <Grid
                        item
                        xs={1}
                        sx={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "flex-start", 
                          mt: 3.5, 
                        }}
                      >
                        <Typography variant="body1" sx={{ fontWeight: "bold", color: "gray" }}>
                          OR
                        </Typography>
                      </Grid>
                      <Grid item xs={3} >
                      <Typography variant="subtitle2" gutterBottom>
                        {"General Ledger Line Number"}
                        
                      </Typography>
                        <SingleSelectDropdown
                          options={glRows.map((opt) => ({ ...opt, code: opt.lineNumber, desc: "" }))}
                          value={selectedMatLines[0]}
                          onChange={(newValue) => {
                            setSelectedMatLines(newValue ? [newValue] : []);
                            setWithRefValues({});
                            setSelectedMaterials(null);
                          }}
                          minWidth={180}
                          listWidth={266}
                          placeholder={t("Select General Ledger Number")}
                          disabled={withReference === "no"}
                          getOptionLabel={(option) => (option?.desc ? `${option.code} - ${option.desc}` : option?.code || "")}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <strong>{option?.code}</strong>
                              {option?.desc ? ` - ${option?.desc}` : ""}
                            </li>
                          )}
                          sx={{
                            minWidth: 270,
                            "& .MuiAutocomplete-popper": { minWidth: 306 },
                          }}
                        />
                      </Grid>
                    </>
                  )}
                </Grid>
              </Grid>
            </Grid>)
            }
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{
                width: "max-content",
                textTransform: "capitalize",
              }}
              onClick={handleDialogClose}
              variant="outlined"
            >
              Cancel
            </Button>
            <Button
              className="button_primary--normal"
              type="save"
              onClick={AddCopiedGL}
              variant="contained"
              disabled={
                withReference === "yes" &&
                !(
                  selectedMatLines?.length > 0 ||
                  ["Chart Of Account", "Company Code", "Account Type", "Account Group", "GL Account"].every(
                    (key) =>
                      withRefValues?.[key]?.code &&
                      withRefValues[key].code.trim() !== ""
                  )
                )
              }
            >
              Proceed
            </Button>
            </DialogActions>
        </Dialog>
      )}


      {openOrgData && (
        <Dialog fullWidth maxWidth={false} open={true} onClose={viewsClose} sx={{ display: "flex", justifyContent: "center" }} disableEscapeKeyDown>
          <Box sx={{ width: "600px !important" }}>
            <DialogTitle sx={{ backgroundColor: "#EAE9FF", marginBottom: ".5rem" }}>
              <DescriptionIcon
                style={{
                  height: "20px",
                  width: "20px",
                  marginBottom: "-5px",
                }}
              />
              <span>Select Company Code to Extend</span>
            </DialogTitle>
            <DialogContent sx={{ paddingBottom: ".5rem" }}>
              <Box display="flex" alignItems="center" sx={{ flex: 1, padding: "22px 0px", gap: "5px" }}>

                <Autocomplete
                  size="small"
                  multiple
                  fullWidth
                  options={[selectAllOption, ...companycodeExtendedTo?.[selectedRow?.id]]}
                  value={selectedExtendDropdownData?.[selectedRow?.id]}
                  getOptionLabel={getOptionLabel}
                  disableCloseOnSelect
                  isOptionEqualToValue={(option, value) => option.code === value.code}
                  onChange={handleChangeExtendCompanycode}
                  renderOption={(props, option, { selected }) => (
                    <li {...props}>
                      <Checkbox checked={selected} sx={{ marginRight: 1 }} />
                      {getOptionLabel(option)}
                    </li>
                  )}
                  renderTags={(tagValue, getTagProps) =>
                    tagValue.map((option, index) => {
                      const { key, ...tagProps } = getTagProps({ index });
                      return (
                        <Chip key={key} label={`${option.code}`} {...tagProps} />
                      );
                    })
                  }
                  renderInput={(params) => <TextField {...params} />}
                />
              </Box>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => {
                  setOpenOrgData(false), handleCellEdit({ id: rowId, field: "views", value: selectedViews });
                }}
                variant="contained"
              >
                Ok
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

      )}

      <Dialog
            open={duplicateTextDialogOpen}
            onClose={() => setDuplicateTextDialogOpen(false)}
            maxWidth="sm"
            fullWidth
            >
            <DialogTitle
                id="missing-fields-dialog-title"
                sx={{
                  backgroundColor: "#fff3e0",
                  color: "#e65100",
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  fontWeight: "bold",
                }}
              >
                <WarningAmberIcon fontSize="medium" />
                {t("Duplicate Description")}
              </DialogTitle>
      
              <DialogContent dividers>
              {duplicateTextDetails.length === 0 ? (
              <Typography>No duplicates found.</Typography>
              ) : (
              <TableContainer component={Paper} elevation={0}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell><strong>Type</strong></TableCell>
                    <TableCell><strong>Remarks</strong></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {duplicateTextDetails.map((dup, index) => (
                    <TableRow key={index}>
                      <TableCell>{dup.type}</TableCell>
                      <TableCell>
                        <strong>{dup.value}</strong> found in Line(s): {dup.lines.join(", ")}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              </TableContainer>
              )}
              </DialogContent>
      
            <DialogActions>
            <Button onClick={() => setDuplicateTextDialogOpen(false)}>Close</Button>
            </DialogActions>
            </Dialog>


      <Dialog
        open={missingFieldsDialogOpen}
        onClose={handleCloseDialog}
        aria-labelledby="missing-fields-dialog-title"
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          id="missing-fields-dialog-title"
          sx={{
            backgroundColor: "#fff3e0",
            color: "#e65100",
            display: "flex",
            alignItems: "center",
            gap: 1,
            fontWeight: "bold"
          }}
        >
          <WarningAmberIcon fontSize="medium" />
          {t("Missing Mandatory Fields")}
        </DialogTitle>

        <DialogContent sx={{ pt: 2 }}>
          <Typography variant="body1" gutterBottom>
            {t("Please complete the following mandatory fields:")}
          </Typography>
          <List dense>
            {missingFields.map((field, index) => (
              <ListItem key={index} disablePadding>
                <ListItemIcon sx={{ minWidth: 30 }}>
                  <WarningAmberIcon fontSize="small" color="warning" />
                </ListItemIcon>
                <ListItemText primary={field} />
              </ListItem>
            ))}
          </List>
        </DialogContent>

        <DialogActions sx={{ pr: 3, pb: 2 }}>
          <Button
            onClick={handleCloseDialog}
            variant="contained"
            color="warning"
            sx={{ textTransform: "none", fontWeight: 500 }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {selectedRow &&
        ((reqBench === "true"  && selectedRowId ) ? (
          <Box
            sx={{
              position: isTabsZoomed ? "fixed" : "relative",
              top: isTabsZoomed ? 0 : "auto",
              left: isTabsZoomed ? 0 : "auto",
              right: isTabsZoomed ? 0 : "auto",
              bottom: isTabsZoomed ? 0 : "auto",
              width: isTabsZoomed ? "100vw" : "100%",
              height: isTabsZoomed ? "100vh" : "auto",
              zIndex: isTabsZoomed ? 1004 : 1,
              backgroundColor: isTabsZoomed ? "white" : "transparent",
              padding: isTabsZoomed ? "20px" : "0",
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              boxShadow: isTabsZoomed
                ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
                : "none",
              transition: "all 0.3s ease",
              borderRadius: "8px",
              border: "1px solid #e0e0e0",
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: "8px 16px",
                borderRadius: "8px 8px 0 0",
              }}
            >
              <Typography variant="h6">{t("View Details")}</Typography>
              <Tooltip
                title={isTabsZoomed ? "Exit Zoom" : "Zoom In"}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleTabsZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isTabsZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ mt: 3 }}>
              <Tabs
                value={selectedTab}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
                aria-label="Request tabs"
                variant="scrollable"
                scrollButtons="auto"
                sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
              >
                {generalLedgerTabs.map((tab, index) => (
                  <Tab key={index} label={tab.tab} />
                ))}
              </Tabs>
              <Paper elevation={2} sx={{ p: 3, borderRadius: 4 }}>
                {generalLedgerTabs[selectedTab] && (
                  <GenericTabsGlobal
                    disabled={false}
                    basicDataTabDetails={generalLedgerTabs[selectedTab].data}
                    dropDownData={{
                      "CompanyCode": allDropDownData?.["accountType"],
                      "Country": dropdownDataCountry,
                      "Accounttype": allDropDownData?.["accountType"],
                      "AccountGroup": allDropDownData?.["accountGroup"],
                      "Segment": dropdownDataSegment,
                      "Language":  allDropDownData?.["Language"],
                      "Template": dropdownDataFormPlanning,
                      "COArea": dropdownDataCOA,
                      "TaxJurisdiction": dropdownDataTaxJur,
                    }}
                    activeViewTab={generalLedgerTabs[selectedTab].tab}
                    uniqueId={selectedRow?.id || selectedRowId || glRows[0]?.id}
                    selectedRow={selectedRow || {}}
                    module={"GeneralLedger"}
                  />
                )}
              </Paper>
            </Box>
          </Box>
        ) : (
          <Box
            sx={{
              position: isTabsZoomed ? "fixed" : "relative",
              top: isTabsZoomed ? 0 : "auto",
              left: isTabsZoomed ? 0 : "auto",
              right: isTabsZoomed ? 0 : "auto",
              bottom: isTabsZoomed ? 0 : "auto",
              width: isTabsZoomed ? "100vw" : "100%",
              height: isTabsZoomed ? "100vh" : "auto",
              zIndex: isTabsZoomed ? 1004 : 1,
              backgroundColor: isTabsZoomed ? "white" : "transparent",
              padding: isTabsZoomed ? "20px" : "0",
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              boxShadow: isTabsZoomed
                ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
                : "none",
              transition: "all 0.3s ease",
              borderRadius: "8px",
              border: "1px solid #e0e0e0",
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: "8px 16px",
                borderRadius: "8px 8px 0 0",
              }}
            >
              <Typography variant="h6">{t("View Details")}</Typography>
              <Tooltip
                title={isTabsZoomed ? "Exit Zoom" : "Zoom In"}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleTabsZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isTabsZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}>
              <Tabs
                value={selectedTab}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
                aria-label="Request tabs"
                variant="scrollable"
                scrollButtons="auto"
                sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
              >
                {generalLedgerTabs.map((tab, index) => (
                  <Tab key={index} label={tab.tab} />
                ))}
              </Tabs>
              <Paper elevation={2} sx={{ p: 3, borderRadius: 4 }}>
                {generalLedgerTabs[selectedTab] && (
                  
                  <GenericTabsGlobal
                    disabled={false}
                    basicDataTabDetails={generalLedgerTabs[selectedTab].data}
                    dropDownData={{
                      "CompanyCode": allDropDownData?.["accountType"],
                      "Country": dropdownDataCountry,
                      "Accounttype": allDropDownData?.["accountType"],
                      "AccountGroup": allDropDownData?.["accountGroup"],
                      "Segment": dropdownDataSegment,
                      "Language": dropdownDataLanguage,
                      "Template": dropdownDataFormPlanning,
                      "COArea": dropdownDataCOA,
                      "TaxJurisdiction": dropdownDataTaxJur,
                    }}
                    activeViewTab={generalLedgerTabs[selectedTab].tab}
                    uniqueId={selectedRow?.id || selectedRowId || glRows[0]?.id}
                    selectedRow={selectedRow || {}}
                    module={"GeneralLedger"}
                  />
                )}
              </Paper>
            </Box>
          </Box>
        ))
      }

      <BottomNavGlobal
        handleSaveAsDraft={handleSaveAsDraft}
        handleSubmitForReview={handleSubmitForReview}
        handleSubmitForApprove={handleSubmitForApprove}
        handleSendBack={handleSendBack}
        handleCorrection={handleCorrection}
        handleRejectAndCancel={handleRejectAndCancel}
        handleValidateAndSyndicate={handleValidateAndSyndicate}
        validateAllRows={validateAllRows}
        isSaveAsDraftEnabled={isSaveAsDraftEnabled}
        filteredButtons={filteredButtons}
        validateEnabled={validateEnabled}
        moduleName={MODULE_MAP?.GL}
      />
      <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
    </div>
  );
};

export default RequestDetailsGL;
