import * as React from "react";
import Radio from "@mui/material/Radio";
import RadioGroup from "@mui/material/RadioGroup";
import Tab from "@mui/material/Tab";
import TabContext from "@mui/lab/TabContext";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import DataElementDetails from "./DataElementDialog";
import fetchWrapper from "../utility/fetchWrapper";
import Table from "@mui/material/Table";
import "./CwMSFieldsDetails.css";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import Button from "@mui/material/Button";
import Input from "@mui/material/Input";
import FormControl from "@mui/material/FormControl";
import Snackbar from "@mui/material/Snackbar";
import MenuItem from "@mui/material/MenuItem";
import InputLabel from "@mui/material/InputLabel";
import Select from "@mui/material/Select";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import FormGroup from "@mui/material/FormGroup";
import FormControlLabel from "@mui/material/FormControlLabel";
import Switch from "@mui/material/Switch";
import FormLabel from "@mui/material/FormLabel";
import IconButton from "@mui/material/IconButton";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import ValueHelp from "./ValueHelp";
import DataElementDialog from "./DataElementsDetails";
import MessageBoxComponentDiscard from "./CwWfDiscardBasicInfo";
// import MessageBoxComponentSubmit from "./CwWfSubmitConfirmation";
import MessageBoxComponentSave from "./CwWfSaveConfirmation";
import InfoSharp from "@mui/icons-material/InfoSharp";
import InputAdornment from "@mui/material/InputAdornment";
import ValueHelpDialog from "./valueHelpDialog";
import ValueHelpDynamicDialog from "./valueHelpDynamicDialog";
import Box from "@mui/material/Box";
import SvgIcon from "@mui/material/SvgIcon";
import Stack from "@mui/material/Stack";
import Chip from "@mui/material/Chip";
import CwWorkText from "../CwWorkText";
import TextField from '@mui/material/TextField';
import Divider from '@mui/material/Divider'
import { read, utils, writeFile } from "xlsx";
import MessageStrip from "../utility/MessageStrip";

class CwMSFieldsDetails extends React.Component {
  constructor(props) {
    super(props);
    this.settingsPopoverRef = React.createRef();
    let emptyRow = this.emptyRow();
    this.destinations = props.destinations.map((ele) => ({ [ele.Name]: ele.URL })).reduce((acc, current) => ({ ...acc, ...current }), {});
    this.state = {
      value: "ValueHelp",
      tableVisible: false,
      showDialog: false,
      showDialogDiscard: false,
      showDialogSubmit: false,
      showDialogSave: false,
      showValueHelpDialog: false,
      showDataElement: false,
      showDataElementDialog: false,
      showVHTableDialog: false,
      showValueHelpDynamicDialog: false,
      selectedRow: this.props.row,
      constraints: [],
      taggingapp: [],
      fields: [],
      selectedHost: {},
      translation: {
        USER_ONBOARDING_UR: {},
        DESTINATION: {
          longDescription: "Destination",
        },
        HOST: {
          longDescription: "Host",
        },
        VALUE_HELP_URL: {
          longDescription: "ValueHelp Url",
        },
        API_TYPE: {
          longDescription: "Api Type",
        },
        CONSTRAINT: {
          longDescription: "Constraint",
        },
        CONSTRAINT_TYPE: {
          longDescription: "Constraint Type",
        },
        OPERATOR: {
          longDescription: "Operator",
        },
        VALUE: {
          longDescription: "Value",
        },
        DISPLAY_NAME: {
          longDescription: "Display Name",
        },
        MAPPED_NAME: {
          longDescription: "Mapped Name",
        },
        RESPONSE_PATH: {
          longDescription: "Response Path",
        },
        KEY: {
          longDescription: "Key",
        },
        VALUE: {
          longDescription: "Value",
        },
        ADDITIONAL_VALUE: {
          longDescription: "Additional Value",
        },
        FIELDS: {
          longDescription: "Fields",
        },
        SEARCHABLE: {
          longDescription: "Searchable",
        },
        UI_DISPLAY_NAME: {
          longDescription: "UI Display Name",
        },
        DATA_TABLE: {
          longDescription: "Data Table",
        },
        FILE_TYPE: {
          longDescription: "File Type",
        },
        SHEET_INDEX: {
          longDescription: "Sheet Index/ Sheet Name",
        },
        ROW_START: {
          longDescription: "Row Start",
        },
      },
      dataElementRow: {
        attributeId: "",
        createdBy: "",
        createdOn: null,
        dataType: "",
        description: "",
        destinations: null,
        isLookup: false,
        label: "",
        length: "",
        lookUpId: "",
        lookUpType: "",
        lookupConfig: null,
        name: "",
        source: "",
        updatedBy: "",
        updatedOn: null,
      },
      apiMetadata: {
        url: "",
        destination: "",
        apiType: "",
        resultPath: null,
        constraints: null,
      },
      dbMetadata: {},
      fileMetadata: {
        fileType: null,
        properties: {
          parentTag: null,
          sheets: [],
        },
      },
      valueList: [],
      applications: [],
      mappedNameList: [],
      selectedMappedNameList: [],
      uniqueMappedNameList: [],
      fieldCatalogArray: [],
      selectedApplication: "",
      tagged: [],
      taggedDetails: [],
      dataElement: [],
      permission: false,
      existing: false,
      lookupType: "VL",
      destinations: [],
      hosts: [],
      dataTable: [],
      sheets: [],
      compositeArray: [],
      dynamicColumn: [],
      rows: [],
    };
  }
  getData = (data, index) => {
    console.log(data);
    var s = 100;
    let arr = [...this.state.compositeArray];
    arr[index].referenceLookup = data;
    this.setState({
      ...this.state,
      compositeArray: arr,
    });
    let constaintLookups = [...this.state.compositeArray];
    console.log(constaintLookups, "compositeArray");
    // let arr=[];
    // constaintLookups[index]
    // constaintLookups.filter(function(element,i){
    //     let obj= {
    //         "constraintValue": element.value,
    //         "id": element.id,
    //         "isDefaulted": i===0?true:false,
    //         "lookupId": data.lookupId,
    //         "referenceId": element.referenceId,
    //         "referenceLookup":element.referenceLookup
    //       }
    //       arr.push(obj);
    // })
  };
  // state = {
  //     tableVisible: true,

  // };
  emptyRow = () => {
    const selectedRow = {
      attributeId: null,
      attributeTag: [],
      dataElementId: null,
      dataElementName: null,
      isTagged: false,
      lookUp: {
        lookupId: "",
        objectId: "",
        objectType: "",
      },
      createdBy: "",
      createdOn: null,
      description: "",
      label: "",
      name: "",
      updatedBy: "",
      updatedOn: null,
      metaData: {
        applicationId: null,
        isMandatory: true,
        isVisible: true,
        isNullable: true,
        defaultValue: "",
        isFilterable: true,
        isSortable: true,
        isEditable: true,
        isLookup: false,
      },
      workText: false,
    };
    return selectedRow;
  };

  componentDidMount() {
    if (this.props.row.metaData.isLookup && this.props.row.metaData.lookupId) {
      let url = "/WorkUtilsServices/v1/lapi?lookupId=" + this.props.row.metaData.lookupId;
      fetchWrapper(url, { headers: { Authorization: this.props.authorization } }, this.destinations)
        .then((res) => res.json())
        .then((result) => {
          let valueList,
            fields,
            apiMetadata,
            constraints = {};
          if (result.data.lookupType.toUpperCase() === "VL") {
            valueList = result.data.valueList;
            this.setState({
              ...this.state,
              apiMetadata: result.data.apiMetaData ? result.data.apiMetaData : [],
              valueList: valueList ? valueList : [],
              lookupType: "VL",
            });
          } else if (result.data.lookupType === "API") {
            constraints = result.data.apiMetadata.constraints;
            fields = result.data.apiMetadata.fields;
            apiMetadata = {
              url: result.data.apiMetadata.url,
              destination: result.data.apiMetadata.destination,
              apiType: result.data.apiMetadata.apiType,
              resultPath: result.data.apiMetadata.resultPath,
            };
            this.setState({
              ...this.state,
              constraints: constraints ? constraints : [],
              fields: fields,
              lookupType: "API",
              apiMetadata: apiMetadata,
              valueList: result.data.valueList ? [] : [],
            });
          } else if (result.data.lookupType === "DB") {
            constraints = result.data.dbMetadata.constraints;
            fields = result.data.dbMetadata.fields;
            var dbMetadata = {
              destination: result.data.dbMetadata.destination,
              lookupTable: result.data.dbMetadata.lookupTable,
            };
            apiMetadata = {
              destination: result.data.dbMetadata.destination,
            };
            this.setState({
              ...this.state,
              constraints: constraints ? constraints : [],
              fields: fields,
              lookupType: "DB",
              dbMetadata: dbMetadata,
              //destination:result.data.dbMetadata.destination,
              //lookupTable: result.data.dbMetadata.lookupTable,
              apiMetadata: apiMetadata,
              valueList: result.data.valueList ? [] : [],
            });
          } else if (result.data.lookupType === "COMPOSITE") {
            constraints = result.data.compositeMetadata.constraint;

            var constraint = [];
            if (constraints) {
              constraint.push(constraints);
            }
            var constraintColumn = result.data.compositeMetadata.constraint.constraintColumn;
            var fieldCatalogArray = this.state.fieldCatalogArray;
            var Data = {};
            fieldCatalogArray.filter(function (element) {
              if (element.name === constraintColumn) {
                Data = element;
              }
            });
            var constaintLookups = result.data.compositeMetadata.constaintLookups;

            if (constaintLookups) {
              for (var i = 0; i < constaintLookups.length; i++) {
                constaintLookups[i]["value"] = constaintLookups[i].constraintValue;
              }
            }

            this.setState({
              ...this.state,
              constraints: constraint,
              lookupType: "COMPOSITE",
              valueList: result.data.valueList ? [] : [],
              apiMetadata: result.data.apiMetadata ? [] : [],
              compositeArray: constaintLookups,
              attributeLapiDataInput: Data.metaData.isLookup,
            });
            this.getLapiData(Data.metaData);
          } else if (result.data.lookupType === "FILE") {
            this.setState({
              ...this.state,
              sheets: result.data.fileMetadata.properties.sheets,
            });
            var sheets = this.state.sheets;
            sheets.filter(function (element, index) {
              var num = element.columnStart + 1;
              let s = "",
                t;

              while (num > 0) {
                t = (num - 1) % 26;
                s = String.fromCharCode(65 + t) + s;
                num = ((num - t) / 26) | 0;
              }
              element.columnStart = s || undefined;
            });
            this.setState({
              ...this.state,
              apiMetadata: result.data.apiMetaData ? result.data.apiMetaData : [],
              valueList: result.data.apiMetaData ? result.data.apiMetaData : [],
              fileMetadata: result.data.fileMetadata ? result.data.fileMetadata : [],
              constraints: result.data.fileMetadata.constraints ? result.data.fileMetadata.constraints : [],
              fields: result.data.fileMetadata.fields ? result.data.fileMetadata.fields : [],
              lookupType: "FILE",
            });
          }
        });
    }

    fetchWrapper("/WorkUtilsServices/v1/workutils/applications", { headers: { Authorization: this.props.authorization } }, this.destinations)
      .then((res) => res.json())
      .then((result) => {
        if (this.props.applicationName) {
          let t = result.data.filter((ele) => ele.name === this.props.applicationName);
          this.getAttributeList(t[0].applicationId);
          this.setState({
            ...this.state,
            applications: t,
            taggingapp: result.data ? result.data : [],
          });
        } else {
          this.setState({
            ...this.state,
            applications: result.data ? result.data : [],
            taggingapp: result.data ? result.data : [],
          });
        }
      })
      .catch(({ message }) => {
        alert(message);
      });
    this.getDataElementsList();
    // let url = "/WorkUtilsServices/v1/data-element"
    // fetchWrapper(url, {headers:{"Authorization":this.props.authorization}})
    //     .then((res) => res.json())
    //     .then((result) => {
    //         this.setState({
    //             ...this.state,
    //             dataElement: result.data ? result.data : [],

    //         });

    //     })
  }
  getDataElementsList = () => {
    let url = "/WorkUtilsServices/v1/data-element";
    fetchWrapper(url, { headers: { Authorization: this.props.authorization } }, this.destinations)
      .then((res) => res.json())
      .then((result) => {
        this.setState({
          ...this.state,
          dataElement: result.data ? result.data : [],
        });
      });
  };
  getDestinationList = () => {
    let url;

    url = "/WorkUtilsServices/v1/lapi/destination";
    fetchWrapper(url, { headers: { Authorization: this.props.authorization } }, this.destinations)
      .then((res) => res.json())
      .then((result) => {
        // this.setState({
        //     ...this.state,
        //     tempArr: result.output
        // });

        this.setState({
          ...this.state,
          destinations: result.data ? result.data : [],
        });
      });
  };
  getHost = () => {
    let url;

    url = "/WorkUtilsServices/v1/lapi/databaseHost";
    fetchWrapper(url, { headers: { Authorization: this.props.authorization } }, this.destinations)
      .then((res) => res.json())
      .then((result) => {
        // this.setState({
        //     ...this.state,
        //     tempArr: result.output
        // });

        this.setState({
          ...this.state,
          hosts: result.data ? result.data : [],
        });
      });
  };
  getDataTableList = (ele) => {
    let url;
    let oPayload = ele;

    url = "/WorkUtilsServices/v1/lapi/databaseTableDetails";
    fetchWrapper(
      url,
      {
        method: "POST",
        headers: { "Content-Type": "application/json", Authorization: this.props.authorization },
        body: JSON.stringify(oPayload),
      },
      this.destinations
    )
      .then((res) => res.json())
      .then((result) => {
        // this.setState({
        //     ...this.state,
        //     tempArr: result.output
        // });

        this.setState({
          ...this.state,
          dataTable: result.data ? result.data : [],
        });
      });
  };
  getAttributeList = (appId) => {
    let url = "/WorkUtilsServices/v1/attribute-master?applicationId=" + appId;
    fetchWrapper(url, { headers: { Authorization: this.props.authorization } }, this.destinations)
      .then((res) => res.json())
      .then((result) => {
        this.setState({
          ...this.state,
          selectedApplication: appId,
          fieldCatalogArray: result.data ? result.data : [],
        });
      });
  };
  createNewField = (event, newValue) => {
    this.setState({
      ...this.state,
      tableVisible: false,
      tagged: [],
      constraints: [],
      compositeArray: [],
      valueList: [],
      apiMetadata: [],
      lookupType: "VL",
      attributeLapiDataInput: false,
    });
  };
  editFields = (row) => {
    let tagged = [];
    let applList = this.state.applications;
    if (row.attributeTag) {
      row.attributeTag.filter(function (element) {
        applList.filter(function (appl) {
          if (element.destinationApp === appl.applicationId) {
            tagged.push(appl.name);
          }
        });
      });
    }
    this.setState({
      ...this.state,
      existing: true,
      tagged: tagged,
      tableVisible: false,
      permission: row.attributeTag ? (row.attributeTag[0].permission == "Write" ? true : false) : false,
      selectedRow: row,
    });
    if (row.metaData.isLookup && row.metaData.lookupId) {
      let url = "/WorkUtilsServices/v1/lapi?lookupId=" + row.metaData.lookupId;
      fetchWrapper(url, { headers: { Authorization: this.props.authorization } }, this.destinations)
        .then((res) => res.json())
        .then((result) => {
          //let  constraints= [];
          let valueList,
            fields,
            apiMetadata,
            constraints = {};
          if (result.data.lookupType.toUpperCase() === "VL") {
            valueList = result.data.valueList;
            this.setState({
              ...this.state,
              apiMetadata: result.data.apiMetaData ? result.data.apiMetaData : [],
              valueList: valueList ? valueList : [],
              lookupType: "VL",
            });
          } else if (result.data.lookupType === "API") {
            constraints = result.data.apiMetadata.constraints;
            fields = result.data.apiMetadata.fields;
            apiMetadata = {
              url: result.data.apiMetadata.url,
              destination: result.data.apiMetadata.destination,
              apiType: result.data.apiMetadata.apiType,
              resultPath: result.data.apiMetadata.resultPath,
            };
            this.setState({
              ...this.state,
              constraints: constraints ? constraints : [],
              fields: fields,
              lookupType: "API",
              apiMetadata: apiMetadata,
              valueList: result.data.valueList ? [] : [],
            });
          } else if (result.data.lookupType === "DB") {
            constraints = result.data.dbMetadata.constraints;
            fields = result.data.dbMetadata.fields;
            var dbMetadata = {
              destination: result.data.dbMetadata.destination,
              lookupTable: result.data.dbMetadata.lookupTable,
            };
            apiMetadata = {
              destination: result.data.dbMetadata.destination,
            };
            this.setState({
              ...this.state,
              constraints: constraints ? constraints : [],
              fields: fields,
              lookupType: "DB",
              dbMetadata: dbMetadata,
              //destination:result.data.dbMetadata.destination,
              //lookupTable: result.data.dbMetadata.lookupTable,
              apiMetadata: apiMetadata,
              valueList: result.data.valueList ? [] : [],
            });
          } else if (result.data.lookupType === "COMPOSITE") {
            constraints = result.data.compositeMetadata.constraint;

            var constraint = [];
            if (constraints) {
              constraint.push(constraints);
            }
            var constraintColumn = result.data.compositeMetadata.constraint.constraintColumn;
            var fieldCatalogArray = this.state.fieldCatalogArray;
            var Data = {};
            fieldCatalogArray.filter(function (element) {
              if (element.name === constraintColumn) {
                Data = element;
              }
            });
            var constaintLookups = result.data.compositeMetadata.constaintLookups;

            if (constaintLookups) {
              for (var i = 0; i < constaintLookups.length; i++) {
                constaintLookups[i]["value"] = constaintLookups[i].constraintValue;
              }
            }

            this.setState({
              ...this.state,
              constraints: constraint,
              lookupType: "COMPOSITE",
              valueList: result.data.valueList ? [] : [],
              apiMetadata: result.data.apiMetadata ? [] : [],
              compositeArray: constaintLookups,
              attributeLapiDataInput: Data.metaData.isLookup,
            });
            this.getLapiData(Data.metaData);
          } else if (result.data.lookupType === "FILE") {
            this.setState({
              ...this.state,
              sheets: result.data.fileMetadata.properties.sheets,
            });
            var sheets = this.state.sheets;
            sheets.filter(function (element, index) {
              var num = element.columnStart + 1;
              let s = "",
                t;

              while (num > 0) {
                t = (num - 1) % 26;
                s = String.fromCharCode(65 + t) + s;
                num = ((num - t) / 26) | 0;
              }
              element.columnStart = s || undefined;
            });
            this.setState({
              ...this.state,
              apiMetadata: result.data.apiMetaData ? result.data.apiMetaData : [],
              valueList: result.data.apiMetaData ? result.data.apiMetaData : [],
              fileMetadata: result.data.fileMetadata ? result.data.fileMetadata : [],
              constraints: result.data.fileMetadata.constraints ? result.data.fileMetadata.constraints : [],
              fields: result.data.fileMetadata.fields ? result.data.fileMetadata.fields : [],
              lookupType: "FILE",
            });
          }
        });
    }
  };
  getColumnDescription = (i) => {
    // var sheets = this.state.sheets;
    //                     sheets.filter(function (element,index) {
    //                         var i =  element.columnStart ;
    //    for (var ret = '', a = 1, b = 26; (num -= a) >= 0; a = b, b *= 26) {
    //     ret = String.fromCharCode(parseInt((num % b) / a) + 65) + ret;
    //   }
    //function getColumnDescription(i) {
    const m = i % 26;
    const c = String.fromCharCode(65 + m);
    const r = i - m;
    return r > 0 ? `${this.getColumnDescription((r - 1) / 26)}${c}` : `${c}`;

    //}

    // });
  };

  backToTable = (event) => {
    let emptyRow = this.emptyRow();
    this.setState(
      {
        ...this.state,
        existing: false,
        tableVisible: true,
        selectedRow: emptyRow,
      },
      () => console.log(this.state.tableVisible)
    );
  };
  handleChangeValueHelpType = (event) => {
    let state = event.target.value;
    let fields = [];
    if (event.target.value !== "VL") {
      if (event.target.value === "DB" || event.target.value === "API") {
        this.getDestinationList();
        this.getHost();
      }
      this.getAttributeList(this.state.selectedRow.metaData.applicationId);
      if (this.state.fields.length === 0) {
        fields = [
          {
            columnName: this.state.selectedRow.name,
            displayName: this.state.selectedRow.label,
            id: null,
            isDisplayName: true,
            mappedName: null,
            searchable: true,
          },
        ];
      } else {
        fields = this.state.fields;
        fields[0].columnName = this.state.selectedRow.name;
        fields[0].displayName = this.state.selectedRow.label;
      }
      // this.setState({
      //     ...this.state,
      //     fields: fields
      // })
    }
    // this.state.selectedRow.isLookup=state;
    this.setState({
      ...this.state,
      fields: fields,
      lookupType: state,
      selectedRow: {
        ...this.state.selectedRow,
        lookupType: state,
        constraints: [],
        compositeArray: [],
        attributeLapiDataInput: false,
        // metaData:{
        //     ...this.state.selectedRow.metaData,
        //     lookUpType: state
        // }
      },
    });
  };

  handleChange = (evt, val) => {
    let value;
    if (evt.target.name !== "defaultValue") {
      value = evt.target.checked;
    } else {
      value = evt.target.value;
    }
    if (val) {
      value = !this.state.selectedRow.metaData[val];
      evt.target.name = val;
    }

    this.setState({
      ...this.state,
      value: val === "WorkText" || "ValueHelp" ? val : this.state.value,
      selectedRow: {
        ...this.state.selectedRow,
        metaData: {
          ...this.state.selectedRow.metaData,
          [evt.target.name]: value,
        },
      },
    });
  };
  workTextSwitch = (event) => {
    let state = event;
    // this.state.selectedRow.isLookup=state;
    this.setState({
      ...this.state,
      value: state ? "WorkText" : this.state.value,
      selectedRow: {
        ...this.state.selectedRow,
        workText: state,
      },
    });
  };
  onValueHelpSwitch = (event) => {
    let state = event.target.value === "Yes" ? true : false;

    if ((this.state.selectedRow.name === "" || this.state.selectedRow.label === "" || this.state.selectedRow.description === "") && state === true) {
      this.setState({
        ...this.state,

        selectedRow: {
          ...this.state.selectedRow,
          metaData: {
            ...this.state.selectedRow.metaData,
            isLookup: false,
          },
        },
        snackbarOpen: true,
        snackbarMessg: "Please fill name, label, description",
        snackbarSeverity: "error"
      });
    } else if (this.state.selectedRow.metaData.applicationId === null && state === true) {
      this.setState({
        ...this.state,

        selectedRow: {
          ...this.state.selectedRow,
          metaData: {
            ...this.state.selectedRow.metaData,
            isLookup: false,
          },
        },
        snackbarOpen: true,
        snackbarMessg: "Please select application",
        snackbarSeverity: "error"
      });
    } else {
      this.setState({
        ...this.state,
        value: state ? "ValueHelp" : this.state.value,
        selectedRow: {
          ...this.state.selectedRow,
          metaData: {
            ...this.state.selectedRow.metaData,
            isLookup: state,
          },
        },
      });
    }
  };
  onPermissionSwitch = (event) => {
    let state = event.target.value === "Write" ? true : false;
    // this.state.selectedRow.isLookup=state;
    this.setState({
      ...this.state,
      permission: state,
    });
  };

  inputChangeHandler = (evt, keyProp) => {
    let value = evt.target.value,
      isValid,
      errorMssg = "",
      isValidObj = {},
      lengthVal;
    if (evt.target.name === "name") {
      value = value.toUpperCase();
      this.setState({
        ...this.state,
        selectedRow: {
          ...this.state.selectedRow,
          [evt.target.name]: value,
        },
      });
    } else if (evt.target.name === "applicationId") {
      this.setState({
        ...this.state,
        selectedRow: {
          ...this.state.selectedRow,
          metaData: {
            ...this.state.selectedRow.metaData,
            applicationId: keyProp.key.split("$")[1],
          },
        },
      });
    } else {
      this.setState({
        ...this.state,
        selectedRow: {
          ...this.state.selectedRow,
          [evt.target.name]: value,
        },
      });
    }

    //this.state.obj[evt.target.name] = evt.target.value;
  };
  inputChangeHandler1 = (evt, val) => {
    let value = evt.target.value,
      key,
      name;
    let fileTypeProp;
    name = evt.target.name;
    if (val) {
      value = val.name;
      name = evt.target.id.split("-")[0];
    }
    if (this.state.lookupType === "API") {
      key = "apiMetadata";
    } else if (this.state.lookupType === "DB") {
      key = "dbMetadata";
    } else if (this.state.lookupType === "FILE") {
      key = "fileMetadata";
      fileTypeProp = {
        parentTag: this.state.fileMetadata.properties.parentTag,
        sheets: this.state.sheets,
      };
      if (name === "parentTag") {
        fileTypeProp.parentTag = value;
      }

      // if (value === ".csv") {
      //     fileTypeProp = {
      //         "parentTag": null,
      //         "sheets": null
      //     };
      // } else if (value === ".xml") {
      //     fileTypeProp = {
      //         "parentTag": "",
      //         "sheets": null

      //     }
      // } else if (value === ".xlsx") {
      //     fileTypeProp = {
      //         "parentTag": null,
      //         "sheets": []
      //     }
      // }
    }
    if (this.state.lookupType === "COMPOSITE" && name === "fileType") {
      //this.setState({
      // ...this.state,
      // if(name = "fileType"){
      key = "fileMetadata";
      // },
    }
    if (this.state.lookupType !== "FILE") {
      this.setState({
        ...this.state,
        // if(name = "fileType"){
        //     key = "fileMetadata"
        // },
        [key]: {
          ...this.state[key],

          [name]: value,
        },
      });
    } else {
      this.setState({
        ...this.state,

        [key]: {
          ...this.state[key],
          [name]: value,
          properties: fileTypeProp,
        },
      });
    }
    console.log(this.state.fileMetadata, "fileMetadata");
  };
  inputChangeHandler2 = (evt, val) => {
    let value = val?.lookupTableId;

    this.setState({
      ...this.state,
      mappedNameList: val?.columns,

      dbMetadata: {
        ...this.state.dbMetadata,

        lookupTable: value,
        lookupTableDetails: val,
      },
    });
  };
  dbInputChangeHandler = (evt) => {
    let index = this.state.hosts.findIndex((ele) => ele.host === evt.target.value);
    this.setState({
      ...this.state,
      dataTable: [],
    });
    this.getDataTableList(this.state.hosts[index]);
    this.setState({
      ...this.state,
      selectedHost: this.state.hosts[index],
      dbMetadata: {
        ...this.state.dbMetadata,
        [evt.target.name]: evt.target.value,
      },
    });
  };
  valueHelpTableInputs = (evt, index, source) => {
    let value;
    if (evt.target.name !== "isDisplayName" && evt.target.name !== "searchable") {
      value = evt.target.value;
    } else {
      value = evt.target.checked;
    }
    if (this.state.lookupType === "API" || this.state.lookupType === "DB" || this.state.lookupType === "FILE" || this.state.lookupType === "COMPOSITE") {
      if (evt.target.id === "fieldsTable" || source === "fieldsTable") {
        let fields = [...this.state.fields];

        if (evt.target.name == "isDisplayName") {
          fields.filter(function (element) {
            element.isDisplayName = false;
          });
        }

        fields[index][evt.target.name] = value;
        if (evt.target.name === "columnName" && evt.target.row) {
          fields[index]["displayName"] = evt.target.row.label;
        }
        this.setState({
          ...this.state,
          fields: fields,
          showVHTableDialog: false,
        });
      } else if (evt.target.id === "constraintTable" || source === "constraintTable") {
        let constraints = [...this.state.constraints];

        constraints[index][evt.target.name] = value;
        if (evt.target.name === "constraintColumn" && evt.target.row) {
          constraints[index]["constraintName"] = evt.target.row.label;
        }
        this.setState({
          ...this.state,
          constraints: constraints,
          showVHTableDialog: false,
          // selectedField: evt.target.row.name,
          // attributeLapiDataInput: evt.target.row.metaData.isLookup
        });
      }
      console.log(this.state.constraints, "constraints");
      if (this.state.lookupType === "DB") {
        let tempArray = this.state.mappedNameList.filter((item) => {
          if (!(this.state.fields.findIndex((ele) => ele.mappedName === item.columnName) === -1 && this.state.constraints.findIndex((ele) => ele.mappedName === item.columnName) === -1)) return item;
        });

        this.setState({
          ...this.state,
          uniqueMappedNameList: tempArray,
        });
      }
    } else if (this.state.lookupType === "VL") {
      let valueList = [...this.state.valueList];
      valueList[index][evt.target.name] = value;
      this.setState({
        ...this.state,
        valueList: valueList,
      });
    }
  };
  valueHelpTableMappedNameInput = (evt, index, val) => {
    let temparray = [];
  };
  addNewConstraint = (event) => {
    let constraints = [...this.state.constraints];
    let obj = {
      constraintColumn: null,
      constraintName: null,
      constraintOperator: "EQ",
      constraintType: null,
      constraintValue: null,
      id: null,
      mappedName: null,
    };

    constraints.push(obj);
    this.setState({
      ...this.state,
      constraints: constraints,
    });
  };
  addNewFields = (event) => {
    let fields = [...this.state.fields];
    let obj = {
      id: "",
      columnName: "",
      displayName: "",
      searchable: true,
      isDisplayName: false,
      mappedName: "",
      tempId: fields.length - 1,
    };

    fields.push(obj);
    this.setState({
      ...this.state,
      fields: fields,
    });
  };
  deleteFieldRows = (row, index) => {
    let fields = [...this.state.fields];
    //  let i;
    // if (row) {
    //     fields.filter(function (element, index) {
    //         if (element.id === row) {
    //             i = index;
    //         }
    //     });
    // }else{
    //     fields.filter(function (element, index) {
    //         if (element.tempId === tempId) {
    //             i = index;
    //         }
    //     });
    // }
    fields.splice(index, 1);
    this.setState({
      ...this.state,
      fields: fields,
    });
  };
  deleteConstraintRows = (row, index) => {
    let constraints = [...this.state.constraints];
    //if(!row.id){
    constraints.splice(index, 1);
    this.setState({
      ...this.state,
      constraints: constraints,
    });
  };
  addNewStaticVL = (event) => {
    let staticVL = [...this.state.valueList];
    let obj = {
      key: "",
      value: "",
      additionalValue: "",
    };

    staticVL.unshift(obj);
    this.setState({
      ...this.state,
      valueList: staticVL,
    });
  };
  deleteStaticVL = (row, index) => {
    let fields = [...this.state.valueList];
    fields.splice(index, 1);
    this.setState({
      ...this.state,
      valueList: fields,
    });
  };
  discardFieldCatalog() {
    this.setState(
      {
        ...this.state,
        tableVisible: true,
      },
      () => {
        console.log(this.state.tableVisible, "discard");
      }
    );
  }
  // this.setState({
  //     ...this.state,
  //     //existing: false,
  //     tableVisible: true
  //     //selectedRow: emptyRow
  // }, ()=>console.log(this.state.tableVisible,"discard"));
  //}
  closeDialog = (action) => {
    if (action === "DISCARD") {
      this.discardFieldCatalog();
    } else if (action === "SUBMIT") {
      //   this.onSaveMappingRoles();
    } else if (action === "SAVE") {
      //   this.onSaveMappingRolesDraft();
      this.onSaveFieldCatalog();
    }

    this.setState({
      ...this.state,
      showDialog: false,
      showDialogDiscard: false,
      showDialogSubmit: false,
      showDialogSave: false,
      showValueHelpDialog: false,
      showDataElementDialog: false,
      showVHTableDialog: false,
      showValueHelpDynamicDialog: false,
    });
  };
  // permissionChange = () => {

  // }
  onSaveFieldCatalog = () => {
    console.log(this.state.existing);
    let createdOn, createdBy, method, url;
    var dDate = new Date().getTime();
    if (this.props.existing) {
      createdOn = this.state.selectedRow.createdOn;
      createdBy = this.state.selectedRow.createdBy;
      method = "PATCH";
      url = "/WorkUtilsServices/v1/attribute-master/" + this.state.selectedRow.attributeId + "?applicationId=" + this.state.selectedRow.metaData.applicationId;
    } else {
      createdOn = dDate;
      createdBy = this.props.pid;
      method = "POST";
      url = "/WorkUtilsServices/v1/attribute-master?applicationId=" + this.state.selectedRow.metaData.applicationId;
    }

    // const payload = {
    //     "createdBy": createdBy,
    //     "createdOn": createdOn,
    //     "dataElementId": this.state.selectedRow.dataElementId,
    //     "dataType": this.state.selectedRow.dataType,
    //     "description": this.state.selectedRow.description,
    //     "isLookup": this.state.selectedRow.isLookup,
    //     "label": this.state.selectedRow.label,
    //     "length": this.state.selectedRow.length,
    //     "lookupId": this.state.selectedRow.lookupId,
    //     "name": this.state.selectedRow.name,
    //     "updatedBy": this.props.pid,
    //     "updatedOn": dDate
    // }
    let attributeTag = this.state.selectedRow.attributeTag;
    let arr = [];
    var permission = this.state.permission === true ? "Write" : this.state.permission === false ? "Read" : this.state.permission;

    // this.setState(
    //     {
    //         ...this.state,
    //         permission: permission
    //     }, () => {
    if (attributeTag.length !== 0) {
      let that = this;

      attributeTag.filter(function (element) {
        let tagObj = {
          attributeId: that.state.selectedRow.attributeId,
          destinationApp: element.destinationApp,
          permission: permission,
          sourceApp: that.state.selectedRow.metaData.applicationId,
          tagId: element.tagId,
          taggedBy: element.taggedBy,
          taggedOn: element.taggedOn,
        };
        arr.push(tagObj);
      });
    }

    const payload = {
      attributeId: this.state.selectedRow.attributeId,
      attributeTag: arr,
      createdBy: createdBy,
      createdOn: createdOn,
      dataElementId: this.state.selectedRow.dataElementId,
      dataElementName: this.state.selectedRow.dataElementName,
      description: this.state.selectedRow.description,
      isTagged: this.state.selectedRow.attributeTag.length > 0 ? true : false,
      label: this.state.selectedRow.label,
      // "lookUp": {
      //     "lookupId": "string",
      //     "objectId": "string",
      //     "objectType": "string"
      // },
      metaData: {
        applicationId: this.state.selectedRow.metaData.applicationId,
        attributeId: this.state.selectedRow.attributeId,
        defaultValue: this.state.selectedRow?.metaData?.defaultValue,
        isDefaulted: this.state.selectedRow?.metaData?.defaultValue ? true : false,
        isEditable: this.state.selectedRow.metaData.isEditable,
        isFilterable: this.state.selectedRow.metaData.isFilterable,
        isLookup: this.state.selectedRow.metaData.isLookup,
        isMandatory: this.state.selectedRow.metaData.isMandatory,
        isSortable: this.state.selectedRow.metaData.isSortable,
        isVisible: this.state.selectedRow.metaData.isVisible,
        isNullable: this.state.selectedRow.metaData.isNullable,
        lookupId: this.state.selectedRow.metaData.lookupId,
      },
      name: this.state.selectedRow.name,
      updatedBy: this.props.pid,
      updatedOn: dDate,
    };

    const requestParam1 = {
      method: method,
      headers: { "Content-Type": "application/json", Authorization: this.props.authorization },
      body: JSON.stringify(payload),
    };
    fetchWrapper(url, requestParam1, this.destinations)
      .then((response) => response.json())
      .then((data, message) => {
        console.log(data);
        if (!data.data) {
          this.setState({
            ...this.state,

            snackbarOpen: true,
            snackbarMessg: data.message,
            snackbarSeverity: "success"
          });
        } else {
          if (this.state.selectedRow?.metaData?.isLookup) {
            this.fnLookupsave(data.data);
          }
          this.getAttributeList(this.state.selectedRow.metaData.applicationId);
          this.setState({
            ...this.state,
            // tableVisible: true,
            snackbarOpen: true,
            snackbarMessg: data.message,
            snackbarSeverity: "success"
          });
          this.props.Back();
        }
      });
    //});
  };

  onCancel = () => {
    // this.props.history.push("/roles");
    // this.props.setNameVis(false);
    this.setState({
      ...this.state,

      showDialogDiscard: true,
    });
  };

  onSaveDataElement = () => {
    var payload1 = this.state.rolesData;
    var a = this.state.isLookup;
    if (this.state.selectedRow.name === "" || this.state.selectedRow.label === "" || this.state.selectedRow.description === "" || !this.state.selectedRow.metaData.applicationId) {
      this.setState({
        ...this.state,
        //tableVisible: true,
        snackbarOpen: true,
        snackbarMessg: "Please fill name, label, description and application.",
        snackbarSeverity: "error"
      });
    } else if (!this.state.selectedRow.dataElementId) {
      this.setState({
        ...this.state,
        //tableVisible: true,
        snackbarOpen: true,
        snackbarMessg: "Please select data element",
        snackbarSeverity: "error"
      });
    }
    //if(this.state.isLookup)
    else if (this.state.selectedRow?.metaData?.isLookup) {
      var sheets = this.state.sheets;
      var bFlag = false;
      if (this.state.sheets.length !== 0) {
        sheets.filter(function (element, i) {
          if (!element.columnStart || !element.rowStart || !element.sheetName) {
            bFlag = true;
          }
        });
      }
      if (this.state.lookupType === "VL" && this.state.valueList.length === 0) {
        this.setState({
          ...this.state,
          //tableVisible: true,
          snackbarOpen: true,
          snackbarMessg: "Please fill the value list details",
          snackbarSeverity: "error"
        });
      } else if (this.state.lookupType === "API" && (!this.state.apiMetadata.destination || !this.state.apiMetadata.url || !this.state.apiMetadata.apiType)) {
        this.setState({
          ...this.state,
          //tableVisible: true,
          snackbarOpen: true,
          snackbarMessg: "Please fill destination, valuehelp Url and Api Type.",
          snackbarSeverity: "error"
        });
      } else if (this.state.lookupType === "DB" && (!this.state.dbMetadata.destination || !this.state.dbMetadata.lookupTable)) {
        this.setState({
          ...this.state,
          //tableVisible: true,
          snackbarOpen: true,
          snackbarMessg: "Please fill destination and data Table details.",
          snackbarSeverity: "error"
        });
      } else if (this.state.lookupType === "FILE" && (!this.state.fileMetadata.fileType || !this.state.fileMetadata.properties || this.state.sheets.length === 0 || bFlag)) {
        this.setState({
          ...this.state,
          //tableVisible: true,
          snackbarOpen: true,
          snackbarMessg: "Please fill file type and sheet details by adding sheets.",
          snackbarSeverity: "error"
        });
      } else if (this.state.lookupType === "COMPOSITE" && (this.state.constraints.length === 0 || !this.state.constraints[0].constraintColumn)) {
        this.setState({
          ...this.state,
          //tableVisible: true,
          snackbarOpen: true,
          snackbarMessg: "Please fill the constraint details",
          snackbarSeverity: "error"
        });
      } else {
        this.setState({
          ...this.state,

          showDialogSave: true,
        });
      }
    } else {
      this.setState({
        ...this.state,

        showDialogSave: true,
      });
    }
  };
  fnLookupsave = (data) => {
    let method,
      lookupType = this.state.lookupType,
      valueList,
      oPayload,
      sUrl;
    if (data.metaData.lookupId) {
      method = "PATCH";
      sUrl = "/WorkUtilsServices/v1/lapi/" + data.metaData.lookupId + "?objectId=" + data.attributeId + "&objectType=FC";
    } else {
      method = "POST";
      sUrl = "/WorkUtilsServices/v1/lapi" + "?objectId=" + data.attributeId + "&objectType=FC";
    }
    if (lookupType === "VL") {
      oPayload = {
        lookupId: data.metaData.lookupId,
        lookupName: data.name + "_" + lookupType,
        lookupType: lookupType,
        valueList: this.state.valueList,
      };
    } else if (lookupType === "API") {
      oPayload = {
        lookupId: data.metaData.lookupId,
        lookupName: data.name + "_" + lookupType,
        lookupType: lookupType,
        apiMetadata: {
          apiType: this.state.apiMetadata.apiType,
          constraints: this.state.constraints,
          destination: this.state.apiMetadata.destination,
          fields: this.state.fields,
          pagination: {
            allowed: true,
            recordsPerPage: "30",
          },
          resultPath: this.state.apiMetadata.resultPath,
          url: this.state.apiMetadata.url,
        },
      };
    } else if (lookupType === "DB") {
      oPayload = {
        lookupId: data.metaData.lookupId,
        lookupName: data.name + "_" + lookupType,
        lookupType: lookupType,
        dbMetadata: {
          constraints: this.state.constraints,
          databasePropertiesDto: {
            dbType: "string",
            lookupId: "string",
            password: "string",
            url: "string",
            userName: "string",
          },
          destination: this.state.dbMetadata.destination,
          fields: this.state.fields,
          lookupTable: this.state.dbMetadata.lookupTable,
          pagination: {
            allowed: true,
            recordsPerPage: "30",
          },
        },
      };
    } else if (lookupType === "FILE") {
      let sheets = [...this.state.sheets];
      if (this.state.sheets.length !== 0) {
        sheets.filter(function (element, i) {
          if (element.columnStart) {
            element.columnStart = element.columnStart.split("").reduce((r, a) => r * 26 + parseInt(a, 36) - 9, 0) - 1;
          }
        });
      }

      oPayload = {
        lookupId: data.metaData.lookupId,
        lookupName: data.name + "_" + lookupType,
        lookupType: lookupType,
        fileMetadata: {
          constraints: this.state.constraints,
          fields: this.state.fields,
          fileType: this.state.fileMetadata.fileType.toUpperCase(),
          lookupId: data.metaData.lookupId,
          properties: {
            parentTag: this.state.fileMetadata.properties.parentTag,
            sheets: this.state.sheets,
          },
        },
      };
    } else if (lookupType === "COMPOSITE") {
      let constaintLookups = this.state.compositeArray;
      console.log(constaintLookups, "compositeArray2");
      let arr = [];
      constaintLookups.filter(function (element, i) {
        let obj = {
          constraintValue: element.value,
          id: element.id,
          isDefaulted: i === 0 ? true : false,
          lookupId: data.metaData.lookupId,
          referenceId: element.referenceId,
          referenceLookup: element.referenceLookup,
        };
        arr.push(obj);
      });

      oPayload = {
        lookupId: data.metaData.lookupId,
        lookupName: data.name + "_" + lookupType,
        lookupType: lookupType,
        compositeMetadata: {
          constaintLookups: arr,
          constraint: {
            constraintColumn: this.state.constraints[0].constraintColumn,
            constraintName: this.state.constraints[0].constraintName,
            constraintOperator: this.state.constraints[0].constraintOperator,
            constraintType: this.state.constraints[0].constraintType,
            constraintValue: this.state.constraints[0].constraintValue,
            id: this.state.constraints[0].id,
            mappedName: this.state.constraints[0].mappedName,
          },
        },
      };
    }
    const requestParam1 = {
      method: method,
      headers: { "Content-Type": "application/json", Authorization: this.props.authorization },
      body: JSON.stringify(oPayload),
    };
    if (this.state.constraints) {
      fetchWrapper(sUrl, requestParam1, this.destinations)
        .then((response) => response.json())
        .then((data, message) => {
          console.log(data);

          this.setState({
            ...this.state,
            snackbarOpen: true,
            snackbarMessg: "Data Element is Saved!",
            snackbarSeverity: "success"
          });
        });
    } else {
      this.setState({
        ...this.state,
        snackbarOpen: true,
        snackbarMessg: "Select constraint!",
        snackbarSeverity: "error"
      });
    }
  };
  onValueHelpTemp = () => {
    this.setState({
      ...this.state,
      showValueHelpDialog: true,
    });
  };
  onValueHelp = () => {
    let url = "/WorkUtilsServices/v1/data-element";
    fetchWrapper(url, { headers: { Authorization: this.props.authorization } }, this.destinations)
      .then((res) => res.json())
      .then((result) => {
        this.setState({
          ...this.state,
          dataElement: result.data ? result.data : [],
          showValueHelpDialog: true,
        });
      });
  };
  closeDataEleDialog = (action) => {
    this.setState({
      ...this.state,

      showDataElement: false,
    });
  };
  onDataElementDialog = () => {
    // let url = "/WorkUtilsServices/v1/data-element"
    // fetchWrapper(url)
    //     .then((res) => res.json())
    //     .then((result) => {
    this.setState({
      ...this.state,
      // dataElement: result.data ? result.data : [],
      showDataElementDialog: true,
    });

    // })
  };
  applicationChangeHandler = (evt, keyProp) => {
    let url;

    url = "/WorkUtilsServices/v1/attribute-master?applicationId=" + keyProp.props.value;
    fetchWrapper(url, { headers: { Authorization: this.props.authorization } }, this.destinations)
      .then((res) => res.json())
      .then((result) => {
        // this.setState({
        //     ...this.state,
        //     tempArr: result.output
        // });

        this.setState({
          ...this.state,
          selectedApplication: evt.target.value,
          fieldCatalogArray: result.data ? result.data : [],
        });
      });
  };
  fnTagging = (event, keyProp) => {
    var taggedValue = [];
    let createdOn = new Date().getTime();
    let createdBy = this.props.pid;
    let id = keyProp.key.split("$")[1];

    let value = event.target.value;
    taggedValue.push();
    // this.setState({
    //     ...this.state,
    //     tagged: value,

    // });

    let exist = false;
    let arr1 = this.state.selectedRow.attributeTag;
    if (arr1.length !== 0 && arr1[0].destinationApp === null) {
      arr1.splice(0);
    }
    let exElement = arr1.filter(function (element, index) {
      if (element.destinationApp === id || element.sourceApp === id) {
        exist = true;
        return index;
      }
    });
    if (id !== this.state.selectedRow.metaData.applicationId) {
      if (!exist) {
        let obj = {
          destinationApp: keyProp.key.split("$")[1],
          tagId: null,
          taggedBy: createdOn,
          taggedOn: createdOn,
        };
        arr1.push(obj);
        this.setState({
          ...this.state,
          tagged: value,
          // taggedDetails: arr,
          selectedRow: {
            ...this.state.selectedRow,
            attributeTag: arr1,
          },
        });
      } else {
        arr1.splice(exElement);
        this.setState({
          ...this.state,
          tagged: value,
          // taggedDetails: arr,
          selectedRow: {
            ...this.state.selectedRow,
            attributeTag: arr1,
          },
        });
        // exElement.tag
      }
    }
  };
  setTaggedApplication = (evt, option, index) => {
    let exist = false;
    let arr = [...this.state.taggedDetails];
    let arr1 = this.state.selectedRow.attributeTag;
    let exIndex = arr1.filter(function (element, index) {
      if (element.destinationApp === option.applicationId) {
        exist = true;
        return index;
      }
    });
    if (!exist) {
      var dDate = new Date().getTime();
      let createdOn = dDate;
      let createdBy = this.props.pid;
      let obj = {
        destinationApp: option.applicationId,
        tagId: null,
        taggedBy: createdOn,
        taggedOn: createdOn,
      };
      arr1.push(obj);
      arr.push(option);
      this.setState({
        ...this.state,
        taggedDetails: arr,
        selectedRow: {
          ...this.state.selectedRow,
          attributeTag: arr1,
        },
      });
    } else {
      arr1.splice(exIndex);
    }
  };
  rowSelected = (row) => {
    this.setState({
      ...this.state,
      showValueHelpDialog: false,
      dataElementRow: row,
      selectedRow: {
        ...this.state.selectedRow,
        dataElementId: row?.dataElementId,
        dataElementName: row.name,
      },
    });
  };
  rowSelectedDynamic = (row, i) => {
    let key = row[this.state.selectedField];
    let arr = [...this.state.compositeArray];
    if (!key) {
      key = row[this.state.dynamicColumn[0].mappedName];
    }
    let value;
    var that = this;
    this.state.dynamicColumn.filter(function (element) {
      if (element.isDisplayName) {
        arr[that.state.compositeArrayIndex].value = row[element.mappedName];
      }
    });

    arr[this.state.compositeArrayIndex].key = key;

    this.setState({
      ...this.state,
      showValueHelpDynamicDialog: false,
      compositeArray: arr,
    });
  };
  rowSelectedValueHelp = (row) => {
    if (this.state.tableType === "constraintTable") {
      let evt = {
        target: {
          row: row,
          value: row.name,
          id: "constraintTable",
          name: "constraintColumn",
        },
      };
      this.setState(
        {
          ...this.state,
          selectedField: row.name,
          attributeLapiDataInput: row.metaData.isLookup,
        },
        () => this.valueHelpTableInputs(evt, this.state.index)
      );
      //     setTimeout(() => {

      // }, 2000)
    } else {
      let evt = {
        target: {
          row: row,
          value: row.name,
          id: "fieldsTable",
          name: "columnName",
        },
      };
      // this.setState({
      //     ...this.state,
      //     selectedField: row.name,
      //     attributeLapiDataInput: row.metaData.isLookup
      // })
      this.valueHelpTableInputs(evt, this.state.index);
    }

    //if (this.state.lookupType === "COMPOSITE" && row.metaData.isLookup) {
    console.log(row, "row");
    this.getLapiData(row.metaData);
    console.log(row.metaData, "row.metaData");
    //}
  };
  rowSelectedValueHelpFields = (row) => {
    let evt = {
      target: {
        row: row,
        value: row.name,
        id: "fieldsTable",
        name: "columnName",
      },
    };
    // this.setState({
    //     ...this.state,
    //     selectedField: row.name,
    //     attributeLapiDataInput: row.metaData.isLookup
    // })
    this.valueHelpTableInputs(evt, this.state.index);

    if (this.state.lookupType === "COMPOSITE" && row.metaData.isLookup) {
      this.getLapiData(row.metaData);
    }
  };
  getLapiConfig = (row) => {
    let sUrl = "/WorkUtilsServices/v1/lapi?lookupName=" + row.name;
    fetchWrapper(sUrl, { headers: { Authorization: this.props.authorization } }, this.destinations)
      .then((res) => res.json())
      .then((result) => {
        if (result.data.lookupType.toUpperCase() === "VL") {
          this.setState({
            ...this.state,
            valueList: result.valueList,
          });
        } else {
          this.getLapiData(result);
        }
      });
  };
  getLapiData = (data) => {
    console.log(data, "data");
    if (data.lookupId && data.isLookup === true) {
      let sUrl = "/WorkUtilsServices/v1/lapi/data?lookupId=" + data.lookupId;

      fetchWrapper(sUrl, { headers: { Authorization: this.props.authorization } }, this.destinations)
        .then((res) => res.json())
        .then((result) => {
          let dynamicColumn, row;
          console.log(result);
          if (result.data.metaData) {
            dynamicColumn = result.data.metadata;
            row = result.data.values;
          } else {
            row = result.data.values;
            dynamicColumn = [
              {
                id: null,
                columnName: "key",
                displayName: "Key",
                searchable: true,
                isDisplayName: false,
                mappedName: "key",
              },
              {
                id: "1",
                columnName: "value",
                displayName: "Text",
                searchable: false,
                isDisplayName: true,
                mappedName: "value",
              },
              {
                id: "2",
                columnName: "additionalValue",
                displayName: "Additional Text",
                searchable: false,
                isDisplayName: false,
                mappedName: "additionalValue",
              },
            ];
          }
          this.setState({
            ...this.state,
            dynamicColumn: dynamicColumn,
            rows: row ? row : [],
          });
        });
    }
  };
  sheetsInputs = (evt, index) => {
    let value, name;
    name = evt.target.name;
    value = evt.target.value;
    let sheets = [...this.state.sheets];
    if (evt.target.name === "sheetIndex") {
      if (parseInt(value)) {
        name = "sheetIndex";
        sheets[index].sheetName = null;
      } else {
        name = "sheetName";
        sheets[index].sheetIndex = null;
      }
    }
    sheets[index][name] = value;
    this.setState({
      ...this.state,
      sheets: sheets,
    });
  };
  deleteSheet = (evt, index) => {
    let sheets = [...this.state.sheets];

    sheets.splice(index, 1);
    this.setState({
      ...this.state,
      sheets: sheets,
    });
  };
  deleteComposite = (row, index) => {
    let compositeArray = [...this.state.compositeArray];
    compositeArray.splice(index, 1);
    this.setState({
      ...this.state,
      compositeArray: compositeArray,
    });
  };
  addSheets = (event) => {
    var sheet = this.state.sheets;
    let obj = {
      sheetName: null,
      sheetIndex: null,
      rowStart: null,
      columnStart: null,
    };
    sheet.push(obj);
    this.setState({
      ...this.state,

      sheets: sheet,
    });
  };
  fnOpenDataElement = () => {
    this.setState({
      ...this.state,
      showDataElement: true,
    });
  };
  addComposite = () => {
    var compositeArray = this.state.compositeArray;
    let obj = {
      key: null,
      value: null,
      lookupType: null,
      referenceId: null,
    };
    compositeArray.push(obj);
    this.setState({
      ...this.state,

      compositeArray: compositeArray,
    });
  };
  fnAttrValueHelp = (index, evt, table) => {
    console.log(index, table);
    this.setState({
      ...this.state,
      showVHTableDialog: true,
      index: index,
      tableType: table,
    });
  };
  compositeValueHelp = (index) => {
    this.setState({
      ...this.state,
      showValueHelpDynamicDialog: true,
      compositeArrayIndex: index,
    });
  };
  onLookupType = (evt, keyProps, index) => {
    let arr = [...this.state.compositeArray];

    arr[index].lookupType = keyProps.props.value;

    this.setState({
      ...this.state,
      lookupTypeC: false,
      compositeArray: arr,
    });
  };
  handleExcelImport = ($event) => {
    const files = $event.target.files;
    if (files.length) {
      const file = files[0];
      const reader = new FileReader();
      reader.onload = (event) => {
        const wb = read(event.target.result);
        const sheets = wb.SheetNames;

        if (sheets.length) {
          const rows = utils.sheet_to_json(wb.Sheets[sheets[0]]);
          const valueHelpData = rows.map((ele) => ({
            key: ele.Key,
            value: ele.Value,
            additionalValue: ele["Additional Value"],
          }));
          this.setState({
            ...this.state,
            valueList: valueHelpData,
          });
        }
      };
      reader.readAsArrayBuffer(file);
      $event.target.files = null;
      $event.target.value = null;
    }
  };

  handleExcelExport = () => {
    const headings = [["Key", "Value", "Additional Value"]];
    const wb = utils.book_new();
    const ws = utils.json_to_sheet([]);
    utils.sheet_add_aoa(ws, headings);
    let excelData = this.state?.valueList?.map(ele => {
      let {id , ...data} = ele;
      return data;
    })
    utils.sheet_add_json(ws, excelData, { origin: "A2", skipHeader: true });
    utils.book_append_sheet(wb, ws, "value_help");
    writeFile(wb, `${this.props.row.name}.xlsx`);
  };

  render() {
    const userAccess = this.props.userAccess;
    const CreateFieldCatalog = userAccess.activities.filter((ele) => ele === "Create Field Catalog") ? true : false;

    return (
      <div style={{ height: "100%", width: "100%", overflow: "auto" }}>
        <Stack height="100%" width="100%" justifyContent="space-between">
          <div>
            {this.state.tableVisible && (
              <div>
                <span className="iconBtn" style={{ float: "right", margin: "1rem" }}>
                  <Button
                    size="small"
                    variant="contained"
                    color="primary"
                    disabled={!CreateFieldCatalog}
                    style={{ marginLeft: 10, padding: 2 }}
                    onClick={this.createNewField}
                    //className={classes.buttonAdd}
                  >
                    Create
                  </Button>
                </span>
                <span style={{ float: "left", margin: "1rem" }}>
                  {this.props.applicationName ? (
                    <FormControl style={{ width: 220 }} variant="outlined" size="small">
                      <InputLabel id="demo-simple-select-outlined-label">Application</InputLabel>
                      <Select
                        labelId="demo-simple-select-outlined-label"
                        id="demo-simple-select-outlined"
                        label="Application"
                        value={this.state.selectedApplication}
                        // size="small"
                        name="selectedApplication"
                        style={{ width: "100%", height: "2rem" }}
                        disabled
                      >
                        {this.state.applications.map((option) => (
                          <MenuItem value={option.applicationId} key={option.applicationId}>
                            {option.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  ) : (
                    <FormControl style={{ width: 220 }} variant="outlined" size="small">
                      <InputLabel id="demo-simple-select-outlined-label">Application</InputLabel>
                      <Select
                        labelId="demo-simple-select-outlined-label"
                        id="demo-simple-select-outlined"
                        label="Application"
                        value={this.state.selectedApplication}
                        // size="small"
                        name="selectedApplication"
                        style={{ width: "100%", height: "2rem" }}
                        onChange={(evt, keyProp) => this.applicationChangeHandler(evt, keyProp)}
                      >
                        {this.state.applications.map((option) => (
                          <MenuItem value={option.applicationId} key={option.applicationId}>
                            {option.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                </span>

                <TableContainer component={Paper} style={{ margin: "0.5rem", width: "99%" }}>
                  <Table size="small" aria-label="a dense table">
                    <TableHead>
                      <TableRow>
                        <TableCell width="10%" style={{ fontWeight: 700 }}>
                          Name
                        </TableCell>
                        <TableCell width="10%" style={{ fontWeight: 700 }}>
                          Label
                        </TableCell>
                        <TableCell width="10%" style={{ fontWeight: 700 }}>
                          Description
                        </TableCell>
                        <TableCell width="10%" style={{ fontWeight: 700 }}>
                          Data Element
                        </TableCell>
                        <TableCell width="10%" style={{ fontWeight: 700 }}></TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {this.state.fieldCatalogArray.map((row) => {
                        return (
                          <TableRow key={row.id}>
                            {/* <TableCell >{row.application}</TableCell> */}

                            <TableCell>{row.name}</TableCell>
                            <TableCell>{row.label}</TableCell>
                            <TableCell>{row.description}</TableCell>

                            {/* <TableCell >{row.permissionType}</TableCell> */}
                            <TableCell>{row.dataElementName}</TableCell>
                            <TableCell>
                              {/* <Tooltip title= 'Edit'> */}
                              <IconButton aria-label="Edit" style={{ color: "green" }} onClick={() => this.editFields(row)}>
                                <ArrowForwardIosIcon />
                              </IconButton>
                              {/* </Tooltip> */}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
              </div>
            )}
            {!this.state.tableVisible && (
              <Box>
                <div style={{ display: "grid", background: "#F1F5FE", fontSize: "0.875rem" }}>
                  <div style={{ marginBottom: "2rem", padding: "0.5rem", background: "white", borderRadius: "0.5rem", height: "37vh" }}>
                    <div style={{ display: "flex", justifyContent: "flex-start", alignItems: "center", marginBottom: "0.5rem" }}>
                      <IconButton onClick={() => this.props.Back()} style={{ width: "1rem", height: "1rem" }}>
                        {/* <ArrowBackIosIcon fontSize='small' /> */}
                        <ArrowBackIosIcon style={{ width: "1rem", height: "1rem" }} />
                      </IconButton>

                      {this.props.existing ? <p style={{ margin: 0 }}>Edit Global Field Catalog</p> : <p style={{ margin: 0 }}>Create Global Field Catalog</p>}
                    </div>
                    <Stack>
                      <Stack mb={1} direction="row " sx={{ width: "100%" }} spacing={3} justifyContent="center" alignItems="center">
                        <Stack style={{ width: "20%" }}>
                          <InputLabel id="demo-simple-select-outlined-label" style={{ marginBottom: "0.25rem", fontSize: "0.875rem" }}>
                            Name<span style={{ color: "red" }}>*</span>
                          </InputLabel>
                          {/* <Input
                            required
                            id="filled-disabled" -- to change --> id="outlined-basic"
                            sx={{ border: "2px solid #d4d5d6", borderRadius: "5px", width: "80%", background: this.props.editLookUpData ? "#B2B2B212" : "" }} --- to change --> sx={{ width: "80%" }}
                            name="name"
                            value={this.state.selectedRow.name}
                            variant="outlined"
                            disabled={this.props.editLookUpData}
                            size="small"
                            onChange={(evt) => this.inputChangeHandler(evt)}
                            inputProps={{ maxLength: 30 }}
                            className="customInputpackage" --- to change --> className="styleDTname"
                          /> */}
                          <TextField className="styleDTname" variant="outlined" id="outlined-basic" required disabled={this.props.editLookUpData} inputProps={{ maxLength: 30 }} onChange={(evt) => this.inputChangeHandler(evt)} value={this.state.selectedRow.name} name="name" size="small" sx={{ padding: "0", width: "80%" }} />
                        </Stack>

                        <Stack style={{ width: "20%" }}>
                          <InputLabel id="demo-simple-select-outlined-label" style={{ marginBottom: "0.25rem", fontSize: "0.875rem" }}>
                            Label<span style={{ color: "red" }}>*</span>
                          </InputLabel>
                          <TextField
                            className="styleDTname"
                            required
                            id="outlined-basic"
                            name="label"
                            value={this.state.selectedRow.label}
                            variant="outlined"
                            disabled={this.props.editLookUpData}
                            sx={{ width: "80%" }}
                            size="small"
                            onChange={(evt) => this.inputChangeHandler(evt)}
                            inputProps={{ maxLength: 30 }}
                          />
                        </Stack>
                        <Stack style={{ width: "20%" }}>
                          <InputLabel id="demo-simple-select-outlined-label" style={{ marginBottom: "0.25rem", fontSize: "0.875rem" }}>
                            Description<span style={{ color: "red" }}>*</span>
                          </InputLabel>
                          <TextField
                            required
                            className="styleDTname"
                            id="outlined-basic"
                            sx={{ width: "80%" }}
                            name="description"
                            value={this.state.selectedRow.description}
                            variant="outlined"
                            disabled={this.props.editLookUpData}
                            size="small"
                            onChange={(evt) => this.inputChangeHandler(evt)}
                            inputProps={{ maxLength: 30 }}
                          />
                        </Stack>

                        <Stack style={{ width: "20%" }}>
                          <InputLabel id="demo-simple-select-outlined-label" style={{ marginBottom: "0.25rem", fontSize: "0.875rem" }}>
                            Data Element<span style={{ color: "red" }}>*</span>
                          </InputLabel>
                          <Stack direction="row" spacing={1} alignItems="center">
                            <TextField
                              id="outlined-basic"
                              className="styleDTname"
                              size="small"
                              sx={{ width: "80%" }}
                              value={this.state.selectedRow.dataElementName}
                              disabled={this.props.editLookUpData}
                              variant="outlined"
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="end">
                                    <IconButton
                                      disabled={!CreateFieldCatalog || this.props.editLookUpData}
                                      onClick={this.onValueHelpTemp}
                                      // onMouseDown={handleMouseDownPassword}
                                      edge="end"
                                    >
                                      <SvgIcon>
                                        <path d="M17.391,2.406H7.266c-0.232,0-0.422,0.19-0.422,0.422v3.797H3.047c-0.232,0-0.422,0.19-0.422,0.422v10.125c0,0.232,0.19,0.422,0.422,0.422h10.125c0.231,0,0.422-0.189,0.422-0.422v-3.797h3.797c0.232,0,0.422-0.19,0.422-0.422V2.828C17.812,2.596,17.623,2.406,17.391,2.406 M12.749,16.75h-9.28V7.469h3.375v5.484c0,0.231,0.19,0.422,0.422,0.422h5.483V16.75zM16.969,12.531H7.688V3.25h9.281V12.531z"></path>
                                      </SvgIcon>
                                    </IconButton>
                                  </InputAdornment>
                                )
                              }}
                              // endAdornment={
                              //   <InputAdornment position="end">
                              //     <IconButton
                              //       disabled={!CreateFieldCatalog || this.props.editLookUpData}
                              //       onClick={this.onValueHelpTemp}
                              //       // onMouseDown={handleMouseDownPassword}
                              //       edge="end"
                              //     >
                              //       <SvgIcon>
                              //         <path d="M17.391,2.406H7.266c-0.232,0-0.422,0.19-0.422,0.422v3.797H3.047c-0.232,0-0.422,0.19-0.422,0.422v10.125c0,0.232,0.19,0.422,0.422,0.422h10.125c0.231,0,0.422-0.189,0.422-0.422v-3.797h3.797c0.232,0,0.422-0.19,0.422-0.422V2.828C17.812,2.596,17.623,2.406,17.391,2.406 M12.749,16.75h-9.28V7.469h3.375v5.484c0,0.231,0.19,0.422,0.422,0.422h5.483V16.75zM16.969,12.531H7.688V3.25h9.281V12.531z"></path>
                              //       </SvgIcon>
                              //     </IconButton>
                              //   </InputAdornment>
                              // }
                            />
                            {/* <IconButton
                              disabled={!CreateFieldCatalog}
                              onClick={this.onDataElementDialog}
                              // onMouseDown={handleMouseDownPassword}
                              size="small"
                            >
                              <InfoSharp fontSize="small" />
                            </IconButton> */}
                          </Stack>
                        </Stack>

                        <Stack style={{ width: "20%" }}>
                          <FormControl>
                            <FormLabel id="demo-radio-buttons-group-label" style={{ fontSize: "0.875rem" }}>
                              Is Field Catalogue Lookup?
                            </FormLabel>
                            <RadioGroup aria-labelledby="demo-radio-buttons-group-label" value={this.state.selectedRow?.metaData?.isLookup ? "Yes" : "No"} disabled={!CreateFieldCatalog} onChange={(evt) => this.onValueHelpSwitch(evt)} name="radio-buttons-group">
                              <Stack direction="row">
                                <FormControlLabel className="styleWURadioButtonText" disabled={this.props.editLookUpData} value="Yes" control={<Radio className="styleWURadioButton" />} label="Yes" />
                                <FormControlLabel className="styleWURadioButtonText" disabled={this.props.editLookUpData} value="No" control={<Radio className="styleWURadioButton" />} label="No" />
                              </Stack>
                            </RadioGroup>
                          </FormControl>
                        </Stack>
                        {/* </Paper> */}
                      </Stack>

                      <Stack direction="row " spacing={2}>
                        <Stack style={{ width: "20%" }}>
                          <InputLabel id="demo-simple-select-outlined-label" style={{ marginBottom: "0.25rem", fontSize: "0.875rem" }}>
                            Default Value
                          </InputLabel>
                          <TextField
                            id="outlined-basic"
                            name="defaultValue"
                            value={this.state.selectedRow?.metaData?.defaultValue}
                            variant="outlined"
                            sx={{ width: "80%" }}
                            size="small"
                            onChange={(evt) => this.handleChange(evt)}
                            disabled={this.props.editLookUpData}
                            className="customInputpackage styleDTname"
                          />
                        </Stack>
                        <Stack style={{ width: "20%" }}>
                          <InputLabel id="demo-simple-select-outlined-label" style={{ marginBottom: "0.25rem", fontSize: "0.875rem" }}>
                            Text Translation
                          </InputLabel>
                          <FormControl component="fieldset" style={{ width: "80%" }}>
                            <FormGroup aria-label="position" row>
                              <FormControlLabel size="small" disabled={this.props.editLookUpData} checked={this.state.selectedRow.workText} name="active" onChange={(evt) => this.workTextSwitch(evt.target.checked)} control={<Switch color="primary" />} />
                            </FormGroup>
                          </FormControl>
                        </Stack>
                        <Stack style={{ width: "20%" }}>
                          <InputLabel id="demo-simple-select-outlined-label" style={{ marginBottom: "0.25rem", fontSize: "0.875rem" }}>
                            Permission
                          </InputLabel>
                          <RadioGroup aria-labelledby="demo-radio-buttons-group-label" value={this.state.permission ? "Write" : "Read"} disabled={!CreateFieldCatalog} onChange={(evt) => this.onPermissionSwitch(evt)} name="radio-buttons-group">
                            <Stack direction="row">
                              <FormControlLabel className="styleWURadioButtonText" disabled={this.props.editLookUpData} value="Write" control={<Radio className="styleWURadioButton" />} label="Write" />
                              <FormControlLabel className="styleWURadioButtonText" disabled={this.props.editLookUpData} value="Read" control={<Radio className="styleWURadioButton" />} label="Read" />
                            </Stack>
                          </RadioGroup>
                        </Stack>
                      </Stack>
                      <Stack direction="row" spacing={2}>
                        <FormControl component="fieldset" variant="standard">
                          <FormLabel component="legend" style={{ fontSize: "0.875rem", marginBottom: "0.5rem" }}>
                            Properties
                          </FormLabel>
                          <FormGroup row={true}>
                            <Chip
                              label="Editable"
                              sx={this.state.selectedRow.metaData.isEditable ? { marginRight: "0.5rem", backgroundColor: "#F1F5FE", color: "#00518D", height: "1.5rem", padding: "0.3rem" } : { marginRight: "0.5rem", backgroundColor: "#FAFAFA", color: "#757575", height: "1.5rem", padding: "0.3rem" }}
                              name="isEditable"
                              onClick={(evt) => this.handleChange(evt, "isEditable")}
                              clickable={!this.props.editLookUpData}
                            />
                            <Chip
                              label="Visible"
                              sx={this.state.selectedRow.metaData.isVisible ? { marginRight: "0.5rem", backgroundColor: "#F1F5FE", color: "#00518D", height: "1.5rem", padding: "0.3rem" } : { marginRight: "0.5rem", backgroundColor: "#FAFAFA", color: "#757575", height: "1.5rem", padding: "0.3rem" }}
                              name="isVisible"
                              onClick={(evt) => this.handleChange(evt, "isVisible")}
                              clickable={!this.props.editLookUpData}
                            />
                            <Chip
                              label="Mandatory"
                              sx={this.state.selectedRow.metaData.isMandatory ? { marginRight: "0.5rem", backgroundColor: "#F1F5FE", color: "#00518D", height: "1.5rem", padding: "0.3rem" } : { marginRight: "0.5rem", backgroundColor: "#FAFAFA", color: "#757575", height: "1.5rem", padding: "0.3rem" }}
                              name="isMandatory"
                              onClick={(evt) => this.handleChange(evt, "isMandatory")}
                              clickable={!this.props.editLookUpData}
                            />
                            <Chip
                              label="Filterable"
                              sx={this.state.selectedRow.metaData.isFilterable ? { marginRight: "0.5rem", backgroundColor: "#F1F5FE", color: "#00518D", height: "1.5rem", padding: "0.3rem" } : { marginRight: "0.5rem", backgroundColor: "#FAFAFA", color: "#757575", height: "1.5rem", padding: "0.3rem" }}
                              name="isFilterable"
                              onClick={(evt) => this.handleChange(evt, "isFilterable")}
                              clickable={!this.props.editLookUpData}
                            />
                            <Chip
                              label="Sortable"
                              sx={this.state.selectedRow.metaData.isSortable ? { marginRight: "0.5rem", backgroundColor: "#F1F5FE", color: "#00518D", height: "1.5rem", padding: "0.3rem" } : { marginRight: "0.5rem", backgroundColor: "#FAFAFA", color: "#757575", height: "1.5rem", padding: "0.3rem" }}
                              name="isSortable"
                              onClick={(evt) => this.handleChange(evt, "isSortable")}
                              clickable={!this.props.editLookUpData}
                            />
                            <Chip
                              label="Nullable"
                              sx={this.state.selectedRow.metaData.isNullable ? { marginRight: "0.5rem", backgroundColor: "#F1F5FE", color: "#00518D", height: "1.5rem", padding: "0.3rem" } : { marginRight: "0.5rem", backgroundColor: "#FAFAFA", color: "#757575", height: "1.5rem", padding: "0.3rem" }}
                              name="isNullable"
                              onClick={(evt) => this.handleChange(evt, "isNullable")}
                              clickable={!this.props.editLookUpData}
                            />
                          </FormGroup>
                        </FormControl>
                      </Stack>
                    </Stack>
                  </div>
                  {/* height was removed from this div */}
                  {(this.state.selectedRow?.metaData?.isLookup || this.state.selectedRow.workText) && (
                    <div style={{ background: "white", overflowY: "none", borderRadius: "0.5rem", marginTop: "-1rem", marginBottom: "-1rem" }}>
                      <Box sx={{ width: "100%", typography: "body1" }}>
                        <TabContext value={this.state.value} className="styleWTTab">
                          <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                            <TabList className="styleWTTab noCaps" onChange={this.handleChange} indicatorColor="primary" textColor="primary">
                              <Tab className="styleWTTab" label="Value Help" value="ValueHelp" disabled={!this.state.selectedRow?.metaData?.isLookup || this.props.editLookUpData} />
                              <Tab className="styleWTTab" label="Text Translation" value="WorkText" disabled={!this.state.selectedRow.workText || this.props.editLookUpData} />
                            </TabList>
                          </Box>
                          <div style={{ overflowX: "hidden", width: "100%", padding: "0px", marginLeft: "5px" , paddingBottom: "2.5rem",}}>
                            <TabPanel className="styleWTTab" value="ValueHelp" hidden={!this.state.selectedRow?.metaData?.isLookup} style={{ backgroundColor: "white" }}>
                              <ValueHelp
                                {...this.props}
                                handleExcelExport={this.handleExcelExport}
                                handleExcelImport={this.handleExcelImport}
                                dbInputChangeHandler={this.dbInputChangeHandler}
                                translation={this.state.translation}
                                mappedNameList={this.state.mappedNameList}
                                selectedRow={this.state.selectedRow}
                                ref={this.valuehelpref}
                                constraints={this.state.constraints}
                                fields={this.state.fields}
                                parentState={this.state}
                                handleChangeValueHelpType={this.handleChangeValueHelpType}
                                addNewConstraint={this.addNewConstraint}
                                addNewFields={this.addNewFields}
                                deleteFieldRows={(row, index) => this.deleteFieldRows(row, index)}
                                deleteConstraintRows={(row, index) => this.deleteConstraintRows(row, index)}
                                addNewStaticVL={this.addNewStaticVL}
                                deleteStaticVL={(row, index) => this.deleteStaticVL(row, index)}
                                inputChangeHandler1={(evt, index) => this.inputChangeHandler1(evt, index)}
                                inputChangeHandler2={(evt, index) => this.inputChangeHandler2(evt, index)}
                                valueHelpTableInputs={(evt, index, source) => this.valueHelpTableInputs(evt, index, source)}
                                addSheets={this.addSheets}
                                valueHelpTableMappedNameInput={(evt, index, val) => this.valueHelpTableMappedNameInput(evt, index, val)}
                                deleteSheet={(row, index) => this.deleteSheet(row, index)}
                                sheetsInputs={(evt, index) => this.sheetsInputs(evt, index)}
                                rowSelected={(row, index) => this.rowSelectedValueHelp(row, index)}
                                rowSelectedFields={(row, index) => this.rowSelectedValueHelpFields(row, index)}
                                fnAttrValueHelp={(index, evt, table) => this.fnAttrValueHelp(index, evt, table)}
                                compositeValueHelp={(index) => this.compositeValueHelp(index)}
                                closeDialog={this.closeDialog}
                                addComposite={this.addComposite}
                                onLookupType={(row, props, index) => this.onLookupType(row, props, index)}
                                deleteComposite={(row, index) => this.deleteComposite(row, index)}
                                getData={(payload, index) => this.getData(payload, index)}
                                destinations={this.destinations}
                                editLookUpData={this.props.editLookUpData}
                              />
                            </TabPanel>
                            {this.state.selectedRow.workText && (
                              <div>
                                <TabPanel className="styleWTTab" value="WorkText" hidden={!this.state.selectedRow.workText} sx={{ overflowY: "scroll", maxHeight: "18rem" }}>
                                  <CwWorkText userAccess={this.props.userAccess} showPropList={false} propertyName={this.state.selectedRow.name} appName={this.props.appName} userDetails={this.props.userDetails} destinations={this.destinations} />
                                </TabPanel>
                              </div>
                            )}
                          </div>
                        </TabContext>
                      </Box>
                    </div>
                  )}
                </div>
              </Box>
            )}
          </div>
          <div>
            <MessageBoxComponentSave title="save" open={this.state.showDialogSave} actions={["SAVE", "CANCEL"]} onClose={this.closeDialog} />
            <MessageBoxComponentDiscard title="discard" open={this.state.showDialogDiscard} actions={["DISCARD", "CANCEL"]} onClose={this.props.Back} />
            {this.state.showValueHelpDialog && <ValueHelpDialog getDataElementsList={this.getDataElementsList} {...this.props} tempDataEle={this.state.dataElement} title="Value Help" fnOpenDataElement={this.fnOpenDataElement} open={this.state.showValueHelpDialog} actions={["DISCARD", "CANCEL"]} onClose={this.closeDialog} dataElement={this.state.dataElement} rowSelected={(row) => this.rowSelected(row)} destinations={this.destinations} />}
            <ValueHelpDynamicDialog title="Value Help" onValueHelp={this.onValueHelp} open={this.state.showValueHelpDynamicDialog} actions={["DISCARD", "CANCEL"]} onClose={this.closeDialog} dataElement={this.state.rows} rowSelected={(row, i) => this.rowSelectedDynamic(row, i)} dynamicColumn={this.state.dynamicColumn} />
            <MessageStrip open={this.state.snackbarOpen} message={this.state.snackbarMessg} severity={this.state.snackbarSeverity} onClose={() => this.setState({ ...this.state, snackbarOpen: false, snackbarMessg: "" })} />
            {/* <Snackbar open={this.state.snackbarOpen} autoHideDuration={3000} onClose={() => this.setState({ ...this.state, snackbarOpen: false })} message={this.state.snackbarMessg} anchorOrigin={{ vertical: "bottom", horizontal: "center" }} /> */}
            <DataElementDialog title="Data Element" onValueHelp={this.onValueHelp} open={this.state.showDataElementDialog} actions={["DISCARD", "CANCEL"]} onClose={this.closeDialog} rowData={this.state.dataElementRow} destinations={this.destinations} getDataElementsList={this.getDataElementsList} rowSelected={(row) => this.rowSelected(row)} />
            <div style={{ width: "80%" }}>
              <DataElementDetails getDataElementsList={this.getDataElementsList} rowSelected={(row) => this.rowSelected(row)} onValueHelp={this.onValueHelp} translation={this.state.translation} onClose={this.closeDataEleDialog} DAopen={this.state.showDataElement} destinations={this.destinations} />
            </div>
            {!this.state.tableVisible && (
              <div className="footerdiv" style={{ display: "flex", flexDirection: "row-reverse", boxShadow: "0rem 0.438rem 0.5rem -0.25rem rgba(0, 0, 0, 0.2), 0rem 0.75rem 1.063rem 0.125rem rgba(0, 0, 0, 0.14), 0rem 0.313rem 1.375rem 0.25rem rgba(0, 0, 0, 0.12)", bottom: 0, right: "0", height: "2.5rem", width: "100%", marginLeft: "auto", backgroundColor: "white", position: "relative", alignItems: "center" }}>
                <Button variant="contained" className="styleWTPrimaryContainedButton" style={{ textTransform: "none", height: "1.8rem", marginRight: "1rem" }} size="small" onClick={this.onSaveDataElement} disabled={!CreateFieldCatalog}>
                  Save
                </Button>
                <Button style={{ margin: "0 0.5rem", textTransform: "none", height: "1.8rem", marginRight: "rem" }} variant="outlined" color="primary" size="small" onClick={this.onCancel}>
                  Cancel
                </Button>
              </div>
            )}
          </div>
        </Stack>
      </div>
    );
  }
}

export default CwMSFieldsDetails;
