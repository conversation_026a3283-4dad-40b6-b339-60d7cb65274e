import PropTypes from 'prop-types';
import Task from './Task';

const Level = ({ label, tasks, onTaskClick }) => (
  <div
    style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'stretch',
      gap: 0,
      padding: 12,
    }}
  >
    <div style={{ marginBottom: 8 }}>
      <span className='level-label' style={{ fontWeight: 600, fontSize: 16, display: 'block', textAlign: 'center',border: `${label == "Requestor" && "2px solid #90EE90"}`, }}>{label}</span>
    </div>
    <div style={{ display: 'flex', flexDirection: 'column', gap: 8, position: 'relative' }}>
      {tasks.map((task) => (
        <Task key={task.id} id={task.id} task={task} onClick={() => onTaskClick(task)} />
      ))}
    </div>
  </div>
);

Level.propTypes = {
  label: PropTypes.string.isRequired,
  tasks: PropTypes.arrayOf(PropTypes.object).isRequired,
  onTaskClick: PropTypes.func.isRequired,
};

export default Level;
