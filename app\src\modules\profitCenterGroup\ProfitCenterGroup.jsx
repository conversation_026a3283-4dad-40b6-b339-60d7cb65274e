import {
  BottomNavigation,
  Button,
  FormControl,
  Grid,
  ButtonGroup,
  Paper,
  Stack,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
} from "@mui/material";
import ClearIcon from "@mui/icons-material/Clear";
import SearchIcon from "@mui/icons-material/Search";
import { colors } from "@constant/colors";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";

import FilterListIcon from "@mui/icons-material/FilterList";

import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

import { styled } from "@mui/material/styles";
import useGenericDtCall from "@hooks/useGenericDtCall";
import React, { useEffect, useRef, useState } from "react";

import { useSelector, useDispatch } from "react-redux";

import {
  font_Small,
  button_Primary,
  container_Padding,
  button_Marginleft,
  outermostContainer,
  container_filter,
  outermostContainer_Information,
} from "../../components/Common/commonStyles";
import { useNavigate } from "react-router-dom";

import { setDropDown } from "../../app/dropDownDataSlice";
import { doAjax } from "../../components/Common/fetchService";
import {
  destination_IDM,
  destination_ProfitCenter_Mass,
} from "../../destinationVariables";

import {
  clearHierarchyGroup,
  commonFilterClear,
  commonFilterUpdate,
} from "../../app/commonFilterSlice";
import ReusableHierarchyTree from "../../components/MasterDataCockpit/Hierarchy/ReusableHIerarchyTree";
import ReusablePreset from "../../components/Common/ReusablePresetFilter";
import ReusableDialog from "../../components/Common/ReusableDialog";
import { clearArtifactId } from "../../app/initialDataSlice";
import {
  clearGeneralLedger,
  clearSingleGLPayloadGI,
} from "../../app/generalLedgerTabSlice";
import { clearTaskData } from "../../app/userManagementSlice";
import ReusableBackDrop from "../../components/Common/ReusableBackDrop";
import { API_CODE, DECISION_TABLE_NAME, ERROR_MESSAGES, PAGESIZE, SEARCH_FIELD_TYPES, VISIBILITY_TYPE } from "@constant/enum";
import useLang from "@hooks/useLang";

const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  minHeight: "2rem !important",
  margin: "0px !important",
  backgroundColor: colors.primary.ultraLight,
  borderRadius: "8px 8px 0 0",
  transition: "all 0.2s ease-in-out",
  "&:hover": {
    backgroundColor: `${colors.primary.light}20`,
  },
}));

const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginTop: "0px !important",
  border: `1px solid ${colors.primary.border}`,
  borderRadius: "8px",
  boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
  "&:not(:last-child)": {
    borderBottom: 0,
  },
  "&:before": {
    display: "none",
  },
}));

const ButtonContainer = styled(Grid)({
  display: "flex",
  justifyContent: "flex-end",
  paddingRight: "0.75rem",
  paddingBottom: "0.75rem",
  paddingTop: "0rem",
  gap: "0.5rem",
});

const ActionButton = styled(Button)({
  borderRadius: "4px",
  padding: "4px 12px",
  textTransform: "none",
  fontSize: "0.875rem",
});

const LabelTypography = styled(Typography)({
  fontSize: "0.75rem",
  color: colors.primary.dark,
  marginBottom: "0.25rem",
  fontWeight: 500,
});

const ProfitCenterGroup = () => {
  let ref_elementForExport = useRef(null);
  const { t } = useLang();

  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [isPresetActive, setIsPresetActive] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [controllingArea, setControllingArea] = useState({
    code: "ETCA",
    desc: "ET FAMILY CO AREA",
  });
  const [profitCenterGroup, setProfitCenterGroup] = useState(null);
  const [profitCenterGroupDescription, setProfitCenterGroupDescription] =
    useState(null);
  const anchorRef = React.useRef(null);


  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const [searchParameters, setSearchParameters] = useState([]);
  const { getDtCall:getSearchParams, dtData:dtSearchParamsResponse } = useGenericDtCall(); 
  const [items,setItem] = useState(); 
  const [initialNodeData, setInitialNodeData] = useState([
    {
      id: "1",
      title: "",
      child: [],
      tags: [],
      description: "",
    },
  ]);
  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["Material"]
  );

  const [isAccordionExpanded, setIsAccordionExpanded] = useState(
    initialNodeData.length === 0 || !initialNodeData[0]?.title // condition to check if no data
  );

  const applicationConfig = useSelector((state) => state.applicationConfig);

  const getFilter = () => {
    setIsAccordionExpanded(false);
    // debugger
    setIsLoading(true);
    var payload = {
      node:
        pcSearchForm?.profitCenterGroup?.code === ""
          ? ""
          : pcSearchForm?.profitCenterGroup?.code,
      controllingArea:
        pcSearchForm?.controllingArea?.code === ""
          ? ""
          : pcSearchForm?.controllingArea?.code,
      classValue: "0106",
      id: "",
      screenName: "Display",
    };
    const hSuccess = (data) => {
      console.log(data.body, "data");
      let innerData = [];
      if (data.statusCode === 200) {
        innerData.push(data.body.HierarchyTree);
        setInitialNodeData(innerData);
      }
      setInitialNodeData(innerData);
      setIsLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/displayHierarchyTreeNodeStructure`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const dropDownData = useSelector((state) => state?.AllDropDown?.dropDown);
  const pcSearchForm = useSelector(
    (state) => state.commonFilter["HierarchyNodeProfitCenter"]
  );
  console.log("pcSearchForm", pcSearchForm);

  // For Search filter
  const handleControllingAreaSearch = (value) => {
    var tempControllingArea = value;

    let tempFilterData = {
      ...pcSearchForm,
      controllingArea: tempControllingArea,
    };
    dispatch(
      commonFilterUpdate({
        module: "HierarchyNodeProfitCenter",
        filterData: tempFilterData,
      })
    );

    getProfitCenterGroupFilter(value.code);
  };
  const handleProfitCenterGroupSearch = (value) => {
    console.log("value", value);

    var tempProfitCenterGroup = value;
    console.log("tempProfitCenterGroup", tempProfitCenterGroup);
    let tempFilterData = {
      ...pcSearchForm,
      profitCenterGroup: tempProfitCenterGroup,
    };
    dispatch(
      commonFilterUpdate({
        module: "HierarchyNodeProfitCenter",
        filterData: tempFilterData,
      })
    );
  };

  const getControllingArea = (value) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "COAREA",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getControllingArea`,
      "get",
      hSuccess,
      hError
    );
  };

  const getNewControllingArea = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_CUSTOM_DROPDOWN_LIST",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MODULE": "PCG",
          "MDG_CONDITIONS.MDG_FIELD_NAME": "Controlling Area",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        const lookupData =
          data?.data?.result[0]?.MDG_CUSTOM_LOOKUP_ACTION_TYPE || [];
        console.log("questionData", lookupData);

        let lookupDataArr = [];
        lookupData?.map((itemData) => {
          let lookupDataHash = {};
          lookupDataHash["code"] = itemData?.MDG_LOOKUP_CODE;
          lookupDataHash["desc"] = itemData?.MDG_LOOKUP_DESC;
          lookupDataArr.push(lookupDataHash);
        });
        console.log(lookupDataArr, "lookupDataArr");

        dispatch(
          setDropDown({ keyName: "NewControllingArea", data: lookupDataArr })
        );
        // setQuestions(questionsData);
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  
  const getProfitCenterGroup = (value) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "PRCTRGroup",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/node/getZeroLevelNodes?controllingArea=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getProfitCenterGroupFilter = (value) => {
    console.log("first", value);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "PRCTRGroupFilter",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCtrGroup?controllingArea=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleClear = () => {
    dispatch(commonFilterClear({ module: "HierarchyNodeProfitCenter" }));
  };

  useEffect(() => {
    getControllingArea();
    getProfitCenterGroup("ETCA");
    getProfitCenterGroupFilter("ETCA");
    getNewControllingArea();
    dispatch(clearArtifactId());
    dispatch(clearGeneralLedger());
    dispatch(clearSingleGLPayloadGI());
    dispatch(clearTaskData());
    dispatch(
      clearHierarchyGroup({
        module: "HierarchyNodeProfitCenter",
        groupName: "profitCenterGroup",
      })
    );
  }, []);

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  useEffect(() => {
    setControllingArea({ code: "ETCA", desc: "ET FAMILY CO AREA" });
    setProfitCenterGroup(null);
    setProfitCenterGroupDescription(null);
  }, []);
  const fetchSearchParameterFromDt = () => {
    let payload = {
          decisionTableId: null,
          decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_PARAMETER,
          version: "v1",
          conditions: [
            {
              "MDG_CONDITIONS.MDG_MAT_REGION":"US",
              "MDG_CONDITIONS.MDG_MODULE":"Profit Center Group",
              "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data",
            },
          ],
        };
        getSearchParams(payload);
  }
    useEffect(() => {
      if(dtSearchParamsResponse){
          const response = dtSearchParamsResponse?.result?.[0]?.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE;
          const additionalData = response?.filter((item) => {
            return item.MDG_MAT_FILTER_TYPE === "Additional";
          }).map((item) => {
            return { title: t(item.MDG_MAT_UI_FIELD_NAME) };
          });
          setSearchParameters(response);
          setItem(additionalData);
        }
    }, [dtSearchParamsResponse]);
     useEffect(() => {
        fetchSearchParameterFromDt();
      }, []);

  return (
    <div ref={ref_elementForExport}>
      <ReusableDialog
        dialogState={openMessageDialog}
        openReusableDialog={handleMessageDialogClickOpen}
        closeReusableDialog={handleMessageDialogClose}
        dialogTitle={messageDialogTitle}
        dialogMessage={messageDialogMessage}
        handleDialogConfirm={handleMessageDialogClose}
        dialogOkText={"OK"}
        // handleExtraButton={handleMessageDialogNavigate}
        dialogSeverity={messageDialogSeverity}
      />

      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />

      <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1}>
          <Grid container mt={0} sx={outermostContainer_Information}>
            <Grid item md={5}>
              <Typography variant="h3">
                <strong>{t("Profit Center Group")}</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                {t("This view displays the selected Profit Center Hierarchy")}
              </Typography>
            </Grid>
          </Grid>

          <Grid container sx={container_filter}>
            <Grid item md={12}>
              <StyledAccordion defaultExpanded={true}>
                <StyledAccordionSummary
                  expandIcon={
                    <ExpandMoreIcon
                      sx={{ fontSize: "1.25rem", color: colors.primary.main }}
                    />
                  }
                  aria-controls="panel1a-content"
                  id="panel1a-header"
                >
                  <FilterListIcon
                    sx={{
                      fontSize: "1.25rem",
                      marginRight: 1,
                      color: colors.primary.main,
                    }}
                  />
                  <Typography
                    sx={{
                      fontSize: "0.875rem",
                      fontWeight: 600,
                      color: colors.primary.dark,
                    }}
                  >
                    {t("Search Profit Center Group")}
                  </Typography>
                </StyledAccordionSummary>
                <AccordionDetails sx={{ padding: "1rem 1rem 0.5rem" }}>
                  <Grid
                    container
                    rowSpacing={1}
                    spacing={2}
                    justifyContent="space-between"
                    alignItems="center"
                    // sx={{ marginBottom: "0.5rem" }}
                  >
                    <Grid
                      container
                      spacing={1}
                      sx={{ padding: "0rem 1rem 0.5rem" }}
                    >
                      {searchParameters?.filter(item => item.MDG_MAT_VISIBILITY !== "Hidden")
                        .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
                        .map((item, index) => {
                          return (
                            <React.Fragment key={index}>
                              {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.CONTROLINGAREA &&
                              <Grid item md={2}>
                                <LabelTypography sx={font_Small}>
                                  {t(item?.MDG_MAT_UI_FIELD_NAME)}{" "}
                                  <span style={{ color: colors?.error?.dark }}>*</span>
                                </LabelTypography>
                                <FormControl size="small" fullWidth>
                                  <SingleSelectDropdown
                                    options={dropDownData?.COAREA ?? []}
                                    value={pcSearchForm?.controllingArea}
                                    onChange={(value) => {
                                      handleControllingAreaSearch(value);
                                    }}
                                    placeholder={t("SELECT CONTROLLING AREA")}
                                    disabled={false}
                                    minWidth="90%"
                                    listWidth={210}
                                  />
                                </FormControl>
                              </Grid>}
                              {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.CCGROUP &&
                              <Grid item md={2}>
                                <LabelTypography sx={font_Small}>
                                  {t(item?.MDG_MAT_UI_FIELD_NAME)}{" "}
                                  <span style={{ color: colors?.error?.dark }}>*</span>
                                </LabelTypography>
                                <FormControl size="small" fullWidth>
                                  <SingleSelectDropdown
                                    options={dropDownData?.PRCTRGroupFilter ?? []}
                                    value={pcSearchForm?.profitCenterGroup}
                                    onChange={(newValue) => {
                                      handleProfitCenterGroupSearch(newValue);
                                    }}
                                    placeholder={t("SELECT PROFIT CENTER GROUP")}
                                    disabled={false}
                                    minWidth="90%"
                                    listWidth={210}
                                  />
                                </FormControl>
                              </Grid>}
                            </React.Fragment>
                          )

                        })
                      }
                    </Grid>
                  </Grid>
                  <ButtonContainer>
                    <ActionButton
                      variant="outlined"
                      size="small"
                      startIcon={<ClearIcon sx={{ fontSize: "1rem" }} />}
                      onClick={() => {
                        handleClear();
                      }}
                      // disabled={isPresetActive}
                      sx={{
                        borderColor: colors.primary.main,
                        color: colors.primary.main,
                      }}
                    >
                      {t("Clear")}
                    </ActionButton>

                    {/* MIGHT UNCOMMENT LATER */}
                    <Grid sx={{ ...button_Marginleft }}>
                      <ReusablePreset
                        moduleName={"MaterialMaster"}
                        handleSearch={getFilter}
                        // disabled={selectedRegion === "" || !selectedSalesOrg?.length || !selectedPlant?.length}
                        onPresetActiveChange={(isActive) =>
                          setIsPresetActive(isActive)
                        }
                        onClearPreset={handleClear}
                      />
                    </Grid>

                    <ActionButton
                      variant="contained"
                      size="small"
                      startIcon={<SearchIcon sx={{ fontSize: "1rem" }} />}
                      sx={{ ...button_Primary, ...button_Marginleft }}
                      // disabled={isPresetActive}
                      onClick={() => {
                        getFilter();
                      }}
                    >
                      {t("Search")}
                    </ActionButton>
                  </ButtonContainer>
                </AccordionDetails>
              </StyledAccordion>
            </Grid>
          </Grid>

          <>
            {initialNodeData.length > 0 && initialNodeData[0]?.label && (
              <Grid container>
                <Grid
                  item
                  md={12}
                  sx={{
                    backgroundColor: "white",
                    maxHeight: "max-content",
                    height: "max-content",
                    borderRadius: "8px",
                    border: "1px solid #E0E0E0",
                    mt: 0.25,
                    boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                    ...container_Padding,
                    // ...container_columnGap,
                  }}
                >
                  <Grid
                    container
                    display="flex"
                    flexDirection="row"
                    flexWrap="nowrap"
                  >
                    <Grid item md={12}>
                      {initialNodeData[0] ? (
                        <Grid
                          item
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                          }}
                        ></Grid>
                      ) : (
                        ""
                      )}

                      <Grid>
                        <Typography
                          variant="body1"
                          fontWeight="bold"
                          justifyContent="flex-start"
                          marginBottom={2}
                        >
                          {t("Existing Structure in SAP")}
                        </Typography>
                      </Grid>

                      <ReusableHierarchyTree
                        initialRawTreeData={initialNodeData}
                        editmode={false}
                        object="Profit Center"
                      />
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            )}
          </>

          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            {iwaAccessData?.length > 0 && (
              <Paper
                sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                elevation={2}
              >
                <BottomNavigation
                  className="container_BottomNav"
                  showLabels
                  sx={{
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: 1,
                  }}
                >
                  <Button
                    size="small"
                    variant="contained"
                    onClick={() => {
                      navigate("/requestBench/ProfitCenterGroupRequestTab");
                    }}
                  >
                    {t("Create Request")}
                  </Button>

                  <ButtonGroup
                    variant="contained"
                    ref={anchorRef}
                    aria-label="split button"
                  ></ButtonGroup>
                </BottomNavigation>
              </Paper>
            )}
          </Paper>
        </Stack>
      </div>
    </div>
  );
};

export default ProfitCenterGroup;
