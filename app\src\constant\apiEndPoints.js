import { destination_BankKey, destination_CostCenter_Mass, destination_ProfitCenter_Mass,destination_GeneralLedger_Mass } from "../destinationVariables";
import { MODULE, MODULE_KEY_MAP, MODULE_MAP, REQUEST_TYPE } from "./enum";
export const END_POINTS = {
  MASS_ACTION: {
    CREATE_MATERIAL_SEND_FOR_REVIEW: "/massAction/createMaterialSendForReview",
    EXTEND_MATERIAL_SEND_FOR_REVIEW: "/massAction/extendMaterialSendForReview",
    CHANGE_MATERIAL_SEND_FOR_REVIEW: "/massAction/changeMaterialSendForReview",
    CREATE_MATERIAL_SEND_FOR_CORRECTION:
      "/massAction/createMaterialSendForCorrection",
    CHANGE_MATERIAL_SEND_FOR_CORRECTION:
      "/massAction/changeMaterialSendForCorrection",
    VALIDATE_MATERIAL: "/massAction/validateMaterial",
    EXTEND_MATERIAL_SEND_FOR_CORRECTION:
      "/massAction/extendMaterialSendForCorrection",
    CREATE_MATERIAL_REJECTION: "/massAction/createMaterialRejection",
    CHANGE_MATERIAL_REJECTION: "/massAction/changeMaterialRejection",
    EXTEND_MATERIAL_REJECTION: "/massAction/extendMaterialRejection",
    MAT_NO_DUPLICATE_CHECK: "/massAction/fetchMaterialNosDupliChk",
    BANK_DUPLICATE_CHECK: "/massAction/fetchBankKeyNosDupliChk",
    MRP_DEFAULT_VALUES: "/data/getMrpBasedOnMrpProfile",
    EXTEND_MATERIAL_SAVE_AS_DRAFT: "/massAction/extendMaterialSaveAsDraft",
    EXTEND_MATERIAL_DIRECT_APPROVED: "/massAction/extendMaterialDirectApproved",
    VALIDATE_FINANCE_COSTING: "/massAction/validateFinanceData",
    AGGREGATE_DAILY_REQUESTS: "massAction/aggregateDailyRequests",
    FINANCE_COSTING_APPROVED: "massAction/financeCostingApproved",
    WORKFLOW_DETAILS_BIFURCATION_HIERARCHY: '/node/workflow-details/bifurcation',
    WORKFLOW_DETAILS_BIFURCATION: '/massAction/workflow-details/bifurcation',
    WORKFLOW_DETAILS_BIFURCATION_IO : '/api/v1/massAction/workflow-details/bifurcation',
    MATERIAL_SEND_TO_LEVEL: '/massAction/materialSendToLevel',
    CREATE_MAT_APPROVED: '/massAction/createMaterialApproved',
    EXTEND_MAT_APPROVED: '/massAction/extendMaterialApproved',
    CHANGE_MAT_APPROVED: '/massAction/changeMaterialApproved',
    CREATE_PC_APPROVED:"/massAction/createProfitCentersApproved",
    BOM_SYNDICATE:"/massAction/createBOMApproved",
    CREATE_CC_APPROVED:"/massAction/createCostCentersApproved",
    CHANGE_PC_APPROVED:"/massAction/changeProfitCentersApproved",
    CHANGE_CC_APPROVED:"/massAction/changeCostCentersApproved",
    CREATE_BOM_REQUEST: '/massAction/createRequestHeader',
    CREATE_IO_REQUEST: '/api/v1/massAction/createRequestHeader',
    VALIDATE_MASS_INTERNAL_ORDER: "/api/v1/massAction/validateInternalOrders",
    INTERNAL_ORDERS_SAVE_AS_DRAFT: "/api/v1/massAction/internalOrdersSaveAsDraft",
    INTERNAL_ORDERS_SUBMIT_FOR_REVIEW: "/api/v1/massAction/internalOrdersSubmitForReview",
    INTERNAL_ORDERS_SUBMIT_FOR_APPROVAL: "/api/v1/massAction/internalOrdersApprovalSubmit",
    CREATE_INTERNAL_ORDERS_APPROVED: "/api/v1/massAction/createInternalOrdersApproved",
    CREATE_BOM_SAVE_AS_DRAFT: '/massAction/createBOMSaveAsDraft',
    CREATE_BOM_SUBMIT_FOR_REVIEW: '/massAction/createBOMSubmitForReview',
    CREATE_BOM_SUBMIT_FOR_APPROVE: '/massAction/createBOMApprovalSubmit',
    VALIDATE_BOM: '/massAction/validateBOM',
    FETCH_BOM_NO_DUPLICATE_CHECK: '/massAction/fetchBOMNosDupliChk',
    UPLOAD_BOM_FILE :'/massAction/getAllBOMsFromExcel'
  },
  DATA: {
    GET_SEARCH_PARAMS_MATERIAL_NO: "/data/getSearchParamsMaterialNo",
    DISPLAY_MATERIAL_DTO: "/data/displayMaterialDTO",
    COPY_FROM_MATERIAL_ORG_ELMS_ETEXTEND:
      "/data/CopyFromMaterialOrgElmSetExtend",
    GET_EXTEND_SEARCH_SET: "/data/getExtendSearchSet",
    GET_VALUATION_CLASS: "/data/getValuationClass",
    GET_SPPROC_TYPE: "/data/getSpecialProcurementBasedOnPlant",
    GET_LANGUAGE: "/data/getLangu",
    GET_COPY_MATERIAL: "/data/CopyFromMaterialOrgElmSetExtend",
    GET_STORAGE_LOCATION_FOR_PLANT: "/data/getStorageLocationForPlant",
    GET_STORAGE_LOCATION_SET_BASED_ON_PLANT:
      "/data/getStorageLocationSetBasedOnPlant",
    GET_SPECMVMT: "/data/getSpecMvmt",
    GET_DISTRCHNL: "/data/getDistrChan",
    GET_DELIVARING_PLANT_BASED_ON_SALES_ORG_AND_DISTCHNL:
      "/data/getDelivaringPlantBasedOnSalesOrgAndDistChnl",
    GET_PROFIT_CENTER_BASED_ON_PLANT: "/data/getProfitCenterBasedOnPlant",
    GET_MRP_CONTROLLER_BASED_ON_PLANT: "/data/getMRPControllerBasedOnPlant",
    GET_PRODUCTION_SCHEDULING_PROFILE_BASED_ON_PLANT:
      "/data/getProductionSchedulingProfileBasedOnPlant",
    GET_PROD_STORAGE_LOCATION_BASED_ON_PLANT:
      "/data/getProdStorageLocationBasedOnPlant",
    GET_PROCUREMENT_STORAGE_LOCATION_BASED_ON_PLANT:
      "/data/getProcurementStorageLocationBasedOnPlant",
    GET_SALES_ORG_BASED_ON_PLANT: "/data/getSalesOrgBasedOnPlant",
    GET_SCHEDULING_MARGIN_KEY_BASED_ON_PLANT:
      "/data/getSchedulingMarginKeyBasedOnPlant",
    GET_COMMODITY_CODE_BASED_ON_COUNTRY: "/data/getCommodityCodeBasedOnCountry",
    GET_PLACEMENT: "/data/getPlacement",
    GET_CHECK_DIGIT: `/data/calculateCheckDigit`,
    GET_HTS_CODE: "/data/getHTSCode",
    GET_SALES_ORG_EXTENDED: "/data/getSalesOrgExtended",
    GET_PLANT_EXTENDED: "/data/getPlantExtended",
    GET_DISTR_CHAN_EXTENDED: "/data/getDistributionChannelExtended",
    GET_WAREHOUSE_EXTENDED: "/data/getWarehouseExtended",
    GET_STOR_LOC_EXTENDED: "/data/getStorageLocationExtended",
    VALIDATE_MANUFACTURER_ID: "/data/checkMINumber",
    GET_COUNTRY_BASED_ON_PLANT: "/data/getCountryBasedOnPlant",
    FETCH_SCHEDULERS_IN_REQ_BENCH: '/scheduler/fetchSchedulersInRequestBench',
    UPDATE_PRIORITY: '/scheduler/updatePriorityForPendingRequests',
    UPDATE_PRIORITY_IN_REQ_BENCH: '/scheduler/updatePriorityAndStatusForScheduledRequests',
    GET_CHANGELOG_DATA:'/data/fetchChangeLogData',
    GET_CLASS_BY_TYPE: '/data/getClassByType',
    GET_CHARACTERISTICS_BY_CLASS: '/data/getCharacteristicsByClass',
    GET_CHARACTERISTIC_VALUES: '/data/getCharacteristicsValues',
    GET_COSTCENTER_DATA : '/data/getCostCentersData',
    GET_SALES_ORG : '/data/getSalesOrgFilteredTest',
    GET_PLANT : '/data/getPlantFilteredTest',
    GET_STORAGE_LOCATION: '/data/getStorageLocationForPlantFilteredTest',
    GET_WAREHOUSE_NO : '/data/getWareHouseNoForPlantFilteredTest',
    
  },
  WORK_FLOW: {
    CANCEL_WORKFLOW: "/workflow/cancelWorkflow",
    FETCH_USER_DATA: "/workflow/fetchIASUsersData",
    FETCH_ROLES_DATA:
      "/workflow/fetchEntitiesAndActivitiesFromRoles?applicationId=1",
    CANCEL_WORKFLOWS: "/workflow/cancelWorkflows",
  },
  INVOKE_RULES: {
    LOCAL: "/rest/v1/invoke-rules",
    PROD: "/v1/invoke-rules",
  },
  USER_ACCESS: {
    CHECK_ACCESS: "/data/getUserAccess",
  },
  CHG_DISPLAY_REQUESTOR: {
    LOGISTIC: "data/getLogisticData",
    MRP: "data/getMRPData",
    SALES: "data/getSalesData",
    DESC: "data/getDescriptionData",
    WAREHOUSE: "data/getWareHouseData",
    CHG_STATUS: "data/getChangeStatusData",
    SET_DNU: "data/getSetToDNUData",
    FETCH_CHANGELOG_DATA: "data/fetchChangeLogData",
    DISPLAY_DTO: "data/displayMassMaterialDTO",
    DISPLAY_BK: "data/displayMassBankKeyDTO",
  },
  MAT_SEARCH_APIS: {
    LOGISTIC: "/data/getMatNoForLogisticData",
    MRP: "/data/getMatNoForMRPData",
    SALES: "/data/getMatNoForSalesData",
    DESC: "/data/getMatNoForUpdateDescriptionData",
    WAREHOUSE: "/data/getMatNoForWareHouseData",
    CHG_STATUS: "/data/getMatNoForChangeStatusData",
    SET_DNU: "/data/getMatNoForSetToDnuData",
  },
  DEPENDENT_LOOKUPS: {
    UNITTYPE: "/data/getUnitType",
  },
  DMS_API: {
    UPLOAD_DOCUMENT: "documentManagement/uploadDocument",
    FETCH_DOCUMENTS: "documentManagement/filterDocument",
    GET_ATTACHMENT_TYPE: "documentManagement/getAttachmentType",
    GET_FILE_TYPE: "documentManagement/getDocType",
  },
  REQUEST_BENCH: {
    BIFURCATION_DETAILS: '/child-requests',
    DIVISION: "data/getSearchParamDivision",
    CREATED_BY: "data/getSearchParamCreatedBy",
    FILTER_DATA: "/data/searchForRequestBench",
    SEARCH_IO:"/api/v1/lookup/search-requestBench"
  },
  DISPLAY_INTERNAL_ORDER: {
    DISPLAY_DTO: "/api/v1/lookup/display-internalOrders"
  },
  DOCUMENT_MANAGEMENT: {
    DELETE: "/documentManagement/delete",
  },
  TAX_DATA: {
    GET_TAX_COUNTRY: "/data/getTaxDataSetBasedOnCountry",
    GET_COUNTRY_SALESORG: "/data/getCountriesBasedOnSalesOrg",
  },
  DASHBOARD_APIS: {
    All_MATERIALS_BY_PLANT_AND_SALES: "getAllMaterialsByPlantsAndSalesOrg",
    DRAFT_STATUS: "getAllReqIdInDraftStatus",
    MATERIAL_LIST: "getAllCreatedMatList",
    PRICING_GROUP: "getAllMatPricingGroup",
    MISSING_HTS: "getHTCCodeMissing",
    KPI3: "counts/getTotalRequestsPendingByGroups",
    KPI7: "counts/getTotalChangeTemplateCount",
    KPI8: "counts/getTotalReqBasedOnRegion",
    KPI10: "counts/getTop5Requestor",
    KPI1: '/getTotalRequestsBasedOnReqTypeAndReqStatus',
    KPI2: '/getAverageLifecycleDurationOfReqByObjType',
    KPI4: '/getPercentageOfReqSuccCompWithOutRejection',
    KPI5: '/getPercentageOfReqSuccCompleted',
    KPI6: '/getReqThatHaveBreachedSLA',
    KPI9: '/getHowManyReqWereRejectedByRoleByUser',
    KPI11: '/getHowManyRequestsArePendingApproverMDM',
    KPI12: '/getAvgTimeFromMDMApproveToSAPSyndication',
    KPI13: '/getAvgApprovalTimeByApprover',
    KPI14: '/getNumberOfRejectedReqBasedOnRequestor',
    KPI15: '/getApproverWhoHaveBreachedSLA',
    KPI16: '/getApproverAndTheirAvgApprovalTime',
    KPI17: '/getNoOfReqCompletedByMDM',
    KPI18: '/getNoOfOpenReqByReqType',
    KPI19: '/getNoOfReqRequestedAndNoOfApprovApproved',
    KPI20: '/getAllReqPendingApprover',
    KPI21: '/getAllTempBlockRequests',
    KPI22: '/getAllScheduledRequests',
    KPI23: '/getAllScheduledPDFs',
    KPI24: '/getReqHoldWithRequestors',
    KPI25: '/getAllErroneousRequests',
    KPI26: '/getObjectNoAssociatedWithRequestId',
    KPI27: '/getAllFERCRequests',
    KPI28: '/fetchRequestHistory',
    KPI29: '/getObjectNoAssociatedWithSunoco',
    KPI30: '/getNoOfFailedReqByReqType',
    KPI31: '/getAllSLAReminderIds',
    KPI_CARDS: '/counts/getTotalRequestCount',
    FETCH_USER_CONFIG: '/config/fetchKpisUserConfig',
    SAVE_USER_CONFIG: '/config/saveKpisUserConfig',
  },
  DUMMY_API: "/dummy/dummyResponse",

  EXCEL: {
    DOWNLOAD_EXCEL_FOR_EXTEND: "/excel/downloadExcelForExtend",
    DOWNLOAD_EXCEL_FOR_EXTEND_MAIL: "/excel/downloadExcelForExtendInMail",
    DOWNLOAD_EXCEL: "/excel/downloadExcel",
    DOWNLOAD_EXCEL_MAIL: "/excel/downloadExcelInMail",
    DOWNLOAD_EXCEL_WITH_DATA: "excel/downloadExcelWithData",
    DOWNLOAD_EXCEL_FINANCE: "/excel/exportFinanceCostExcel",
    DOWNLOAD_EXCEL_MAT: "/excel/exportMATExcel",
    DOWNLOAD_EXCEL_SAP_REPORT: "/excel/downloadExcelForCopyFromOrgData",
    DOWNLOAD_EXCEL_PCG_CREATE_WITH_UPLOAD: "/excel/downloadHierarchyNodeTemplate",
    DOWNLOAD_EXCEL_PCG_CHANGE_WITH_UPLOAD: "/excel/downloadHierarchyNodeTemplateForChange",
    DOWNLOAD_EXCEL_PCG_CREATE_WITH_UPLOAD_MAIL: "/excel/downloadHierarchyNodeTemplateInMail",
    DOWNLOAD_EXCEL_PCG_CHANGE_WITH_UPLOAD_MAIL: "/excel/downloadHierarchyNodeTemplateForChangeInMail",
    EXPORT_PREVIEW_EXCEL: '/excel/exportPreviewExcel',
    DOWNLOAD_EXCEL_GL: "/excel/exportGLExcel",
    DOWNLOAD_EXCEL_BOM: "/excel/downloadExcelForBOM",
    EXPORT_EXCEL_BK:"/excel/exportBankKeyExcel"
  },
  ERROR_HISTORY: {
    ERROR_LOG: "data/getErrorLogFromRequestId",
    ERROR_LOG_CHILD: 'data/getErrorLogFromChildRequestId',
    DOWNLOAD_ERROR_LOG:'excel/errorReportGenerationParent',
    DOWNLOAD_ERROR_LOG_CHILD:'excel/errorReportGeneration',
    DOWNLOAD_EXCEL_BP_ERROR: "mass/downloadExcelForBpHavingError",
    EXCEL_ERROR_HISTORY: "excel/findExcelErrorByRequestId",
    ERROR_LOG_HIERARCHY: "node/getErrorLogFromRequestId",
    ERROR_LOG_CHILD_HIERARCHY: "node/getErrorLogFromChildRequestId",
    ERROR_LOG_PARENT_IO: "api/v1/lookup/getErrorDetailsParent",
    ERROR_LOG_CHILD_IO: "api/v1/lookup/getErrorDetailsChild",
  },
  TASK_ACTION_DETAIL: {
    FETCH_TASK: "activitylog/fetchTaskActionDetail",
    FETCH_REQUEST_HISTORY: "activitylog/fetchRequestHistory",
    FETCH_DETAILS_WORKFLOW: "activitylog/fetchInitialDetailsForWorkflow",
    GET_DOCS: "documentManagement/getDocByRequestId",
    GET_CHILD_DOCS: 'documentManagement/getDocByChildRequestId',
    FETCH_MAILS: "mail/fetchMails",
    TASKDETAILS_FOR_REQUESTID: "activitylog/fetchTaskDetailsForRequestId",
  },
  ACCORDION_API: {
    WAREHOUSE: "data/displayLimitedWarehouseData",
    PLANT: "data/displayLimitedPlantData",
    ACCOUNTING: "data/displayLimitedAccountingData",
  },
  HEALTH_API: {
    LAST_TWO_RESULTS: "api/lastTwoResults",
  },
  EMAIL_CONFIG: {
    FETCH_NOTIFICATION_MODULES: '/mail/fetchNotificationModulesOnIdentifiersHana',
    FETCH_NOTIFICATION_EVENTS: '/mail/fetchNotificationEventsOnIdentifiersHana',
    POPULATE_APP_IDENTIFIERS_HANA:'/v1/mail-definition/populateAppIdentifiersHana',
    PROCESS_VARIABLE:'/v1/mail-definition/process/Variable',
  },
  SYSTEM_CONFIG: {
    GET_CURRENT_SAP_SYSTEM: '/api/global/getCurrentSapSystem',
    UPDATE_SAP_SYSTEM: '/api/global/updateSapSystem',
  },
  CHATBOT_QUERY_APIS: {
    GNL_QUERY_API: `/api/sql/searchVectorStore`,
    DB_QUERY_API: `/api/sql/convert`,
    NAV_QUERY_API: `/api/navigation/navigate`,
    REQ_QUERY_API: `/api/request/ai/create`,
    REPORT_QUERY_API: `/reports/ai/create`,
  },
  DOCUMENT_CONFIGURATION_APIS: {
    GET_FILES_LIST_API: `/api/sql/getFilesList`,
    UPLOAD_FILES_API: `/api/sql/extractChunks/mdg_metadata`,
    DELETE_FILE_API: `/api/sql/delete`,
    UPDATE_VISIBILITY_API: `/api/sql/updateVisibility`
  },
  DATA_CLEANSE_APIS: {
    GET_MATERIAL_GRP: `/data/getMatlGroup`,
    SEARCH_MAT_NO: `/data/getSearchParamsMaterialNo`,
    GET_MAT_TYPE: '/data/getMatlType',
    CLEANSING_REQ: '/dataCleansing/getDataCleansingRequests',
    INITIATE_DATA_QUALITY_CHECK: '/dataCleansing/initiateDataQualityCheck',
    CLEANSING_REQ_DETAILS: '/dataCleansing/getDataCleansingRequestDetails',
    DOWNLOAD_PDF: '/dataCleansing/generatePDF',
  },
  BANKKEY_APIS: {
    CREATE_BANKKEY_REQUEST_HEADER: `/massAction/createRequestHeader`,
  },

  MASTER_BUTTON_APIS: {
    [MODULE?.PCG]: {
      [REQUEST_TYPE?.CREATE]: {
        SAVE_AS_DRAFT: {
          URL: `/${destination_ProfitCenter_Mass}/node/pcNodeSaveAsDraft`,
          NAVIGATE_TO: `/requestBench`,
        },
        SUBMIT_FOR_REVIEW: {
          URL: `/${destination_ProfitCenter_Mass}/node/pcNodeSubmitForReview`,
          NAVIGATE_TO: `/requestBench`,
        },
        SEND_BACK: {
          URL: `/${destination_ProfitCenter_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        CORRECTION: {
          URL: `/${destination_ProfitCenter_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        REJECT: {
          URL: `/${destination_ProfitCenter_Mass}/node/pcNodeRejected`,
          NAVIGATE_TO: `/requestBench`,
        },
        VALIDATE: {
          URL: `/${destination_ProfitCenter_Mass}/node/validateHierarchyTree`,
          NAVIGATE_TO: `/requestBench`,
        },
        SYNDICATE: {
          URL: `/${destination_ProfitCenter_Mass}/node/createPcNodeApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
         APPROVE: {
          URL: `/${destination_ProfitCenter_Mass}/node/createPcNodeApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
      },
      [REQUEST_TYPE?.CREATE_WITH_UPLOAD]: {
        SAVE_AS_DRAFT: {
          URL: `/${destination_ProfitCenter_Mass}/node/pcNodeSaveAsDraft`,
          NAVIGATE_TO: `/requestBench`,
        },
        SUBMIT_FOR_REVIEW: {
          URL: `/${destination_ProfitCenter_Mass}/node/pcNodeSubmitForReview`,
          NAVIGATE_TO: `/requestBench`,
        },
        SEND_BACK: {
          URL: `/${destination_ProfitCenter_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        CORRECTION: {
          URL: `/${destination_ProfitCenter_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
          REJECT: {
          URL: `/${destination_ProfitCenter_Mass}/node/pcNodeRejected`,
          NAVIGATE_TO: `/requestBench`,
        },
        VALIDATE: {
          URL: `/${destination_ProfitCenter_Mass}/node/validateHierarchyTree`,
          NAVIGATE_TO: `/requestBench`,
        },
        SYNDICATE: {
          URL: `/${destination_ProfitCenter_Mass}/node/createPcNodeApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
          APPROVE: {
          URL: `/${destination_ProfitCenter_Mass}/node/pcNodeApprovalSubmit`,
          NAVIGATE_TO: `/requestBench`,
        },
      },
      [REQUEST_TYPE?.CHANGE]: {
        SAVE_AS_DRAFT: {
          URL: `/${destination_ProfitCenter_Mass}/node/changePcNodeSaveAsDraft`,
          NAVIGATE_TO: `/requestBench`,
        },
        SUBMIT_FOR_REVIEW: {
          URL: `/${destination_ProfitCenter_Mass}/node/changePcNodeSubmitForReview`,
          NAVIGATE_TO: `/requestBench`,
        },
         SEND_BACK: {
          URL: `/${destination_ProfitCenter_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        CORRECTION: {
          URL: `/${destination_ProfitCenter_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
          REJECT: {
          URL: `/${destination_ProfitCenter_Mass}/node/changePcNodeRejected`,
          NAVIGATE_TO: `/requestBench`,
        },
        VALIDATE: {
          URL: `/${destination_ProfitCenter_Mass}/node/validateHierarchyTree`,
          NAVIGATE_TO: `/requestBench`,
        },
        SYNDICATE: {
          URL: `/${destination_ProfitCenter_Mass}/node/changePcNodeApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
          APPROVE: {
          URL: `/${destination_ProfitCenter_Mass}/node/changePcNodeApprovalSubmit`,
          NAVIGATE_TO: `/requestBench`,
        },
      },
      [REQUEST_TYPE?.CHANGE_WITH_UPLOAD]: {
        SAVE_AS_DRAFT: {
          URL: `/${destination_ProfitCenter_Mass}/node/changePcNodeSaveAsDraft`,
          NAVIGATE_TO: `/requestBench`,
        },
        SUBMIT_FOR_REVIEW: {
          URL: `/${destination_ProfitCenter_Mass}/node/changePcNodeSubmitForReview`,
          NAVIGATE_TO: `/requestBench`,
        },
         SEND_BACK: {
          URL: `/${destination_ProfitCenter_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        CORRECTION: {
          URL: `/${destination_ProfitCenter_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
         REJECT: {
          URL: `/${destination_ProfitCenter_Mass}/node/changePcNodeRejected`,
          NAVIGATE_TO: `/requestBench`,
        },
        VALIDATE: {
          URL: `/${destination_ProfitCenter_Mass}/node/validateHierarchyTree`,
          NAVIGATE_TO: `/requestBench`,
        },
        SYNDICATE: {
          URL: `/${destination_ProfitCenter_Mass}/node/changePcNodeApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
      },
    },

    [MODULE?.CCG]: {
      [REQUEST_TYPE?.CREATE]: {
        SAVE_AS_DRAFT: {
          URL: `/${destination_CostCenter_Mass}/node/ccNodeSaveAsDraft`,
          NAVIGATE_TO: `/requestBench`,
        },
        SUBMIT_FOR_REVIEW: {
          URL: `/${destination_CostCenter_Mass}/node/ccNodeSubmitForReview`,
          NAVIGATE_TO: `/requestBench`,
        },
         SEND_BACK: {
          URL: `/${destination_CostCenter_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        CORRECTION: {
          URL: `/${destination_CostCenter_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        REJECT: {
          URL: `/${destination_CostCenter_Mass}/node/ccNodeRejected`,
          NAVIGATE_TO: `/requestBench`,
        },
        VALIDATE: {
          URL: `/${destination_CostCenter_Mass}/node/validateHierarchyTree`,
          NAVIGATE_TO: `/requestBench`,
        },
        SYNDICATE: {
          URL: `/${destination_CostCenter_Mass}/node/createCcNodeApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
      },
      [REQUEST_TYPE?.CREATE_WITH_UPLOAD]: {
        SAVE_AS_DRAFT: {
          URL: `/${destination_CostCenter_Mass}/node/ccNodeSaveAsDraft`,
          NAVIGATE_TO: `/requestBench`,
        },
         REJECT: {
          URL: `/${destination_CostCenter_Mass}/node/ccNodeRejected`,
          NAVIGATE_TO: `/requestBench`,
        },
          SEND_BACK: {
          URL: `/${destination_CostCenter_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        CORRECTION: {
          URL: `/${destination_CostCenter_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        SUBMIT_FOR_REVIEW: {
          URL: `/${destination_CostCenter_Mass}/node/ccNodeSubmitForReview`,
          NAVIGATE_TO: `/requestBench`,
        },
        VALIDATE: {
          URL: `/${destination_CostCenter_Mass}/node/validateHierarchyTree`,
          NAVIGATE_TO: `/requestBench`,
        },
        SYNDICATE: {
          URL: `/${destination_CostCenter_Mass}/node/createCcNodeApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
      },
      [REQUEST_TYPE?.CHANGE]: {
        SAVE_AS_DRAFT: {
          URL: `/${destination_CostCenter_Mass}/node/changeCcNodeSaveAsDraft`,
          NAVIGATE_TO: `/requestBench`,
        },
         REJECT: {
          URL: `/${destination_CostCenter_Mass}/node/changeCcNodeRejected`,
          NAVIGATE_TO: `/requestBench`,
        },
        SEND_BACK: {
          URL: `/${destination_CostCenter_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        CORRECTION: {
          URL: `/${destination_CostCenter_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        SUBMIT_FOR_REVIEW: {
          URL: `/${destination_CostCenter_Mass}/node/changeCcNodeSubmitForReview`,
          NAVIGATE_TO: `/requestBench`,
        },
        VALIDATE: {
          URL: `/${destination_CostCenter_Mass}/node/validateHierarchyTree`,
          NAVIGATE_TO: `/requestBench`,
        },
        SYNDICATE: {
          URL: `/${destination_CostCenter_Mass}/node/changeCcNodeApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
      },
      [REQUEST_TYPE?.CHANGE_WITH_UPLOAD]: {
        SAVE_AS_DRAFT: {
          URL: `/${destination_CostCenter_Mass}/node/changeCcNodeSaveAsDraft`,
          NAVIGATE_TO: `/requestBench`,
        },
         REJECT: {
          URL: `/${destination_CostCenter_Mass}/node/changeCcNodeRejected`,
          NAVIGATE_TO: `/requestBench`,
        },
        SEND_BACK: {
          URL: `/${destination_CostCenter_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        CORRECTION: {
          URL: `/${destination_CostCenter_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        SUBMIT_FOR_REVIEW: {
          URL: `/${destination_CostCenter_Mass}/node/changeCcNodeSubmitForReview`,
          NAVIGATE_TO: `/requestBench`,
        },
        VALIDATE: {
          URL: `/${destination_CostCenter_Mass}/node/validateHierarchyTree`,
          NAVIGATE_TO: `/requestBench`,
        },
        SYNDICATE: {
          URL: `/${destination_CostCenter_Mass}/node/changeCcNodeApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
      },
    },
    [MODULE?.CEG]: {
      [REQUEST_TYPE?.CREATE]: {
        SAVE_AS_DRAFT: {
          URL: `/${destination_GeneralLedger_Mass}/node/glNodeSaveAsDraft`,
          NAVIGATE_TO: `/requestBench`,
        },
        SUBMIT_FOR_REVIEW: {
          URL: `/${destination_GeneralLedger_Mass}/node/glNodeSubmitForReview`,
          NAVIGATE_TO: `/requestBench`,
        },
        SEND_BACK: {
          URL: `/${destination_GeneralLedger_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        CORRECTION: {
          URL: `/${destination_GeneralLedger_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
         REJECT: {
          URL: `/${destination_GeneralLedger_Mass}/node/glNodeRejected`,
          NAVIGATE_TO: `/requestBench`,
        },
        VALIDATE: {
          URL: `/${destination_GeneralLedger_Mass}/node/validateHierarchyTree`,
          NAVIGATE_TO: `/requestBench`,
        },
        SYNDICATE: {
          URL: `/${destination_GeneralLedger_Mass}/node/createGlNodeApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
      },
      [REQUEST_TYPE?.CHANGE]: {
        SAVE_AS_DRAFT: {
          URL: `/${destination_GeneralLedger_Mass}/node/glNodeSaveAsDraft`,
          NAVIGATE_TO: `/requestBench`,
        },
        SUBMIT_FOR_REVIEW: {
          URL: `/${destination_GeneralLedger_Mass}/node/glNodeSubmitForReview`,
          NAVIGATE_TO: `/requestBench`,
        },
          REJECT: {
          URL: `/${destination_GeneralLedger_Mass}/node/changeGlNodeRejected`,
          NAVIGATE_TO: `/requestBench`,
        },
          SEND_BACK: {
          URL: `/${destination_GeneralLedger_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        CORRECTION: {
          URL: `/${destination_GeneralLedger_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        VALIDATE: {
          URL: `/${destination_GeneralLedger_Mass}/node/validateHierarchyTree`,
          NAVIGATE_TO: `/requestBench`,
        },
        SYNDICATE: {
          URL: `/${destination_GeneralLedger_Mass}/node/createGlNodeApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
      },
      [REQUEST_TYPE?.CREATE_WITH_UPLOAD]: {
        SAVE_AS_DRAFT: {
          URL: `/${destination_GeneralLedger_Mass}/node/changeGlNodeSaveAsDraft`,
          NAVIGATE_TO: `/requestBench`,
        },
          SEND_BACK: {
          URL: `/${destination_GeneralLedger_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        CORRECTION: {
          URL: `/${destination_GeneralLedger_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
          REJECT: {
          URL: `/${destination_GeneralLedger_Mass}/node/glNodeRejected`,
          NAVIGATE_TO: `/requestBench`,
        },
        SUBMIT_FOR_REVIEW: {
          URL: `/${destination_GeneralLedger_Mass}/node/changeGlNodeSubmitForReview`,
          NAVIGATE_TO: `/requestBench`,
        },
        VALIDATE: {
          URL: `/${destination_GeneralLedger_Mass}/node/validateHierarchyTree`,
          NAVIGATE_TO: `/requestBench`,
        },
        SYNDICATE: {
          URL: `/${destination_GeneralLedger_Mass}/node/changeGlNodeApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
      },
      [REQUEST_TYPE?.CHANGE_WITH_UPLOAD]: {
        SAVE_AS_DRAFT: {
          URL: `/${destination_GeneralLedger_Mass}/node/changeGlNodeSaveAsDraft`,
          NAVIGATE_TO: `/requestBench`,
        },
          REJECT: {
          URL: `/${destination_GeneralLedger_Mass}/node/changeGlNodeRejected`,
          NAVIGATE_TO: `/requestBench`,
        },
        SUBMIT_FOR_REVIEW: {
          URL: `/${destination_GeneralLedger_Mass}/node/changeGlNodeSubmitForReview`,
          NAVIGATE_TO: `/requestBench`,
        },
        SEND_BACK: {
          URL: `/${destination_GeneralLedger_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        CORRECTION: {
          URL: `/${destination_GeneralLedger_Mass}/node/nodeSendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
        VALIDATE: {
          URL: `/${destination_GeneralLedger_Mass}/node/validateHierarchyTree`,
          NAVIGATE_TO: `/requestBench`,
        },
        SYNDICATE: {
          URL: `/${destination_GeneralLedger_Mass}/node/changeGlNodeApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
      },
    },

    [MODULE_MAP?.BK]: {
      [REQUEST_TYPE?.CREATE]: {
        SAVE_AS_DRAFT: {
          URL: `/${destination_BankKey}/massAction/createBankKeySaveAsDraft`,
          NAVIGATE_TO: `/requestBench`,
        },
        SUBMIT_FOR_REVIEW: {
          URL: `/${destination_BankKey}/massAction/createBankKeySubmitForReview`,
          NAVIGATE_TO: `/requestBench`,
        },
        APPROVE: {
          URL: `/${destination_BankKey}/massAction/createBankKeyApprovalSubmit`,
          NAVIGATE_TO: `/requestBench`,
        },
        VALIDATE: {
          URL: `/${destination_BankKey}/massAction/validateBankKey`,
          NAVIGATE_TO: `/requestBench`,
        },
        SYNDICATE: {
          URL: `/${destination_BankKey}/massAction/createBankKeyApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
        SEND_BACK: {
          URL: `/${destination_BankKey}/massAction/bankKeySendToLevel`,
          NAVIGATE_TO: `/requestBench`,
        },
      },
      [REQUEST_TYPE?.CREATE_WITH_UPLOAD]: {
        SAVE_AS_DRAFT: {
          URL: `/${destination_BankKey}/massAction/createBankKeySaveAsDraft`,
          NAVIGATE_TO: `/requestBench`,
        },
        SUBMIT_FOR_REVIEW: {
          URL: `/${destination_BankKey}/massAction/createBankKeySubmitForReview`,
          NAVIGATE_TO: `/requestBench`,
        },
        APPROVE: {
          URL: `/${destination_BankKey}/massAction/createBankKeyApprovalSubmit`,
          NAVIGATE_TO: `/requestBench`,
        },
        VALIDATE: {
          URL: `/${destination_BankKey}/massAction/validateBankKey`,
          NAVIGATE_TO: `/requestBench`,
        },
        SYNDICATE: {
          URL: `/${destination_BankKey}/massAction/createBankKeyApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
      },
      [REQUEST_TYPE?.CHANGE]: {
        SAVE_AS_DRAFT: {
          URL: `/${destination_BankKey}/node/changeCcNodeSaveAsDraft`,
          NAVIGATE_TO: `/requestBench`,
        },
        SUBMIT_FOR_REVIEW: {
          URL: `/${destination_BankKey}/node/changeCcNodeSubmitForReview`,
          NAVIGATE_TO: `/requestBench`,
        },
        VALIDATE: {
          URL: `/${destination_BankKey}/node/validateHierarchyTree`,
          NAVIGATE_TO: `/requestBench`,
        },
        SYNDICATE: {
          URL: `/${destination_BankKey}/node/changeCcNodeApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
      },
      [REQUEST_TYPE?.CHANGE_WITH_UPLOAD]: {
        SAVE_AS_DRAFT: {
          URL: `/${destination_BankKey}/node/changeCcNodeSaveAsDraft`,
          NAVIGATE_TO: `/requestBench`,
        },
        SUBMIT_FOR_REVIEW: {
          URL: `/${destination_BankKey}/node/changeCcNodeSubmitForReview`,
          NAVIGATE_TO: `/requestBench`,
        },
        VALIDATE: {
          URL: `/${destination_BankKey}/node/validateHierarchyTree`,
          NAVIGATE_TO: `/requestBench`,
        },
        SYNDICATE: {
          URL: `/${destination_BankKey}/node/changeCcNodeApproved`,
          NAVIGATE_TO: `/requestBench`,
        },
      },
    },
  },
  API: {
    MODULE_FEATURE_ACCESS: '/iwa/user/module-feature-access?iwaAppId=MDG',
    USER_DETAILS: '/iwa/user/current-user',
    ROLES: '/iwa/user/roles',
    MODULE_FEATURE_ACCESS_PROD: '/api/v1/user-roles/user/module-feature-access?iwaAppId=MDG',
    USER_DETAILS_PROD: '/api/v1/users/current-user',
    ROLES_PROD: '/api/v1/user-roles/roles',
    MAIL_DEFINATION:'/v1/mail-definition',
  },
  MODULE_DESTINATION_MAP: {
    [MODULE?.CCG]: destination_CostCenter_Mass,
    [MODULE?.PCG]: destination_ProfitCenter_Mass
  }
};
