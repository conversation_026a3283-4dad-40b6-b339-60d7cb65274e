{"configuration": {"moreOptions": 6}, "fieldListAccordingToView": [{"label": "Basic data", "value": "basicData", "fields": [], "code": "K"}, {"label": "Classification", "value": "classification", "fields": [], "code": "C"}, {"label": "Sales", "value": "sales", "fields": ["Plant", "Sales Organization", "Distribution Channel"], "code": "V"}, {"label": "Purchasing", "value": "purchasing", "fields": ["Plant"], "code": "E"}, {"label": "MRP", "value": "mrp", "fields": ["Plant", "Storage Location", "MRP Profile"], "code": "D"}, {"label": "Plant Stock", "value": "plantStock", "fields": ["Plant"], "code": "X"}, {"label": "Storage Location Stocks", "value": "storageLocationStocks", "fields": ["Plant", "Storage Location"], "code": "Z"}, {"label": "Storage", "value": "storage", "fields": ["Storage Location"], "code": "L"}, {"label": "Quality Management", "value": "qualityManagement", "fields": ["Plant"], "code": "Q"}, {"label": "Accounting", "value": "accounting", "fields": ["Plant"], "code": "B"}, {"label": "Costing", "value": "costing", "fields": ["Plant"], "code": "G"}, {"label": "Forecasting", "value": "forecasting", "fields": ["Plant", "Forecast Profile"], "code": "P"}, {"label": "Production Resources/Tools", "value": "productionResources/tools", "fields": ["Plant"], "code": "F"}, {"label": "Warehouse Management", "value": "warehouseManagement", "fields": ["Plant", "Warehouse No", "Storage Type"], "code": "S"}, {"label": "Work Scheduling", "value": "workScheduling", "fields": ["Plant"], "code": "A"}]}