import { ActionTypes } from "@mui/base";
import { createReducer } from "@reduxjs/toolkit";

let systemInitialState = {
  userState:{
    system: "",
    userToken: null,
  },
  url: {}
}

let systemReducer = createReducer(systemInitialState, {
  SET_USERSTATE: (state, action) => {
    state.userState = action.payload;
    return state;
  },
  SET_SYSTEMURL: (state, action) => {
    state.url = action.payload;
    return state;
  },
  CLEAR_DATA: (state, action) => {
    state.userState = {
      system: "",
      userToken: null,
    };
    state.url = {}
    return state;
  },
})


let createReturnInitialState = {
  poData: {
    selectedRow: [],
    selectedDetails: [],
  },
};

let createReturnReducer = createReducer(createReturnInitialState, {
  DATA_STORE: (state, action) => {
    state.poData = action.payload;
    return state;
  },
  CLEAR_DATA: (state, action) => {
    state.poData = {
      selectedRow: [],
      selectedDetails: [],
    };
    return state;
  },
});
let utilityInitialState = {
 redirection:{
   url_LastPath : null,
   history_Module: null,
   data:null
  },
  redirectionFilter:{
    data: null,
    targetModule: null
  }
}

let utilityReducer = createReducer(utilityInitialState, {
  SET_HISTORYPATH: (state, action)=>{
      state.redirection.url_LastPath = action.payload.url;
      state.redirection.history_Module = action.payload.module;
      state.redirection.data = action.payload.data? action.payload.data : null;
      
      return state
  },
  CLEAR_LASTPATH:(state,action)=>{
    state.redirection.url_LastPath = null;
    state.redirection.history_Module = null;
    state.redirection.data =  null;
    return state;
  },
  SET_REDIRECTIONFILTER: (state,action)=>{
    state.redirectionFilter.data = action.payload.filter;
    state.redirectionFilter.targetModule = action.payload.targetModule;
    return state;
  },
  CLEAR_REDIRECTIONFILTER: (state,action)=>{
    state.redirectionFilter.data = null;
    state.redirectionFilter.targetModule = null;
    return state;
  }

})


export {
  systemReducer,
  utilityReducer,
  createReturnReducer,
};
