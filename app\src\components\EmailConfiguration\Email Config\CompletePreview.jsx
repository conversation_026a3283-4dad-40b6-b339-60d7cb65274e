import React, { useState, useEffect } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import CloseIcon from "@mui/icons-material/Close";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import Switch from "@mui/material/Switch";
import Card from "@mui/material/Card";
import CardActions from "@mui/material/CardActions";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";
import CardHeader from "@mui/material/CardHeader";
import Avatar from "@mui/material/Avatar";
import Toolbar from "@mui/material/Toolbar";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import AppBar from "@mui/material/AppBar";
import { Editor } from "react-draft-wysiwyg";
import { EditorState, ContentState } from "draft-js";
import "react-draft-wysiwyg/dist/react-draft-wysiwyg.css";
import draftToHtml from "draftjs-to-html";
import htmlToDraft from "html-to-draftjs";
import { doAjax, doCrudApi } from "../utility/serviceRequest";
import { useDispatch, useSelector } from "react-redux";
import { Provider } from "react-redux";
import {
  setAPIBaseUrl,
  setConfigs,
  setToken,
} from "../redux/reducers/userReducer";
import store from "../redux/store";
import cherrywork from "./Cherrywork.svg";
import logo from "./Logo.png";

const CompletePreview = (props) => {
  const userReducer = useSelector((state) => state.userReducer);
  const [isLoader, setLoader] = React.useState(false);
  const [editorState, setEditorState] = React.useState(() =>
    EditorState.createEmpty()
  );
  const [defaultEditorValue, setDefaultEditorValue] = React.useState(() =>
    EditorState.createEmpty()
  );
  const setEditorValue = (content) => {
    const blocksFromHtml = htmlToDraft(content);
    const { contentBlocks, entityMap } = blocksFromHtml;
    const contentState = ContentState.createFromBlockArray(
      contentBlocks,
      entityMap
    );
    const editorState = EditorState.createWithContent(contentState);

    setDefaultEditorValue(editorState);
    setEditorState(editorState);
  };
  const getFileBase64 = (file, callback) => {
    var reader = new FileReader();
    reader.readAsDataURL(file);
    // Since FileReader is asynchronous,
    // we need to pass data back.
    reader.onload = () => callback(reader.result);
    // TODO: catch an error
    reader.onerror = (error) => {};
  };
  const imageUploadCallback = (file) =>
    new Promise((resolve, reject) =>
      getFileBase64(file, (data) => resolve({ data: { link: data } }))
    );
//   React.useEffect(() => {
//     if (userReducer.applicationName !== "") {
//       setEditorValue(props?.data.content);
//     }

    // eslint-disable-next-line
//   }, [props?.data, userReducer]);
  return (
    <Dialog open={props?.openPreview} onClose={props?.closePreview}>
      <DialogTitle >
        <Stack
          direction="row"
          spacing={2}
          alignItems="center"
          justifyContent="space-between"
          width="100%"
        >
          <Typography
            sx={{
              fontWeight: 500,

              color: "#1D1D11",
              fontSize: "16px",
              fontFamily: `"Roboto", sans-serif !important`,
            }}
          >
            Preview
          </Typography>

          <Button onClick={props?.closePreview} startIcon={<CloseIcon />} />
        </Stack>
      </DialogTitle>
      <DialogContent>
        <Box
          //   sx={{
          //     display: 'flex',
          //     flexWrap: 'wrap',
          //     '& > :not(style)': {
          //       m: 1,
          //       width: 128,
          //       height: 128,
          //     },
          //   }}
        
        >
          <Paper elevation={3} width="80%" height="90%">
            <Stack backgroundColor="#F0EFFF">
              <img src={logo} style={{ width: "100%", height: "2rem" }} />
            </Stack>
            <Stack>
            <Stack justifyContent="space-around">
           <Stack direction="row"  justifyContent="space-around" alignItems="center" >
          <span  style={{display: "flex", flexDirection: "column" , padding:" 0px 0px 0px 2px ",width:"50%" }}>
           <span  style={{width: "46.5%",fontWeight: 500,
            color: " #6A6A6A; !important",
            fontSize: "14px",
            fontFamily: `"Roboto", sans-serif !important`, whiteSpace: "nowrap",
                }} >
            Template Name
           </span>
           <span style={{ fontWeight: 400,
                  width: "50%",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  color: "#1D1D11",
                  fontSize: "14px",
                  fontFamily: `"Roboto", sans-serif !important`}}>
          {/* {props?.data.name} */}
          name
          
           </span>
           </span>

           <span  style={{display: "flex", flexDirection: "column",padding:" 0px 2px 0px 2px ",width:"50%"}}>
           <span  style={{width: "50%",fontWeight: 500,
            color: " #6A6A6A; !important",
            fontSize: "14px",
            fontFamily: `"Roboto", sans-serif !important`, whiteSpace: "nowrap",
                }}>
            Identifier
           </span>
           <span style={{ fontWeight: 400,
                  width: "70%",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  color: "#1D1D11",
                  fontSize: "14px",
                  fontFamily: `"Roboto", sans-serif !important`}}>
                  Identifier
          {/* {props?.data.identifierDesc} */}
          
           </span>
           </span>

           
           
           </Stack>
           <Stack direction="row" justifyContent="space-around" >

           <span  style={{display: "flex", flexDirection: "column", padding:" 0px 0px 0px 2px ", width:"50%"}}>
           <span style={{width: "50%",fontWeight: 500,
            color: " #6A6A6A; !important",
            fontSize: "14px",
            fontFamily: `"Roboto", sans-serif !important`, whiteSpace: "nowrap",
                  }} >
          Module
           </span>
           <span style={{ fontWeight: 500,
                  width: "50%",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  color: "#1D1D11",
                  fontSize: "14px",
                  fontFamily: `"Roboto", sans-serif !important`,}}>
                  Module
           {/* {props?.data.entityDesc} */}
           
           </span>
           </span>
           <span  style={{display: "flex", flexDirection: "column",padding:" 0px 2px 0px 2px ",width:"50%"}}>
           <span style={{width: "50%",fontWeight: 500,
            color: " #6A6A6A; !important",
            fontSize: "14px",
            fontFamily: `"Roboto", sans-serif !important`, whiteSpace: "nowrap",
                 }}>
            Event
           </span>
           <span style={{ fontWeight: 500,
                  width: "50%",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  color: "#1D1D11",
                  fontSize: "14px",
                  fontFamily: `"Roboto", sans-serif !important`,}}>
                  Event
           {/* {props?.data.processDesc} */}
           
           </span>
           </span>
           </Stack>
           <Stack direction="row"  >

<span  style={{display: "flex", flexDirection: "column",padding:" 0px 0px 0px 2px ",width:"50%"}}>
<span style={{width: "50%",fontWeight: 500,
 color: " #6A6A6A; !important",
 fontSize: "14px",
 fontFamily: `"Roboto", sans-serif !important`,}} >
Subject
</span>
<span style={{ fontWeight: 500,
       width: "50%",
       textOverflow: "ellipsis",
       whiteSpace: "nowrap",
       overflow: "hidden",
       color: "#1D1D11",
       fontSize: "14px",
       fontFamily: `"Roboto", sans-serif !important`,}}>
       Subject
{/* {props?.data.entityDesc} */}

</span>
</span>

</Stack>
           </Stack>
              <Editor
                editorState={editorState}
                wrapperClassName="Editor"
                editorClassName="Editor"
                defaultEditorState={defaultEditorValue}
                onEditorStateChange={setEditorState}
                // onContentStateChange={onContentStateChange}
                toolbar={{
                  inline: { inDropdown: false },
                  list: { inDropdown: false },
                  textAlign: { inDropdown: false },
                  link: { inDropdown: false },
                  history: { inDropdown: false },
                  image: {
                    uploadCallback: imageUploadCallback,
                    previewImage: true,
                    alignmentEnabled: true,
                  },
                }}
                toolbarHidden
                readOnly
                // mention={{
                //   separator: " ",
                //   trigger: "$",
                //   suggestions: processVariables.map((option) => ({
                //     text: option.variable ?? "",
                //     value: option.variable ?? "",
                //     url: option.variable ?? "",
                //   })),
                // }}
                // blockRenderMap={blockRenderMap}
              />
            </Stack>
            <Stack backgroundColor="#F5F5F5" width="100%">
              <Typography
                sx={{
                  fontWeight: 400,

                  color: "#757575",
                  fontSize: "12px",
                  fontFamily: `"Roboto", sans-serif !important`,
                }}
              >
                Send us your <u>feedback!</u>
              </Typography>
              <Typography
                sx={{
                  fontWeight: 400,

                  color: "#1D1D11",
                  fontSize: "12px",
                  fontFamily: `"Roboto", sans-serif !important`,
                }}
              >
                Reply Directly to this email to comment , and CC teamsmates to
                add them as collaborators.
              </Typography>
              <Typography
                sx={{
                  fontWeight: 400,

                  color: "#757575",
                  fontSize: "12px",
                  fontFamily: `"Roboto", sans-serif !important`,
                }}
              >
                If you want to stop recieving notifications about this task ,
                you can
                <u>remove yourself from it.</u>
              </Typography>
            </Stack>
          </Paper>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default CompletePreview;
