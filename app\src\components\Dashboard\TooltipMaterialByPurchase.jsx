import { Box, Paper } from "@mui/material";
import React from "react";

const TooltipMaterialByPurchase = ({ active, payload, label }) => {
  if (active && payload && payload?.length) {
    return (
      <Box component={Paper} p={1}>
        <p className="desc">{`${label} : ${payload[0]?.value} ${payload[0]?.payload?.baseUnit}`}</p>
      </Box>
    );
  }
};

export default TooltipMaterialByPurchase;
