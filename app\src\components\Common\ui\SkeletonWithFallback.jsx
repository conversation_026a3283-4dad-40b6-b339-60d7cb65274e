import { useState, useEffect } from "react";
import { Skeleton, Typography } from "@mui/material";

const SkeletonWithFallback = ({ 
  fallback = "--", 
  variant = "text", 
  delay = 3000, 
  sx = { fontSize: '1rem' } 
}) => {
  const [showSkeleton, setShowSkeleton] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowSkeleton(false);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  return showSkeleton ? (
    <Skeleton variant={variant} sx={sx} />
  ) : (
    <Typography component="span" sx={sx}>
      {fallback}
    </Typography>
  );
};

export default SkeletonWithFallback;