import { Modelling } from '@cw/idm_qa';
import {useMemo } from 'react';
import {Box} from "@mui/material"
import { destination_IDM } from '../../../destinationVariables';
import { useSelector } from 'react-redux';

const ModellingComp = (props) => {

    const destinationsList = [

        {
          "Description": "",
          "Name": "WorkRulesServices",
          "URL": destination_IDM
        },
        {
          "Description": "",
          "Name": "CW_Worktext",
          "URL": destination_IDM
        },
        {
          "Description": "",
          "Name": "WorkRuleEngineServices",
          "URL": destination_IDM
        },
        {
          "Description": "",
          "Name": "WorkUtilsServices",
          "URL": destination_IDM
        }
    
      ]

      const userManagementState = useSelector((state)=>state?.userManagement)
      const userData = userManagementState?.userData
 
    let userDetails1 = {
        emailId: userData?.emailId,
        user_id: userData?.user_id
    }
    const modellingMemo = useMemo(() => (
        <Modelling translationDataObjects={[]} userDetails={userDetails1} destinations={destinationsList} />
    ) ,[userData]);
    return ( 
        
      <Box className='content' sx={{padding:"8px 16px"}}>
      <Box 
        sx={{
          height:"100%",
          "& .MuiSnackbar-root":{
            left:"50% !important"
          }
        }}
        >
          {modellingMemo}
      </Box>
  </Box>
       );
}
export default ModellingComp;