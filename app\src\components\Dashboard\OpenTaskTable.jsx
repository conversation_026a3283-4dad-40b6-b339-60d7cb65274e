import {
  LinearProgress,
  Stack,
  Box,
  Typography,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  Autocomplete,
  IconButton,
  Grid,
  FormControl,
  DialogActions,
  Button,
  TextField,
  Checkbox,
  ListItemText,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  container_table,
  container_tableHeader,
  font_Small,
  primary_Color,
} from "../common/commonStyles";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment/moment";
import { DataGrid } from "@mui/x-data-grid";
// import MyErrorBoundary from "../PurchaseOrder/Common/MyErrorBoundary";
import styled from "@emotion/styled";
import { useNavigate } from "react-router-dom";
import { v4 as uuidv4 } from "uuid";
import { commonFilterUpdate } from "../../app/commonFilterSlice";
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import CloseIcon from "@mui/icons-material/Close";
import { doAjax } from "../Common/fetchService";
import { destination_Dashboard } from "../../destinationVariables";
export const selectedOption = "Create";
export default function OpenTaskTable({
  tableRow,
  loader,
  count,
  tableHeader,
  selectedOption,
  setSelectedOption,
  pageName,
  handleSearch
}) {
  console.log(tableRow,tableHeader,"tableRow======")
  const dispatch = useDispatch();
  const StyledGridOverlay = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    marginRight: "10px",
    justifyContent: "center",
    height: "100%",
  }));
  /*var rows = [];
      for (let index = 0; index < tableRow?.length; index++) {
        var tempObj = tableRow[index];
        if (true) {
          var tempRow = {
            id: uuidv4(),
            createdBy: tempObj.createdBy,
            requestId: tempObj.requestId,
            createdOn: tempObj. createdOn,
            
          };
          rows.push(tempRow);
        }
      }*/
     
 // console.log(rows,"rowData")
 const dashboardSearchForm = useSelector(
  (state) => state?.commonFilter["Dashboard"]
);
  function CustomNoRowsOverlay() {
    return (
      <StyledGridOverlay>
        <Box sx={{ mt: 1 }}>No Data Available</Box>
      </StyledGridOverlay>
    );
  }
  
  const [openRequestType,setOpenRequestType]=useState(false)
  const [requestTypeOptions,setRequestTypeOptions]=useState([])
  const [reqtypeOptionSelected,setReqTypeOptionsSelected]=useState("Create")
  const [openRequestTypeSLA,setOpenRequestTypeSLA]=useState(false)
  const appSettings = useSelector((state) => state.appSettings);
  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };
  const navigate = useNavigate();
  useEffect(() => {

    getReqTypeOptions()
    
  }, []);

  const getReqTypeOptions =()=>{

    const hSuccess3 = (data) => {
      console.log(data, "dataofdropdown1");
      //setReqTypeOptions(data.body)
      let req_type_arr = data?.body
      // data?.body.map((req_type, i) => {
      //   let req_type_hash = {}
      //   req_type_hash["id"] = i + 1
      //   req_type_hash["label"] = req_type
      //   req_type_arr.push(req_type_hash)
      // })
      
      //req_type_arr.push({ "id": "all", "label": "All" })
      console.log(req_type_arr, "req_type_arr1")
      setRequestTypeOptions(req_type_arr)
      
    };

    const hError3 = () => { };
      doAjax(
        `/${destination_Dashboard}/GraphConfig/getReqTypes?module=${dashboardSearchForm?.dashBoardModuleName ? dashboardSearchForm?.dashBoardModuleName : "Cost Center"}`,
        "get",
        hSuccess3,
        hError3,

      );

  }

  const handleOptionChange = (event) => {
    setSelectedOption(event.target.value);
    console.log(event.target.value, "requestDropDownOptions")

    let tempDataForTaskTableOptions=event.target.value
    dispatch(
      commonFilterUpdate({
        module: "Dashboard",
        filterData: {
          ...dashboardSearchForm,
          requestDropDownOptions: tempDataForTaskTableOptions,
        },
      })
    );
  };


  let header_arr=[]
  tableHeader?.map((header_item)=>{
    let header_hash={}
    header_hash["field"]=header_item
    header_hash["headerName"]=header_item
    header_hash["width"]=150
    header_hash["headerAlign"]="left"
    header_hash["align"]="left"
    header_arr.push(header_hash)
  })

  console.log(header_arr,"header_arr")

  const columns = [
    {
      field: "requestId",
      headerName: "Request Id",
      width: 190,
      headerAlign: "left",
      align:"left",
    },
    {
      field: "createdOn",
      headerName: "Created At",
      editable: false,
      flex: 1,
      align: "left",
      headerAlign: "left",
      // renderCell: (params) => (
      //   <Typography variant="body2">
      //     {moment(params.row.createdAt).format(appSettings.date)}
      //   </Typography>
      // ),
      renderCell:(params)=>{
        return(<Typography sx={{fontSize:"12px"}}>{moment(params.row.createdOn).format(appSettings?.dateFormat)}</Typography>)
      }
    },
    {
      field: "createdAt",
      headerName: "Created By",
      editable: false,
      flex: 1,
      align: "left",
      headerAlign: "left",
    },
  ];
  console.log(container_table,"container_table")
  const openRequestTypeOption =() =>{
    //alert("coming")
    setOpenRequestType(true)
  }
  const openRequestTypeOptionSla =() =>{
    //alert("coming")
    setOpenRequestTypeSLA(true)
  }
  // const handleRequestTypeOption =(e, value) =>{
  //   if (true) {
  //     let temprequestType = value
  //     //console.log(temprequestType, "temprequestType")
  //     setReqTypeOptionsSelected(temprequestType)
  //     // if (value.label.length > 0) {
  //     //   //alert("coming")
  //     //   //setReqStatusOptions([])
  //     //   /*dispatch(
  //     //     commonFilterUpdate({
  //     //       module: "Dashboard",
  //     //       filterData: {
  //     //         ...dashboardSearchForm,
  //     //         selectedRequestStatus: "tyueh",
  //     //       },
  //     //     })
  //     //   );*/
  //     //   setRequestStatusOptionsDisabled(true)
  //     //   //requestStatusOptions
  //     //   setSelectedRequestStatus({ "id": "All", "label": "All" })

  //     // } else {
  //     //   setRequestStatusOptionsDisabled(false)
  //     // }
  //     dispatch(
  //       commonFilterUpdate({
  //         module: "Dashboard",
  //         filterData: {
  //           ...dashboardSearchForm,
  //           selectedRequestTypeSLA: temprequestType,
            
  //         },
  //       })
  //     );
  //   }
  // }
  const handleRequestTypeOption = (e, value) => {
    if (e.target.value !== null) {
      let temprequestType = e.target.value
      //console.log(tempusersId[0].split('-')[0],"tempusersId")
      //setRequestTypeOptions(temprequestType)
      dispatch(
        commonFilterUpdate({
          module: "Dashboard",
          filterData: {
            ...dashboardSearchForm,
            selectedRequestTypeSLA: temprequestType,

          },
        })
      );

    }
  };
  const handleRequestTypeOptionSLA = (e, value) => {
    if (e.target.value !== null) {
      let temprequestType = e.target.value
      //console.log(tempusersId[0].split('-')[0],"tempusersId")
      //setRequestTypeOptions(temprequestType)
      dispatch(
        commonFilterUpdate({
          module: "Dashboard",
          filterData: {
            ...dashboardSearchForm,
            selectedRequestTypeSLATable: temprequestType,

          },
        })
      );

    }
};

  const handleClose = () => {
    setOpenRequestType(false);
  };
  const handleCloseSLA =() =>{
    setOpenRequestTypeSLA(false);
  }
  const handleProceedbutton =() =>{
    setOpenRequestType(false);
    handleSearch()
  }
  const handleProceedbuttonSLA =() =>{
    setOpenRequestTypeSLA(false);
    handleSearch()
  }
  
  return (
    <>
      <>
      <div>
        <Dialog
          open={openRequestType}
          //onClose={handleClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              // borderBottom: "1px solid grey",
              display: "flex",
            }}
          >
            <Typography variant="h6">Add Filter</Typography>

            <IconButton
              sx={{ width: "max-content" }}
              onClick={handleClose}
              children={<CloseIcon />}
            />
          </DialogTitle>
          <DialogContent>
            <Grid
              item
              md={6}
              sx={{ width: "100%", marginTop: ".5rem" }}
            >
              <Typography>
                REQUEST TYPE
                
              </Typography>
              {/* <FormControl
                fullWidth
                sx={{ margin: ".5em 0px", minWidth: "250px" }}
              >
                <Autocomplete
                  sx={{ height: "31px" }}
                  fullWidth
                  size="small"
                  onChange={handleRequestTypeOption}
                  value={reqtypeOptionSelected}

                  options={requestTypeOptions}
                  getOptionLabel={(option) => {
                    if (option?.id) return `${option?.label}` ?? "";
                    else return "";
                  }}

                  renderOption={(props, option) => (
                    <li {...props}>
                      <Typography style={{ fontSize: 12 }}>
                        {`${option?.label}`}
                      </Typography>
                    </li>
                  )}
                  renderInput={(params) => (
                    <TextField
                      sx={{ fontSize: "12px !important" }}
                      {...params}
                      variant="outlined"
                      placeholder="SELECT REQUEST TYPE"
                    />
                  )}

                />
              </FormControl> */}
              <FormControl fullWidth
                //size="small"
                sx={{ margin: ".5em 0px", minWidth: "250px",maxWidth: "250px" }}
                >
                <Select
                  placeholder={"Select Request Type"}
                  multiple
                  size="small"
                  value={dashboardSearchForm?.selectedRequestTypeSLA}
                  name="moduleName"
                  onChange={(e) => handleRequestTypeOption(e)}
                  //displayEmpty={true}
                  // input={<OutlinedInput label="Tag" />}
                  //disabled={requestTypeOptionsDisabled}
                  renderValue={(selected) => selected.join(", ")}
                  MenuProps={MenuProps}
                >
                  <MenuItem sx={font_Small}  value={""}>
                    <div
                      style={{ color: "#C1C1C1", fontSize: "12px" }}
                    >
                      SELECT REQUEST TYPE
                    </div>
                  </MenuItem>
                  {requestTypeOptions?.map((name) => (
                    <MenuItem
                      //sx={font_Small}
                      
                      key={name}
                      value={name}
                      style={{ fontSize: "12px !important" }}
                    >
                      <Checkbox
                        checked={dashboardSearchForm?.selectedRequestTypeSLA.indexOf(name) > -1
                        }
                      />
                      <ListItemText
                        //sx={font_Small}
                        primary={name}
                        style={{ fontSize: "12px !important" }}
                      />
                    </MenuItem>
                  ))}


                </Select>


              </FormControl>

            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose}>Cancel</Button>
            <Button onClick={handleProceedbutton} autoFocus>
              Proceed
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog
          open={openRequestTypeSLA}
          //onClose={handleClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              // borderBottom: "1px solid grey",
              display: "flex",
            }}
          >
            <Typography variant="h6">Add Filter</Typography>

            <IconButton
              sx={{ width: "max-content" }}
              onClick={handleCloseSLA}
              children={<CloseIcon />}
            />
          </DialogTitle>
          <DialogContent>
            <Grid
              item
              md={6}
              sx={{ width: "100%", marginTop: ".5rem" }}
            >
              <Typography>
                REQUEST TYPE
                
              </Typography>
              <FormControl fullWidth
                //size="small"
                sx={{ margin: ".5em 0px", minWidth: "250px",maxWidth: "250px" }}
                >
                <Select
                  placeholder={"Select Request Type"}
                  multiple
                  size="small"
                  value={dashboardSearchForm?.selectedRequestTypeSLATable}
                  name="moduleName"
                  onChange={(e) => handleRequestTypeOptionSLA(e)}
                  //displayEmpty={true}
                  // input={<OutlinedInput label="Tag" />}
                  //disabled={requestTypeOptionsDisabled}
                  renderValue={(selected) => selected.join(", ")}
                  MenuProps={MenuProps}
                >
                  <MenuItem sx={font_Small}  value={""}>
                    <div
                      style={{ color: "#C1C1C1", fontSize: "12px" }}
                    >
                      SELECT REQUEST TYPE
                    </div>
                  </MenuItem>
                  {requestTypeOptions?.map((name) => (
                    <MenuItem
                      //sx={font_Small}
                      
                      key={name}
                      value={name}
                      style={{ fontSize: "12px !important" }}
                    >
                      <Checkbox
                        checked={dashboardSearchForm?.selectedRequestTypeSLATable.indexOf(name) > -1
                        }
                      />
                      <ListItemText
                        //sx={font_Small}
                        primary={name}
                        style={{ fontSize: "12px !important" }}
                      />
                    </MenuItem>
                  ))}


                </Select>


              </FormControl>

            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseSLA}>Cancel</Button>
            <Button onClick={handleProceedbuttonSLA} autoFocus>
              Proceed
            </Button>
          </DialogActions>
        </Dialog>

        </div>
        {pageName === 'senario'?
        <Box sx={container_table}>
          <div className="reusable-table" style={{ position: "relative" }}>
            <Stack
              justifyContent="space-between"
              direction="row"
              sx={container_tableHeader}
            >
              <Typography fontSize={"12px"} fontWeight={"bold"} marginTop={"10px"}>
                List of Top{" "}
                {/* {selectedOption.charAt(0).toUpperCase() +
                  selectedOption.slice(1).toLowerCase()}{" "} */}
                Requests ({count})
              </Typography>
              <Typography >
                <FilterAltIcon
                  sx={{
                    fontSize: "20px", cursor: "pointer",
                    marginTop:"9px"
                  }}
                  onClick={openRequestTypeOption}
                />
              </Typography>
              <Typography fontSize={"10px"} >
              {/* <Select value={selectedOption} onChange={handleOptionChange}  style={{ minWidth: "300px" }} size="small" >
                <MenuItem value="Create"  style={{ width: "80px" }}>Create</MenuItem>
                <MenuItem value="Change"  style={{ width: "80px" }}>Change</MenuItem>
                <MenuItem value="Extend"  style={{ width: "80px" }}>Extend</MenuItem>
                <MenuItem value="Mass Change"  style={{ width: "80px" }}>Mass Change</MenuItem>
                <MenuItem value="Mass Create"  style={{ width: "80px" }}>Mass Create</MenuItem>
              </Select> */}
              <Select value={selectedOption} onChange={handleOptionChange}  style={{ minWidth: "300px",maxWidth:"300px" }} size="small" >
                <MenuItem value="1"  style={{ maxWidth: "300px" }}>Who created the most number of requests?</MenuItem>
                <MenuItem value="2"  style={{ maxWidth: "300px" }}>Whose requests have been sent for correction <br></br>maximum times?</MenuItem>
                <MenuItem value="3"  style={{ maxWidth: "300px" }}>Who approved maximum number of Requests?</MenuItem>
                <MenuItem value="4"  style={{ maxWidth: "300px" }}>Who delays the most as per requestor?</MenuItem>
                <MenuItem value="5"  style={{ maxWidth: "300px" }}>Top 5 Requests who have breached SLA?</MenuItem>
              </Select>
              </Typography>
              
            </Stack>
            <DataGrid
              loading={loader}
              getRowId={(tableRow) => tableRow["Email Id"]}
              rows={tableRow ?? []}
              width="100%"
              autoHeight
              rowHeight={56}
              disableSelectionOnClick
              columns={header_arr}
              disableExtendRowFullWidth={false}
              hideFooter={true}
              marginRight={10}
              
              sx={{
                "& .MuiDataGrid-row:hover": {
                  backgroundColor: "#EAE9FF40",
                },
                backgroundColor: "#fff",
              }}
              components={{
                LoadingOverlay: LinearProgress,
                NoRowsOverlay: CustomNoRowsOverlay,
              }}
            />
          </div>
        </Box>:<Box sx={{marginTop:"-200px"}}>
          <div className="reusable-table" style={{ position: "relative" }}>
            <Stack
              justifyContent="space-between"
              direction="row"
              sx={container_tableHeader}
            >
              <Typography fontSize={"12px"} fontWeight={"bold"} marginTop={2}>
                Top 5 SLA Breached Users
              </Typography>
              
              <Typography >
                <FilterAltIcon
                  sx={{
                    fontSize: "20px", cursor: "pointer",
                    marginTop:"9px"
                  }}
                  onClick={openRequestTypeOptionSla}
                />
              </Typography>
            </Stack>
            <DataGrid
              loading={loader}
              getRowId={(tableRow) => tableRow["Email"]}
              rows={tableRow ?? []}
              width="100%"
              autoHeight
              rowHeight={56}
              disableSelectionOnClick
              columns={tableHeader}
              disableExtendRowFullWidth={false}
              hideFooter={true}
              marginRight={10}
              
              sx={{
                "& .MuiDataGrid-row:hover": {
                  backgroundColor: "#EAE9FF40",
                },
                backgroundColor: "#fff",
              }}
              components={{
                LoadingOverlay: LinearProgress,
                NoRowsOverlay: CustomNoRowsOverlay,
              }}
            />
          </div>
        </Box>}
      </>
    </>
  );
}
