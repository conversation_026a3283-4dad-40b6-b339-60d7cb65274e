import {
  Autocomplete,
  Box,
  Button,
  CardContent,
  Checkbox,
  Divider,
  Grid,
  Paper,
  Select,
  Stack,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  button_Outlined,
  container_Padding,
  container_columnGap,
} from "../Common/commonStyles";
import { destination_MaterialMgmt } from "../../destinationVariables";
import { Table } from "rsuite";
import { useSelector } from "react-redux";
import { doAjax } from "../Common/fetchService";

const ClassificationTabs = (props) => {
  const [numOfIcon, setNumOfIcons] = useState(1);
  const PayloadData = useSelector((state) => state.payloadS);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);

  return (
    <div style={{ display: "flex", flexDirection: "column", width: "100%" }}>
      {new Array(numOfIcon).fill(1).map((item) => {
        return (
          <Grid>
            <Grid
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                ...container_Padding,
                ...container_columnGap,
              }}
            >
              <Grid>
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                  }}
                >
                  Classification Data
                </Typography>
              </Grid>
              <Grid>
                <Grid marginBottom={5} display="flex">
                  <Grid width="50%">
                    <Grid item md={6} mt={1}>
                      <Stack>
                        <Typography variant="body2" color="#777">
                          Class Type:
                        </Typography>
                        <Autocomplete
                          sx={{ height: "31px" }}
                          fullWidth
                          size="small"
                          value={props?.basicData?.ClassType}
                          onChange={(e, value) => {
                            props?.setBasicData({
                              ...props?.basicData,
                              ClassType: value,
                            });
                          }}
                          options={dropDownData?.ClassType ?? []}
                          getOptionLabel={(option) => `${option?.Class} `}
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {option?.Class}
                              </Typography>
                            </li>
                          )}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              variant="outlined"
                              placeholder="Select Class Type"
                            />
                          )}
                        />
                      </Stack>
                    </Grid>
                    <Grid item md={6} mt={1}>
                      <Stack>
                        <Typography variant="body2" color="#777">
                          Class:
                        </Typography>
                        <Autocomplete
                          sx={{ height: "31px" }}
                          fullWidth
                          size="small"
                          disablePortal
                          value={props.basicData?.matGroup}
                          onChange={(e, value) => {
                            props?.setBasicData({
                              ...props?.basicData,
                              matGroup: value,
                            });
                          }}
                          options={props?.dropDownData?.matGroup ?? []}
                          getOptionLabel={(option) =>
                            `${option?.MaterialGroup} - ${option?.MatlGrpDesc}`
                          }
                          renderOption={(props, option) => (
                            <li {...props}>
                              <Typography style={{ fontSize: 12 }}>
                                {option?.MaterialGroup} - {option?.MatlGrpDesc}
                              </Typography>
                            </li>
                          )}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              variant="outlined"
                              placeholder="Select Class"
                            />
                          )}
                        />
                      </Stack>
                    </Grid>
                  </Grid>

                  <Divider orientation="vertical" flexItem></Divider>
                  <Grid container sx={{ ml: "30px" }}>
                    <Typography
                      sx={{
                        fontSize: "12px",
                        fontWeight: "700",
                        mb: 2,
                      }}
                    >
                      Values for {props?.basicData?.ClassType?.Class}
                    </Typography>

                    <Grid container display="flex" flexDirection="row">
                      <Grid item width="40%">
                        <Typography variant="body2" color="#777">
                          Characteristic Description
                        </Typography>
                      </Grid>

                      <Grid item width="40%">
                        <Typography variant="body2" color="#777">
                          Value
                        </Typography>
                      </Grid>
                    </Grid>

                    <Divider
                      sx={{ width: "60%" }}
                      orientation="horizontal"
                    ></Divider>

                    <Grid container marginTop={2} display="flex">
                      <Grid item width="40%">
                        <Typography
                          variant="body2"
                          color="#777"
                          fontWeight="bold"
                        >
                          Expiration Date Shelf Number
                        </Typography>
                      </Grid>
                      <Grid item>
                        <TextField
                          size="small"
                          placeholder="Enter Value"
                          onChange={(e) => {
                            props.setBasicData((prev) => {
                              return {
                                ...prev,
                                oldMaterialGroup: e.target.value,
                              };
                            });
                          }}
                        />
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        );
      })}

      <Button
        variant="outlined"
        sx={{ button_Outlined, mt: 2, alignSelf: "flex-end" }}
        onClick={() => {
          setNumOfIcons(numOfIcon + 1);
        }}
      >
        Add
      </Button>
    </div>
  );
};

export default ClassificationTabs;
