import React, {
  useEffect,
  useState,
  useMemo,
  useImperativeHandle,
  forwardRef,
} from "react";
import {
  Box,
  Tabs,
  Tab,
  Grid,
  TextField,
  Paper,
  MenuItem,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@mui/material";
import useProfitCenterFieldConfig from "../../../hooks/UseProfitCenterFieldConfig";
import { useSelector } from "react-redux";
import SegmentDropdown from "../../Common/SegmentDropdown";
import CountryDropdown from "../../Common/CountryDropdown";
import RegionDropdown from "../../Common/RegionDropdown";
import TaxjurDropdown from "../../Common/TaxjurDropdown";
import LanguageDropdown from "../../Common/LanguageDropdown";
import UserDropdown from "../../Common/UserDropdown";

const TabsDetailsPC = forwardRef(
  (
    {
      shortDescription,
      setShortDescription,
      profitCenterTabs,
      selectedRowId,
      selectedCompanyCode,
      companyDescription,
      setSelectedCompanyCode,
    },
    ref
  ) => {
    const [tabIndex, setTabIndex] = useState(0);
    const [localFormData, setLocalFormData] = useState([]);

    console.log("localFormData", localFormData);
    console.log("tabIndex", tabIndex);

    const { loading, error, fetchProfitCenterFieldConfig } = useProfitCenterFieldConfig();

    // Filter out "Initial Screen" tab
    const filteredTabs = useMemo(
      () => profitCenterTabs.filter((tab) => tab.tab !== "Initial Screen"),
      [profitCenterTabs]
    );

    const tabNames = useMemo(
      () => filteredTabs.map((tab) => tab.tab),
      [filteredTabs]
    );

    const selectedTab = useMemo(
      () => filteredTabs[tabIndex] || {},
      [tabIndex, filteredTabs]
    );
    const formData = useMemo(() => selectedTab.data || {}, [selectedTab]);

    console.log("formData", formData);

    // Initialize state when visiting a new tab
    // useEffect(() => {
    //   setLocalFormData((prevData) => ({
    //     ...prevData,
    //     [tabIndex]: prevData[tabIndex] || formData,
    //   }));
    // }, [tabIndex, formData]);

    useEffect(() => {
      setLocalFormData((prevData) => ({
        ...prevData,
        [tabIndex]: {
          ...(prevData[tabIndex] || {}),
          CompanyCode: selectedCompanyCode, // Ensure tab reflects the selected company code
        },
      }));
    }, [selectedCompanyCode, tabIndex]);
    // useEffect(() => {
    //   // Reset all local tab form data when row changes
    //   setLocalFormData({});
    //   setTabIndex(0); // optional: reset to first tab
    // }, [selectedRowId]);

    useEffect(() => {
      fetchProfitCenterFieldConfig();
    }, []);

    useImperativeHandle(ref, () => ({
        validateAllFields: () => {
          let missingFields = [];
      
          filteredTabs.forEach((tab, index) => {
            const tabData = localFormData[index] || {};
            Object.entries(tab.data || {}).forEach(([cardName, fields]) => {
              fields.forEach((field) => {
                if (
                  field.visibility === "Mandatory" &&
                  (!tabData[field.jsonName] ||
                    tabData[field.jsonName]?.trim() === "")
                ) {
                  missingFields.push(
                    `${field.fieldName} is required in ${tab.tab} > ${cardName}`
                  );
                }
              });
            });
          });
      
          return missingFields;
        },
      
        getTabData: () => localFormData, // ✅ Expose tab data for debugging

        resetFields: () => {
            const resetFormData = [...localFormData];
            
        
            filteredTabs.forEach((tab, index) => {
              const tabFields = {};
              Object.entries(tab.data || {}).forEach(([_, fields]) => {
                fields.forEach((field) => {
                  tabFields[field.jsonName] = ""; // or null, depending on your use case
                });
              });
              resetFormData[index] = tabFields;
            });
        
            setLocalFormData(resetFormData);
          },
      }));
      

    const handleTabChange = (event, newIndex) => {
      setTabIndex(newIndex);
    };

    // Handle user input
    const handleInputChange = (fieldName) => (event) => {
      const newValue = event.target.value;
      console.log("newValue", newValue); // ✅ Log when input changes

      setLocalFormData((prev) => ({
        ...prev,
        [tabIndex]: {
          ...(prev[tabIndex] || {}),
          [fieldName]: newValue,
        },
      }));
    };

    if (loading) return <Box>Loading...</Box>;
    if (error) return <Box>Error loading data</Box>;

    return (
      <Box sx={{ width: "100%", p: 2 }}>
        {/* Tabs Section */}
        <Tabs value={tabIndex} onChange={handleTabChange}>
          {tabNames.map((tabName, index) => (
            <Tab key={index} label={tabName} />
          ))}
        </Tabs>

        {/* Render selected tab's content */}
        <Paper elevation={3} sx={{ p: 3, mt: 2 }}>
          <Box sx={{ fontSize: "1.2rem", fontWeight: "bold", mb: 2 }}>
            {selectedTab.tab}
          </Box>

          {Object.entries(formData).map(([cardName, fields]) => (
            <Paper key={cardName} elevation={2} sx={{ p: 2, mb: 2 }}>
              <Box sx={{ fontSize: "1rem", fontWeight: "bold", mb: 2 }}>
                {cardName}
              </Box>
              <Grid container spacing={2}>
                {fields.map((field) => (
                  <Grid key={field.jsonName} item xs={12} md={4}>
                    {/* Render SegmentDropdown for "Segment" field */}
                    {field.jsonName === "Segment" ? (
                      <SegmentDropdown
                        formData={localFormData[tabIndex] || {}}
                        handleChange={handleInputChange}
                      />
                    ) : field.jsonName === "Country" ? (
                      <CountryDropdown
                        formData={localFormData[tabIndex] || {}}
                        handleChange={handleInputChange}
                      />
                    ) : field.jsonName === "Regio" ? (
                      <RegionDropdown
                        formData={localFormData[tabIndex] || {}}
                        handleChange={handleInputChange}
                        selectedCountry={localFormData[tabIndex]?.Country}
                      />
                    ) : field.jsonName === "TaxJurisdiction" ? (
                      <TaxjurDropdown
                        formData={localFormData[tabIndex] || {}}
                        handleChange={handleInputChange}
                      />
                    ) : field.jsonName === "Language" ? (
                      <LanguageDropdown
                        formData={localFormData[tabIndex] || {}}
                        handleChange={handleInputChange}
                      />
                    ) : field.jsonName === "UserResponsible" ? (
                      <UserDropdown
                        formData={localFormData[tabIndex] || {}}
                        handleChange={handleInputChange}
                      />
                    ) : field.jsonName === "PrctrHierGrp" ? (
                      <TextField
                        fullWidth
                        label={<>{field.fieldName}</>}
                        value={field.value}
                      />
                    ) : field.jsonName === "CompanyCode" ? (
                      <TextField
                        fullWidth
                        label="Company Code"
                        value={localFormData[tabIndex]?.CompanyCode || ""}
                        disabled // Disable editing since it's inherited from the table
                      />
                    ) : field.jsonName === "CompanyName" ? (
                      <TextField
                        fullWidth
                        label="Company Description"
                        value={companyDescription}
                        disabled // Disable editing since it's inherited from the table
                      />
                    ) : (
                      <>
                        {field.fieldType === "Radio Button" && (
                          <FormControl component="fieldset" fullWidth>
                            <FormLabel>
                              {field.fieldName}{" "}
                              {field.visibility === "Mandatory" && (
                                <span style={{ color: "red" }}>*</span>
                              )}
                            </FormLabel>
                            <RadioGroup
                              row
                              value={
                                localFormData[tabIndex]?.[field.jsonName] || ""
                              }
                              onChange={handleInputChange(field.jsonName)}
                            >
                              <FormControlLabel
                                value="Yes"
                                control={<Radio />}
                                label="Yes"
                              />
                              <FormControlLabel
                                value="No"
                                control={<Radio />}
                                label="No"
                              />
                            </RadioGroup>
                          </FormControl>
                        )}

                        {/* Input Field */}
                        {field.fieldType === "Input" && (
                          <TextField
                            fullWidth
                            label={
                              <>
                                {field.fieldName}{" "}
                                {field.visibility === "Mandatory" && (
                                  <span style={{ color: "red" }}>*</span>
                                )}
                              </>
                            }
                            value={
                              localFormData[tabIndex]?.[field.jsonName] || ""
                            }
                            onChange={handleInputChange(field.jsonName)}
                          />
                        )}

                        {/* Calendar Field */}
                        {field.fieldType === "Calendar" && (
                          <TextField
                            fullWidth
                            label={field.fieldName}
                            value={field.value}
                            InputProps={{ readOnly: true }} // Makes it uneditable
                          />
                        )}

                        {/* Drop Down Field (General Dropdowns) */}
                        {field.fieldType === "Drop Down" &&
                          field.jsonName !== "segment" && (
                            <TextField
                              select
                              fullWidth
                              label={field.fieldName}
                              value={
                                localFormData[tabIndex]?.[field.jsonName] || ""
                              }
                              onChange={handleInputChange(field.jsonName)}
                            >
                              {field.options?.map((option) => (
                                <MenuItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.label}
                                </MenuItem>
                              ))}
                            </TextField>
                          )}
                      </>
                    )}
                  </Grid>
                ))}
              </Grid>
            </Paper>
          ))}
        </Paper>
      </Box>
    );
  }
);

export default TabsDetailsPC;


