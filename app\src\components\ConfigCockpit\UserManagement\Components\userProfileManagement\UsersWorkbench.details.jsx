import {
  Backdrop,
  BottomNavigation,
  Box,
  Button,
  Card,
  CardContent,
  FormControl,
  Grid,
  IconButton,
  MenuItem,
  Paper,
  Select,
  Stack,
  TextField,
  Tooltip,
  Typography,
  CircularProgress,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import SearchBar from "../../../../common/SearchBar";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";

import {
  button_Primary,
  outermostContainer_Information,
  outermostContainer,
  font_Small,
  iconButton_SpacingSmall,
  button_Marginleft,
  button_MarginRight,
  button_Outlined
} from "../../../../common/commonStyles";
import EditUserDetails from "./EditUserDetails";
import ReusableTable from "../../../../common/ReusableTable";
import axios from "axios";
import { useSelector } from "react-redux";
import { destination_Admin, destination_DocumentManagement, destination_ManageAccount, destination_PR, destination_Po } from "../../../../../destinationVariables";
import { useNavigate, useParams } from "react-router-dom";
import { useDispatch } from "react-redux";
import { clearLastPath, setHistoryPath, setRedirectionFilter } from "../../../../../app/utilitySlice";
import { v4 as uuidv4 } from "uuid";
import moment from "moment";
import { doAjax } from "../../../../common/fetchService";
import ReusableDialog from "../../../../common/ReusableDialog";
import ReusableSnackBar from "../../../../common/ReusableSnackBar";
import { MatView } from "../../../../DocumentManagement/UtilDoc";
import { capitalize } from "../../../../../functions";

const UsersWorkbenchDetailsPage = () => {
  const [isLoading, setIsLoading] = useState(true);
  //<--Varibles for user data fields
  const [supplierDetails, setsupplierDetails] = useState({});
  const [companyDetails_Data, setcompanyDetails_Data] = useState({});
  const [addressAndContact, setaddressAndContact] = useState({});
  const [bankInfo_Data, setbankInfo_Data] = useState({});
  const [registration_Data, setregistration_Data] = useState({});
  const [additionalContact_Data, setadditionalContact_Data] = useState([]);
  const [documentDetails_Data, setdocumentDetails_Data] = useState([]);
  const [taskStatus, settaskStatus] = useState(null);

  let utilityReduxStore = useSelector((state) => state.utility);
  let navigate = useNavigate();
  let dispatch = useDispatch();
  let { confId } = useParams();
  // const [userDetails, setuserDetails] = useState({})
  // const [userDetails_Prev, setUserDetails_Prev] = useState({})

  const [supplierDetails_PrevData, setsupplierDetails_PrevData] = useState({});
  const [companyDetails_PrevData, setcompanyDetails_PrevData] = useState({});
  const [addressAndContact_PrevData, setaddressAndContact_PrevData] = useState(
    {}
  );
  const [bankInfo_PrevData, setbankInfo_PrevData] = useState([]);
  const [registration_PrevData, setregistration_PrevData] = useState([]);
  const [additionalContact_PrevData, setadditionalContact_PrevData] = useState(
    []
  );
  const [documentDetails_PrevData, setdocumentDetails_PrevData] = useState([]);
  const [reusableDialog_Ref, setreusableDialog_Ref] = useState('')

  const [warning_Notification, setwarning_Notification] = useState({
    currentNotification: "",
    success: true,
    open: false,
    title: "",
    severity: "",
  });
  const [Success_Notification, setSuccess_Notification] = useState({
    currentNotification: "",
    success: true,
    open: false,
    title: "",
    severity: "",
  }); 
  const functions_ReusableDialogBox = {
    MessageDialogCancel: () => {
      setwarning_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
    },
    MessageDialogClickOpen: () => {
      setwarning_Notification((prev) => ({ ...prev, open: true }));
      // setOpenMessageDialog(true);
    },

    MessageDialogClose: () => {
      setwarning_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));

      setSuccess_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
    },
    messageDialogCloseAndRedirect: () => {
      setwarning_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
      setSuccess_Notification((prev) => ({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      }));
      // navigate('/manageAccount')
    },
    getHandleOkFunction:()=>{
      switch(reusableDialog_Ref){
        case 'CONFIRMSUBMIT':
          setwarning_Notification((prev) => ({
            open: false,
            currentNotification: "",
            success: "",
            title: "",
            severity: "",
          }));
          handleApproveReject('Approve')
          break;
        case 'ERROR':
          functions_ReusableDialogBox.MessageDialogClose()
          break;
        case 'CONFIRMREJECT':
          setwarning_Notification((prev) => ({
            open: false,
            currentNotification: "",
            success: "",
            title: "",
            severity: "",
          }));
          handleApproveReject('Reject')
        default :
        functions_ReusableDialogBox.MessageDialogClose()
      }
    },
    viewOkButton:()=>{
      // console.log(reusableDialog_Ref,'ref')
      switch(reusableDialog_Ref){
        case 'CONFIRMSUBMIT':
          return true
          break;
        case 'ERROR':
          return false
          break;
        case 'CONFIRMREJECT':
          return true
        default :
       return false
      }
    },
    viewCancelButton: ()=>{
      switch(reusableDialog_Ref){
        case 'CONFIRMSUBMIT':
          return true
          break;
        case 'ERROR':
          return false
          break;
          case 'CONFIRMREJECT':
         return true
        default :
       return false
      }
    },
    getOkButtonText:()=>{
      switch(reusableDialog_Ref){
        case 'CONFIRMSUBMIT':
          return 'OK'
          break;
        case 'ERROR':
          return 'OK'
        case 'CONFIRMREJECT':
           return 'OK'
        default :
       return ''
      }
    },
    getHandleCancleFunction:()=>{
      switch(reusableDialog_Ref){
        case 'CONFIRMSUBMIT':
          return functions_ReusableDialogBox.MessageDialogClose()
        case 'ERROR':
          return ()=>{}
        case 'CONFIRMREJECT':
           return functions_ReusableDialogBox.MessageDialogClose()
        default :
       return ()=>{}
      }
    }
  };


  let userDetails;
  let userDetails_Prev;
  let companyCodes;
  let countryList
  let regionList

  const fetchUserPrevDetails = async (supplierId) => {
   await doAjax(
      `/${destination_ManageAccount}/userManagement/getUserDetailsFromOdata/vendorId/${supplierId}`,'get',(data)=>{
        userDetails_Prev = data
      }
    );

    let data = userDetails_Prev.d.results[0];
    await doAjax(`/${destination_PR}/Odata/Region/${data.Country}`,`GET`,(res)=> regionList = res)

    let profileData = {
      bankInfo: {
        bankName: data.ToBank?.results[0]?.BankAccount,
        bankCountry:'',
        accountNumber:
        data.ToBank?.results[0]?.AccountNumber ??
          data.ToBank?.results[0]?.IBAN,
        accountHolderName: data.ToBank?.results[0]?.Accountholder,
        swiftCodeBankKey: data.ToBank?.results[0]?.Banknumber,
      },
      registration: {
        country: `${data.Country} - ${countryList[data.Country]}`,
        taxRegistrationId: data.TaxNumber,
        type: data.taxtype,
        ifOther:''
      },
      companyDetails: {
        companyName: `${data.ToCompanyData?.results[0]?.CompanyCode} - ${
          companyCodes[data.ToCompanyData?.results[0]?.CompanyCode]
        }`,
        areaOfBusiness: "",
        currency: "",
        registeredCapital: "",
        totalEmployee: "",

        street1: data.Street,
        street2: data.Street2,
        street3: data.Street3,
        street4: data.Street4,
        street5: data.Street5,
        city: data.City,
        country: `${data.Country} - ${countryList[data.Country]}`,
        fax: data.ToFax?.results[0]?.Fax,
        companyPhone: data.ToPhone?.results[0]?.Telephoneno,
        companyWebsite: "",
        region: `${data.Region} - ${regionList[data.Region]}`,
        productCategory: "",
        certifications: "",
        groupOfCompany: "",
        pinCode: data.PostalCode,
        supplierName: data?.Name,
        supplierType: "",
        partnerType: "",
        supplierEmail: data.ToEmail?.results[0]?.EMailAddress,
        supplierNumber: supplierId,
      },
      additionalContacts: [],
    };
 
    let returnValue = (data) => {
      //check the values
      //check if values are not === null, undefined, ''
      //return [] if all values are null or undefined or ""
      // debugger
      let list = Object.values(data);
      let count = 0;
      list.forEach((item) => {
        if (item !== undefined || "" || null) count++;
      });
      return count === 0 ? [] : [data];
    };

    setbankInfo_PrevData(returnValue(profileData.bankInfo));
    setcompanyDetails_PrevData(profileData.companyDetails);
    setregistration_PrevData(returnValue(profileData.registration));
    setsupplierDetails_PrevData(profileData.supplierDetails);
    // setaddressAndContact_PrevData(profileData.addressAndContact);
    setadditionalContact_PrevData([]);

  };
  const fetchUserUpdatedData = async () => {
    fetchAttachments()

    let hSuccess = (res)=>{
      userDetails = res.userDetailDto;
    }
    await doAjax(
      `/${destination_ManageAccount}/userManagement/getTaskById/${confId}`,'get',hSuccess
      );
    await fetchUserPrevDetails(userDetails?.companyInformationDo?.supplierNumber);

    let addIdToData = (data) => {
      if (data) {
        return data.map((item) => {
          item.id = uuidv4();
          return item;
        });
      }
      return [];
    };
    let userData = {
      companyDetails_Data: userDetails.companyInformationDo,
      bankInfo_Data: addIdToData(userDetails.bankDo),
      registration_Data: addIdToData(userDetails.taxDo),
      additionalContact_Data: addIdToData(userDetails.contactDo),
      documentDetails_Data: addIdToData(userDetails.documentDetailsDo),

    };
    // userData.bankInfo_Data.country = userData.bankInfo_Data.country?countryList[userData.bankInfo_Data.country]:""
    
    settaskStatus(userDetails.status)
    setcompanyDetails_Data({...userData.companyDetails_Data,
      companyName: `${userData.companyDetails_Data.companyName} - ${
      companyCodes[userData.companyDetails_Data.companyName]
    }`,
    country: `${userData.companyDetails_Data.country} - ${countryList[userData.companyDetails_Data.country]}`,
    region: `${userData.companyDetails_Data.region} - ${regionList[userData.companyDetails_Data.region]}`
  });
    setbankInfo_Data(userData.bankInfo_Data);
    setadditionalContact_Data(userData.additionalContact_Data);
    setregistration_Data(userData.registration_Data);

    await fetchAttachments(userData.companyDetails_Data.mapId)
    // console.log(userData, "payload");
    setIsLoading(false);
  };
  const fetchCompanyCode = async ()=>{
    let hSuccess = (data) => {
      companyCodes = data;
    }
   await doAjax(`/${destination_Po}/Odata/populateCompanyCodeDetails`,'get',hSuccess)
  }
  const fetchCountryList = async()=>{
    await doAjax(`/${destination_PR}/Odata/Country`,`GET`,(res)=> countryList = res)
  }
  const fetchAttachments = (artifactId) => {
    let hSuccess = (data) => {
      var attachmentRows = [];
          data.documentDetailDtoList.forEach((doc) => {
            var tempRow = {
              id: doc.documentId,
              documentType: doc.fileType,
              document: doc.fileName,
              updatedDate: doc.docCreationDate,
              expiryDate: '',
            };
             attachmentRows.push(tempRow);
            });
            // setAttachments(attachmentRows);
            setdocumentDetails_Data(attachmentRows);
    }
      doAjax(`/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${artifactId}`, 'get', hSuccess)
  };
  const appSettings = useSelector((state) => state.appSettings["Format"])
  const handleApproveReject = async(status)=>{
    let hSuccess = (res)=>{
      if(res){
        setSuccess_Notification((prev) => ({
          open: true,
          currentNotification: `Profile ${confId} Successfully ${capitalize(status)}`,
          success: "",
          title: "Success",
          severity: "",
        }));
      }
    }
    let hError = (err)=>{

    }
    doAjax(`/${destination_ManageAccount}/userManagement/postUserInformationToSAP?taskId=${confId}&actionStatus=${status}`,'get',hSuccess)
  }
  const confirmApproveReject = (status)=>{
    if(status==='Approve'){
      setreusableDialog_Ref('CONFIRMSUBMIT')

      setwarning_Notification((prev) => ({
        ...prev,
        currentNotification: `Proceed with submission of profile confirmation`,
        success: false,
        open: true,
        title: "Account confirmation",
        severity: "success",
      }));
    }
    if(status === 'Reject'){
      setreusableDialog_Ref('CONFIRMREJECT')

      setwarning_Notification((prev) => ({
        ...prev,
        currentNotification: `Proceed with rejection of profile confirmation`,
        success: false,
        open: true,
        title: "Account confirmation",
        severity: "success",
      }));
    }
  }
 

  useEffect(() => {
    fetchUserUpdatedData();
    fetchCompanyCode()
    fetchCountryList()
  }, []);

  useEffect(() => {
    console.log(documentDetails_Data,'documentDetails_Data')
  }, [documentDetails_Data]);

  let companyDetails_fieldData = [
    {
      name: "companyName",
      label: "Company Name",
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "areaOfBusiness",
      label: "Area Of Business",
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "registeredCapital",
      label: "Registered Capital",
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "currency",
      label: "Currency",
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "totalEmployee",
      label: "Total Employees",
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "productCategory",
      label: "Product Category",
      value: companyDetails_Data,
      disabled: true,
      isRequired: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "certifications",
      label: "Accreditions & Certifications",
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "groupOfCompany",
      label: "Group Of Company",
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
  ];
  let addressAndContact_fieldData = [
    {
      name: "street1",
      label: "Street 1",
      isRequired: true,
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "street2",
      label: "Street 2",
      isRequired: false,
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "street3",
      label: "Street 3",
      isRequired: false,
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "street4",
      label: "Street 4",
      isRequired: true,
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "street5",
      label: "Street 5",
      isRequired: true,
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "city",
      label: "City",
      isRequired: true,
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "pinCode",
      label: "Pin Code",
      isRequired: true,
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "country",
      label: "Country",
      isRequired: true,
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "companyPhone",
      label: "Company Phone",
      isRequired: true,
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "fax",
      label: "Fax",
      isRequired: false,
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "companyWebsite",
      label: "Website",
      isRequired: true,
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    ,
    {
      name: "region",
      label: "Region",
      isRequired: true,
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
  ];
  let supplierDetails_fieldData = [
    {
      name: "supplierName",
      label: "Supplier Name",
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "supplierType",
      label: "Supplier Type",
      value: companyDetails_Data,
      disabled: true,
      isRequired: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "supplierNumber",
      label: "Supplier Number",
      value: companyDetails_Data,
      disabled: true,
      isRequired: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "partnerType",
      isRequired: true,
      label: "Partner Type",
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
    {
      name: "supplierEmail",
      isRequired: true,
      label: "Email ID",
      value: companyDetails_Data,
      disabled: true,
      oldValue: companyDetails_PrevData,
    },
  ];
  let additionalContactColumn = [
    {
      field: "id",
      headerName: "id",
      hide: true,
      width: 200,
    },
    {
      field: "function",
      headerName: "Function",
      flex: 1,
    },
    {
      field: "contactName",
      headerName: "Contact Name",
      flex: 1,
    },
    {
      field: "position",
      headerName: "Position",
      flex: 1,
    },
    {
      field: "phoneNumber",
      headerName: "Contact Phone",
      flex: 1,
    },
    {
      field: "email",
      headerName: "Email",
      flex: 1,
    },
  ];
  let documentColumn = [
    {
      field: "id",
      headerName: "id",
      editable: false,
      hide: true,
      flex: 1,
    },
    {
      field: "documentType",
      headerName: "Document Type",
    },
    {
      field: "document",
      headerName: "Document Name",
      flex: 1,
    },
    {
      field: "updatedDate",
      headerName: "Updated On",
      flex: 1,
       renderCell: (param) => {
        return moment(param.row.expiryDate).format(appSettings.date);
      },
    },
    {
      field: "expiryDate",
      headerName: "ExpiryDate",
      flex: 1,
      // type: "date",
      // renderCell: (param) => {
      //   return moment(param.row.expiryDate).format(appSettings.date);
      // },
    },
    {
      field: "actionItem",
      headerName: "Action",
      headerAlign: "center",
      align: "center",
      editable: false,
      width: 150,
      clickable: false,
      renderCell: (params) => {
        return (
          <>         
            
            <MatView index={params.row.id} name={params.row.document} />
            
          </>
        );
      },
    },
  ];
  let bankInfo_Columns = [
    {
      field: "bankName",
      headerName: "Bank Name",
      flex: 1,
    },
    {
      field: "accountNumber",
      headerName: "Bank Account Number/IBAN Code",
      flex: 1,
    },
    {
      field: "accountHolderName",
      headerName: "Bank Holder Name",
      flex: 1,
    },
    {
      field: "currency",
      headerName: "Currency",
      flex: 1,
    },
    {
      field: "swiftKey",
      headerName: "Swift Code/ Bank Key",
      flex: 1,
    },
  ];
  let taxAndRegistration_Column = [
    {
      field: "id",
      headerName: "id",
      hide: true,
      width: 200,
    },
    {
      field: "country",
      headerName: "Country",
      flex: 1,
    },
    {
      field: "type",
      headerName: "Type",
      flex: 1,
    },
    {
      field: "taxRegistrationId",
      headerName: "Tax Registration ID",
      flex: 1,
    },
    {
      field: "IfOther",
      headerName: "If other",
      flex: 1,
    },
  ];
  const handleBack = () => {
    dispatch(setRedirectionFilter({targetModule:'User Management', filter:{tab:'userswb'}}))
    // utilityReduxStore.redirection.url_LastPath
    //   ? navigate(utilityReduxStore.redirection.url_LastPath)
    //   : navigate("/configCockpit/userManagement");
    navigate(-1)
    // dispatch(clearLastPath());
  };
 
  const getTextFields = (data) => {
    return (
      <>
        <Card sx={{ width: "98%" }}>
          <CardContent>
            <Grid
              columns={12}
              container
              rowSpacing={1}
              sx={{ marginBottom: "0.5rem" }}
              spacing={2}
              justifyContent="start"
              alignItems="start"
            >
              {data.map((item) => {
               
                return (
                  <Grid item xs={1} md={3} sx={{
                    display:'flex', 
                    justifyContent:'start'
                  }}>
                    <Box sx={{ minWidth: '100%', }}>
                    
                        <Typography
                          sx={{ ...font_Small, display: "inline-block" }}
                        >
                          {`${item.label}`}
                        </Typography>
                       
                      
                      {!item.multiSelect && (
                        <>
                        <Box>
                        {console.log(item.name,(item.value[item.name]??"") , item.oldValue[item.name])}
                        <TextField
                          sx={{
                            marginBottom:'auto',
                            "& .css-ibxa63-MuiInputBase-root-MuiOutlinedInput-root .css-1n4twyu-MuiInputBase-input-MuiOutlinedInput-input":
                              {
                                width:'100%',
                                border: (item.value[item.name]??"") !== item.oldValue[item.name] && '2px solid #089b13',
                                borderRadius:  (item.value[item.name]??"") !== item.oldValue[item.name]&& '4px'
                              },
                          }}
                          fullWidth
                          size="small"
                          placeholder={item.label}
                          name={item.name}
                          InputProps={{
                            readOnly: item.disabled,
                          }}
                          value={item.value[item.name]}
                          onChange={item.onchange}
                        ></TextField>
                        <Stack
                        flexDirection={"row"}
                        justifyContent={"end"}
                      >
                         <span style={{
                          backgroundColor:'#d7eadb',
                          minWidth:'max-content',
                          height:'max-content',
                          borderRadius:'5px',
                          padding:'1px 8px'
                         }}>

                          {(item.value[item.name]??"" )!== item.oldValue[item.name] &&
                            item.oldValue[item.name]}
                            </span>
                        
                        </Stack>
                        </Box>

                        </>

                      )}
                    </Box>
                  </Grid>
                );
              })}
            </Grid>
          </CardContent>
        </Card>
      </>
    );
  };
  return (
    <>
      {/* Loader */}
      <Backdrop className="backdrop" open={isLoading}>
        <CircularProgress color="primary" />
      </Backdrop>
      <ReusableDialog
        dialogState={warning_Notification.open}
        openReusableDialog={functions_ReusableDialogBox.MessageDialogClickOpen}
        closeReusableDialog={functions_ReusableDialogBox.MessageDialogCancel}
        dialogTitle={warning_Notification.title}
        dialogMessage={warning_Notification.currentNotification}
        handleOk={functions_ReusableDialogBox.getHandleOkFunction }
        dialogOkText={functions_ReusableDialogBox.getOkButtonText()}
        showOkButton={functions_ReusableDialogBox.viewOkButton() }
        showCancelButton= {functions_ReusableDialogBox.viewCancelButton()}
        dialogSeverity={warning_Notification.severity}
        handleDialogReject={functions_ReusableDialogBox.getHandleCancleFunction}
      />
      <ReusableSnackBar
        openSnackBar={Success_Notification.open}
        alertMsg={Success_Notification.currentNotification}
        handleSnackBarClose={
          functions_ReusableDialogBox.messageDialogCloseAndRedirect
        }
      />
      {taskStatus?.toLowerCase() === "pending action" && (
        <Paper
          sx={{ position: "fixed", zIndex: "5", bottom: 0, left: 0, right: 0 }}
          elevation={10}
        >
          <BottomNavigation
            showLabels
            className="container_BottomNav"
            sx={{
              display: "flex",
              justifyContent: "flex-end",
            
            }}
          >
            <Button
              size="small"
              variant="outlined"
              className='btn-mr'
              onClick={()=> confirmApproveReject('Reject')}
            >
              Reject
            </Button>
            <Button
              size="small"
              variant="contained"
              
              onClick={()=> confirmApproveReject('Approve')}

            >
              Accept
            </Button>
          </BottomNavigation>
        </Paper>
      )}
     {!isLoading && <div
        className=""
        style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}
      >
        <Stack spacing={1}>
          {/* Information */}
          <Grid container sx={outermostContainer_Information}>
            <Grid item sx={{ maxWidth: "max-content" }}>
              <IconButton
                onClick={handleBack}
                color="primary"
                aria-label="upload picture"
                component="label"
                sx={iconButton_SpacingSmall}
              >
                <ArrowCircleLeftOutlinedIcon
                  sx={{
                    fontSize: "25px",
                    color: "#000000",
                  }}
                />
              </IconButton>
            </Grid>
            <Grid item md={5}>
              <Typography variant="h3">
                <strong>{`Profile Confirmation: ${confId}`}</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                This view displays the user profile details
              </Typography>
            </Grid>
            <Grid item md={7} sx={{ display: "flex" }}>
              <Grid
                container
                direction="row"
                justifyContent="flex-end"
                alignItems="center"
                spacing={0}
              ></Grid>
            </Grid>
          </Grid>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              width: "100%",
            }}
          >
            {/*supplier details */}
            <Stack
              direction={"column"}
              sx={{ width: "100%", padding: ".5rem" }}
            >
              <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                Supplier Details
              </Typography>
              {getTextFields(supplierDetails_fieldData)}
            </Stack>

            {/*Company Details*/}
            <Stack
              direction={"column"}
              sx={{ width: "100%", padding: ".5rem" }}
            >
              <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                Company Details
              </Typography>
              {getTextFields(companyDetails_fieldData)}
            </Stack>

            {/*Company Address and COntacts*/}
            <Stack
              direction={"column"}
              sx={{ width: "100%", padding: ".5rem" }}
            >
              <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                Company Address and Contacts
              </Typography>
              {getTextFields(addressAndContact_fieldData)}
            </Stack>

            {/*Bank info*/}

            <Stack
              direction={"column"}
              sx={{ width: "100%", padding: ".5rem" }}
            >
              <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                Bank Information
              </Typography>
              <ReusableTable
                width="100%"
                rows={bankInfo_Data}
                columns={bankInfo_Columns}
                hideFooter={true}
                isLoading={false}
              />
            </Stack>

            {/*Site Tax ID /Registration*/}
            <Stack
              direction={"column"}
              sx={{ width: "100%", padding: ".5rem" }}
            >
              <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                Site Tax ID/ Registration
              </Typography>
              <ReusableTable
                width="100%"
                rows={registration_Data}
                columns={taxAndRegistration_Column}
                hideFooter={true}
                isLoading={false}
              />
            </Stack>
            {/*Additional contact Information*/}

            <Stack
              direction={"column"}
              sx={{ width: "100%", padding: ".5rem" }}
            >
              <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                Additional Contacts
              </Typography>
              <ReusableTable
                width="100%"
                rows={additionalContact_Data}
                columns={additionalContactColumn}
                hideFooter={true}
                isLoading={false}
              />
            </Stack>
            {/*Additional contact Information*/}
            <Stack
              direction={"column"}
              sx={{ width: "100%", padding: ".5rem" }}
            >
              <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                Uploaded Documents
              </Typography>
              {
                <ReusableTable
                  width="100%"
                  status_onRowDoubleClick={false}
                  rows={documentDetails_Data}
                  columns={documentColumn}
                  hideFooter={true}
                  isLoading={false}
                />
              }
            </Stack>
          </Box>
        </Stack>
      </div>}
    </>
  );
};

export default UsersWorkbenchDetailsPage;
