import React, { useState, useEffect } from 'react';
import {
  <PERSON>rid,
  Typography,
  Stack,
  IconButton,
  Tooltip,
  Button
} from '@mui/material';
import MaterialSelector from './MaterialSelector';
import { doAjax } from "../Common/fetchService";
import { destination_MaterialMgmt, destination_IDM } from "../../destinationVariables";
import ReusableBackDrop from '@components/Common/ReusableBackDrop';
import { useLocation, useNavigate } from 'react-router-dom';
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { iconButton_SpacingSmall } from '@components/Common/commonStyles';
import useLang from '@hooks/useLang';
import DownloadIcon from '@mui/icons-material/Download';
import { DATA_CLEANSE_CONSTANTS } from '@constant/enum';
import { END_POINTS } from '@constant/apiEndPoints';
import { useSnackbar } from '@hooks/useSnackbar';
import DataCleanseSkeletonLoader from './DataCleanseSkeletonLoader';


const Data = () => {
  const [blurLoading, setBlurLoading] = useState(false);
  const [skeletonLoading, setSkeletonLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [apiData, setapiData] = useState({});
  const { t } = useLang()
  const { showSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const location = useLocation();
  const rowData = location?.state

  useEffect(() => {
    if (rowData?.requestId) {
      fetchDisplayData(rowData?.requestId)
    }
  }, [rowData])


  const outermostContainer = {
    padding: '1rem',
    minHeight: '100vh'
  };

  const fetchDisplayData = (requestId) => {
    setSkeletonLoading(true)
    let payload = { requestId }
    const hSuccess = (data) => {
      setapiData(data?.body);
      setSkeletonLoading(false)
    }
    const hError = (error) => {
      setSkeletonLoading(false)
    };
    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS?.DATA_CLEANSE_APIS?.CLEANSING_REQ_DETAILS}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  }

  const handleDownloadPdf = (requestId) => {
    setBlurLoading(true);
    let payload = { requestId }

    const hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute("download", `${requestId}_Data Cleanse.pdf`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");
      showSnackbar(`${requestId}${DATA_CLEANSE_CONSTANTS?.EXPORT_SUCCESS}`, "success")

    };
    const hError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      showSnackbar(`Failed exporting ${requestId}_Data Cleanse.pdf`, "error")
    };

    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS?.DATA_CLEANSE_APIS?.DOWNLOAD_PDF}`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  return (
    <div style={{ maxHeight: "50vh" }}>
      <Grid container sx={{ padding: '16px' }}>
        <Grid item md={12} sx={{ padding: "16px", display: "flex", mb: -6 }}>
          <Grid md={9} sx={{ display: "flex" }}>
            <IconButton color="primary" sx={iconButton_SpacingSmall} onClick={() => navigate(-1)}>
              <ArrowCircleLeftOutlinedIcon sx={{ fontSize: "25px", color: "#000000" }} />
            </IconButton>
            <Grid item md={12}>
              <Typography variant="h3">
                <strong>{`${t(DATA_CLEANSE_CONSTANTS.CLEANSE_REQUEST)} - ${rowData?.requestId}`}</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                {t(DATA_CLEANSE_CONSTANTS.VIEW)}
              </Typography>
            </Grid>
          </Grid>
          <Grid md={3} sx={{ display: "flex", justifyContent: "flex-end", alignItems: "flex-start" }}>
            <Button
              variant="outlined"
              color="primary"
              startIcon={<DownloadIcon />}
              sx={iconButton_SpacingSmall}
              onClick={() => {
                handleDownloadPdf(rowData?.requestId || "")
              }}
            >
              {DATA_CLEANSE_CONSTANTS.PDF}
            </Button>
          </Grid>
        </Grid>
      </Grid>
      <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage}/>
      <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1}>
          {(skeletonLoading || !Object?.keys(apiData)?.length) ? (
            <DataCleanseSkeletonLoader />
          ) : (
            <MaterialSelector apiData={apiData} />
          )}
        </Stack>
      </div>
    </div>
  );
};

export default Data;