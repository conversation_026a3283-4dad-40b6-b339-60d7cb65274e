import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';

function CustomCheckbox({ 
  label, 
  checked = false, 
  onChange, 
  id 
}) {
  return (
    <FormControlLabel
      control={
        <Checkbox
          id={id}
          checked={checked}
          onChange={onChange}
          sx={{
            color: 'primary.main',
            '&.Mui-checked': {
              color: 'primary.main',
            },
          }}
        />
      }
      label={label}
      sx={{
        ml: 0,
        '.MuiFormControlLabel-label': {
          fontSize: 14,
          color: '#4B5563',
          cursor: 'pointer',
        },
      }}
    />
  );
}

export default CustomCheckbox;
