import useLogger from "@hooks/useLogger";
import { doAjax } from "@components/Common/fetchService";
import { useDispatch } from "react-redux";

const useFMDFetchDropdownAndDispatch = (destination,setDropDown) => {
const { customError } = useLogger();
const dispatch = useDispatch()
  const fetchDataAndDispatch = (keyName, endPoint, method = "get", payload = {}) => {
    const hSuccess = (data) => {
          dispatch(setDropDown({
            keyName,
            data: data.body,
          }))
      }
    const hError = (error) => {
      customError(error);
    };
    doAjax(`/${destination}/data/${endPoint}`, method.toLowerCase(), hSuccess, hError, payload);
  };

  return { fetchDataAndDispatch };
};

export default useFMDFetchDropdownAndDispatch
