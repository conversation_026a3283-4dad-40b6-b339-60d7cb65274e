import { Card, CardContent, Grid, Stack, Typography } from "@mui/material";
import { Box } from "@mui/system";
import React from "react";
import CountUp from 'react-countup';
import { motion } from "framer-motion";

import ArrowForwardIosRoundedIcon from "@mui/icons-material/ArrowForwardIosRounded";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { commonFilterUpdate } from "../../app/commonFilterSlice";
import { setRedirectionFilter } from "../../app/utilitySlice";
const DashboardCard = (props) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const dashboardSearchForm = useSelector(
    (state) => state?.commonFilter["Dashboard"]
  );
  const invTrackerSearchForm = useSelector(
    (state) => state?.commonFilter["InvoiceTracker"]
  );
  const RBSearchForm = useSelector(
    (state) => state.commonFilter["RequestBench"]
  );
  const returnSearchForm = useSelector(
    (state) => state?.commonFilter["ReturnWorkbench"]
  );
 
  const controller_redirectionPo = () => {
    console.log(props?.type,"props?.type")
    if (
      props?.type == "RB"
      
    ) {
      dispatch(
        commonFilterUpdate({
          module: "RequestBench",
          filterData: {
            // companyCode: dashboardSearchForm?.companyCode,
            // vendorNo: dashboardSearchForm?.vendorNo,
            // status: props?.status,
            date: dashboardSearchForm?.dashboardDate,
            requestType:props?.status
          },
        })
      );
      // dispatch(setRedirectionFilter({ targetModule: 'PurchaseOrder' }))
      return `/requestBench`;
    }

    else if (
      props?.type == "RBEC"
      
    ) {
      dispatch(
        commonFilterUpdate({
          module: "RequestBench",
          filterData: {
            // companyCode: dashboardSearchForm?.companyCode,
            // vendorNo: dashboardSearchForm?.vendorNo,
            requestType: props?.status === "Extend" ? "Mass Extend" : props?.status,
            date: dashboardSearchForm?.dashboardDate,
            //module:dashboardSearchForm?.dashBoardModuleName,
            
          },
        })
      );
      // dispatch(setRedirectionFilter({ targetModule: 'PurchaseOrder' }))
      return `/requestBench`;
    }


    else if (
      props?.type == "PO"
      
    ) {
      dispatch(
        commonFilterUpdate({
          module: "PurchaseOrder",
          filterData: {
            companyCode: dashboardSearchForm?.companyCode,
            vendorNo: dashboardSearchForm?.vendorNo,
            poStatus: props?.status,
            poDate: dashboardSearchForm?.dashboardDate,
            acknowledgement:props?.additionalStatus || ""
          },
        })
      );
      dispatch(setRedirectionFilter({ targetModule: 'PurchaseOrder' }))
      return `/purchaseOrder`;
    }
    
    else if (
      props?.type == "INV"
    ) {
      dispatch(
        commonFilterUpdate({
          module: "EInvoice",
          filterData: {
            companyCode: dashboardSearchForm?.companyCode,
            vendorNo: dashboardSearchForm?.vendorNo,
            poStatus: props?.status,
            poDate: dashboardSearchForm?.dashboardDate,
          },
        })
      );
      dispatch(setRedirectionFilter({ targetModule: 'EInvoice' }))
      return `/invoices/management`;
    }else if (
      props?.type == "INVtracker"
    ) {
      // props.setinvStatus(props.invStatus);
      dispatch(
        commonFilterUpdate({
          module: "InvoiceTracker",
          filterData: {
            companyCode: dashboardSearchForm?.companyCode,
            vendorNo: dashboardSearchForm?.vendorNo,
            invoiceStatus: props?.status,
            creationDate: dashboardSearchForm?.dashboardDate,
            clearingDate:[],
            postingDate:[],
            poNumber: "",
          },
        })
      );
      dispatch(setRedirectionFilter({ targetModule: "InvoiceTracker" }))
      return `/invoices/invoiceTracker`;
    } else if (
      props?.type == "DPR"
    ) {
     
      dispatch(setRedirectionFilter({ targetModule: 'DPR' }))

      dispatch(commonFilterUpdate({
        filterData:{
        company: dashboardSearchForm?.companyCode,
        supplier:  dashboardSearchForm?.vendorNo,
        dprStatus:props?.status,
       
        date: dashboardSearchForm?.dashboardDate
      },
       module:'DPR'
      }))
      // dispatch(setRedirectionFilter({ targetModule: "dpr" }))
      return `/purchaseOrder/DPR`;
    } else if (props?.type == "CreateASN") {
      dispatch(setRedirectionFilter({ targetModule: 'ASN' }))
      dispatch(
        commonFilterUpdate({
          module: "ASN",
          filterData: {
            Filter_Company: dashboardSearchForm?.companyCode,
            Filter_Supplier: dashboardSearchForm?.vendorNo,
            Filter_Status: props?.status,
            // Filter_FromDate: dashboardSearchForm.dashboardDate[1],
            // Filter_ToDate: dashboardSearchForm.dashboardDate[0]
            Filter_Date:dashboardSearchForm?.dashboardDate
          },
        })
        
      );
     
     
      return `/purchaseOrder/ASN/CreateASN`;
    }
    else if (props?.type == "ASN") {
      dispatch(setRedirectionFilter({ targetModule: 'ASNhome' }))
      dispatch(
        commonFilterUpdate({
          module: "ASNhome",
          filterData: {
            Filter_Company: dashboardSearchForm?.companyCode,
            Filter_Supplier: dashboardSearchForm?.vendorNo,
            Filter_Status: props?.status || [],
            Filter_Consumption:props?.additionalStatus,
            // Filter_FromDate: dashboardSearchForm.dashboardDate[1],
            // Filter_ToDate: dashboardSearchForm.dashboardDate[0]
            Filter_Date:dashboardSearchForm?.dashboardDate
          },
        })
        
      );
     
     
      return `/purchaseOrder/ASN`;
    }
    else if (props?.type == "Planning") {
      dispatch(setRedirectionFilter({ targetModule: 'PlanningManagement' }))
      dispatch(
        commonFilterUpdate({
          module: "PlanningManagement",
          filterData: {
            companyCode: dashboardSearchForm?.companyCode,
            vendorNo: dashboardSearchForm?.vendorNo,
            Filter_Status: [],
            // planningSheetStatus:["Planning Task Created","Planning Task Submitted"],
            planningSheetStatus:props?.status,

            // Filter_FromDate: dashboardSearchForm.dashboardDate[1],
            // Filter_ToDate: dashboardSearchForm.dashboardDate[0]
            taskCreationDate:dashboardSearchForm?.dashboardDate
          },
        })
        
      );
     
     
      return `/planningManagement`;
    }
    else if(
      props?.type==='Returns'
    ){
      dispatch(setRedirectionFilter({ targetModule: 'ReturnWorkbench' }))

      dispatch(
        commonFilterUpdate({
          module: "ReturnWorkbench",
          filterData: {...returnSearchForm,
            company: dashboardSearchForm?.companyCode,
            supplier: dashboardSearchForm?.vendorNo,
            // status:["Pending Action"],
            status:props?.status,
            confirmdate: dashboardSearchForm?.dashboardDate,
          },
        }))
        return `/ReturnManagement/ReturnWorkbench`
    }
  };
  return (
    <>
    {props.count === 'BLANK' ?'':(
    <Grid item xs={2} sm={2} md={2} lg={2} xl={2}>
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 1.2,
        ease: "easeOut"
      }}
    >
      <Card
        sx={{
          borderRadius: "10px",
          height: 'max-content',
          boxShadow: "5",
          backgroundColor: props.color,
          transition: "transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out",
          "&:hover": {
            transform: "scale(1.08)",
            boxShadow: 7,
          },
        }}
      >
        <Box sx={{ position: "relative", boxSizing:'border-box',padding:'.5rem 1rem',minHeight:'inherit' }} p={0} >
          <Box sx={{ position: "absolute", right: "5%", top: "35%" }}>
            {props?.icon &&
            <Typography variant="caption" color="#777">
              <ArrowForwardIosRoundedIcon
                sx={{ fontSize: "14px", cursor: "pointer" }}
                onClick={() => navigate(controller_redirectionPo())}
              />
            </Typography>}
          </Box>

          <Stack sx={{
            alignItems:'row'
          }} textAlign={"center"}
          >
            <Box sx={{
              height: "40px",
              display:'flex',
              alignItems:'center',
              justifyContent:'center',

            }}>

            <Typography sx={{
               overflow:'hidden',
               whiteSpace: "no-wrap",
           textOverflow: "ellipsis",
            }}variant="caption" color="#4B5768" fontSize="0.875rem" fontWeight="500">
              {props?.header}
            </Typography>
            </Box>
            <Box sx={{
              maxHeight: "40px",
              display:'flex',
              alignItems:'center',
              justifyContent:'center',
              overflow:'hidden'

            }}>

            <Typography variant="h4" color="#1D1D11" fontSize="1.5rem" fontWeight="500">
            <CountUp
              end={props?.count}
              suffix={props?.isPercentage ? '%' : ''}
              duration={2.5}
            />
            </Typography>
            </Box>
          </Stack>
        </Box>

      </Card>
    </motion.div>
    </Grid>)}
  </>
  );
};

export default DashboardCard;
