import {
    FormControlLabel,
    Grid,
    Radio,
    RadioGroup,
    Stack,
    TextField,
    Typography,
  } from "@mui/material";
  
  import React, { useEffect, useState } from "react";
  
  import { useDispatch, useSelector } from "react-redux";
  import {
    setMatRequiredFieldsGI,
    setSingleMaterialPayload} from "../../app/payloadSlice"

  const GeneralInformationTabForMaterial = ({
    questions,
    // handleCheckValidationError,
  }) => {
    const [mandatoryFieldsFilled, setMandatoryFieldsFilled] = useState(false);
    const dispatch = useDispatch();
    const payloadData = useSelector((state) => state.payload.singleMatPayload);
    console.log("questions", questions);
    const onEditCapitalize = (label,newValue) => {
      let selectedLabel = label.replaceAll("(", "")
      .replaceAll(")", "")
      .replaceAll("/", "")
      .replaceAll("-", "")
      .replaceAll(".", "")
      .split(" ")
      .join("")
      console.log("labell",label.replaceAll("(", "")
      .replaceAll(")", "")
      .replaceAll("/", "")
      .replaceAll("-", "")
      .replaceAll(".", "")
      .split(" ")
      .join(""))
      dispatch(
        setSingleMaterialPayload({
          keyName: selectedLabel,
          data: newValue,
        })
      );
    };
    const onEdit = (label, newValue) => {
      console.log("newlabel", newValue, label);
      const mandatoryFields = questions.filter(
        (q) => q.visibility === " Mandatory"
      );
      const filledMandatoryFields = mandatoryFields.every(
        (q) => payloadData[q.MDG_GI_QUESTION_TYPE] !== ""
      );
      setMandatoryFieldsFilled(filledMandatoryFields);
      dispatch(
        setSingleMaterialPayload({
          keyName: label
            .replaceAll("(", "")
            .replaceAll(")", "")
            .replaceAll("/", "")
            .replaceAll("-", "")
            .replaceAll(".", "")
            .split(" ")
            .join(""),
          data: newValue,
        })
      );
    };
    useEffect(() => {
      
        questions.map((item) => {
          console.log("questionstest", item?.MDG_GI_VISIBILITY);
  
          if (
            item?.MDG_GI_VISIBILITY === " Mandatory" ||
            item?.MDG_GI_VISIBILITY === "0"
          ) {
            console.log("rakesh", item);
            dispatch(
              setMatRequiredFieldsGI(
                item?.MDG_GI_QUESTION_TYPE?.replaceAll("(", "")
                  .replaceAll(")", "")
                  .replaceAll("/", "")
                  .replaceAll("-", "")
                  .replaceAll(".", "")
                  .replaceAll("%", "")
                  .split(" ")
                  .join("")
              )
            );
          }
        });
      
    }, [questions]);
  
    return (
      <>
        <Grid container>
          {questions.map((question, index) => (
            <Grid item md={12} key={index}>
              <Grid
                container
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  padding: "16px",
                }}
              >
                <Stack sx={{ width: "100%" }}>
                  <Typography
                    sx={{
                      fontSize: "12px",
                      fontWeight: "700",
                    }}
                  >
                    {question.MDG_GI_QUESTION_TYPE}
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
  
                  {question.MDG_GI_INPUT_OPTION === "Radio Button" ? (
                    <RadioGroup
                      aria-labelledby={`radio-group-label-${index}`}
                      defaultValue=""
                      name={`radio-group-${index}`}
                      row
                      value={
                        payloadData?.[
                          question.MDG_GI_QUESTION_TYPE?.replaceAll("(", "")
                            .replaceAll(")", "")
                            .replaceAll("/", "")
                            .replaceAll("-", "")
                            .replaceAll(".", "")
                            .split(" ")
                            .join("")
                        ]
                      }
                    >
                      {question.MDG_GI_INPUT_VALUE.split(",").map(
                        (option, optIndex) => (
                          <FormControlLabel
                            key={optIndex}
                            value={option}
                            control={<Radio />}
                            label={option}
                            onChange={(event) => {
                              let label = question.MDG_GI_QUESTION_TYPE;
                              let newValue = event.target.value;
                              console.log(
                                "newValue",
                                event.target.value,
                                question.MDG_GI_QUESTION_TYPE
                              );
                              onEdit(label, newValue);
                            //   handleCheckValidationError();
                            }}
                          />
                        )
                      )}
                    </RadioGroup>
                  ) : (
                    <TextField
                      fullWidth
                      placeholder="PLEASE ENTER..."
                      multiline
                      value={
                        payloadData?.[
                          question.MDG_GI_QUESTION_TYPE?.replaceAll("(", "")
                            .replaceAll(")", "")
                            .replaceAll("/", "")
                            .replaceAll("-", "")
                            .replaceAll(".", "")
                            .split(" ")
                            .join("")
                        ]
                      }
                      onChange={(event) => {
                        let label = question.MDG_GI_QUESTION_TYPE;
                      console.log("label",label);
                      const newValue = event.target.value;
                        if (newValue.length > 0 && newValue[0] === " ") {
                          onEditCapitalize(label,newValue.trimStart());
                        } else {
                          let valueUpperCase = newValue.toUpperCase();
                          onEditCapitalize(label,valueUpperCase);
                        }
                        // handleCheckValidationError();
                      }}
                    />
                  )}
                </Stack>
              </Grid>
            </Grid>
          ))}
        </Grid>
      </>
    );
  };
  
  export default GeneralInformationTabForMaterial;
    
