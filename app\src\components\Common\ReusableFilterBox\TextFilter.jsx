import { Box, FormControl, TextField, Typography } from "@mui/material";
import { font_Small } from "../commonStyles";

export default function TextFilter(props) {
  return (
    <Box sx={{ minWidth: 120 }}>
    <Typography sx={font_Small}>{props?.filterTitle}</Typography>
    <FormControl sx={font_Small} fullWidth>
      <TextField
        size="small"
        placeholder={`Enter ${props?.filterTitle}`}
        name={props?.filterName}
        onChange={props?.onChangeFilter}
        value={props?.moduleFilter?.filterName}
      ></TextField>
    </FormControl>
  </Box>
  )
}
