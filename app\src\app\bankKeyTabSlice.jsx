import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  bankKeyBankData: {},
  bankKeyAddressData: {},
  singleBKPayload: {},
  requiredFields: [],
  errorFields: [],
  handleMassMode:"",
  MultipleBankKeyData: [],
  bankKeyViewData: [],
};

export const bankKeyTabSlice = createSlice({
  name: "bankKey",
  initialState,
  reducers: {
    setBankKeyBankData: (state, action) => {
      state.bankKeyBankData = action.payload;
    },
    setBankKeyAddressData : (state, action) => {
      state.bankKeyAddressData = action.payload;
    },
    setSingleBankKeyPayload: (state, action) => {
      state.singleBKPayload[action.payload.keyName] = action.payload.data;
      return state;
    },
    setBankKeyViewData: (state, action) => {
      state.bankKeyViewData = action.payload;
    },
    clearBankKeyPayload: (state) => {
      state.singleBKPayload = {};
    },
    setBKRequiredFields: (state, action) => {
      if (
        state.requiredFields.findIndex((item) => item == action.payload) == -1
      ) {
        state.requiredFields.push(action.payload);
      }
      return state;
    },
    setBKErrorFields: (state, action) => {
      state.errorFields = action.payload;
      return state;
    },
    setMultipleBankKeyData(state, action) {
      state.MultipleBankKeyData = action.payload;
      return state;
    },
    setHandleMassMode(state,action){
      state.handleMassMode = action.payload
    },
    clearBankKey :(state) =>{
      //   state.bankKeyAddressDat = {}
        state.errorFields = []
        state.requiredFields = []
        state.singleBKPayload = {}
        
        //state.EditMultiple = {}
      },
  

  },
});

// Action creators are generated for each case reducer function
export const {
  setBankKeyBankData,
  setBankKeyAddressData,
  setSingleBankKeyPayload,
  setMultipleBankKeyData,
  setBankKeyViewData,
  setBKRequiredFields,
  setBKErrorFields,
  setHandleMassMode,
  clearBankKey
} = bankKeyTabSlice.actions;

export const bankKeyReducer = bankKeyTabSlice.reducer;
