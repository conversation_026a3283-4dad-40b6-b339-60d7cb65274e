export const barColorsStore = [
  "#fbb708",
  "#b1aade",
  "#59e0e9",
  "#6c5ec0",
  "#8ed950",
  "#6F4840",
];
// export const barColors1Store = [
//   "#FFFDCC",
//   "#59C2E3",
//   "#C3F0FE",
//   "#fef7e6",
//   "#fee7ae",
//   "#8ed950",
//   "#6c5ec0",
//   "#ff5555",
//   "#59e0e9",
// ];
export const barColors1Store = [
  '#4dc9f6',
  '#f67019',
  '#8549ba',
  "#f53050",
  '#537bc4',
  '#acc236',
  '#166a8f',
  '#00a950',
  '#58595b',
  
  '#ff6384',
  '#36a2eb',
  '#ffce56',
  '#cc65fe',
  '#ff9f40',
  '#8c564b',
  '#e377c2',
  '#7f7f7f',
  '#bcbd22',
  '#17becf'
];
export const barColors2Store = [
  "#59e0e9",
  "#fbb708",
  "#ff5555",
  "#eefbfc",
  "#a7eff3",
  "#6c5ec0",
  "#8ed950",
];
export const barColors3Store = [
  "#59e0e9",
  "#fbb708",
  "#eefbfc",
  "#a7eff3",
  "#7f74c8",
  "#d2cff5",
];
export const barColors4Store = [
  "#3236a8",
  "#5e32a8",
  "#ba571e",
  "#5aba1e",
  "#68d4de",
  "#8ed950",
  "#6c5ec0",
  "#ff5555",
  "#59e0e9",
];

export const legendDataPopieStore = [
  { name: "undefined" },
  { name: "PO Open", col: "#a7eff3", status: ["PO Open"] },
  {
    name: "PO Confirmation Submitted",
    col: "#ff5555",
    status: ["PO Confirmation Submitted"],
  },

  { name: "PO Blocked", col: "#b1aade", status: ["PO Blocked"] },
  {
    name: "GR Partially Completed",
    col: "#c8f5f8",
    status: ["GR Partially Completed"],
  },

  {
    name: "PO Requires Confirmation",
    col: "#59e0e9",
    status: ["PO Requires Confirmation"],
  },
  {
    name: "GR Fully Completed",
    col: "#fbb708",
    status: ["GR Fully Completed"],
  },

  {
    name: "PO Confirmed",
    col: "#6c5ec0",
    status: ["PO Confirmed"],
  },
  {
    name: "Invoice Partially Completed",
    col: "#fef7e6",
    status: ["Invoice Partially Completed"],
  },

  {
    name: "Invoice Fully Completed",
    col: "#fee7ae",
    status: ["Invoice Fully Completed"],
  },
];
export const legendDataStore = [
  { name: "undefined" },
  { name: "Posted (Paid)", col: "#a7eff3", invstatus: ["Posted (Paid)"] },
  { name: "Posted (Unpaid)", col: "#c8f5f8", invstatus: ["Posted (Unpaid)"] },
  { name: "Blocked for Payment", col: "#fbb708", invstatus: ["Rejected"] },
  {
    name: "Pending for Approval",
    col: "#b1aade",
    invstatus: ["Ready To Post"],
  },
];
export const legendDataInvPieStore = [
  { name: "undefined" },
  { name: "Paid", col: "#a7eff3", invstatus: ["Posted (Paid)"] },
  { name: "Unpaid", col: "#c8f5f8", invstatus: ["Posted (Unpaid)"] },
];
export const legendSRstatusStore = [
  { name: "undefined" },
  { name: "Open", col: "#a7eff3", SRstatus: ["Open"], SRpriority: [] },
  {
    name: "Requires Input",
    col: "#c8f5f8",
    SRstatus: ["Requires Input"],
    SRpriority: [],
  },
  { name: "Assigned", col: "#fbb708", SRstatus: ["Assigned"], SRpriority: [] },
  {
    name: "Cancelled",
    col: "#b1aade",
    SRstatus: ["Cancelled"],
    SRpriority: [],
  },

  {
    name: "Work In Progress",
    col: "#fef7e6",
    SRstatus: ["Work In Progress"],
    SRpriority: [],
  },
  { name: "Closed", col: "#fee7ae", SRstatus: ["Closed"], SRpriority: [] },
];
export const legendSRStore = [
  { name: "undefined" },
  {
    name: "Critical",
    col: "#a7eff3",
    SRpriority: ["1 - Critical"],
    SRstatus: [],
  },
  { name: "Medium", col: "#c8f5f8", SRpriority: ["3 - Medium"], SRstatus: [] },
  { name: "High", col: "#fbb708", SRpriority: ["2 - High"], SRstatus: [] },
  { name: "Low", col: "#b1aade", SRpriority: ["4 - Low"], SRstatus: [] },
];
export const legendDataProductionPieStore = [
  { name: "undefined" },
  {
    name: "Production In Progress",
    col: "#a7eff3",
    dprStat: ["Production In Progress"],
  },
  {
    name: "Production to be Initiated",
    col: "#c8f5f8",
    dprStat: ["Production to be Initiated"],
  },
  {
    name: "Production Completed",
    col: "#fbb708",
    dprStat: ["Production Completed"],
  },
];
export const graphnamelistPoStore = [
  { name: "Top 5 Open POs", keyName: "openPo", id: 1, type: "openPO" },
  {
    name: "Top 5 Pending Confirmation POs",
    keyName: "pendingPo",
    id: 2,
    type: "pendingPO",
  },
  {
    name: "Top 5 Confirmed POs",
    keyName: "confirmed",
    id: 3,
    type: "confirmedPO",
  },
  {
    name: "Top 5 Delivered POs",
    keyName: "delivered",
    id: 4,
    type: "deliveredPO",
  },
  { name: "Top 5 Blocked POs", keyName: "blocked", id: 5, type: "blockedPO" },
  {
    name: "Top 5 Production in Progress POs",
    keyName: "pip",
    id: 6,
    type: "pipPO",
  },
];

export const graphnamelistDelStore = [
  {
    name: "Top 5 Production Completed POs",
    id: 1,
    keyName: "completed",
    type: "productionCompleted",
  },
  {
    name: "Top 5 Pending Advanced Shipment Notifications",
    id: 2,
    keyName: "asn",
    type: "asn",
  },
  {
    name: "Top 5 Overdue Shipments",
    id: 3,
    keyName: "overdue",
    type: "overdue",
  },
  // {
  //   name: "Top 5 On-time Delivery",
  //   id: 4,
  //   keyName: "ontime",
  //   type: "onTime",
  // },
  {
    name: "Top 5 Pending Returns",
    id: 5,
    keyName: "pending",
    type: "pendingReturns",
  },
];
export const graphnamelistInvStore = [
  {
    name: "Top 5 Ready to Invoice",
    id: 1,
    keyName: "readyto",
    type: "readyTo",
  },
  {
    name: "Top 5 Ready to Post Invoice",
    id: 2,
    keyName: "readytopost",
    type: "readyToPost",
  },
  { name: "Top 5 Posted Invoice", id: 3, keyName: "posted", type: "posted" },
  { name: "Top 5 Paid Invoice", id: 4, keyName: "paid", type: "paid" },
  { name: "Top 5 Unpaid Invoice", id: 5, keyName: "unpaid", type: "unpaid" },
  {
    name: "Top 5 Cancelled Invoice",
    id: 6,
    keyName: "cancelled",
    type: "cancelled",
  },
];
export const graphnamelistOpStore = [
  {
    name: "Top 5 Materials Purchased by Quantity",
    id: 1,
    keyName: "materialPurchase",
    type: "materialPurchase",
  },
  {
    name: "Top 5 Materials Purchased by Value",
    id: 2,
    keyName: "materialValue",
    type: "materialValue",
  },
  { name: "Purchase Order Status", id: 3, keyName: "poStatus", type: "pie" },
  { name: "Invoice Status", id: 4, keyName: "invStatus", type: "pie" },
  { name: "Payment Status", id: 5, keyName: "paymentStatus", type: "pie" },
  { name: "Production Status", id: 6, keyName: "dprStatus", type: "pie" },

  { name: "Service Request Status", id: 8, keyName: "srStatus", type: "pie" },
  {
    name: "Service Request Priority",
    id: 9,
    keyName: "srPriority",
    type: "pie",
  },
  {
    name: "Delivery Delays",
    id: 10,
    keyName: "deliveryDelay",
    type: "deliverydelay",
  },
];

export const drawerNameStore = [
  "Operational Metrics",
  "Purchase Metrics",
  "Delivery Metrics",
  "Invoice Metrics",
];
