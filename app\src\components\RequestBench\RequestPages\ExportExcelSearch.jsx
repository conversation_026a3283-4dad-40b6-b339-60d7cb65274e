import { useState } from "react";
import { useSelector } from "react-redux";
import ExportExcelFilterFields from "@components/Common/ExportExcelFilterFields";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { REGION, MATERIAL_TYPE_DRODOWN, EXPORT_EXCEL_KEYS } from "@constant/enum";
import { ERROR_MESSAGES, EXPORT_PARAMETERS, LOADING_MESSAGE, REQUEST_TYPE, SUCCESS_MESSAGES } from "../../../constant/enum";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import { doAjax } from "../../Common/fetchService";
import { destination_MaterialMgmt } from "../../../destinationVariables";
import { END_POINTS } from "../../../constant/apiEndPoints";

const ExportExcelSearch = ({ openSearch, setOpenSearch, onSearchComplete }) => {
    const [blurLoading, setBlurLoading] = useState(false);
    const dropDownDataFromRedux = useSelector((state) => state.AllDropDown.dropDown);
    const [loaderMessage, setLoaderMessage] = useState("");
    const [successMsg, setSuccessMsg] = useState(false);
    const [messageDialogMessage, setMessageDialogMessage] = useState("");
    const [alertType, setAlertType] = useState("");
    const [openSnackbar, setOpenSnackbar] = useState(false);

    const handleSnackBarOpen = () => {
      setOpenSnackbar(true);
    };

    const handleSnackBarClose = () => {
      setOpenSnackbar(false);
    };

    const handleDownload = (searchParams) => {
        setLoaderMessage(LOADING_MESSAGE?.REPORT_LOADING);
        setBlurLoading(true);
        let payload = {
            "orgDetails" : [
            {
                "material" : searchParams?.[EXPORT_EXCEL_KEYS?.MATERIAL_NUMBER]?.[0]?.code || "",
                "whseNo" : searchParams?.[EXPORT_EXCEL_KEYS?.WAREHOUSE]?.[0]?.code || "",
                "storLoc" : searchParams?.[EXPORT_EXCEL_KEYS?.STORAGE_LOCATION]?.[0]?.code || "",
                "salesOrg" : searchParams?.[EXPORT_EXCEL_KEYS?.SALES_ORG]?.[0]?.code || "",
                "distrChan" : searchParams?.[EXPORT_EXCEL_KEYS?.DISTRIBUTION_CHANNEL]?.[0]?.code || "",
                "valArea" : searchParams?.[EXPORT_EXCEL_KEYS?.PLANT]?.[0]?.code || "",
                "plant" : searchParams?.[EXPORT_EXCEL_KEYS?.PLANT]?.[0]?.code || ""
            }],
            "region": searchParams?.[EXPORT_EXCEL_KEYS?.REGION]?.[0]?.code || "",
            "scenario": REQUEST_TYPE?.EXTEND_WITH_UPLOAD,
            "matlType": "ALL",
            "dtName": "MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",
            "version": "v1",
        };
        const hSuccess = (response) => {
            if (response?.size == 0) {
            setBlurLoading(false);
            setLoaderMessage("");
            setSuccessMsg(true);
            setMessageDialogMessage(ERROR_MESSAGES?.DATA_NOT_FOUND_FOR_SEARCH);
            setAlertType("danger");
            handleSnackBarOpen();
            return
            }
            setOpenSearch(false)
            const href = URL.createObjectURL(response);
            const link = document.createElement("a");

            link.href = href;
            link.setAttribute("download", 'SAP Excel Report.xlsx');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(href);

            setBlurLoading(false);
            setLoaderMessage("");
            setSuccessMsg(true);
            setMessageDialogMessage(SUCCESS_MESSAGES?.SAP_DOWNLOAD_SUCCESS);
            setAlertType("success");
            handleSnackBarOpen();
        }
        const hError = () => {
            setBlurLoading(false);
            setSuccessMsg(true);
            setMessageDialogMessage(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL);
            setAlertType("danger");
            handleSnackBarOpen();
          }

        const downloadUrl = `/${destination_MaterialMgmt}${END_POINTS.EXCEL.DOWNLOAD_EXCEL_SAP_REPORT}`;
        doAjax(downloadUrl, "postandgetblob", hSuccess, hError, payload);
    };

    return (
        <>
            <ExportExcelFilterFields
                open={openSearch}
                onClose={() => setOpenSearch(false)}
                parameters={EXPORT_PARAMETERS}
                onSearch={(params, skip, callback) => handleDownload(params, skip, callback)}
                templateName={"Export"}
                allDropDownData={dropDownDataFromRedux}
                buttonName={"Export"}
            />
            <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage}/>
            {successMsg && (
                <ReusableSnackBar
                    openSnackBar={openSnackbar}
                    alertMsg={messageDialogMessage}
                    alertType={alertType}
                    handleSnackBarClose={handleSnackBarClose}
                />
        )}
        </>
    );
};

export default ExportExcelSearch;