import {
  Backdrop,
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  FormControl,
  Grid,
  MenuItem,
  Select,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import SearchBar from "../../../../common/SearchBar";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import {
  button_Primary,
  outermostContainer_Information,
  outermostContainer,
  font_Small,
} from "../../../../common/commonStyles";
import EditUserDetails from "./EditUserDetails";
import ReusableTable from "../../../../common/ReusableTable";
import axios from "axios";
import { useSelector } from "react-redux";
import { destination_ManageAccount, destination_PR, destination_Po } from "../../../../../destinationVariables";
import { doAjax } from "../../../../common/fetchService";

const UserDetailsPage = () => {
  const [isLoading, setisLoading] = useState(true);
  //<--Varibles for user data fields
  const [supplierDetails, setsupplierDetails] = useState({});
  const [companyDetails, setcompanyDetails] = useState({});
  const [addressAndContact, setaddressAndContact] = useState({});
  const [bankInfo_Data, setbankInfo_Data] = useState([]);
  const [registration_Data, setregistration_Data] = useState([]);
  const [additionalContact_Data, setadditionalContact_Data] = useState([]);
  const [documentDetails_Data, setdocumentDetails_Data] = useState([]);

  let companyCodes
  let userDetails
  let countryList
  let regionList
  const userData = useSelector(
    (state) => state.userManagement.userData
  );

  const fetchUserDetails = async () => {
    
    await doAjax(
      `/${destination_ManageAccount}/userManagement/getUserDetailsFromOdata/vendorId/${userData?.supplierId}`,'get',(res)=>{
        userDetails = res
      }
    );
    await doAjax(`/${destination_Po}/Odata/populateCompanyCodeDetails`,'get',(data) => {
      companyCodes = data
       // console.log(data,'compdata')
     })
      
    // console.log(response);
    let data = userDetails?.d?.results[0];
    await doAjax(`/${destination_PR}/Odata/Region/${data?.Country}`,`GET`,(res)=> regionList = res)
    await doAjax(`/${destination_PR}/Odata/Country`,`GET`,(res)=> countryList = res)

    let profileData = {
      supplierDetails: {
        supplierName: data?.Name,
        supplierType: "",
        partnerType: "",
        supplierEmail: data?.ToEmail?.results[0]?.EMailAddress,
        supplierNumber: userData?.supplierId,
      },
      bankInfo: {
        bankName: data?.ToBank?.results[0]?.BankAccount,
        accountNumber:
          data?.ToBank?.results[0]?.AccountNumber ??
          data?.ToBank?.results[0]?.IBAN,
        accountHolderName: data?.ToBank?.results[0]?.Accountholder,
        swiftCodeBankKey: data?.ToBank?.results[0]?.Banknumber,
      },
      registration: {
        country: `${data?.Country} - ${countryList[data?.Country]}`,
        taxRegistrationId: data?.TaxNumber,
        type: data?.taxtype,
      },
      companyDetails: {
        companyName: `${data?.ToCompanyData?.results[0]?.CompanyCode} - ${companyCodes[data?.ToCompanyData?.results[0]?.CompanyCode]}`,
        areaOfBusiness: "",
        currency: "",
      },
      addressAndContact: {
        street1: data?.Street,
        street2: data?.Street2,
        street3: data?.Street3,
        street4: data?.Street4,
        street5: data?.Street5,
        city: data?.City,
        country: `${data?.Country} - ${countryList[data?.Country]}`,
        fax: data?.ToFax?.results[0]?.Fax,
        companyPhone: data?.ToPhone?.results[0]?.Telephoneno,
        website: "",
        region: `${data?.Region} - ${regionList[data?.Region]}`,
        pinCode: data?.PostalCode
      },
      additionalContacts: [
      ],
    };
    let returnValue = (data)=>{
      //check the values
      //check if values are not === null, undefined, ''
      //return [] if all values are null or undefined or ""
      // debugger
      let list = Object.values(data)
      let count=0;
      list.forEach((item)=>{
        if(item !== undefined || "" || null) count++
      })
      return count === 0 ? [] : [data]
    }
    // console.log(returnValue(profileData.registration))
    setbankInfo_Data(returnValue(profileData.bankInfo));
    setcompanyDetails(profileData.companyDetails);
    setregistration_Data(returnValue(profileData.registration));
    setsupplierDetails(profileData.supplierDetails);
    setaddressAndContact(profileData.addressAndContact);
    setisLoading(false);
  };

  //<-- Functions and controllers for edit -->
  const [editState, seteditState] = useState(false);
  const handleOnClickEdit = () => {
    editState ? seteditState(false) : seteditState(true);
  };
  let bankInfo_Columns= [
    {
      field: "bankName",
      headerName: "Bank Name",
      flex: 1,
     
    },
    {
      field: "accountNumber",
      headerName: "Bank Account Number/IBAN Code",
      flex: 1,
     
    },
    {
      field: "accountHolderName",
      headerName: "Bank Holder Name",
      flex: 1,
     
    },
    {
      field: "currency",

      headerName: "Currency",
      flex: 1,

    },
    {
      field: "swiftCodeBankKey",
  
      headerName: "Swift Code/Bank Key",
      flex: 1,
 
    },
  ];
  let companyDetails_fieldData = [
    {
      name: "companyName",
      label: "Company Name",
      value: companyDetails,
      disabled: true,
    },
    {
      name: "areaOfBusiness",
      label: "Area Of Business",
      value: companyDetails,
      disabled: true,
    },
    {
      name: "registeredCapital",
      label: "Registered Capital",
      value: companyDetails,
      disabled: true,
    },
    {
      name: "currency",
      label: "Currency",
      value: companyDetails,
      disabled: true,
    },
    {
      name: "totalEmployees",
      label: "Total Employees",
      value: companyDetails,
      disabled: true,
    },
    {
      name: "productCategory",
      label: "Product Category",
      value: companyDetails,
      disabled: true,
      isRequired: true,
    },
    {
      name: "accreditionAndCertification",
      label: "Accreditions & Certifications",
      value: companyDetails,
      disabled: true,
    },
    {
      name: "groupOfCompany",
      label: "Group Of Company",
      value: companyDetails,
      disabled: true,
    },
  ];
  let addressAndContact_fieldData = [
    {
      name: "street1",
      label: "Street 1",
      isRequired: true,
      value: addressAndContact,
      disabled: true,
    },
    {
      name: "street2",
      label: "Street 2",
      isRequired: false,
      value: addressAndContact,
      disabled: true,
    },
    {
      name: "street3",
      label: "Street 3",
      isRequired: false,
      value: addressAndContact,
      disabled: true,
    },
    {
      name: "street4",
      label: "Street 4",
      isRequired: true,
      value: addressAndContact,
      disabled: true,
    },
    {
      name: "street5",
      label: "Street 5",
      isRequired: true,
      value: addressAndContact,
      disabled: true,
    },
    {
      name: "city",
      label: "City",
      isRequired: true,
      value: addressAndContact,
      disabled: true,
    },
    {
      name: "pinCode",
      label: "Pin Code",
      isRequired: true,
      value: addressAndContact,
      disabled: true,
    },
    {
      name: "country",
      label: "Country",
      isRequired: true,
      value: addressAndContact,
      disabled: true,
    },
    {
      name: "companyPhone",
      label: "Company Phone",
      isRequired: true,
      value: addressAndContact,
      disabled: true,
    },
    {
      name: "fax",
      label: "Fax",
      isRequired: false,
      value: addressAndContact,
      disabled: true,
    },
    {
      name: "website",
      label: "Website",
      isRequired: true,
      value: addressAndContact,
      disabled: true,
    },
    ,
    {
      name: "region",
      label: "Region",
      isRequired: true,
      value: addressAndContact,
      disabled: true,
    },
  ];
  let supplierDetails_fieldData = [
    {
      name: "supplierName",
      label: "Supplier Name",
      value: supplierDetails,
      disabled: true,
    },
    {
      name: "supplierType",
      label: "Supplier Type",
      value: supplierDetails,
      disabled: true,
      isRequired: true,
    },
    {
      name: "supplierNumber",
      label: "Supplier Number",
      value: supplierDetails,
      disabled: true,
      isRequired: true,
    },
    {
      name: "partnerType",
      isRequired: true,
      label: "Partner Type",
      value: supplierDetails,
      disabled: true,
    },
    {
      name: "supplierEmail",
      isRequired: true,
      label: "Email ID",
      value: supplierDetails,
      disabled: true,
    },
  ];
  let taxAndRegistration_Column = [
    {
      field: "id",
      headerName: "id",
      hide: true,
      width: 200,
    },
    {
      field: "country",
      headerName: "Country",
      flex: 1,
    
    },
    {
      field: "type",
      headerName: "Type",
      flex: 1,
    
    },
    {
      field: "taxRegistrationId",
      headerName: "Tax Registration ID",
      flex: 1,

    },
    {
      field: "IfOther",
      headerName: "If other",
      flex: 1,

    },
  ];
  let additionalContactColumn = [
    {
      field: "id",
      headerName: "id",
      hide: true,
      width: 200,
    },
    {
      field: "function",
      headerName: "Function",
      flex: 1,
    },
    {
      field: "contactName",
      headerName: "Contact Name",
      flex: 1,
    },
    {
      field: "position",
      headerName: "Position",
      flex: 1,
    },
    {
      field: "phoneNumber",
      headerName: "Contact Phone",
      flex: 1,
    },
    {
      field: "email",
      headerName: "Email",
      flex: 1,
    },
  ];
  let documentColumn = [
    {
      field: "id",
      headerName: "id",
      editable: false,
      hide: true,
      flex: 1,
    },
    {
      field: "documentType",
      headerName: "Document Type",
      flex: 1,

    },
    {
      field: "document",
      headerName: "Document Name",
      
      flex: 1,
    },
    {
      field: "updatedDate",
      headerName: "Updated On",
      flex: 1,
    },
    {
      field: "expiryDate",
      headerName: "ExpiryDate",
      flex: 1,
      type: "date",
    },
  ];
  const getTextFields = (data) => {
    return (
      <>
        <Card sx={{ width: "98%" }}>
          <CardContent>
            <Grid
              columns={12}
              container
              rowSpacing={1}
              sx={{ marginBottom: "0.5rem" }}
              spacing={2}
              justifyContent="start"
              alignItems="center"
            >
              {data?.map((item) => {
                return (
                  <Grid item xs={1} md={3}>
                    <Box sx={{ minWidth: 120 }}>
                      <Typography
                        sx={{ ...font_Small, display: "inline-block" }}
                      >
                        {item.label}
                      </Typography>

                      {!item.multiSelect && (
                        <TextField
                          fullWidth
                          size="small"
                          placeholder={item.label}
                          name={item.name}
                          InputProps={{
                            readOnly: item.disabled,
                          }}
                          value={item.value[item.name]}
                          onChange={item.onchange}
                        ></TextField>
                      )}
                    </Box>
                  </Grid>
                );
              })}
            </Grid>
          </CardContent>
        </Card>
      </>
    );
  };
  useEffect(() => {
    fetchUserDetails();
  }, []);

  return (
    <>
      {/* Loader */}
      <Backdrop className="backdrop" open={isLoading}>
        <CircularProgress color="primary" />
      </Backdrop>
      {editState ? (
        <EditUserDetails seteditState={seteditState} editState={editState} />
      ) : (
        <div
          className="dailyproductionReport"
          style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}
        >
          <Stack spacing={1}>
            {/* Information */}
            <Grid container sx={outermostContainer_Information}>
              <Grid item md={5}>
                <Typography variant="h3">
                  <strong>My Account</strong>
                </Typography>
                <Typography variant="body2" color="#777">
                  This view displays the User Profile to supplier and allows
                  them to edit it
                </Typography>
              </Grid>
              <Grid item md={7} sx={{ display: "flex" }}>
                <Grid
                  container
                  direction="row"
                  justifyContent="flex-end"
                  alignItems="center"
                  spacing={0}
                >
                  <Button
                    onClick={handleOnClickEdit}
                    variant="contained"
                    startIcon={<EditOutlinedIcon />}
                    sx={{ ...button_Primary }}
                  >
                    Edit Account
                  </Button>
                </Grid>
              </Grid>
            </Grid>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                width: "100%",
              }}
            >
              {/*supplier details */}
              <Stack
                direction={"column"}
                sx={{ width: "100%", padding: ".5rem" }}
              >
                <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                  Supplier Details
                </Typography>
                {getTextFields(supplierDetails_fieldData)}
              </Stack>

              {/*Company Details*/}
              <Stack
                direction={"column"}
                sx={{ width: "100%", padding: ".5rem" }}
              >
                <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                  Company Details
                </Typography>
                {getTextFields(companyDetails_fieldData)}
              </Stack>

              {/*Company Address and COntacts*/}
              <Stack
                direction={"column"}
                sx={{ width: "100%", padding: ".5rem" }}
              >
                <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                  Company Address and Contacts
                </Typography>
                {getTextFields(addressAndContact_fieldData)}
              </Stack>

              {/*Bank info*/}
              <Stack
                direction={"column"}
                sx={{ width: "100%", padding: ".5rem" }}
              >
                <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                  Bank Information
                </Typography>
                <ReusableTable
                  width="100%"
                  rows={bankInfo_Data}
                  columns={bankInfo_Columns}
                  hideFooter={true}
                  isLoading={false}
                />
                {/* {getTextFields(bankInfo_fieldData)} */}
              </Stack>

              {/*Site Tax ID /Registration*/}
              <Stack
                direction={"column"}
                sx={{ width: "100%", padding: ".5rem" }}
              >
                <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                  Site Tax ID/ Registration
                </Typography>
                <ReusableTable
                  width="100%"
                  rows={registration_Data}
                  columns={taxAndRegistration_Column}
                  hideFooter={true}
                  isLoading={false}
                />
                {/* {getTextFields(siteTaxIDRegistration_fieldData)} */}
              </Stack>
              {/*Additional contact Information*/}

              <Stack
                direction={"column"}
                sx={{ width: "100%", padding: ".5rem" }}
              >
                <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                  Additional Contacts
                </Typography>
                <ReusableTable
                  width="100%"
                  rows={additionalContact_Data}
                  columns={additionalContactColumn}
                  hideFooter={true}
                  isLoading={false}
                />
              </Stack>
              {/*Additional contact Information*/}
              <Stack
                direction={"column"}
                sx={{ width: "100%", padding: ".5rem" }}
              >
                <Typography variant="h6" sx={{ margin: ".5rem 0px" }}>
                  Uploaded Documents
                </Typography>
                {
                  <ReusableTable
                    width="100%"
                    status_onRowDoubleClick={false}
                    rows={documentDetails_Data}
                    columns={documentColumn}
                    hideFooter={true}
                    isLoading={false}
                  />
                }
              </Stack>
            </Box>
          </Stack>
        </div>
      )}
    </>
  );
};

export default UserDetailsPage;
