import { doAjax } from "@components/Common/fetchService";
import { END_POINTS } from "@constant/apiEndPoints";
import { useSelector } from "react-redux";
import { destination_IDM } from "../../src/destinationVariables";
import { API_CODE } from "@constant/enum";
import { useState } from "react";

const useCustomDtCall = () => {
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const [dtData, setDtData] = useState(null);
  const [error, setError] = useState(null);

  const getDtCall = async (payload,customParam='') => {
    try {
      const hSuccess = (data) => {
        if (data?.statusCode === API_CODE.STATUS_200) {
            setDtData({data: data?.data,customParam});
        }
      };
      const hError = (error) => {
        setError(error);
      };

      if (applicationConfig.environment === "localhost") {
        await doAjax(`/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}`, "post", hSuccess, hError, payload);
      } else {
        await doAjax(`/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`, "post", hSuccess, hError, payload);
      }
    } catch (err) {
      setError(err);
    }
  };

  return { getDtCall, dtData, error };
};

export default useCustomDtCall;
