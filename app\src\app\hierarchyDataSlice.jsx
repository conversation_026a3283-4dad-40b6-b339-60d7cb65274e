import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  treeData: [],
  TreeChanges: {},
  NodesList: [],
  TagList: [],
  DescList: [],
  ReplaceNodesList: [],
  ReplaceTagList: [],
  DeleteNodeList: [],
  EditDescList: [],
  nodesListForDBDuplicateCheck: [],
  descListForDBDuplicateCheck: [],
  requestHeaderData: {},
  changeLog: [],
  changeLog: [],
};

const hierarchyDataSlice = createSlice({
  name: "hierarchyData",
  initialState,
  reducers: {
    setDisplayDataHierarchy(state, action) {
      state = action.payload.data;
      return state;
    },
    setTreeData(state, action) {
      state.treeData = action.payload;
    },
    clearHierarchyData(state) {
      state.treeData = [];
      state.NodesList = [];
      state.TagList = [];
      state.DescList = [];
      state.ReplaceNodesList = [];
      state.ReplaceTagList = [];
      state.DeleteNodeList = [];
      state.EditDescList = [];
      state.requestHeaderData = {};
      state.changeLog = [];
    },
    setTreeChanges(state, action) {
      state.TreeChanges = action.payload;
    },
    updateTreeChanges: (state, action) => {
      const { nodeLabel, changes } = action.payload;

      // Ensure TreeChanges exists
      if (!state.TreeChanges) {
        state.TreeChanges = {};
      }

      // Get existing node if available
      const existingNode = state.TreeChanges[nodeLabel] || {};

      // Merge and assign the node
      state.TreeChanges[nodeLabel] = {
        ...existingNode,
        ...changes,
      };
    },
    setRequestHeaderPayloadDataPCG(state, action) {
      const { keyName, data } = action.payload;
      if (keyName) {
        state.requestHeaderData[keyName] = data?.code
          ? data?.code
          : data
            ? data
            : "";
      } else {
        state.requestHeaderData = action.payload;
      }
    },


    // List setters
    setNodeList(state, action) {
      state.NodesList = action.payload;
    },
    setReplaceNodesList(state, action) {
      state.ReplaceNodesList = action.payload;
    },
    setTagList(state, action) {
      state.TagList = action.payload;
    },
    setReplaceTagList(state, action) {
      state.ReplaceTagList = action.payload;
    },
    setDescList(state, action) {
      state.DescList = action.payload;
    },
    setEditDescList(state, action) {
      state.EditDescList = action.payload;
    },
    setDeleteNodeList(state, action) {
      state.DeleteNodeList = action.payload;
    },
    setNodesListForDBDuplicateCheck: (state, action) => {
      state.nodesListForDBDuplicateCheck = action.payload;
    },
    setDescListForDBDuplicateCheck: (state, action) => {
      state.descListForDBDuplicateCheck = action.payload;
    },

    updateToChangeLog: (state, action) => {
      const { id, type, description, updatedBy, updatedOn } = action.payload;
      if (type) {
        state.changeLog?.push({ id, type, description, updatedBy, updatedOn });
      } else {
        state.changeLog = action.payload;
      }

    },

    resetHierarchyData(state) {
      return initialState;
    },

  },
});

export const {
  setDisplayDataHierarchy,
  setTreeData,
  setNodeList,
  setReplaceNodesList,
  setTagList,
  setReplaceTagList,
  setDescList,
  setEditDescList,
  setDeleteNodeList,
  setRequestHeaderPayloadDataPCG,
  resetHierarchyData,
  updateToChangeLog,
  clearHierarchyData,
  updateTreeChanges,
  setNodesListForDBDuplicateCheck,
  setDescListForDBDuplicateCheck
} = hierarchyDataSlice.actions;

export const hierarchyReducer = hierarchyDataSlice.reducer;
