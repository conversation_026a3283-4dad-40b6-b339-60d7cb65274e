import * as React from 'react';
import {
    Dialog,
    DialogTitle,    
    Slide
  } from "@mui/material";
import { colors } from '../../../constant/colors';

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function CustomDialog({isOpen, Title, children, handleClose, titleIcon, width}) {
  return (
    <React.Fragment>
      <Dialog
        open={isOpen}
        TransitionComponent={Transition}
        keepMounted
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
        maxWidth={width || "xs"}
        fullWidth={true}
      >
        <DialogTitle sx={{backgroundColor:colors.primary.light}}>
          <div style={{display:"flex",flexDirection:'row',alignItems:"center",gap:4}}>
            {titleIcon}{Title}
          </div>
        </DialogTitle>
        {children}
      </Dialog>
    </React.Fragment>
  );
}

