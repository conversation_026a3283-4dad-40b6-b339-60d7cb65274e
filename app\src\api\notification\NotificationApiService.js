import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import {baseUrl_Notification} from '@data/baseUrl';
const {VITE_APP_TOKEN} = import.meta.env;

export const notificationApi = createApi({
  reducerPath: 'notificationApi',
  baseQuery: fetchBaseQuery({
    baseUrl: baseUrl_Notification,
    prepareHeaders: (headers, { getState }) => {
      const { token, environment } = getState().applicationConfig || {};
      if (token && (environment === 'localhost' || environment === '127.0.0.1')) {
        headers.set('authorization', `Bearer ${VITE_APP_TOKEN}`);
      }
      headers.set("Access-Control-Allow-Origin", "*");
      return headers;
    },
  }),
  endpoints: () => ({}),
});