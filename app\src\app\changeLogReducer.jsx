import { createSlice } from "@reduxjs/toolkit";
import { handleChangeLogData } from '@helper/helper';

const initialState = {
    createPayloadCopyForChangeLog: [],
    createTemplateArray: [],
    createChangeLogData: {},
    createChangeLogDataGL:{},
    createChangeLogDataBK: {}
}

export const changeLogSlice = createSlice({
    name: "changeLog",
    initialState,
    reducers: {
      setCreatePayloadCopyForChangeLog:(state, action) => {
        if(Array.isArray(action.payload))
          state.createPayloadCopyForChangeLog = action.payload
        else if(typeof action.payload === "object" && action.payload !== null) {
          if (Array.isArray(state.changeFieldRows)) {
            state.createPayloadCopyForChangeLog = {};
          }
          state.createPayloadCopyForChangeLog = action.payload;
        }
      },
      setCreateTemplateArray: (state, action) => {
        const obj = action.payload;
        const index = state.createTemplateArray.findIndex(
          item => item.ObjectNo === obj.ObjectNo && item.FieldName === obj.FieldName
        );
        
        if (index !== -1) {
          state.createTemplateArray[index] = { ...state.createTemplateArray[index], ...obj };
        } else {
          state.createTemplateArray.push(obj);
        }
      },
      clearCreateTemplateArray: (state) => {
        state.createTemplateArray = initialState.createTemplateArray;
      },
      setCreateChangeLogDataGL: (state, action) => {
        console.log(action.payload,"ljsfljdl")
        state.createChangeLogDataGL = action.payload;
      },
      setCreateChangeLogDataBK: (state, action) => {
        const { uniqueId, changeLog } = action.payload;
        if (!state.createChangeLogDataBK[uniqueId]) {
          state.createChangeLogDataBK[uniqueId] = { changeLog: [] };
        }

        state.createChangeLogDataBK[uniqueId].changeLog = changeLog;
      },
      setCreateChangeLogData: (state, action) => {
        state.createChangeLogData = handleChangeLogData(state.createChangeLogData, action.payload);
      },
      clearCreateChangeLogData: (state) => {
        state.createChangeLogData = initialState.createChangeLogData;
      },
      clearCreateChangeLogDataGL: (state) => {
        state.createChangeLogDataGL = initialState.createChangeLogDataGL;
      },
     
    },
})

export const {
    setCreatePayloadCopyForChangeLog,
    setCreateTemplateArray,
    clearCreateTemplateArray,
    clearCreateChangeLogDataGL,
    setCreateChangeLogData,
    setCreateChangeLogDataGL,
    setCreateChangeLogDataBK,
    clearCreateChangeLogData
} = changeLogSlice.actions;

export const changeLogReducer = changeLogSlice.reducer;
