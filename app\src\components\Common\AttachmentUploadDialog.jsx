import {
  <PERSON>,
  <PERSON><PERSON>,
  Dialog,
  <PERSON>alogContent,
  DialogT<PERSON>le,
  Icon<PERSON>utt<PERSON>,
  <PERSON><PERSON>,
  Typography,
} from "@mui/material";
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import DriveFolderUploadIcon from "@mui/icons-material/DriveFolderUpload";
import { doAjax } from "./fetchService";
import DeleteIcon from '@mui/icons-material/Delete';
import CloseIcon from "@mui/icons-material/Close";
import utilityImages from "../../utilityImages";
const AttachmentUploadDialog = ({
  artifactId = "",
  artifactName = "",
  poNumber,
  isAnAttachment,
  setOpen,
  handleUpload,
  
}) => {
  const [metaData, setMetaData] = useState("Other");
  const [isDragOver, setIsDragOver] = useState(false);
  const [docList, setDocList] = useState([]);
  const [attachmentData, setattachmentData] = useState([]);
  const [attachmentOpenStatus, setattachmentOpenStatus] = useState(false);
  const [metaDataOptions, setmetaDataOptions] = useState(["Others"]);
  const [fileSizeError, setFileSizeError] = useState(false);

  const [payload, setPayload] = useState({
    attachments: [],
    comments: [],
  });
  let userData = useSelector((state) => state.userManagement.userData);
  let handleCloseAttachment = () => {
    setDocList([]);
    setOpen(false);
  };
  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    // Process the dropped files here
    handleOnChangeFile(files);
  };
  const addFile = () => {
    let addFileButton = document.getElementById("fileButton");
    addFileButton.click();
  };
  const handleOnChangeFile = (files) => {
    let filesWithId = [];
    files.forEach((item) => {
      // item.id = uuidv4();
      item.metaData = metaData;
      filesWithId.push(item);
    });
    setDocList((prev) => [...prev, ...filesWithId]);
  };

  let handleAdd = () => {
    //if size limit exceeds the given limit
    if (!fileSizeError && docList[0]) {
      let fileData = [...docList];
      handleUpload(docList);
      
      return ;
    }
    //if there are no file to upload


    // if (!docList[0]) {
    //   promptAction_Functions.handleOpenPromptBox("Warning", {
    //     title: "Warning",
    //     message: `Please add some files to upload`,
    //     severity: "warning",
    //     cancelButton: false,
    //   });
    // }
  };
  let handleCancel = () => {
    handleCloseAttachment();
  };
  let deleteAttachment = (id) => {
    let data = docList.filter((i) => i.id !== id);
    setDocList(data);
    // setPayload(data);
  };
  let checkFileSize = () => {
    let error = false;
    let size = 0;
    docList.forEach((i) => {
      size += i.size;
    });
    if (size > 500000000) {
      // file greater than 50mb
      // console.log('')
      promptAction_Functions.handleOpenPromptBox("ERROR", {
        title: "Warning",
        message: `Files size excceded`,
        severity: "warning",
        cancelButton: false,
      });
      setFileSizeError(true);
    } else {
      setFileSizeError(false);
    }
  };
  
  useEffect(() => {
    checkFileSize();
  }, [docList]);

  return (
    <div style={{display:'flex',flexDirection:'row'}}>
      <Dialog
    fullWidth
            maxWidth="sm"
    
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: '12px',
            padding: '1rem',
          },
          overflow:'hidden'
        }}
        
        open={true}
        onClose={handleCloseAttachment}
      >
         <DialogTitle sx={{ padding: '1rem 1.5rem' }}>
                     <Typography variant="h6" sx={{ fontWeight: 500 }}>
                       Add New Attachment
                     </Typography>
                     <IconButton
                 aria-label="close"
                 onClick={handleCancel}
                 sx={(theme) => ({
                   position: 'absolute',
                   right: 12,
                   top: 10,
                   color: theme.palette.grey[500],
                 })}
               >
                 <CloseIcon />
               </IconButton>
                   </DialogTitle>
        <DialogContent sx={{ padding: "1rem", height: "max-content" }} dividers>

          <Box
            className={`dropzone ${isDragOver ? "dragover" : ""}`}
            sx={{
              width: '100%',
              border: `2px dashed ${isDragOver ? '#3b30c8' : '#d0d5dd'}`,
              borderRadius: '8px',
              padding: '2rem',
              backgroundColor: isDragOver ? '#f8f9ff' : '#fafbff',
              // transition: 'all 0.3s ease',
              cursor: 'pointer',
              minHeight: '200px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            {!docList[0] && (
              <Box
                sx={{
                  padding: "2rem",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  flexDirection: "column",
                }}
              >
                  <CloudUploadIcon sx={{ fontSize: 48, color: '#3b30c8' }} />
                <Typography>Drag and drop file here</Typography>
                <Typography>or</Typography>
                <Typography>
                  <a onClick={addFile}>Browse file</a>
                </Typography>
              </Box>
            )}
           {docList.length > 0 && (
              <Box
                sx={{
                  padding: "0rem",
                  display: "flex",
                  flexDirection: "column",
                  gap: "1.5rem",
                }}
              >
                {docList.map((i, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      padding: "1rem",
                      borderRadius: "10px",
                      backgroundColor: "#fff",
                      border: "1px solid #ddd",
                      boxShadow: "0 2px 6px rgba(0, 0, 0, 0.1)",
                      transition: "background 0.2s ease-in-out, transform 0.1s ease-in-out",
                      '&:hover': {
                        backgroundColor: "#f1f5f9",
                        // transform: "scale(1.02)",
                      },
                      width: "100%",
                    }}
                  >
                    <img
                      style={{ width: "32px", height: "32px", marginRight: "1rem" }}
                      src={utilityImages[i.name?.split(".")[1]]}
                      alt="file-icon"
                    />
                    <Typography variant="body1" sx={{ flexGrow: 1, fontWeight: 500, fontSize: "1rem" }}>
                      {i.name}
                    </Typography>

                    <Typography
                      sx={{
                        marginLeft: "auto",
                        marginRight: "10%",
                        color: fileSizeError ? "error.main" : "gray",
                        fontWeight: 500,
                        fontSize: "0.9rem",
                      }}
                    >
                      {parseFloat(i.size / 1000000).toFixed(2)} MB
                    </Typography>
                    
                    <IconButton
                      id={`closeBtn-${i.id}`}
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteAttachment(i.id);
                      }}
                      sx={{
                        marginLeft: "0.5rem",
                        opacity: 0.8,
                        '&:hover': {
                          opacity: 1,
                        },
                      }}
                    >
                      <DeleteIcon fontSize="small" color="error" />
                    </IconButton>
                  </Box>
                ))}

                {/* Additional Information */}
                <Typography component="div" sx={{ padding: "", background: "#f9f9f9", borderRadius: "10px", fontSize: "0.95rem", lineHeight: "1.6" }}>
                <ul style={{ margin: "0", paddingLeft: "1.2rem" }}>
              <li style={{ width: "100%", marginBottom: "0.2rem", padding: "0.5rem", background: "#f5f5f5", borderRadius: "4px" }}>
                Mass Upload Process will start in the background. Once file is uploaded, you will receive a notification and an email containing the request ID number.
              </li>
              <li style={{ width: "100%", marginBottom: "0.2rem", padding: "0.5rem", background: "#f5f5f5", borderRadius: "4px" }}>
                You can visit the <strong>Request Bench</strong> tab, search for the request ID, and perform further actions on it.
              </li>
              <li style={{ width: "100%", marginBottom: "0.2rem", padding: "0.5rem", background: "#f5f5f5", borderRadius: "4px" }}>
                <strong>Note:</strong> All request IDs generated in the background will initially have the status <strong>Draft</strong> and will be <strong>Upload Successful</strong> or <strong>Upload Failed</strong> after Uploading the Excel based on it's validation.
              </li>
            </ul>


                </Typography>
              </Box>
            )}
            <input
              id="fileButton"
              multiple={false}
              accept=".jpeg, .jpg, .xls, .xlsx, .docx, .pdf"
              type="file"
              name="files"
              onChange={(e) => handleOnChangeFile([...e.target.files])}
              style={{ display: "none" }}
            />
          </Box>
          <Stack
            direction={"row"}
            sx={{
              justifyContent: "end",
              marginTop: "1rem",
              position: "relative",
            }}
          >
            <Typography
              sx={(theme) => ({
                color: theme.palette.grey,
                position: "absolute",
                left: 0,
                top: 0,
              })}
            >
              *Max file size 500 MB
            </Typography>
            <Button className="btn-mr" variant="contained" onClick={handleAdd}>
              Upload
            </Button>
            <Button variant="outlined" onClick={handleCancel}>
              Cancel
            </Button>
          </Stack>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AttachmentUploadDialog;