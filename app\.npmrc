registry=https://registry.npmjs.org/
 
@cw:registry=https://pkgs.dev.azure.com/InctureProducts/_packaging/Workbox/npm/registry/
 
always-auth=true
 
; begin auth token
 
//pkgs.dev.azure.com/InctureProducts/_packaging/Workbox/npm/registry/:username=InctureProducts
 
//pkgs.dev.azure.com/InctureProducts/_packaging/Workbox/npm/registry/:_password=QXhPUEdSTDdTbE9ZNnpYV1BBZWZpaGNkY0FyWTl3SG93a0RScDg3RGh4c1c1ZGt6ckJDM0pRUUo5OUJDQUNBQUFBQUoweDltQUFBU0FaRE8xdzd6==
 
//pkgs.dev.azure.com/InctureProducts/_packaging/Workbox/npm/registry/:email=<EMAIL>
 
//pkgs.dev.azure.com/InctureProducts/_packaging/Workbox/npm/:username=InctureProducts
 
//pkgs.dev.azure.com/InctureProducts/_packaging/Workbox/npm/:_password=QXhPUEdSTDdTbE9ZNnpYV1BBZWZpaGNkY0FyWTl3SG93a0RScDg3RGh4c1c1ZGt6ckJDM0pRUUo5OUJDQUNBQUFBQUoweDltQUFBU0FaRE8xdzd6==
 
//pkgs.dev.azure.com/InctureProducts/_packaging/Workbox/npm/:email=<EMAIL>
 
; end auth token