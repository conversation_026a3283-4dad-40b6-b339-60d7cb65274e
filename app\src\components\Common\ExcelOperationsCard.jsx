import { Grid, Paper, Typography, Divider, Box } from "@mui/material";
import CloudDownloadIcon from "@mui/icons-material/CloudDownload";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import AttachmentUploadDialog from "./AttachmentUploadDialog";
import useLang from "@hooks/useLang";

const ExcelOperations = ({ handleDownload, setEnableDocumentUpload, enableDocumentUpload, handleUploadMaterial }) => {
  const { t } = useLang();
  return (
    <Grid
      item
      md={12}
      sx={{
        backgroundColor: "white",
        maxHeight: "max-content",
        height: "max-content",
        borderRadius: "12px",
        border: "1px solid #E0E0E0",
        mt: 1,
        boxShadow: "0px 8px 24px rgba(48, 38, 185, 0.12)",
        padding: "1.5rem 2rem",
      }}
    >
      <Grid container alignItems="center" sx={{ mb: 2 }}>
        <Typography sx={{ fontSize: "12px", fontWeight: "700" }}>{t("Excel Operations")}</Typography>
      </Grid>

      <Grid container spacing={2} sx={{ display: "flex", justifyContent: "center", position: "relative" }}>
        <Grid item xs={12} md={6} sx={{ display: "flex", justifyContent: "center" }}>
          <Paper
            elevation={0}
            onClick={handleDownload}
            sx={{
              width: "100%",
              maxWidth: "280px",
              height: "200px",
              borderRadius: "16px",
              padding: "1.5rem",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              position: "relative",
              overflow: "hidden",
              cursor: "pointer",
              transition: "all 0.3s ease",
              "&:hover": {
                backgroundColor: "rgba(63, 140, 218, 0.1)",
              },
            }}
          >
            <Box sx={{ backgroundColor: "rgba(63, 140, 218, 0.2)", borderRadius: "50%", padding: "16px", marginBottom: "16px" }}>
              <CloudDownloadIcon sx={{ fontSize: 64, color: "#1976d2", filter: "drop-shadow(0px 4px 6px rgba(7, 31, 54, 0.3))" }} />
            </Box>
            <Typography sx={{ fontSize: "16px", fontWeight: "600", color: "#0D47A1", mb: 1 }}>{t("Download Excel")}</Typography>
            <Typography sx={{ fontSize: "12px", color: "#1565C0", textAlign: "center" }}>{t("Download Excel if you have not downloaded yet")}</Typography>
          </Paper>
        </Grid>

        <Divider
          orientation="vertical"
          flexItem
          sx={{ position: "absolute", height: "80%", top: "10%", left: "50%", display: { xs: "none", md: "block" } }}
        />

        <Grid item xs={12} md={6} sx={{ display: "flex", justifyContent: "center" }}>
          <Paper
            elevation={0}
            onClick={() => setEnableDocumentUpload(true)}
            sx={{
              width: "100%",
              maxWidth: "280px",
              height: "200px",
              borderRadius: "16px",
              padding: "1.5rem",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              position: "relative",
              overflow: "hidden",
              cursor: "pointer",
              transition: "all 0.3s ease",
              "&:hover": {
                backgroundColor: "rgba(55, 43, 224, 0.1)",
              },
            }}
          >
            <Box sx={{ backgroundColor: "rgba(55, 43, 224, 0.2)", borderRadius: "50%", padding: "16px", marginBottom: "16px" }}>
              <CloudUploadIcon sx={{ fontSize: 64, color: "#3026B9", filter: "drop-shadow(0px 4px 6px rgba(58, 48, 199, 0.4))" }} />
            </Box>
            <Typography sx={{ fontSize: "16px", fontWeight: "600", color: "#3026B9", mb: 1 }}>{t("Upload Excel")}</Typography>
            <Typography sx={{ fontSize: "12px", color: "#5148C2", textAlign: "center" }}>{t("Upload your Excel after doing the necessary changes")}</Typography>
          </Paper>
        </Grid>
      </Grid>
      {enableDocumentUpload && <AttachmentUploadDialog artifactId="" artifactName="" setOpen={setEnableDocumentUpload} handleUpload={handleUploadMaterial} />}
    </Grid>
  );
};

export default ExcelOperations;
