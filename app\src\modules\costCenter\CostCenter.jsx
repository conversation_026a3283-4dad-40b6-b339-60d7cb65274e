import React, { use<PERSON><PERSON>back, useMemo } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  Button,
  Checkbox,
  Grid,
  Paper,
  IconButton,
  Typography,
  TextField,
  Box,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  BottomNavigation,
  ListItemText,
  Autocomplete,
  InputAdornment,
  FormControlLabel,
  FormGroup,
  CircularProgress,
} from "@mui/material";

import moment from "moment/moment";
import { Stack } from "@mui/system";
import useGenericDtCall from "@hooks/useGenericDtCall";
import Select from "@mui/material/Select";
import { FormControl, MenuItem } from "@mui/material";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {API_CODE, DECISION_TABLE_NAME, ERROR_MESSAGES, PAGESIZE, SEARCH_FIELD_TYPES, VISIBILITY_TYPE} from "@constant/enum";
import ReusableDialog from "../../components/Common/ReusableDialog";
import { useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import styled from "@emotion/styled";
import {
  commonFilterClear,
  commonFilterUpdate,
} from "../../app/commonFilterSlice";
import { v4 as uuidv4 } from "uuid";
import {
  button_Marginleft,
  button_Outlined,
  button_Primary,
  button_Outlined,
  container_filter,
  font_Small,
  outerContainer_Information,
  outermostContainer,
  outermostContainer_Information,
} from "../../components/Common/commonStyles";
import {
  destination_CostCenter_Mass,
  destination_IDM,
} from "../../destinationVariables";
import { doAjax } from "../../components/Common/fetchService";
import ClearIcon from "@mui/icons-material/Clear";
import ReusableTable from "../../components/Common//ReusableTable";
import SearchIcon from "@mui/icons-material/Search";
import { setDropDown } from "../../app/dropDownDataSlice";
import {
  clearCostCenter,
  clearSingleCostCenter,
} from "../../app/costCenterTabsSlice";
import { clearCostCenterPayload } from "../../app/costCenterTabSliceET";
import { clearArtifactId } from "../../app/initialDataSlice";
import { saveExcel } from "../../functions";
import { clearTaskData, setTaskData } from "../../app/userManagementSlice";

import ReusablePreset from "../../components/Common/ReusablePresetFilter";

import { clearPayload } from "../../app/editPayloadSlice";

import FilterListIcon from "@mui/icons-material/FilterList";
import { colors } from "@constant/colors";
import { REQUEST_TYPE } from "@constant/enum";
import useLang from "@hooks/useLang";

const CostCenter = () => {
  const [isDropDownLoading, setIsDropDownLoading] = useState(false); // State for loader

  const [openSearchDialog, setOpenSearchDialog] = useState(false);
  const [searchDialogTitle, setSearchDialogTitle] = useState("");
  const [searchDialogMessage, setSearchDialogMessage] = useState();
  const [isPresetActive, setIsPresetActive] = useState(false);

  const [descInputValue, setDescInputValue] = useState("");

  const [userInputValue, setUserInputValue] = useState("");
  const [personInputValue, setPersonInputValue] = useState("");
  const [ccInputValue, setCcInputValue] = useState("");
  const [selectedValues, setSelectedValues] = useState({});
  const [selectedCreatedBy, setselectedCreatedBy] = useState([]);
  const [selectedProfitCenter, setselectedProfitCenter] = useState([]);
  const [selectedCostCenter, setselectedCostCenter] = useState([]);
  const [selectedPersonResponsible, setselectedPersonResponsible] = useState(
    []
  );
  const [selectedStreet, setselectedStreet] = useState([]);
  const [selectedLocation, setselectedLocation] = useState([]);
  const [selectedCostCenterName, setselectedCostCenterName] = useState([]);
  const [selectedUserResponsible, setselectedUserResponsible] = useState([]);
  const [selectedPresetCreatedBy, setselectedPresetCreatedBy] = useState([]);
  const [selectedPresetProfitCenter, setselectedPresetProfitCenter] = useState(
    []
  );
  const [selectedPresetCostCenter, setselectedPresetCostCenter] = useState([]);
  const [selectedPresetPersonResponsible, setselectedPresetPersonResponsible] =
    useState([]);
  const [selectedPresetLocation, setselectedPresetLocation] = useState([]);
  const [selectedPresetCostCenterName, setselectedPresetCostCenterName] =
    useState([]);
  const [selectedPresetUserResponsible, setselectedPresetUserResponsible] =
    useState([]);
  const [selectedPresetComanyCode, setselectedPresetComanyCode] = useState([]);
  const [selectedPresetDescription, setselectedPresetDescription] = useState(
    []
  );
  const [selectedPresetCCcategory, setselectedPresetCCcategory] = useState([]);

  const [dynamicColumns, setDynamicColumns] = useState([]);
  const {getDtCall: getMasterDataColumn, dtData:masterDataDtResponse} = useGenericDtCall();
  const {getDtCall:getSearchParams, dtData:dtSearchParamsResponse } = useGenericDtCall();
  const [searchParameters, setSearchParameters] = useState([]);
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const rmSearchForm = useSelector((state) => state.commonFilter["CostCenter"]);
  const [selectedComanyCode, setselectedComanyCode] = useState([]);
  const [selectedDescription, setselectedDescription] = useState([]);
  const [selectedCCcategory, setselectedCCcategory] = useState([]);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [count, setCount] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const [skip, setSkip] = useState(0);
  const [costCenterLength, setCostCenterLength] = useState("");
  const [dataListCOA, setDataListCOA] = useState([]);
  const [selectedListItemsCOA, setSelectedListItemsCOA] = useState([]);
  const [alignment, setAlignment] = useState("ALL OTHER CHANGES");
  const [openSelectColumnDialog, setOpenSelectColumnDialog] = useState(false);
  const [filteredRuleData, setFilteredRuleData] = useState(false);
  const [filteredRuleDataMass, setFilteredRuleDataMass] = useState(false);
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [openDownloadChangeDialog, setOpenDownloadChangeDialog] =
    useState(false);
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [requiredArrayDetailsMass, setRequiredArrayDetailsMass] = useState([]);
  const { t } = useLang();
  const [items,setItem] = useState();

  const memoizedCCValue = useMemo(() => {
    if (selectedCostCenter.length > 0) return selectedCostCenter;
    else if (selectedPresetCostCenter.length > 0)
      return selectedPresetCostCenter;
    else return [];
  }, [selectedCostCenter, selectedPresetCostCenter]);

  const memoizedLDValue = useMemo(() => {
    if (selectedDescription.length > 0) return selectedDescription;
    else if (selectedPresetDescription.length > 0)
      return selectedPresetDescription;
    else return [];
  }, [selectedDescription, selectedPresetDescription]);

  const memoizedPRValue = useMemo(() => {
    if (selectedPersonResponsible.length > 0) return selectedPersonResponsible;
    else if (selectedPresetPersonResponsible.length > 0)
      return selectedPresetPersonResponsible;
    else return [];
  }, [selectedPersonResponsible, selectedPresetPersonResponsible]);

  const memoizedURValue = useMemo(() => {
    if (selectedUserResponsible.length > 0) return selectedUserResponsible;
    else if (selectedPresetUserResponsible.length > 0)
      return selectedPresetUserResponsible;
    else return [];
  }, [selectedUserResponsible, selectedPresetUserResponsible]);

  const [selectedMassChangeRowData, setSelectedMassChangeRowData] = useState(
    []
  );

  const StyledAccordion = styled(Accordion)(({ theme }) => ({
    marginTop: "0px !important",
    border: `1px solid ${colors.primary.border}`,
    borderRadius: "8px",
    boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
    "&:not(:last-child)": {
      borderBottom: 0,
    },
    "&:before": {
      display: "none",
    },
  }));

  const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
    minHeight: "2rem !important",
    margin: "0px !important",
    backgroundColor: colors.primary.ultraLight,
    borderRadius: "8px 8px 0 0",
    transition: "all 0.2s ease-in-out",
    "&:hover": {
      backgroundColor: `${colors.primary.light}20`,
    },
  }));

  const LabelTypography = styled(Typography)({
    fontSize: "0.75rem",
    color: colors.primary.dark,
    marginBottom: "0.25rem",
    fontWeight: 500,
  });
  const ActionButton = styled(Button)({
  borderRadius: '4px',
  padding: '4px 12px',
  textTransform: 'none',
  fontSize: '0.875rem',
});
const ButtonContainer = styled(Grid)({
  display: 'flex',
  justifyContent: 'flex-end',
  paddingRight: '0.75rem',
  paddingBottom: '0.75rem',
  paddingTop: '0rem',
  gap: '0.5rem',

});


  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };
  const handlePageSizeChange = useCallback((event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
    setSkip(0);
  }, []);

  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };

  const handleOnClick = (costCenterNumber) => {
    // setViewDetailpage(true);
    //     dispatch(setHistoryPath({url:window.location.pathname, module:"po workbench"}));

    navigate(
      "/masterDataCockpit/materialMaster/displayMaterialDetail/" +
        materialNumber
    );
  };


  useEffect(() => {
    Object.keys(selectedValues).forEach((option) => {
      const tempSelected = selectedValues[option]
        ?.map((item) => item?.code)
        .join("$^$");
      let tempFilterData = {
        ...rmSearchForm,
        [option]: tempSelected,
      };

      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    });
  }, [selectedValues]);

  useEffect(() => {
    var tempCompanyCode = selectedComanyCode
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      "Company Code": tempCompanyCode,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedComanyCode]);

  useEffect(() => {
    var tempCreatedBy = selectedCreatedBy.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      createdBy: tempCreatedBy,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedCreatedBy]);

  useEffect(() => {
    var tempLocation = selectedLocation.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      location: tempLocation,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedLocation]);

  useEffect(() => {
    var tempCostCenterName = selectedCostCenterName
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      costCenterName: tempCostCenterName,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedCostCenterName]);

  useEffect(() => {
    var tempStreet = selectedStreet.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      street: tempStreet,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedStreet]);

  useEffect(() => {
    var tempPC = selectedProfitCenter.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      "Profit Center": tempPC,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedProfitCenter]);

  useEffect(() => {
    var tempCC = selectedCostCenter.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      "Cost Center": tempCC,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedCostCenter]);

  useEffect(() => {
    var tempUserResponsible = selectedUserResponsible
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      "User Responsible": tempUserResponsible,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedUserResponsible]);

  useEffect(() => {
    var tempPersonResponsible = selectedPersonResponsible
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      personResponsible: tempPersonResponsible,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedPersonResponsible]);

  useEffect(() => {
    var tempCostCenterCategory = selectedCCcategory
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      "Cost Center Category": tempCostCenterCategory,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedCCcategory]);

  useEffect(() => {
    var tempDescription = selectedDescription
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      description: tempDescription,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  }, [selectedDescription]);


  useEffect(() => {
    if (page * pageSize >= rmDataRows?.length) {
      getFilterBasedOnPagination();

    }
  }, [page, pageSize]);

  useEffect(() => {
    getFilter(skip + 1000);
  }, []);

  const [timerId, setTimerId] = useState(null);
  const [tableLoading, setTableLoading] = useState(false);
  const [value, setValue] = useState("1");

  const [rmDataRows, setRmDataRows] = useState([]);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [displayedFields, setDisplayedFields] = useState([]);
  const [filterFieldData, setFilterFieldData] = useState({});
  const [dynamicOptions, setDynamicOptions] = useState({});
  const dropDownData = useSelector((state) => state?.AllDropDown?.dropDown);
  const handleSelectAllCompanyCodes = () => {
    if (selectedComanyCode.length === dropDownData.CompanyCode.length) {
      setselectedComanyCode([]);
      setselectedPresetComanyCode([]);
    } else {
      setselectedComanyCode(dropDownData?.CompanyCode);
    }
  };

  const handleSelectAllCostCenter = () => {
    if (selectedCostCenter.length === dropDownData?.ETCCSearchData?.length) {
      setselectedCostCenter([]);
      setselectedPresetCostCenter([]);
    } else {
      setselectedCostCenter(dropDownData?.ETCCSearchData);
    }
  };

  const handleSelectAllPersonResponsible = () => {
    if (
      selectedPersonResponsible.length ===
      dropDownData?.ETPersonResponsibleSearch?.length
    ) {
      setselectedPersonResponsible([]);
      setselectedPresetPersonResponsible([]);
    } else {
      setselectedPersonResponsible(dropDownData?.ETPersonResponsibleSearch);
    }
  };

  const handleSelectAllUserResponsible = () => {
    if (
      selectedUserResponsible.length === dropDownData?.ETUserResponsible?.length
    ) {
      setselectedUserResponsible([]);
      setselectedPresetUserResponsible([]);
    } else {
      setselectedUserResponsible(dropDownData?.ETUserResponsible);
    }
  };

  const handleSelectAllDescription = () => {
    if (
      selectedDescription.length ===
      dropDownData?.ETDescriptionSearchData.length
    ) {
      setselectedDescription([]);
      setselectedPresetDescription([]);
    } else {
      setselectedDescription(dropDownData?.ETDescriptionSearchData);
    }
  };

  const handleSelectAllCCcategory = () => {
    if (
      selectedCCcategory.length ===
      dropDownData?.CostCenterCategorySearch.length
    ) {
      setselectedCCcategory([]);
      setselectedPresetCCcategory([]);
    } else {
      setselectedCCcategory(dropDownData?.CostCenterCategorySearch);
    }
  };

  const handleControllingArea = (value) => {
    const tempControllingArea = value;
    const tempFilterData = {
      ...rmSearchForm,
      controllingArea: tempControllingArea,
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
    // getHierarchyArea(tempFilterData);
  };

  const handleBlockingStatus = (e) => {
    if (e.target.value !== null) {
      var tempBlockingStatus = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        blockingStatus: tempBlockingStatus,
      };
      dispatch(
        commonFilterUpdate({
          module: "CostCenter",
          filterData: tempFilterData,
        })
      );
    }
  };

  let dynamicDataApis = {
    "FERC Indicator": `/${destination_CostCenter_Mass}/data/getSearchParamsFercInd`,
    "Functional Area": `/${destination_CostCenter_Mass}/data/getSearchParamsFuncArea`,
    "Country/Reg": `/${destination_CostCenter_Mass}/data/getSearchParamsCountryReg`,
  };
  const handleSelection = (event) => {
    const selectedItems = event.target.value;
    setSelectedOptions(selectedItems);
    setDisplayedFields([]);

    selectedItems.forEach(async (selectedItem) => {
      const apiEndpoint = dynamicDataApis[selectedItem];
      fetchOptionsForDynamicFilter(apiEndpoint, selectedItem);
    });
  };

  const isCompanyCodeSelected = (option) => {
    return selectedComanyCode.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isCostCenterSelected = (option) => {
    return selectedCostCenter.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isPersonResponsibleSelected = (option) => {
    return selectedPersonResponsible.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isUserResponsibleSelected = (option) => {
    return selectedUserResponsible.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isCCcategorySelected = (option) => {
    return selectedCCcategory.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };
  const isDescriptionSelected = (option) => {
    return selectedDescription.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const handleAddFields = () => {
    const numSelected = selectedOptions.length;
    const newFields = Array.from({ length: numSelected }, (_, index) => ({
      id: index,
      value: "",
    }));
    setDisplayedFields(newFields);
  };

  const handleFieldChange = (fieldId, value) => {
    setDisplayedFields(
      (selectedOptions) => selectedOptions?.map((option) => option)
      // prevFields?.map((field) => (field.id === fieldId ? { ...field, value } : field))
    );
  };

  const titleToFieldMapping = {
    "Short Description": "CostCenterName",
    "FERC Indicator": "fercIndicator",
    "Functional Area": "functionalArea",
    "Profit Center": "ProfitCenter",
    Street: "street",
    Location: "location",
    "Country/Reg": "countryreg",
    Region: "region",
    "Created On": "createdOn",
    "Created By": "createdBy",
    // "Person Responsible": "personResponsible",
    // "Business Area": "businessArea",
    // Add more mappings as needed
  };
  const names = ["Blocked", "Unblocked", ""];
  const getCompanyCode = () => {
    setIsDropDownLoading(true);
    const Payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      top: 200,
      skip: 0,
    };
    // var HA = "TZUS";
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompCodeSearch", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getSearchParamsCompCode`,
      "post",
      hSuccess,
      hError,
      Payload
    );
  };

  const getControllingArea = () => {
    setIsDropDownLoading(true);
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ControllingArea", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getSearchParamsConArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getUserResponsible = (user) => {
    setIsDropDownLoading(true);
    const payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      userRespons: user,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ETUserResponsible", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_CostCenter_Mass}/data/getUserResponsible`,
      `/${destination_CostCenter_Mass}/data/getSearchParamsUserRespons`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const getPersonReponsible = (person) => {
    setIsDropDownLoading(true);
    const payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      personRespons: person,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "ETPersonResponsibleSearch", data: data.body })
      );
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_ProfitCenter}/data/getCompCode?contrllingArea=${CA?.controllingArea?.code}`,
      `/${destination_CostCenter_Mass}/data/getSearchParamsPersonRespons`,
      "post",

      hSuccess,
      hError,
      payload
    );
  };
  const getCostCenterCategory = () => {
    setIsDropDownLoading(true);
    const payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "CostCenterCategorySearch", data: data.body })
      );
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getSearchParamsCCCategory`,
      "post",

      hSuccess,
      hError,
      payload
    );
  };
  const getDescription = (desc) => {
    setIsDropDownLoading(true);
    let payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      description: desc,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "ETDescriptionSearchData", data: data.body })
      );
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(

      `/${destination_CostCenter_Mass}/data/getSearchParamsDescription`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getCostCenterSearch = (cc) => {
    setIsDropDownLoading(true);
    let payload = {
      costCenter: cc,
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ETCCSearchData", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getSearchParamsCostCenter`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getCostCenter = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostCenter", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCenter`,
      "get",
      hSuccess,
      hError
    );
  };

  const getHierarchyArea = (CA) => {
    const payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      userRespons: "",
    };
    console.log("CA", CA);
    // var HA = "TZUS";
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "HierarchyAreaSearch", data: data.body })
      );
      console.log("data", data);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(

      `/${destination_CostCenter_Mass}/data/getSearchParamsHierarchyArea`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getFunctionalArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FunctionalArea", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getFunctionalArea`,
      "get",
      hSuccess,
      hError
    );
  };

  const getRegion = () => {
    setIsDropDownLoading(true);
    const payload = {
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      rolePrefix: "ETP",
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      setDynamicOptions((prev) => ({ ...prev, Region: data.body }));
      setIsDropDownLoading(false);
      // dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(

      `/${destination_CostCenter_Mass}/data/getSearchParamsRegion`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getNewControllingArea = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_CUSTOM_DROPDOWN_LIST",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MODULE": "Cost Center",
          "MDG_CONDITIONS.MDG_FIELD_NAME": "Controlling Area",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        const lookupData =
          data?.data?.result[0]?.MDG_CUSTOM_LOOKUP_ACTION_TYPE || [];
        console.log("questionData", lookupData);

        let lookupDataArr = [];
        lookupData?.map((itemData) => {
          let lookupDataHash = {};
          lookupDataHash["code"] = itemData?.MDG_LOOKUP_CODE;
          lookupDataHash["desc"] = itemData?.MDG_LOOKUP_DESC;
          lookupDataArr.push(lookupDataHash);
        });


        dispatch(
          setDropDown({ keyName: "NewControllingArea", data: lookupDataArr })
        );

      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  useEffect(() => {
    // getUserResponsible();
    getCostCenterCategory();
    getNewControllingArea();
    getControllingArea();
    // getPersonReponsible();
    getRegion();
    getFunctionalArea();
    getCostCenter();
    getCompanyCode();
    dispatch(setTaskData({}));
    dispatch(clearCostCenter());
    dispatch(clearPayload({}));
    dispatch(clearTaskData());
    dispatch(clearSingleCostCenter());
    dispatch(clearCostCenterPayload());
    dispatch(clearArtifactId());
  }, []);

  const fetchOptionsForDynamicFilter = (apiEndpoint, selectedItem) => {
    setIsDropDownLoading(true);
    let payload;
    if (selectedItem === "FERC Indicator") {
      payload = {
        controllingArea: rmSearchForm?.controllingArea?.code
          ? rmSearchForm?.controllingArea?.code === ""
            ? "ETCA"
            : rmSearchForm?.controllingArea?.code
          : "ETCA",
        rolePrefix: "ETP",
        fercInd: "",
        top: 200,
        skip: 0,
      };
    } else if (selectedItem === "Functional Area") {
      payload = {
        controllingArea: rmSearchForm?.controllingArea?.code
          ? rmSearchForm?.controllingArea?.code === ""
            ? "ETCA"
            : rmSearchForm?.controllingArea?.code
          : "ETCA",
        rolePrefix: "ETP",
        funcArea: "",
        top: 200,
        skip: 0,
      };
    } else {
      payload = {
        controllingArea: rmSearchForm?.controllingArea?.code
          ? rmSearchForm?.controllingArea?.code === ""
            ? "ETCA"
            : rmSearchForm?.controllingArea?.code
          : "ETCA",
        rolePrefix: "ETP",
        top: 200,
        skip: 0,
      };
    }

    const hSuccess = (data) => {
      const newOptions = data.body;
      // Merge the new options with the existing dynamicOptions
      //setDynamicOptions((prevOptions) => [...prevOptions, ...newOptions]);
      setDynamicOptions((prev) => ({ ...prev, [selectedItem]: newOptions }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(apiEndpoint, "post", hSuccess, hError, payload);
  };

  /* Setting Default Dates */
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 15);

  // Get Filter Data
  const getFilter = (fetchSkip) => {
    setTableLoading(true);
    setPage(0);
    let payload = {
      costCenterName: rmSearchForm?.costCenterName ?? "",
      costCenter: rmSearchForm?.["Cost Center"] ?? "",
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      companyCode: rmSearchForm?.["Company Code"] ?? "",
      profitCenter: rmSearchForm?.["Profit Center"] ?? "",
      hierarchyArea: rmSearchForm?.["Hierarchy Area"] ?? "",
      rolePrefix: "ETP",
      costCenterCategory: rmSearchForm?.["Cost Center Category"] ?? "",
      createdBy: rmSearchForm?.createdBy ?? "",
      fromDate:
        moment(rmSearchForm?.createdOn[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate:
        moment(rmSearchForm?.createdOn[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      businessArea: "",
      personResponsible: rmSearchForm?.personResponsible ?? "",
      userResponsible: rmSearchForm?.["User Responsible"] ?? "",

      functionalArea: rmSearchForm?.["Functional Area"] ?? "",
      fercIndicator: rmSearchForm?.["FERC Indicator"] ?? "",
      street: rmSearchForm?.street ?? "",
      location: rmSearchForm?.location ?? "",
      description: rmSearchForm?.description ?? "",
      country: rmSearchForm?.["Country/Reg"] ?? "",
      region: rmSearchForm?.["Region"] ?? "",
      blockingStatus:
        rmSearchForm?.blockingStatus === "Blocked"
          ? "X"
          : rmSearchForm?.blockingStatus === "Unblocked"
            ? "Y"
            : "",
      top: 100,
      skip: 0,

    };
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        var rows = [];
        for (let index = 0; index < data?.body?.list?.length; index++) {
          var tempObj = data?.body?.list[index];
          var tempRow = {
            id: uuidv4(),
            description: tempObj?.Description,
            controllingArea: tempObj?.controllingArea,
            companyCode: tempObj?.CompanyCode,
            hierarchyArea: tempObj?.HeirarchyArea,
            costCenterCategory: tempObj?.CCtrCategory,
            costCenter: tempObj?.costCenter,
            CostCenterName: tempObj?.CostCenterName,
            businessArea:
              tempObj["BusinessArea"] !== ""
                ? `${tempObj["BusinessArea"]}`
                : "Not Available",

            functionalArea:
              tempObj["FunctionalArea"] !== ""
                ? `${tempObj["FunctionalArea"]}`
                : "Not Available",
            personResponsible:
              tempObj["PersonResponsible"] !== ""
                ? `${tempObj["PersonResponsible"]}`
                : "Not Available",
            userResponsible:
              tempObj["UserResponsible"] !== ""
                ? `${tempObj["UserResponsible"]}`
                : "Not Available",
            fercIndicator:
              tempObj["FERCindicator"] !== ""
                ? `${tempObj["FERCindicator"]}`
                : "Not Available",
            street:
              tempObj["Street"] !== ""
                ? `${tempObj["Street"]}`
                : "Not Available",
            location:
              tempObj["Location"] !== ""
                ? `${tempObj["Location"]}`
                : "Not Available",
            countryreg:
              tempObj["Country"] !== ""
                ? `${tempObj["Country"]}`
                : "Not Available",
            region:
              tempObj["Region"] !== ""
                ? `${tempObj["Region"]}`
                : "Not Available",
            createdOn:
              tempObj["CreatedOn"] !== ""
                ? `${moment(tempObj["CreatedOn"]).format("DD MMM YYYY")}`
                : "Not Available",
            createdBy:
              tempObj["CreatedBy"] !== ""
                ? `${tempObj["CreatedBy"]}`
                : "Not Available",
            ProfitCenter:
              tempObj["ProfitCenter"] !== ""
                ? `${tempObj["ProfitCenter"]}`
                : "Not Available",
            blockingStatus:
              tempObj["BlockingStatus"] === "X" ? "Blocked" : "Unblocked",
          };
          rows.push(tempRow);
        }


        setRmDataRows(rows);
        setTableLoading(false);
        setroCount(rows.length);
        setCount(data?.body?.count);
      } else if (data.statusCode === 400) {
        setSearchDialogTitle("Warning");
        setSearchDialogMessage(
          "Please Select Lesser Fields as the URL is getting too long !!"
        );
        handleSearchDialogClickOpen();
      }
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCentersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const getFilterBasedOnPagination = (fetchSkip) => {
    setTableLoading(true);
    let payload = {
      costCenterName: rmSearchForm?.costCenterName ?? "",
      costCenter: rmSearchForm?.["Cost Center"] ?? "",
      //userId: userId?.UserId || compCodeData[0]?.UserId || "",
      rolePrefix: "ETP",
      controllingArea: rmSearchForm?.controllingArea?.code
        ? rmSearchForm?.controllingArea?.code === ""
          ? "ETCA"
          : rmSearchForm?.controllingArea?.code
        : "ETCA",
      companyCode: rmSearchForm?.["Company Code"] ?? "",
      profitCenter: rmSearchForm?.["Profit Center"] ?? "",
      hierarchyArea: rmSearchForm?.["Hierarchy Area"]?.code ?? "",
      costCenterCategory: rmSearchForm?.["Cost Center Category"] ?? "",
      createdBy: rmSearchForm?.createdBy ?? "",
      fromDate:
        moment(rmSearchForm?.createdOn[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate:
        moment(rmSearchForm?.createdOn[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      businessArea: "",
      personResponsible: rmSearchForm?.personResponsible ?? "",
      userResponsible: rmSearchForm?.["User Responsible"] ?? "",
      // businessArea: filterFieldData?.["Business Area"]?.code ?? "",
      functionalArea: rmSearchForm?.["Functional Area"] ?? "",
      fercIndicator: rmSearchForm?.["FERC Indicator"] ?? "",
      street: rmSearchForm?.street ?? "",
      location: rmSearchForm?.location ?? "",
      description: rmSearchForm?.description ?? "",
      country: rmSearchForm?.["Country/Reg"] ?? "",
      region: rmSearchForm?.["Region"] ?? "",
      blockingStatus:
        rmSearchForm?.blockingStatus === "Blocked"
          ? "X"
          : rmSearchForm?.blockingStatus === "Unblocked"
            ? "Y"
            : "",
      top: 100,
      skip: rmDataRows?.length ?? 0,
    };
    const hSuccess = (data) => {
      var rows = [];
      for (let index = 0; index < data?.body?.list?.length; index++) {
        var tempObj = data?.body?.list[index];
        var tempRow = {
          id: uuidv4(),
          description: tempObj?.Description,
          controllingArea: tempObj?.controllingArea,
          companyCode: tempObj?.CompanyCode,
          hierarchyArea: tempObj?.HeirarchyArea,
          costCenterCategory: tempObj?.CCtrCategory,
          costCenter: tempObj?.costCenter,
          CostCenterName: tempObj?.CostCenterName,
          businessArea:
            tempObj["BusinessArea"] !== ""
              ? `${tempObj["BusinessArea"]}`
              : "Not Available",

          functionalArea:
            tempObj["FunctionalArea"] !== ""
              ? `${tempObj["FunctionalArea"]}`
              : "Not Available",
          personResponsible:
            tempObj["PersonResponsible"] !== ""
              ? `${tempObj["PersonResponsible"]}`
              : "Not Available",
          userResponsible:
            tempObj["UserResponsible"] !== ""
              ? `${tempObj["UserResponsible"]}`
              : "Not Available",
          fercIndicator:
            tempObj["FERCindicator"] !== ""
              ? `${tempObj["FERCindicator"]}`
              : "Not Available",
          street:
            tempObj["Street"] !== "" ? `${tempObj["Street"]}` : "Not Available",
          location:
            tempObj["Location"] !== ""
              ? `${tempObj["Location"]}`
              : "Not Available",
          countryreg:
            tempObj["Country"] !== ""
              ? `${tempObj["Country"]}`
              : "Not Available",
          region:
            tempObj["Region"] !== "" ? `${tempObj["Region"]}` : "Not Available",
          createdOn:
            tempObj["CreatedOn"] !== ""
              ? `${moment(tempObj["CreatedOn"]).format("DD MMM YYYY")}`
              : "Not Available",
          createdBy:
            tempObj["CreatedBy"] !== ""
              ? `${tempObj["CreatedBy"]}`
              : "Not Available",
          ProfitCenter:
            tempObj["ProfitCenter"] !== ""
              ? `${tempObj["ProfitCenter"]}`
              : "Not Available",
          blockingStatus:
            tempObj["BlockingStatus"] === "X" ? "Blocked" : "Unblocked",
        };
        rows.push(tempRow);
      }
      setRmDataRows((prevRows) => [...prevRows, ...rows]);

      setTableLoading(false);
      setroCount(rows.length);
      setCount(data?.body?.count);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCentersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const [roCount, setroCount] = useState(0);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();


  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  const handleSearchDialogClickOpen = () => {
    setOpenSearchDialog(true);
  };

  const handleSearchDialogClose = () => {
    setOpenSearchDialog(false);
  };
  const handleClear = () => {
    dispatch(commonFilterClear({ module: "CostCenter" }));
    setselectedCCcategory([]);
    setselectedComanyCode([]);
    setselectedPersonResponsible([]);
    setselectedUserResponsible([]);
    setselectedProfitCenter([]);
    setselectedCostCenter([]);
    setselectedCostCenterName([]);
    setselectedLocation([]);
    setselectedDescription([]);
    setselectedCreatedBy([]);
    setselectedStreet([]);
    setSelectedValues({});
    setselectedPresetCCcategory([]);
    setselectedPresetComanyCode([]);
    setselectedPresetPersonResponsible([]);
    setselectedPresetUserResponsible([]);
    setselectedPresetProfitCenter([]);
    setselectedPresetCostCenter([]);
    setselectedPresetCostCenterName([]);
    setselectedPresetLocation([]);
    setselectedPresetDescription([]);
    setselectedPresetCreatedBy([]);
    setFilterFieldData({});
  };


  const handleSelectionModelChange = (newSelection) => {
    let filterValueColumns = columns?.map((t) => t.field);
    const selectedRowsDetails = rmDataRows.filter((row) =>
      newSelection.includes(row.id)
    );
    let requiredArrayDetails = [];
    selectedRowsDetails?.map((s) => {
      let requiredObject = {};
      filterValueColumns.forEach((y) => {
        if (s[y] !== null) {
          requiredObject[y] = s[y] || "";
        }
      });
      requiredObject["controllingArea"] = s["controllingArea"];
      requiredArrayDetails.push(requiredObject);
      setSelectedMassChangeRowData(requiredArrayDetails);
    });
  };

  function refreshPage() {
    dispatch(commonFilterClear({ module: "CostCenter" }));
    getFilter();
  }

  const [company, setCompany] = useState([]);
  const [Companyid, setCompanyid] = useState([]);

  // let { poId } = useParams();
  const [open, setOpen] = useState(false);
  const [matAnchorEl, setMatAnchorEl] = useState(null);
  const [materialDetails, setMaterialDetails] = useState(null);
  const [itemDataRows, setItemDataRows] = useState([]);

  const handlePODetailsClick = (event) => {
    setOpendialog3(true);
  };

  const matOpen = Boolean(matAnchorEl);
  const popperId = matOpen ? "simple-popper" : undefined;

  const handleClose = () => {
    setOpen(false);
  };

  const [poNum, setPONum] = useState(null);
  const [id, setID] = useState("");
  const createMultiValueCell = (fieldName, displayName) => ({
        field: fieldName,
        headerName: displayName,
        editable: false,
        flex: 1,
        renderCell: (params) => {
          const values = params.value ? params.value.split(",").map(m => m.trim()) : [];
          const displayCount = values.length - 1;
    
          if (values.length === 0) return "-";
    
          const formatText = (text) => {
            const [code, ...rest] = text.split('-');
            return (
              <>
                <strong>{code}</strong>{rest.length ? ` - ${rest.join('-')}` : ''}
              </>
            );
          };
    
          return (
            <Box sx={{ 
              display: "flex", 
              alignItems: "center",
              width: "100%",
              minWidth: 0
            }}>
              <Tooltip 
                title={values[0]}
                placement="top"
                arrow
              >
                <Typography 
                  variant="body2" 
                  sx={{ 
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    flex: 1,
                    minWidth: 0,
                  }}
                >
                  {formatText(values[0])}
                </Typography>
              </Tooltip>
              {displayCount > 0 && (
                <Box sx={{ 
                  display: "flex",
                  alignItems: "center",
                  ml: 1,
                  flexShrink: 0 
                }}>
                  <Tooltip
                    arrow
                    placement="right"
                    title={
                      <Box sx={{ p: 1, maxHeight: 200, overflowY: "auto" }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                          Additional {displayName}s ({displayCount})
                        </Typography>
                        {values.slice(1).map((value, idx) => (
                          <Typography key={idx} variant="body2" sx={{ mb: 0.5 }}>
                            {formatText(value)}
                          </Typography>
                        ))}
                      </Box>
                    }
                  >
                    <Box sx={{ 
                      display: "flex", 
                      alignItems: "center",
                      cursor: "pointer"
                    }}>
                      <InfoIcon 
                        sx={{ 
                          fontSize: "1rem",
                          color: "primary.main",
                          "&:hover": { color: "primary.dark" }
                        }} 
                      />
                      <Typography 
                        variant="caption" 
                        sx={{ 
                          ml: 0.5,
                          color: "primary.main",
                          fontSize: "11px"
                        }}
                      >
                        +{displayCount}
                      </Typography>
                    </Box>
                  </Tooltip>
                </Box>
              )}
            </Box>
          );
        }
      });
      const  createSingleValueCell = (fieldName, displayName) => ({
        field: fieldName,
        headerName: displayName,
        editable: false,
        flex: 1,
        renderCell: (params) => {
            const [firstPart, ...rest] = params.value?.split(" - ") || [];
            return (
              <span style={{ flex: 1, wordBreak: 'break-word', whiteSpace: 'normal' }}>
                <strong>{firstPart}</strong> {rest.length ? `- ${rest.join(" - ")}` : ""}
              </span>
            );
          },
      });
    const fetchMasterDataColumns = () => {
      console.log("Error")
      let payload = {
        decisionTableId: null,
        decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_COLUMN,
        version: "v1",
        conditions: [
          {
            "MDG_CONDITIONS.MDG_MAT_REGION": "US",
            "MDG_CONDITIONS.MDG_MODULE":"Cost Center",
            "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE": "Master Data",
          },
        ],
      };
      getMasterDataColumn(payload);
    };
    const fetchSearchParameterFromDt = () => {
        let payload = {
              decisionTableId: null,
              decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_PARAMETER,
              version: "v1",
              conditions: [
                {
                  "MDG_CONDITIONS.MDG_MAT_REGION":"US",
                  "MDG_CONDITIONS.MDG_MODULE":"CostCenter",
                  "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data",
                },
              ],
            };
            getSearchParams(payload);
      }
  const columns = [
    {
      field: "companyCode",
      headerName: "Company Code",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "costCenter",
      headerName: "Cost Center",
      editable: false, //dd
      flex: 1,
    },
    {
      field: "description",
      headerName: "Long Description",
      editable: false, //text
      flex: 1,
    },
    {
      field: "costCenterCategory",
      headerName: "Cost Center Category", //dd
      editable: false,
      flex: 1,
    },
    {
      field: "personResponsible",
      headerName: "Person Responsible", //dd
      editable: false,
      flex: 1,
    },
    {
      field: "userResponsible",
      headerName: "User Responsible", //dd
      editable: false,
      flex: 1,
    },

    {
      field: "blockingStatus",
      headerName: "Blocking Status", //dd
      editable: false,
      flex: 1,
    },
  ];
  const dynamicFilterColumns = selectedOptions
    ?.map((option) => {
      const field = titleToFieldMapping[option]; // Get the corresponding field from the mapping
      if (!field) {
        return null; // Handle the case when the field doesn't exist in the mapping
      }
      return {
        field: field, // Use the field name from the mapping
        headerName: option,
        editable: false,
        flex: 1,
      };
    })
    .filter((column) => column !== null); // Remove any null columns

  const allColumns = [
    ...columns,
    ...dynamicFilterColumns,
    // ...actionColumn,
    // Other fixed and dynamic columns as needed
  ];
  const capitalize = (str) => {
    //  str?.map((str)=>{
    const arr = str.split(" ");
    for (var i = 0; i < arr.length; i++) {
      arr[i] = arr[i].charAt(0) + arr[i].slice(1).toLowerCase();
    }

    const str2 = arr.join(" ");
    return str2;
    //  })
  };
 const createMasterDataColums = (data) => {
        const columns = [];
        let sortedData = data?.sort(
        (a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO
      )|| [];
        if (sortedData) {
          sortedData?.forEach((item) => {
            if(item?.MDG_MAT_VISIBILITY===VISIBILITY_TYPE.DISPLAY){
              
            if (item?.MDG_MAT_UI_FIELD_NAME) {
              const fieldName = item.MDG_MAT_JSON_FIELD_NAME;
              const headerName = item.MDG_MAT_UI_FIELD_NAME;
              if(fieldName==="DataValidation"){
                columns.push(displayCell());
              }
              else if (item.MDG_MAT_FIELD_TYPE === "Multiple") {
                columns.push(createMultiValueCell(fieldName, headerName));
              } 
              else if (item.MDG_MAT_FIELD_TYPE === "Single") {
                columns.push(createSingleValueCell(fieldName, headerName));
              }
            }
          }
        });
        }
        return columns;
      }
    useEffect(() => {
             if (masterDataDtResponse) {
               const columnsGlobal = createMasterDataColums(masterDataDtResponse?.result?.[0]?.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);
               setDynamicColumns(columnsGlobal);
             }
             if(dtSearchParamsResponse){
                const response = dtSearchParamsResponse?.result?.[0]?.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE;
                const additionalData = response?.filter((item) => {
                  return item.MDG_MAT_FILTER_TYPE === "Additional";
                    }).map((item) => {
                  return { title: item.MDG_MAT_UI_FIELD_NAME };
                  });
                setSearchParameters(response);
                setItem(additionalData);
            }
      },[masterDataDtResponse,dtSearchParamsResponse]); 

  // useEffect(() => {
  //   getFilter();
  // }, []);

  useEffect(() => {
              fetchMasterDataColumns();
              fetchSearchParameterFromDt();
        },[]);

  // useEffect(() => {
  //   if ((rmSearchForm?.company).length) {
  //     getVendorDetails();
  //     getPlantCodeSet()
  //   }
  // }, [rmSearchForm?.company]);

  // let serviceRequestForm_Component = new createServiceRequestForm(Status_ServiceReqForm, setStatus_ServiceReqForm)
  // <-- Function for taking screenshot (Export button) -->
  let ref_elementForExport = useRef(null);
  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      allColumns.forEach((item) => {
        if (item.headerName.toLowerCase() !== "actions" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `Cost Center Data-${moment(presentDate).format(
          "DD-MMM-YYYY"
        )}`,
        columns: excelColumns,
        rows: rmDataRows,
      });
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };

  const handleDescInputChange = (e) => {
    // Clear any existing timer
    const inputValue = e.target.value;
    setDescInputValue(inputValue);
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      // Set a new timer to execute handleDQM after 1 second
      const newTimerId = setTimeout(() => {
        getDescription(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };

  const handleUserResInputChange = (e) => {
    const inputValue = e.target.value;
    setUserInputValue(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      // Set a new timer to execute handleDQM after 1 second
      const newTimerId = setTimeout(() => {
        getUserResponsible(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };

  const handlePersonResInputChange = (e) => {
    const inputValue = e.target.value;
    setPersonInputValue(inputValue);
    // Clear any existing timer
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      // Set a new timer to execute handleDQM after 1 second
      const newTimerId = setTimeout(() => {
        getPersonReponsible(inputValue);
      }, 500);

      // Store the new timer ID
      setTimerId(newTimerId);
    }
  };

  const handleCCInputChange = (e) => {
    const inputValue = e.target.value;
    setCcInputValue(inputValue);
    if (inputValue.length >= 4) {
       setTimeout(() => {
        getCostCenterSearch(inputValue);
      }, 500);
    }
  };

  const handlePresetActiveChange = useCallback((isActive) => {
    setIsPresetActive(isActive);
  }, [setIsPresetActive]);

  return (
    <>
      {/* {isLoading === true ? (
        <LoadingComponent />
      ) : ( */}
      <div ref={ref_elementForExport}>
        <ReusableDialog
          dialogState={openMessageDialog}
          openReusableDialog={handleMessageDialogClickOpen}
          closeReusableDialog={handleMessageDialogClose}
          dialogTitle={messageDialogTitle}
          dialogMessage={messageDialogMessage}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          // handleExtraButton={handleMessageDialogNavigate}
          dialogSeverity={messageDialogSeverity}
        />

        <ReusableDialog
          dialogState={openSearchDialog}
          openReusableDialog={handleSearchDialogClickOpen}
          closeReusableDialog={handleSearchDialogClose}
          dialogTitle={searchDialogTitle}
          dialogMessage={searchDialogMessage}
          handleDialogConfirm={handleSearchDialogClose}
          dialogSeverity={"danger"}
          showCancelButton={false}
          dialogOkText={"OK"}
        />

        <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
          <Stack spacing={1}>
            {/* Information */}
            <Grid container sx={outermostContainer_Information}>
              <Grid item md={5} sx={outerContainer_Information}>
                <Typography variant="h3">
                  <strong>{t("Cost Center")}</strong>
                </Typography>
                <Typography variant="body2" color="#777">
                  {t("This view displays the list of Cost Centers")}
                </Typography>
              </Grid>
            </Grid>

            <Grid container sx={container_filter}>
              <Grid item md={12}>
                <StyledAccordion defaultExpanded={true}>
                  <StyledAccordionSummary
                    expandIcon={
                      <ExpandMoreIcon
                        sx={{ fontSize: "1.25rem", color: colors.primary.main }}
                      />
                    }
                    aria-controls="panel1a-content"
                    id="panel1a-header"
                  >
                    <FilterListIcon
                      sx={{
                        fontSize: "1.25rem",
                        marginRight: 1,
                        color: colors.primary.main,
                      }}
                    />
                    <Typography
                      sx={{
                        fontSize: "0.875rem",
                        fontWeight: 600,
                        color: colors.primary.dark,
                      }}
                    >
                      {t("Search Cost Center")}
                    </Typography>
                  </StyledAccordionSummary>
                  <AccordionDetails sx={{ padding: "0.5rem 1rem 0.5rem" }}>
                    <Grid
                      container
                      rowSpacing={1}
                      spacing={2}
                      justifyContent="space-between"
                      alignItems="center"
                    // sx={{ marginBottom: "0.5rem" }}
                    >
                      <Grid
                        container
                        spacing={1}
                        sx={{ padding: "0rem 1rem 0.5rem" }}
                      >
                        {searchParameters?.filter(item => item.MDG_MAT_VISIBILITY !== "Hidden")
                          .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
                          .map((item, index) => {
                            return (
                              <React.Fragment key={index}>
                                {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.CONTROLINGAREA &&
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                       {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                    </LabelTypography>
                                    <FormControl size="small" fullWidth>
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        size="small"
                                        value={rmSearchForm?.controllingArea}
                                        onChange={handleControllingArea}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        options={dropDownData?.ControllingArea ?? []}
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return `${option?.code}` ?? "";
                                          else return "";
                                        }}
                                        renderOption={(props, option) => (
                                          <li {...props}>
                                            <Typography style={{ fontSize: 12 }}>
                                              {/* {`${option?.code}-${option?.desc}`} */}
                                              {option?.desc ? (
                                                <>
                                                  <strong>{option.code}</strong> -{" "}
                                                  {option.desc}
                                                </>
                                              ) : (
                                                <strong>{option.code}</strong>
                                              )}
                                            </Typography>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <TextField
                                            sx={{ fontSize: "12px !important" }}
                                            {...params}
                                            variant="outlined"
                                            placeholder="Select Controlling Area"
                                          />
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>}
                                {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.COSTCENTER &&
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                       {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                    </LabelTypography>
                                    <FormControl
                                      fullWidth
                                      size="small"
                                      sx={{ paddingBottom: "0.7rem" }}
                                    >
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        multiple
                                        disableCloseOnSelect
                                        size="small"
                                        value={memoizedCCValue}
                                        // loading={isDropDownLoading}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        onChange={(e, value, reason) => {
                                          if (reason === "clear" || value?.length === 0) {
                                            setselectedCostCenter([]);
                                            setselectedPresetCostCenter([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code === "Select All"
                                          ) {
                                            handleSelectAllCostCenter();
                                          } else {
                                            setselectedCostCenter(value);
                                          }
                                        }}
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": { padding: "0 6px" },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        limitTags={1}
                                        options={
                                          dropDownData?.ETCCSearchData?.length
                                            ? [
                                              {
                                                code: "Select All",
                                                desc: "Select All",
                                              },
                                              ...dropDownData?.ETCCSearchData,
                                            ]
                                            : dropDownData?.ETCCSearchData ?? []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return `${option?.code}` ?? "";
                                          else return "";
                                        }}
                                        renderOption={(props, option, { selected }) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isCostCenterSelected(option) ||
                                                      (option?.code === "Select All" &&
                                                        selectedCostCenter?.length ===
                                                        dropDownData?.ETCCSearchData
                                                          ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code} - ${option?.desc}`}
                                                label={
                                                  <>
                                                    <strong>{option.code}</strong> -{" "}
                                                    {option.desc}
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              ccInputValue.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            disableHoverListener={
                                              ccInputValue.length >= 4
                                            }
                                            placement="top" // Ensures the tooltip appears above the text field
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              // helperText={ccInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                              placeholder={
                                                memoizedCCValue.length === 0
                                                  ? "Select Cost Center"
                                                  : ""
                                              }
                                              onChange={(e) => {
                                                handleCCInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>}
                                {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.COMPANYCODE &&
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                       {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                    </LabelTypography>
                                    <FormControl
                                      fullWidth
                                      size="small"
                                      sx={{ paddingBottom: "0.7rem" }}
                                    >
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        size="small"
                                        multiple
                                        limitTags={1}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        options={[
                                          { code: "Select All", desc: "Select All" },
                                          ...(dropDownData?.CompanyCode ?? []),
                                        ]}
                                        disableCloseOnSelect
                                        onChange={(e, value, reason) => {
                                          if (reason === "clear" || value?.length === 0) {
                                            setselectedComanyCode([]);
                                            setselectedPresetComanyCode([]);
                                            return;
                                          }

                                          console.log(value, "valueinauto");
                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code === "Select All"
                                          ) {
                                            handleSelectAllCompanyCodes();
                                          } else {
                                            setselectedComanyCode(value);
                                          }
                                        }}
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return `${option?.code}` ?? "";
                                          else return "";
                                        }}
                                        value={
                                          selectedComanyCode.length > 0
                                            ? selectedComanyCode
                                            : selectedPresetComanyCode.length > 0
                                              ? selectedPresetComanyCode
                                              : []
                                        }
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": { padding: "0 6px" },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        renderOption={(props, option, { selected }) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isCompanyCodeSelected(option) ||
                                                      (option?.code === "Select All" &&
                                                        selectedComanyCode?.length ===
                                                        dropDownData?.CompanyCode
                                                          ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code} - ${option?.desc}`}
                                                label={
                                                  <>
                                                    <strong>{option.code}</strong> -{" "}
                                                    {option.desc}
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <TextField
                                            sx={{
                                              fontSize: "12px !important",
                                              "& .MuiOutlinedInput-root": {
                                                height: 35,
                                              },
                                              "& .MuiInputBase-input": {
                                                padding: "10px 14px",
                                              },
                                            }}
                                            {...params}
                                            variant="outlined"
                                            placeholder="Select Company Code"
                                          />
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>}
                                {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.LONGDESC &&
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                       {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                    </LabelTypography>
                                    <FormControl fullWidth size="small">
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        size="small"
                                        multiple
                                        disableCloseOnSelect
                                        value={memoizedLDValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": { padding: "0 6px" },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        onChange={(e, value, reason) => {
                                          if (reason === "clear" || value?.length === 0) {
                                            setselectedDescription([]);
                                            setselectedPresetDescription([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code === "Select All"
                                          ) {
                                            handleSelectAllDescription();
                                          } else {
                                            setselectedDescription(value);
                                          }
                                        }}
                                        limitTags={1}
                                        options={
                                          dropDownData?.ETDescriptionSearchData?.length
                                            ? [
                                              { code: "Select All" },
                                              ...dropDownData?.ETDescriptionSearchData,
                                            ]
                                            : dropDownData?.ETDescriptionSearchData ?? []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code) return option?.code ?? "";
                                          else return "";
                                        }}
                                        renderOption={(props, option, { selected }) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isDescriptionSelected(option) ||
                                                      (option?.code === "Select All" &&
                                                        selectedDescription?.length ===
                                                        dropDownData
                                                          ?.ETDescriptionSearchData
                                                          ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code}`}
                                                label={
                                                  <>
                                                    <strong>{option.code}</strong>
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              descInputValue.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            disableHoverListener={
                                              descInputValue.length >= 4
                                            }
                                            placement="top" // Ensures the tooltip appears above the text field
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              placeholder={
                                                memoizedLDValue.length === 0
                                                  ? "Select Long Description"
                                                  : ""
                                              }
                                              onChange={(e) => {
                                                handleDescInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>}
                                {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.COSTCENTERCAT &&
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                       {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                    </LabelTypography>
                                    <FormControl fullWidth size="small">
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        size="small"
                                        multiple
                                        disableCloseOnSelect
                                        value={
                                          selectedCCcategory.length > 0
                                            ? selectedCCcategory
                                            : selectedPresetCCcategory.length > 0
                                              ? selectedPresetCCcategory
                                              : []
                                        }
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": { padding: "0 6px" },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        onChange={(e, value, reason) => {
                                          if (reason === "clear" || value?.length === 0) {
                                            setselectedCCcategory([]);
                                            setselectedPresetCCcategory([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code === "Select All"
                                          ) {
                                            handleSelectAllCCcategory();
                                          } else {
                                            setselectedCCcategory(value);
                                          }
                                        }}
                                        limitTags={1}
                                        options={
                                          dropDownData?.CostCenterCategorySearch?.length
                                            ? [
                                              {
                                                code: "Select All",
                                                desc: "Select All",
                                              },
                                              ...dropDownData?.CostCenterCategorySearch,
                                            ]
                                            : dropDownData?.CostCenterCategorySearch ?? []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return `${option?.code}` ?? "";
                                          else return "";
                                        }}
                                        renderOption={(props, option, { selected }) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isCCcategorySelected(option) ||
                                                      (option?.code === "Select All" &&
                                                        selectedCCcategory?.length ===
                                                        dropDownData
                                                          ?.CostCenterCategorySearch
                                                          ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code} - ${option?.desc}`}
                                                label={
                                                  <>
                                                    <strong>{option.code}</strong> -{" "}
                                                    {option.desc}
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <TextField
                                            sx={{
                                              fontSize: "12px !important",
                                              "& .MuiOutlinedInput-root": {
                                                height: 35,
                                              },
                                              "& .MuiInputBase-input": {
                                                padding: "10px 14px",
                                              },
                                            }}
                                            {...params}
                                            variant="outlined"
                                            placeholder="Select Cost Center Category"
                                          />
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>}
                                {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.PERSONRES &&
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                      {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                    </LabelTypography>
                                    <FormControl fullWidth size="small">
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        size="small"
                                        multiple
                                        disableCloseOnSelect
                                        value={memoizedPRValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        onChange={(e, value, reason) => {
                                          if (reason === "clear" || value?.length === 0) {
                                            setselectedPersonResponsible([]);
                                            setselectedPresetPersonResponsible([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code === "Select All"
                                          ) {
                                            handleSelectAllPersonResponsible();
                                          } else {
                                            setselectedPersonResponsible(value);
                                          }
                                        }}
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": { padding: "0 6px" },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        limitTags={1}
                                        options={
                                          dropDownData?.ETPersonResponsibleSearch?.length
                                            ? [
                                              { code: "Select All" },
                                              ...dropDownData?.ETPersonResponsibleSearch,
                                            ]
                                            : dropDownData?.ETPersonResponsibleSearch ??
                                            []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code) return option?.code ?? "";
                                          else return "";
                                        }}
                                        renderOption={(props, option, { selected }) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isPersonResponsibleSelected(
                                                        option
                                                      ) ||
                                                      (option?.code === "Select All" &&
                                                        selectedPersonResponsible?.length ===
                                                        dropDownData
                                                          ?.ETPersonResponsibleSearch
                                                          ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code}`}
                                                label={
                                                  <>
                                                    <strong>{option.code}</strong>
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              personInputValue.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            disableHoverListener={
                                              personInputValue.length >= 4
                                            }
                                            placement="top" // Ensures the tooltip appears above the text field
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              // helperText={personInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                              placeholder={
                                                memoizedPRValue.length === 0
                                                  ? "Select Person Responsible"
                                                  : ""
                                              }
                                              onChange={(e) => {
                                                handlePersonResInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>}
                                {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.USERRES &&
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                       {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                    </LabelTypography>
                                    <FormControl
                                      size="small"
                                      fullWidth
                                      sx={{ paddingBottom: "0.7rem" }}
                                    >
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        size="small"
                                        multiple
                                        disableCloseOnSelect
                                        value={memoizedURValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        onChange={(e, value, reason) => {
                                          if (reason === "clear" || value?.length === 0) {
                                            setselectedUserResponsible([]);
                                            setselectedPresetUserResponsible([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code === "Select All"
                                          ) {
                                            handleSelectAllUserResponsible();
                                          } else {
                                            setselectedUserResponsible(value);
                                          }
                                        }}
                                        limitTags={1}
                                        options={
                                          dropDownData?.ETUserResponsible?.length
                                            ? [
                                              { code: "Select All" },
                                              ...dropDownData?.ETUserResponsible,
                                            ]
                                            : dropDownData?.ETUserResponsible ?? []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code) return option?.code ?? "";
                                          else return "";
                                        }}
                                        renderOption={(props, option, { selected }) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isUserResponsibleSelected(option) ||
                                                      (option?.code === "Select All" &&
                                                        selectedUserResponsible?.length ===
                                                        dropDownData?.ETUserResponsible
                                                          ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code}`}
                                                label={
                                                  <>
                                                    <strong>{option.code}</strong>
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": { padding: "0 6px" },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              userInputValue.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            disableHoverListener={
                                              userInputValue.length >= 4
                                            }
                                            placement="top" // Ensures the tooltip appears above the text field
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              // helperText={userInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                              placeholder={
                                                memoizedURValue.length === 0
                                                  ? "Select User Responsible"
                                                  : ""
                                              }
                                              onChange={(e) => {
                                                handleUserResInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>}
                                {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.BLOCKINGSAT &&
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                       {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                    </LabelTypography>
                                    <FormControl fullWidth size="small">
                                      <Select
                                        placeholder={"Select Blocking Status"}
                                        sx={{ height: "35px" }}
                                        size="small"
                                        value={rmSearchForm?.blockingStatus}
                                        name="blockingStatus"
                                        onChange={(e) => handleBlockingStatus(e)}
                                        displayEmpty={true}
                                        // input={<OutlinedInput label="Tag" />}
                                        // renderValue={(selected) => selected}
                                        MenuProps={MenuProps}
                                      >
                                        {/* <MenuItem sx={font_Small} disabled value={""}>
                                <div
                                  style={{
                                    color: "#C1C1C1",
                                    fontSize: "12px",
                                  }}
                                >
                                  Select Blocking Status
                                </div>
                              </MenuItem> */}
                                        {names?.map((name) => (
                                          <MenuItem
                                            sx={font_Small}
                                            key={name}
                                            value={name}
                                            style={{
                                              fontSize: "12px !important",
                                              height: "35px",
                                            }}
                                          >
                                            {/* <Checkbox
                                  checked={
                                    rbSearchForm?.reqStatus.indexOf(name) > -1
                                  }
                                /> */}
                                            <ListItemText
                                              sx={font_Small}
                                              primary={name}
                                              style={{ fontSize: "12px !important" }}
                                            />
                                          </MenuItem>
                                        ))}
                                      </Select>
                                    </FormControl>
                                  </Grid>}
                              </React.Fragment>)
                          })}
                        {/* dynamic filter// */}
                        <Grid item md={2}>
                          <LabelTypography sx={font_Small}>
                            {t("Add New Filters")}
                          </LabelTypography>
                          <FormControl fullWidth>
                            <Select
                              sx={{
                                font_Small,
                                height: "35px",
                                fontSize: "12px",
                              }}
                              // fullWidth
                              size="small"
                              multiple
                              limitTags={2}
                              value={selectedOptions}
                              onChange={handleSelection}
                              renderValue={(selected) => selected.join(", ")}
                              MenuProps={{
                                MenuProps,
                              }}
                              endAdornment={
                                selectedOptions.length > 0 && (
                                  <InputAdornment
                                    position="end"
                                    sx={{ marginRight: "15px" }}
                                  >
                                    <IconButton
                                      size="small"
                                      sx={{ height: "10px", width: "10px" }}
                                      onClick={() => {
                                        // handleClearPayload(selectedOptions)
                                        setSelectedOptions([]);
                                      }}
                                      aria-label="Clear selections"
                                    >
                                      <ClearIcon />
                                    </IconButton>
                                  </InputAdornment>
                                )
                              }
                            >
                              {items?.map((option) => (
                                <MenuItem
                                  key={option.title}
                                  value={option.title}
                                >
                                  <Checkbox
                                    checked={
                                      selectedOptions.indexOf(option.title) > -1
                                    }
                                  />
                                  {option.title}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                          <Grid
                            style={{
                              display: "flex",
                              justifyContent: "space-around",
                            }}
                          ></Grid>
                        </Grid>
                      </Grid>
                      <Grid
                        container
                        rowSpacing={1}
                        spacing={2}
                        justifyContent="space-between"
                        alignItems="center"
                        sx={{ padding: "0.5rem 1rem 0.5rem" }}
                        // sx={{ marginBottom: "0.5rem" }}
                      >
                        <Grid
                          container
                          spacing={1}
                          sx={{ padding: "0rem 1rem 0.5rem" }}
                        >
                          {selectedOptions?.map((option, i) => {
                            console.log(
                              "fercvalue",
                              option,
                              rmSearchForm[option],
                              rmSearchForm
                            );
                            if (option === "Short Description") {
                              return (
                                <>
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                      {t("Short Description")}
                                    </LabelTypography>
                                    <FormControl
                                      fullWidth
                                      size="small"
                                      sx={{ paddingBottom: "0.7rem" }}
                                    >
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        size="small"
                                        multiple
                                        disableCloseOnSelect
                                        value={memoizedSDValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        onChange={(e, value, reason) => {
                                          if (
                                            reason === "clear" ||
                                            value?.length === 0
                                          ) {
                                            setselectedCostCenterName([]);
                                            setselectedPresetCostCenterName([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code ===
                                              "Select All"
                                          ) {
                                            handleSelectAllCostCenterName();
                                          } else {
                                            setselectedCostCenterName(value);
                                          }
                                        }}
                                        limitTags={1}
                                        options={
                                          dropDownData?.ETCCNameSearchData
                                            ?.length
                                            ? [
                                                { code: "Select All" },
                                                ...dropDownData?.ETCCNameSearchData,
                                              ]
                                            : dropDownData?.ETCCNameSearchData ??
                                              []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return option?.code ?? "";
                                          else return "";
                                        }}
                                        renderOption={(
                                          props,
                                          option,
                                          { selected }
                                        ) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isCostCenterNameSelected(
                                                        option
                                                      ) ||
                                                      (option?.code ===
                                                        "Select All" &&
                                                        selectedCostCenterName?.length ===
                                                          dropDownData
                                                            ?.ETCCNameSearchData
                                                            ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code}`}
                                                label={
                                                  <>
                                                    <strong>
                                                      {option.code}
                                                    </strong>
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              ccNameInputValue.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            disableHoverListener={
                                              ccNameInputValue.length >= 4
                                            }
                                            placement="top" // Ensures the tooltip appears above the text field
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              placeholder={
                                                memoizedSDValue.length === 0
                                                  ? "Select Short Description"
                                                  : ""
                                              }
                                              onChange={(e) => {
                                                handleCCNameInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>
                                </>
                              );
                            } else if (option === "Created On") {
                              return (
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {option}
                                  </LabelTypography>
                                  <FormControl size="small" fullWidth>
                                    <LocalizationProvider
                                      dateAdapter={AdapterDateFns}
                                    >
                                      <DateRange
                                        handleDate={handleDate}
                                        date={selectedDateRange}
                                      />
                                    </LocalizationProvider>
                                  </FormControl>
                                </Grid>
                              );
                            } else if (option === "Street") {
                              return (
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {t("Street")}
                                  </LabelTypography>
                                  <FormControl fullWidth size="small">
                                    <Autocomplete
                                      sx={{ height: "31px" }}
                                      fullWidth
                                      size="small"
                                      multiple
                                      disableCloseOnSelect
                                      value={memoizedStreetValue}
                                      noOptionsText={
                                        isDropDownLoading ? (
                                          <Box
                                            sx={{
                                              display: "flex",
                                              justifyContent: "center",
                                              mt: 1,
                                              zIndex: 9999,
                                              top: "10px",
                                            }}
                                          >
                                            <CircularProgress size={20} />
                                          </Box>
                                        ) : (
                                          "No Data Available"
                                        )
                                      }
                                      limitTags={1}
                                      renderTags={(value, getTagProps) =>
                                        value.length > 0 ? (
                                          <>
                                            <Chip
                                              label={value[0].code}
                                              {...getTagProps({ index: 0 })}
                                              sx={{
                                                height: 20,
                                                fontSize: "0.75rem",
                                                ".MuiChip-label": {
                                                  padding: "0 6px",
                                                },
                                              }}
                                            />
                                            {value.length > 1 && (
                                              <Chip
                                                label={`+${value.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                            )}
                                          </>
                                        ) : null
                                      }
                                      onChange={(e, value, reason) => {
                                        if (
                                          reason === "clear" ||
                                          value?.length === 0
                                        ) {
                                          setselectedStreet([]);
                                          setselectedPresetStreet([]);
                                          return;
                                        }

                                        if (
                                          value.length > 0 &&
                                          value[value.length - 1]?.code ===
                                            "Select All"
                                        ) {
                                          handleSelectAllStreet();
                                        } else {
                                          setselectedStreet(value);
                                        }
                                      }}
                                      options={
                                        dropDownData?.ETStreetSearchData?.length
                                          ? [
                                              { code: "Select All" },
                                              ...dropDownData?.ETStreetSearchData,
                                            ]
                                          : dropDownData?.ETStreetSearchData ??
                                            []
                                      }
                                      getOptionLabel={(option) => {
                                        if (option?.code)
                                          return option?.code ?? "";
                                        else return "";
                                      }}
                                      renderOption={(
                                        props,
                                        option,
                                        { selected }
                                      ) => (
                                        <li {...props}>
                                          <FormGroup>
                                            <FormControlLabel
                                              control={
                                                <Checkbox
                                                  checked={
                                                    isStreetSelected(option) ||
                                                    (option?.code ===
                                                      "Select All" &&
                                                      selectedStreet?.length ===
                                                        dropDownData
                                                          ?.ETStreetSearchData
                                                          ?.length)
                                                  }
                                                />
                                              }
                                              // label={`${option?.code}`}
                                              label={
                                                <>
                                                  <strong>{option.code}</strong>
                                                </>
                                              }
                                            />
                                          </FormGroup>
                                        </li>
                                      )}
                                      renderInput={(params) => (
                                        <Tooltip
                                          title={
                                            streetInputValue.length < 4
                                              ? "Enter at least 4 characters"
                                              : ""
                                          }
                                          arrow
                                          disableHoverListener={
                                            streetInputValue.length >= 4
                                          }
                                          placement="top" // Ensures the tooltip appears above the text field
                                        >
                                          <TextField
                                            sx={{
                                              fontSize: "12px !important",
                                              "& .MuiOutlinedInput-root": {
                                                height: 35,
                                              },
                                              "& .MuiInputBase-input": {
                                                padding: "10px 14px",
                                              },
                                            }}
                                            {...params}
                                            variant="outlined"
                                            placeholder={
                                              memoizedStreetValue.length === 0
                                                ? "Select Street"
                                                : ""
                                            }
                                            onChange={(e) => {
                                              handleStreetInputChange(e);
                                            }}
                                          />
                                        </Tooltip>
                                      )}
                                    />
                                  </FormControl>
                                </Grid>
                              );
                            } else if (option === "Location") {
                              return (
                                <>
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                      {t("Location")}
                                    </LabelTypography>
                                    <FormControl fullWidth size="small">
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        size="small"
                                        multiple
                                        disableCloseOnSelect
                                        value={memoizedLocationValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        limitTags={1}
                                        onChange={(e, value, reason) => {
                                          if (
                                            reason === "clear" ||
                                            value?.length === 0
                                          ) {
                                            setselectedLocation([]);
                                            setselectedPresetLocation([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code ===
                                              "Select All"
                                          ) {
                                            handleSelectAllLocation();
                                          } else {
                                            setselectedLocation(value);
                                          }
                                        }}
                                        options={
                                          dropDownData?.ETLocationSearchData
                                            ?.length
                                            ? [
                                                { code: "Select All" },
                                                ...dropDownData?.ETLocationSearchData,
                                              ]
                                            : dropDownData?.ETLocationSearchData ??
                                              []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return option?.code ?? "";
                                          else return "";
                                        }}
                                        renderOption={(
                                          props,
                                          option,
                                          { selected }
                                        ) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isLocationSelected(
                                                        option
                                                      ) ||
                                                      (option?.code ===
                                                        "Select All" &&
                                                        selectedLocation?.length ===
                                                          dropDownData
                                                            ?.ETLocationSearchData
                                                            ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code}`}
                                                label={
                                                  <>
                                                    <strong>
                                                      {option.code}
                                                    </strong>
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              locationInputValue.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            disableHoverListener={
                                              locationInputValue.length >= 4
                                            }
                                            placement="top" // Ensures the tooltip appears above the text field
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              // helperText={locationInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                              placeholder={
                                                memoizedLocationValue.length ===
                                                0
                                                  ? "Select Location"
                                                  : ""
                                              }
                                              onChange={(e) => {
                                                handleLocationInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>
                                </>
                              );
                            } else if (option === "Created By") {
                              return (
                                <>
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                      {t("Created By")}
                                    </LabelTypography>
                                    <FormControl
                                      fullWidth
                                      size="small"
                                      sx={{ paddingBottom: "0.7rem" }}
                                    >
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        multiple
                                        disableCloseOnSelect
                                        size="small"
                                        value={memoizedCreatedByValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        onChange={(e, value, reason) => {
                                          if (
                                            reason === "clear" ||
                                            value?.length === 0
                                          ) {
                                            setselectedCreatedBy([]);
                                            setselectedPresetCreatedBy([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code ===
                                              "Select All"
                                          ) {
                                            handleSelectAllCreatedBy();
                                          } else {
                                            setselectedCreatedBy(value);
                                          }
                                        }}
                                        limitTags={1}
                                        options={
                                          dropDownData?.ETCreatedBySearchData
                                            ?.length
                                            ? [
                                                { code: "Select All" },
                                                ...dropDownData?.ETCreatedBySearchData,
                                              ]
                                            : dropDownData?.ETCreatedBySearchData ??
                                              []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return option?.code ?? "";
                                          else return "";
                                        }}
                                        renderOption={(
                                          props,
                                          option,
                                          { selected }
                                        ) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isCreatedBySelected(
                                                        option
                                                      ) ||
                                                      (option?.code ===
                                                        "Select All" &&
                                                        selectedCreatedBy?.length ===
                                                          dropDownData
                                                            ?.ETCreatedBySearchData
                                                            ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code}`}
                                                label={
                                                  <>
                                                    <strong>
                                                      {option.code}
                                                    </strong>
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              createdByInputValue.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            disableHoverListener={
                                              createdByInputValue.length >= 4
                                            }
                                            placement="top" // Ensures the tooltip appears above the text field
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              // helperText={createdByInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                              placeholder={
                                                memoizedCreatedByValue.length ===
                                                0
                                                  ? "Select Created By"
                                                  : ""
                                              }
                                              onChange={(e) => {
                                                handleCreatedByInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>
                                </>
                              );
                            } else if (option === "Profit Center") {
                              return (
                                <>
                                  <Grid item md={2}>
                                    <LabelTypography sx={font_Small}>
                                      {t("Profit Center")}
                                    </LabelTypography>
                                    <FormControl fullWidth size="small">
                                      <Autocomplete
                                        sx={{ height: "31px" }}
                                        fullWidth
                                        multiple
                                        disableCloseOnSelect
                                        size="small"
                                        value={memoizedPCValue}
                                        noOptionsText={
                                          isDropDownLoading ? (
                                            <Box
                                              sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                mt: 1,
                                                zIndex: 9999,
                                                top: "10px",
                                              }}
                                            >
                                              <CircularProgress size={20} />
                                            </Box>
                                          ) : (
                                            "No Data Available"
                                          )
                                        }
                                        renderTags={(value, getTagProps) =>
                                          value.length > 0 ? (
                                            <>
                                              <Chip
                                                label={value[0].code}
                                                {...getTagProps({ index: 0 })}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                              {value.length > 1 && (
                                                <Chip
                                                  label={`+${value.length - 1}`}
                                                  sx={{
                                                    height: 20,
                                                    fontSize: "0.75rem",
                                                    ".MuiChip-label": {
                                                      padding: "0 6px",
                                                    },
                                                  }}
                                                />
                                              )}
                                            </>
                                          ) : null
                                        }
                                        onChange={(e, value, reason) => {
                                          if (
                                            reason === "clear" ||
                                            value?.length === 0
                                          ) {
                                            setselectedProfitCenter([]);
                                            setselectedPresetProfitCenter([]);
                                            return;
                                          }

                                          if (
                                            value.length > 0 &&
                                            value[value.length - 1]?.code ===
                                              "Select All"
                                          ) {
                                            handleSelectAllProfitCenter();
                                          } else {
                                            setselectedProfitCenter(value);
                                          }
                                        }}
                                        limitTags={1}
                                        options={
                                          dropDownData?.ETPCSearchData?.length
                                            ? [
                                                {
                                                  code: "Select All",
                                                  desc: "Select All",
                                                },
                                                ...dropDownData?.ETPCSearchData,
                                              ]
                                            : dropDownData?.ETPCSearchData ?? []
                                        }
                                        getOptionLabel={(option) => {
                                          if (option?.code)
                                            return `${option?.code}` ?? "";
                                          else return "";
                                        }}
                                        renderOption={(
                                          props,
                                          option,
                                          { selected }
                                        ) => (
                                          <li {...props}>
                                            <FormGroup>
                                              <FormControlLabel
                                                control={
                                                  <Checkbox
                                                    checked={
                                                      isProfitCenterSelected(
                                                        option
                                                      ) ||
                                                      (option?.code ===
                                                        "Select All" &&
                                                        selectedProfitCenter?.length ===
                                                          dropDownData
                                                            ?.ETPCSearchData
                                                            ?.length)
                                                    }
                                                  />
                                                }
                                                // label={`${option?.code} - ${option?.desc}`}
                                                label={
                                                  <>
                                                    <strong>
                                                      {option.code}
                                                    </strong>{" "}
                                                    - {option.desc}
                                                  </>
                                                }
                                              />
                                            </FormGroup>
                                          </li>
                                        )}
                                        renderInput={(params) => (
                                          <Tooltip
                                            title={
                                              pcInputValue.length < 4
                                                ? "Enter at least 4 characters"
                                                : ""
                                            }
                                            arrow
                                            disableHoverListener={
                                              pcInputValue.length >= 4
                                            }
                                            placement="top" // Ensures the tooltip appears above the text field
                                          >
                                            <TextField
                                              sx={{
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }}
                                              {...params}
                                              variant="outlined"
                                              // helperText={pcInputValue.length < 4 ? 'Enter at least 4 characters' : ''}
                                              placeholder={
                                                memoizedPCValue.length === 0
                                                  ? "Select Profit Center"
                                                  : ""
                                              }
                                              onChange={(e) => {
                                                handlePCInputChange(e);
                                              }}
                                            />
                                          </Tooltip>
                                        )}
                                      />
                                    </FormControl>
                                  </Grid>
                                </>
                              );
                            } else {
                              return (
                                <Grid item md={2}>
                                  <LabelTypography sx={font_Small}>
                                    {option}
                                  </LabelTypography>
                                  <FormControl fullWidth size="small">
                                    <Autocomplete
                                      sx={{ height: "31px" }}
                                      fullWidth
                                      multiple
                                      disableCloseOnSelect
                                      size="small"
                                      noOptionsText={
                                        isDropDownLoading ? (
                                          <Box
                                            sx={{
                                              display: "flex",
                                              justifyContent: "center",
                                              mt: 1,
                                              zIndex: 9999,
                                              top: "10px",
                                            }}
                                          >
                                            <CircularProgress size={20} />
                                          </Box>
                                        ) : (
                                          "No Data Available"
                                        )
                                      }
                                      value={
                                        selectedValues[option]?.length > 0
                                          ? selectedValues[option]
                                          : selectedPresetValues[option]
                                              ?.length > 0
                                          ? selectedPresetValues[option]
                                          : []
                                      }
                                      renderTags={(value, getTagProps) =>
                                        value.length > 0 ? (
                                          <>
                                            <Chip
                                              label={value[0].code}
                                              {...getTagProps({ index: 0 })}
                                              sx={{
                                                height: 20,
                                                fontSize: "0.75rem",
                                                ".MuiChip-label": {
                                                  padding: "0 6px",
                                                },
                                              }}
                                            />
                                            {value.length > 1 && (
                                              <Chip
                                                label={`+${value.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  ".MuiChip-label": {
                                                    padding: "0 6px",
                                                  },
                                                }}
                                              />
                                            )}
                                          </>
                                        ) : null
                                      }
                                      onChange={(e, value, reason) => {
                                        if (
                                          reason === "clear" ||
                                          value?.length === 0
                                        ) {
                                          setSelectedValues((prev) => ({
                                            ...prev,
                                            [option]: [],
                                          }));
                                          setSelectedPresetValues((prev) => ({
                                            ...prev,
                                            [option]: [],
                                          }));
                                          return;
                                        }

                                        if (
                                          value.length > 0 &&
                                          value[value.length - 1]?.code ===
                                            "Select All"
                                        ) {
                                          handleSelectAllOptions(option);
                                        } else {
                                          setSelectedValues((prev) => ({
                                            ...prev,
                                            [option]: value,
                                          }));
                                        }
                                      }}
                                      limitTags={1}
                                      options={
                                        dynamicOptions?.[option]?.length
                                          ? [
                                              { code: "Select All" },
                                              ...dynamicOptions?.[option],
                                            ]
                                          : dynamicOptions?.[option] ?? []
                                      }
                                      getOptionLabel={(option) =>
                                        option?.code ? `${option.code}` : ""
                                      }
                                      renderOption={(
                                        props,
                                        dropdownOption,
                                        { selected }
                                      ) => (
                                        <li {...props}>
                                          <FormGroup>
                                            <FormControlLabel
                                              control={
                                                <Checkbox
                                                  checked={
                                                    isOptionSelected(
                                                      option,
                                                      dropdownOption
                                                    ) ||
                                                    (dropdownOption?.code ===
                                                      "Select All" &&
                                                      selectedValues[option]
                                                        ?.length ===
                                                        dynamicOptions?.[option]
                                                          ?.length)
                                                  }
                                                />
                                              }
                                              // label={`${dropdownOption?.code}`}
                                              label={
                                                <>
                                                  <strong>
                                                    {dropdownOption?.code}
                                                  </strong>
                                                </>
                                              }
                                            />
                                          </FormGroup>
                                        </li>
                                      )}
                                      renderInput={(params) => (
                                        <TextField
                                          sx={{
                                            fontSize: "12px !important",
                                            "& .MuiOutlinedInput-root": {
                                              height: 35,
                                            },
                                            "& .MuiInputBase-input": {
                                              padding: "10px 14px",
                                            },
                                          }}
                                          {...params}
                                          variant="outlined"
                                          placeholder={`Select ${option}`}
                                        />
                                      )}
                                    />
                                  </FormControl>
                                </Grid>
                              );
                            }
                          })}
                        </Grid>
                      </Grid>

                    </Grid>
                    
                    <Grid
                      container
                      style={{
                        display: "flex",
                        justifyContent: "flex-end",
                      }}
                    >
                      <Grid
                        item
                        style={{
                          display: "flex",
                          justifyContent: "space-around",
                        }}
                      >
                        <Grid>
                          <Button
                            variant="outlined"
                            sx={button_Outlined}
                            onClick={handleClear}
                          >
                            Clear
                          </Button>
                        </Grid>
                        <Grid sx={{ ...button_Marginleft }}>
                          <ReusablePreset
                            moduleName={"CostCenter"}
                            // PresetObj={PresetObj}
                            handleSearch={() => getFilter()}
                            // PresetMethod={PresetMethod}
                          />
                        </Grid>
                        <Grid>
                          <Button
                            variant="contained"
                            sx={{ ...button_Primary, ...button_Marginleft }}
                            onClick={() => getFilter()}
                          >
                            Search
                          </Button>
                        </Grid>
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </StyledAccordion>
              </Grid>
            </Grid>

            <Grid item sx={{ position: "relative" }}>
              <Stack>
                <ReusableTable
                  isLoading={tableLoading}
                  module={"CostCenter"}
                  width="100%"
                  title={"List of Cost Centers"}
                  rows={rmDataRows}
                  columns={dynamicColumns ?? []}
                  showSearch={true}
                  page={page}
                  pageSize={pageSize}
                  showExport={true}
                  showRefresh={true}
                  rowCount={count ?? rmDataRows?.length ?? 0}
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  getRowIdValue={"id"}
                  hideFooter={true}
                  checkboxSelection={true}
                  disableSelectionOnClick={true}
                  status_onRowDoubleClick={true}
                  onRowsSelectionHandler={handleSelectionModelChange}
                  callback_onRowDoubleClick={(params) => {
                    const costCenterNumber = params.row.costCenter; // Adjust this based on your data structure
                    navigate(
                      `/masterDataCockpit/costCenter/displayCostCenter/${costCenterNumber}`,
                      {
                        state: params.row,
                      }
                    );
                  }}
                  // setShowWork={setShowWork}
                  stopPropagation_Column={"action"}
                  // status_onRowDoubleClick={true}
                  showCustomNavigation={true}
                />

              </Stack>
            </Grid>

            <Paper
              sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
              elevation={2}
            >
              <BottomNavigation
                className="container_BottomNav"
                showLabels
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
                value={value}
                onChange={(newValue) => {
                  setValue(newValue);
                }}
              >
                <Button
                  onClick={() => {
                    navigate("/requestBench/CostCenterRequestTab");
                  }}
                  variant="contained"
                  size="small"
                  sx={{ ...button_Primary }}
                >
                  Create Request
                </Button>
              </BottomNavigation>
            </Paper>

          </Stack>
        </div>
      </div>
    </>
  );
};

export default CostCenter;
