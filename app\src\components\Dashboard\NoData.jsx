import { Box, Card, CardContent, Stack, Typography } from "@mui/material";
import React from "react";
import InsertChartIcon from "@mui/icons-material/InsertChart";
const NoData = ({ header, margintop = 13, ht = "320px", marginleft = 10 }) => {
  return (
    <>
      <Card sx={{ borderRadius: "10px", boxShadow: "4", minHeight: `${ht}` }}>
        <CardContent>
          <Stack sx={{alignItems:"center" ,justifyContents:'center'}}>
            <Typography variant="subtitle2" color="black">
              <strong>{header}</strong>
            </Typography>
            <Box
             
              // className="usd"
              mt={margintop}
             
            >
              <Typography
                variant="subtitle2"
                fontWeight="normal"
                
                color="#757575"
                fontSize="14px"
                sx={{ fontWeight: 500 ,}}
              >
                No data is available for the selected filter criteria{" "}
                <InsertChartIcon sx={{ fontSize: "20px" }} />
              </Typography>
            </Box>
          </Stack>
        </CardContent>
      </Card>
    </>
  );
};

export default NoData;
