import { destination_Admin } from "../../destinationVariables";
import { doAjax } from "../Common/fetchService";

 export const getAnnouncements = (setannouncementList,setisLoading) => {
    setisLoading(true);
    let hSuccess = (data) => {
      setannouncementList(data.broadcastDetailsDtoList);
      setisLoading(false);
    };
    let hError = () => {};
    doAjax(
      `/${destination_Admin}/broadcastManagement/category/Announcements`,
      "get",
      hSuccess,
      hError
    );
  };
export const getEvents = (setisLoadingEvent,seteventList) => {
    setisLoadingEvent(true);
    let hSuccess = (data) => {
      seteventList(data.broadcastDetailsDtoList);
      setisLoadingEvent(false);
    };
    let hError = () => {};

    doAjax(
      `/${destination_Admin}/broadcastManagement/upcomingEvents`,
      "get",
      hSuccess,
      hError
    );
  };
export const getVideos = (setisLoadingVideo,setvideoList) => {
    setisLoadingVideo(true);
    let hSuccess = (data) => {
      setvideoList(data.broadcastDetailsDtoList);
      setisLoadingVideo(false);
    };
    let hError = () => {};
    doAjax(
      `/${destination_Admin}/broadcastManagement/category/Videos`,
      "get",
      hSuccess,
      hError
    );
  };
 export const getallAnnouncement = (setisLoadingAnnouncement,setannouncementList1) => {
    setisLoadingAnnouncement(true);
    let hSuccess = (data) => {
      setannouncementList1(data.broadcastDetailsDtoList);
      setisLoadingAnnouncement(false);
    };
    let hError = () => {};
    doAjax(
      `/${destination_Admin}/broadcastManagement/getAll/category/Announcements`,
      "get",
      hSuccess,
      hError
    );
  };
 export const getallEvent = (setisLoadingEvent1, seteventList1) => {
    setisLoadingEvent1(true);
    let hSuccess = (data) => {
      seteventList1(data.broadcastDetailsDtoList);
      setisLoadingEvent1(false);
    };
    let hError = () => {};
    doAjax(
      `/${destination_Admin}/broadcastManagement/getAll/upcomingEvents`,
      "get",
      hSuccess,
      hError
    );
  };
export  const getallVideo = ( setisLoadingVideo1, setvideoList1) => {
    setisLoadingVideo1(true);
    let hSuccess = (data) => {
      setvideoList1(data.broadcastDetailsDtoList);
      setisLoadingVideo1(false);
    };
    let hError = () => {};

    doAjax(
      `/${destination_Admin}/broadcastManagement/getAll/category/Videos`,
      "get",
      hSuccess,
      hError
    );
  };
