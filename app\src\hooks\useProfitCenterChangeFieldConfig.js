import { changeTemplateDT } from "@app/tabsDetailsSlice";
import { doAjax } from "@components/Common/fetchService";
import React from "react";
import { useSelector, useDispatch } from "react-redux";
import { destination_IDM } from "../destinationVariables";
import { END_POINTS } from "@constant/apiEndPoints";
import { API_CODE, TASK_NAME } from "@constant/enum";
import { setDropDown } from "../app/dropDownDataSlice";
import useLogger from "./useLogger";
import { setChangeFieldSelectionData } from "../app/payloadSlice";

const useProfitCenterChangeFieldConfig = (props) => {

  const currentHash = window.location.hash;
  const parts = currentHash.split("/");

  const { customError } = useLogger();
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const dispatch = useDispatch();

  const getChangeTemplate = (templateName) => {
    const payload = {
      decisionTableId: null,
      decisionTableName: "MDG_CHANGE_TEMPLATE_DT",
      version: "v6",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MODULE": props,
          "MDG_CONDITIONS.MDG_MAT_ROLE": TASK_NAME.REQ_INITIATE_FIN,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    const hSuccess = (data) => {
      if (data.statusCode === API_CODE.STATUS_200) {
        const responseData =
          data?.data?.result?.[0]?.MDG_CHANGE_TEMPLATE_ACTION_TYPE || [];
        dispatch(setChangeFieldSelectionData(responseData));
        // Get unique template names
        const templateNames = [
          ...new Set(
            responseData
              .map((item) => item?.MDG_CHANGE_TEMPLATE_NAME)
              .filter(Boolean)
          ),
        ].map((name) => ({ code: name }));        
        dispatch(setDropDown({ keyName: "TemplateName", data: templateNames }));
        // return templateNames;
      } else {
        customError("Failed to fetch data");
        return [];
      }
    };
    const hError = (error) => {
      customError(error);
    };

    const endpoint =
      applicationConfig.environment === "localhost"
        ? END_POINTS.INVOKE_RULES.LOCAL
        : END_POINTS.INVOKE_RULES.PROD;

    doAjax(`/${destination_IDM}${endpoint}`, "post", hSuccess, hError, payload);
  };

  return { getChangeTemplate };
};

export default useProfitCenterChangeFieldConfig;
