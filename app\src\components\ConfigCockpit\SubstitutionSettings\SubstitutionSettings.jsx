import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
// import WorkspaceComponent from "@cw/cherrywork-iwm-workspace/Workspace";
import Substitution from "@cw/cherrywork-iwm-workspace/Substitution";
 
import { useNavigate } from "react-router-dom";
import { useLocation } from "react-router-dom";
 
import configData from "../../../data/configData";
import LoadingComponent from "../../Common/LoadingComponent";


 
 
export default function SubstitutionSettings() {
  let userData = useSelector((state) => state.userManagement.userData);
 
  let dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const applicationConfig = useSelector((state) => state.applicationConfig);
 
  const task = {
    inboxTypeKey: location.pathname.endsWith("workspace/completedtasks") ? "MY_COMPLETED_TASKS" : location.pathname.endsWith("workspace/admintasks") ? "ADMIN_TASKS" : location.pathname.endsWith("workspace/admincompletedtasks") ? "ADMIN_COMPLETED_TASKS" : "MY_TASKS",
    workspaceLabel: location.pathname.endsWith("workspace/completedtasks") ? "Completed Tasks" : location.pathname.endsWith("workspace/admintasks") ? "Admin Tasks" : location.pathname.endsWith("workspace/admincompletedtasks") ? "Admin Completed Tasks" : "Open Tasks",
  };
  const [userRawData, setUserRawData] = useState(null);
  const [userGroupRawData, setUserGroupRawData] = useState(null);
  const [userListBySystem, setUserListBySystem] = useState(null);
  const [loading, setLoading] = useState(false);
 
 
  const token = "eyJhbGciOiJSUzI1NiIsImprdSI6Imh0dHBzOi8vZXRwLXNoYXJlZC1sYWIuYXV0aGVudGljYXRpb24udXMxMC5oYW5hLm9uZGVtYW5kLmNvbS90b2tlbl9rZXlzIiwia2lkIjoiZGVmYXVsdC1qd3Qta2V5LWNjOWI5NjJkMzEiLCJ0eXAiOiJKV1QiLCJqaWQiOiAiNTUvYmJKYWdRejNhRWZrZjRHM0l5R3Bhdk0yR2V3aFVSbzJ4eEpnaUdJUT0ifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.V1JVYF-M9Jns-aZ0jNaTgvAXxYJB6jd7YzmU84rdZcz_1GVtDyvL6qKbpPBtVvg1eXZC2MWsaKt9bUoMvSvmzWqkwimapvN6TQm73pJNIJ4Tm_MycsIzd8UvmuPLozZTKBguK3Q9X4m7s2QqCcSlMqusN9nIT34Ey66rXGbLMIv2de5Vw3OUieAAbC9hYjbuNU-0PHD4y-aNIkg1Q6qcJRJTePDEkiRBA2e34Fc_tzoW6kSMs148xIOoVt05kvHqF9Lu6uOBOUJ6VsEYGIunctRM1ymmGUnMoAmWihwEKk-itLkJgCDr7r9Lf-VJ0RH-UmOXGbT08eYvhXszhzUbuA"
  const DestinationConfig = {
    APPLICATION_NAME: "1784",
    CRUD_API_ENV: "itm",
    DB_TYPE: "hana",
    SERVICE_BASE_URL:{
      APPLICATION_NAME: "1784",
      CRUD_API_ENV: "itm",
      DB_TYPE: "hana",
      SERVICE_BASE_URL: [
        {
          Description: "",
          Name: "ITMJavaServices",
          URL: "https://etp-shared-lab-cw-caf-cw-caf-iwm.cfapps.us10-001.hana.ondemand.com",
        },
        {
          Description: "",
          Name: "IWAServices",
          URL: "https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com",
        },
        {
          Description: "",
          Name: "ConfigServer",
          URL: "https://etp-shared-lab-cw-caf-cw-caf-configserver.cfapps.us10-001.hana.ondemand.com",
        },
        {
          Description: "",
          Name: "WorkNetServices",
          URL: "https://etp-shared-lab-cw-caf-cw-caf-worknet.cfapps.us10-001.hana.ondemand.com",
        },
        {
          Description: "",
          Name: "CrudApiServices",
          URL: "https://etp-shared-lab-cw-caf-cw-caf-crudapi.cfapps.us10-001.hana.ondemand.com",
        },
        {
          Description: "",
          Name: "WorkFormsServices",
          URL: "https://cherrywork-wf-java-dev.cfapps.eu10-004.hana.ondemand.com/workforms",
        },
        {
          Description: "",
          Name: "NotificationServices",
          URL: "https://etp-shared-lab-cw-caf-cw-caf-messaging.cfapps.us10-001.hana.ondemand.com",
        },
        {
          Description: "",
          Name: "ITMGraphServices",
          URL: "https://cherrywork-btp-dev-dashboard.cfapps.eu10-004.hana.ondemand.com",
        },
        {
          Description: "Native Workflow Services",
          Name: "NativeWorkflowServices",
          URL: "https://cherryworkproducts-custom-wf-dev.cfapps.eu10-004.hana.ondemand.com",
        },
        {
          Description: "Native Workflow UI URL",
          Name: "NativeWorkflowUiUrl",
          URL: "https://cherrywork-native-workflow-dev.cfapps.eu10-004.hana.ondemand.com/native-ui",
        },
        {
          Description: "",
          Name: "OnboardingServices",
          URL: "https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com",
        },
      ],
    
    }}
 
  return (
    <div style={{ position: "relative" }}>
      {loading && <LoadingComponent mg="30vh" />}
      <Substitution token={token} configData={configData} useWorkAccess={applicationConfig.environment === "localhost" ? true : false} useConfigServerDestination={applicationConfig.environment === "localhost" ? true : false} destinationData={DestinationConfig} userData={{ ...userData, user_id: userData?.emailId }} userList={{}} groupList={{}} userListBySystem={userListBySystem} />
    </div>
  );
}
 