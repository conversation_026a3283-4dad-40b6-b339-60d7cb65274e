export const returnUserGroupMap = (oData) => {
  let parsedGroups = {};
  oData?.data.map((group, index) => {
    parsedGroups[group.id] = group;
  });
  return parsedGroups;
};

export const userGroupRawData = {
  status: "true",
  statusCode: "200",
  message: "Groups are fetched",
  data: [
    {
      groupName: "Deb_Group",
      roleDetails: [
        { roleName: "default", roleId: 905 },
        { roleName: "SystemAdministrator", roleId: 906 },
        { roleName: "TaskCreator", roleId: 907 },
        { roleName: "WorkflowBuilder", roleId: 908 },
        { roleName: null, roleId: 0 },
        { roleName: null, roleId: 0 },
      ],
      id: 137,
      applicationId: 1783,
      userDetails: [
        { userName: "Arpita Mandal", userId: "<EMAIL>", userEmailId: "<EMAIL>" },
        { userName: "Debadutta Panda", userId: "<EMAIL>", userEmailId: "<EMAIL>" },
        { userName: "<PERSON>ks<PERSON><PERSON> Brah<PERSON>", userId: "<EMAIL>", userEmailId: "<EMAIL>" },
        { userName: "George Abraham", userId: "<EMAIL>", userEmailId: "<EMAIL>" },
      ],
    },
    {
      groupName: "CW-ITM Quality Assurance Approvers",
      roleDetails: [{ roleName: "default", roleId: 905 }],
      id: 643,
      applicationId: 1783,
      userDetails: [
        { userName: "adil shariff", userId: "<EMAIL>", userEmailId: "<EMAIL>" },
        { userName: "Akash Mugalikatti", userId: "<EMAIL>", userEmailId: "<EMAIL>" },
        { userName: "Ekta Soni", userId: "<EMAIL>", userEmailId: "<EMAIL>" },
        { userName: "Syed Shafiuddin", userId: "<EMAIL>", userEmailId: "<EMAIL>" },
      ],
    },
    {
      groupName: "L1 Approvers",
      roleDetails: [{ roleName: "default", roleId: 905 }],
      id: 644,
      applicationId: 1783,
      userDetails: [
        { userName: "Arpita Mandal", userId: "<EMAIL>", userEmailId: "<EMAIL>" },
        { userName: "Prashant Jha", userId: "<EMAIL>", userEmailId: "<EMAIL>" },
        { userName: "Preetham m", userId: "<EMAIL>", userEmailId: "<EMAIL>" },
        { userName: "Prince Kumar", userId: "<EMAIL>", userEmailId: "<EMAIL>" },
      ],
    },
    {
      groupName: "CW-ITM Quality Assurance Reviewers",
      roleDetails: [{ roleName: "default", roleId: 905 }],
      id: 647,
      applicationId: 1783,
      userDetails: [
        { userName: "Ekta Soni", userId: "<EMAIL>", userEmailId: "<EMAIL>" },
        { userName: "Syed Shafiuddin", userId: "<EMAIL>", userEmailId: "<EMAIL>" },
      ],
    },
  ],
};
