import { Accordion, AccordionDetails, AccordionSummary, Box, Button, Checkbox, FormControl, Grid, InputLabel, ListItemText, MenuItem, Select, TextField, Typography } from '@mui/material'
import React from 'react'
import { button_Outlined, button_Primary, container_filter, font_Small, iconButton_SpacingSmall, icon_MarginLeft } from '../common/commonStyles'
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useDispatch, useSelector } from 'react-redux';
// import { commonFilterClear, commonFilterUpdate } from '../../app/commonFilterSlice';
import { destination_Admin } from '../../destinationVariables';
import axios from 'axios';
// import PresetV3 from '../common/PresetV3';
import FilterFieldBroadcast from '../common/ReusableFilterBox/FilterFieldBroadcast';

const BroadcastFilter = ({handleSearch,PresetMethod,PresetObj}) => {
  const presentDate = new Date();
const backDate = new Date();
backDate.setDate(backDate.getDate() - 8);
  const category=[
    "Announcements","Videos","Events"
  ];
  const status=[
"Active","Inactive","Draft","Archived"
  ];
  const supplier=[];
  // const dispatch = useDispatch();
  // const FilterSearchForm = useSelector(
  //   (state) => state.commonFilter["BroadcastHome"]
  // );
  // const appSettings=useSelector((state)=> state.appSettings["Format"])

  const moduleFilterData = [
    {
      type: "text",
      filterName: "id",
      filterTitle: "Broadcast ID",
    },
    {
      type: "multiSelect",
      filterName: "category",
      filterData: category,
      filterTitle: "Broadcast Category",
    },
    {
      type: "multiSelect",
      filterName: "status",
      filterData: status,
      filterTitle: "Broadcast Status",
    },
    {
      type: "dateRange",
      filterName: "startDate",
      filterTitle: "Start Date",
    },
    {
      type: "dateRange",
      filterName: "endDate",
      filterTitle: "End Date",
    },
  ];
 

  // const handleClear=()=>{
  //   dispatch(commonFilterClear({module:"BroadcastHome",days:appSettings.range}))
  //   // dispatch(
  //   //   commonFilterUpdate({
  //   //     module: "BroadcastHome",
  //   //     filterData: {
  //   //       ...FilterSearchForm,id:"",category:[],status:[],supplier:[],startDate:[presentDate,backDate],
  //   //       endDate:[presentDate,backDate],title:""
  //   //     },
  //   //   })
  //   // );
  // }
  return (
    <Grid container sx={container_filter}>
      <Grid item md={12}>
       <FilterFieldBroadcast
        searchParameters={moduleFilterData}
        // onFilterChange={(fieldName, value) => {
        //   // Handle filter changes
        // }}
        onSearch={handleSearch}
        // onClear={() => {
        // }}
        moduleName="BroadcastHome"
        isLoading={false}
        t={(key) => key} // Translation function
      />
      </Grid>
    </Grid>
  )
}

export default BroadcastFilter