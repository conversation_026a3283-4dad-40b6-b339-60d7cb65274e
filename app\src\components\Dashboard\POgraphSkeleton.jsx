import { Card, CardContent, Skeleton, Stack, Typography } from "@mui/material";
import React from "react";

const POgraphSkeleton = ({header,ht='320px'}) => {
  return (
    <>
      <Card
        sx={{
          borderRadius: "10px",
          boxShadow: "4",
          minHeight: ht,
          maxHeight: ht,
        }}
      >
        {" "}
        <CardContent sx={{backgroundColor:'#F5F5F5'}}>
          <Stack alignItems="center">
            <Typography variant="subtitle2" color="#1d1d1d">
              <strong>{header}</strong>
            </Typography>
            <Stack
              sx={{
                display: "flex",
                flexDirection: "row",
                alignItems: "flex-end",
              }}
              mt={5}
            >
              <Skeleton
                animation="pulse"
                variant="rectangular"
                width={10}
                height={200}
                sx={{ marginRight: "25px" }}
              />

              <Skeleton
                animation="pulse"
                variant="rectangular"
                width={30}
                height={180}
                sx={{
                  backgroundColor: "#ff5555",
                  marginRight: "25px",
                }}
              />

              <Skeleton
                animation="pulse"
                variant="rectangular"
                width={30}
                height={100}
                sx={{
                  backgroundColor: "#a7eff3",
                  marginRight: "25px",
                }}
              />
              <Skeleton
                animation="pulse"
                variant="rectangular"
                width={30}
                height={130}
                sx={{
                  backgroundColor: "#fbb708",
                  marginRight: "25px",
                }}
              />
              <Skeleton
                animation="pulse"
                variant="rectangular"
                width={30}
                height={80}
                sx={{
                  backgroundColor: "#b1aade",
                  marginRight: "25px",
                }}
              />
              <Skeleton
                animation="pulse"
                variant="rectangular"
                width={30}
                height={100}
                sx={{ backgroundColor: "#59e0e9" }}
              />
            </Stack>
            <Skeleton animation="pulse" width={350} height={10} />
          </Stack>
        </CardContent>{" "}
      </Card>
    </>
  );
};

export default POgraphSkeleton;
