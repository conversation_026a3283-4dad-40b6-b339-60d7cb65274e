import { useCallback } from "react";
import { useSelector } from "react-redux";
import { useSnackbar } from "@hooks/useSnackbar";
import {  API_CODE } from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import { destination_BOM } from "../../../destinationVariables";
import { createBOMPayload } from "../../../functions";
import { doAjax } from "@components/Common/fetchService";
import { useNavigate } from "react-router-dom";

const BOM_ACTION_MAPPING = {
  SAVE_AS_DRAFT: {
    endpoint: `/${destination_BOM}${END_POINTS.MASS_ACTION.CREATE_BOM_SAVE_AS_DRAFT}`,
  },
  SUBMIT_FOR_REVIEW: {
    endpoint: `/${destination_BOM}${END_POINTS.MASS_ACTION.CREATE_BOM_SUBMIT_FOR_REVIEW}`,
  },
  APPROVE: {
    endpoint: `/${destination_BOM}${END_POINTS.MASS_ACTION.CREATE_BOM_SUBMIT_FOR_APPROVE}`,
  },
  VALIDATE: {
    endpoint: `/${destination_BOM}${END_POINTS.MASS_ACTION.VALIDATE_BOM}`,
  },
  SYNDICATE: {
    endpoint: `/${destination_BOM}${END_POINTS.MASS_ACTION.BOM_SYNDICATE}`,
  },
};

export default function useBomBottomButtons() {
  const bomRows = useSelector((state) => state.bom.bomRows);
  const tabFieldValues = useSelector((state) => state.bom.tabFieldValues);
  const payloadFields = useSelector((state) => state.bom.BOMpayloadData);
  const createdRequestId = useSelector((state) => state.bom.requestHeaderID);
  const taskData = useSelector((state) => state.userManagement.taskData);
  const { showSnackbar } = useSnackbar();
  const navigate = useNavigate();

  const handleBottomButton = useCallback(
    (type) => {
      const payload = createBOMPayload(
        bomRows,
        tabFieldValues,
        payloadFields,
        createdRequestId,
        taskData
      );
      const actionConfig = BOM_ACTION_MAPPING[type];
      if (!actionConfig) {
        showSnackbar("Invalid action type", "error");
        return;
      }
      navigate(-1)
      doAjax(
        actionConfig.endpoint,
        "post",
        (data) => {
          if (data.statusCode !== API_CODE.STATUS_200) {
            showSnackbar(data.message, "error");
          } else {
            showSnackbar(data.message, "success");
          }
        },
        (err) => {
          showSnackbar(actionConfig.errorMessage, "error");
        },
        payload
      );
    },
    [
      bomRows,
      tabFieldValues,
      payloadFields,
      createdRequestId,
      taskData,
      showSnackbar,
    ]
  );

  return { handleBottomButton };
} 