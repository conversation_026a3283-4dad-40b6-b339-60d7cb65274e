import { createSlice } from "@reduxjs/toolkit";

const initialState = {
//   profitCenterBasicData: {},
//   profitCenterCompCodes: {},
//   profitCenterIndicators: {},
//   profitCenterAddress: {},
//   profitCenterCommunication: {},
//   profitCenterHistory: {},
//   profitCenterViewData: [],
//   singlePCPayload: {},
//   singlePCPayloadGI:{},
singleETCCPayloadGI:{},
//   requiredFields: [],
//   errorFields: [],
//   EditMultipleCostCenter:{},
//   EditMultipleProfitCenterData:[],
//   MultipleProfitCenterData: [],
//   MultipleProfitCenterRequestBench:[],
//   requiredFieldsGI: [],
//   companyCode: [
//     {
//       id: 1,
//       companyCodes: "",
//       companyName: "",
//       assigned: "",
//     },
//   ],
//   handleMassMode:""
};

export const costCenterTabSliceET = createSlice({
  name: "costCenterET",
  initialState,
  reducers: {
    // setProfitCenterBasicDataTab: (state, action) => {
    //   state.profitCenterBasicData = action.payload;
    // },
    // setProfitCenterCompCodesTab: (state, action) => {
    //   state.profitCenterCompCodes = action.payload;
    // },
    // setProfitCenterIndicatorsTab: (state, action) => {
    //   state.profitCenterIndicators = action.payload;
    // },
    // setProfitCenterAddressTab: (state, action) => {
    //   state.profitCenterAddress = action.payload;
    // },
    // setProfitCenterCommunicationTab: (state, action) => {
    //   state.profitCenterCommunication = action.payload;
    // },
    // setProfitCenterHistoryTab: (state, action) => {
    //   state.profitCenterHistory = action.payload;
    // },
    // setProfitCenterViewData: (state, action) => {
    //   state.profitCenterViewData = action.payload;
    // },
    // setSingleProfitCenterPayload: (state, action) => {
    //   state.singlePCPayload[action.payload.keyName] = action.payload.data;
    //   return state;
    // },
    // setSingleProfitCenterPayloadGI: (state, action) => {
    //   state.singlePCPayloadGI[action.payload.keyName] = action.payload.data;
    //   return state;
    // },
    setSingleCostCenterETPayloadGI: (state, action) => {
      state.singleETCCPayloadGI[action.payload.keyName] = action.payload.data;
      return state;
    },
    clearCostCenterPayload: (state) => {
      state.singleETCCPayloadGI = {};
    },
    // setPCRequiredFieldsGI: (state, action) => {
    //   if (
    //     state.requiredFieldsGI.findIndex((item) => item == action.payload) == -1
    //   ) {
    //     state.requiredFieldsGI.push(action.payload);
    //   }
    //   return state;
    // },
    // setPCRequiredFields: (state, action) => {
    //   if (
    //     state.requiredFields.findIndex((item) => item == action.payload) == -1
    //   ) {
    //     state.requiredFields.push(action.payload);
    //   }
    //   return state;
    // },
    // setPCErrorFields: (state, action) => {
    //   state.errorFields = action.payload;
    //   return state;
    // },
    // setCompanyCode: (state, action) => {
    //   state.companyCode = action.payload;
    //   return state;
    // },
    // setEditCostCenter(state, action) {
    //   state.EditMultipleCostCenter = {
    //     ...state.EditMultipleCostCenter,
    //     ...action.payload,
    //   };
    // },
    // setMultipleProfitCenterData(state, action) {
    //   state.MultipleProfitCenterData = action.payload;
    //   return state;
    // },
    // setEditMultipleProfitCenterData(state,action){
    //   state.editMultipleProfitCenterData =action.payload;
    // },
    // setMultipleProfitCenterRequestBench(state, action) {
    //   state.MultipleProfitCenterRequestBench = action.payload;
    //   return state;
    // },
    // setHandleMassMode(state,action){
    //   state.handleMassMode = action.payload
    // },
    // clearProfitCenter :(state) =>{
    //   state.singlePCPayload = {}
    //   state.errorFields = []
    //   state.requiredFields = []
    //   state.EditMultipleCostCenter = {}
    //   state.companyCode = initialState.companyCode
    // },

  },
});

// Action creators are generated for each case reducer function
export const {
//   setProfitCenterBasicDataTab,
//   setProfitCenterCompCodesTab,
//   setProfitCenterIndicatorsTab,
//   setProfitCenterAddressTab,
//   setProfitCenterCommunicationTab,
//   setProfitCenterHistoryTab,
//   setSingleProfitCenterPayload,
//   clearProfitCenterPayload,
//   setPCRequiredFields,
//   setPCErrorFields,
//   setCompanyCode,
//   setMultipleProfitCenterData,
//   setEditMultipleProfitCenterData,
//   setMultipleProfitCenterRequestBench,
//   setHandleMassMode,
//   clearProfitCenter,
//   setProfitCenterViewData,
//   setPCRequiredFieldsGI,
//   setSingleProfitCenterPayloadGI,
  setSingleCostCenterETPayloadGI,
  clearCostCenterPayload
} = costCenterTabSliceET.actions;

export const costCenterETReducer = costCenterTabSliceET.reducer;
