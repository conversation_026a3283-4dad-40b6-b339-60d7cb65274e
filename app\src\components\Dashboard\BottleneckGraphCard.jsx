import React, { useEffect, useState } from "react";
import { Card, CardContent, Typography, Box } from "@mui/material";
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, YAxis, Tooltip } from "recharts";
import LegendBox from "./LegendBox";
import { useSelector } from "react-redux";
import {  destination_MaterialMgmt } from "../../destinationVariables";
import { doAjax } from "../common/fetchService";
import moment from "moment";

const BottleneckGraphCard = () => {
  const dashboardSearchForm = useSelector(
    (state) => state.commonFilter["Dashboard"]
  );
  const [data, setData] = useState([]);

  const API_Controller = {
    handleSearch2: () => {
      let payload = new FormData();
      payload.append(
        "fromDate",
        moment(dashboardSearchForm?.dashboardDate[0]).format(
          "YYYY-MM-DDTHH:mm:ss"
        )
      );
      payload.append(
        "toDate",
        moment(dashboardSearchForm?.dashboardDate[1]).format(
          "YYYY-MM-DDTHH:mm:ss"
        )
      );
      const hSuccess1 = (data) => {
        setData(data?.data);
      };
      const hError1 = (err) => {
        console.error("Error fetching bottleneck data:", err);
      };
      doAjax(
        // `/${destination_MaterialMgmt}/dashboard/getBottleneckResponse`,
        "postformdata",
        hSuccess1,
        hError1,
        payload
      );
    },
  };

  useEffect(() => {
    API_Controller.handleSearch2();
  }, [dashboardSearchForm?.dashboardDate]);

  const legendDataStackedBar = [
    {},
    { name: "Create Requests", col: "#59C2E3" },

    { name: "Change Requests", col: "#C3F0FE" },
    { name: "Extend Requests", col: "#FFFDCC" },
  ];

  const CustomTooltip = ({ active, payload }) => {
    if (active) {
      const data = payload[0].payload;
      return (
        <div
          style={{
            background: "white",
            border: "1px solid #ccc",
            padding: "10px",
          }}
        >
          <p>
            <strong>Status: {data.processName}</strong>
          </p>
          <p>Create Requests: {data.createCount}</p>

          <p>Change Requests: {data.changeCount}</p>
          <p>Extend Requests: {data.extendCount}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card
      style={{
        minHeight: "400px",
        borderRadius: "10px",
        boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2)",
        textAlign: "center", // Center-align the content horizontally
      }}
    >
      <CardContent>
        <Typography variant="h6">Bottleneck by Status</Typography>
        <Box mt={3} mx={15} mb={3}>
          {" "}
          {/* mx="auto" centers the child horizontally */}
          <BarChart width={900} height={300} data={data}>
            <XAxis dataKey="processName"
            tick={{ fontSize: 10, fontFamily: "Roboto, sans-serif" }}
            />
            <YAxis tick={{ fontSize: "10px" }} />
            <Tooltip content={<CustomTooltip />} />
            <Bar
              dataKey="createCount"
              fill="#59C2E3"
              barSize={30}
              radius={[5, 5, 5, 5]}
            />
            <Bar
              dataKey="changeCount"
              fill="#C3F0FE"
              barSize={30}
              radius={[5, 5, 5, 5]}
            />
            <Bar
              dataKey="extendCount"
              fill="#FFFDCC"
              barSize={30}
              radius={[5, 5, 5, 5]}
            />
          </BarChart>
        </Box>
        <Box mx={20}>
          <div onClick={() => {}} style={{ pointerEvents: "none" }}>
            <LegendBox
              content={legendDataStackedBar}
              margin={10}
              //type={type}
              itemCount={3}
              //mat={matflag}
            />
          </div>
        </Box>
      </CardContent>
    </Card>
  );
};

export default BottleneckGraphCard;
