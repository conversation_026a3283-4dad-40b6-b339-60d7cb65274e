const configDataForCompletedTasks = {
  applicationProperties: {
    taskClassification: "PROCESS",
    taskDisplay: {
      taskIdentifier: false,
      taskDescription: true,
    },
    workspaceColumns: [
      {
        width: "15%",
        label: "Task Name",
        accessorKey: "taskName",
      },
      {
        width: "11%",
        label: "Transaction Id",
        accessorKey: "processDesc",
      },
      // {
      //   width: "11%",
      //   label: "Company",
      //   accessorKey: "priority",
      // },
      // {
      //   width: "11%",
      //   label: "Supplier",
      //   accessorKey: "subject",
      // },
      {
        width: "15%",
        label: "System & Process",
        accessorKey: "system_process",
      },
      {
        width: "12%",
        label: "Created On",
        accessorKey: "createdOn",
      },
      {
        width: "15%",
        label: "Due Date",
        accessorKey: "dueDate",
      },
      {
        width: "10%",
        label: "Status",
        accessorKey: "status",
      },

    ],
    default: {
      useWorkAccess: true,
      useConfigServerDestination: true,
      showAppHeader: true,
      allowInAppNotification: false,
      configEndpoint: "",
      forwardOptions: null,
      primaryActions: "ALL",
      secondaryActions: "ALL",
      tertiaryActions: "ALL",
      allowAttachments: "ALL",
      isSessionExpiryEnabled: false,
      sessionExpiresIn: 600,
      adminTasksPerPage: 50,
      dateTimeFormat: "DD MMM YYYY, hh:mm A",
    },
    attachmentsConfigs: {
      maximumFileNameLength: 255,
      maximumDescriptionLength: 255,
      maxAttachmentSizeInMB: 25,
      defaultAttachmentDecription: "Document",
      allowedMIMETypes: [
        "text/csv",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "image/jpeg",
        "image/jpg",
        "application/vnd.oasis.opendocument.presentation",
        "application/vnd.oasis.opendocument.text",
        "image/png",
        "application/pdf",
        "application/vnd.ms-powerpoint",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "application/rtf",
        "text/plain",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/xml",
        "text/xml",
        "application/atom+xml",
        "application/xml",
        "application/vnd.openxmlformats-officedocument.presentationml.slideshow",
      ],
    },
  },
  taskPermissions: {
    ASSIGNED_TO_ME_TASKS_AssignedToMe: {
      technicalFilter: {
        taskTypes: [],
        status: ["READY", "RESERVED"],
      },
      allowAttachments: true,
      allowFullScreen: false,
      taskCategorization: {
        isAvailable: true,
        categories: ["Due Today", "Upcoming Tasks"],
      },
      pin: true,
      multiSelect: true,
      actions: {
        primaryActions: true,
        secondaryActions: true,
        tertiaryActions: true,
      },
      SLABand: true,
      comment: false,
      viewChat: true,
      viewLogs: true,
      printable: true,
      viewTaskForm: true,
      taskVisualisations: ["WB_GRID_VIEW"],
    },
    ASSIGNED_TO_ME_TASKS_PinnedTasks: {
      allowAttachments: true,
      allowFullScreen: false,
      technicalFilter: {
        taskTypes: ["ASSIGNED_TO_ME_TASKS", "SUBSTITUTED_FOR_ME_TASKS", "ALL_GROUP_TASKS"],
        status: ["READY", "RESERVED"],
      },
      taskCategorization: {
        isAvailable: false,
        categories: null,
      },
      pin: true,
      multiSelect: true,
      actions: {
        primaryActions: true,
        secondaryActions: true,
        tertiaryActions: true,
      },
      SLABand: true,
      comment: false,
      viewChat: true,
      viewLogs: true,
      viewTaskForm: true,
      viewFlow: true,
      printable: true,
      taskVisualisations: ["WB_GRID_VIEW"],
    },
    ASSIGNED_TO_ME_TASKS_CompletedTasks: {
      allowAttachments: true,
      allowFullScreen: false,
      technicalFilter: {
        taskTypes: ["ASSIGNED_TO_ME_TASKS", "SUBSTITUTED_FOR_ME_TASKS", "ALL_GROUP_TASKS"],
        status: ["COMPLETED"],
      },
      taskCategorization: {
        isAvailable: false,
        categories: [],
      },
      pin: false,
      multiSelect: true,
      actions: {
        primaryActions: false,
        secondaryActions: false,
        tertiaryActions: false,
      },
      forward: false,
      SLABand: false,
      comment: false,
      viewChat: true,
      viewLogs: true,
      viewTaskForm: true,
      printable: true,
      taskVisualisations: ["WB_GRID_VIEW"],
    },
    SUBSTITUTED_FOR_ME_TASKS_SubstitutedToMe: {
      allowAttachments: true,
      allowFullScreen: false,
      technicalFilter: {
        taskTypes: [],
        status: ["READY", "RESERVED"],
      },
      taskCategorization: {
        isAvailable: true,
        categories: ["Due Today", "Upcoming Tasks"],
      },
      pin: true,
      multiSelect: true,
      actions: {
        primaryActions: true,
        secondaryActions: true,
        tertiaryActions: true,
      },
      SLABand: true,
      comment: false,
      viewChat: true,
      viewLogs: true,
      viewTaskForm: true,
      printable: true,
      taskVisualisations: ["WB_GRID_VIEW"],
    },
    SUBSTITUTED_FOR_ME_TASKS_PinnedTasks: {
      allowAttachments: true,
      allowFullScreen: false,
      technicalFilter: {
        taskTypes: ["ASSIGNED_TO_ME_TASKS", "SUBSTITUTED_FOR_ME_TASKS", "ALL_GROUP_TASKS"],
        status: ["READY", "RESERVED"],
      },
      taskCategorization: {
        isAvailable: false,
        categories: null,
      },
      pin: true,
      multiSelect: false,
      actions: {
        primaryActions: true,
        secondaryActions: true,
        tertiaryActions: true,
      },
      SLABand: true,
      comment: false,
      viewChat: true,
      printable: true,
      viewLogs: true,
      viewTaskForm: true,
      taskVisualisations: ["WB_GRID_VIEW"],
    },
    SUBSTITUTED_FOR_ME_TASKS_CompletedTasks: {
      allowAttachments: true,
      allowFullScreen: false,
      technicalFilter: {
        taskTypes: ["ASSIGNED_TO_ME_TASKS", "SUBSTITUTED_FOR_ME_TASKS", "ALL_GROUP_TASKS"],
        status: ["COMPLETED"],
      },
      taskCategorization: {
        isAvailable: false,
        categories: [],
      },
      pin: false,
      multiSelect: false,
      actions: {
        primaryActions: false,
        secondaryActions: false,
        tertiaryActions: false,
      },
      SLABand: false,
      comment: false,
      viewChat: true,
      viewLogs: true,
      printable: true,
      viewTaskForm: true,
      taskVisualisations: ["WB_GRID_VIEW"],
    },
    SUBSTITUTION_TASKS_SubstitutedByMe: {
      allowAttachments: true,
      allowFullScreen: false,
      technicalFilter: {
        taskTypes: [],
        status: ["READY", "RESERVED"],
      },
      taskCategorization: {
        isAvailable: false,
        categories: [],
      },
      pin: true,
      multiSelect: false,
      actions: {
        primaryActions: false,
        secondaryActions: false,
        tertiaryActions: false,
      },
      SLABand: true,
      comment: false,
      viewChat: true,
      viewLogs: true,
      viewTaskForm: true,
      printable: true,
      taskVisualisations: ["WB_GRID_VIEW"],
    },
    SUBSTITUTION_TASKS_PinnedTasks: {
      allowAttachments: true,
      allowFullScreen: false,
      technicalFilter: {
        taskTypes: [],
        status: ["READY", "RESERVED"],
      },
      taskCategorization: {
        isAvailable: false,
        categories: [],
      },
      pin: true,
      multiSelect: false,
      actions: {
        primaryActions: true,
        secondaryActions: true,
        tertiaryActions: true,
      },
      SLABand: true,
      comment: false,
      viewChat: true,
      viewLogs: true,
      viewTaskForm: true,
      printable: true,
      taskVisualisations: ["WB_GRID_VIEW"],
    },
    SUBSTITUTION_TASKS_CompletedTasks: {
      allowAttachments: true,
      allowFullScreen: false,
      technicalFilter: {
        taskTypes: [],
        status: ["COMPLETED"],
      },
      taskCategorization: {
        isAvailable: false,
        categories: [],
      },
      pin: false,
      multiSelect: false,
      actions: {
        primaryActions: false,
        secondaryActions: false,
        tertiaryActions: false,
      },
      SLABand: false,
      comment: false,
      viewChat: true,
      viewLogs: true,
      viewTaskForm: true,
      printable: true,
      taskVisualisations: ["WB_GRID_VIEW"],
    },
    ADMIN_TASKS_AdminTasks: {
      allowAttachments: true,
      allowFullScreen: false,
      technicalFilter: {
        taskTypes: [],
        status: ["READY", "RESERVED"],
      },
      taskCategorization: {
        isAvailable: true,
        categories: ["Due Today", "Upcoming Tasks"],
      },
      pin: true,
      multiSelect: false,
      actions: {
        primaryActions: false,
        secondaryActions: false,
        tertiaryActions: false,
      },
      SLABand: true,
      comment: false,
      printable: true,
      viewChat: true,
      viewLogs: true,
      viewTaskForm: true,
    },
    ADMIN_TASKS_PinnedTasks: {
      allowAttachments: true,
      allowFullScreen: false,
      technicalFilter: {
        taskTypes: [],
        status: ["READY", "RESERVED"],
      },
      taskCategorization: {
        isAvailable: false,
        categories: null,
      },
      pin: true,
      multiSelect: false,
      actions: {
        primaryActions: true,
        secondaryActions: true,
        tertiaryActions: true,
      },
      SLABand: true,
      comment: false,
      viewChat: true,
      printable: true,
      viewLogs: true,
      viewTaskForm: true,
    },
    ADMIN_TASKS_CompletedTasks: {
      allowAttachments: true,
      allowFullScreen: false,
      technicalFilter: {
        taskTypes: [],
        status: ["COMPLETED"],
      },
      taskCategorization: {
        isAvailable: false,
        categories: null,
      },
      pin: true,
      multiSelect: false,
      actions: {
        primaryActions: true,
        secondaryActions: true,
        tertiaryActions: true,
      },
      SLABand: true,
      comment: false,
      viewChat: true,
      printable: true,
      viewLogs: true,
      viewTaskForm: true,
    },
    CREATED_TASKS_CreatedTask: {
      technicalFilter: {
        taskTypes: [],
        status: ["READY", "RESERVED"],
      },
      allowAttachments: true,
      allowFullScreen: false,
      taskCategorization: {
        isAvailable: true,
        categories: ["Due Today", "Upcoming Tasks"],
      },
      pin: true,
      multiSelect: false,
      actions: {
        primaryActions: false,
        secondaryActions: false,
        tertiaryActions: false,
      },
      SLABand: true,
      comment: false,
      viewChat: true,
      viewLogs: true,
      printable: true,
      viewTaskForm: true,
      taskVisualisations: ["WB_GRID_VIEW"],
    },
    CREATED_TASKS_PinnedTasks: {
      allowAttachments: true,
      allowFullScreen: false,
      technicalFilter: {
        taskTypes: ["ASSIGNED_TO_ME_TASKS", "SUBSTITUTED_FOR_ME_TASKS", "ALL_GROUP_TASKS"],
        status: ["READY", "RESERVED"],
      },
      taskCategorization: {
        isAvailable: false,
        categories: null,
      },
      pin: true,
      multiSelect: false,
      actions: {
        primaryActions: false,
        secondaryActions: false,
        tertiaryActions: false,
      },
      SLABand: true,
      comment: false,
      viewChat: true,
      viewLogs: true,
      viewTaskForm: true,
      viewFlow: true,
      printable: true,
      taskVisualisations: ["WB_GRID_VIEW"],
    },
    CREATED_TASKS_CompletedTasks: {
      allowAttachments: true,
      allowFullScreen: false,
      technicalFilter: {
        taskTypes: ["ASSIGNED_TO_ME_TASKS", "SUBSTITUTED_FOR_ME_TASKS", "ALL_GROUP_TASKS"],
        status: ["COMPLETED"],
      },
      taskCategorization: {
        isAvailable: false,
        categories: [],
      },
      pin: false,
      multiSelect: false,
      actions: {
        primaryActions: false,
        secondaryActions: false,
        tertiaryActions: false,
      },
      forward: false,
      SLABand: false,
      comment: false,
      viewChat: true,
      viewLogs: true,
      viewTaskForm: true,
      printable: true,
      taskVisualisations: ["WB_GRID_VIEW"],
    },
  },
};

export default configDataForCompletedTasks;
