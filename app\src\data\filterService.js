export const task = {
  systemId: "Flowable",
  systemName: "Native Workflow",
  processName: "onboarding:5:1277aa81-d9df-11ed-bcef-02051f9a391c",
  taskType: "HR task for generate offer letter",
  taskId: "8e46324d-d9e0-11ed-bcef-02051f9a391c",
  processId: "8e460b2b-d9e0-11ed-bcef-02051f9a391c",
  requestId: "8e460b2b-d9e0-11ed-bcef-02051f9a391c",
  referenceId: "",
  taskDesc: "HR task for generate offer letter HR task for generate offer letter HR task for generate offer letter HR task for generate offer letter",
  processDesc: "onboarding:5:1277aa81-d9df-11ed-bcef-02051f9a391c",
  businessStatus: "RESERVED",
  technicalStatus: "RESERVED",
  priority: "0",
  subject: "onboarding:5:1277aa81-d9df-11ed-bcef-02051f9a391c",
  createdBy: "<EMAIL>",
  updatedBy: "<EMAIL>",
  completedBy: null,
  formId: "5ce67f7b-d29b-4182-9571-bb2a6b4efdbb",
  itmStatus: null,
  isForwarded: 0,
  color: null,
  isPinned: 1,
  compDeadline: 1681552200000,
  criticalDeadline: 1681552200000,
  createdOn: 1681379400000,
  updatedOn: 1681379400000,
  completedAt: null,
  taskNature: "Single-User",
  actions: [
    {
      systemId: "Flowable",
      processName: "onboarding:5:1277aa81-d9df-11ed-bcef-02051f9a391c",
      taskType: "HR task for generate offer letter",
      action: "APPROVE",
      icon: "MaterialIcon.MdDone",
      hoverIcon: "",
      label: "Approve",
      priority: "PRIMARY",
      actionOrder: "POSITIVE",
      status: "READY",
      isActive: 1,
    },
    {
      systemId: "Flowable",
      processName: "onboarding:5:1277aa81-d9df-11ed-bcef-02051f9a391c",
      taskType: "HR task for generate offer letter",
      action: "FORWARD",
      icon: "SVGIcons.ForwardIcon",
      hoverIcon: "",
      label: "Forward",
      priority: "SECONDARY",
      actionOrder: "POSITIVE",
      status: "READY",
      isActive: 1,
    },
    {
      systemId: "Flowable",
      processName: "onboarding:5:1277aa81-d9df-11ed-bcef-02051f9a391c",
      taskType: "HR task for generate offer letter",
      action: "REJECT",
      icon: "IcoMoon5Icon.IoClose",
      hoverIcon: "",
      label: "Reject",
      priority: "PRIMARY",
      actionOrder: "NEGATIVE",
      status: "READY",
      isActive: 1,
    },
  ],
  ownerType: "USER",
  ownerId: "<EMAIL>",
  createdByName: "George Abraham",
  updatedByName: "George Abraham",
  forwardedByName: "",
  isCritical: false,
  isBreached: true,
  timeLeftDisplayString: "BREACHED",
  taskSla: "BREACHED",
};

export const systemList = {
  Ariba: { systemId: "Ariba", systemLogo: "SVGIcons.Ariba", systemName: "Ariba", isActive: true, createTaskEnabled: false },
  DocuSign: { systemId: "DocuSign", systemLogo: "SVGIcons.DocuSign", systemName: "DocuSign", isActive: true, createTaskEnabled: false },
  ECC: { systemId: "ECC", systemLogo: "SVGIcons.ECCSystem", systemName: "ECC", isActive: true, createTaskEnabled: false },
  ITM: { systemId: "ITM", systemLogo: "SVGIcons.Flowable", systemName: "Intelligent Task Managemant", isActive: true, createTaskEnabled: false },
  Microsoft: { systemId: "Microsoft Outlook", systemLogo: "SVGIcons.MSOutlookSystem", systemName: "Microsoft Outlook", isActive: true, createTaskEnabled: false },
  Flowable: { systemId: "Flowable", systemLogo: "SVGIcons.Flowable", systemName: "Native Workflow", isActive: true, createTaskEnabled: true },
  S4: { systemId: "S4", systemLogo: "SVGIcons.ECCSystem", systemName: "S4", isActive: true, createTaskEnabled: "" },
  SalesForce: { systemId: "SalesForce", systemLogo: "SVGIcons.SalesForce", systemName: "SalesForce", isActive: true, createTaskEnabled: false },
  SCP_BTP: { systemId: "SCP_BTP", systemLogo: "SVGIcons.BTPSystem", systemName: "SAP BTP Workflow Trial", isActive: true, createTaskEnabled: false },
  SCP_BTP_TRAIL_POC: { systemId: "SCP_BTP_TRAIL_POC", systemLogo: "SVGIcons.BTPSystem", systemName: "SAP BTP Workflow Trial POC", isActive: true, createTaskEnabled: false },
  SAP_Concur: { systemId: "SAP_Concur", systemLogo: "SVGIcons.SAPConcurSystem", systemName: "SAP Concur", isActive: true, createTaskEnabled: false },
  SCP: { systemId: "SCP", systemLogo: "SVGIcons.BTPSystem", systemName: "SAP Workflow Engine", isActive: true, createTaskEnabled: true },
  SF: { systemId: "SF", systemLogo: "SVGIcons.SuccessFactorSystem", systemName: "SF", isActive: true, createTaskEnabled: false },
  SuccessFactors: { systemId: "SuccessFactors", systemLogo: "SVGIcons.SuccessFactorSystem", systemName: "SuccessFactors", isActive: true, createTaskEnabled: false },
};

export const filterServiceResponse = {
  status: true,
  statusCode: 200,
  message: "Task fetched successfully!",
  data: {
    count: 106,
    pageCount: null,
    tasks: [
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:26:7e287eae-e4c0-11ed-b216-da8497fb855a",
        taskType: "testanchit6",
        taskId: "3e43757c-e4c2-11ed-99a7-d2911a106c4e",
        processId: "ea625f99-e4c1-11ed-99a7-d2911a106c4e",
        requestId: "ea625f99-e4c1-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "testanchit6",
        processDesc: "ANCHIT_DEMO:26:7e287eae-e4c0-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:26:7e287eae-e4c0-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "2455540e-a8d2-4253-a74c-77258f884b99",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930564000,
        criticalDeadline: 1682930564000,
        createdOn: 1682575844000,
        updatedOn: 1682575844000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:26:7e287eae-e4c0-11ed-b216-da8497fb855a", taskType: "testanchit6", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:26:7e287eae-e4c0-11ed-b216-da8497fb855a", taskType: "testanchit6", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:26:7e287eae-e4c0-11ed-b216-da8497fb855a", taskType: "testanchit6", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 2.5122137,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:17:86487f66-dd25-11ed-8549-ba997a741d41",
        taskType: "HR Task for generate NDA",
        taskId: "e59e82b1-dd31-11ed-91c6-52ceb58466bf",
        processId: "d97b7aea-dd25-11ed-8549-ba997a741d41",
        requestId: "d97b7aea-dd25-11ed-8549-ba997a741d41",
        referenceId: "",
        taskDesc: "HR Task for generate NDA",
        processDesc: "onboarding:17:86487f66-dd25-11ed-8549-ba997a741d41",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:17:86487f66-dd25-11ed-8549-ba997a741d41",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "36d9770d-f681-46dd-8793-e6193e89067d",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1681916989000,
        criticalDeadline: 1681916989000,
        createdOn: 1681744189000,
        updatedOn: 1681744189000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:17:86487f66-dd25-11ed-8549-ba997a741d41", taskType: "HR Task for generate NDA", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:17:86487f66-dd25-11ed-8549-ba997a741d41", taskType: "HR Task for generate NDA", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:17:86487f66-dd25-11ed-8549-ba997a741d41", taskType: "HR Task for generate NDA", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d",
        taskType: "HR for Training",
        taskId: "ae6fd1b8-e425-11ed-99a7-d2911a106c4e",
        processId: "1179a115-e007-11ed-99a7-d2911a106c4e",
        requestId: "1179a115-e007-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "HR for Training",
        processDesc: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "3fe8b866-e7f6-46d2-b5df-2118eb38cb5c",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930536000,
        criticalDeadline: 1682930536000,
        createdOn: 1682508601000,
        updatedOn: 1682508601000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d", taskType: "HR for Training", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d", taskType: "HR for Training", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d", taskType: "HR for Training", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 17.413145,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d",
        taskType: "HR for Training",
        taskId: "c43b3f5f-e1e9-11ed-99a7-d2911a106c4e",
        processId: "ea164dd4-e1d4-11ed-99a7-d2911a106c4e",
        requestId: "ea164dd4-e1d4-11ed-99a7-d2911a106c4e",
        referenceId: "_51801",
        taskDesc: "HR for Training",
        processDesc: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "3fe8b866-e7f6-46d2-b5df-2118eb38cb5c",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930545000,
        criticalDeadline: 1682930545000,
        createdOn: 1682262965000,
        updatedOn: 1682262965000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d", taskType: "HR for Training", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d", taskType: "HR for Training", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d", taskType: "HR for Training", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 47.002907,
      },
      {
        systemId: "ECC",
        systemName: "ECC",
        processName: "**********",
        taskType: "Release of purchase order",
        taskId: "000000615475",
        processId: "4500011090",
        requestId: "PT17H35M47S",
        referenceId: null,
        taskDesc: "Please release purchase order 4500011090",
        processDesc: "Please release purchase order 4500011090",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "000000615475",
        createdBy: "P000301",
        updatedBy: "<EMAIL>",
        completedBy: "P000301",
        formId: "4D613465-BEC2-40E9-9B34-76DE12E41686",
        itmStatus: "Open",
        isForwarded: 1,
        color: null,
        isPinned: 0,
        compDeadline: 1673740800000,
        criticalDeadline: 1673568000000,
        createdOn: 1673308800000,
        updatedOn: 1682482911000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "ECC", processName: "**********", taskType: "Release of purchase order", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve PO", priority: "PRIMARY", actionOrder: "RIGHT", status: "READY", isActive: 1 },
          { systemId: "ECC", processName: "**********", taskType: "Release of purchase order", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "RIGHT", status: "READY", isActive: 1 },
          { systemId: "ECC", processName: "**********", taskType: "Release of purchase order", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject PO", priority: "PRIMARY", actionOrder: "RIGHT", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "P000301",
        updatedByName: "Prince Kumar",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d",
        taskType: "Assign Manager",
        taskId: "ae6faa9e-e425-11ed-99a7-d2911a106c4e",
        processId: "1179a115-e007-11ed-99a7-d2911a106c4e",
        requestId: "1179a115-e007-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "Assign Manager",
        processDesc: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "548e4b17-7c79-43e1-bb3d-bd47d905d22c",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930535000,
        criticalDeadline: 1682930535000,
        createdOn: 1682508601000,
        updatedOn: 1682508601000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d", taskType: "Assign Manager", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d", taskType: "Assign Manager", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d", taskType: "Assign Manager", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 17.413183,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d",
        taskType: "Assign Manager",
        taskId: "c43b3f52-e1e9-11ed-99a7-d2911a106c4e",
        processId: "ea164dd4-e1d4-11ed-99a7-d2911a106c4e",
        requestId: "ea164dd4-e1d4-11ed-99a7-d2911a106c4e",
        referenceId: "_51801",
        taskDesc: "Assign Manager",
        processDesc: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "548e4b17-7c79-43e1-bb3d-bd47d905d22c",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930544000,
        criticalDeadline: 1682930544000,
        createdOn: 1682262965000,
        updatedOn: 1682262965000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d", taskType: "Assign Manager", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d", taskType: "Assign Manager", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d", taskType: "Assign Manager", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 47.002975,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:5:1277aa81-d9df-11ed-bcef-02051f9a391c",
        taskType: "HR task for generate offer letter",
        taskId: "8e46324d-d9e0-11ed-bcef-02051f9a391c",
        processId: "8e460b2b-d9e0-11ed-bcef-02051f9a391c",
        requestId: "8e460b2b-d9e0-11ed-bcef-02051f9a391c",
        referenceId: "",
        taskDesc: "HR task for generate offer letter",
        processDesc: "onboarding:5:1277aa81-d9df-11ed-bcef-02051f9a391c",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:5:1277aa81-d9df-11ed-bcef-02051f9a391c",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "5ce67f7b-d29b-4182-9571-bb2a6b4efdbb",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 1,
        compDeadline: 1681552200000,
        criticalDeadline: 1681552200000,
        createdOn: 1681379400000,
        updatedOn: 1681379400000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:5:1277aa81-d9df-11ed-bcef-02051f9a391c", taskType: "HR task for generate offer letter", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:5:1277aa81-d9df-11ed-bcef-02051f9a391c", taskType: "HR task for generate offer letter", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:5:1277aa81-d9df-11ed-bcef-02051f9a391c", taskType: "HR task for generate offer letter", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:13:ee248715-e419-11ed-b216-da8497fb855a",
        taskType: "HXMOnboardingEmployeetaskforRelocationV1",
        taskId: "927a6274-e41a-11ed-99a7-d2911a106c4e",
        processId: "927a3b59-e41a-11ed-99a7-d2911a106c4e",
        requestId: "927a3b59-e41a-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "HXMOnboardingEmployeetaskforRelocationV1",
        processDesc: "ANCHIT_DEMO:13:ee248715-e419-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:13:ee248715-e419-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "660ae016-9d6e-465a-b58a-cad145c56279",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930531000,
        criticalDeadline: 1682930531000,
        createdOn: 1682503830000,
        updatedOn: 1682503830000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:13:ee248715-e419-11ed-b216-da8497fb855a", taskType: "HXMOnboardingEmployeetaskforRelocationV1", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:13:ee248715-e419-11ed-b216-da8497fb855a", taskType: "HXMOnboardingEmployeetaskforRelocationV1", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:13:ee248715-e419-11ed-b216-da8497fb855a", taskType: "HXMOnboardingEmployeetaskforRelocationV1", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 18.29938,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:7:4b66f7b2-e402-11ed-b216-da8497fb855a",
        taskType: "testing2",
        taskId: "968e001e-e423-11ed-99a7-d2911a106c4e",
        processId: "bcf3c58b-e412-11ed-99a7-d2911a106c4e",
        requestId: "bcf3c58b-e412-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "testing2",
        processDesc: "ANCHIT_DEMO:7:4b66f7b2-e402-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:7:4b66f7b2-e402-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "6c5868a2-4449-490a-8f7b-dab476175aa8",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930531000,
        criticalDeadline: 1682930531000,
        createdOn: 1682507702000,
        updatedOn: 1682507702000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:7:4b66f7b2-e402-11ed-b216-da8497fb855a", taskType: "testing2", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:7:4b66f7b2-e402-11ed-b216-da8497fb855a", taskType: "testing2", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:7:4b66f7b2-e402-11ed-b216-da8497fb855a", taskType: "testing2", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 17.581766,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "Quick_demo:1:3fc862a2-d89f-11ed-bcef-02051f9a391c",
        taskType: "quick",
        taskId: "00e78423-d8eb-11ed-914d-6244377b1ba4",
        processId: "00e75d09-d8eb-11ed-914d-6244377b1ba4",
        requestId: "00e75d09-d8eb-11ed-914d-6244377b1ba4",
        referenceId: null,
        taskDesc: "quick",
        processDesc: "Quick_demo:1:3fc862a2-d89f-11ed-bcef-02051f9a391c",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Quick_demo:1:3fc862a2-d89f-11ed-bcef-02051f9a391c",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "7008A14A-49C8-400A-8D89-807177D71C2D",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930547000,
        criticalDeadline: 1682930547000,
        createdOn: 1681273936000,
        updatedOn: 1681273936000,
        completedAt: null,
        actions: null,
        taskNature: "Single-User",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 78.303055,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "Quick_demo:1:3fc862a2-d89f-11ed-bcef-02051f9a391c",
        taskType: "quick",
        taskId: "db74b956-d8ea-11ed-914d-6244377b1ba4",
        processId: "db73f5fc-d8ea-11ed-914d-6244377b1ba4",
        requestId: "db73f5fc-d8ea-11ed-914d-6244377b1ba4",
        referenceId: null,
        taskDesc: "quick",
        processDesc: "Quick_demo:1:3fc862a2-d89f-11ed-bcef-02051f9a391c",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Quick_demo:1:3fc862a2-d89f-11ed-bcef-02051f9a391c",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "7008A14A-49C8-400A-8D89-807177D71C2D",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930552000,
        criticalDeadline: 1682930552000,
        createdOn: 1681273873000,
        updatedOn: 1681273873000,
        completedAt: null,
        actions: null,
        taskNature: "Single-User",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 78.303635,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ToDoTask:3:f4610153-e010-11ed-9f6f-be5cc965649d",
        taskType: "To Do",
        taskId: "2f35a26d-e021-11ed-99a7-d2911a106c4e",
        processId: "2f357b53-e021-11ed-99a7-d2911a106c4e",
        requestId: "2f357b53-e021-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "To Do",
        processDesc: "ToDoTask:3:f4610153-e010-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ToDoTask:3:f4610153-e010-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "748fa830-0526-46cc-ae64-a6425bc9ec29",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682239665000,
        criticalDeadline: 1682239665000,
        createdOn: 1682066865000,
        updatedOn: 1682066865000,
        completedAt: null,
        actions: null,
        taskNature: "Single-User",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ToDoTask:3:f4610153-e010-11ed-9f6f-be5cc965649d",
        taskType: "To Do",
        taskId: "3d1cb289-e011-11ed-99a7-d2911a106c4e",
        processId: "3d1c8b6f-e011-11ed-99a7-d2911a106c4e",
        requestId: "3d1c8b6f-e011-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "To Do",
        processDesc: "ToDoTask:3:f4610153-e010-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ToDoTask:3:f4610153-e010-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "748fa830-0526-46cc-ae64-a6425bc9ec29",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682232816000,
        criticalDeadline: 1682232816000,
        createdOn: 1682060016000,
        updatedOn: 1682060016000,
        completedAt: null,
        actions: null,
        taskNature: "Single-User",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "Charan K",
        updatedByName: "Charan K",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ToDoTask:3:f4610153-e010-11ed-9f6f-be5cc965649d",
        taskType: "To Do",
        taskId: "f4938dbe-e011-11ed-99a7-d2911a106c4e",
        processId: "f49366a4-e011-11ed-99a7-d2911a106c4e",
        requestId: "f49366a4-e011-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "To Do",
        processDesc: "ToDoTask:3:f4610153-e010-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ToDoTask:3:f4610153-e010-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "748fa830-0526-46cc-ae64-a6425bc9ec29",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682233124000,
        criticalDeadline: 1682233124000,
        createdOn: 1682060324000,
        updatedOn: 1682060324000,
        completedAt: null,
        actions: null,
        taskNature: "Single-User",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "Quick_demo:2:5f40236a-d8f1-11ed-bcef-02051f9a391c",
        taskType: "quick",
        taskId: "47a407e5-d9ba-11ed-91c6-52ceb58466bf",
        processId: "47a3e0cb-d9ba-11ed-91c6-52ceb58466bf",
        requestId: "47a3e0cb-d9ba-11ed-91c6-52ceb58466bf",
        referenceId: null,
        taskDesc: "quick",
        processDesc: "Quick_demo:2:5f40236a-d8f1-11ed-bcef-02051f9a391c",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Quick_demo:2:5f40236a-d8f1-11ed-bcef-02051f9a391c",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "74AF0D7C-1ED5-4237-8734-4E9806CFC6EA",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930529000,
        criticalDeadline: 1682930529000,
        createdOn: 1681362961000,
        updatedOn: 1681362961000,
        completedAt: null,
        actions: null,
        taskNature: "Single-User",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 77.085724,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "Quick_demo:2:5f40236a-d8f1-11ed-bcef-02051f9a391c",
        taskType: "quick",
        taskId: "b0356370-d8f1-11ed-914d-6244377b1ba4",
        processId: "b0351546-d8f1-11ed-914d-6244377b1ba4",
        requestId: "b0351546-d8f1-11ed-914d-6244377b1ba4",
        referenceId: null,
        taskDesc: "quick",
        processDesc: "Quick_demo:2:5f40236a-d8f1-11ed-bcef-02051f9a391c",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Quick_demo:2:5f40236a-d8f1-11ed-bcef-02051f9a391c",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "74AF0D7C-1ED5-4237-8734-4E9806CFC6EA",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930537000,
        criticalDeadline: 1682930537000,
        createdOn: 1681276807000,
        updatedOn: 1681276807000,
        completedAt: null,
        actions: null,
        taskNature: "Single-User",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 78.26626,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:15:c745f06c-e41c-11ed-b216-da8497fb855a",
        taskType: "abcdefgh",
        taskId: "09fa3354-e41d-11ed-99a7-d2911a106c4e",
        processId: "09fa0d39-e41d-11ed-99a7-d2911a106c4e",
        requestId: "09fa0d39-e41d-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "abcdefgh",
        processDesc: "ANCHIT_DEMO:15:c745f06c-e41c-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:15:c745f06c-e41c-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "7694158f-a057-4a4a-8d78-e07d6d3c9d04",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930551000,
        criticalDeadline: 1682930551000,
        createdOn: 1682504889000,
        updatedOn: 1682504889000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:15:c745f06c-e41c-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:15:c745f06c-e41c-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:15:c745f06c-e41c-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 18.103537,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:15:c745f06c-e41c-11ed-b216-da8497fb855a",
        taskType: "abcdefgh",
        taskId: "f1334716-e41c-11ed-99a7-d2911a106c4e",
        processId: "f1331ffb-e41c-11ed-99a7-d2911a106c4e",
        requestId: "f1331ffb-e41c-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "abcdefgh",
        processDesc: "ANCHIT_DEMO:15:c745f06c-e41c-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:15:c745f06c-e41c-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "7694158f-a057-4a4a-8d78-e07d6d3c9d04",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930556000,
        criticalDeadline: 1682930556000,
        createdOn: 1682504848000,
        updatedOn: 1682504848000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:15:c745f06c-e41c-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:15:c745f06c-e41c-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:15:c745f06c-e41c-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 18.1109,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:26:1d61d16e-df79-11ed-9f6f-be5cc965649d",
        taskType: "HR for Training",
        taskId: "1d7ce742-e1d2-11ed-9f6f-be5cc965649d",
        processId: "b911a0bd-e002-11ed-99a7-d2911a106c4e",
        requestId: "b911a0bd-e002-11ed-99a7-d2911a106c4e",
        referenceId: "_49554",
        taskDesc: "HR for Training",
        processDesc: "onboarding:26:1d61d16e-df79-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:26:1d61d16e-df79-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "7bf1455c-e6c0-4a8a-9a51-6e1dfb8857ed",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930556000,
        criticalDeadline: 1682930556000,
        createdOn: 1682252807000,
        updatedOn: 1682252807000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:26:1d61d16e-df79-11ed-9f6f-be5cc965649d", taskType: "HR for Training", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:26:1d61d16e-df79-11ed-9f6f-be5cc965649d", taskType: "HR for Training", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:26:1d61d16e-df79-11ed-9f6f-be5cc965649d", taskType: "HR for Training", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 47.775925,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:26:1d61d16e-df79-11ed-9f6f-be5cc965649d",
        taskType: "HR for Training",
        taskId: "bfb9c425-e03e-11ed-99a7-d2911a106c4e",
        processId: "82ad8f8e-df7a-11ed-99a7-d2911a106c4e",
        requestId: "82ad8f8e-df7a-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "HR for Training",
        processDesc: "onboarding:26:1d61d16e-df79-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:26:1d61d16e-df79-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "7bf1455c-e6c0-4a8a-9a51-6e1dfb8857ed",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930542000,
        criticalDeadline: 1682930542000,
        createdOn: 1682079563000,
        updatedOn: 1682079563000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:26:1d61d16e-df79-11ed-9f6f-be5cc965649d", taskType: "HR for Training", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:26:1d61d16e-df79-11ed-9f6f-be5cc965649d", taskType: "HR for Training", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:26:1d61d16e-df79-11ed-9f6f-be5cc965649d", taskType: "HR for Training", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "System User",
        updatedByName: "System User",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 58.188343,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:8:4be9d38b-e413-11ed-b216-da8497fb855a",
        taskType: "testing2",
        taskId: "dae7a909-e413-11ed-99a7-d2911a106c4e",
        processId: "9ea5c939-e413-11ed-99a7-d2911a106c4e",
        requestId: "9ea5c939-e413-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "testing2",
        processDesc: "ANCHIT_DEMO:8:4be9d38b-e413-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:8:4be9d38b-e413-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "92f66d55-1508-42be-ab81-fd8d692f6ad8",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930551000,
        criticalDeadline: 1682930551000,
        createdOn: 1682500945000,
        updatedOn: 1682500945000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:8:4be9d38b-e413-11ed-b216-da8497fb855a", taskType: "testing2", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:8:4be9d38b-e413-11ed-b216-da8497fb855a", taskType: "testing2", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:8:4be9d38b-e413-11ed-b216-da8497fb855a", taskType: "testing2", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 18.825153,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:23:6b89b7ae-e4be-11ed-b216-da8497fb855a",
        taskType: "abcdefgh2",
        taskId: "d1d68752-e4be-11ed-99a7-d2911a106c4e",
        processId: "93be13c7-e4be-11ed-99a7-d2911a106c4e",
        requestId: "93be13c7-e4be-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "abcdefgh2",
        processDesc: "ANCHIT_DEMO:23:6b89b7ae-e4be-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:23:6b89b7ae-e4be-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "9887842b-4087-4374-b368-d61141b9c3af",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930548000,
        criticalDeadline: 1682930548000,
        createdOn: 1682574373000,
        updatedOn: 1682574373000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:23:6b89b7ae-e4be-11ed-b216-da8497fb855a", taskType: "abcdefgh2", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:23:6b89b7ae-e4be-11ed-b216-da8497fb855a", taskType: "abcdefgh2", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:23:6b89b7ae-e4be-11ed-b216-da8497fb855a", taskType: "abcdefgh2", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 2.8955762,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "leave_app_demo:4:9c38cc2d-dd0b-11ed-8549-ba997a741d41",
        taskType: "Approver1",
        taskId: "d122a254-dd0b-11ed-91c6-52ceb58466bf",
        processId: "d1227b37-dd0b-11ed-91c6-52ceb58466bf",
        requestId: "d1227b37-dd0b-11ed-91c6-52ceb58466bf",
        referenceId: null,
        taskDesc: "Approver1",
        processDesc: "leave_app_demo:4:9c38cc2d-dd0b-11ed-8549-ba997a741d41",
        businessStatus: "READY",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "leave_app_demo:4:9c38cc2d-dd0b-11ed-8549-ba997a741d41",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "9966c221-1dac-4fd7-8a02-d18ea0b39707",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682419034000,
        criticalDeadline: 1682419034000,
        createdOn: 1681727834000,
        updatedOn: 1681727834000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "Flowable", processName: "leave_app_demo:4:9c38cc2d-dd0b-11ed-8549-ba997a741d41", taskType: "Approver1", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "Flowable", processName: "leave_app_demo:4:9c38cc2d-dd0b-11ed-8549-ba997a741d41", taskType: "Approver1", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "Flowable", processName: "leave_app_demo:4:9c38cc2d-dd0b-11ed-8549-ba997a741d41", taskType: "Approver1", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
          { systemId: "Flowable", processName: "leave_app_demo:4:9c38cc2d-dd0b-11ed-8549-ba997a741d41", taskType: "Approver1", action: "RELEASE", icon: "SVGIcons.ReleaseIcon", hoverIcon: "", label: "Release", priority: "SECONDARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:17:e9df4a8d-e41e-11ed-b216-da8497fb855a",
        taskType: "abcdefgh2",
        taskId: "07501747-e427-11ed-99a7-d2911a106c4e",
        processId: "e2a34277-e420-11ed-99a7-d2911a106c4e",
        requestId: "e2a34277-e420-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "abcdefgh2",
        processDesc: "ANCHIT_DEMO:17:e9df4a8d-e41e-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:17:e9df4a8d-e41e-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "a0348017-0872-4097-a596-0ed3dc8ec40f",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930550000,
        criticalDeadline: 1682930550000,
        createdOn: 1682509180000,
        updatedOn: 1682509180000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:17:e9df4a8d-e41e-11ed-b216-da8497fb855a", taskType: "abcdefgh2", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:17:e9df4a8d-e41e-11ed-b216-da8497fb855a", taskType: "abcdefgh2", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:17:e9df4a8d-e41e-11ed-b216-da8497fb855a", taskType: "abcdefgh2", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 17.303759,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:24:fba09e5a-e4be-11ed-b216-da8497fb855a",
        taskType: "testanchit3",
        taskId: "2b9d035f-e4bf-11ed-99a7-d2911a106c4e",
        processId: "2b9cdc44-e4bf-11ed-99a7-d2911a106c4e",
        requestId: "2b9cdc44-e4bf-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "testanchit3",
        processDesc: "ANCHIT_DEMO:24:fba09e5a-e4be-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:24:fba09e5a-e4be-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "a1e9a82b-85f7-46be-b88c-41cd5e00569b",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930560000,
        criticalDeadline: 1682930560000,
        createdOn: 1682574524000,
        updatedOn: 1682574524000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:24:fba09e5a-e4be-11ed-b216-da8497fb855a", taskType: "testanchit3", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:24:fba09e5a-e4be-11ed-b216-da8497fb855a", taskType: "testanchit3", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:24:fba09e5a-e4be-11ed-b216-da8497fb855a", taskType: "testanchit3", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 2.856282,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:13:ee248715-e419-11ed-b216-da8497fb855a",
        taskType: "testing2",
        taskId: "6ea9fcff-e41a-11ed-99a7-d2911a106c4e",
        processId: "24b3a9ee-e41a-11ed-99a7-d2911a106c4e",
        requestId: "24b3a9ee-e41a-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "testing2",
        processDesc: "ANCHIT_DEMO:13:ee248715-e419-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:13:ee248715-e419-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "b2a4f3fb-bf68-4222-9dc8-e277df6ac04b",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930541000,
        criticalDeadline: 1682930541000,
        createdOn: 1682503770000,
        updatedOn: 1682503770000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:13:ee248715-e419-11ed-b216-da8497fb855a", taskType: "testing2", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:13:ee248715-e419-11ed-b216-da8497fb855a", taskType: "testing2", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:13:ee248715-e419-11ed-b216-da8497fb855a", taskType: "testing2", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 18.309988,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:11:8f3b4ed1-e418-11ed-b216-da8497fb855a",
        taskType: "testing1",
        taskId: "bccd91b5-e418-11ed-99a7-d2911a106c4e",
        processId: "bccd6a9a-e418-11ed-99a7-d2911a106c4e",
        requestId: "bccd6a9a-e418-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "testing1",
        processDesc: "ANCHIT_DEMO:11:8f3b4ed1-e418-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:11:8f3b4ed1-e418-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "b6baf5bb-4449-4669-9d94-f0b4773f3ec2",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930541000,
        criticalDeadline: 1682930541000,
        createdOn: 1682503042000,
        updatedOn: 1682503042000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:11:8f3b4ed1-e418-11ed-b216-da8497fb855a", taskType: "testing1", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:11:8f3b4ed1-e418-11ed-b216-da8497fb855a", taskType: "testing1", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:11:8f3b4ed1-e418-11ed-b216-da8497fb855a", taskType: "testing1", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 18.44348,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "Onboarding_test:1:41afb1e3-df59-11ed-9f6f-be5cc965649d",
        taskType: "HR Task for generate NDA",
        taskId: "8bdee88e-df5c-11ed-9f6f-be5cc965649d",
        processId: "e5eabc56-df5a-11ed-99a7-d2911a106c4e",
        requestId: "e5eabc56-df5a-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "HR Task for generate NDA",
        processDesc: "Onboarding_test:1:41afb1e3-df59-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Onboarding_test:1:41afb1e3-df59-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "bba0a277-17c2-4b54-9dcd-a9979b17a0ca",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682155209000,
        criticalDeadline: 1682155209000,
        createdOn: 1681982409000,
        updatedOn: 1681982409000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "Onboarding_test:1:41afb1e3-df59-11ed-9f6f-be5cc965649d", taskType: "HR Task for generate NDA", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "Onboarding_test:1:41afb1e3-df59-11ed-9f6f-be5cc965649d", taskType: "HR Task for generate NDA", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "Onboarding_test:1:41afb1e3-df59-11ed-9f6f-be5cc965649d", taskType: "HR Task for generate NDA", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:23:670634f8-df49-11ed-9f6f-be5cc965649d",
        taskType: "IT Admin",
        taskId: "21a73def-df66-11ed-99a7-d2911a106c4e",
        processId: "bb745271-df4a-11ed-99a7-d2911a106c4e",
        requestId: "bb745271-df4a-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "IT Admin",
        processDesc: "onboarding:23:670634f8-df49-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:23:670634f8-df49-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "be3fe55a-87aa-412d-8a6b-3548e9c173cb",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930558000,
        criticalDeadline: 1682930558000,
        createdOn: 1681986526000,
        updatedOn: 1681986526000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:23:670634f8-df49-11ed-9f6f-be5cc965649d", taskType: "IT Admin", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:23:670634f8-df49-11ed-9f6f-be5cc965649d", taskType: "IT Admin", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:23:670634f8-df49-11ed-9f6f-be5cc965649d", taskType: "IT Admin", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "System User",
        updatedByName: "System User",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 62.230938,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:24:ef5dc35b-df5f-11ed-9f6f-be5cc965649d",
        taskType: "HR task for generate offer letter",
        taskId: "840b737b-df64-11ed-99a7-d2911a106c4e",
        processId: "840b4c56-df64-11ed-99a7-d2911a106c4e",
        requestId: "840b4c56-df64-11ed-99a7-d2911a106c4e",
        referenceId: "_46745",
        taskDesc: "HR task for generate offer letter",
        processDesc: "onboarding:24:ef5dc35b-df5f-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:24:ef5dc35b-df5f-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "BFDA675F-7EBF-40CC-82DA-DF600737C69A",
        itmStatus: "Open",
        isForwarded: 0,
        color: "#089B13",
        isPinned: 0,
        compDeadline: 1682158632000,
        criticalDeadline: 1682158632000,
        createdOn: 1681985832000,
        updatedOn: 1681985832000,
        completedAt: 1681985955000,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:24:ef5dc35b-df5f-11ed-9f6f-be5cc965649d", taskType: "HR task for generate offer letter", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:24:ef5dc35b-df5f-11ed-9f6f-be5cc965649d", taskType: "HR task for generate offer letter", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:24:ef5dc35b-df5f-11ed-9f6f-be5cc965649d", taskType: "HR task for generate offer letter", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "System User",
        updatedByName: "System User",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:18:b7151a07-dd32-11ed-8549-ba997a741d41",
        taskType: "HR Task for generate NDA",
        taskId: "330130e4-dd33-11ed-8549-ba997a741d41",
        processId: "d524df62-dd32-11ed-8549-ba997a741d41",
        requestId: "d524df62-dd32-11ed-8549-ba997a741d41",
        referenceId: null,
        taskDesc: "HR Task for generate NDA",
        processDesc: "onboarding:18:b7151a07-dd32-11ed-8549-ba997a741d41",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:18:b7151a07-dd32-11ed-8549-ba997a741d41",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "c1dac6e0-bd95-4ace-a8e3-084ee98b884f",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1681917549000,
        criticalDeadline: 1681917549000,
        createdOn: 1681744749000,
        updatedOn: 1681744749000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:18:b7151a07-dd32-11ed-8549-ba997a741d41", taskType: "HR Task for generate NDA", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:18:b7151a07-dd32-11ed-8549-ba997a741d41", taskType: "HR Task for generate NDA", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:18:b7151a07-dd32-11ed-8549-ba997a741d41", taskType: "HR Task for generate NDA", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:23:670634f8-df49-11ed-9f6f-be5cc965649d",
        taskType: "Assign Manager",
        taskId: "21a73dec-df66-11ed-99a7-d2911a106c4e",
        processId: "bb745271-df4a-11ed-99a7-d2911a106c4e",
        requestId: "bb745271-df4a-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "Assign Manager",
        processDesc: "onboarding:23:670634f8-df49-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:23:670634f8-df49-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "c475f332-12d5-4416-94f3-e7b159edf916",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930557000,
        criticalDeadline: 1682930557000,
        createdOn: 1681986526000,
        updatedOn: 1681986526000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:23:670634f8-df49-11ed-9f6f-be5cc965649d", taskType: "Assign Manager", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:23:670634f8-df49-11ed-9f6f-be5cc965649d", taskType: "Assign Manager", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:23:670634f8-df49-11ed-9f6f-be5cc965649d", taskType: "Assign Manager", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "System User",
        updatedByName: "System User",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 62.231003,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:6:60b8b06e-e401-11ed-b216-da8497fb855a",
        taskType: "testing1",
        taskId: "8d855960-e401-11ed-99a7-d2911a106c4e",
        processId: "8d855955-e401-11ed-99a7-d2911a106c4e",
        requestId: "8d855955-e401-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "testing1",
        processDesc: "ANCHIT_DEMO:6:60b8b06e-e401-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:6:60b8b06e-e401-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "c49ccedf-e359-453e-ae8d-839693f1f114",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930529000,
        criticalDeadline: 1682930529000,
        createdOn: 1682493084000,
        updatedOn: 1682493084000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:6:60b8b06e-e401-11ed-b216-da8497fb855a", taskType: "testing1", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:6:60b8b06e-e401-11ed-b216-da8497fb855a", taskType: "testing1", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:6:60b8b06e-e401-11ed-b216-da8497fb855a", taskType: "testing1", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 20.227144,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "leave_app_demo:3:61d422aa-dd09-11ed-8549-ba997a741d41",
        taskType: "Approver1",
        taskId: "a16a5df5-dd09-11ed-91c6-52ceb58466bf",
        processId: "a16a36d8-dd09-11ed-91c6-52ceb58466bf",
        requestId: "a16a36d8-dd09-11ed-91c6-52ceb58466bf",
        referenceId: null,
        taskDesc: "Approver1",
        processDesc: "leave_app_demo:3:61d422aa-dd09-11ed-8549-ba997a741d41",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "leave_app_demo:3:61d422aa-dd09-11ed-8549-ba997a741d41",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "c5e1121a-7a60-49c8-a6c0-ee5f93e48042",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682418095000,
        criticalDeadline: 1682418095000,
        createdOn: 1681726895000,
        updatedOn: 1681726895000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "Flowable", processName: "leave_app_demo:3:61d422aa-dd09-11ed-8549-ba997a741d41", taskType: "Approver1", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "leave_app_demo:3:61d422aa-dd09-11ed-8549-ba997a741d41", taskType: "Approver1", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "leave_app_demo:3:61d422aa-dd09-11ed-8549-ba997a741d41",
        taskType: "Approver1",
        taskId: "e6d5dc04-dd0a-11ed-91c6-52ceb58466bf",
        processId: "e6d5dbf7-dd0a-11ed-91c6-52ceb58466bf",
        requestId: "e6d5dbf7-dd0a-11ed-91c6-52ceb58466bf",
        referenceId: "NWF_38806",
        taskDesc: "Approver1",
        processDesc: "leave_app_demo:3:61d422aa-dd09-11ed-8549-ba997a741d41",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "leave_app_demo:3:61d422aa-dd09-11ed-8549-ba997a741d41",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "c5e1121a-7a60-49c8-a6c0-ee5f93e48042",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682418641000,
        criticalDeadline: 1682418641000,
        createdOn: 1681727441000,
        updatedOn: 1681727441000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "Flowable", processName: "leave_app_demo:3:61d422aa-dd09-11ed-8549-ba997a741d41", taskType: "Approver1", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "leave_app_demo:3:61d422aa-dd09-11ed-8549-ba997a741d41", taskType: "Approver1", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d",
        taskType: "IT Admin",
        taskId: "ae6faaa1-e425-11ed-99a7-d2911a106c4e",
        processId: "1179a115-e007-11ed-99a7-d2911a106c4e",
        requestId: "1179a115-e007-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "IT Admin",
        processDesc: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "c740030e-62c6-43e6-8166-cd87409dc640",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930535000,
        criticalDeadline: 1682930535000,
        createdOn: 1682508601000,
        updatedOn: 1682508601000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d", taskType: "IT Admin", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d", taskType: "IT Admin", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:27:9d27571b-e006-11ed-9f6f-be5cc965649d", taskType: "IT Admin", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 17.413183,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ToDoTask:2:23d30207-d85e-11ed-bcef-02051f9a391c",
        taskType: "To Do Task",
        taskId: "8c4dd609-d85e-11ed-914d-6244377b1ba4",
        processId: "8c4857be-d85e-11ed-914d-6244377b1ba4",
        requestId: "8c4857be-d85e-11ed-914d-6244377b1ba4",
        referenceId: null,
        taskDesc: "To Do Task",
        processDesc: "ToDoTask:2:23d30207-d85e-11ed-bcef-02051f9a391c",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ToDoTask:2:23d30207-d85e-11ed-bcef-02051f9a391c",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "c98b7f62-595a-4e94-9d04-3d4373473d73",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1681645611000,
        criticalDeadline: 1681645611000,
        createdOn: 1681213611000,
        updatedOn: 1681213611000,
        completedAt: null,
        actions: null,
        taskNature: "Single-User",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:3:55c4e480-e293-11ed-9f6f-be5cc965649d",
        taskType: "testing1",
        taskId: "934b2b0a-e293-11ed-99a7-d2911a106c4e",
        processId: "934b2aff-e293-11ed-99a7-d2911a106c4e",
        requestId: "934b2aff-e293-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "testing1",
        processDesc: "ANCHIT_DEMO:3:55c4e480-e293-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:3:55c4e480-e293-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "cdce025f-762a-41cd-adfe-47e68e9e5cf2",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930531000,
        criticalDeadline: 1682930531000,
        createdOn: 1682335898000,
        updatedOn: 1682335898000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:3:55c4e480-e293-11ed-9f6f-be5cc965649d", taskType: "testing1", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:3:55c4e480-e293-11ed-9f6f-be5cc965649d", taskType: "testing1", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:3:55c4e480-e293-11ed-9f6f-be5cc965649d", taskType: "testing1", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 40.69476,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:3:55c4e480-e293-11ed-9f6f-be5cc965649d",
        taskType: "testing1",
        taskId: "d2673624-e294-11ed-99a7-d2911a106c4e",
        processId: "d2673619-e294-11ed-99a7-d2911a106c4e",
        requestId: "d2673619-e294-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "testing1",
        processDesc: "ANCHIT_DEMO:3:55c4e480-e293-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:3:55c4e480-e293-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "cdce025f-762a-41cd-adfe-47e68e9e5cf2",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930549000,
        criticalDeadline: 1682930549000,
        createdOn: 1682336433000,
        updatedOn: 1682336433000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:3:55c4e480-e293-11ed-9f6f-be5cc965649d", taskType: "testing1", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:3:55c4e480-e293-11ed-9f6f-be5cc965649d", taskType: "testing1", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:3:55c4e480-e293-11ed-9f6f-be5cc965649d", taskType: "testing1", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 40.64173,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:22:c82028cd-ddf5-11ed-9f6f-be5cc965649d",
        taskType: "HR task for convert employee to Joining employee",
        taskId: "fdd81f1f-df3a-11ed-99a7-d2911a106c4e",
        processId: "e034595f-de8f-11ed-99a7-d2911a106c4e",
        requestId: "e034595f-de8f-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "HR task for convert employee to Joining employee",
        processDesc: "onboarding:22:c82028cd-ddf5-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:22:c82028cd-ddf5-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "d8a6501b-78e7-44a8-ab1b-feff11204f4c",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930560000,
        criticalDeadline: 1682930560000,
        createdOn: 1681967998000,
        updatedOn: 1681967998000,
        completedAt: null,
        actions: null,
        taskNature: "Single-User",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "System User",
        updatedByName: "System User",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 62.944473,
      },
      {
        systemId: "DocuSign",
        systemName: "DocuSign",
        processName: "ENVELOPE",
        taskType: "Sign Document",
        taskId: "3908489b-ddb3-4aea-9d96-53f6dce5a796",
        processId: "da9c7fab-69f2-4890-a972-85304bebba67",
        requestId: "da9c7fab-69f2-4890-a972-85304bebba67",
        referenceId: "DE_29768",
        taskDesc: "Complete with DocuSign: Mobile Testing.docx",
        processDesc: "Sign Document",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Complete with DocuSign: Mobile Testing.docx",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "DF33D66D-1A9C-4E8C-8A48-6F84032EA108",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1681026027000,
        criticalDeadline: 1680248427000,
        createdOn: 1680248427000,
        updatedOn: 1680255243000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "Preetham m",
        updatedByName: "Preetham m",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "DocuSign",
        systemName: "DocuSign",
        processName: "ENVELOPE",
        taskType: "Sign Document",
        taskId: "4778ad95-83c5-4e95-b60a-0c6dd667e9a0",
        processId: "9630e732-bfd0-473c-b26b-1470c186f1a2",
        requestId: "9630e732-bfd0-473c-b26b-1470c186f1a2",
        referenceId: "DE_29300",
        taskDesc: "Complete with DocuSign: Creation of PR.pdf",
        processDesc: "Sign Document",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Complete with DocuSign: Creation of PR.pdf",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "DF33D66D-1A9C-4E8C-8A48-6F84032EA108",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1680958415000,
        criticalDeadline: 1680180815000,
        createdOn: 1680180815000,
        updatedOn: 1680175887000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "Preetham m",
        updatedByName: "Preetham m",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "DocuSign",
        systemName: "DocuSign",
        processName: "ENVELOPE",
        taskType: "Sign Document",
        taskId: "59ccf003-ea5a-4644-a049-5064223475bf",
        processId: "0c66a487-f016-4f6a-b361-df1c2361074a",
        requestId: "0c66a487-f016-4f6a-b361-df1c2361074a",
        referenceId: "DE_29301",
        taskDesc: "Complete with DocuSign: Untitled (1).png",
        processDesc: "Sign Document",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Complete with DocuSign: Untitled (1).png",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "DF33D66D-1A9C-4E8C-8A48-6F84032EA108",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1680956930000,
        criticalDeadline: 1680179330000,
        createdOn: 1680179330000,
        updatedOn: 1680177874000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "Preetham m",
        updatedByName: "Preetham m",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "DocuSign",
        systemName: "DocuSign",
        processName: "ENVELOPE",
        taskType: "Sign Document",
        taskId: "63446fe0-d592-4ea5-98fb-dbb24b2bc0f8",
        processId: "1b5e16f3-f1d4-437a-836c-9a61029dd3c6",
        requestId: "1b5e16f3-f1d4-437a-836c-9a61029dd3c6",
        referenceId: "DE_34129",
        taskDesc: "Complete with DocuSign: ECC Task id in web.png",
        processDesc: "Sign Document",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Complete with DocuSign: ECC Task id in web.png",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "DF33D66D-1A9C-4E8C-8A48-6F84032EA108",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1681884669000,
        criticalDeadline: 1681107069000,
        createdOn: 1681107069000,
        updatedOn: 1681114088000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "Preetham m",
        updatedByName: "Preetham m",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "DocuSign",
        systemName: "DocuSign",
        processName: "ENVELOPE",
        taskType: "Sign Document",
        taskId: "648fa059-6cc6-45da-808c-0d466e53600c",
        processId: "7fcecd7d-0514-47e7-b3ee-42d3be50b069",
        requestId: "7fcecd7d-0514-47e7-b3ee-42d3be50b069",
        referenceId: "DE_28551",
        taskDesc: "Please sign the NDA",
        processDesc: "Sign Document",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Please sign the NDA",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "DF33D66D-1A9C-4E8C-8A48-6F84032EA108",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1680160454000,
        criticalDeadline: 1679382854000,
        createdOn: 1679382854000,
        updatedOn: 1679644478000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "DocuSign",
        systemName: "DocuSign",
        processName: "ENVELOPE",
        taskType: "Sign Document",
        taskId: "7fdc1b8b-a747-4e72-9734-ea0362d6857d",
        processId: "af9e01cf-8a5d-4a56-869c-a69666fd09a4",
        requestId: "af9e01cf-8a5d-4a56-869c-a69666fd09a4",
        referenceId: "DE_29946",
        taskDesc: "Syed Test",
        processDesc: "Sign Document",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Syed Test",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "DF33D66D-1A9C-4E8C-8A48-6F84032EA108",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1681038317000,
        criticalDeadline: 1680260717000,
        createdOn: 1680260717000,
        updatedOn: 1680260347000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "Preetham m",
        updatedByName: "Preetham m",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "DocuSign",
        systemName: "DocuSign",
        processName: "ENVELOPE",
        taskType: "Sign Document",
        taskId: "846113d3-b2d7-421f-8441-73989b7ed23b",
        processId: "c9add7b9-179e-4a3d-8849-737e589f7a7b",
        requestId: "c9add7b9-179e-4a3d-8849-737e589f7a7b",
        referenceId: "DE_29104",
        taskDesc: "Please sign the Offer Letter",
        processDesc: "Sign Document",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Please sign the Offer Letter",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "DF33D66D-1A9C-4E8C-8A48-6F84032EA108",
        itmStatus: null,
        isForwarded: 1,
        color: null,
        isPinned: 0,
        compDeadline: 1680767661000,
        criticalDeadline: 1679990061000,
        createdOn: 1679990061000,
        updatedOn: 1679990231000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "DocuSign",
        systemName: "DocuSign",
        processName: "ENVELOPE",
        taskType: "Sign Document",
        taskId: "8b830fb6-d0ff-4681-93c0-ac3448b81948",
        processId: "a209ed66-7587-40b1-9466-678834202248",
        requestId: "a209ed66-7587-40b1-9466-678834202248",
        referenceId: "DE_29261",
        taskDesc: "Complete with DocuSign: Planned Deliverables- Sprint 19 (2023) (1).csv",
        processDesc: "Sign Document",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Complete with DocuSign: Planned Deliverables- Sprint 19 (2023) (1).csv",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "DF33D66D-1A9C-4E8C-8A48-6F84032EA108",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1680854163000,
        criticalDeadline: 1680076563000,
        createdOn: 1680076563000,
        updatedOn: 1680079932000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "Prince Kumar",
        updatedByName: "Prince Kumar",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "DocuSign",
        systemName: "DocuSign",
        processName: "ENVELOPE",
        taskType: "Sign Document",
        taskId: "90f3d829-5394-4094-bf3b-933d8d5ed6cd",
        processId: "6d1a5269-72e7-484c-822c-f8438331bbe5",
        requestId: "6d1a5269-72e7-484c-822c-f8438331bbe5",
        referenceId: "DE_29260",
        taskDesc: "Complete with DocuSign: file-sample_150kB.pdf",
        processDesc: "Sign Document",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Complete with DocuSign: file-sample_150kB.pdf",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "DF33D66D-1A9C-4E8C-8A48-6F84032EA108",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1680855760000,
        criticalDeadline: 1680078160000,
        createdOn: 1680078160000,
        updatedOn: 1680074865000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "Prince Kumar",
        updatedByName: "Prince Kumar",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "DocuSign",
        systemName: "DocuSign",
        processName: "ENVELOPE",
        taskType: "Sign Document",
        taskId: "97680b44-bffe-4aa2-b985-d02d814594cb",
        processId: "ec6aef11-8b53-47ef-ac21-fc35d8a7d309",
        requestId: "ec6aef11-8b53-47ef-ac21-fc35d8a7d309",
        referenceId: "DE_28553",
        taskDesc: "Please sign the Offer Letter",
        processDesc: "Sign Document",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Please sign the Offer Letter",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "DF33D66D-1A9C-4E8C-8A48-6F84032EA108",
        itmStatus: "Open",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1679836364000,
        criticalDeadline: 1679058764000,
        createdOn: 1679058764000,
        updatedOn: 1679058934000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "DocuSign",
        systemName: "DocuSign",
        processName: "ENVELOPE",
        taskType: "Sign Document",
        taskId: "aae793f9-1697-43ac-be53-c4396f6511dc",
        processId: "5ee78136-08ab-41be-a153-6757079ec0f1",
        requestId: "5ee78136-08ab-41be-a153-6757079ec0f1",
        referenceId: "DE_35751",
        taskDesc: "Complete with DocuSign: test1.pdf",
        processDesc: "Sign Document",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Complete with DocuSign: test1.pdf",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "DF33D66D-1A9C-4E8C-8A48-6F84032EA108",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1681994154000,
        criticalDeadline: 1681216554000,
        createdOn: 1681216554000,
        updatedOn: 1681224403000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "Prince Kumar",
        updatedByName: "Prince Kumar",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "DocuSign",
        systemName: "DocuSign",
        processName: "ENVELOPE",
        taskType: "Sign Document",
        taskId: "ba42bf7f-97c3-43a0-b12d-79922859b19a",
        processId: "5deed9c1-aade-4547-8858-2caf868340ca",
        requestId: "5deed9c1-aade-4547-8858-2caf868340ca",
        referenceId: "DE_29944",
        taskDesc: "Docu sign Task created by Syed",
        processDesc: "Sign Document",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Docu sign Task created by Syed",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "DF33D66D-1A9C-4E8C-8A48-6F84032EA108",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1681035836000,
        criticalDeadline: 1680258236000,
        createdOn: 1680258236000,
        updatedOn: 1680263899000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "Preetham m",
        updatedByName: "Preetham m",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "DocuSign",
        systemName: "DocuSign",
        processName: "ENVELOPE",
        taskType: "Sign Document",
        taskId: "c5d97ed6-467a-420f-8da1-54f8fe4f6510",
        processId: "0c66a487-f016-4f6a-b361-df1c2361074a",
        requestId: "0c66a487-f016-4f6a-b361-df1c2361074a",
        referenceId: "DE_29301",
        taskDesc: "Complete with DocuSign: Untitled (1).png",
        processDesc: "Sign Document",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Complete with DocuSign: Untitled (1).png",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "DF33D66D-1A9C-4E8C-8A48-6F84032EA108",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1680937130000,
        criticalDeadline: 1680159530000,
        createdOn: 1680159530000,
        updatedOn: 1680158074000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "Preetham m",
        updatedByName: "Preetham m",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "DocuSign",
        systemName: "DocuSign",
        processName: "ENVELOPE",
        taskType: "Sign Document",
        taskId: "d169b8b5-3dbe-4d32-b585-19edb34e4676",
        processId: "5deed9c1-aade-4547-8858-2caf868340ca",
        requestId: "5deed9c1-aade-4547-8858-2caf868340ca",
        referenceId: "DE_29944",
        taskDesc: "Docu sign Task created by Syed",
        processDesc: "Sign Document",
        businessStatus: "READY",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "Docu sign Task created by Syed",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "DF33D66D-1A9C-4E8C-8A48-6F84032EA108",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1681035836000,
        criticalDeadline: 1680258236000,
        createdOn: 1680258236000,
        updatedOn: 1680263899000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "Preetham m",
        updatedByName: "Preetham m",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "DocuSign",
        systemName: "DocuSign",
        processName: "ENVELOPE",
        taskType: "Sign Document",
        taskId: "fb8883e3-88f3-41df-9238-dc8286e05fd6",
        processId: "4ef325d2-73b9-41ec-a07b-8d604b26d54b",
        requestId: "4ef325d2-73b9-41ec-a07b-8d604b26d54b",
        referenceId: "DE_29262",
        taskDesc: "Complete with DocuSign: file-sample_150kB.pdf",
        processDesc: "Sign Document",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Complete with DocuSign: file-sample_150kB.pdf",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "DF33D66D-1A9C-4E8C-8A48-6F84032EA108",
        itmStatus: "Open",
        isForwarded: 1,
        color: null,
        isPinned: 0,
        compDeadline: 1680853986000,
        criticalDeadline: 1680076386000,
        createdOn: 1680076386000,
        updatedOn: 1680078683000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "DocuSign", processName: "ENVELOPE", taskType: "Sign Document", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "Prince Kumar",
        updatedByName: "Prince Kumar",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:17:e9df4a8d-e41e-11ed-b216-da8497fb855a",
        taskType: "abcdefgh",
        taskId: "34af34a8-e41f-11ed-99a7-d2911a106c4e",
        processId: "34af0d8d-e41f-11ed-99a7-d2911a106c4e",
        requestId: "34af0d8d-e41f-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "abcdefgh",
        processDesc: "ANCHIT_DEMO:17:e9df4a8d-e41e-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:17:e9df4a8d-e41e-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "e432e857-7e96-4d68-a1ff-051513e2ed96",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930562000,
        criticalDeadline: 1682930562000,
        createdOn: 1682505820000,
        updatedOn: 1682505820000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:17:e9df4a8d-e41e-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:17:e9df4a8d-e41e-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:17:e9df4a8d-e41e-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 17.930876,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:19:1fbd96d9-e4b7-11ed-b216-da8497fb855a",
        taskType: "testanchit",
        taskId: "4fdfd2a0-e4b7-11ed-99a7-d2911a106c4e",
        processId: "4fdfd295-e4b7-11ed-99a7-d2911a106c4e",
        requestId: "4fdfd295-e4b7-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "testanchit",
        processDesc: "ANCHIT_DEMO:19:1fbd96d9-e4b7-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:19:1fbd96d9-e4b7-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "efabab69-ae77-48fe-a2bf-fb96f91bf2ef",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930533000,
        criticalDeadline: 1682930533000,
        createdOn: 1682571149000,
        updatedOn: 1682571149000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:19:1fbd96d9-e4b7-11ed-b216-da8497fb855a", taskType: "testanchit", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:19:1fbd96d9-e4b7-11ed-b216-da8497fb855a", taskType: "testanchit", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:19:1fbd96d9-e4b7-11ed-b216-da8497fb855a", taskType: "testanchit", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 3.7252572,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:22:c82028cd-ddf5-11ed-9f6f-be5cc965649d",
        taskType: "Assign Manager",
        taskId: "e909f956-de9d-11ed-99a7-d2911a106c4e",
        processId: "3813fb8a-de8a-11ed-99a7-d2911a106c4e",
        requestId: "3813fb8a-de8a-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "Assign Manager",
        processDesc: "onboarding:22:c82028cd-ddf5-11ed-9f6f-be5cc965649d",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:22:c82028cd-ddf5-11ed-9f6f-be5cc965649d",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "f008aca6-cdeb-4eb0-9144-51131633fde2",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930554000,
        criticalDeadline: 1682930554000,
        createdOn: 1681900532000,
        updatedOn: 1681900532000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:22:c82028cd-ddf5-11ed-9f6f-be5cc965649d", taskType: "Assign Manager", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:22:c82028cd-ddf5-11ed-9f6f-be5cc965649d", taskType: "Assign Manager", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:22:c82028cd-ddf5-11ed-9f6f-be5cc965649d", taskType: "Assign Manager", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "System User",
        updatedByName: "System User",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 65.33026,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:25:a39f3580-e4bf-11ed-b216-da8497fb855a",
        taskType: "abcdefgh2",
        taskId: "03205e7e-e4c0-11ed-99a7-d2911a106c4e",
        processId: "ccc5b99b-e4bf-11ed-99a7-d2911a106c4e",
        requestId: "ccc5b99b-e4bf-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "abcdefgh2",
        processDesc: "ANCHIT_DEMO:25:a39f3580-e4bf-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:25:a39f3580-e4bf-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "f5481d64-0f99-4661-8956-0031eef946cd",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930548000,
        criticalDeadline: 1682930548000,
        createdOn: 1682574886000,
        updatedOn: 1682574886000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:25:a39f3580-e4bf-11ed-b216-da8497fb855a", taskType: "abcdefgh2", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:25:a39f3580-e4bf-11ed-b216-da8497fb855a", taskType: "abcdefgh2", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:25:a39f3580-e4bf-11ed-b216-da8497fb855a", taskType: "abcdefgh2", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 2.7622619,
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000393526",
        processId: "1000000013",
        requestId: "000000393526",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1000000013",
        processDesc: "Please release purchase requisition 1000000013",
        businessStatus: "RESERVED",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "Please release purchase requisition 1000000013",
        createdBy: "20113289",
        updatedBy: "P000301",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1678704339000,
        criticalDeadline: 1588464000000,
        createdOn: 1678704339000,
        updatedOn: 1677572865000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "20113289",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000393530",
        processId: "1200000020",
        requestId: "000000393530",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1200000020",
        processDesc: "Please release purchase requisition 1200000020",
        businessStatus: "RESERVED",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "Please release purchase requisition 1200000020",
        createdBy: "139178",
        updatedBy: "P000301",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1678707519000,
        criticalDeadline: 1588464000000,
        createdOn: 1678707519000,
        updatedOn: 1677575031000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "139178",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000393536",
        processId: "1000000024",
        requestId: "000000393536",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1000000024",
        processDesc: "Please release purchase requisition 1000000024",
        businessStatus: "RESERVED",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "Please release purchase requisition 1000000024",
        createdBy: "20113289",
        updatedBy: "P000301",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1588636800000,
        criticalDeadline: 1588464000000,
        createdOn: 1588189953000,
        updatedOn: 1678094966000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "20113289",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000393548",
        processId: "1000000001",
        requestId: "000000393548",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1000000001",
        processDesc: "Please release purchase requisition 1000000001",
        businessStatus: "RESERVED",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "Please release purchase requisition 1000000001",
        createdBy: "20113289",
        updatedBy: "P000301",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1588636800000,
        criticalDeadline: 1588464000000,
        createdOn: 1588195534000,
        updatedOn: 1678095416000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "20113289",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000393576",
        processId: "1000000028",
        requestId: "000000393576",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1000000028",
        processDesc: "Please release purchase requisition 1000000028",
        businessStatus: "RESERVED",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "Please release purchase requisition 1000000028",
        createdBy: "20113289",
        updatedBy: "P000301",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1588723200000,
        criticalDeadline: 1588550400000,
        createdOn: 1588257317000,
        updatedOn: 1677577082000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "20113289",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000393580",
        processId: "1100000021",
        requestId: "000000393580",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1100000021",
        processDesc: "Please release purchase requisition 1100000021",
        businessStatus: "RESERVED",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "Please release purchase requisition 1100000021",
        createdBy: "20113289",
        updatedBy: "P000301",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1588723200000,
        criticalDeadline: 1588550400000,
        createdOn: 1588260885000,
        updatedOn: 1678094417000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "20113289",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000393651",
        processId: "1100000022",
        requestId: "000000393651",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1100000022",
        processDesc: "Please release purchase requisition 1100000022",
        businessStatus: "RESERVED",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "Please release purchase requisition 1100000022",
        createdBy: "243398",
        updatedBy: "P000301",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1588896000000,
        criticalDeadline: 1588723200000,
        createdOn: 1588416803000,
        updatedOn: 1678093216000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "243398",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000394159",
        processId: "1100000031",
        requestId: "000000394159",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1100000031",
        processDesc: "Please release purchase requisition 1100000031",
        businessStatus: "RESERVED",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "Please release purchase requisition 1100000031",
        createdBy: "20113289",
        updatedBy: "P000301",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1588982400000,
        criticalDeadline: 1588809600000,
        createdOn: 1588489252000,
        updatedOn: 1677575907000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "20113289",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000394199",
        processId: "1100000035",
        requestId: "000000394199",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1100000035",
        processDesc: "Please release purchase requisition 1100000035",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Please release purchase requisition 1100000035",
        createdBy: "243398",
        updatedBy: "139178",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1588982400000,
        criticalDeadline: 1588809600000,
        createdOn: 1588515826000,
        updatedOn: 1588464000000,
        completedAt: null,
        taskNature: "Group",
        actions: [{ systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "RIGHT", status: "READY", isActive: 1 }],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "243398",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000394203",
        processId: "1100000037",
        requestId: "000000394203",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1100000037",
        processDesc: "Please release purchase requisition 1100000037",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Please release purchase requisition 1100000037",
        createdBy: "243398",
        updatedBy: "139178",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1588982400000,
        criticalDeadline: 1588809600000,
        createdOn: 1588517049000,
        updatedOn: 1588464000000,
        completedAt: null,
        taskNature: "Group",
        actions: [{ systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "RIGHT", status: "READY", isActive: 1 }],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "243398",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000394223",
        processId: "1100000039",
        requestId: "000000394223",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1100000039",
        processDesc: "Please release purchase requisition 1100000039",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Please release purchase requisition 1100000039",
        createdBy: "243398",
        updatedBy: "139178",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1588982400000,
        criticalDeadline: 1588809600000,
        createdOn: 1588518206000,
        updatedOn: 1588464000000,
        completedAt: null,
        taskNature: "Group",
        actions: [{ systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "RIGHT", status: "READY", isActive: 1 }],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "243398",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000394227",
        processId: "1100000040",
        requestId: "000000394227",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1100000040",
        processDesc: "Please release purchase requisition 1100000040",
        businessStatus: "RESERVED",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "Please release purchase requisition 1100000040",
        createdBy: "243398",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1588982400000,
        criticalDeadline: 1588809600000,
        createdOn: 1588518590000,
        updatedOn: 1682514077000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "243398",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000394233",
        processId: "1100000041",
        requestId: "000000394233",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1100000041",
        processDesc: "Please release purchase requisition 1100000041",
        businessStatus: "RESERVED",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "Please release purchase requisition 1100000041",
        createdBy: "20113289",
        updatedBy: "P000301",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1588982400000,
        criticalDeadline: 1588809600000,
        createdOn: 1588523674000,
        updatedOn: 1678094576000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "20113289",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000394246",
        processId: "1100000042",
        requestId: "000000394246",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1100000042",
        processDesc: "Please release purchase requisition 1100000042",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Please release purchase requisition 1100000042",
        createdBy: "243398",
        updatedBy: "139178",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1588982400000,
        criticalDeadline: 1588809600000,
        createdOn: 1588539167000,
        updatedOn: 1588464000000,
        completedAt: null,
        taskNature: "Group",
        actions: [{ systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "RIGHT", status: "READY", isActive: 1 }],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "243398",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000394248",
        processId: "1000000033",
        requestId: "000000394248",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1000000033",
        processDesc: "Please release purchase requisition 1000000033",
        businessStatus: "RESERVED",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "Please release purchase requisition 1000000033",
        createdBy: "836499",
        updatedBy: "P000301",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1588982400000,
        criticalDeadline: 1588809600000,
        createdOn: 1588542759000,
        updatedOn: 1678094595000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "836499",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000394268",
        processId: "1100000043",
        requestId: "000000394268",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1100000043",
        processDesc: "Please release purchase requisition 1100000043",
        businessStatus: "RESERVED",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "Please release purchase requisition 1100000043",
        createdBy: "243398",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1589068800000,
        criticalDeadline: 1588896000000,
        createdOn: 1588578696000,
        updatedOn: 1681301272000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "243398",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000394271",
        processId: "1100000044",
        requestId: "000000394271",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1100000044",
        processDesc: "Please release purchase requisition 1100000044",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Please release purchase requisition 1100000044",
        createdBy: "243398",
        updatedBy: "139178",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1589068800000,
        criticalDeadline: 1588896000000,
        createdOn: 1588579373000,
        updatedOn: 1588550400000,
        completedAt: null,
        taskNature: "Group",
        actions: [{ systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "RIGHT", status: "READY", isActive: 1 }],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "243398",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000394282",
        processId: "1100000046",
        requestId: "000000394282",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1100000046",
        processDesc: "Please release purchase requisition 1100000046",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Please release purchase requisition 1100000046",
        createdBy: "243398",
        updatedBy: "139178",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1589068800000,
        criticalDeadline: 1588896000000,
        createdOn: 1588580323000,
        updatedOn: 1588550400000,
        completedAt: null,
        taskNature: "Group",
        actions: [{ systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "RIGHT", status: "READY", isActive: 1 }],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "243398",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000394296",
        processId: "1100000048",
        requestId: "000000394296",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1100000048",
        processDesc: "Please release purchase requisition 1100000048",
        businessStatus: "RESERVED",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "Please release purchase requisition 1100000048",
        createdBy: "243398",
        updatedBy: "P000301",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1589068800000,
        criticalDeadline: 1588896000000,
        createdOn: 1588581051000,
        updatedOn: 1677575187000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "243398",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000394304",
        processId: "1100000049",
        requestId: "000000394304",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1100000049",
        processDesc: "Please release purchase requisition 1100000049",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Please release purchase requisition 1100000049",
        createdBy: "243398",
        updatedBy: "139178",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1589068800000,
        criticalDeadline: 1588896000000,
        createdOn: 1588581560000,
        updatedOn: 1588550400000,
        completedAt: null,
        taskNature: "Group",
        actions: [{ systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "RIGHT", status: "READY", isActive: 1 }],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "243398",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000394306",
        processId: "1000000034",
        requestId: "000000394306",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1000000034",
        processDesc: "Please release purchase requisition 1000000034",
        businessStatus: "RESERVED",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "Please release purchase requisition 1000000034",
        createdBy: "20113289",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1589068800000,
        criticalDeadline: 1588896000000,
        createdOn: 1588581686000,
        updatedOn: 1682513937000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "20113289",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000394308",
        processId: "1100000050",
        requestId: "000000394308",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1100000050",
        processDesc: "Please release purchase requisition 1100000050",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Please release purchase requisition 1100000050",
        createdBy: "243398",
        updatedBy: "139178",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1589068800000,
        criticalDeadline: 1588896000000,
        createdOn: 1588581758000,
        updatedOn: 1588550400000,
        completedAt: null,
        taskNature: "Group",
        actions: [{ systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "CLAIM", icon: "SVGIcons.ClaimIcon", hoverIcon: "", label: "Claim", priority: "SECONDARY", actionOrder: "RIGHT", status: "READY", isActive: 1 }],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "243398",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "S4",
        systemName: "S4",
        processName: "**********",
        taskType: "Overall release of requisition",
        taskId: "000000394310",
        processId: "1100000051",
        requestId: "000000394310",
        referenceId: null,
        taskDesc: "Please release purchase requisition 1100000051",
        processDesc: "Please release purchase requisition 1100000051",
        businessStatus: "RESERVED",
        technicalStatus: "RESERVED",
        priority: "0",
        subject: "Please release purchase requisition 1100000051",
        createdBy: "243398",
        updatedBy: "<EMAIL>",
        completedBy: "<EMAIL>",
        formId: "F62109E6-F011-4A5A-BA5B-2D243CB25EC4",
        itmStatus: "In Progress",
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1589068800000,
        criticalDeadline: 1588896000000,
        createdOn: 1588582242000,
        updatedOn: 1681883087000,
        completedAt: null,
        taskNature: "Group",
        actions: [
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "RESERVED", isActive: 1 },
          { systemId: "S4", processName: "**********", taskType: "Overall release of requisition", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "RESERVED", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "243398",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "leave_app_demo:5:0758641a-dd0d-11ed-8549-ba997a741d41",
        taskType: "Approver1",
        taskId: "3ee26134-dd0d-11ed-91c6-52ceb58466bf",
        processId: "3ee23a17-dd0d-11ed-91c6-52ceb58466bf",
        requestId: "3ee23a17-dd0d-11ed-91c6-52ceb58466bf",
        referenceId: null,
        taskDesc: "Approver1",
        processDesc: "leave_app_demo:5:0758641a-dd0d-11ed-8549-ba997a741d41",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "leave_app_demo:5:0758641a-dd0d-11ed-8549-ba997a741d41",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "fc7e0298-09e0-4852-a7ef-e7aad8764b4b",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682419648000,
        criticalDeadline: 1682419648000,
        createdOn: 1681728448000,
        updatedOn: 1681728448000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "leave_app_demo:5:0758641a-dd0d-11ed-8549-ba997a741d41", taskType: "Approver1", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "leave_app_demo:5:0758641a-dd0d-11ed-8549-ba997a741d41", taskType: "Approver1", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "leave_app_demo:5:0758641a-dd0d-11ed-8549-ba997a741d41", taskType: "Approver1", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:22:c0807ff9-e4ba-11ed-b216-da8497fb855a",
        taskType: "abcdefgh2",
        taskId: "281d9d2d-e4bb-11ed-99a7-d2911a106c4e",
        processId: "ed54547b-e4ba-11ed-99a7-d2911a106c4e",
        requestId: "ed54547b-e4ba-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "abcdefgh2",
        processDesc: "ANCHIT_DEMO:22:c0807ff9-e4ba-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:22:c0807ff9-e4ba-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "fd0781ae-20d3-4f58-b0f2-6ec723f94ae3",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930559000,
        criticalDeadline: 1682930559000,
        createdOn: 1682572800000,
        updatedOn: 1682572800000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:22:c0807ff9-e4ba-11ed-b216-da8497fb855a", taskType: "abcdefgh2", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:22:c0807ff9-e4ba-11ed-b216-da8497fb855a", taskType: "abcdefgh2", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:22:c0807ff9-e4ba-11ed-b216-da8497fb855a", taskType: "abcdefgh2", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 3.3019896,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:26:7e287eae-e4c0-11ed-b216-da8497fb855a",
        taskType: "testanchit5",
        taskId: "268513bf-e4c1-11ed-99a7-d2911a106c4e",
        processId: "2684eca4-e4c1-11ed-99a7-d2911a106c4e",
        requestId: "2684eca4-e4c1-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "testanchit5",
        processDesc: "ANCHIT_DEMO:26:7e287eae-e4c0-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:26:7e287eae-e4c0-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "fd2d3696-8105-454c-a899-93d11326a222",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930558000,
        criticalDeadline: 1682930558000,
        createdOn: 1682575374000,
        updatedOn: 1682575374000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:26:7e287eae-e4c0-11ed-b216-da8497fb855a", taskType: "testanchit5", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:26:7e287eae-e4c0-11ed-b216-da8497fb855a", taskType: "testanchit5", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:26:7e287eae-e4c0-11ed-b216-da8497fb855a", taskType: "testanchit5", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 2.6350336,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:7:26df294c-d9e9-11ed-bcef-02051f9a391c",
        taskType: "HR task for generate offer letter",
        taskId: "47a42398-d9e9-11ed-bcef-02051f9a391c",
        processId: "47a3fc76-d9e9-11ed-bcef-02051f9a391c",
        requestId: "47a3fc76-d9e9-11ed-bcef-02051f9a391c",
        referenceId: null,
        taskDesc: "HR task for generate offer letter",
        processDesc: "onboarding:7:26df294c-d9e9-11ed-bcef-02051f9a391c",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:7:26df294c-d9e9-11ed-bcef-02051f9a391c",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: "ff5fc0a1-f294-4027-a914-2b2eacca7b78",
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1681555947000,
        criticalDeadline: 1681555947000,
        createdOn: 1681383147000,
        updatedOn: 1681383147000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:7:26df294c-d9e9-11ed-bcef-02051f9a391c", taskType: "HR task for generate offer letter", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:7:26df294c-d9e9-11ed-bcef-02051f9a391c", taskType: "HR task for generate offer letter", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:7:26df294c-d9e9-11ed-bcef-02051f9a391c", taskType: "HR task for generate offer letter", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:14:7e350fec-e41b-11ed-b216-da8497fb855a",
        taskType: "testing2",
        taskId: "293d8bf9-e41c-11ed-99a7-d2911a106c4e",
        processId: "c09ce157-e41b-11ed-99a7-d2911a106c4e",
        requestId: "c09ce157-e41b-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "testing2",
        processDesc: "ANCHIT_DEMO:14:7e350fec-e41b-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:14:7e350fec-e41b-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: "Open",
        isForwarded: 0,
        color: "#089B13",
        isPinned: 0,
        compDeadline: 1682853514000,
        criticalDeadline: 1682853514000,
        createdOn: 1682504512000,
        updatedOn: 1682504512000,
        completedAt: 1682507994000,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:14:7e350fec-e41b-11ed-b216-da8497fb855a", taskType: "testing2", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:14:7e350fec-e41b-11ed-b216-da8497fb855a", taskType: "testing2", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:14:7e350fec-e41b-11ed-b216-da8497fb855a", taskType: "testing2", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a",
        taskType: "abcdefgh",
        taskId: "04c297d4-e433-11ed-99a7-d2911a106c4e",
        processId: "04c297c9-e433-11ed-99a7-d2911a106c4e",
        requestId: "04c297c9-e433-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "abcdefgh",
        processDesc: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930549000,
        criticalDeadline: 1682930549000,
        createdOn: 1682514329000,
        updatedOn: 1682514329000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 16.323183,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:21:d4ece111-e4b9-11ed-b216-da8497fb855a",
        taskType: "testanchit1",
        taskId: "0580f21b-e4ba-11ed-99a7-d2911a106c4e",
        processId: "0580f210-e4ba-11ed-99a7-d2911a106c4e",
        requestId: "0580f210-e4ba-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "testanchit1",
        processDesc: "ANCHIT_DEMO:21:d4ece111-e4b9-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:21:d4ece111-e4b9-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930549000,
        criticalDeadline: 1682930549000,
        createdOn: 1682572313000,
        updatedOn: 1682572313000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:21:d4ece111-e4b9-11ed-b216-da8497fb855a", taskType: "testanchit1", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:21:d4ece111-e4b9-11ed-b216-da8497fb855a", taskType: "testanchit1", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:21:d4ece111-e4b9-11ed-b216-da8497fb855a", taskType: "testanchit1", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 3.4272437,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        taskType: "Leave request form",
        taskId: "0d87234a-9198-11ed-8894-b2afb59da3b3",
        processId: "0d86fc32-9198-11ed-8894-b2afb59da3b3",
        requestId: "0d86fc32-9198-11ed-8894-b2afb59da3b3",
        referenceId: null,
        taskDesc: "Leave request form",
        processDesc: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1673604577000,
        criticalDeadline: 1673604577000,
        createdOn: 1673431777000,
        updatedOn: 1673431777000,
        completedAt: null,
        actions: null,
        taskNature: "Group",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        taskType: "Leave request form",
        taskId: "2bbdf53f-8cfc-11ed-a704-ba6944721b05",
        processId: "2bbb0f08-8cfc-11ed-a704-ba6944721b05",
        requestId: "2bbb0f08-8cfc-11ed-a704-ba6944721b05",
        referenceId: null,
        taskDesc: "Leave request form",
        processDesc: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1673097821000,
        criticalDeadline: 1673097821000,
        createdOn: 1672925021000,
        updatedOn: 1672925021000,
        completedAt: null,
        actions: null,
        taskNature: "Group",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:16:0bfe43e1-dd23-11ed-8549-ba997a741d41",
        taskType: "HR task for generate offer letter",
        taskId: "303867cf-dd23-11ed-8549-ba997a741d41",
        processId: "303867bd-dd23-11ed-8549-ba997a741d41",
        requestId: "303867bd-dd23-11ed-8549-ba997a741d41",
        referenceId: null,
        taskDesc: "HR task for generate offer letter",
        processDesc: "onboarding:16:0bfe43e1-dd23-11ed-8549-ba997a741d41",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:16:0bfe43e1-dd23-11ed-8549-ba997a741d41",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1681910672000,
        criticalDeadline: 1681910672000,
        createdOn: 1681737872000,
        updatedOn: 1681737872000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:16:0bfe43e1-dd23-11ed-8549-ba997a741d41", taskType: "HR task for generate offer letter", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:16:0bfe43e1-dd23-11ed-8549-ba997a741d41", taskType: "HR task for generate offer letter", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:16:0bfe43e1-dd23-11ed-8549-ba997a741d41", taskType: "HR task for generate offer letter", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        taskType: "Leave request form",
        taskId: "3e963085-9198-11ed-8894-b2afb59da3b3",
        processId: "3e96096d-9198-11ed-8894-b2afb59da3b3",
        requestId: "3e96096d-9198-11ed-8894-b2afb59da3b3",
        referenceId: null,
        taskDesc: "Leave request form",
        processDesc: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1673604659000,
        criticalDeadline: 1673604659000,
        createdOn: 1673431859000,
        updatedOn: 1673431859000,
        completedAt: null,
        actions: null,
        taskNature: "Group",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        taskType: "Leave request form",
        taskId: "4064f5a0-8dc7-11ed-a2fb-28dfeb1e7284",
        processId: "405e17c8-8dc7-11ed-a2fb-28dfeb1e7284",
        requestId: "405e17c8-8dc7-11ed-a2fb-28dfeb1e7284",
        referenceId: null,
        taskDesc: "Leave request form",
        processDesc: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        createdBy: "anonymousUser",
        updatedBy: "anonymousUser",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1673185044000,
        criticalDeadline: 1673185044000,
        createdOn: 1673012244000,
        updatedOn: 1673012244000,
        completedAt: null,
        actions: null,
        taskNature: "Group",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "anonymousUser",
        updatedByName: "",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "practice_1:1:f872eec8-8c1f-11ed-b057-c6000bf78a30",
        taskType: "",
        taskId: "4581463c-8c22-11ed-b50a-32660e410a70",
        processId: "4580f815-8c22-11ed-b50a-32660e410a70",
        requestId: "4580f815-8c22-11ed-b50a-32660e410a70",
        referenceId: null,
        taskDesc: "",
        processDesc: "practice_1:1:f872eec8-8c1f-11ed-b057-c6000bf78a30",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "practice_1:1:f872eec8-8c1f-11ed-b057-c6000bf78a30",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1675775068000,
        criticalDeadline: 1675775123000,
        createdOn: 1672831434000,
        updatedOn: 1672831434000,
        completedAt: null,
        actions: null,
        taskNature: "Group",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41",
        taskType: "Assign Manager",
        taskId: "4ca94d33-e400-11ed-99a7-d2911a106c4e",
        processId: "ba2b8c2b-ddd2-11ed-99a7-d2911a106c4e",
        requestId: "ba2b8c2b-ddd2-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "Assign Manager",
        processDesc: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930530000,
        criticalDeadline: 1682930530000,
        createdOn: 1682492546000,
        updatedOn: 1682492546000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41", taskType: "Assign Manager", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41", taskType: "Assign Manager", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41", taskType: "Assign Manager", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "System User",
        updatedByName: "System User",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 20.321222,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41",
        taskType: "IT Admin",
        taskId: "4caa1086-e400-11ed-99a7-d2911a106c4e",
        processId: "ba2b8c2b-ddd2-11ed-99a7-d2911a106c4e",
        requestId: "ba2b8c2b-ddd2-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "IT Admin",
        processDesc: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930530000,
        criticalDeadline: 1682930530000,
        createdOn: 1682492546000,
        updatedOn: 1682492546000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41", taskType: "IT Admin", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41", taskType: "IT Admin", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41", taskType: "IT Admin", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "System User",
        updatedByName: "System User",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 20.321222,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41",
        taskType: "HR for Training",
        taskId: "4caa379d-e400-11ed-99a7-d2911a106c4e",
        processId: "ba2b8c2b-ddd2-11ed-99a7-d2911a106c4e",
        requestId: "ba2b8c2b-ddd2-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "HR for Training",
        processDesc: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930532000,
        criticalDeadline: 1682930532000,
        createdOn: 1682492546000,
        updatedOn: 1682492546000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41", taskType: "HR for Training", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41", taskType: "HR for Training", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41", taskType: "HR for Training", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "System User",
        updatedByName: "System User",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 20.321135,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41",
        taskType: "HR for Training",
        taskId: "4caa37a0-e400-11ed-99a7-d2911a106c4e",
        processId: "ba2b8c2b-ddd2-11ed-99a7-d2911a106c4e",
        requestId: "ba2b8c2b-ddd2-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "HR for Training",
        processDesc: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930532000,
        criticalDeadline: 1682930532000,
        createdOn: 1682492546000,
        updatedOn: 1682492546000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41", taskType: "HR for Training", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41", taskType: "HR for Training", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "onboarding:21:ee7b3232-dd40-11ed-8549-ba997a741d41", taskType: "HR for Training", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "System User",
        updatedByName: "System User",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 20.321135,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        taskType: "Leave request form",
        taskId: "56b393e9-8cfc-11ed-a704-ba6944721b05",
        processId: "56b393e2-8cfc-11ed-a704-ba6944721b05",
        requestId: "56b393e2-8cfc-11ed-a704-ba6944721b05",
        referenceId: null,
        taskDesc: "Leave request form",
        processDesc: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1673097893000,
        criticalDeadline: 1673097893000,
        createdOn: 1672925093000,
        updatedOn: 1672925093000,
        completedAt: null,
        actions: null,
        taskNature: "Group",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "Leave:3:76773080-7713-11ed-9932-28dfeb1e7284",
        taskType: "Leave request form",
        taskId: "5c64cb99-8c38-11ed-bfa7-96243e897ee3",
        processId: "5c56c1d2-8c38-11ed-bfa7-96243e897ee3",
        requestId: "5c56c1d2-8c38-11ed-bfa7-96243e897ee3",
        referenceId: null,
        taskDesc: "Leave request form",
        processDesc: "Leave:3:76773080-7713-11ed-9932-28dfeb1e7284",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Leave:3:76773080-7713-11ed-9932-28dfeb1e7284",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1673013721000,
        criticalDeadline: 1673013721000,
        createdOn: 1672840921000,
        updatedOn: 1672840921000,
        completedAt: null,
        actions: null,
        taskNature: "Group",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a",
        taskType: "abcdefgh",
        taskId: "5f576889-e425-11ed-99a7-d2911a106c4e",
        processId: "5f57416e-e425-11ed-99a7-d2911a106c4e",
        requestId: "5f57416e-e425-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "abcdefgh",
        processDesc: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930537000,
        criticalDeadline: 1682930537000,
        createdOn: 1682508468000,
        updatedOn: 1682508468000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 17.438066,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        taskType: "Leave request form",
        taskId: "7ea0e2df-8dc5-11ed-a704-ba6944721b05",
        processId: "7ea0bbc8-8dc5-11ed-a704-ba6944721b05",
        requestId: "7ea0bbc8-8dc5-11ed-a704-ba6944721b05",
        referenceId: null,
        taskDesc: "Leave request form",
        processDesc: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "Leave:4:d6784aa4-8cda-11ed-87ab-22e13c82c869",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1673184289000,
        criticalDeadline: 1673184289000,
        createdOn: 1673011489000,
        updatedOn: 1673011489000,
        completedAt: null,
        actions: null,
        taskNature: "Group",
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: true,
        timeLeftDisplayString: "BREACHED",
        taskSla: "BREACHED",
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:9:c49521ac-e414-11ed-b216-da8497fb855a",
        taskType: "testing1",
        taskId: "956825b3-e415-11ed-99a7-d2911a106c4e",
        processId: "956825a8-e415-11ed-99a7-d2911a106c4e",
        requestId: "956825a8-e415-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "testing1",
        processDesc: "ANCHIT_DEMO:9:c49521ac-e414-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:9:c49521ac-e414-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930531000,
        criticalDeadline: 1682930531000,
        createdOn: 1682501687000,
        updatedOn: 1682501687000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:9:c49521ac-e414-11ed-b216-da8497fb855a", taskType: "testing1", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:9:c49521ac-e414-11ed-b216-da8497fb855a", taskType: "testing1", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:9:c49521ac-e414-11ed-b216-da8497fb855a", taskType: "testing1", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 18.691204,
      },
      {
        systemId: "Flowable",
        systemName: "Native Workflow",
        processName: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a",
        taskType: "abcdefgh",
        taskId: "bc2a7f1a-e433-11ed-99a7-d2911a106c4e",
        processId: "bc2a57ff-e433-11ed-99a7-d2911a106c4e",
        requestId: "bc2a57ff-e433-11ed-99a7-d2911a106c4e",
        referenceId: null,
        taskDesc: "abcdefgh",
        processDesc: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a",
        businessStatus: "READY",
        technicalStatus: "READY",
        priority: "0",
        subject: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a",
        createdBy: "<EMAIL>",
        updatedBy: "<EMAIL>",
        completedBy: null,
        formId: null,
        itmStatus: null,
        isForwarded: 0,
        color: null,
        isPinned: 0,
        compDeadline: 1682930541000,
        criticalDeadline: 1682930541000,
        createdOn: 1682514637000,
        updatedOn: 1682514637000,
        completedAt: null,
        taskNature: "Single-User",
        actions: [
          { systemId: "Flowable", processName: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "APPROVE", icon: "MaterialIcon.MdDone", hoverIcon: "", label: "Approve", priority: "PRIMARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "FORWARD", icon: "SVGIcons.ForwardIcon", hoverIcon: "", label: "Forward", priority: "SECONDARY", actionOrder: "POSITIVE", status: "READY", isActive: 1 },
          { systemId: "Flowable", processName: "ANCHIT_DEMO:18:1b46efd7-e425-11ed-b216-da8497fb855a", taskType: "abcdefgh", action: "REJECT", icon: "IcoMoon5Icon.IoClose", hoverIcon: "", label: "Reject", priority: "PRIMARY", actionOrder: "NEGATIVE", status: "READY", isActive: 1 },
        ],
        ownerType: "USER",
        ownerId: "<EMAIL>",
        createdByName: "George Abraham",
        updatedByName: "George Abraham",
        forwardedByName: "",
        isCritical: false,
        isBreached: false,
        timeLeftDisplayString: "On_Time",
        taskSla: "On_Time",
        timePercentCompleted: 16.264088,
      },
    ],
  },
  count: null,
};
