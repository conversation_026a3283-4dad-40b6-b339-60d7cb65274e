import {
  convertToDateFormat,
  getLocalStorage,
  removeOptionsFromOrgData,
} from "../helper/helper";
import { MATERIAL_VIEWS, REQUEST_TYPE, EXCLUDED_VIEWS, LOCAL_STORAGE_KEYS } from "../constant/enum";
import { useSelector } from "react-redux";
import { useLocation } from "react-router-dom";

const usePayloadCreation = ({initialReqScreen,isReqBench,remarks = "",userInput ="",selectedLevel = ""}) => {
  const payloadData = useSelector((state) => state.payload);
  const requestType = payloadData?.payloadData?.RequestType;
  const taskData = useSelector((state) => state.userManagement.taskData);
  const requestState = useSelector((state) => state.request);
  const taskDataString = getLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK);
  const localTask = typeof taskDataString === "string" ? JSON.parse(taskDataString) : taskDataString;
  const createChangeLogData = useSelector(
    (state) => state.changeLog.createChangeLogData
  );
  const location = useLocation();
  const urlSearchParams = new URLSearchParams(location.search.split("?")[1]);
  const requestId = urlSearchParams.get("RequestId");
  function createPayloadFromReduxState(reduxState) {
    const payloadArray = [];
    Object.keys(reduxState).forEach((key) => {
      if (key.includes("-") || /\d/.test(key)) {
        const item = reduxState[key];
        if (item?.headerData?.included) {
          const orgData = removeOptionsFromOrgData(item?.headerData?.orgData || []);
          const viewNamesString = (item?.headerData?.views?.filter(view => !EXCLUDED_VIEWS.includes(view)) || []).join(",").trim();
          const salesViewData = item?.payloadData?.Sales ? Object.values(item.payloadData.Sales) : [];
          // Map Tosalesdata based on orgData
          const uniqueSalesOrgs = new Set();
          const salesResultArrayCreate = orgData
            .filter((org) => {
              if (!org.salesOrg?.code || !org.dc?.value?.code) return false;
              const combination = `${org.salesOrg.code}-${org.dc.value.code}`;
              if (uniqueSalesOrgs.has(combination)) return false;
              uniqueSalesOrgs.add(combination);
              return true;
            })
            .map((org, index) => ({
              SalesId: requestType === REQUEST_TYPE.EXTEND && initialReqScreen ? null : salesViewData[index]?.SalesId || "", // Placeholder, will be updated from idsArray
              Function: "INS",
              Material: item.headerData?.materialNumber || "",
              SalesOrg: org.salesOrg?.code || "",
              DistrChan: org.dc?.value?.code || "",
              DelFlag: false,
              MatlStats: salesViewData[index]?.MatlStats || "",
              RebateGrp: salesViewData[index]?.RebateGrp || "",
              CashDisc: salesViewData[index]?.CashDisc || true,
              SalStatus: salesViewData[index]?.SalStatus || "",
              DelyUnit: "0.000",
              ValidFrom: salesViewData[index]?.ValidFrom ? convertToDateFormat(salesViewData[index]?.ValidFrom) : null,
              DelyUom: salesViewData[index]?.DelyUom || "",
              DelygPlnt: salesViewData[index]?.DelygPlnt || "",
              MatPrGrp: salesViewData[index]?.MatPrGrp || "",
              AcctAssgt: salesViewData[index]?.AcctAssgt || "",
              MatlGrp4: salesViewData[index]?.MatlGrp4 || "",
              MatlGrp2: salesViewData[index]?.MatlGrp2 || "",
              MatlGrp5: salesViewData[index]?.MatlGrp5 || "",
              BatchMgmt: salesViewData[index]?.BatchMgmt || "",
              Countryori: salesViewData[index]?.Countryori || "",
              Depcountry: salesViewData[index]?.Depcountry || "",
              SalesUnit: salesViewData[index]?.SalesUnit || "",
              ItemCat: salesViewData[index]?.ItemCat || "",
            }));

          const PurchaseViewArray = item?.payloadData?.Purchasing ? Object.entries(item.payloadData.Purchasing) : [];
          const MrpViewArray = item?.payloadData?.MRP ? Object.entries(item.payloadData.MRP) : [];
          const SalesPlantViewArray = item?.payloadData?.[MATERIAL_VIEWS.SALES_PLANT] ? Object.entries(item.payloadData?.[MATERIAL_VIEWS.SALES_PLANT]) : [];
          const storagePlantViewArray = item?.payloadData?.[MATERIAL_VIEWS.STORAGE_PLANT] ? Object.entries(item.payloadData?.[MATERIAL_VIEWS.STORAGE_PLANT]) : [];
          const AccountingViewArray = item?.payloadData?.Accounting ? Object.entries(item.payloadData.Accounting) : [];
          let ToControlDataForCreate = [];
          const hasTaxData = item?.payloadData?.TaxData?.TaxData?.TaxDataSet?.length > 0;
          if (hasTaxData) {
            const groupedByCountry = {};

            item?.payloadData?.TaxData?.TaxData?.TaxDataSet?.forEach((taxEntry) => {
              const countryKey = taxEntry.Country;

              if (!groupedByCountry[countryKey]) {
                groupedByCountry[countryKey] = {
                  ControlId: taxEntry.ControlId ?? null,
                  Function: "INS",
                  Material: item.headerData?.materialNumber || "",
                  Depcountry: taxEntry.Country,
                };
              }

              const taxIndex = Object.keys(groupedByCountry[countryKey]).filter((key) => key.startsWith("TaxType")).length + 1;

              if (taxIndex <= 9) {
                groupedByCountry[countryKey][`TaxType${taxIndex}`] = taxEntry.TaxType;
                groupedByCountry[countryKey][`Taxclass${taxIndex}`] = taxEntry.SelectedTaxClass?.TaxClass || ""; // Handle undefined case
              }
            });

            ToControlDataForCreate = Object.values(groupedByCountry);
          }
          const CostingViewArr = item?.payloadData?.Costing ? Object.entries(item.payloadData.Costing) : [];
          const org = item?.headerData?.orgData || [];
          const uniquePlantCodes = new Set();
          const accountingCostingArrayCreate = org
            .filter((plant) => {
              if (!plant.plant?.value?.code) return false;
              const plantCode = plant.plant.value.code;
              if (uniquePlantCodes.has(plantCode)) return false;
              uniquePlantCodes.add(plantCode);
              return true;
            })
            .map((plant) => {
              const plantCode = plant.plant?.value?.code;
              const accountingView = AccountingViewArray.find(([key]) => key === plantCode)?.[1] || {};
              const costingView = CostingViewArr.find(([key]) => key === plantCode)?.[1] || {};
              return {
                ...accountingView,
                AccountingId: accountingView.AccountingId || costingView.AccountingId || null,
                Function: "INS",
                Material: item.headerData?.materialNumber || "",
                DelFlag: "",
                PriceCtrl: accountingView.PriceCtrl || "",
                MovingPr: accountingView.MovingPr || costingView.MovingPr || "",
                StdPrice: accountingView.StdPrice || costingView.StdPrice || "",
                PriceUnit: accountingView.PriceUnit || "",
                ValClass: accountingView.ValClass || "",
                OrigMat: costingView.OrigMat === true || costingView.OrigMat === "X" || costingView.OrigMat === "TRUE" ? "X" : "",
                ValArea: plantCode || "",

                 //NOTE: Maybe required later
              // MovPrPp: costingView.MovPrPp || "",//not present in DT
              // MovPrPy: costingView.MovPrPy || "",//not present in DT
              // PrCtrlPp: costingView.PrCtrlPp || "",//not present in DT
              // PrCtrlPy: costingView.PrCtrlPy || "",//not present in DT
              // PrUnitPp: costingView.PrUnitPp || "",//not present in DT
              // PrUnitPy: costingView.PrUnitPy || "",//not present in DT
              // StdPrPp: "string",//not present in DT
              // StdPrPy: "string",//not present in DT
              // ValArea: "string",//not present in DT--> plant toe mapped from org data
              // ValCat: "string",//not present in DT
              // ValidFrom: "string",//not present in DT for Accounting // check in sales
              // ValType: "string",//not present in DT
              // VclassPy: "string",//valclass jsonName to be updated
              };
            });
          const warehouseData = item?.payloadData?.Warehouse ? Object.entries(item.payloadData.Warehouse) : [];
          const toWarehouseData = item.headerData?.views?.includes(MATERIAL_VIEWS?.WAREHOUSE) ? warehouseData.map(([wh, warehouse], key) => {
            // Push each warehouse's data as an object into the result array
            return {
              WarehouseId: warehouse.WarehouseId || "",
              Function: warehouse.Function || "",
              Material: item.headerData?.materialNumber || "",
              DelFlag: warehouse.DelFlag || true,
              WhseNo: wh || "",
              SpecMvmt: warehouse.SpecMvmt || "",
              LEquip1: warehouse.LEquip1 || "",
              LeqUnit1: warehouse.LeqUnit1 || "",
              Unittype1: warehouse.Unittype1 || "",
              Placement: warehouse.Placement || "",
            };
          }) : [];
          const storageViewData = item?.payloadData?.[MATERIAL_VIEWS?.STORAGE] ? Object.values(item.payloadData?.[MATERIAL_VIEWS?.STORAGE]) : [];
          const uniqueStoragePlants = new Set();
          const storageResultArrayCreate = orgData
            .filter((org) => {
              if (!org.plant?.value?.code || !org.sloc?.value?.code) return false;
              const combination = `${org.plant?.value?.code}-${org.sloc?.value?.code}`;
              if (uniqueStoragePlants.has(combination)) return false;
              uniqueStoragePlants.add(combination);
              return true;
            })
            .map((org, index) => {
              return {
                StorageLocationId: storageViewData[index]?.StorageLocationId || "",
                Plant: org.plant?.value?.code || "",
                StgeLoc: org.sloc?.value?.code || "",
                Material : item.headerData?.materialNumber || "",
                PickgArea: storageViewData[index]?.PickgArea || "",
                StgeBin: storageViewData[index]?.StgeBin || "",
              };
            });
          const classificationData = item.headerData?.views?.includes(MATERIAL_VIEWS.CLASSIFICATION)
          ? item?.payloadData?.[MATERIAL_VIEWS.CLASSIFICATION]
          : {};
          let ToClassification = [];
          if (
            classificationData &&
            classificationData.basic &&
            classificationData.basic.Classtype &&
            classificationData.basic.Classnum &&
            Array.isArray(classificationData.classification)
          ) {
            ToClassification.push({
              ClassificationId : classificationData?.ClassificationId || "",
              Classnum: classificationData.basic.Classnum,
              Classtype: classificationData.basic.Classtype,
              Object: item.headerData?.materialNumber || "", // or use the correct object number if available
              Tochars: classificationData.classification?.map((row) => ({
                CharacteristicsId : row?.CharacteristicsId || "",
                Charact: row.characteristic,
                CharactDescr: row.description,
                Tocharvalues: [
                  {
                    ValueChar: row.value || "", // fallback to empty string if no value
                  },
                ],
              })),
            });
          }
          const workSchedulingArr = item?.payloadData?.[MATERIAL_VIEWS.WORKSCHEDULING] ? Object.entries(item.payloadData?.[MATERIAL_VIEWS.WORKSCHEDULING]) : [];
          // Updated Toplantdata logic
          const BOMView = item?.payloadData?.[MATERIAL_VIEWS.BOM] ? Object.entries(item.payloadData?.[MATERIAL_VIEWS.BOM]) : [];
          const SourceListView = item?.payloadData?.[MATERIAL_VIEWS.SOURCE_LIST] ? Object.entries(item.payloadData?.[MATERIAL_VIEWS.SOURCE_LIST]) : [];
          const plants = item?.headerData?.orgData || [];
          const uniquePlants = plants.filter((plant, index, self) =>
            index === self?.findIndex((p) => p.plant?.value?.code === plant?.plant?.value?.code)
          );
          const toplantdatacreate = uniquePlants.map((plant, index) => {
            const plantCode = plant.plant?.value?.code;
            const MRPProfile = plant.mrpProfile?.code;
            const purchaseView = PurchaseViewArray.find(([key]) => key === plantCode)?.[1] || {};
            const costingView = CostingViewArr.find(([key]) => key === plantCode)?.[1] || {};
            const mrpView = MrpViewArray.find(([key]) => key.startsWith(plantCode))?.[1] || {};
            const salesPlantView = SalesPlantViewArray.find(([key]) => key === plantCode)?.[1] || {};
            const storagePlantView = storagePlantViewArray.find(([key]) => key === plantCode)?.[1] || {};
            const workScheduling = workSchedulingArr.find(([key]) => key === plantCode)?.[1] || {};
            const BomFields = BOMView.find(([key]) => key === plantCode)?.[1] || {};
            const SourceList = SourceListView.find(([key]) => key === plantCode)?.[1] || {};
            return {
              PlantId: requestType === REQUEST_TYPE.EXTEND && initialReqScreen ? null : item.payloadData?.Purchasing?.[plantCode]?.PlantId ?? null,
              Function: "INS",
              Material: item.headerData?.materialNumber || "",
              Plant: plantCode || "",
              DelFlag: false,
              CritPart: false,
              PurGroup: purchaseView?.PurGroup || "",
              PurStatus: purchaseView?.PurStatus || "",
              RoundProf: mrpView?.RoundProf || "",
              IssueUnitIso: "",
              Mrpprofile: MRPProfile || "",
              MrpType: mrpView?.MrpType || "",
              MrpCtrler: mrpView?.MrpCtrler || "",
              PlndDelry: mrpView?.PlndDelry || "",
              GrPrTime: mrpView?.GrPrTime || "",
              PeriodInd: mrpView?.PeriodInd || "",
              Lotsizekey: mrpView?.Lotsizekey || "",
              ProcType: mrpView?.ProcType || "",
              Consummode: mrpView?.Consummode || "",
              FwdCons: mrpView?.FwdCons || "",
              ReorderPt: mrpView?.ReorderPt || "",
              MaxStock: mrpView?.MaxStock || "",
              SafetyStk: mrpView?.SafetyStk || "",
              Minlotsize: mrpView?.Minlotsize || "",
              PlanStrgp: mrpView?.PlanStrgp || "",
              BwdCons: mrpView?.BwdCons || "",
              Maxlotsize: mrpView?.Maxlotsize || "",
              FixedLot: mrpView?.FixedLot || "",
              RoundVal: mrpView?.RoundVal || "",
              GrpReqmts: mrpView?.GrpReqmts || "",
              MixedMrp: mrpView?.MixedMrp || "",
              SmKey: mrpView?.SmKey || "",
              Backflush: mrpView?.Backflush || "",
              AssyScarp: mrpView?.AssyScarp || "",
              Replentime: mrpView?.Replentime || "",
              PlTiFnce: mrpView?.PlTiFnce || "",
              ReplacePt: "",
              IndPostToInspStock: purchaseView?.IndPostToInspStock === true || purchaseView?.IndPostToInspStock === "X" || purchaseView?.IndPostToInspStock === "TRUE" ? "X" : "",
              HtsCode: purchaseView?.HtsCode || "",
              CtrlKey: "",
              // BatchMgmt: mrpView?.BatchMgmt || false,
              DepReqId: mrpView?.DepReqId || "",
              SaftyTId: mrpView?.SaftyTId || "",
              Safetytime: mrpView?.Safetytime || "",
              Matfrgtgrp: salesPlantView?.Matfrgtgrp || "",
              Availcheck: salesPlantView?.Availcheck || "",
              ProfitCtr: salesPlantView?.ProfitCtr || "",
              Loadinggrp: salesPlantView?.Loadinggrp || "",
              MinLotSize: mrpView?.MinLotSize || "",
              MaxLotSize: mrpView?.MaxLotSize || "",
              FixLotSize: mrpView?.FixLotSize || "",
              AssyScrap: mrpView?.AssyScrap || "",
              IssStLoc: mrpView?.IssStLoc || "",
              SalesView: item?.headerData?.views.includes(MATERIAL_VIEWS?.SALES) || false,
              PurchView: item?.headerData?.views.includes(MATERIAL_VIEWS?.PURCHASING) || false,
              MrpView: item?.headerData?.views.includes(MATERIAL_VIEWS?.MRP) || false,
              WorkSchedView: item?.headerData?.views.includes(MATERIAL_VIEWS?.WORK_SCHEDULING_2) || false,
              WarehouseView: item?.headerData?.views.includes(MATERIAL_VIEWS?.WAREHOUSE) || false,
              AccountView: item?.headerData?.views.includes(MATERIAL_VIEWS?.ACCOUNTING) || false,
              CostView: item?.headerData?.views.includes(MATERIAL_VIEWS?.COSTING) || false,
              ForecastView: false,
              PrtView: false,
              StorageView: item?.headerData?.views.includes(MATERIAL_VIEWS?.STORAGE) || false,
              QualityView: false,
              GrProcTime: "",
              GiProcTime: "",
              StorageCost: "",
              LotSizeUom: "",
              LotSizeUomIso: "",
              Unlimited: workScheduling.Unlimited || "",
              ProdProf: workScheduling.ProdProf || "",
              VarianceKey: costingView.VarianceKey || "",
              PoUnit: "", // to br mapped from purchasing
              Spproctype: mrpView.Spproctype || "",
              CommCode: purchaseView?.CommCode || "",
              CommCoUn: purchaseView?.CommCoUn || "",
              Countryori: purchaseView?.Countryori,
              LotSize: costingView.LotSize || "",
              SlocExprc: mrpView.SlocExprc || "",
              Inhseprodt: costingView.Inhseprodt || "",
              // ProfitCtr: costingView.ProfitCtr || "",
              BomUsage : BomFields?.BomUsage || "",
              AltBom : BomFields?.AltBom || "",
              Category : BomFields?.Category || "",
              Component : BomFields?.Component || "",
              Quantity : BomFields?.Quantity || "",
              CompUom : BomFields?.CompUom || "",
              Bvalidfrom : BomFields?.Bvalidfrom ? convertToDateFormat(BomFields?.Bvalidfrom) : convertToDateFormat(new Date().toISOString()),
              Bvalidto : BomFields?.Bvalidto ? convertToDateFormat(BomFields?.Bvalidto) : convertToDateFormat(new Date().toISOString()),
              Supplier : SourceList?.Supplier || "",
              PurchaseOrg : SourceList?.PurchaseOrg || "",
              ProcurementPlant : SourceList?.ProcurementPlant || "",
              SOrderUnit : SourceList?.SOrderUnit || "",
              Agreement : SourceList?.Agreement || "",
              AgreementItem : SourceList?.AgreementItem || "",
              FixedSupplySource : SourceList?.FixedSupplySource || "",
              Blocked : SourceList?.Blocked || "",
              SMrp : SourceList?.SMrp || "",
              Slvalidfrom : SourceList?.Slvalidfrom ? convertToDateFormat(SourceList?.Slvalidfrom) : convertToDateFormat(new Date().toISOString()),
              Slvalidto : SourceList?.Slvalidto ? convertToDateFormat(SourceList?.Slvalidto) : convertToDateFormat(new Date().toISOString()),
              CcPhInv : storagePlantView?.CcPhInv || "",
              CcFixed : storagePlantView?.CcFixed || "",
              StgePdUn : storagePlantView?.StgePdUn || "",
              DefaultStockSegment : storagePlantView?.DefaultStockSegment || "",
              NegStocks : storagePlantView?.NegStocks || "",
              SernoProf : storagePlantView?.SernoProf || "",
              DistrProf : storagePlantView?.DistrProf || "",
              DetermGrp : storagePlantView?.DetermGrp || "",
              IuidRelevant : storagePlantView?.IuidRelevant || "",
              UidIea : storagePlantView?.UidIea || "",
              IuidType : storagePlantView?.IuidType || "",
              SortStockBasedOnSegment: storagePlantView?.SortStockBasedOnSegment || "",
              SegmentationStrategy: storagePlantView?.SegmentationStrategy || "",
              IssueUnit : storagePlantView?.IssueUnit || "",
              BatchMgmt: storagePlantView?.BatchMgmt || false,
            };
          });

          const descriptionData = item?.additionalData || [];
          const uomData = item?.unitsOfMeasureData || [];
          const eanData = item?.eanData || [];

          const toMaterialDesc = descriptionData?.length
            ? descriptionData?.map((desc) => {
                return {
                  MaterialDescriptionId: requestType === REQUEST_TYPE.EXTEND && initialReqScreen ? null : desc.MaterialDescriptionId || null,
                  Function: "INS",
                  Material: item.headerData?.materialNumber || "",
                  Langu: desc.language || "EN",
                  LanguIso: "",
                  MatlDesc: desc?.materialDescription || "",
                  DelFlag: false,
                };
              })
            : [
                {
                  MaterialDescriptionId: null,
                  Function: "INS",
                  Material: item.headerData?.materialNumber || "",
                  Langu: "EN",
                  LanguIso: "",
                  MatlDesc: item.headerData?.globalMaterialDescription || "",
                  DelFlag: false,
                },
              ];
          const toUomData = uomData?.length
            ? uomData?.map((uom) => {
                return {
                  UomId: requestType === REQUEST_TYPE.EXTEND && initialReqScreen ? null : uom?.UomId || null,
                  Function: "INS",
                  Material: item.headerData?.materialNumber || "",
                  AltUnit: uom?.aUnit || "",
                  AltUnitIso: "",
                  Numerator: uom?.yValue || "1",
                  Denominatr: uom?.xValue || "1",
                  EanUpc: uom?.eanUpc || "",
                  EanCat: uom.eanCategory || "",
                  Length: uom.length,
                  NetWeight: uom.netWeight || "",
                  Width: uom.width,
                  Height: uom.height,
                  UnitDim: uom.unitsOfDimension || "",
                  UnitDimIso: "",
                  Volume: uom.volume || "",
                  Volumeunit: uom.volumeUnit || "",
                  VolumeunitIso: "",
                  GrossWt: uom.grossWeight || "",
                  UnitOfWt: uom.weightUnit || "",
                  UnitOfWtIso: "",
                  DelFlag: false,
                  SubUom: "",
                  SubUomIso: "",
                  GtinVariant: "",
                  MaterialExternal: null,
                  MaterialGuid: null,
                  MaterialVersion: null,
                  NestingFactor: "",
                  MaximumStacking: null,
                  CapacityUsage: uom.capacityUsage,
                  EwmCwUomType: "",
                  MaterialLong: null,
                };
              })
            : [
                {
                  UomId: null,
                  Function: "INS",
                  Material: item.headerData?.materialNumber || "",
                  AltUnit: item?.payloadData?.["Basic Data"]?.basic?.BaseUom || "",
                  AltUnitIso: "",
                  Numerator: "1",
                  Denominatr: "1",
                  EanUpc: "",
                  EanCat: "",
                  Length: "",
                  Width: "",
                  Height: "",
                  UnitDim: "",
                  UnitDimIso: "",
                  Volume: item?.payloadData?.["Basic Data"]?.basic?.Volume || "",
                  Volumeunit: item?.payloadData?.["Basic Data"]?.basic?.VolumeUnit || "",
                  VolumeunitIso: "",
                  GrossWt: "",
                  UnitOfWt: item?.payloadData?.["Basic Data"]?.basic?.UnitOfWt || "",
                  UnitOfWtIso: "",
                  DelFlag: false,
                  SubUom: "",
                  SubUomIso: "",
                  GtinVariant: "",
                  MaterialExternal: null,
                  MaterialGuid: null,
                  MaterialVersion: null,
                  NestingFactor: "",
                  MaximumStacking: null,
                  CapacityUsage: "",
                  EwmCwUomType: "",
                  MaterialLong: null,
                },
              ];

          const toEanData = eanData?.length
            ? eanData?.map((ean) => {
                return { 
                  EanId: requestType === REQUEST_TYPE.EXTEND && initialReqScreen ? null : ean?.EanId || null,
                  Function: "INS", Material: item.headerData?.materialNumber || "", 
                  Unit: ean?.altunit || "",
                  EanUpc: ean?.eanUpc || "", 
                  EanCat: ean?.eanCategory || "", 
                  MainEan: ean.MainEan || false,
                  Au: ean.au || false,
                };
              })
            : null;
          const uniqueStorageCombinations = new Set();
          const toStorageViewData = item?.payloadData?.Tostroragelocationdata
            ? item?.payloadData?.Tostroragelocationdata
            : item.headerData?.views?.includes(MATERIAL_VIEWS?.STORAGE)
            ? orgData
                .filter((org) => {
                  if (!org?.plant?.value?.code || !org?.sloc?.value?.code) return false;
                  const combination = `${org.plant.value.code}-${org.sloc.value.code}`;
                  if (uniqueStorageCombinations.has(combination)) return false;
                  uniqueStorageCombinations.add(combination);
                  return true;
                })
                .map((org) => ({
                  Function: "INS",
                  Material: item?.headerData?.materialNumber || "",
                  Plant: org?.plant?.value?.code || "",
                  StgeLoc: org?.sloc?.value?.code || "",
                }))
            : [];
          const childRequestHeaderData = {
            ChildRequestId: item?.Tochildrequestheaderdata?.ChildRequestId || null,
            MaterialGroupType: item?.Tochildrequestheaderdata?.MaterialGroupType || null,
            TaskId: taskData?.taskId || null,
            Comments: remarks || userInput,
            TotalIntermediateTasks: item?.Tochildrequestheaderdata?.TotalIntermediateTasks || null,
            IntermediateTaskCount: item?.Tochildrequestheaderdata?.IntermediateTaskCount || null,
            ReqCreatedBy: item?.Tochildrequestheaderdata?.ReqCreatedBy || null,
            ReqCreatedOn: item?.Tochildrequestheaderdata?.ReqCreatedOn || null,
            ReqUpdatedOn: item?.Tochildrequestheaderdata?.ReqUpdatedOn || null,
            RequestType: item?.Tochildrequestheaderdata?.RequestType || null,
            RequestPrefix: item?.Tochildrequestheaderdata?.RequestPrefix || null,
            RequestDesc: item?.Tochildrequestheaderdata?.RequestDesc || null,
            RequestPriority: item?.Tochildrequestheaderdata?.RequestPriority || null,
            RequestStatus: item?.Tochildrequestheaderdata?.RequestStatus || null,
            CurrentLevel: taskData?.ATTRIBUTE_3 || "",
            CurrentLevelName: taskData?.ATTRIBUTE_4 || "",
            ParticularLevel: selectedLevel,
            TaskName: taskData?.taskDesc || "",
            ApproverGroup: taskData?.ATTRIBUTE_5 || "",
            // Index: 4
          };
          const payloadObject = {
            MaterialId: (requestType === REQUEST_TYPE.EXTEND && initialReqScreen) || (requestType === REQUEST_TYPE.CREATE && initialReqScreen) ? null : (taskData?.taskId || isReqBench || localTask?.ATTRIBUTE_5) && !key.includes("-") ? Number(key) : null,
            Flag: "",
            Function: "INS",
            Material: item?.headerData?.materialNumber || "",
            MatlType: item?.headerData?.materialType?.code || item?.headerData?.materialType || "",
            IndSector: item?.headerData?.industrySector?.code || item?.headerData?.industrySector || "",
            Comments: remarks || userInput,
            ViewNames: viewNamesString,
            Description: item?.headerData?.globalMaterialDescription || "",
            // Bom: item?.headerData?.Bom || "",
            // SourceList : item?.headerData?.sourceList || "",
            // Pir : item?.headerData?.PIR || "",
            Uom: item?.headerData?.Uom?.code ? item.headerData.Uom.code : item?.headerData?.Uom || "",
            Category: item?.headerData?.Category?.code ? item.headerData.Category.code : item?.headerData?.Category || "",
            Relation: item?.headerData?.Relation?.code ? item.headerData.Relation.code : item?.headerData?.Relation || "",
            Usage: item?.headerData?.Usage || "",
            CreationDate: (taskData?.requestId || isReqBench || reduxState?.payloadData?.RequestId) ? convertToDateFormat(reduxState?.payloadData?.ReqCreatedOn) : `/Date(${Date.now()}+0000)/`,
            EditId: null,
            ExtendId: null,
            MassCreationId: requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ? (taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.RequestId : "":null,
            MassEditId: item?.payloadData?.MassEditId || "",
            MassExtendId: requestType === REQUEST_TYPE.EXTEND || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? ((taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.RequestId : Boolean(requestState?.requestHeader?.requestId) ? requestState?.requestHeader?.requestId : requestId) : null,
            TaskId: taskData?.taskId || null,
            TaskName: taskData?.taskDesc || null,
            TotalIntermediateTasks: (taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.TotalIntermediateTasks : "",
            IntermediateTaskCount: (taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.IntermediateTaskCount : "",
            BasicView: item?.headerData?.views.includes(MATERIAL_VIEWS?.BASIC_DATA) || false,
            SalesView: item?.headerData?.views.includes(MATERIAL_VIEWS?.SALES) || false,
            MrpView: item?.headerData?.views.includes(MATERIAL_VIEWS?.MRP) || false,
            PurchaseView: item?.headerData?.views.includes(MATERIAL_VIEWS?.PURCHASING) || false,
            AccountView: item?.headerData?.views.includes(MATERIAL_VIEWS?.ACCOUNTING) || false,
            CostView: item?.headerData?.views.includes(MATERIAL_VIEWS?.COSTING) || false,
            WorkSchedView: item?.headerData?.views.includes(MATERIAL_VIEWS?.WORK_SCHEDULING_2) || false,
            WarehouseView: item?.headerData?.views.includes(MATERIAL_VIEWS?.WAREHOUSE) || false,
            ForecastView: false,
            PrtView: false,
            StorageView: item?.headerData?.views.includes(MATERIAL_VIEWS?.STORAGE) || false,
            QualityView: false,
            IsFirstChangeLogCommit: false,
            IsMarkedForDeletion: false,
            IsFirstCreate: !taskData?.taskId,
            creationTime: taskData?.createdOn ? convertToDateFormat(taskData?.createdOn) : null,
            dueDate: taskData?.criticalDeadline ? convertToDateFormat(taskData?.criticalDeadline) : null,
            ManufacturerId: item?.ManufacturerID || "",
            OrgData: orgData || [],
            Toclientdata: {
              ClientId: item?.headerData?.clientId || null,
              Function: "INS",
              Material: item?.headerData?.materialNumber || "",
              DelFlag: true,
              MatlGroup: item?.payloadData?.["Basic Data"]?.basic?.MatlGroup || "",
              Extmatlgrp: item?.payloadData?.["Basic Data"]?.basic?.Extmatlgrp || "",
              OldMatNo: item?.payloadData?.["Basic Data"]?.basic?.OldMatNo || "",
              BaseUom: item?.payloadData?.["Basic Data"]?.basic?.BaseUom || "",
              Document: item?.payloadData?.["Basic Data"]?.basic?.Document,
              DocType: item?.payloadData?.["Basic Data"]?.basic?.DocType,
              DocVers: item?.payloadData?.["Basic Data"]?.basic?.DocVers,
              DocFormat: item?.payloadData?.["Basic Data"]?.basic?.DocFormat,
              DocChgNo: item?.payloadData?.["Basic Data"]?.basic?.DocChgNo,
              PageNo: item?.payloadData?.["Basic Data"]?.basic?.PageNo,
              NoSheets: item?.payloadData?.NoSheets,
              ProdMemo: item?.payloadData?.ProdMemo,
              Pageformat: item?.payloadData?.DocFormat,
              SizeDim: item?.payloadData?.SizeDim,
              BaseUomIso: "",
              BasicMatl: item?.payloadData?.["Basic Data"]?.basic?.BasicMatl || "",
              StdDescr: item?.payloadData?.StdDescr,
              DsnOffice: item?.payloadData?.["Basic Data"]?.basic?.DsnOffice || "",
              PurValkey: item?.payloadData?.["Purchasing-General"]?.["Purchasing-General"]?.PurValkey || "", // to be added in purchasing general
              NetWeight: item?.payloadData?.["Basic Data"]?.basic?.NetWeight,
              UnitOfWt: item?.payloadData?.["Basic Data"]?.basic?.UnitOfWt || "",
              TransGrp: item?.payloadData?.["Sales-General"]?.["Sales-General"]?.TransGrp,
              XSalStatus: item?.payloadData?.["Sales-General"]?.["Sales-General"]?.XSalStatus,
              Svalidfrom: item?.payloadData?.["Sales-General"]?.["Sales-General"]?.Svalidfrom ? convertToDateFormat(item?.payloadData?.["Sales-General"]?.["Sales-General"]?.Svalidfrom) : null,
              Division: reduxState?.payloadData?.Division ? reduxState?.payloadData?.Division : item?.payloadData?.["Basic Data"]?.basic?.Division || "",
              ProdHier: item?.payloadData?.["Basic Data"]?.basic?.ProdHier || "",
              CadId: item?.payloadData?.["Basic Data"]?.basic?.CadId,
              VarOrdUn: item?.payloadData?.["Purchasing-General"]?.["Purchasing-General"]?.VarOrdUn,
              UnitOfWtIso: "",
              MatGrpSm: "",
              Authoritygroup: "",
              QmProcmnt: "",
              BatchMgmt: item?.payloadData?.["Sales-General"]?.["Sales-General"]?.BatchMgmt,
              SalStatus: "",
              Catprofile: "",
              // Minremlife: "",
              ShelfLife: "",
              StorPct: "",
              Hazmatprof: item?.payloadData?.["Basic Data"]?.basic?.Hazmatprof || "",
              HighVisc: item?.payloadData?.["Basic Data"]?.basic?.HighVisc,
              AppdBRec: "",
              Pvalidfrom: item?.payloadData?.["Basic Data"]?.basic?.Pvalidfrom ? convertToDateFormat(item?.payloadData?.["Basic Data"]?.basic?.Pvalidfrom) : null,
              EnvtRlvt: "",
              ProdAlloc: item?.payloadData?.["Basic Data"]?.basic?.ProdAlloc,
              PeriodIndExpirationDate: "",
              ParEff: true,
              Matcmpllvl: "",
              GItemCat: item?.payloadData?.["Basic Data"]?.basic?.GItemCat || "",
              CSalStatus: item?.payloadData?.["Basic Data"]?.basic?.CSalStatus || "",
              IntlPoPrice: item?.payloadData?.["Basic Data"]?.basic?.IntlPoPrice || "",
              PryVendor: item?.payloadData?.["Basic Data"]?.basic?.PryVendor || "",
              PlanningArea: item?.payloadData?.["Basic Data"]?.basic?.PlanningArea || "",
              PlanningFactor: item?.payloadData?.["Basic Data"]?.basic?.PlanningFactor || "",
              ReturnMatNumber: item?.payloadData?.["Basic Data"]?.basic?.ReturnMatNumber || "",
              ParentMatNumber: item?.payloadData?.["Basic Data"]?.basic?.ParentMatNumber || "",
              DiversionControlFlag: item?.payloadData?.["Basic Data"]?.basic?.DiversionControlFlag || "",
              MatGroupPackagingMat: item?.payloadData?.["Basic Data"]?.basic?.MatGroupPackagingMat || "",
              HazMatNo : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.HazMatNo || "",
              QtyGrGi : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.QtyGrGi || "",
              TempConds : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.TempConds || "",
              Container : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.Container || "",
              LabelType : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.LabelType || "",
              LabelForm  : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.LabelForm || "",
              AppdBRec : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.AppdBRec || "",
              Minremlife : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.Minremlife || "",
              ShelfLife : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.ShelfLife || "",
              PeriodIndExpirationDate : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.PeriodIndExpirationDate || "",
              RoundUpRuleExpirationDate : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.RoundUpRuleExpirationDate || "",
              StorPct : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.StorPct || "",
              SledBbd : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.SledBbd || "",
              SerializationLevel : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.SerializationLevel || "",
              ShelfLifeReqMax : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.ShelfLifeReqMax || "",
              ShelfLifeReqMin : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.ShelfLifeReqMin || "",
              MaturityDur : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.MaturityDur || "",
              StorPct : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.StorPct || "",
              StorConds : item?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.StorConds || "",
            },
            Toplantdata: toplantdatacreate,
            Tosalesdata: item?.headerData?.views.includes("Sales") ? salesResultArrayCreate : [],
            Tomaterialdescription: toMaterialDesc,
            Touomdata: toUomData,
            Toeandata: toEanData,
            Tostroragelocationdata: storageResultArrayCreate,
            ToClassification,
            Tomaterialerrordata: item?.Tomaterialerrordata || {},
            Toaccountingdata: item?.headerData?.views.includes(MATERIAL_VIEWS?.ACCOUNTING) ? accountingCostingArrayCreate : [],
            Tocontroldata: item?.headerData?.views.includes("Sales") ? ToControlDataForCreate : [],
            Torequestheaderdata: {
              RequestId: (taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.RequestId : Boolean(requestState?.requestHeader?.requestId) ? requestState?.requestHeader?.requestId : requestId,
              ReqCreatedBy: (taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.ReqCreatedBy : requestState?.requestHeader?.reqCreatedBy,
              ReqCreatedOn: convertToDateFormat((taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.ReqCreatedOn : requestState?.requestHeader?.reqCreatedOn),
              ReqUpdatedOn: convertToDateFormat((taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.ReqUpdatedOn : requestState?.requestHeader?.reqCreatedOn),
              RequestType: (taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.RequestType : requestState?.requestHeader?.requestType,
              Division: (taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.Division : requestState?.requestHeader?.Division,
              RequestPriority: (taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.RequestPriority : requestState?.requestHeader?.requestPriority,
              RequestDesc: (taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.RequestDesc : requestState?.requestHeader?.requestDesc,
              RequestStatus: (taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.RequestStatus : requestState?.requestHeader?.requestStatus,
              FirstProd: (taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.FirstProd : payloadData?.payloadData?.FirstProductionDate || null,
              LaunchDate: (taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.LaunchDate : payloadData?.payloadData?.LaunchDate || null,
              LeadingCat: (taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.LeadingCat : requestState?.requestHeader?.leadingCat,
              Region: (taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? reduxState?.payloadData?.Region : requestState?.requestHeader?.region,
              // "filterDetails": null,
              // "SapSystem": "S4",
              IsBifurcated: true,
              //"IncompleteChildTasks": "3"
            },
            Tochildrequestheaderdata : (taskData?.requestId || taskData?.ATTRIBUTE_1 || isReqBench || reduxState?.payloadData?.RequestId) ? childRequestHeaderData : {},
            
            Towarehousedata: toWarehouseData,
            changeLogData: createChangeLogData && Object.keys(createChangeLogData).length > 0 && 
              (createChangeLogData[item.headerData?.materialNumber] || createChangeLogData[item.headerData?.id])
              ? {
                  RequestId: createChangeLogData?.RequestId || item?.changeLogData?.RequestId,
                  ChangeLogId: item?.changeLogData?.ChangeLogId ?? null,
                  ChildRequestId: (createChangeLogData?.ChildRequestId || item?.changeLogData?.ChildRequestId) ?? null,
                  ...createChangeLogData[item.headerData?.materialNumber],
                  ...createChangeLogData[item.headerData?.id]
                }
              : {
                  RequestId: item?.changeLogData?.RequestId,
                  ChangeLogId: item?.changeLogData?.ChangeLogId ?? null,
                  ChildRequestId: item?.changeLogData?.ChildRequestId ?? null
                }
          };
          payloadArray.push(payloadObject);
        }
      }
    });

    return payloadArray;
  }

  return { createPayloadFromReduxState };
};

export default usePayloadCreation;
