import React, { useState } from 'react';
import {
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  FormLabel,
  Grid,
  Paper,
  Radio,
  RadioGroup,
  Typography,
} from '@mui/material';
import FieldCheckboxRadio from './FieldCheckboxRadio';

const ReusableFieldCatalog = ({
  fields, 
  heading, 
  childCheckedStates,
  setChildCheckedStates,
  childRadioValues,
  setChildRadioValues,
  onSubmitButtonClick,
  mandatoryFields,
DisabledChildCheck,
fieldVisibility}) => {
  const [value, setValue] = useState(null);
  const [parentChecked, setParentChecked] = useState(false);
  const [parentRadioValue, setParentRadioValue] = useState("");
  // const [childCheckedStates, setChildCheckedStates] = useState({});
  // const [childRadioValues, setChildRadioValues] = useState({});
  const [fieldStates, setFieldStates] = useState(
    fields.reduce((acc, field) => {
      acc[field] = {
        mandatory: mandatoryFields.includes(field), // Check if the field is mandatory
        hide: false,
        optional: false,
      };
      return acc;
    }, {})
  );

  const handleParentCheckboxChange = (event) => {
    const isChecked = event.target.checked;
    setParentChecked(isChecked);

    // Update child checkboxes without clearing them
    const newChildCheckedStates = { ...childCheckedStates };
    fields.forEach((field) => {
      if(!DisabledChildCheck[field]){
      if (isChecked && !newChildCheckedStates[field]) {
        newChildCheckedStates[field] = true;
      } else if (!isChecked && newChildCheckedStates[field]) {
        newChildCheckedStates[field] = false;
      }
    }
    });
    setChildCheckedStates(newChildCheckedStates);

    // Update child radio buttons without clearing them
    const newChildRadioValues = { ...childRadioValues };
    fields.forEach((field) => {
      if(!DisabledChildCheck[field]){
      if (isChecked) {
        newChildRadioValues[field] = parentRadioValue;
      } else {
        newChildRadioValues[field] = '';
      }
    }
    });
    setChildRadioValues(newChildRadioValues);
  };
  const handleParentRadioChange = (event) => {
    const radioValue = event.target.value;
    setParentRadioValue(radioValue);
    // Update child radio buttons
    setChildRadioValues((prevRadioValues) => {
      const newChildRadioValues = { ...prevRadioValues };
      fields.forEach((field) => {
        if(!DisabledChildCheck[field]){
        if (childCheckedStates[field]) {
          newChildRadioValues[field] = radioValue;
        }
      }
      });
      return newChildRadioValues;
    });
  };

  const handleChildCheckboxChange = (field) => (event) => {
    const isChecked = event.target.checked;
    setChildCheckedStates((prevCheckedStates) => ({
      ...prevCheckedStates,
      [field]: isChecked,
    }));
  
    // Check if the child field's visibility is not equal to '0'
    if (!isChecked && fieldVisibility[field] !== "0") {
      console.log(fieldVisibility[field],"hemlo")
      // Set the radio button value based on visibility mapping
      setChildRadioValues((prevRadioValues) => ({
        ...prevRadioValues,
        [field]: fieldVisibility[field],
      }));
    } 
    // else if (isChecked) {
    //   // If checked, set the radio button value to 'Mandatory' by default
    //   setChildRadioValues((prevRadioValues) => ({
    //     ...prevRadioValues,
    //     [field]: "Mandatory",
    //   }));
    // }
  };
  const handleChildRadioChange = (field) => (event) => {
    const radioValue = event.target.value;
    setChildRadioValues((prevRadioValues) => ({
      ...prevRadioValues,
      [field]: radioValue,
    }));
  };
  // ... your state and handler functions ...

  return (
    <>
      {/* Parent Field */}
      <Grid container sx={{backgroundColor:"#F1F0FF", border: "1px", borderRadius:"10px"}}>
                    <Grid item md={8} >
                     
                        <FormControlLabel
                        style={{marginLeft:"1.5px"}}
                      
                          control={
                            <Checkbox
                              checked={parentChecked}
                              onChange={handleParentCheckboxChange}
      
                            />
                          }
                          label={<Typography
                            sx={{
                              fontWeight: "700",
                              margin: "0px !important"
                            }}
                          >{heading}</Typography>}
                        />
                    </Grid>
                    <Grid item md={4}>
                      <FormControl 
                      sx={{
                            display: 'flex',
                            alignItems: 'center',
                            width: '100%',
                            fontWeight: 'bold',
                            padding: '2px 0', // Add some padding
                            '&:hover': {
                              backgroundColor: '#F1F0FF', // Add a hover background color
                            },
                          }}
                          component="fieldset">
                        <RadioGroup
                          row
                          value={parentRadioValue}
                          onChange={handleParentRadioChange}
                          sx={{fontSize:"10px !important"}}
                        >
                          <FormControlLabel
                          sx={{fontSize:"8px !important"}}
                            value="Mandatory"
                            control={<Radio />}
                            label="Mandatory"
                            disabled={!parentChecked}
                          />
                          <FormControlLabel
                          sx={{fontSize:"12px !important"}}
                            value="Hide"
                            control={<Radio />}
                            label="Hide"
                            disabled={!parentChecked}
                          />
                          <FormControlLabel
                          sx={{fontSize:"8px !important"}}
                            value="Optional"
                            control={<Radio />}
                            label="Optional"
                            disabled={!parentChecked}
                          />
                        </RadioGroup>
                      </FormControl>
                    </Grid>
                  </Grid>
      {/* Child Fields */}
      <Grid container md={12} sx={{ display: "flex" }}>
        {fields.map((field) => (
          
          <FieldCheckboxRadio
          key={field}
          field={field}
          checked={childCheckedStates[field] || false}
          isDisabled={DisabledChildCheck[field]||false}
          radioValue={childRadioValues[field] || ""}
          onCheckboxChange={handleChildCheckboxChange(field)}
          onRadioChange={handleChildRadioChange(field)}
          setChildCheckedStates={setChildCheckedStates}
          setChildRadioValues={setChildRadioValues}
          />
        ))}
      </Grid>
    </>
  );
};

export default ReusableFieldCatalog;
