//code cleanup for old export as excel
import xlsx from "json-as-xlsx";
import React, { useState, useEffect } from "react";
import {
  IconButton,
  Typography,
  Grid,
  Button,
  Menu,
  MenuItem,
  ListItemIcon,
  Tooltip,
  Box,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import {
  ArrowBack,
  MoreVert,
  Add,
  Refresh,
  ArrowForwardIos,
  IosShare,
} from "@mui/icons-material";
import { useDispatch, useSelector } from "react-redux";
import NewItem from "./NewItem";
import CustomCard from "./CustomCard";
import {
  setActivities,
  setEntities,
  setResponseMessage,
} from "../../../../../app/userManagementSlice";
import NotFound from "../NotFound";
import {
  activityFileHeading,
  downloadFile,
  entityFileHeading,
} from "../../Utility/file";
import UploadFile from "../UploadFile";
import { findApplicationById } from "../../Utility/basic";
import DeletionMessageBox from "../DeletionMessageBox";
import { SystemBasicInfo } from "./SystemDetails";
import SystemUserTable from "./SystemUserTable";
import Loading from "../Loading";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import {
  appHeaderHeight,
  cwAppDetailPageHeaderHeight,
  cwAppDetailPageTabHeight,
  sidebarWidth,
} from "../../Data/cssConstant";
import { iconButton_SpacingSmall } from "../../../../common/commonStyles";
import moment from "moment";
import { destination_IWA } from "../../../../../destinationVariables";
import ReusableIcon from "../../../../common/ReusableIcon";
import { saveExcel, saveExcelMultiSheets } from "../../../../../functions";
import ReusablePromptBox from "../../../../common/ReusablePromptBox/ReusablePromptBox";

const useStyle = makeStyles((theme) => ({
  applicationContainer: {
    width: "100%",
  },
  applicationHeaderContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    padding: "4px 10px",
    paddingBottom: 10,
    position: "sticky",
    top: 0,
    zIndex: 99,
    height: cwAppDetailPageTabHeight,
    // backgroundColor: theme.palette.background.paper,
  },
  applicationHeadeTitle: {
    fontSize: 14,
    fontWeight: 600,
    color: theme.palette.text.primary,
  },
  applicationHeadeAddButton: {
    marginLeft: 10,
    borderRadius: 50,
  },
  applicationContentContainer: {
    // height: `calc(100vh - ${appHeaderHeight} - ${cwAppDetailPageHeaderHeight} - ${cwAppDetailPageTabHeight} - 18px)`,
    padding: "0 4px",
  },
  applicationDetailHeaderContainer: {
    display: "flex",
    alignItems: "center",
    padding: 8,
    height: cwAppDetailPageTabHeight,
  },
  applicationDetailHeaderItem: {
    color: theme.palette.text.secondary,
    fontWeight: "normal",
    cursor: "pointer",
    maxWidth: 160,
    fontSize: 14,
    marginLeft: 8,
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
    padding: "0px 8px",
  },
  applicationDetailHeaderItemSelected: {
    color: theme.palette.primary.main,
    fontWeight: "bold",
    borderBottom: `2px solid ${theme.palette.primary.main}`,
  },
}));

function ApplicationDetail({ selectedApplication, setSelectedApplication }) {
  const classes = useStyle();
  const userReducerState = useSelector((state) => state.userReducer);
  const userManagement = useSelector((state) => state.userManagement);
  const modulesAndFeatures_Data = userManagement.entitiesAndActivities || {};
  const dispatch = useDispatch();
  const [params, setParams] = useState({ applicationId: selectedApplication });

  const [application, setApplication] = useState(null);
  const [anchorElMenu, setAnchorElMenu] = useState(null);
  const [anchorElSubMenu, setAnchorElSubMenu] = useState(null);
  const [operation, setOperation] = useState("");
  const [isUpdateItem, setIsUpdateItem] = useState(false);
  const [
    selectedApplicationDetailContentType,
    setSelectedApplicationDetailContentType,
  ] = useState("Basic Info");
  const [entities, setentities] = useState([]);
  const [activities, setactivities] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([
    // {
    //     "employeeId": null,
    //     "displayName": "Bhupendra Shekawat",
    //     "emailId": "<EMAIL>",
    //     "userId": "P000716",
    //     "roleName": [
    //         "Admin"
    //     ],
    //     "reqCreatedOn": null,
    //     "reqCreatedBy": null,
    //     "reqApprovedOn": null,
    //     "reqApprovedBy": null
    // },
  ]);

  const [entityFile, setEntityFile] = useState(null);
  const [activityFile, setActivityFile] = useState(null);

  const [onLoad, setOnLoad] = useState(false);
  const initialItem = { name: "", label: "", description: "" };
  const [item, setItem] = useState(initialItem);
  const [selectEntity, setSelectedEntity] = useState(null);

  const [addNewEntityDialog, setAddNewEntityDialog] = useState(false);
  const [addNewActivityDialog, setAddNewActivityDialog] = useState(false);

  const [openEntityFileDialog, setOpenEntityFileDialog] = useState(false);
  const [openActivityFileDialog, setOpenActivityFileDialog] = useState(false);

  const [deletingEntity, setDeletingEntity] = useState(null);
  const [deletingActivity, setDeletingActivity] = useState(null);

  const getEntities = () => {
    setOnLoad(true);
    const getAllEntitiesUrl = `/${destination_IWA}/api/v1/entities`;
    const getAllEntitiesRequestParams = {
      headers: {
        "Content-Type": "application/json",
      },
    };
    fetch(getAllEntitiesUrl, getAllEntitiesRequestParams)
      .then((res) => res.json())
      .then((data) => {
        dispatch(setEntities(data?.data || []));
        setOnLoad(false);
      })
      .catch((err) => {
        setOnLoad(false);
      });
  };

  const getActivities = () => {
    setOnLoad(true);
    const getAllActivitiesUrl = `/${destination_IWA}/api/v1/activities`;
    const getAllActivitiesRequestParams = {
      headers: {
        "Content-Type": "application/json",
      },
    };
    fetch(getAllActivitiesUrl, getAllActivitiesRequestParams)
      .then((res) => res.json())
      .then((data) => {
        dispatch(setActivities(data?.data || []));
        setOnLoad(false);
      })
      .catch((err) => {
        setOnLoad(false);
      });
  };
  const getSystemDetail = () => {
    setOnLoad(true);
    const getSystemDetailUrl = `/${destination_IWA}/api/v1/applications/read?id=${params?.applicationId}`;
    const getSystemDetailRequestParam = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    };
    fetch(getSystemDetailUrl, getSystemDetailRequestParam)
      .then((res) => res.json())
      .then((data) => {
        setOnLoad(false);
        setFilteredUsers(data?.data || []);
      })
      .catch((err) => {
        setOnLoad(false);
      });
  };

  const handleCreateEntity = () => {
    setOnLoad(true);
    const insertEntityUrl = `/${destination_IWA}/api/v1/entities`;
    const insertEntityPayload = {
      applicationId: params?.applicationId,
      description: item?.description,
      isActive: 1,
      isDeleted: 0,
      label: item?.label,
      name: item?.name,
      status: "Active",
    };
    const insertEntityRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(insertEntityPayload),
    };
    fetch(insertEntityUrl, insertEntityRequestParam)
      .then((res) => res.json())
      .then((entity_data) => {
        setOnLoad(false);
        setItem(initialItem);
        getEntities(
          () => {
            setOnLoad(true);
          },
          (data) => {
            dispatch(setEntities(data?.data || []));
            setOnLoad(false);

            dispatch(
              setResponseMessage({
                open: true,
                status: entity_data?.status ? "success" : "error",
                message: entity_data?.status
                  ? "Entity created successfully"
                  : "Something went wrong",
              })
            );
          },
          (err) => {
            setOnLoad(false);
          }
        );
      })
      .catch((err) => {
        setOnLoad(false);
      });
  };
  const handleCreateActivity = () => {
    setOnLoad(true);
    const insertActivityUrl = `/${destination_IWA}/api/v1/activities`;
    const insertActivityPayload = {
      description: item?.description,
      entityId: selectEntity,
      isActive: 1,
      isDeleted: 0,
      label: item?.label,
      name: item?.name,
      status: "Active",
    };
    const insertActivityRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(insertActivityPayload),
    };
    fetch(insertActivityUrl, insertActivityRequestParam)
      .then((res) => res.json())
      .then((activity_data) => {
        setOnLoad(false);
        setItem(initialItem);
        getActivities(
          () => {
            setOnLoad(true);
          },
          (data) => {
            dispatch(setActivities(data?.data || []));
            setOnLoad(false);

            dispatch(
              setResponseMessage({
                open: true,
                status: activity_data?.status ? "success" : "error",
                message: activity_data?.status
                  ? "Activity created successfully"
                  : "Something went wrong",
              })
            );
          },
          (err) => {
            setOnLoad(false);
          }
        );
      })
      .catch((err) => {
        setOnLoad(true);
      });
  };

  const handleUpdateEntity = () => {
    setOnLoad(true);
    const updateEntityUrl = `/${destination_IWA}/api/v1/entities/modify`;
    const updateEntityPayload = {
      id: item?.id,
      applicationId: params?.applicationId,
      description: item?.description,
      isActive: 1,
      isDeleted: 0,
      label: item?.label,
      name: item?.name,
      status: "Active",
    };
    const updateEntityRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateEntityPayload),
    };
    fetch(updateEntityUrl, updateEntityRequestParam)
      .then((res) => res.json())
      .then((data) => {
        setOnLoad(false);
        setItem(initialItem);
        dispatch(
          setEntities(
            userManagement?.entities?.map((entity) =>
              Number(entity?.id) === Number(item?.id)
                ? {
                    ...entity,
                    name: item?.name,
                    label: item.label,
                    description: item.description,
                  }
                : { ...entity }
            ) || []
          )
        );

        dispatch(
          setResponseMessage({
            open: true,
            status: data?.status ? "success" : "error",
            message: data?.status
              ? "Entity updated successfully"
              : "Something went wrong",
          })
        );
      })
      .catch((err) => {
        setOnLoad(false);
      });
  };
  const handleUpdateActivity = () => {
    setOnLoad(true);
    const updateActivityUrl = `/${destination_IWA}/api/v1/activities/modify`;
    const updateActivityPayload = {
      id: item?.id,
      description: item?.description,
      entityId: selectEntity,
      isActive: 1,
      isDeleted: 0,
      label: item?.label,
      name: item?.name,
      status: "Active",
    };
    const updateActivityRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateActivityPayload),
    };
    fetch(updateActivityUrl, updateActivityRequestParam)
      .then((res) => res.json())
      .then((data) => {
        setOnLoad(false);
        setItem(initialItem);
        dispatch(
          setActivities(
            userManagement?.activities?.map((activity) =>
              Number(activity?.id) === Number(item?.id)
                ? {
                    ...activity,
                    name: item?.name,
                    label: item.label,
                    description: item.description,
                  }
                : { ...activity }
            ) || []
          )
        );

        dispatch(
          setResponseMessage({
            open: true,
            status: data?.status ? "success" : "error",
            message: data?.status
              ? "Activity updated successfully"
              : "Something went wrong",
          })
        );
      })
      .catch((err) => {
        setOnLoad(false);
      });
  };

  const handleDeleteEntity = (entityId) => {
    setOnLoad(true);
    const disableEntityUrl = `/${destination_IWA}/api/v1/entities/deactivate`;
    const entityIdPayload = {
      id: entityId,
    };
    const disableEntityRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(entityIdPayload),
    };
    fetch(disableEntityUrl, disableEntityRequestParam)
      .then((res) => res.json())
      .then((data) => {
        setOnLoad(false);
        dispatch(
          setEntities(
            entities?.filter((entity) => entity?.id !== entityId) || []
          )
        );
        setDeletingEntity(null);
        setSelectedEntity(null);

        if (data.statusCode === 200) {
          promptAction_Functions.handleOpenPromptBox("SUCCESS", {
            message: `Module deleted successfully`,
            redirectOnClose: false,
          });
        } else {
          promptAction_Functions.handleOpenPromptBox("ERROR", {
            title: "Failed",
            message: `Module deletion failed`,
            severity: "danger",
            cancelButton: false,
          });
        }
      })
      .catch((err) => {
        setOnLoad(false);
        setDeletingEntity(null);
      });
  };
  const handleDeleteActivity = (activityId) => {
    setOnLoad(true);
    const disableActivityUrl = `/${destination_IWA}/api/v1/activities/deactivate`;
    const activityIdPayload = {
      id: activityId,
    };
    const disableActivityRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(activityIdPayload),
    };
    fetch(disableActivityUrl, disableActivityRequestParam)
      .then((res) => res.json())
      .then((data) => {
        setOnLoad(false);
        dispatch(
          setActivities(
            activities?.filter((activity) => activity?.id !== activityId) || []
          )
        );
        setDeletingActivity(null);

        if (data.statusCode === 200) {
          promptAction_Functions.handleOpenPromptBox("SUCCESS", {
            message: `Feature deleted successfully`,
            redirectOnClose: false,
          });
        } else {
          promptAction_Functions.handleOpenPromptBox("ERROR", {
            title: "Failed",
            message: `Feature deletion failed`,
            severity: "danger",
            cancelButton: false,
          });
        }
      })
      .catch((err) => {
        setOnLoad(false);
        setDeletingActivity(null);
      });
  };

  const uploadEntitiesFile = () => {
    if (!entityFile) {
      console.log("no file found");
      return;
    }
    setOnLoad(true);
    const url = `/${destination_IWA}/api/v1/entities/addEntitiesUsingCsv?applicationId=${params?.applicationId}`;
    let formData = new FormData();
    formData.append("file", entityFile);
    formData.append("name", entityFile.name);
    const requestParam = {
      method: "POST",
      headers: {},
      body: formData,
    };
    fetch(url, requestParam)
      .then((res) => {
        console.log(res);
      })
      .then((data) => {
        setOnLoad(false);
        setOpenEntityFileDialog(false);
        setEntityFile(null);
        getEntities();

        dispatch(
          setResponseMessage({
            open: true,
            status: "success",
            message: "Entity file uploaded successfully",
          })
        );
      })
      .catch((err) => {
        setOnLoad(false);
        dispatch(
          setResponseMessage({
            open: true,
            status: "error",
            message: "Something went wrong",
          })
        );
      });
  };
  const uploadActivitiesFile = () => {
    if (!activityFile) {
      console.log("no file found");
      return;
    }
    setOnLoad(true);
    const url = `/${destination_IWA}/api/v1/activities/addActivitiesUsingCsv`;
    let formData = new FormData();
    formData.append("file", activityFile);
    formData.append("file_name", activityFile.name);
    formData.append(
      "name",
      JSON.stringify(
        userManagement?.entities
          ?.filter(
            (entity) =>
              Number(entity?.applicationId) === Number(params?.applicationId)
          )
          ?.map((entity) => ({
            id: entity?.id,
            name: entity?.name,
          })) || []
      )
    );
    const requestParam = {
      method: "POST",
      headers: {},
      body: formData,
    };
    // console.log(formData);
    fetch(url, requestParam)
      .then((res) => {
        console.log(res);
        // return res.json();
      })
      .then((data) => {
        setOnLoad(false);
        setOpenActivityFileDialog(false);
        setActivityFile(null);
        getActivities();

        dispatch(
          setResponseMessage({
            open: true,
            status: "success",
            message: "Activity file uploaded successfully",
          })
        );
      })
      .catch((err) => {
        setOnLoad(false);
        dispatch(
          setResponseMessage({
            open: true,
            status: "error",
            message: "Something went wrong",
          })
        );
      });
  };

  const getApplicationById = (applicationId) => {
    const application = findApplicationById(
      applicationId,
      userManagement?.applications
    );
    return application || null;
  };

  // <-- Functions for exporting as excel -->

  // let modulesAndFeatures_Data2=[];
  // let loopCount=0;
  // let keys = Object.keys(modulesAndFeatures_Data)
  // let structureData = ()=>{
  //   try{
  //   for(let i=0;i<loopCount; i++){
  //     let d = {}
  //     keys.forEach(key=>{
  //       d[key] = modulesAndFeatures_Data[key]?.[i]??""
  //     })
  //     modulesAndFeatures_Data2.push(d)

  //   }
  // }catch(e){
  //   console.log(e)
  // }

  // }
  // structureData()

  let excelColumns = [];

  const functions_ExportAsExcel = {
    data: [],
    convertJsonToExcel: () => {
      //temoModulesData_Datatype = {
      //}
      let tempModulesData = {};
      userManagement.entities.forEach((i) => {
        tempModulesData[i.id] = i.name;
      });

      let modulesColumn = [
        {
          header: "Name",
          key: "name",
        },
        {
          header: "Label",
          key: "label",
        },
        {
          header: "Description",
          key: "description",
        },
      ];
      let featuresColumn = [
        {
          header: "Name",
          key: "name",
        },
        {
          header: "Label",
          key: "label",
        },
        {
          header: "Description",
          key: "description",
        },
        {
          header: "Module Name",
          key: "moduleName",
        },
      ];
      saveExcelMultiSheets([
        {
          fileName: `Modules And Features Datasheet-${moment(new Date()).format(
            "DD-MMM-YYYY"
          )}`,
          sheetName: `Modules`,
          columns: modulesColumn,
          rows: userManagement.entities,
        },
        {
          fileName: `Modules And Features Datasheet-${moment(new Date()).format(
            "DD-MMM-YYYY"
          )}`,
          sheetName: `Features`,
          columns: featuresColumn,
          rows: userManagement.activities?.map((item) => {
            return {
              ...item,
              moduleName: tempModulesData[item.entityId],
            };
          }),
        },
      ]);
      // xlsx(functions_ExportAsExcel.data, functions_ExportAsExcel.settings);
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
    downloadFile: () => {
      functions_ExportAsExcel.convertJsonToExcel();
    },
  };

  useEffect(() => {
    setentities(userManagement.entities);
    setactivities(userManagement.activities);
  }, [
    userManagement.entities,
    userManagement.apis,
    userManagement.activities,
    userManagement?.payloads,
  ]);

  useEffect(() => {
    getSystemDetail();

    setApplication(getApplicationById(params?.applicationId));
  }, [params?.applicationId]);

  //<-- Functions and variables for ReusablePromptBox *promptAction_Functions -->
  const [promptBoxState, setPromptBoxState] = useState({
    open: false,
    type: "",
    redirectOnClose: true,
    message: "",
    title: "",
    severity: "",
  });
  const [promptBoxScenario, setPromptBoxScenario] = useState("");

  const promptAction_Functions = {
    handleClosePromptBox: () => {
      setPromptBoxState((prev) => ({
        open: false,
        type: "",
        redirectOnClose: false,
        message: "",
        title: "",
        severity: "",
      }));
      getEntities();
      getActivities();
      setPromptBoxScenario("");
    },
    handleOpenPromptBox: (ref, data = {}) => {
      // SUCCESS,FAILURE
      let initialData = {
        open: true,
        title: "",
        message: "",
        okButton: true,
        cancelButton: true,
        okButtonText: "Ok",
        cancelText: "Cancel",
        type: "dialog",
      };
      if (ref === "SUCCESS") {
        initialData.type = "snackbar";
      }
      setPromptBoxScenario(ref);
      setPromptBoxState({
        ...initialData,
        ...data,
      });
    },
    handleCloseAndRedirect: () => {
      promptAction_Functions.handleClosePromptBox();
      // navigate("/purchaseOrder/management");
    },
    getCancelFunction: () => {
      switch (promptBoxScenario) {
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getCloseFunction: () => {
      switch (promptBoxScenario) {
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getOkFunction: () => {
      switch (promptBoxScenario) {
        case "DELETE":
          if (deletingEntity) {
            return handleDeleteEntity(deletingEntity);
          } else if (deletingActivity) {
            return handleDeleteActivity(deletingActivity);
          }
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getCloseAndRedirectFunction: () => {
      if (!promptBoxState.redirectOnClose) {
        return promptAction_Functions.handleClosePromptBox;
      }
      return promptAction_Functions.handleCloseAndRedirect;
    },
  };

  return (
    <div
      style={{
        width: "auto",
        // height: `calc(100vh - ${appHeaderHeight})`,
      }}
    >
      <Loading load={onLoad} />

      <ReusablePromptBox
        type={promptBoxState.type}
        promptState={promptBoxState.open}
        setPromptState={promptAction_Functions.handleClosePromptBox}
        onCloseAction={promptAction_Functions.getCloseFunction()}
        promptMessage={promptBoxState.message}
        dialogSeverity={promptBoxState.severity}
        dialogTitleText={promptBoxState.title}
        handleCancelButtonAction={promptAction_Functions.getCancelFunction()}
        cancelButtonText={promptBoxState.cancelText} //Cancel button display text
        showCancelButton={promptBoxState.cancelButton} //Enable Cancel button
        handleSnackBarPromptClose={promptAction_Functions.getCloseAndRedirectFunction()}
        handleOkButtonAction={promptAction_Functions.getOkFunction}
        okButtonText={promptBoxState.okButtonText}
        showOkButton={promptBoxState.okButton}
      />

      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          width: "100%",
          height: cwAppDetailPageHeaderHeight,
        }}
      >
        <div style={{ display: "flex", alignItems: "center" }}>
          <IconButton
            onClick={() => {
              setSelectedApplication(null);
              setParams(null);
            }}
            color="primary"
            aria-label="upload picture"
            component="label"
            sx={iconButton_SpacingSmall}
          >
            <ArrowCircleLeftOutlinedIcon
              sx={{
                fontSize: "25px",
                color: "#000000",
              }}
            />
          </IconButton>

          <Typography style={{ marginLeft: 8, fontSize: 14 }}>
            {application?.name}
          </Typography>
        </div>

        <div style={{ display: "flex", alignItems: "center" }}>
          <IconButton
            size="small"
            onClick={() => {
              getEntities();
              getActivities();
            }}
          >
            <Refresh style={{ fontSize: 20 }} />
          </IconButton>
          {selectedApplicationDetailContentType === "Modules" && (
            <>
              <IconButton
                size="small"
                aria-controls="menu"
                onClick={() => {
                  setOpenEntityFileDialog(true);

                  // setOpenActivityFileDialog(true);
                }}
              >
                <ReusableIcon
                  iconName={"FileUpload"}
                  iconSize={"20px"}
                  iconColor={"rgb(0,0,0,0.5)"}
                />
              </IconButton>

              <Tooltip title="Download ">
                <IconButton
                  sx={iconButton_SpacingSmall}
                  onClick={(e) => {
                    functions_ExportAsExcel.downloadFile();
                  }}
                >
                  <ReusableIcon
                    iconName={"FileDownload"}
                    iconSize={"20px"}
                    iconColor={"rgb(0,0,0,0.5)"}
                  />
                </IconButton>
              </Tooltip>
            </>
          )}
        </div>
      </div>

      <Grid
        container
        spacing={2}
        style={{ height: "100%", paddingTop: "0.5rem" }}
      >
        <Grid
          item
          xs={
            selectedApplicationDetailContentType === "Modules" ||
            selectedApplicationDetailContentType === "Apis"
              ? 6
              : 12
          }
          //  sm={5} md={4} lg={3}
        >
          <div className={classes.applicationDetailHeaderContainer}>
            <div style={{ display: "flex", alignItems: "center", flex: 1 }}>
              <Typography
                className={`${classes.applicationDetailHeaderItem} ${
                  selectedApplicationDetailContentType === "Basic Info" &&
                  classes.applicationDetailHeaderItemSelected
                }`}
                onClick={() => {
                  setSelectedApplicationDetailContentType("Basic Info");
                  setSelectedEntity(null);
                }}
              >
                Basic Info
              </Typography>

              <Typography
                className={`${classes.applicationDetailHeaderItem} ${
                  selectedApplicationDetailContentType === "Modules" &&
                  classes.applicationDetailHeaderItemSelected
                }`}
                onClick={() => {
                  setSelectedApplicationDetailContentType("Modules");
                  setSelectedEntity(null);
                }}
              >
                Modules
              </Typography>

              {/* <Typography
                className={`${classes.applicationDetailHeaderItem} ${
                  selectedApplicationDetailContentType === "Users" &&
                  classes.applicationDetailHeaderItemSelected
                }`}
                onClick={() => {
                  setSelectedApplicationDetailContentType("Users");
                  setSelectedEntity(null);
                }}
              >
                Users
              </Typography> */}
            </div>

            {selectedApplicationDetailContentType === "Modules" && (
              <Button
                size="small"
                variant="contained"
                onClick={() => {
                  if (selectedApplicationDetailContentType === "Modules") {
                    setAddNewEntityDialog(true);
                  } else {
                  }
                }}
                startIcon={<Add />}
                disabled={onLoad}
              >
                Add
              </Button>
            )}
          </div>

          {selectedApplicationDetailContentType === "Basic Info" && (
            <SystemBasicInfo
              selectedApplication={selectedApplication}
              height={`calc(100vh - ${appHeaderHeight} - ${cwAppDetailPageHeaderHeight} - ${cwAppDetailPageTabHeight} - 18px)`}
            />
          )}

          {selectedApplicationDetailContentType === "Modules" && (
            <>
              {entities?.length === 0 ? (
                onLoad ? null : (
                  <NotFound />
                )
              ) : (
                <div>
                  <Box
                    maxHeight="calc(100vh - 380px)"
                    overflow="auto"
                    marginTop="0.5rem"
                  >
                    {entities?.map(
                      (entity, index) =>
                        Number(params?.applicationId) ===
                          entity?.applicationId && (
                          <CustomCard
                            key={`${entity?.id}-${index}`}
                            item={entity}
                            application={application}
                            onDelete={(e) => {
                              e.stopPropagation();
                              // handleDeleteEntity(entity?.id);
                              setDeletingEntity(entity?.id);
                              promptAction_Functions.handleOpenPromptBox(
                                "DELETE",
                                {
                                  title: "Confirm Delete",
                                  message: `Do you want to delete the module?`,
                                  severity: "warning",
                                  cancelButton: true,
                                  okButton: true,
                                  okButtonText: "Delete",
                                }
                              );
                            }}
                            onClick={() => {
                              setSelectedEntity(entity?.id);
                            }}
                            selected={
                              params?.applicationId &&
                              selectEntity &&
                              entity?.id === Number(selectEntity)
                            }
                            load={onLoad}
                            onUpdate={(e) => {
                              e.stopPropagation();
                              setIsUpdateItem(true);
                              setAddNewEntityDialog(true);
                              setItem(entity);
                            }}
                          />
                        )
                    )}
                  </Box>
                </div>
              )}

              <NewItem
                open={addNewEntityDialog}
                onClose={() => {
                  setAddNewEntityDialog(false);
                  setItem(initialItem);
                  setSelectedEntity(null);
                  setIsUpdateItem(null);
                }}
                title={`${isUpdateItem ? "Update" : "New"} Modules`}
                label={true}
                newItem={item}
                setNewItem={setItem}
                onCreate={
                  isUpdateItem ? handleUpdateEntity : handleCreateEntity
                }
                error={
                  isUpdateItem
                    ? userManagement?.entities?.find(
                        (entity) =>
                          entity?.name === item?.name &&
                          Number(entity?.applicationId) ===
                            Number(params?.applicationId) &&
                          Number(entity?.id) !== Number(item?.id)
                      )
                    : userManagement?.entities?.find(
                        (entity) =>
                          entity?.name === item?.name &&
                          Number(entity?.applicationId) ===
                            Number(params?.applicationId)
                      )
                }
                errorMessage="Module name already exists"
              />

              <UploadFile
                open={openEntityFileDialog}
                onClose={() => {
                  setOpenEntityFileDialog(false);
                  setEntityFile(null);
                }}
                onUpload={() => {
                  uploadEntitiesFile();
                }}
                file={entityFile}
                setFile={setEntityFile}
                disableCondition={!entityFile}
                load={onLoad}
              />
            </>
          )}

          {/* {selectedApplicationDetailContentType === "Users" && (
            <SystemUserTable
              filteredUsers={filteredUsers}
              load={onLoad}
              // height={`calc(100vh - ${appHeaderHeight} - ${cwAppDetailPageHeaderHeight} - ${cwAppDetailPageTabHeight} - 18px)`}
            />
          )} */}
        </Grid>

        {selectedApplicationDetailContentType === "Modules" && (
          <>
            {selectEntity && (
              <Grid item xs={6}>
                <div className={classes.applicationHeaderContainer}>
                  <Typography variant="h6">Features</Typography>

                  <div style={{ display: "flex", alignItems: "center" }}>
                    {/* <Tooltip title="Download template">
                  <IconButton
                    size="small"
                    // color="primary"
                    style={{ marginLeft: 4 }}
                    disabled={onLoad}
                    onClick={(e) => {
                      downloadFile({
                        data: activityFileHeading,
                        fileName: "cw_features.csv",
                        fileType: "text/csv",
                      });
                    }}
                  >
                    <GetApp style={{ fontSize: 20 }} />
                  </IconButton>
                </Tooltip>

                <Tooltip title="Upload file">
                  <IconButton
                    size="small"
                    // color="primary"
                    style={{ marginLeft: 4 }}
                    disabled={onLoad}
                    onClick={() => {
                      setOpenActivityFileDialog(true);
                    }}
                  >
                    <Publish style={{ fontSize: 20 }} />
                  </IconButton>
                </Tooltip>

                <Tooltip title="Refresh">
                  <IconButton
                    size="small"
                    disabled={onLoad}
                    onClick={getActivities}
                  >
                    <Refresh style={{ fontSize: 20 }} />
                  </IconButton>
                </Tooltip> */}

                    <Button
                      size="small"
                      variant="contained"
                      onClick={() => setAddNewActivityDialog(true)}
                      startIcon={<Add />}
                      disabled={onLoad}
                    >
                      Add
                    </Button>
                  </div>
                </div>

                {activities?.length === 0 ? (
                  onLoad ? null : (
                    <NotFound />
                  )
                ) : (
                  <div>
                    <Box
                      maxHeight="calc(100vh - 380px)"
                      overflow="auto"
                      marginTop=".5rem"
                    >
                      {activities?.map(
                        (activity, index) =>
                          selectEntity === activity?.entityId && (
                            <CustomCard
                              key={`${activity?.id}-${index}`}
                              item={activity}
                              application={application}
                              onDelete={(e) => {
                                e.stopPropagation();
                                // handleDeleteActivity(activity?.id);
                                setDeletingActivity(activity?.id);
                                promptAction_Functions.handleOpenPromptBox(
                                  "DELETE",
                                  {
                                    title: "Confirm Delete",
                                    message: `Do you want to delete the feature?`,
                                    severity: "warning",
                                    cancelButton: true,
                                    okButton: true,
                                    okButtonText: "Delete",
                                  }
                                );
                              }}
                              onClick={() => {}}
                              load={onLoad}
                              onUpdate={(e) => {
                                e.stopPropagation();
                                setIsUpdateItem(true);
                                setAddNewActivityDialog(true);
                                setItem(activity);
                              }}
                            />
                          )
                      )}
                    </Box>
                  </div>
                )}

                <NewItem
                  open={addNewActivityDialog}
                  onClose={() => {
                    setAddNewActivityDialog(false);
                    setItem(initialItem);
                    setIsUpdateItem(null);
                  }}
                  title={`${isUpdateItem ? "Update" : "New"} Feature`}
                  newItem={item}
                  label={true}
                  setNewItem={setItem}
                  onCreate={
                    isUpdateItem ? handleUpdateActivity : handleCreateActivity
                  }
                  error={
                    isUpdateItem
                      ? userManagement?.activities?.find(
                          (activity) =>
                            activity?.name === item?.name &&
                            Number(activity?.entityId) ===
                              Number(selectEntity) &&
                            Number(activity?.id) !== Number(item?.id)
                        )
                      : userManagement?.activities?.find(
                          (activity) =>
                            activity?.name === item?.name &&
                            Number(activity?.entityId) === Number(selectEntity)
                        )
                  }
                  errorMessage="Feature name already exists"
                />

                <UploadFile
                  open={openActivityFileDialog}
                  onClose={() => {
                    setOpenActivityFileDialog(false);
                    setActivityFile(null);
                  }}
                  onUpload={() => {
                    uploadActivitiesFile();
                  }}
                  file={activityFile}
                  setFile={setActivityFile}
                  disableCondition={!activityFile}
                  load={onLoad}
                />
              </Grid>
            )}

            {!selectEntity && (
              <Grid
                item
                xs={6}
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  flexDirection: "column",
                  boxShadow: "0px 1px 3px rgba(112, 112, 112, 0.25)",
                }}
              >
                <img src="/Frame.png" alt="no select" />

                <Typography style={{ fontSize: 24, color: "#DFDFDF" }}>
                  Select an item to view
                </Typography>
              </Grid>
            )}
          </>
        )}
      </Grid>
    </div>
  );
}

export default ApplicationDetail;
