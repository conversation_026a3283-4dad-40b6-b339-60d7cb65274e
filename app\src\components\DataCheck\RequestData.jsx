import React, { useState, useEffect } from 'react';
import {
  Grid,
  FormControl,
  TextField,
  Select,
  MenuItem,
  Typography,
  Box,
  Button,
  Stack,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  styled,
  Chip,
  Tooltip,
  IconButton,
  BottomNavigation,
  Paper
} from '@mui/material';
import PreviewIcon from "@mui/icons-material/Preview";
import SummarizeOutlinedIcon from "@mui/icons-material/SummarizeOutlined";
import CloudDownloadIcon from '@mui/icons-material/CloudDownload';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import FilterListIcon from '@mui/icons-material/FilterList';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import DateRange from '@components/Common/DateRangePicker';
import { doAjax } from "../Common/fetchService";
import { destination_MaterialMgmt, destination_IDM } from "../../destinationVariables";
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';
import ReusableTable from "../Common/ReusableTable";
import { status } from '@components/ConfigCockpit/UserManagement/Data/data';
import CreateRequestDialog from './CreatRequestDialog';
import ReusableBackDrop from '@components/Common/ReusableBackDrop';
import { colors } from '@constant/colors';
import { DATA_CLEANSE_CONSTANTS, PAGESIZE, REQUEST_STATUS } from '@constant/enum';
import { outermostContainer, outermostContainer_Information } from '@components/Common/commonStyles';
import { useSnackbar } from '@hooks/useSnackbar';
import moment from 'moment';
import SingleSelectDropdown from '@components/Common/ui/dropdown/SingleSelectDropdown';
import { END_POINTS } from '@constant/apiEndPoints';
import { APP_END_POINTS } from '@constant/appEndPoints';

const RequestData = () => {
  const [value, setValue] = useState(null);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [isLoading, setIsLoading] = useState("");
  const navigate = useNavigate();
  const [rmSearchForm, setRmSearchForm] = useState({ createdOn: null });
  const [isDialogOpen, setDialogOpen] = useState(false);
  const [accordionExpanded, setAccordionExpanded] = useState(false);
  const [selectedModule, setSelectedModule] = useState("");
  const [rowData, setRowData] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [count, setCount] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(PAGESIZE?.TOP_SKIP);
  const dispatch = useDispatch();
  const { showSnackbar } = useSnackbar();

  const handleClear = () => {
    setSelectedCreatedBy([]);
    setSelectedMaterial([]);
    setSelectedDivision([]);
    setSelectedReqType([]);
    setSelectedOptions([]);
    let tempFilterData = {
      ...rbSearchForm,
      reqPriority: "",
    };
    dispatch(
      commonFilterUpdate({
        module: "RequestBench",
        filterData: tempFilterData,
      })
    );
    dispatch(commonFilterClear({ module: "RequestBench", days: 7 }));
    setClearClicked(true);
  };

  const font_Small = {
    fontSize: '12px',
    fontWeight: 500
  };

  const StyledAccordion = styled(Accordion)(({ theme }) => ({

    marginTop: "0px !important",
    border: `1px solid ${colors.primary.border}`,
    borderRadius: '8px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
    '&:not(:last-child)': {
      borderBottom: 0,
    },
    '&:before': {
      display: 'none',
    },
  }));

  const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
    minHeight: "2rem !important",
    margin: "0px !important",
    backgroundColor: colors.primary.ultraLight,
    borderRadius: '8px 8px 0 0',
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      backgroundColor: `${colors.primary.light}20`,
    },
  }));

  const ActionButton = styled(Button)({
    borderRadius: '4px',
    padding: '4px 12px',
    textTransform: 'none',
    fontSize: '0.875rem',
  });

  const FilterContainer = styled(Grid)({
    padding: '0.75rem',
    gap: '0.5rem',
  });

  const ButtonContainer = styled(Grid)({
    display: 'flex',
    justifyContent: 'flex-end',
    paddingRight: '0.75rem',
    paddingBottom: '0.75rem',
    paddingTop: '0rem',
    gap: '0.5rem',

  });

  const LabelTypography = styled(Typography)({
    fontSize: '0.75rem',
    color: colors.primary.dark,
    marginBottom: '0.25rem',
    fontWeight: 500,
  });

  useEffect(() => {
    fetchRequestList()
  }, [])

  useEffect(() =>
    setTableData([...rowData]
    ), [rowData]);

  const fetchRequestList = (key = "") => {
    const payload = {
      "page": key === "pagination" ? page : 0,
      "size": key === "pagination" ? pageSize : 100,
      "orderBy": "creationDate"
    }
    setIsLoading(true)
    const hSuccess = (data) => {
      setRowData(data?.body || [])
      setIsLoading(false)
    }
    const hError = (error) => {
      setRowData([])
      setIsLoading(false)
    };
    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS?.DATA_CLEANSE_APIS?.CLEANSING_REQ}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  }

  const handleDownloadPdf = (requestId) => {
    setBlurLoading(true);
    let payload = { requestId }

    const hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute("download", `${requestId}_Data Cleanse.pdf`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");
      showSnackbar(`${requestId}${DATA_CLEANSE_CONSTANTS?.EXPORT_SUCCESS}`, "success")

    };
    const hError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      showSnackbar(`Failed exporting ${requestId}_Data Cleanse.pdf`, "error")
    };

    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS?.DATA_CLEANSE_APIS?.DOWNLOAD_PDF}`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  const moduleOptions = [
    { code: 'Material', desc: "" },
    { code: 'Cost Center', desc: "" },
    { code: 'Profit Center', desc: "" },
    { code: 'General Ledger', desc: "" },
    { code: 'Hierarchy', desc: "" }
  ];

  const handleSubmit = (payloadData) => {
    let payload = {
      "RequestId": "",
      "ObjectCount": 0,
      "UserId": "<EMAIL>",
      "InitiatedOn": moment(new Date()).format("YYYY-MM-DDThh:mm:ss") ?? "",
      "Status": "In-Progress",
      "Top": payloadData?.top?.code || "10",
      "Skip": "0",
      "FromDate": moment(payloadData?.createdOn[0]).format("YYYY-MM-DDThh:mm:ss") ?? "",
      "ToDate": moment(payloadData?.createdOn[1]).format("YYYY-MM-DDThh:mm:ss") ?? "",
      "DtName": "MDG_MAT_MATERIAL_FIELD_CONFIG",
      "DtVersion": "v4",
      "DtRegion": "US",
      "DtScenario": "Create",
      "DtMaterialType": "FERT"
    };
    const hSuccess = (data) => {
      showSnackbar(data?.message, "success")
      fetchRequestList()
      setDialogOpen(false)
    }
    const hError = (error) => {
      showSnackbar(error.message || DATA_CLEANSE_CONSTANTS.ERROR, "error")

    };
    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS?.DATA_CLEANSE_APIS?.INITIATE_DATA_QUALITY_CHECK}`,
      "post",
      hSuccess,
      hError,
      [payload]
    );
  };

  const createStatusValueCell = (fieldName, displayName) => ({
    field: fieldName,
    headerName: displayName,
    editable: false,
    flex: 1.4,
    renderCell: (cellValues) => {
      return (
        <Chip
          sx={{
            justifyContent: "flex-start",
            borderRadius: "4px",
            color: "#000",
            width: "100%",
            minWidth: "4.6rem",
            fontSize: "12px",
            background:
              colors.statusColorMap[
              cellValues.row.status.toLowerCase().replace(/[^a-z0-9]/gi, '')
              ] || colors.statusColorMap.default
          }}
          label={cellValues.row.status}
        />
      );
    },
  })

  const handleDate = (date) => {
    setRmSearchForm({ ...rmSearchForm, createdOn: date });
  };

  const handleInputChange = (e) => {
    if (e.target.value !== null) {
      const fieldName = e.target.name;
      const tempRequestId = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        [fieldName]: tempRequestId,
      };

      setRmSearchForm(tempFilterData)
    }
  };

  const handleSearchAction = (value) => {
    if (!value) {
      setTableData([...rowData]);
      setCount(rowData?.length);
      return;
    }
    const selected = rowData?.filter((row) => {
      let rowMatched = false;
      let keys = Object.keys(row);

      for (let k = 0; k < keys.length; k++) {
        rowMatched = !row[keys[k]] ? false : row?.[keys?.[k]] && row?.[keys?.[k]].toString().toLowerCase()?.indexOf(value?.toLowerCase()) != -1;

        if (rowMatched) break;
      }
      return rowMatched;
    });

    setTableData([...selected]);
    setCount(selected?.length);
  };

  const handlePageSizeChange = (event) => {

    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);

  };
  const handlePageChange = (event, newPage) => {
    setPage(isNaN(newPage) ? 0 : newPage);
  };

  useEffect(() => {
    if (page !== 0) {
      const requiredDataCount = pageSize * (page + 1);
      if (requiredDataCount > rowData.length && rowData.length % pageSize === 0) {
        fetchRequestList("pagination");
      }
    }
  }, [page]);

  const columns = [
    { field: 'requestId', headerName: 'Request ID', flex: 1.5 },
    {
      field: 'module',
      headerName: 'Module',
      flex: 1,
      renderCell: (params) => {
        return params.row.module || 'Material';
      }
    },
    { field: 'userId', headerName: 'Initiated By', flex: 1 },
    {
      field: 'creationDate', headerName: 'Initiated On', flex: 1,
      renderCell: (params) => {
        return moment(params?.row?.initiatedOn).format("YYYY-MM-DD hh:mm") ?? "";
      }
    },
    { field: 'objectCount', headerName: 'Object Count', flex: 1 },
    createStatusValueCell(status, "Status"),
    {
      field: 'actions', headerName: 'Actions', flex: 1, headerAlign: "center", align: "center",
      renderCell: (params) => {
        return (
          <div>
            <Tooltip title={"Report"}>
              <IconButton
                disabled={params?.row?.status === `${REQUEST_STATUS.DRAFT}`}
                onClick={() => {
                  if (params?.row?.objectCount) {
                    handleDownloadPdf(params?.row?.requestId)
                    return;
                  }
                  showSnackbar(DATA_CLEANSE_CONSTANTS?.FAILED_FETCHING_DATA, "error")
                }}
              >
                <CloudDownloadIcon
                  sx={{
                    color: params?.row?.status === `${REQUEST_STATUS.DRAFT}` ? "#808080" : "#ffd93f",
                  }}
                />
              </IconButton>
            </Tooltip>
            <Tooltip title={"View Requests"}>
              <IconButton
                disabled={params?.row?.status === `${REQUEST_STATUS.DRAFT}`}
                onClick={() => {
                  if (params?.row?.objectCount) {
                    navigate(`${APP_END_POINTS?.DATA_CHECK}?requestId=${params?.row?.requestId}`, {
                      state: params?.row
                    })
                    return;
                  }
                  showSnackbar(DATA_CLEANSE_CONSTANTS?.FAILED_FETCHING_DATA, "error")
                }}
              >
                <PreviewIcon sx={{ fontSize: "20px", color: params.row.status === REQUEST_STATUS.DRAFT ? "#808080" : `${colors.blue.indigo}` }}
                />
              </IconButton>
            </Tooltip>
          </div>
        )
      },
    }
  ];
  return (
    <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
      <Stack spacing={1}>
        <Grid container mt={0} sx={outermostContainer_Information}>
          <Grid item md={4}>
            <Typography variant="h3">
              <strong>{DATA_CLEANSE_CONSTANTS.DATA_CLEANSE}</strong>
            </Typography>
            <Typography variant="body2" color="#777">
              {"This view displays the list of Data Cleanse Requests"}
            </Typography>
          </Grid>
        </Grid>

        <Grid container sx={{ paddingBottom: '10px' }}>
          <Grid item md={12}>
            <StyledAccordion
              expanded={accordionExpanded}
              onChange={(event, isExpanded) => setAccordionExpanded(isExpanded)}
              sx={{ mb: 2 }}
            >
              <StyledAccordionSummary
                expandIcon={<ExpandMoreIcon sx={{ fontSize: '1.25rem', color: colors.primary.main }} />}
                aria-controls="panel1a-content"
                id="panel1a-header"
              >
                <FilterListIcon sx={{ fontSize: '1.25rem', marginRight: 1, color: colors.primary.main }} />
                <Typography
                  sx={{
                    fontSize: '0.875rem',
                    fontWeight: 600,
                    color: colors.primary.dark,
                  }}
                >
                  {DATA_CLEANSE_CONSTANTS.FILTER}
                </Typography>
              </StyledAccordionSummary>
              <AccordionDetails sx={{ padding: 0 }}>
                <FilterContainer>
                  <Grid
                    container
                    rowSpacing={1}
                    spacing={2}
                    alignItems="center"
                    sx={{ padding: "0rem 1rem 0.5rem" }}
                  >
                    <Grid item md={2}>
                      <LabelTypography sx={font_Small}>{"Module"}</LabelTypography>
                      <FormControl size="small" fullWidth>
                        <SingleSelectDropdown
                          options={moduleOptions}
                          value={selectedModule}
                          onChange={(newValue) => {
                            setSelectedModule(newValue)
                          }
                          }
                          placeholder={"Select Module"}
                          disabled={false}
                          minWidth="90%"
                          listWidth={210}
                        />
                      </FormControl>
                    </Grid>
                    <Grid item md={2}>
                      <LabelTypography sx={font_Small}>{"Request ID"}</LabelTypography>
                      <TextField
                        name="requestId"
                        fullWidth
                        variant="outlined"
                        placeholder="Request ID"
                        size="small"
                        InputLabelProps={{ shrink: true }}
                        onChange={handleInputChange}
                        value={rmSearchForm?.requestId}
                      />
                    </Grid>
                    <Grid item md={2}>
                      <LabelTypography sx={font_Small}>{"Created On"}</LabelTypography>
                      <FormControl fullWidth sx={{ padding: 0 }}>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                          <DateRange
                            handleDate={handleDate}
                            date={rmSearchForm?.createdOn}
                          />
                        </LocalizationProvider>
                      </FormControl>
                    </Grid>
                  </Grid>

                </FilterContainer>
                <ButtonContainer>
                  <ActionButton
                    variant="outlined"
                    size="small"
                    startIcon={<ClearIcon sx={{ fontSize: '1rem' }} />}
                    onClick={handleClear}
                    sx={{ borderColor: colors.primary.main, color: colors.primary.main }}
                  >
                    {"Clear"}
                  </ActionButton>

                  <ActionButton
                    variant="contained"
                    size="small"
                    startIcon={<SearchIcon sx={{ fontSize: '1rem' }} />}
                    onClick={() => handleSubmit(rmSearchForm)}
                    sx={{ backgroundColor: colors.primary.main }}
                  >
                    {"Search"}
                  </ActionButton>
                </ButtonContainer>
              </AccordionDetails>
            </StyledAccordion>
          </Grid>
        </Grid>
        <ReusableTable
          isLoading={isLoading}
          //   paginationLoading={isLoading}
          module={"DataCleanse"}
          width="100%"
          title={"Data Cleanse Requests"}
          rows={tableData || []}
          columns={columns}
          onSearch={(value) => handleSearchAction(value)}
          onRefresh={fetchRequestList}
          page={page}
          showSearch={true}
          showRefresh={true}
          //   showSelectedCount={true}
          //   showExport={true}
          pageSize={pageSize}
          rowCount={count ?? tableData?.length ?? 0}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          getRowIdValue="id"
          hideFooter={true}
          //   tempheight={"calc(100vh - 320px)"}
          //   checkboxSelection={true}
          //   disableSelectionOnClick={true}
          //   isRowSelectable={isRowSelectable}
          //   status_onRowDoubleClick={true}
          //   onRowsSelectionHandler={onRowsSelectionHandler}
          //   
          //   // setShowWork={setShowWork}
          //   stopPropagation_Column={"action"}
          showCustomNavigation={true}
        />
      </Stack>
      <Paper
        sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
        elevation={2}
      >
        <BottomNavigation
          className="container_BottomNav"
          showLabels
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 1,
          }}
          value={value}
          onChange={(newValue) => {
            setValue(newValue);
          }}
        >
          <Button
            size="small"
            variant="contained"
            onClick={() => {
              setDialogOpen(true);
            }}
          >
            {DATA_CLEANSE_CONSTANTS.CLEANSE_REQUEST}
          </Button>
        </BottomNavigation>
      </Paper>
      {/* Create Request Dialog */}
      <CreateRequestDialog
        open={isDialogOpen}
        onClose={() => setDialogOpen(false)}
        handleSubmit={handleSubmit}
        title={DATA_CLEANSE_CONSTANTS.NEW_REQUEST}
        submitButtonText={DATA_CLEANSE_CONSTANTS.INITIATE_REQUEST}
        isLoading={false}
      />

      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />
    </div>
  );
};
export default RequestData;