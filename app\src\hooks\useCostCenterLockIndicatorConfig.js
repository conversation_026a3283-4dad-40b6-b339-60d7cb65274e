import { changeTemplateDT } from "@app/tabsDetailsSlice";
import { doAjax } from "@components/Common/fetchService";
import React from "react";
import { useSelector, useDispatch } from "react-redux";
import { destination_IDM } from "../destinationVariables";
import { END_POINTS } from "@constant/apiEndPoints";
import { API_CODE } from "@constant/enum";
import { setDropDown } from "../app/dropDownDataSlice";
import useLogger from "./useLogger";
import {setLockIndicatorData} from "../app/payloadSlice";

const useCostCenterLockIndicatorConfig = () => {

  const currentHash = window.location.hash; 
  const parts = currentHash.split("/");
  const activeLocation = parts[parts.length - 1]; 

  console.log("activeLocation", activeLocation);

  const { customError } = useLogger();
  const initialPayload = useSelector((state) => state?.costCenter?.payload?.requestHeaderData);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const dispatch = useDispatch();

  // const module ="Cost Center";
// console.log("moduleeee",module)
  const getLockIndicator = () => {
    const payload = {
      decisionTableId: null,
      decisionTableName: "MDG_CC_CONTROL_TAB_FIELDS_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
        //   "MDG_CONDITIONS.MDG_MODULE": module,
          // "MDG_CONDITIONS.MDG_MAT_TEMPLATE":
          //   initialPayload?.TemplateName || templateName,
        //   "MDG_CONDITIONS.MDG_MAT_ROLE": "REQ_DISPLAY",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
  const hSuccess = (data) => {
    
    if (data.statusCode === API_CODE.STATUS_200) {
        console.log("lockIndicatorList",data)
        const transformedData = data?.data?.result?.[0]?.conditions.map((condition, index) => {
            console.log("conditionnn",condition);
        const key = condition?.["MDG_CONDITIONS.MDG_CC_COST_CENTER_CATEGORY"];
        const value = data?.data?.result?.[0]?.MDG_CC_CONTROL_TAB_FIELDS_ACTION_TYPE?.[index]?.MDG_CC_CONTROL_TAB_FIELDS.split(",").map((item) => item.trim()) || "";
        return { [key]: value };
        });
        console.log("transformedDataLI",transformedData)
        dispatch(setLockIndicatorData(transformedData));
    } else {
      customError("Failed to fetch data");
      return [];
    }
  };

    const hError = (error) => {
      customError(error);
    };

    const endpoint =
      applicationConfig.environment === "localhost"
        ? END_POINTS.INVOKE_RULES.LOCAL
        : END_POINTS.INVOKE_RULES.PROD;

    doAjax(`/${destination_IDM}${endpoint}`, "post", hSuccess, hError, payload);
  };

  return { getLockIndicator };
};

export default useCostCenterLockIndicatorConfig;
