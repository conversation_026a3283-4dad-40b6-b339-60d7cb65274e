import {
  <PERSON><PERSON>,
  Icon<PERSON>utton,
  <PERSON><PERSON>ield,
  Typo<PERSON>,
  Box,
  Card,
} from "@mui/material";
import { Stack, display } from "@mui/system";
import SendIcon from "@mui/icons-material/Send";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { getTimestamp } from "../../../functions";
// import { destination_CommonUtils } from "../../../destinationVariables";
import { doAjax } from "../fetchService";

const CommentsComponent = ({poNumber, artifactName, artifactId, promptAction_Functions,module='' }) => {
  let userData = useSelector((state) => state.userManagement.userData);
  const [commentPayload, setcommentPayload] = useState({
    comment: "",
  });
  const [commentsData, setcommentsData] = useState([
  ]);
  let onChangeComment = (e) => {
    setcommentPayload((prev) => ({
      ...prev,
      comment: e.target.value,
    }));
  };
  const getComments = () => {
    if(artifactName === 'Purchase Order'){
      let hSuccess = (data) => {
        var commentRows = [];
        data.commentDtoList.forEach((cmt) => {
          var tempRow = {
            id: cmt.commentId,
            comment: cmt.comment,
            user: cmt.createdBy,
            createdAt: cmt.createdAt
          };
          commentRows.push(tempRow);
        });
        setcommentsData(commentRows?.reverse());
      }
      // artifactId &&
        // doAjax(`/${destination_CommonUtils}/comment/getCommentsByPoNumber/${artifactId}`, 'get', hSuccess)
      
    }else{
    //check if comments exist, if yes push it to commentsData
    let hSuccess = (data) => {
      var commentRows = [];
      data.commentDtoList.forEach((cmt) => {
        var tempRow = {
          id: cmt.commentId,
          comment: cmt.comment,
          user: cmt.createdBy,
          createdAt: cmt.createdAt
        };
        commentRows.push(tempRow);
      });
      setcommentsData(commentRows?.reverse());
    }
    // artifactId &&
      // doAjax(`/${destination_CommonUtils}/comment/getCommentsByRequestId/${artifactId}`, 'get', hSuccess)
    }

  };
  let addComment = async () => {
    if (commentPayload.comment) {
      // setcommentsData(prev => ([...prev,{
      //   ...commentPayload,
      //   user:userData.emailId,
      //   createdAt: new Date()
      // }]))  
      handleSubmitComment()
    }
  }
  const handleSubmitComment = () => {

    const formData = new FormData();
    var comments = {
      requestId: artifactId,
      comment: commentPayload.comment,
      commentArtifactType: artifactName,
      createdBy: userData?.emailId,
      roleName: userData?.role,
    };
    if(poNumber) {
        comments.poNumber = poNumber
      } 
    formData.append("comments", JSON.stringify(comments));
    const hSuccess = (data) => {
      promptAction_Functions.handleOpenPromptBox("SUCCESS", {
        message: `Comment for confirmation of PO Posted successfully`,
        redirectOnClose: false,
      });

      setcommentPayload({ comment: '', user: '' })

      getComments();
    };
    const hError = (error) => {
      promptAction_Functions.handleOpenPromptBox("ERROR", {
        title: "Failed",
        message: `Comment for confirmation of PO Posted failed`,
        severity: "danger",
        cancelButton: false,
      });
    };
    doAjax(
      // `/${destination_CommonUtils}/comment/saveCommentWithoutFile`,
      "postformdata",
      hSuccess,
      hError,
      formData
    );
  };
 

  useEffect(() => {
    // getComments()
  }, [])
  useEffect(() => {
    getComments()
  }, [])

  return (
    <div
      style={{
        width: "100%",
      }}
    >
      <div
        style={{
          width: "max-content",
        }}
      >
        <Stack>
          <Typography variant="h5">Remarks</Typography>
        </Stack>
        <Stack
          sx={{
            width: "max-content",
            marginTop: "1rem",
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "start",
              width: "max-content",
            }}
          >
            <div
              className="btn-mr"
              sx={{
                width: "max-content",
              }}
            >
              <Avatar
                sx={{
                  width: 36,
                  height: 36,
                  color: "#3730c7",
                  backgroundColor: "#eae9ff",
                  fontSize: "14px",
                }}
              >
                {`${userData.firstName?.charAt(0)}${userData.lastName?.charAt(
                  0
                )}`}
              </Avatar>
            </div>
            <div
              style={{
                minWidth: "38rem",
              }}
            >
              <TextField
                fullWidth
                placeholder="Add a new comment"
                variant="outlined"
                size="small"
                name="comment"
                onChange={onChangeComment}
                value={commentPayload.comment}
                inputProps={{ maxLength: 300 }}
              ></TextField>
            </div>
            <div className="btn-ml">
              <IconButton onClick={addComment}>
                <SendIcon color={commentPayload.comment && 'primary'} />
              </IconButton>
            </div>
          </Box>
        </Stack>
        <Stack
          sx={{

            width: "100%",
          }}
        >
          {commentsData.map((item) => {
            console.log(item)
            return (
              <>
                <Card
                  elevation={0}
                  sx={{
                    border: 1,
                    borderColor: "#C4C4C4",
                    borderRadius: "8px",
                    marginTop: ".5rem",
                  }}
                >
                  <Box
                    sx={{
                      padding: "1rem",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      flexDirection: "column",
                      width: "inherit",
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        textAlign: "start",
                        width: "100%",

                      }}
                    >
                      {item.comment}
                    </Typography>

                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "end",
                        justifyContent: "space-between",
                        width: "100%",
                        marginTop: '.5rem'
                      }}
                    >
                      <div
                        className="btn-mr"
                        style={{
                          width: "max-content",
                          display: "flex",
                          flexDirection: 'row',
                          justifyContent: "start",
                          alignItems: 'center'
                        }}
                      >
                        <Avatar
                          className="btn-mr"
                          sx={{
                            width: 24,
                            height: 24,
                            color: "#3730c7",
                            backgroundColor: "#eae9ff",
                            fontSize: "8px",
                          }}
                        >
                          {`${item.user?.split(" ")[0]?.charAt(0)}`}
                          {`${item.user?.split(" ")[1]?.charAt(0)}`}
                        </Avatar>
                        <Typography
                          sx={{
                            fontSize: "12px",

                            color: " #757575",
                            fontWeight: "700",
                          }}
                        >
                          {item.user}
                        </Typography>
                      </div>

                      <Typography
                        sx={{
                          textAlign: "right",
                          color: " #757575",
                          fontWeight: "700",
                          fontSize: "12px",
                        }}
                      >
                        {getTimestamp(item.createdAt)}
                      </Typography>
                    </Box>
                  </Box>
                </Card>
              </>
            );
          })}
        </Stack>
      </div>
    </div>
  );
};

export default CommentsComponent;
