import React, { useEffect, useState } from "react";
import {
  <PERSON>ton,
  <PERSON>alog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  MenuItem,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
  Checkbox,
  Stack,
  Box,
  Tab,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import CloseIcon from "@mui/icons-material/Close";
import { v4 as uuidv4 } from "uuid";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import {
  Add,
  Close,
  Remove,
  CheckBoxOutlineBlank,
  CheckBox,
} from "@mui/icons-material";
import { useDispatch, useSelector } from "react-redux";
import { findEntityById, findRoleById } from "../../Utility/basic";
import Loading from "../Loading";
import { findApplicationById, findUserById } from "../../Utility/basic";
import { Autocomplete } from "@mui/material";
import {
  setRoles,
  setResponseMessage,
  setEntitiesAndActivities,
} from "../../../../../app/userManagementSlice";
import DeletionMessageBox from "../DeletionMessageBox";
import { applicationIds } from "../../Utility/config";
import {
  appHeaderHeight,
  buttonHeight,
  crossIconContainerHeight,
  roleDetailPageCss,
  rolePageHeaderHeight,
} from "../../Data/cssConstant";
import { font_Small } from "../../../../common/commonStyles";
import ReusableTable from "../../../../common/ReusableTable";
import ReusablePromptBox from "../../../../common/ReusablePromptBox/ReusablePromptBox";
import {
  destination_IWA,
  destination_IWA_NPI,
  // destination_IWA_SCP,
} from "../../../../../destinationVariables";
import PeopleIcon from "@mui/icons-material/People";
import SettingsIcon from "@mui/icons-material/Settings";
import InfoIcon from "@mui/icons-material/Info";
import { TabContext, TabList, TabPanel } from "@mui/lab";

const useStyle = makeStyles((theme) => ({
  roleInfoContainer: {
    display: "flex",
    flexDirection: "column",
    padding: 10,
    height: `calc(100vh - ${appHeaderHeight} - ${rolePageHeaderHeight} - ${crossIconContainerHeight} - ${roleDetailPageCss?.tabsContainerHeight} - ${roleDetailPageCss?.footerHeight} - 18px)`,
  },
  roleInfoItemContainer: {
    margin: 4,
    alignItems: "center",
  },
  roleInfoContainerLabel: {
    margin: "0px",
    fontFamily: "Roboto, Helvetica, Arial, sans-serif",
    fontWeight: "400",
    fontSize: "12px",
  },
  roleInfoContainerText: {
    fontFamily: "Roboto, Helvetica, Arial, sans-serif",
    fontWeight: "400",
    fontSize: "12px",
  },

  newRoleAssignedUserDialogTitle: {
    height: "3rem",
    display: "flex",
    alignItems: "center",
    borderBottom: "1px solid #d9d9d9",
    margin: 0,
    padding: 0,
    paddingLeft: 10,
  },
  newRoleAssignedUserDialogActions: {
    height: "3rem",
    borderTop: "1px solid #d9d9d9",
  },

  roleAssignedUsersTableContainer: {
    height: `calc(100vh - ${appHeaderHeight} - ${rolePageHeaderHeight} - ${crossIconContainerHeight} - ${roleDetailPageCss?.tabsContainerHeight} - ${buttonHeight} - ${roleDetailPageCss?.footerHeight} - 26px)`,
    width: "100%",
  },
  roleAssignedUsersTableHead: {
    position: "sticky",
    top: 0,
    zIndex: 99,
    backgroundColor: "#F1F5FE",
  },
  roleAssignedUsersTableHeadCell: {
    whiteSpace: "nowrap",
    fontSize: 9,
    fontWeight: "bold",
    borderLeft: "1px solid #E5E5E5",
    borderRight: "1px solid #E5E5E5",
  },
  roleAssignedUsersTableBody: {
    height: "100%",
  },
  roleAssignedUsersTableBodyRow: {
    "&:hover": {
      backgroundColor: theme.palette.action.hover,
    },
  },
  roleAssignedUsersTableBodyCell: {
    whiteSpace: "nowrap",
    fontSize: 12,
    backgroundColor: "white",
    borderLeft: "1px solid #E5E5E5",
    borderRight: "1px solid #E5E5E5",
  },
  roleAssignedUsersBottonAddButton: {
    // margin: "0.5rem 0.2rem",
    textTransform: "capitalize",
    height: buttonHeight,
  },

  newRoleAssignedActivityDialogTitle: {
    height: "3rem",
    display: "flex",
    alignItems: "center",
    borderBottom: "1px solid #d9d9d9",
    margin: 0,
    padding: 0,
    paddingLeft: 10,
  },
  newRoleAssignedActivityDialogActions: {
    height: "3rem",
    borderTop: "1px solid #d9d9d9",
  },

  roleAssignedActivitiesTableContainer: {
    height: `calc(100vh - ${appHeaderHeight} - ${rolePageHeaderHeight} - ${crossIconContainerHeight} - ${roleDetailPageCss?.tabsContainerHeight} - ${buttonHeight} - ${roleDetailPageCss?.footerHeight} - 26px)`,
    width: "100%",
  },
  roleAssignedActivitiesTableHead: {
    position: "sticky",
    top: 0,
    zIndex: 99,
    backgroundColor: "#F1F5FE",
  },
  roleAssignedActivitiesTableHeadCell: {
    whiteSpace: "nowrap",
    fontSize: 9,
    fontWeight: "bold",
    borderLeft: "1px solid #E5E5E5",
    borderRight: "1px solid #E5E5E5",
  },
  roleAssignedActivitiesTableBody: {
    height: "100%",
  },
  roleAssignedActivitiesTableBodyRow: {
    "&:hover": {
      backgroundColor: theme.palette.action.hover,
    },
  },
  roleAssignedActivitiesTableBodyCell: {
    whiteSpace: "nowrap",
    fontSize: 12,
    backgroundColor: "white",
    borderLeft: "1px solid #E5E5E5",
    borderRight: "1px solid #E5E5E5",
  },
  roleAssignedActivitiesBottonAddButton: {
    margin: "4px 10px",
    textTransform: "capitalize",
    height: buttonHeight,
  },

  roleDetailContainer: {
    flexDirection: "column",
    height: "100%",
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
    backgroundColor: theme.palette.background.paper,
    margin: 0,
    padding: 0,
    position: "relative",
  },
  roleDetailCrossButtonContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end",
    paddingRight: 10,
    paddingTop: 10,
    height: crossIconContainerHeight,
  },
  roleDetailHeaderContainer: {
    display: "flex",
    alignItems: "center",
    padding: 10,
    borderBottom: `1px solid ${theme.palette.text.secondary}`,
    height: roleDetailPageCss?.tabsContainerHeight,
  },
  roleDetailHeaderItem: {
    color: theme.palette.text.secondary,
    fontWeight: "normal",
    cursor: "pointer",
    width: 150,
    fontSize: 14,
    marginLeft: 8,
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
  },
  roleDetailHeaderItemSelected: {
    color: theme.palette.text.primary,
    fontWeight: "bold",
  },
  roleDetailFooter: {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end",
    boxShadow: "0px 0px 9px #D8D8D8",
    padding: "8px 16px",
    // position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: theme.palette.background.paper,
    zIndex: 999,
    height: roleDetailPageCss?.footerHeight,
  },
  roleDetailFooterButton: {
    textTransform: "capitalize",
    fontSize: 14,
    fontWeight: "bold",
  },
}));

const RoleInfo = ({ roleDetail, setRoleDetail, params, load }) => {
  const userReducerState = useSelector((state) => state.userReducer);
  const basicReducerState = useSelector((state) => state.userManagement);
  const classes = useStyle();

  const getApplicationNameById = (applicationId) => {
    const application = findApplicationById(
      Number(applicationId),
      basicReducerState.applications
    );
    return application?.name || "-";
  };

  return (
    <div
      style={{
        minHeight: "22rem",
      }}
    >
      <Grid container>
        <Grid container
        sx={{
          marginTop: ".5rem",
        }}>
          <Grid item xs={4} sx={{
                display: "flex",
                alignItems: "center",
                height: "max-content",
                marginTop: ".5rem",
              }}>
          <Typography variant="body2">Name<span style={{ color: "red" }}>*</span></Typography>
          </Grid>

          <Grid item xs={6}>
            {roleDetail?.applicationId !== applicationIds?.SAP_BTP ? (
              <>
                <div
                  className={`inputContainer ${
                    (roleDetail?.name?.length === 0 ||
                      basicReducerState?.roles?.find(
                        (role) =>
                          role?.name?.toLowerCase() ===
                            roleDetail?.name?.toLowerCase() &&
                          role?.applicationId === roleDetail?.applicationId &&
                          Number(role?.id) !== Number(params?.roleId)
                      )) &&
                    !load &&
                    "inputError"
                  }`}
                >
                  <TextField
                    className={classes.roleInfoContainerText}
                    inputProps={{
                      style: {
                        fontFamily: "Roboto, Helvetica, Arial, sans-serif",
                        fontWeight: "400",
                        fontSize: "12px",
                      },
                    }}
                    fullWidth
                    size="small"
                    value={roleDetail?.name}
                    onChange={(e) =>
                      setRoleDetail({ ...roleDetail, name: e.target.value })
                    }
                  />
                </div>

                {basicReducerState?.roles?.find(
                  (role) =>
                    role?.name?.toLowerCase() ===
                      roleDetail?.name?.toLowerCase() &&
                    role?.applicationId === roleDetail?.applicationId &&
                    Number(role?.id) !== Number(params?.roleId)
                ) &&
                  !load && (
                    <p style={{ color: "red", fontSize: 10, marginTop: 3 }}>
                      Role name already exists
                    </p>
                  )}

                {roleDetail?.name?.length === 0 && !load && (
                  <p style={{ color: "red", fontSize: 10, marginTop: 3 }}>
                    Please fill it
                  </p>
                )}
              </>
            ) : (
              <Typography className={classes.roleInfoContainerText}>
                {roleDetail?.name}
              </Typography>
            )}
          </Grid>
        </Grid>

        <Grid container sx={{
              marginTop: ".5rem",
            }}>
          <Grid item xs={4} sx={{
                display: "flex",
                alignItems: "center",
                height: "max-content",
                marginTop: ".5rem",
              }}>
            <Typography variant="body2">
              {roleDetail?.applicationId === applicationIds?.SAP_BTP
                ? "Role Template"
                : "Label"}
              <span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>

          <Grid item xs={6}>
            {roleDetail?.applicationId !== applicationIds?.SAP_BTP ? (
              <>
                <div className={`inputContainer`}>
                  <TextField
                    className={classes.roleInfoContainerText}
                    inputProps={{
                      style: {
                        fontFamily: "Roboto, Helvetica, Arial, sans-serif",
                        fontWeight: "400",
                        fontSize: "12px",
                      },
                    }}
                    fullWidth
                    size="small"
                    value={roleDetail?.label}
                    onChange={(e) => {
                      if (
                        roleDetail?.applicationId === applicationIds.SAP_BTP &&
                        !roleDetail?.isComposite
                      ) {
                        return;
                      }
                      setRoleDetail({ ...roleDetail, label: e.target.value });
                    }}
                  />
                </div>

                {roleDetail?.label?.length === 0 && !load && (
                  <p style={{ color: "red", fontSize: 10, marginTop: 3 }}>
                    Please fill it
                  </p>
                )}
              </>
            ) : (
              <Typography className={classes.roleInfoContainerText}>
                {roleDetail?.label}
              </Typography>
            )}
          </Grid>
        </Grid>

        <Grid container sx={{
              marginTop: ".5rem",
            }}>
          <Grid item xs={4} sx={{
                display: "flex",
                alignItems: "center",
                height: "max-content",
                marginTop: ".5rem",
              }}>
            <Typography variant="body2">
              Description<span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>

          <Grid item xs={6}>
            {roleDetail?.applicationId !== applicationIds?.SAP_BTP ? (
              <>
                <div className={`inputContainer`}>
                  <TextField
                    className={classes.roleInfoContainerText}
                    inputProps={{
                      style: {
                        fontFamily: "Roboto, Helvetica, Arial, sans-serif",
                        fontWeight: "400",
                        fontSize: "12px",
                      },
                    }}
                    fullWidth
                    size="small"
                    value={roleDetail?.description}
                    onChange={(e) => {
                      if (
                        roleDetail?.applicationId === applicationIds.SAP_BTP &&
                        !roleDetail?.isComposite
                      ) {
                        return;
                      }
                      setRoleDetail({
                        ...roleDetail,
                        description: e.target.value,
                      });
                    }}
                  />
                </div>

                {roleDetail?.description?.length === 0 && !load && (
                  <p style={{ color: "red", fontSize: 10, marginTop: 3 }}>
                    Please fill it
                  </p>
                )}
              </>
            ) : (
              <Typography className={classes.roleInfoContainerText}>
                {roleDetail?.description}
              </Typography>
            )}
          </Grid>
        </Grid>

        <Grid container sx={{
              marginTop: ".5rem",
            }}>
          <Grid item xs={4} sx={{
                display: "flex",
                alignItems: "center",
                height: "max-content",
                marginTop: ".5rem",
              }}>
            <Typography variant="body2">
              Role Type<span style={{ color: "red" }}>*</span>
            </Typography>
          </Grid>

          <Grid item xs={6}>
            <>
              <div className={`inputContainer`}>
                <TextField
                  disabled={true}
                  className={classes.roleInfoContainerText}
                  inputProps={{
                    style: {
                      fontFamily: "Roboto, Helvetica, Arial, sans-serif",
                      fontWeight: "400",
                      fontSize: "12px",
                    },
                  }}
                  fullWidth
                  size="small"
                  value={roleDetail?.userType}
                />
              </div>
            </>
          </Grid>
        </Grid>

        {/* <Grid container className={classes.roleInfoItemContainer}>
        <Grid item xs={4}>
          <Typography className={classes.roleInfoContainerLabel}>
            Application
          </Typography>
        </Grid>

        <Grid item xs={6}>
        <div>
            <TextField
              disabled={true}
              className={classes.roleInfoContainerText}
              inputProps={{style: {fontFamily: "Roboto, Helvetica, Arial, sans-serif",
              fontWeight: "400",
              fontSize: "12px"}}}
              fullWidth
              size="small"
              value={getApplicationNameById(roleDetail?.applicationId)}
            />
          </div>
        </Grid>
      </Grid>

      <Grid container className={classes.roleInfoItemContainer}>
        <Grid item xs={4}>
          <Typography className={classes.roleInfoContainerLabel}>
            Type
          </Typography>
        </Grid>

        <Grid item xs={6}>
        <div>
            <TextField
              disabled={true}
              className={classes.roleInfoContainerText}
              inputProps={{style: {fontFamily: "Roboto, Helvetica, Arial, sans-serif",
              fontWeight: "400",
              fontSize: "12px"}}}
              fullWidth
              size="small"
              value={roleDetail?.isComposite === 1 ? "Composite" : "Single"}
            />
          </div>
        </Grid>
      </Grid>

      <Grid container className={classes.roleInfoItemContainer}>
        <Grid item xs={4}>
          <Typography className={classes.roleInfoContainerLabel}>
            Status
          </Typography>
        </Grid>

        <Grid item xs={6}>
        <div>
            <TextField
              disabled={true}
              className={classes.roleInfoContainerText}
              inputProps={{style: {fontFamily: "Roboto, Helvetica, Arial, sans-serif",
              fontWeight: "400",
              fontSize: "12px"}}}
              fullWidth
              size="small"
              value={roleDetail?.status}
            />
          </div>
        </Grid>
      </Grid> */}

        {/* <Grid container className={classes.roleInfoItemContainer}>
        <Grid item xs={4}>
          <Typography className={classes.roleInfoContainerLabel}>
            No of Expiry Days
          </Typography>
        </Grid>

        <Grid item xs={6}>
          <Typography className={classes.roleInfoContainerText}>
            {roleDetail?.noOfExpiryDays}
          </Typography>
        </Grid>
      </Grid>

      <Grid container className={classes.roleInfoItemContainer}>
        <Grid item xs={4}>
          <Typography className={classes.roleInfoContainerLabel}>
            Expiry Mail Trigger Days
          </Typography>
        </Grid>

        <Grid item xs={6}>
          <Typography className={classes.roleInfoContainerText}>
            {roleDetail?.expiryMailTriggerDays}
          </Typography>
        </Grid>
      </Grid> */}
      </Grid>
    </div>
  );
};

const NewRoleAssociateRole = ({
  open,
  onClose,
  roleDetail,
  setRoleDetail,
  params: { roleId },
}) => {
  const classes = useStyle();
  const basicReducerState = useSelector((state) => state.userManagement);
  const [newAssociateRoles, setNewAssociateRoles] = useState([]);

  const presentRole = (roleId) => {
    return roleDetail?.associateRoles?.filter(
      (role) => Number(role?.id) === Number(roleId)
    ).length > 0
      ? true
      : false;
  };

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose}>
      <DialogTitle className={classes.newRoleAssignedActivityDialogTitle}>
        New Associate Role
      </DialogTitle>

      <DialogContent>
        <Autocomplete
          multiple
          size="small"
          id="combo-box-demo"
          sx={font_Small}
          disableCloseOnSelect
          filterSelectedOptions
          value={newAssociateRoles}
          onChange={(e, roles) => {
            setNewAssociateRoles(roles || []);
          }}
          options={basicReducerState?.roles?.filter(
            (role) =>
              role?.isComposite !== 1 &&
              role?.applicationId === roleDetail?.applicationId &&
              !presentRole(role?.id)
          )}
          getOptionLabel={(option) => option.name}
          renderOption={(props, option, { selected }) => (
            <li {...props}>
              <Checkbox
                icon={<CheckBoxOutlineBlank fontSize="small" />}
                checkedIcon={<CheckBox color="primary" fontSize="small" />}
                checked={selected}
              />
              <Typography style={{ fontSize: 12 }}>{option.name}</Typography>
            </li>
          )}
          renderInput={(params) => (
            <TextField
              {...params}
              variant="standard"
              style={{ fontSize: 12 }}
              placeholder="Enter Associate Role"
            />
          )}
        />
      </DialogContent>

      <DialogActions>
        <Button
          key={"CANCEL"}
          size="small"
          variant="outlined"
          onClick={() => {
            onClose();
            setNewAssociateRoles([]);
          }}
        >
          Cancel
        </Button>

        <Button
          key={"ADD"}
          size="small"
          variant={newAssociateRoles?.length === 0 ? "outlined" : "contained"}
          className="btn-ml"
          onClick={() => {
            setRoleDetail({
              ...roleDetail,
              associateRoles: [
                ...roleDetail?.associateRoles,
                ...newAssociateRoles?.map((role) => ({
                  ...role,
                  status: "Draft",
                })),
              ],
            });
            onClose();
            setNewAssociateRoles([]);
          }}
          style={{ textTransform: "capitalize" }}
          disabled={newAssociateRoles?.length === 0}
        >
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const RoleAssociateRoles = ({
  roleDetail,
  setRoleDetail,
  getRoleInfoById,
  load,
  setLoad,
  params,
}) => {
  const classes = useStyle();
  const dispatch = useDispatch();
  const [openAssociateRoleDialog, setOpenAssociateRoleDialog] = useState(false);
  const [deletingAssociateRole, setDeletingAssociateRole] = useState(null);

  const deleteAssociateRole = (associateRole) => {
    if (associateRole?.status === "Draft") {
      setRoleDetail({
        ...roleDetail,
        associateRoles: roleDetail?.associateRoles?.filter(
          (role) => Number(role?.id) !== Number(associateRole?.id)
        ),
      });
    } else {
      const updateRoleAssociateRoleUrl = `/${destination_IWA}/api/v1/roles/modify`;
      const updateRoleAssociateRolePayload = {
        applicationId: roleDetail?.applicationId,
        associateRoles: "",
        description: roleDetail?.description,
        expiryMailTriggerDays: roleDetail?.expiryMailTriggerDays,
        hasExpiry: roleDetail?.hasExpiry,
        id: Number(params?.roleId),
        isActive: 1,
        isComposite: roleDetail?.isComposite,
        isDeleted: 0,
        label: roleDetail?.label,
        name: roleDetail?.name,
        noOfExpiryDays: roleDetail?.noOfExpiryDays,
        status: "Active",
        userType: roleDetail?.userType,
      };
      const updateRoleAssociateRoleRequestParam = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateRoleAssociateRolePayload),
      };
      setLoad(true);
      fetch(updateRoleAssociateRoleUrl, updateRoleAssociateRoleRequestParam)
        .then((res) => res.json())
        .then((data) => {
          getRoleInfoById();
          // setRoleDetail({
          //   ...roleDetail,
          //   associateRoles:
          //     roleDetail?.associateRoles
          //       ?.filter(
          //         (role) => Number(role?.id) !== Number(associateRole?.id)
          //       )
          //       .map((role) => role?.id)
          //       ?.join(",") || "",
          // });
          setLoad(false);
          setDeletingAssociateRole(null);

          dispatch(
            setResponseMessage({
              open: true,
              status: data?.status ? "success" : "error",
              message: data?.status
                ? "Asociated role deleted successfully"
                : "Something went wrong",
            })
          );
        })
        .catch((err) => {
          setLoad(false);
        });
    }
  };

  return (
    <>
      <Loading load={load} />

      <NewRoleAssociateRole
        open={openAssociateRoleDialog}
        onClose={() => setOpenAssociateRoleDialog(false)}
        roleDetail={roleDetail}
        setRoleDetail={setRoleDetail}
        params={params}
      />

      <DeletionMessageBox
        open={deletingAssociateRole ? true : false}
        onClose={() => setDeletingAssociateRole(null)}
        onDelete={() => {
          deleteAssociateRole(deletingAssociateRole);
        }}
        load={load}
      />

      <TableContainer
        // component={Paper}
        className={`${classes.roleAssignedActivitiesTableContainer} iagScroll`}
      >
        <Table size="small">
          <TableHead className={classes.roleAssignedActivitiesTableHead}>
            <TableRow>
              <TableCell
                className={classes.roleAssignedActivitiesTableHeadCell}
              >
                Role Name
              </TableCell>

              <TableCell
                align="center"
                className={classes.roleAssignedActivitiesTableHeadCell}
              >
                Action
              </TableCell>
            </TableRow>
          </TableHead>

          <TableBody
            className={`${classes.roleAssignedActivitiesTableBody} iagScroll`}
          >
            {roleDetail?.associateRoles
              ?.filter(
                (associateRole) => associateRole && associateRole !== "null"
              )
              ?.map((associateRole, index) => {
                return (
                  <TableRow
                    key={`${associateRole?.id}-${index}`}
                    className={classes.roleAssignedActivitiesTableBodyRow}
                  >
                    <TableCell
                      className={classes.roleAssignedActivitiesTableBodyCell}
                    >
                      {associateRole?.name}
                    </TableCell>

                    <TableCell
                      align="center"
                      className={classes.roleAssignedActivitiesTableBodyCell}
                    >
                      <Tooltip
                        title={
                          associateRole?.status === "Draft"
                            ? "Remove"
                            : "Delete"
                        }
                      >
                        <IconButton
                          // color="secondary"
                          onClick={(e) => {
                            e.stopPropagation();
                            // deleteAssociateRole(associateRole);
                            setDeletingAssociateRole(associateRole);
                          }}
                          disabled={load}
                        >
                          {associateRole?.status === "Draft" ? (
                            <Remove style={{ fontSize: 16 }} />
                          ) : (
                            <DeleteOutlinedIcon color="danger" />
                          )}
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                );
              })}
          </TableBody>
        </Table>
      </TableContainer>

      <Button
        size="small"
        variant="contained"
        onClick={() => setOpenAssociateRoleDialog(true)}
        startIcon={<Add />}
        disabled={load}
      >
        Add
      </Button>
    </>
  );
};

const NewRoleAssignedUser = ({
  open,
  onClose,
  roleDetail,
  setRoleDetail,
  params: { roleId },
}) => {
  const classes = useStyle();
  const basicReducerState = useSelector((state) => state.userManagement);
  const initialNewAssignedUser = {
    userEmail: "",
    permissionType: "",
    isActive: 1,
    isEdited: 0,
    roleId: Number(roleId),
    associationType: "ROLE",
    // createdBy: userReducerState?.user?.email,
    // createdOn: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
    isDeleted: 0,
    status: "Draft",
    // updatedBy: userReducerState?.user?.email,
    // updatedOn: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
    isGroupRole: 0,
    groupRole: "",
  };
  const assignedUsersId = roleDetail?.mappedUsers?.map(
    (assignedUser) => assignedUser?.userEmail
  );
  const [newAssignedUser, setNewAssignedUser] = useState([]);

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose}>
      <DialogTitle
        sx={{
          height: "3rem",
          display: "flex",
          margin: 0,
          justifyContent: "space-between",
          alignItems: "center",
          padding: ".5rem",
          paddingLeft: "1rem",
          backgroundColor: "#EAE9FF40",
        }}
      >
        <Typography variant="h6">New Assigned User</Typography>
        <IconButton
          sx={{ width: "max-content" }}
          onClick={onClose}
          children={<CloseIcon />}
        />
      </DialogTitle>

      <DialogContent sx={{ padding: "1rem 1rem" }}>
        <Grid
          container
          sx={{
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Grid
            item
            xs
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Typography variant="body1">
              Assigned Users<span style={{ color: "red" }}>*</span>
            </Typography>
            <Autocomplete
              multiple
              size="small"
              id="combo-box-demo"
              sx={font_Small}
              disableCloseOnSelect
              filterSelectedOptions
              value={newAssignedUser}
              onChange={(e, users) => {
                setNewAssignedUser(users || []);
              }}
              options={basicReducerState?.users?.filter(
                (user) => !assignedUsersId?.includes(user?.emailId)
              )}
              getOptionLabel={(option) => option.displayName}
              renderOption={(props, option, { selected }) => (
                <li {...props}>
                  <Checkbox
                    icon={<CheckBoxOutlineBlank fontSize="small" />}
                    checkedIcon={<CheckBox color="primary" fontSize="small" />}
                    checked={selected}
                  />
                  <Typography style={{ fontSize: 12 }}>
                    {option.displayName}
                  </Typography>
                </li>
              )}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  style={{ fontSize: 12 }}
                  placeholder={newAssignedUser[0] ? `` : `Select Users`}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
        <Button
          key={"CANCEL"}
          size="small"
          variant="outlined"
          onClick={() => {
            onClose();
            setNewAssignedUser([]);
          }}
        >
          Cancel
        </Button>

        <Button
          key={"ADD"}
          size="small"
          variant={newAssignedUser?.length === 0 ? "outlined" : "contained"}
          className="btn-ml"
          onClick={() => {
            const newMappedUsers = newAssignedUser?.map((user) => ({
              ...initialNewAssignedUser,
              userEmail: user?.emailId,
            }));
            setRoleDetail({
              ...roleDetail,
              mappedUsers: [...newMappedUsers, ...roleDetail?.mappedUsers],
            });
            onClose();
            setNewAssignedUser([]);
          }}
          disabled={newAssignedUser?.length === 0}
        >
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const RoleAssignedUsers = ({
  roleDetail,
  setRoleDetail,
  load,
  setLoad,
  params: { roleId },
  getRoleInfoById,
}) => {
  const classes = useStyle();
  const dispatch = useDispatch();
  const basicReducerState = useSelector((state) => state.userManagement);
  const [openAssignUserDialog, setOpenAssignUserDialog] = useState(false);
  const [deletingAssignedUser, setDeletingAssignedUser] = useState(null);
  const [openDeletionDialog, setOpenDeletionDialog] = useState(false);
  const [promptType, setPromptType] = useState("dialog");
  const [promptMessage, setPromptMessage] = useState("");

  const deleteAssignedUser = () => {
    let assignedUser = deletingAssignedUser;
    if (assignedUser?.status === "Draft") {
      setRoleDetail({
        ...roleDetail,
        mappedUsers:
          roleDetail?.mappedUsers?.filter(
            (user) => !(user?.emailId === assignedUser?.userEmail)
          ) || [],
      });
    } else {
      // setLoad(true);
      const disableRoleUserMappingUrl = `/${destination_IWA}/api/v1/users/userRoleMapping`;
      const disableRoleUserMappingPayload = {
        roleId: assignedUser?.roleId,
        isActive: 0,
        isDeleted: 1,
        status: "Inactive",
        userEmail: assignedUser?.userEmail,
        isGroupRole: assignedUser?.isGroupRole,
        groupRole: assignedUser?.groupRole,
      };
      const disableRoleUserMappingRequestParam = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(disableRoleUserMappingPayload),
      };
      fetch(disableRoleUserMappingUrl, disableRoleUserMappingRequestParam)
        .then((res) => res.json())
        .then((data) => {
          if (data.statusCode === 201) {
            setPromptType("snackbar");
            setPromptMessage(`Assigned user deleted successfully`);
            setOpenDeletionDialog(true);
          } else {
            setPromptType("snackbar");
            setPromptMessage(`Assigned user deletion failed`);
            setOpenDeletionDialog(true);
          }
          setPromptType("");
          setPromptMessage("");
          setOpenDeletionDialog(false);
          setRoleDetail({
            ...roleDetail,
            mappedUsers:
              roleDetail?.mappedUsers?.filter(
                (user) => !(user?.userEmail === assignedUser?.userEmail)
              ) || [],
          });
          // setLoad(false);
          setDeletingAssignedUser(null);
          // dispatch(
          //   setResponseMessage({
          //     open: true,
          //     status: data?.status ? "success" : "error",
          //     message: data?.status
          //       ? "Assigned user to role deleted successfully"
          //       : "Something went wrong",
          //   })
          // );
          fetch(
            `/${destination_IWA_NPI}/api/v1/applicationsMDG/entitiesAndActivitiesMDG?applicationId=1`
          )
            .then((res) => res.json())
            .then((data) => {
              if (data.statusCode === 200) {
                dispatch(
                  setEntitiesAndActivities(data.data.entitiesAndActivities[0])
                );
              }
            });
        })
        .catch((err) => {
          setLoad(false);
        });
    }
  };
  const getUserNameById = (userEmail) => {
    const user = findUserById(userEmail, basicReducerState.users);
    return user?.displayName || "-";
  };
  const assignedUsersColumns = [
    {
      field: "user",
      headerName: "User",
      width: 300,
      renderCell: (data) => {
        return (
          <>
            {data.row.status === "Draft"
              ? getUserNameById(data.row?.userEmail)
              : getUserNameById(data.row?.userEmail)}
          </>
        );
      },
    },
    {
      field: "status",
      headerName: "Status",
      width: 120,
    },
    // {
    //   field: "action",
    //   headerName: "Action",
    //   width: 80,
    //   headerAlign: "center",
    //   align: "center",
    //   renderCell: (data) => {
    //     return (
    //       <Tooltip title={data.row.status === "Draft" ? "Remove" : "Delete"}>
    //         <IconButton
    //           // color="secondary"
    //           onClick={(e) => {
    //             // e.stopPropagation();
    //             // deleteAssignedUser(assignedUser);
    //             setDeletingAssignedUser(data.row);
    //             setOpenDeletionDialog(true);
    //             setPromptType("dialog");
    //             setPromptMessage("Do you want to delete the assigned user?");
    //           }}
    //           disabled={load}
    //         >
    //           {data.row.status === "Draft" ? (
    //             <Remove style={{ fontSize: 16 }} />
    //           ) : (
    //             <DeleteOutlinedIcon color="danger" />
    //           )}
    //         </IconButton>
    //       </Tooltip>
    //     );
    //   },
    // },
  ];

  useEffect(() => {
    console.log(roleDetail.mappedUsers);
  }, [roleDetail?.mappedUsers]);

  return (
    <>
      <NewRoleAssignedUser
        open={openAssignUserDialog}
        onClose={() => setOpenAssignUserDialog(false)}
        roleDetail={roleDetail}
        setRoleDetail={setRoleDetail}
        params={{ roleId: roleId }}
      />

      <ReusablePromptBox
        type={promptType}
        promptState={openDeletionDialog}
        setPromptState={setOpenDeletionDialog}
        dialogSeverity={"danger"}
        dialogTitleText={"Assigned User Deletion"}
        cancelButtonText={"Cancel"}
        showCancelButton={true}
        okButtonText={"Delete"}
        showOkButton={true}
        promptMessage={promptMessage}
        handleOkButtonAction={deleteAssignedUser}
        handleSnackBarPromptClose={() => {
          getRoleInfoById();
          setOpenDeletionDialog(false);
        }}
      />

      <ReusableTable
        width="100%"
        status_onRowSingleClick={false}
        rows={
          roleDetail?.mappedUsers ?? []
          // [
          //   {
          //     "userEmail": "<EMAIL>",
          //     "roleId": 7,
          //     "status": "Active",
          //     "isDeleted": 0,
          //     "isActive": 1,
          //     "createdBy": "<EMAIL>",
          //     "createdOn": "2023-02-20 10:35:42.000000000",
          //     "updatedBy": "<EMAIL>",
          //     "updatedOn": "2023-03-01 14:34:09.000000000",
          //     "isGroupRole": 0,
          //     "groupRole": ""
          //   },
          //   {
          //     "userEmail": "<EMAIL>",
          //     "roleId": 7,
          //     "status": "Active",
          //     "isDeleted": 0,
          //     "isActive": 1,
          //     "createdBy": "<EMAIL>",
          //     "createdOn": "2023-03-01 09:07:18.000000000",
          //     "updatedBy": " ",
          //     "updatedOn": null,
          //     "isGroupRole": 0,
          //     "groupRole": ""
          //   },
          // ]
        }
        columns={assignedUsersColumns}
        getRowIdValue={"userEmail"}
        stopPropagation_Column={"action"}
        hideFooter={false}
        noOfColumns={5}
        rowsPerPageOptions={[5, 10, 15]}
        checkboxSelection={false}
        disableSelectionOnClick={false}
      />

      {/* <div style={{ display: "flex", justifyContent: "flex-end" }}>
        <Button
          size="small"
          variant="contained"
          sx={{ margin: "1rem 0" }}
          onClick={() => setOpenAssignUserDialog(true)}
          startIcon={<Add />}
          disabled={load}
        >
          Add
        </Button>
      </div> */}
    </>
  );
};

const NewRoleAssignedActivity = ({
  open,
  onClose,
  roleDetail,
  setRoleDetail,
  params: { roleId },
}) => {
  const classes = useStyle();
  const basicReducerState = useSelector((state) => state.userManagement);
  const initialNewAssignedActivity = {
    isActive: 1,
    id: uuidv4(),
    // createdBy: userReducerState?.user?.email,
    // createdOn: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
    isDeleted: 0,
    roleId: Number(roleId),
    status: "Draft",
    // updatedBy: userReducerState?.user?.email,
    // updatedOn: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
  };
  // const [newAssignedActivity, setNewAssignedActivity] = useState(
  //   initialNewAssignedActivity
  // );
  const [newAssignedActivity, setNewAssignedActivity] = useState([]);
  const [entitiesId, setEntitiesId] = useState([]);

  useEffect(() => {
    setEntitiesId(getEntitiesIdByApplicationId());
  }, [roleDetail?.mappedActivities]);

  const presentAssignedActivities = (activityName) => {
    return roleDetail?.mappedActivities?.filter(
      (assignedActivity) => assignedActivity?.name === activityName
    ).length > 0
      ? true
      : false;
  };

  const getEntityNameById = (entityId) => {
    const entity = findEntityById(Number(entityId), basicReducerState.entities);
    return entity?.name || "-";
  };

  const getEntitiesIdByApplicationId = () => {
    const entitiesList =
      basicReducerState?.entities?.filter(
        (entity) =>
          entity?.applicationId === roleDetail?.applicationId && entity?.id
      ) || [];
    const entitiesIdList = entitiesList.map((entity) => entity.id);
    return entitiesIdList;
  };

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose}>
      <DialogTitle
        sx={{
          height: "3rem",
          display: "flex",
          margin: 0,
          justifyContent: "space-between",
          alignItems: "center",
          padding: ".5rem",
          paddingLeft: "1rem",
          backgroundColor: "#EAE9FF40",
        }}
      >
        <Typography variant="h6">New Assigned Feature</Typography>
        <IconButton
          sx={{ width: "max-content" }}
          onClick={onClose}
          children={<CloseIcon />}
        />
      </DialogTitle>

      <DialogContent sx={{ padding: "1rem 1rem" }}>
        <Grid
          container
          sx={{
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Grid
            item
            xs
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Typography variant="body1">
              Assigned Features<span style={{ color: "red" }}>*</span>{" "}
            </Typography>
            <Autocomplete
              multiple
              size="small"
              id="combo-box-demo"
              sx={font_Small}
              disableCloseOnSelect
              filterSelectedOptions
              disableListWrap
              value={newAssignedActivity}
              onChange={(e, activities) => {
                setNewAssignedActivity(activities || []);
              }}
              options={basicReducerState?.activities?.filter(
                (activity) =>
                  entitiesId.includes(activity?.entityId) &&
                  !presentAssignedActivities(activity?.name)
              )}
              groupBy={(option) => getEntityNameById(option?.entityId)}
              getOptionLabel={(option) =>
                `${option?.name} (${getEntityNameById(option?.entityId)})`
              }
              renderOption={(props, option, { selected }) => (
                <li {...props}>
                  <Checkbox
                    icon={<CheckBoxOutlineBlank fontSize="small" />}
                    checkedIcon={<CheckBox color="primary" fontSize="small" />}
                    checked={selected}
                  />
                  <Typography style={{ fontSize: 12 }}>
                    {`${option?.name} (${getEntityNameById(option?.entityId)})`}
                  </Typography>
                </li>
              )}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  style={{ fontSize: 12 }}
                  placeholder={newAssignedActivity[0] ? `` : `Select Features`}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
        <Button
          key={"CANCEL"}
          size="small"
          variant="outlined"
          onClick={() => {
            onClose();
            setNewAssignedActivity([]);
          }}
        >
          Cancel
        </Button>

        <Button
          key={"ADD"}
          size="small"
          variant={newAssignedActivity?.length === 0 ? "outlined" : "contained"}
          className="btn-ml"
          onClick={() => {
            const newmappedActivity = newAssignedActivity?.map((activity) => ({
              ...initialNewAssignedActivity,
              id: activity?.id,
            }));
            setRoleDetail({
              ...roleDetail,
              mappedActivities: [
                ...newmappedActivity,
                ...roleDetail.mappedActivities,
              ],
            });
            onClose();
            setNewAssignedActivity([]);
          }}
          disabled={newAssignedActivity?.length === 0}
        >
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const RoleAssignedActivities = ({
  roleDetail,
  setRoleDetail,
  load,
  setLoad,
  params: { roleId },
}) => {
  const classes = useStyle();
  const dispatch = useDispatch();
  const basicReducerState = useSelector((state) => state.userManagement);
  const [openAssignActivityDialog, setOpenAssignActivityDialog] =
    useState(false);
  const [deletingAssignedActivity, setDeletingAssignedActivity] =
    useState(null);
  const [openDeletionDialog, setOpenDeletionDialog] = useState(false);
  const [promptType, setPromptType] = useState("dialog");
  const [promptMessage, setPromptMessage] = useState("");

  const deleteAssignedActivity = () => {
    let assignedActivity = deletingAssignedActivity;
    if (assignedActivity.status === "Draft") {
      setRoleDetail({
        ...roleDetail,
        mappedActivities:
          roleDetail?.mappedActivities?.filter(
            (activity) => !(activity?.id === assignedActivity?.id)
          ) || [],
      });
    } else {
      // setLoad(true);
      const disableRoleMappedctivityUrl = `/${destination_IWA}/api/v1/roles/activityRoleMapping/modify`;
      const disableRoleMappedctivityPayload = {
        activityId: assignedActivity?.id,
        isActive: 0,
        isDeleted: 1,
        status: "Inactive",
        roleId: Number(roleId),
      };
      const disableRoleMappedctivityRequestParam = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(disableRoleMappedctivityPayload),
      };
      fetch(disableRoleMappedctivityUrl, disableRoleMappedctivityRequestParam)
        .then((res) => res.json())
        .then((data) => {
          if (data.statusCode === 201) {
            setPromptType("snackbar");
            setPromptMessage(`Assigned feature deleted successfully`);
            setOpenDeletionDialog(true);
          } else {
            setPromptType("snackbar");
            setPromptMessage(`Assigned feature deletion failed`);
            setOpenDeletionDialog(true);
          }
          setPromptType("");
          setPromptMessage("");
          setOpenDeletionDialog(false);
          setRoleDetail({
            ...roleDetail,
            mappedActivities:
              roleDetail?.mappedActivities?.filter(
                (activity) => activity?.id !== assignedActivity?.id
              ) || [],
          });
          // setLoad(false);
          setDeletingAssignedActivity(null);

          // dispatch(
          //   setResponseMessage({
          //     open: true,
          //     status: data?.status ? "success" : "error",
          //     message: data?.status
          //       ? "Assigned activity to role deleted successfully"
          //       : "Something went wrong",
          //   })
          // );
        })
        .catch((err) => {
          setLoad(false);
        });
    }
  };
  const getEntityNameById = (entityId) => {
    const entity = findEntityById(Number(entityId), basicReducerState.entities);
    return entity?.name || "-";
  };

  const assignedFeaturesColumns = [
    {
      field: "feature",
      headerName: "Feature",
      width: 450,
      renderCell: (data) => {
        return (
          <>
            {data.row?.status === "Draft" ? (
              <Select
                size="small"
                style={{ fontSize: 12 }}
                value={data.row?.id}
                readOnly
              >
                {basicReducerState?.activities.map((activity, index) => (
                  <MenuItem
                    key={`${activity?.id}-${index}`}
                    value={activity?.id}
                    style={{ fontSize: 12 }}
                  >
                    {`${activity?.name} (${getEntityNameById(
                      activity?.entityId
                    )})`}
                  </MenuItem>
                ))}
              </Select>
            ) : (
              `${data.row?.name} (${getEntityNameById(data.row?.entityId)})`
            )}
          </>
        );
      },
    },
    // {
    //   field: "status",
    //   headerName: "Status",
    //   flex: 1,
    // },
    {
      field: "action",
      headerName: "Action",
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (data) => {
        return (
          <>
            <Tooltip title={data.row?.status === "Draft" ? "Remove" : "Delete"}>
              <IconButton
                // color="secondary"
                onClick={(e) => {
                  // e.stopPropagation();
                  // deleteAssignedActivity(assignedActivity);
                  setDeletingAssignedActivity(data?.row);
                  setOpenDeletionDialog(true);
                  setPromptType("dialog");
                  setPromptMessage(
                    "Do you want to delete the assigned feature?"
                  );
                }}
                disabled={load}
              >
                {data?.row?.status === "Draft" ? (
                  <Remove style={{ fontSize: 16 }} />
                ) : (
                  <DeleteOutlinedIcon color="danger" />
                )}
              </IconButton>
            </Tooltip>
          </>
        );
      },
    },
  ];

  // useEffect(() => {
  //   console.log(roleDetail.mappedActivities);
  // }, [roleDetail?.mappedActivities]);

  return (
    <>
      <NewRoleAssignedActivity
        open={openAssignActivityDialog}
        onClose={() => setOpenAssignActivityDialog(false)}
        roleDetail={roleDetail}
        setRoleDetail={setRoleDetail}
        params={{ roleId: roleId }}
      />

      {/* <DeletionMessageBox
        open={deletingAssignedActivity ? true : false}
        onClose={() => setDeletingAssignedActivity(null)}
        onDelete={() => {
          deleteAssignedActivity(deletingAssignedActivity);
        }}
        load={load}
      /> */}

      <ReusablePromptBox
        type={promptType}
        promptState={openDeletionDialog}
        setPromptState={setOpenDeletionDialog}
        dialogSeverity={"danger"}
        dialogTitleText={"Assigned Feature Deletion"}
        cancelButtonText={"Cancel"}
        showCancelButton={true}
        okButtonText={"Delete"}
        showOkButton={true}
        promptMessage={promptMessage}
        handleOkButtonAction={deleteAssignedActivity}
        handleSnackBarPromptClose={() => {
          setOpenDeletionDialog(false);
        }}
      />

      <ReusableTable
        width="100%"
        status_onRowSingleClick={false}
        rows={
          roleDetail?.mappedActivities ?? []
          // [
          // {
          //   "id": 51,
          //   "name": "Advanced Shipment Notification",
          //   "label": "Advanced Shipment Notification",
          //   "description": "Displays the list of Advanced Shipment Notifications",
          //   "entityId": 25,
          //   "status": "Active",
          //   "isDeleted": 0,
          //   "isActive": 1,
          //   "createdBy": "<EMAIL>",
          //   "createdOn": "2023-03-01 09:45:44.000000000",
          //   "updatedBy": "<EMAIL>",
          //   "updatedOn": "2023-03-01 09:45:44.000000000"
          // },
          // {
          //   "id": 72,
          //   "name": "Track DPR",
          //   "label": "Track DPR",
          //   "description": "Track DPR",
          //   "entityId": 31,
          //   "status": "Active",
          //   "isDeleted": 0,
          //   "isActive": 1,
          //   "createdBy": "<EMAIL>",
          //   "createdOn": "2023-03-01 12:25:59.000000000",
          //   "updatedBy": "<EMAIL>",
          //   "updatedOn": "2023-03-01 12:25:59.000000000"
          // }
          // ]
        }
        columns={assignedFeaturesColumns}
        getRowIdValue={"id"}
        hideFooter={false}
        noOfColumns={5}
        stopPropagation_Column={"action"}
        rowsPerPageOptions={[5, 10, 15]}
        checkboxSelection={false}
        disableSelectionOnClick={false}
      />

      {roleDetail?.isComposite !== 1 && (
        <div style={{ display: "flex", justifyContent: "flex-end" }}>
          <Button
            size="small"
            variant="contained"
            sx={{ margin: "1rem 0" }}
            onClick={() => setOpenAssignActivityDialog(true)}
            startIcon={<Add />}
            disabled={load}
          >
            Add
          </Button>
        </div>
      )}
    </>
  );
};

function RolesDetail({ params: { roleId }, setParams }) {
  const basicReducerState = useSelector((state) => state.userManagement);
  const classes = useStyle();
  const [selectedRoleDetailContentType, setSelectedRoleDetailContentType] =
    useState("Basic Info");
  const [load, setLoad] = useState(false);
  const [roleDetail, setRoleDetail] = useState({});
  const dispatch = useDispatch();
  const [value, setValue] = useState("Basic Info");
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  useEffect(() => {
    if (roleId) {
      getRoleInfoById();
    }
  }, [roleId]);

  const getRoleInfoById = () => {
    setLoad(true);
    const getRoleByIdUrl = `/${destination_IWA_NPI}/api/v1/rolesMDG/byRoleIdMDG?id=${roleId}`;
    const getRoleByIdRequestParam = {
      headers: {
        "Content-Type": "application/json",
      },
    };
    const getRoleMappedUsersUrl = `/${destination_IWA_NPI}/api/v1/rolesMDG/userRoleMappingMDG?roleId=${roleId}`;
    const getRoleMappedUsersRequestParam = {
      headers: {
        "Content-Type": "application/json",
      },
    };
    Promise.all([
      fetch(getRoleByIdUrl, getRoleByIdRequestParam).then((res) => res.json()),
      fetch(getRoleMappedUsersUrl, getRoleMappedUsersRequestParam).then((res) =>
        res.json()
      ),
    ])
      .then(([roleDetail, roleMappedUsersDetail]) => {
        const getRoleMappedActivitiesUrl = `/${destination_IWA}/api/v1/activities/role`;
        var roleIdList = [];
        if (roleDetail?.data?.isComposite === 1) {
          const rolesIdList =
            roleDetail?.data?.associateRoles?.split(",") || [];
          rolesIdList
            ?.filter((id) => id?.length > 0)
            ?.map((id) => {
              if (id !== null && id !== "null") {
                roleIdList.push(id);
              }
              return null;
            });
        } else {
          roleIdList.push(roleId);
        }
        const getRoleMappedActivitiesPayload = {
          roleId: roleIdList,
        };
        const getRoleMappedActivitiesRequestParam = {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(getRoleMappedActivitiesPayload),
        };
        fetch(getRoleMappedActivitiesUrl, getRoleMappedActivitiesRequestParam)
          .then((res) => res.json())
          .then((roleMappedActivitiesDetail) => {
            setLoad(false);
            const associateRolesId =
              roleDetail?.data?.associateRoles?.length === 0
                ? []
                : roleDetail?.data?.associateRoles
                    ?.split(",")
                    .map((id) => Number(id));
            setRoleDetail({
              ...roleDetail?.data,
              associateRoles:[],
              mappedUsers: roleMappedUsersDetail?.data || [],
              mappedActivities: roleMappedActivitiesDetail?.data || [],
            });
            dispatch(
              setRoles(
                basicReducerState?.roles?.map((role) =>
                  Number(role?.id) === Number(roleId)
                    ? {
                        ...role,
                        name: roleDetail?.data?.name,
                        label: roleDetail?.data?.label,
                        description: roleDetail?.data?.description,
                      }
                    : role
                )
              )
            );
            setSelectedRoleDetailContentType("Basic Info");
          })
          .catch((err) => {
            setLoad(false);
          });
      })
      .catch((err) => {
        setLoad(false);
      });
  };
  const updateRoleInfo = () => {
    setLoad(true);
    const updateRoleUrl = `/${destination_IWA}/api/v1/roles/modify`;
    const updateRolePayload = {
      description: roleDetail?.description,
      label: roleDetail?.label,
      isActive: 1,
      noOfExpiryDays: roleDetail?.noOfExpiryDays,
      isDeleted: 0,
      hasExpiry: roleDetail?.hasExpiry ? 1 : 0,
      expiryMailTriggerDays: roleDetail?.expiryMailTriggerDays,
      name: roleDetail?.name,
      id: Number(roleId),
      applicationId: roleDetail?.applicationId,
      status: "Active",
      isComposite: roleDetail?.isComposite,
      associateRoles: "",
      userType: roleDetail?.userType,
    };
    const updateRoleRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateRolePayload),
    };
    const updatedMappedUsersAndActivitiesUrl = `/${destination_IWA_NPI}/api/v1/rolesMDG/mapActivitiesAndUsersMDG`;
    const updateMappedUsersAndActivitiesPayload = {
      userRoleMappings:
        roleDetail?.mappedUsers?.filter(
          (mappedUser) => mappedUser?.status === "Draft"
        ) || [],
      activityRoleMappings: [],
    };
    console.log(roleDetail);
    console.log(roleDetail.mappedActivities);
    console.log(roleDetail.mappedUsers);
    roleDetail?.mappedActivities?.forEach((mappedActivity) => {
      console.log(mappedActivity);
      let clonedItem = { ...mappedActivity };
      clonedItem.activityId = mappedActivity.id;
      delete clonedItem.id;

      if (mappedActivity?.status === "Draft") {
        updateMappedUsersAndActivitiesPayload.activityRoleMappings.push(
          clonedItem
        );
      }
    });
    const updateMappedUsersAndActivitiesRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateMappedUsersAndActivitiesPayload),
    };
    Promise.all([
      fetch(updateRoleUrl, updateRoleRequestParam).then((res) => res.json()),
      fetch(
        updatedMappedUsersAndActivitiesUrl,
        updateMappedUsersAndActivitiesRequestParam
      ).then((res) => res.json()),
    ])
      .then(
        ([
          updateRoleData,
          updateMappedUserAndActivityData,
          updateMappedApisData,
        ]) => {
          setLoad(false);
          getRoleInfoById();

          dispatch(
            setResponseMessage({
              open: true,
              status:
                updateRoleData?.status &&
                updateMappedUserAndActivityData?.status
                  ? "success"
                  : "error",
              message:
                updateRoleData?.status &&
                updateMappedUserAndActivityData?.status
                  ? "Role details updated successfully"
                  : "Something went wrong",
            })
          );
        }
      )
      .catch((err) => {
        setLoad(false);
      });
  };

  return (
    <Paper sx={{ minHeight: "22rem" }}>
      <Loading load={load} />

      <>
        <TabContext value={value} >
          <Box
            sx={{
              borderBottom: 1,
              borderColor: "divider",
              padding: "0px 12px 0px",
              position: "relative",
            }}
          >
            <Box
              sx={{
                position: "absolute",
                right: "1rem",
                top: "1rem",
                zIndex: "10",
              }}
            >
              <IconButton
                onClick={() => {
                  setParams({});
                }}
              >
                <Close style={{ fontSize: 16, cursor: "pointer" }} />
              </IconButton>
            </Box>
            <TabList onChange={handleChange} aria-label="basic tabs example">
              <Tab
                label={
                  <Stack
                    direction="row"
                    sx={{
                      alignItems: "center",
                    }}
                  >
                    <InfoIcon sx={{ fontSize: "15px" }} />
                    <Typography
                      variant="body1"
                      ml={1}
                      sx={{ fontWeight: 600, fontSize: "14px" }}
                    >
                      Basic Info
                    </Typography>
                  </Stack>
                }
                value="Basic Info"
                sx={{ textTransform: "none", fontWeight: "bold" }}
              />

              {roleDetail?.isComposite === 1 && (
                <Tab
                  label={
                    <Stack
                      direction="row"
                      sx={{
                        alignItems: "center",
                      }}
                    >
                      <PeopleIcon sx={{ fontSize: "15px" }} />
                      <Typography
                        variant="body1"
                        ml={1}
                        sx={{ fontWeight: 600, fontSize: "14px" }}
                      >
                        Associate Roles
                      </Typography>
                    </Stack>
                  }
                  value="Associate Roles"
                  sx={{ textTransform: "none", fontWeight: "bold" }}
                />
              )}

              {!(
                roleDetail?.applicationId === applicationIds?.SAP_BTP &&
                roleDetail?.isComposite === 0
              ) && (
                <Tab
                  label={
                    <Stack
                      direction="row"
                      sx={{
                        alignItems: "center",
                      }}
                    >
                      <PeopleIcon sx={{ fontSize: "15px" }} />
                      <Typography
                        variant="body1"
                        ml={1}
                        sx={{ fontWeight: 600, fontSize: "14px" }}
                      >
                        Assigned Users
                      </Typography>
                    </Stack>
                  }
                  value="Assigned Users"
                  sx={{ textTransform: "none", fontWeight: "bold" }}
                />
              )}

              {roleDetail?.applicationId !== applicationIds?.SAP_BTP && (
                <Tab
                  label={
                    <Stack
                      direction="row"
                      sx={{
                        alignItems: "center",
                      }}
                    >
                      <SettingsIcon sx={{ fontSize: "15px" }} />
                      <Typography
                        variant="body1"
                        ml={1}
                        sx={{ fontWeight: 600, fontSize: "14px" }}
                      >
                        Assigned Features
                      </Typography>
                    </Stack>
                  }
                  value="Assigned Features"
                  sx={{ textTransform: "none", fontWeight: "bold" }}
                />
              )}
            </TabList>
            <TabPanel value={"Basic Info"} sx={{ padding: "0px", minHeight: "22rem" }}>
              <Stack sx={{ paddingTop: ".5rem" }}>
                <RoleInfo
                  roleDetail={roleDetail}
                  setRoleDetail={setRoleDetail}
                  params={{ roleId: roleId }}
                  load={load}
                />
              </Stack>
            </TabPanel>
            {!(
              roleDetail?.applicationId === applicationIds?.SAP_BTP &&
              roleDetail?.isComposite === 0
            ) && (
              <TabPanel value={"Assigned Users"} sx={{ padding: "0px", minHeight: "22rem" }}>
                <Stack sx={{ paddingTop: ".5rem" }}>
                  <RoleAssignedUsers
                    roleDetail={roleDetail}
                    setRoleDetail={setRoleDetail}
                    load={load}
                    setLoad={setLoad}
                    params={{ roleId: roleId }}
                    getRoleInfoById={getRoleInfoById}
                  />
                </Stack>
              </TabPanel>
            )}

            {roleDetail?.applicationId !== applicationIds?.SAP_BTP && (
              <TabPanel value={"Assigned Features"} sx={{ padding: "0px", minHeight: "22rem" }}>
                <Stack sx={{ paddingTop: ".5rem" }}>
                  <RoleAssignedActivities
                    roleDetail={roleDetail}
                    setRoleDetail={setRoleDetail}
                    load={load}
                    setLoad={setLoad}
                    params={{ roleId: roleId }}
                  />
                </Stack>
              </TabPanel>
            )}
          </Box>
        </TabContext>

        <div className={classes.roleDetailFooter}>
          <Button
            size="small"
            variant={
              load ||
              value === 'Assigned Users' ||
              roleDetail?.name?.length === 0 ||
              roleDetail?.label?.length === 0 ||
              roleDetail?.description?.length === 0 ||
              basicReducerState?.roles?.find(
                (role) =>
                  role?.name === roleDetail?.name &&
                  role?.applicationId === roleDetail?.applicationId &&
                  Number(role?.id) !== Number(roleId) &&
                  role?.isComposite === roleDetail?.isComposite
              ) ||
              (roleDetail?.isComposite === 0 &&
                roleDetail?.applicationId === applicationIds?.SAP_BTP)
                ? "outlined"
                : "contained"
            }
            onClick={updateRoleInfo}
            disabled={
              load ||
              value === 'Assigned Users' ||
              roleDetail?.name?.length === 0 ||
              roleDetail?.label?.length === 0 ||
              roleDetail?.description?.length === 0 ||
              basicReducerState?.roles?.find(
                (role) =>
                  role?.name === roleDetail?.name &&
                  role?.applicationId === roleDetail?.applicationId &&
                  Number(role?.id) !== Number(roleId) &&
                  role?.isComposite === roleDetail?.isComposite
              ) ||
              (roleDetail?.isComposite === 0 &&
                roleDetail?.applicationId === applicationIds?.SAP_BTP)
            }
          >
            Submit
          </Button>
        </div>
      </>
    </Paper>
  );
}

export default RolesDetail;
