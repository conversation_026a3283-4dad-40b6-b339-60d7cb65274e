import { createSlice } from "@reduxjs/toolkit";
import moment from "moment";
import { useSelector } from "react-redux";
// import { capitalize } from "../functions";

const initialDataSlice = createSlice({
  name: "initialData",
  initialState: {
    PurchaseManagement: [],
    POWorkbench: [],
    DPR: [],
    ASNHome: [],
    CreateASN: [],
    EInvoice: [],
    InvoiceTracker: [],
    InvoiceWorkbench: [],
    CreateReturn: [],
    Document: [],
    ServiceRequest: [],
    CreateServiceEntry: [],
    ServiceEntryOrder: [],
    PlanningManagement: [],
    IWMMyTask: {},
    MultipleMaterial: [],
    MultipleMaterialRequestBench:[],
    MultipleCostCenterData: [],
    EditMultipleMaterial: {},
    CostCenter:[],
    GeneralLedger:[],
    ArtifactId: [],
    AttachmentType: [],
  },
  reducers: {
    initialDataAdd(state, action) {
      state.push(action.payload);
    },
    initialDataUpdate(state, action) {
      state[action.payload["module"]] = action.payload["initialData"];
      return state;
    },
    setIwmMyTask: (state, action) => {
      state.IWMMyTask = action.payload;
    },
    setMultipleMaterial(state, action) {
      console.log("mulAction", action);
      state.MultipleMaterial = action.payload;
    },
    setMultipleMaterialRequestBench(state, action) {
      console.log("mulAction", action);
      state.MultipleMaterialRequestBench = action.payload;
    },
    setMultipleCostCenterData(state, action) {
      console.log("mulAction", action);
      state.MultipleCostCenterData = action.payload;
    },
    setEditMultipleMaterial(state, action) {
      state.EditMultipleMaterial = {
        ...state.EditMultipleMaterial,
        ...action.payload,
      };
    },
    setArtifactId(state, action){
      state.ArtifactId = [...state.ArtifactId, action.payload];
    },
    setAttachmentType(state, action){
      state.AttachmentType = [...state.AttachmentType, action.payload];
    },
    clearArtifactId(state) {
      state.ArtifactId =[];
    },
    clearAttachmentType(state) {
      state.AttachmentType = [];
    }
  },
});

export const {
  initialDataAdd,
  initialDataUpdate,
  setIwmMyTask,
  setMultipleMaterial,
  setMultipleCostCenterData,
  setEditMultipleMaterial,
  setMultipleMaterialRequestBench,
  setArtifactId,
  setAttachmentType,
  clearArtifactId,
  clearAttachmentType,
} = initialDataSlice.actions;

export default initialDataSlice.reducer;
