import 'react-dropzone-uploader/dist/styles.css'
import Dropzone from 'react-dropzone-uploader'
import { Box, Stack } from '@mui/material'
import { useState } from 'react'

const Layout = ({ input, previews, submitButton, dropzoneProps, files, extra: { maxFiles } }) => {
 
  return (
    <Stack spacing={1}>
      <Box>
        {previews}
      </Box>
      <Box {...dropzoneProps}>
        {files.length < maxFiles && input}
      </Box>
      <Box sx={{display: "flex", justifyContent: "flex-end"}}>
        
        {files.length > 0 && submitButton}
      </Box>
    </Stack>
  )
}

const FileUpload = (props) => {
    // const handleChangeStatus = ({ meta }, status) => {
    //   console.log(status, meta)
    // }
    return (
      <Dropzone
        onSubmit={props?.handleAttachmentsSubmit}
        styles={{ submitButton:{background:"#3B30C8",marginRight: "1.5rem",textTransform:"none"} ,dropzone: { border:0, padding:0, overflow:"hidden",minHeight:"10vh", flexDirection: "row", display: "flex", justifyContent: "flex-end", paddingRight: "1.5rem" },
        submitButtonContainer: {margin: 0} }}
        LayoutComponent={Layout}
        inputContent={props?.inputContent}
        submitButtonContent={props?.submitButtonContent}
        submitButtonDisabled={props?.submitButtonDisabled}
      />
    )
  }
export default FileUpload;