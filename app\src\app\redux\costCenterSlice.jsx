import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  // Rows and Tabs
  rows: [],
  selectedRowId: null,
  tabFieldValues: {}, // { rowId: { tabIndex: { fieldName: value } } }

  // Tab Configuration
  tabs: [],
  config: {
    allfields: {},
    mandatoryFields: {},
  },
  error: null,
  loading: false,

  // Request Header
  requestHeader: {
    requestId: '',
    reqCreatedBy: '',
    reqCreatedOn: '',
    reqUpdatedOn: '',
    requestType: '',
    requestDesc: '',
    requestStatus: 'DRAFT',
    requestPriority: '',
    fieldName: '',
    templateName: '',
    division: '',
    region: '',
    leadingCat: '',
    firstProd: '',
    launchDate: '',
    isBifurcated: false,
    screenName: '',
    isHeaderFinalized: false,
  },
  successMsg: '',
};

const costCenterSlice = createSlice({
  name: "costCenter",
  initialState,
  reducers: {
    // Row/Table handlers
    setRows: (state, action) => {
      state.rows = action.payload;
    },
    updateRow: (state, action) => {
      const { id, field, value } = action.payload;
      state.rows = state.rows.map((row) =>
        row.id === id ? { ...row, [field]: value } : row
      );
    },
    addRow: (state, action) => {
      state.rows.push(action.payload);
    },
    deleteRow: (state, action) => {
      const id = action.payload;
      state.rows = state.rows.filter((row) => row.id !== id);
      delete state.tabFieldValues[id];
    },
    setSelectedRowId: (state, action) => {
      state.selectedRowId = action.payload;
    },
    updateTabFieldValues: (state, action) => {
      const { rowId, tabIndex, field, value } = action.payload;
      if (!state.tabFieldValues[rowId]) state.tabFieldValues[rowId] = {};
      if (!state.tabFieldValues[rowId][tabIndex]) state.tabFieldValues[rowId][tabIndex] = {};
      state.tabFieldValues[rowId][tabIndex][field] = value;
    },

    // Tab Configuration
    setCostCenterTabs: (state, action) => {
      state.tabs = action.payload;
    },
    setCostCenterConfig: (state, action) => {
      state.config = action.payload;
    },
    setCostCenterError: (state, action) => {
      state.error = action.payload;
    },
    setCostCenterLoading: (state, action) => {
      state.loading = action.payload;
    },

    // Request Header
    setRequestHeaderData: (state, action) => {
      state.requestHeader = { ...state.requestHeader, ...action.payload };
      state.successMsg = `Request Header Created Successfully! Request ID: ${action.payload.requestId}`;
      state.error = null;
    },
    updateHeaderField: (state, action) => {
      const { field, value } = action.payload;
      state.requestHeader[field] = value;
    },
    setHeaderFinalized: (state, action) => {
      state.requestHeader.isHeaderFinalized = action.payload;
    },

    // Shared
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
      state.successMsg = '';
    },
    clearStatus: (state) => {
      state.error = null;
      state.successMsg = '';
    },

    // Full Reset
    resetCostCenterState: () => initialState,
  },
});

export const {
  // Rows
  setRows,
  updateRow,
  addRow,
  deleteRow,
  setSelectedRowId,
  updateTabFieldValues,

  // Tabs & Config
  setCostCenterTabs,
  setCostCenterConfig,
  setCostCenterError,
  setCostCenterLoading,

  // Request Header
  setRequestHeaderData,
  updateHeaderField,
  setHeaderFinalized,

  // Shared
  setLoading,
  setError,
  clearStatus,

  // Reset
  resetCostCenterState,
} = costCenterSlice.actions;

export default costCenterSlice.reducer;
