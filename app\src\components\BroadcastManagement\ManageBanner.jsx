import "react-dropzone-uploader/dist/styles.css";
import Dropzone from "react-dropzone-uploader";
import { 
  Box, 
  Button, 
  Grid, 
  Paper, 
  Stack, 
  Typography, 
  Modal,
  IconButton,
  Skeleton,
  Chip,
  Fade,
  alpha
} from "@mui/material";
import { useState, useEffect } from "react";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import { useDispatch, useSelector } from "react-redux";
import CloseIcon from '@mui/icons-material/Close';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import ImageIcon from '@mui/icons-material/Image';
import DeleteIcon from '@mui/icons-material/Delete';

const Layout = ({
  input,
  previews,
  submitButton,
  dropzoneProps,
  files,
  extra: { maxFiles },
}) => {
  const [DragView, setDragView] = useState([]);
  const dispatch = useDispatch();
  
  // Initialize DragView when previews change
  useEffect(() => {
    if (previews && Array.isArray(previews)) {
      const previewsWithIds = previews.map((preview, index) => ({
        ...preview,
        id: preview.key || `preview-${index}`,
        uniqueId: `${Date.now()}-${index}`
      }));
      setDragView(previewsWithIds);
    }
  }, [previews]);

  function handleOnDragEnd(result) {
    if (!result.destination) return;
    
    const items = Array.from(DragView);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    setDragView(items);
  }

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Paper
          elevation={0}
          sx={{ 
            border: "2px dashed",
            borderColor: "primary.main",
            borderRadius: "16px",
            padding: 4,
            minHeight: "320px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: alpha("#3B30C8", 0.02),
            transition: "all 0.3s ease",
            position: "relative",
            overflow: "hidden",
            "&:hover": {
              borderColor: "primary.dark",
              backgroundColor: alpha("#3B30C8", 0.04),
              transform: "translateY(-2px)",
              boxShadow: "0 8px 25px rgba(59, 48, 200, 0.15)"
            }
          }} 
          {...dropzoneProps}
        >
          {/* Gradient overlay */}
          <Box
            sx={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: "linear-gradient(135deg, rgba(59, 48, 200, 0.05) 0%, rgba(59, 48, 200, 0.02) 100%)",
              zIndex: 0
            }}
          />
          
          <Box textAlign="center" sx={{ position: "relative", zIndex: 1 }}>
            <CloudUploadIcon 
              sx={{ 
                fontSize: 48, 
                color: "primary.main", 
                mb: 2,
                animation: "bounce 2s infinite"
              }} 
            />
            <Typography
              sx={{
                color: "primary.main",
                fontSize: "18px",
                fontWeight: 600,
                mb: 1,
                letterSpacing: "0.5px"
              }}
            >
              Drag & Drop Your Files Here
            </Typography>
            <Typography
              sx={{ 
                fontSize: "14px", 
                mb: 3,
                color: "text.secondary",
                fontWeight: 500
              }}
            >
              or click to browse
            </Typography>
            <Box mb={3} sx={{ "& > *": { borderRadius: "12px !important" } }}>
              {input}
            </Box>
            <Chip
              icon={<ImageIcon />}
              label={`PNG, JPG, SVG • Max ${maxFiles} files`}
              variant="outlined"
              sx={{ 
                color: "text.secondary",
                borderColor: "divider",
                backgroundColor: "background.paper"
              }}
            />
          </Box>
        </Paper>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <Stack spacing={2} sx={{ height: "100%" }}>
          <Box>
            <Typography 
              variant="h6" 
              sx={{ 
                fontWeight: 600, 
                color: "text.primary",
                display: "flex",
                alignItems: "center",
                gap: 1
              }}
            >
              <ImageIcon color="primary" />
              Uploaded Files ({DragView.length})
            </Typography>
            <Typography 
              variant="body2" 
              sx={{ 
                color: "text.secondary",
                mt: 0.5,
                fontStyle: "italic"
              }}
            >
              Drag items to reorder • Click to remove
            </Typography>
          </Box>
          
          <Paper
            elevation={0}
            sx={{ 
              flex: 1,
              maxHeight: "280px", 
              overflowY: "auto",
              border: "1px solid",
              borderColor: "divider",
              borderRadius: "12px",
              backgroundColor: "grey.50",
              p: 1
            }}
          >
            {DragView.length > 0 ? (
              <DragDropContext onDragEnd={handleOnDragEnd}>
                <Droppable droppableId="droppable">
                  {(provided, snapshot) => (
                    <Stack 
                      {...provided.droppableProps} 
                      ref={provided.innerRef}
                      spacing={1}
                      sx={{
                        minHeight: "100px",
                        backgroundColor: snapshot.isDraggingOver ? alpha("#3B30C8", 0.05) : "transparent",
                        borderRadius: "8px",
                        transition: "background-color 0.2s ease"
                      }}
                    >
                      {DragView.map((item, index) => {
                        const draggableId = item.uniqueId || item.id || `draggable-${index}`;
                        return (
                          <Draggable
                            key={draggableId}
                            index={index}
                            draggableId={draggableId}
                          >
                            {(provided, snapshot) => (
                              <Paper
                                elevation={snapshot.isDragging ? 8 : 1}
                                sx={{ 
                                  position: "relative",
                                  backgroundColor: snapshot.isDragging ? "primary.main" : "background.paper",
                                  borderRadius: "12px",
                                  p: 2,
                                  transition: "all 0.2s ease",
                                  border: snapshot.isDragging ? "2px solid" : "1px solid",
                                  borderColor: snapshot.isDragging ? "primary.light" : "divider",
                                  transform: snapshot.isDragging ? "rotate(2deg)" : "none",
                                  cursor: "grab",
                                  "&:hover": {
                                    boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                                    transform: "translateY(-1px)"
                                  }
                                }}
                                {...provided.draggableProps}
                                ref={provided.innerRef}
                              >
                                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                                  <Box
                                    {...provided.dragHandleProps}
                                    sx={{
                                      display: "flex",
                                      alignItems: "center",
                                      color: snapshot.isDragging ? "white" : "text.secondary",
                                      "&:hover": { color: "primary.main" }
                                    }}
                                  >
                                    <DragIndicatorIcon />
                                  </Box>
                                  
                                  <Box sx={{ flex: 1, display: "flex", alignItems: "center" }}>
                                    {item}
                                  </Box>
                                  
                                  <Box sx={{ minWidth: 120 }}>
                                    <Typography 
                                      variant="body2" 
                                      sx={{ 
                                        fontWeight: 500,
                                        color: snapshot.isDragging ? "white" : "text.primary",
                                        textAlign: "right"
                                      }}
                                    >
                                      {item.props?.meta?.name || `Image ${index + 1}`}
                                    </Typography>
                                  </Box>
                                </Box>
                              </Paper>
                            )}
                          </Draggable>
                        );
                      })}
                      {provided.placeholder}
                    </Stack>
                  )}
                </Droppable>
              </DragDropContext>
            ) : (
              <Box 
                sx={{ 
                  display: "flex", 
                  flexDirection: "column", 
                  alignItems: "center", 
                  justifyContent: "center",
                  height: "200px",
                  color: "text.secondary"
                }}
              >
                <ImageIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  No files uploaded yet
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.7 }}>
                  Upload some files to see them here
                </Typography>
              </Box>
            )}
          </Paper>
          
          {files.length > 0 && (
            <Fade in={true}>
              <Box sx={{ pt: 1 }}>
                {submitButton}
              </Box>
            </Fade>
          )}
        </Stack>
      </Grid>
    </Grid>
  );
};

const FileDropZone = (props) => {
  const [initialFiles, setInitialFiles] = useState([]);
  
  // Convert uploaded files to File objects for dropzone
  const convertUploadedFiles = async () => {
    if (!props?.uploadedFiles || props.uploadedFiles.length === 0) {
      setInitialFiles([]);
      return;
    }

    try {
      const filePromises = props.uploadedFiles.map(async (item, index) => {
        if (!item.url || !item.name) return null;
        
        try {
          const response = await fetch(item.url);
          const blob = await response.blob();
          
          return new File([blob], item.name, {
            type: item.type || 'image/jpeg',
          });
        } catch (error) {
          console.error(`Error converting file ${item.name}:`, error);
          return null;
        }
      });

      const convertedFiles = await Promise.all(filePromises);
      const validFiles = convertedFiles.filter(file => file !== null);
      setInitialFiles(validFiles);
    } catch (error) {
      console.error('Error converting uploaded files:', error);
      setInitialFiles([]);
    }
  };

  useEffect(() => {
    convertUploadedFiles();
  }, [props?.uploadedFiles]);

  const handleChangeStatus = ({ meta, file, xhr }, status) => {
    console.log('File status changed:', { meta: meta.name, status });
  };

  return (
    <div id="imageUpload" className="dropzone">
      <style jsx>{`
        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
          }
          40% {
            transform: translateY(-10px);
          }
          60% {
            transform: translateY(-5px);
          }
        }
      `}</style>
      <Dropzone
        onSubmit={props?.handleAttachmentsSubmit}
        onChangeStatus={handleChangeStatus}
        styles={{
          submitButton: {
            background: "linear-gradient(135deg, #3B30C8 0%, #5B4FD1 100%)",
            color: "white",
            border: "none",
            borderRadius: "12px",
            padding: "12px 24px",
            cursor: "pointer",
            fontWeight: 600,
            fontSize: "14px",
            transition: "all 0.3s ease",
            boxShadow: "0 4px 12px rgba(59, 48, 200, 0.3)",
            "&:hover": {
              transform: "translateY(-2px)",
              boxShadow: "0 6px 20px rgba(59, 48, 200, 0.4)"
            }
          },
          dropzone: {
            border: "none",
            padding: 0,
            overflow: "hidden",
            backgroundColor: "transparent",
          },
          submitButtonContainer: { 
            textAlign: "right",
            marginTop: "8px",
          },
          previewImage: {
            borderRadius: "8px",
            width: "60px",
            height: "60px",
            objectFit: "cover",
            border: "2px solid #e0e0e0"
          },
          preview: { 
            padding: "0",
            border: "none",
            borderRadius: "0",
            marginBottom: "0",
            backgroundColor: "transparent",
            display: "flex",
            alignItems: "center"
          },
          inputLabel: {
            textTransform: "none",
            height: "44px",
            background: "linear-gradient(135deg, #3B30C8 0%, #5B4FD1 100%)",
            color: "white",
            fontWeight: 600,
            padding: "12px 24px",
            borderRadius: "12px",
            border: "none",
            cursor: "pointer",
            fontSize: "14px",
            transition: "all 0.3s ease",
            boxShadow: "0 4px 12px rgba(59, 48, 200, 0.3)",
            "&:hover": {
              transform: "translateY(-2px)",
              boxShadow: "0 6px 20px rgba(59, 48, 200, 0.4)"
            }
          },
          inputLabelWithFiles: {
            textTransform: "none",
            background: "linear-gradient(135deg, #3B30C8 0%, #5B4FD1 100%)",
            color: "white",
            fontWeight: 600,
            padding: "10px 20px",
            borderRadius: "12px",
            border: "none",
            cursor: "pointer",
            fontSize: "13px",
            transition: "all 0.3s ease",
            boxShadow: "0 2px 8px rgba(59, 48, 200, 0.3)"
          },
        }}
        LayoutComponent={Layout}
        inputContent="Choose Files"
        inputWithFilesContent="Add More"
        maxFiles={4}
        initialFiles={initialFiles}
        submitButtonContent="Save Changes"
        accept="image/*"
      />
    </div>
  );
};

// Enhanced Modal Styles
const modalStyle = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 900,
  maxWidth: '95vw',
  bgcolor: 'background.paper',
  borderRadius: '20px',
  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
  p: 0,
  maxHeight: '90vh',
  overflow: 'hidden',
  border: '1px solid',
  borderColor: 'divider'
};

const headerStyle = {
  p: 3,
  background: 'linear-gradient(135deg,rgb(104, 214, 247) 0%, #5B4FD1 100%)',
  color: 'white',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  position: 'relative',
  overflow: 'hidden',
  "&::before": {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
    zIndex: 0
  }
};

const contentStyle = {
  p: 3,
  maxHeight: 'calc(90vh - 140px)',
  overflow: 'auto',
  backgroundColor: '#fafafa'
};

const ManageBanner = ({
  open,
  handleOpen,
  handleClose,
  handleAttachmentsSubmit,
  uploadedFiles = [],
  skeleton = false
}) => {
  
  const handleModalClose = (event, reason) => {
    // Prevent closing when clicking outside if skeleton is loading
    if (skeleton && reason === 'backdropClick') {
      return;
    }
    handleClose();
  };

  return (
    <Modal
      open={open}
      onClose={handleModalClose}
      aria-labelledby="manage-banner-title"
      aria-describedby="manage-banner-description"
      disableEscapeKeyDown={skeleton}
      closeAfterTransition
    >
      <Fade in={open}>
        <Box sx={modalStyle}>
          {/* Enhanced Header */}
          <Box sx={headerStyle}>
            <Box sx={{ position: 'relative', zIndex: 1 }}>
              <Typography 
                id="manage-banner-title" 
                variant="h5" 
                component="h2"
                sx={{ 
                  fontWeight: 700,
                  color: "white",
                  letterSpacing: '0.5px',
                  textShadow: '0 2px 4px rgba(255, 255, 255, 0.1)'
                }}
              >
                Manage Banners
              </Typography>
              <Typography 
                variant="body2" 
                sx={{ 
                  opacity: 0.9, 
                  mt: 0.5,
                  fontSize: '13px'
                }}
              >
                Upload and organize your banner images
              </Typography>
            </Box>
            {!skeleton && (
              <IconButton 
                onClick={handleClose}
                size="small"
                sx={{ 
                  color: 'white',
                  backgroundColor: 'rgba(255,255,255,0.1)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  position: 'relative',
                  zIndex: 1,
                  '&:hover': {
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    transform: 'scale(1.05)'
                  }
                }}
              >
                <CloseIcon />
              </IconButton>
            )}
          </Box>

          {/* Enhanced Content */}
          <Box sx={contentStyle}>
            {skeleton ? (
              <SkeletonLoader />
            ) : (
              <FileDropZone  
                uploadedFiles={uploadedFiles}
                handleAttachmentsSubmit={handleAttachmentsSubmit}
                closeModal={handleClose}
              />
            )}
          </Box>
        </Box>
      </Fade>
    </Modal>
  );
};

// Enhanced Skeleton Component
const SkeletonLoader = () => (
  <Grid container spacing={3}>
    <Grid item xs={12} md={6}>
      <Skeleton 
        animation="wave" 
        variant="rectangular" 
        width="100%" 
        height={320}
        sx={{ borderRadius: '16px' }}
      />
    </Grid>
    <Grid item xs={12} md={6}>
      <Stack spacing={2}>
        <Skeleton 
          animation="wave" 
          variant="text" 
          width="70%" 
          height={32}
          sx={{ borderRadius: '8px' }}
        />
        <Skeleton 
          animation="wave" 
          variant="text" 
          width="90%" 
          height={20}
          sx={{ borderRadius: '4px' }}
        />
        <Box sx={{ p: 1, backgroundColor: 'grey.100', borderRadius: '12px' }}>
          {[...Array(4)].map((_, index) => (
            <Skeleton 
              key={index}
              animation="wave" 
              variant="rectangular" 
              width="100%" 
              height={80}
              sx={{ borderRadius: '12px', mb: 1 }}
            />
          ))}
        </Box>
      </Stack>
    </Grid>
  </Grid>
);

export default ManageBanner;