const configData = {
  //   "applicationProperties": {
  //     "taskClassification": "PROCESS",
  //     "taskDisplay": {
  //       "taskIdentifier": true,
  //       "taskDescription": true,
  //       "systemChip": true,
  //       "processChip": true
  //     },
  //     "taskSettings": {
  //       "SYSTEM": {
  //         "DocuSignComponent": [
  //           "DocuSign",
  //           "DocuSign_QA",
  //           "DocuSign_MOBILE",
  //           "DocuSign_WORKACCESS"
  //         ],
  //         "ECC": {
  //           "delete": true
  //         },
  //         "Default": {
  //           "enable": true,
  //           "delete": true
  //         }
  //       }
  //     },
  //     "default": {
  //       "allowInAppNotification": false,
  //       "forwardOptions": null,
  //       "primaryActions": "ALL",
  //       "secondaryActions": "ALL",
  //       "tertiaryActions": "ALL",
  //       "allowAttachments": "ALL",
  //       "allowAttachmentsInCreateTask": true,
  //       "isSessionExpiryEnabled": false,
  //       "sessionExpiresIn": 600,
  //       "adminTasksPerPage": 50,
  //       "enableAIBasedCommentGeneration": false,
  //       "enableTaskSwitchInDetail": false,
  //       "isCrudServiceReplaced": false,
  //       "isCustomForwardUserList": false,
  //       "customForwardUserListURL": "/ColPalServices/rest/v1/price-book/approval/user-list?taskInstanceId={$taskId}&isBackupUser=true",
  //       "customAPIForActions": false
  //     },
  //     "attachmentsConfigs": {
  //       "maximumFileNameLength": 255,
  //       "maximumDescriptionLength": 255,
  //       "maxAttachmentSizeInMB": 25,
  //       "allowAttachmentDesc": true,
  //       "defaultAttachmentDecription": "Document",
  //       "allowedMIMETypes": [
  //         "text/csv",
  //         "application/msword",
  //         "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  //         "image/jpeg",
  //         "image/jpg",
  //         "application/vnd.oasis.opendocument.presentation",
  //         "application/vnd.oasis.opendocument.text",
  //         "image/png",
  //         "application/pdf",
  //         "application/vnd.ms-powerpoint",
  //         "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  //         "application/rtf",
  //         "text/plain",
  //         "application/vnd.ms-excel",
  //         "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  //         "application/xml",
  //         "text/xml",
  //         "application/atom+xml",
  //         "application/xml",
  //         "application/vnd.openxmlformats-officedocument.presentationml.slideshow"
  //       ]
  //     },
  //     "workspaceConfigs": {
  //       "searchEnabled": true,
  //       "searchPlaceholder": "Search",
  //       "exportEnabled": true,
  //       "exportButtonText": "Export",
  //       "defaultSortSelectEnabled": true,
  //       "defaultTaskSorterKey": "updatedOn-desc"
  //     },
  //     "workspaceColumns": [
  //       {
  //         "width": "8%",
  //         "label": "",
  //         "accessorKey": "selection_pinning",
  //         "shouldRedirect": false,
  //         "isSortable": false,
  //         "sortingParams": [],
  //         "enabled": {
  //           "CREATED_TASKS": true,
  //           "MY_TASKS": true,
  //           "SUBSTITUTED_TO_OTHERS": true,
  //           "MY_COMPLETED_TASKS": false,
  //           "ADMIN_TASKS": true,
  //           "ADMIN_COMPLETED_TASKS": false
  //         }
  //       },
  //       {
  //         "width": "26%",
  //         "label": "Request Id",
  //         "accessorKey": "subject",
  //         "isSortable": true,
  //         "sortingParams": ["subject"],
  //         "shouldRedirect": false,
  //         "enabled": {
  //           "CREATED_TASKS": true,
  //           "MY_TASKS": true,
  //           "SUBSTITUTED_TO_OTHERS": true,
  //           "MY_COMPLETED_TASKS": true,
  //           "ADMIN_TASKS": true,
  //           "ADMIN_COMPLETED_TASKS": true
  //         }
  //       },
  //       {
  //         "width": "26%",
  //         "label": "Task Name",
  //         "accessorKey": "taskDesc",
  //         "isSortable": true,
  //         "sortingParams": ["taskDesc"],
  //         "shouldRedirect": false,
  //         "enabled": {
  //            "CREATED_TASKS": true,
  //            "MY_TASKS": true,
  //            "SUBSTITUTED_TO_OTHERS": true,
  //            "MY_COMPLETED_TASKS": true,
  //            "ADMIN_TASKS": true,
  //            "ADMIN_COMPLETED_TASKS": true
  //         }
  //      },
  
  //       {
  //         "width": "15%",
  //         "label": "Request Type",
  //         "accessorKey": "processDesc",
  //         "isSortable": true,
  //         "sortingParams": ["processDesc"],
  //         "shouldRedirect": false,
  //         "enabled": {
  //           "CREATED_TASKS": true,
  //           "MY_TASKS": true,
  //           "SUBSTITUTED_TO_OTHERS": true,
  //           "MY_COMPLETED_TASKS": true,
  //           "ADMIN_TASKS": true,
  //           "ADMIN_COMPLETED_TASKS": true
  //         }
  //       },
  //       {
  //         "width": "20%",
  //         "label": "System & Process",
  //         "accessorKey": "system_process",
  //         "isSortable": true,
  //         "sortingParams": ["processDisplayName"],
  //         "shouldRedirect": false,
  //         "showIcons": true,
  //         "enabled": {
  //           "CREATED_TASKS": true,
  //           "MY_TASKS": true,
  //           "SUBSTITUTED_TO_OTHERS": true,
  //           "MY_COMPLETED_TASKS": true,
  //           "ADMIN_TASKS": true,
  //           "ADMIN_COMPLETED_TASKS": true
  //         }
  //       },
  //       {
  //         "width": "20%",
  //         "label": "Due Date",
  //         "accessorKey": "dueDate",
  //         "isSortable": true,
  //         "sortingParams": ["compDeadline"],
  //         "shouldRedirect": false,
  //         "enabled": {
  //           "CREATED_TASKS": true,
  //           "MY_TASKS": true,
  //           "SUBSTITUTED_TO_OTHERS": true,
  //           "MY_COMPLETED_TASKS": true,
  //           "ADMIN_TASKS": true,
  //           "ADMIN_COMPLETED_TASKS": true
  //         }
  //       },
  //       {
  //         "width": "14%",
  //         "label": "Status",
  //         "accessorKey": "status",
  //         "isSortable": true,
  //         "sortingParams": ["itmStatus", "technicalStatus"],
  //         "shouldRedirect": false,
  //         "enabled": {
  //           "CREATED_TASKS": true,
  //           "MY_TASKS": true,
  //           "SUBSTITUTED_TO_OTHERS": true,
  //           "MY_COMPLETED_TASKS": true,
  //           "ADMIN_TASKS": true,
  //           "ADMIN_COMPLETED_TASKS": true
  //         }
  //       },
  //       {
  //         "width": "6%",
  //         "label": "Actions",
  //         "accessorKey": "actions",
  //         "isSortable": false,
  //         "sortingParams": [],
  //         "shouldRedirect": false,
  //         "enabled": {
  //           "CREATED_TASKS": true,
  //           "MY_TASKS": true,
  //           "SUBSTITUTED_TO_OTHERS": true,
  //           "MY_COMPLETED_TASKS": false,
  //           "ADMIN_TASKS": true,
  //           "ADMIN_COMPLETED_TASKS": false
  //         }
  //       }
  //     ]
  //   },
  //   "taskPermissions": {
  //     "attachments": {
  //       "ADMIN_TASKS": {
  //         "upload": false,
  //         "enabled": true
  //       },
  //       "ADMIN_COMPLETED_TASKS": {
  //         "upload": false,
  //         "enabled": true
  //       },
  //       "CREATED_TASKS": {
  //         "upload": true,
  //         "enabled": true
  //       },
  //       "MY_TASKS": {
  //         "upload": true,
  //         "enabled": true
  //       },
  //       "SUBSTITUTED_TO_OTHERS": {
  //         "upload": false,
  //         "enabled": true
  //       },
  //       "MY_FILTERS": {
  //         "upload": true,
  //         "enabled": true
  //       },
  //       "MY_COMPLETED_TASKS": {
  //         "upload": false,
  //         "enabled": true
  //       },
  //       "DEFAULT": {
  //         "upload": true,
  //         "enabled": true
  //       }
  //     },
  //     "collaboration": {
  //       "ADMIN_TASKS": {
  //         "postComment": false,
  //         "enabled": true
  //       },
  //       "ADMIN_COMPLETED_TASKS": {
  //         "postComment": false,
  //         "enabled": true
  //       },
  //       "CREATED_TASKS": {
  //         "postComment": true,
  //         "enabled": true
  //       },
  //       "MY_TASKS": {
  //         "postComment": true,
  //         "enabled": true
  //       },
  //       "SUBSTITUTED_TO_OTHERS": {
  //         "postComment": true,
  //         "enabled": true
  //       },
  //       "MY_FILTERS": {
  //         "postComment": true,
  //         "enabled": true
  //       },
  //       "MY_COMPLETED_TASKS": {
  //         "postComment": true,
  //         "enabled": true
  //       }
  //     },
  //     "acitivityLogs": {
  //       "ADMIN_TASKS": {
  //         "enabled": true
  //       },
  //       "ADMIN_COMPLETED_TASKS": {
  //         "enabled": true
  //       },
  //       "CREATED_TASKS": {
  //         "enabled": true
  //       },
  //       "MY_TASKS": {
  //         "enabled": true
  //       },
  //       "SUBSTITUTED_TO_OTHERS": {
  //         "enabled": true
  //       },
  //       "MY_FILTERS": {
  //         "enabled": true
  //       },
  //       "MY_COMPLETED_TASKS": {
  //         "enabled": true
  //       }
  //     },
  //     "workflow": {
  //       "ADMIN_TASKS": {
  //         "enabled": true
  //       },
  //       "ADMIN_COMPLETED_TASKS": {
  //         "enabled": true
  //       },
  //       "CREATED_TASKS": {
  //         "enabled": true
  //       },
  //       "MY_TASKS": {
  //         "enabled": true
  //       },
  //       "SUBSTITUTED_TO_OTHERS": {
  //         "enabled": true
  //       },
  //       "MY_FILTERS": {
  //         "enabled": true
  //       },
  //       "MY_COMPLETED_TASKS": {
  //         "enabled": true
  //       }
  //     },
  //     "taskForms": {
  //       "ADMIN_TASKS": {
  //         "enabled": true
  //       },
  //       "ADMIN_COMPLETED_TASKS": {
  //         "enabled": true
  //       },
  //       "CREATED_TASKS": {
  //         "enabled": true
  //       },
  //       "MY_TASKS": {
  //         "enabled": true
  //       },
  //       "SUBSTITUTED_TO_OTHERS": {
  //         "enabled": true
  //       },
  //       "MY_FILTERS": {
  //         "enabled": true
  //       },
  //       "MY_COMPLETED_TASKS": {
  //         "enabled": true
  //       }
  //     },
  //     "primaryActions": {
  //       "ADMIN_TASKS": {
  //         "enabled": false
  //       },
  //       "ADMIN_COMPLETED_TASKS": {
  //         "enabled": false
  //       },
  //       "CREATED_TASKS": {
  //         "enabled": true
  //       },
  //       "MY_TASKS": {
  //         "enabled": true
  //       },
  //       "SUBSTITUTED_TO_OTHERS": {
  //         "enabled": true
  //       },
  //       "MY_FILTERS": {
  //         "enabled": true
  //       },
  //       "MY_COMPLETED_TASKS": {
  //         "enabled": false
  //       }
  //     },
  //     "secondaryActions": {
  //       "ADMIN_TASKS": {
  //         "enabled": true
  //       },
  //       "ADMIN_COMPLETED_TASKS": {
  //         "enabled": false
  //       },
  //       "CREATED_TASKS": {
  //         "enabled": true
  //       },
  //       "MY_TASKS": {
  //         "enabled": true
  //       },
  //       "SUBSTITUTED_TO_OTHERS": {
  //         "enabled": true
  //       },
  //       "MY_FILTERS": {
  //         "enabled": true
  //       },
  //       "MY_COMPLETED_TASKS": {
  //         "enabled": false
  //       }
  //     },
  //     "tertiaryActions": {
  //       "ADMIN_TASKS": {
  //         "enabled": true
  //       },
  //       "ADMIN_COMPLETED_TASKS": {
  //         "enabled": false
  //       },
  //       "CREATED_TASKS": {
  //         "enabled": true
  //       },
  //       "MY_TASKS": {
  //         "enabled": true
  //       },
  //       "SUBSTITUTED_TO_OTHERS": {
  //         "enabled": true
  //       },
  //       "MY_FILTERS": {
  //         "enabled": true
  //       },
  //       "MY_COMPLETED_TASKS": {
  //         "enabled": false
  //       }
  //     },
  //     "multiSelect": {
  //       "MY_TASKS": {
  //         "enabled": true,
  //         "permittedActions": {
  //           "APPROVE": true,
  //           "REJECT": true,
  //           "FORWARD": true,
  //           "CLAIM": true,
  //           "RELEASE": true
  //         }
  //       },
  //       "ADMIN_TASKS": {
  //         "enabled": true,
  //         "permittedActions": {
  //           "APPROVE": false,
  //           "REJECT": false,
  //           "FORWARD": true,
  //           "CLAIM": false,
  //           "RELEASE": false
  //         }
  //       },
  //       "MY_FILTERS": {
  //         "enabled": true,
  //         "permittedActions": {
  //           "APPROVE": true,
  //           "REJECT": true,
  //           "FORWARD": true,
  //           "CLAIM": true,
  //           "RELEASE": true
  //         }
  //       },
  //       "CREATED_TASKS": {
  //         "enabled": true,
  //         "permittedActions": {
  //           "APPROVE": false,
  //           "REJECT": false,
  //           "FORWARD": false,
  //           "CLAIM": false,
  //           "RELEASE": false
  //         }
  //       },
  //       "SUBSTITUTED_TO_OTHERS": {
  //         "enabled": true,
  //         "permittedActions": {
  //           "APPROVE": true,
  //           "REJECT": true,
  //           "FORWARD": true,
  //           "CLAIM": true,
  //           "RELEASE": true
  //         }
  //       }
  //     },
  //     "pin": {
  //       "ADMIN_TASKS": {
  //         "enabled": true
  //       },
  //       "ADMIN_COMPLETED_TASKS": {
  //         "enabled": false
  //       },
  //       "CREATED_TASKS": {
  //         "enabled": true
  //       },
  //       "MY_TASKS": {
  //         "enabled": true
  //       },
  //       "SUBSTITUTED_TO_OTHERS": {
  //         "enabled": true
  //       },
  //       "MY_FILTERS": {
  //         "enabled": true
  //       },
  //       "MY_COMPLETED_TASKS": {
  //         "enabled": false
  //       }
  //     },
  //     "SLABand": {
  //       "ADMIN_TASKS": {
  //         "enabled": true
  //       },
  //       "ADMIN_COMPLETED_TASKS": {
  //         "enabled": false
  //       },
  //       "CREATED_TASKS": {
  //         "enabled": true
  //       },
  //       "MY_TASKS": {
  //         "enabled": true
  //       },
  //       "SUBSTITUTED_TO_OTHERS": {
  //         "enabled": true
  //       },
  //       "MY_FILTERS": {
  //         "enabled": true
  //       },
  //       "MY_COMPLETED_TASKS": {
  //         "enabled": false
  //       }
  //     }
  //   },
  //   "btpServerSideEventUrlMap":  {
  //     "notificationUrl": "https://cherryworkproducts-messaging-"+import.meta.env.MODE+".cfapps.eu10.hana.ondemand.com",
  //     "cachingUrl": "https://cherryworkproducts-itm-java-"+import.meta.env.MODE+".cfapps.eu10.hana.ondemand.com",
  //     "collaborationUrl": "https://cherryworkproducts-itm-java-"+import.meta.env.MODE+".cfapps.eu10.hana.ondemand.com"
  //   }
  // }
  
  
  //New Version
    "applicationProperties": {
       "CRUD_API_ENV": "itm",
       "taskClassification": "PROCESS",
       "APPLICATION_NAME": "2",
       "processInboxFilter": [
        "MY_TASKS",
        "MY_COMPLETED_TASKS",
        "MY_FILTERS",
        "SUBSTITUTED_TO_OTHERS",
        "DRAFT",
        "MY_OUTBOX",
        "CREATED_TASKS",
        "ADMIN_TASKS",
        "ADMIN_COMPLETED_TASKS"
       ],
       "taskDisplay": {
          "taskIdentifier": false,
          "taskDescription": true,
          "systemChip": true,
          "processChip": true
       },
       "taskDetailsHeader": {
          "status": {
             "enable": true,
             "systemHidden": []
          },
          "dueDate": {
             "enable": true
          },
          "createdDate": {
             "enable": true
          },
          "processName": {
             "enable": true
          },
          "owner": {
             "enable": true
          }
       },
       "taskSettings": {
          "SYSTEM": {
             "DocuSignComponent": [
                "DocuSign",
                "DocuSign_QA",
                "DocuSign_MOBILE",
                "DocuSign_WORKACCESS"
             ],
             "ECC": {
                "delete": true
             },
             "Default": {
                "enable": true,
                "delete": true
             }
          }
       },
       "workflowApps": {
          "ChartOfAccountsApp": "https://iffcotech-limited-fzc-iffcodevprocessautomation-dev-coa-app-ui.cfapps.eu10.hana.ondemand.com/index.html#/createtask",
          "ARBadDebtsApp": "https://iffcotech-limited-fzc-iffcodevprocessautomation-dev-arbd-app-ui.cfapps.eu10.hana.ondemand.com/index.html#/CreateBadDebts",
          "FixedAssetsApp": "https://iffcotech-limited-fzc-iffcodevprocessautomation-dev-fa-app-ui.cfapps.eu10.hana.ondemand.com/index.html#/CreateFARequest",
          "InventoryApp": "https://iffcotech-limited-fzc-iffcodevprocessautomation-dev-inv-app-ui.cfapps.eu10.hana.ondemand.com/index.html#/createInventorytask",
          "ASLMApp": "https://iffcotech-limited-fzc-iffcodevprocessautomation-dev-aslm-app-ui.cfapps.eu10.hana.ondemand.com/index.html#/CreateASLMRequest"
       },
       "substitutionSettings": {
         "substitutionDeleteIconForSystem": ["SCP","ECC"],
         "substitutionEnableDisableIconForSystem": [],
       },
       "default": {
          "allowInAppNotification": false,
          "forwardOptions": null,
          "primaryActions": "ALL",
          "secondaryActions": "ALL",
          "tertiaryActions": "ALL",
          "allowAttachments": "ALL",
          "allowAttachmentsInCreateTask": true,
          "isSessionExpiryEnabled": false,
          "sessionExpiresIn": 600,
          "adminTasksPerPage": 50,
          "enableAIBasedCommentGeneration": false,
          "enableTaskSwitchInDetail": false,
          "isCrudServiceReplaced": false,
          "isCustomForwardUserList": true,
          "customForwardUserListURL": "/cw-mdg-materialmanagement/workflow/fetchAllIASUsersForward",
          "customAPIForActions": false,
          "enableDetailPageTasksNavigation": true,
          "dateTimeFormat": null,
          "useRelativedateTime": true,
          "userPreferedHomeScreen": false,
          "actionDisabledForProcess": [
             "manualjournals"
          ],
          "multiSelectDisabledProcesses": [
             "accountreceviables",
             "COA_GLcreation",
             "fixedasset",
             "inventory",
             "Aslm",
             "manualjournals"
          ],
          "filterServiceDateKeys": [
             "createdOn",
             "compDeadline",
             "criticalDeadline",
             "updatedOn",
             "completedAt",
             "updatedOnForProcess",
             "completedAtForProcess",
             "createdOnForProcess"
          ],
          "taskActionsRedirect": false
       },
       "attachmentsConfigs": {
          "maximumFileNameLength": 255,
          "maximumDescriptionLength": 255,
          "maxAttachmentSizeInMB": 25,
          "allowAttachmentDesc": true,
          "defaultAttachmentDecription": "Document",
          "allowedMIMETypes": [
             "text/csv",
             "application/msword",
             "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
             "image/jpeg",
             "image/jpg",
             "application/vnd.oasis.opendocument.presentation",
             "application/vnd.oasis.opendocument.text",
             "image/png",
             "application/pdf",
             "application/vnd.ms-powerpoint",
             "application/vnd.openxmlformats-officedocument.presentationml.presentation",
             "application/rtf",
             "text/plain",
             "application/vnd.ms-excel",
             "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
             "application/xml",
             "text/xml",
             "application/atom+xml",
             "application/xml",
             "application/vnd.openxmlformats-officedocument.presentationml.slideshow"
          ]
       },
       "workspaceHeaderConfigs": {
          "searchEnabled": true,
          "searchPlaceholder": "Search",
          "exportEnabled": true,
          "exportButtonText": "Export",
          "defaultSortSelectEnabled": true,
          "defaultTaskSorterKey": "updatedOn-desc",
          "defaultTasksSorterData": {
             "DEFAULT": [
                {
                   "key": "updatedOn-desc",
                   "label": "Newest First"
                },
                {
                   "key": "updatedOn-asc",
                   "label": "Oldest First"
                },
                {
                   "key": "compDeadline-asc",
                   "label": "Delayed First"
                },
                {
                   "key": "compDeadline-desc",
                   "label": "Delayed Last"
                }
             ],
             "DRAFT": [
                {
                   "key": "createdOn-desc",
                   "label": "Newest First"
                },
                {
                   "key": "createdOn-asc",
                   "label": "Oldest First"
                }
             ]
          }
       },
       "workspaceConfigs": {
          "searchEnabled": true,
          "searchPlaceholder": "Search",
          "exportEnabled": true,
          "exportButtonText": "Export",
          "defaultSortSelectEnabled": true,
          "defaultTaskSorterKey": "updatedOn-desc",
          "showSystemIcons": true,
          "refreshButtonEnabled": false,
          "refreshButtonLabel": "Refresh",
          "refreshDisabledTime": 60000,
          "isDisableSaveButton":true
       },
       "workspaceColumns": [
          {
             "width": "8%",
             "label": "",
             "accessorKey": "selection_pinning",
             "isSortable": false,
             "sortingParams": [],
             "shouldRedirect": false,
             "enabled": {
                "CREATED_TASKS": true,
                "MY_TASKS": true,
                "SUBSTITUTED_TO_OTHERS": true,
                "MY_COMPLETED_TASKS": false,
                "ADMIN_TASKS": true,
                "ADMIN_COMPLETED_TASKS": false,
                "MY_OUTBOX": false,
                "DRAFT": false
             }
          },
          {
            "width": "26%",
            "label": "Request Id",
            "accessorKey": "ATTRIBUTE_1", //Changed
            "isSortable": true,
            "sortingParams": ["ATTRIBUTE_1"],  //Changed
            "shouldRedirect": false,
            "enabled": {
              "CREATED_TASKS": true,
              "MY_TASKS": true,
              "SUBSTITUTED_TO_OTHERS": true,
              "MY_COMPLETED_TASKS": true,
              "ADMIN_TASKS": true,
              "ADMIN_COMPLETED_TASKS": true
            }
          },
          {
             "width": "26%",
             "label": "Task Name",
             "accessorKey": "taskDesc",
             "isSortable": true,
             "sortingParams": ["taskDesc"],
             "shouldRedirect": false,
             "enabled": {
                "CREATED_TASKS": true,
                "MY_TASKS": true,
                "SUBSTITUTED_TO_OTHERS": true,
                "MY_COMPLETED_TASKS": true,
                "ADMIN_TASKS": true,
                "ADMIN_COMPLETED_TASKS": true,
                "MY_OUTBOX": true,
                "DRAFT": false
             }
          },
          {
            "width": "15%",
            "label": "Request Type",
            "accessorKey": "ATTRIBUTE_2",   //Changed
            "isSortable": true,
            "sortingParams": ["ATTRIBUTE_2"],  //Changed
            "shouldRedirect": false,
            "enabled": {
              "CREATED_TASKS": true,
              "MY_TASKS": true,
              "SUBSTITUTED_TO_OTHERS": true,
              "MY_COMPLETED_TASKS": true,
              "ADMIN_TASKS": true,
              "ADMIN_COMPLETED_TASKS": true,
                "MY_OUTBOX": false,
                "DRAFT": false
             }
          },
          {
             "width": "20%",
             "label": "System & Process",
             "accessorKey": "system_process",
             "isSortable": true,
             "sortingParams": ["processDisplayName"],
             "shouldRedirect": false,
             "showIcons": true,
             "enabled": {
                "CREATED_TASKS": true,
                "MY_TASKS": true,
                "SUBSTITUTED_TO_OTHERS": true,
                "MY_COMPLETED_TASKS": true,
                "ADMIN_TASKS": true,
                "ADMIN_COMPLETED_TASKS": true,
                "MY_OUTBOX": true,
                "DRAFT": false
             }
          },
          {
             "width": "15%",
             "label": "System & Process",
             "accessorKey": "processDesc",
             "isSortable": true,
             "sortingParams": [
                "processDesc"
             ],
             "shouldRedirect": false,
             "showIcons": true,
             "enabled": {
                "CREATED_TASKS": false,
                "MY_TASKS": false,
                "SUBSTITUTED_TO_OTHERS": false,
                "MY_COMPLETED_TASKS": false,
                "ADMIN_TASKS": false,
                "ADMIN_COMPLETED_TASKS": false,
                "MY_OUTBOX": false,
                "DRAFT": true
             }
          },
          {
             "width": "15%",
             "label": "Process Description",
             "accessorKey": "processDesc",
             "isSortable": false,
             "sortingParams": [
                "processDesc"
             ],
             "shouldRedirect": false,
             "showIcons": true,
             "enabled": {
                "CREATED_TASKS": true,
                "MY_TASKS": false,
                "SUBSTITUTED_TO_OTHERS": false,
                "MY_COMPLETED_TASKS": false,
                "ADMIN_TASKS": false,
                "ADMIN_COMPLETED_TASKS": false,
                "MY_OUTBOX": false,
                "DRAFT": false
             }
          },
          {
             "width": "15%",
             "label": "Created On",
             "accessorKey": "createdOn",
             "isSortable": true,
             "sortingParams": [
                "createdOn"
             ],
             "shouldRedirect": false,
             "showIcons": false,
             "enabled": {
                "CREATED_TASKS": false,
                "MY_TASKS": true,
                "SUBSTITUTED_TO_OTHERS": false,
                "MY_COMPLETED_TASKS": true,
                "ADMIN_TASKS": false,
                "ADMIN_COMPLETED_TASKS": false,
                "MY_OUTBOX": false,
                "DRAFT": true
             }
          },
          {
             "width": "20%",
             "label": "Assigned To",
             "accessorKey": "owners",
             "isSortable": true,
             "sortingParams": [
                "owners"
             ],
             "shouldRedirect": false,
             "enabled": {
                "CREATED_TASKS": false,
                "MY_TASKS": false,
                "SUBSTITUTED_TO_OTHERS": false,
                "MY_COMPLETED_TASKS": false,
                "ADMIN_TASKS": true,
                "ADMIN_COMPLETED_TASKS": true,
                "MY_OUTBOX": false,
                "DRAFT": false
             }
          },
          {
             "width": "16%",
             "label": "Due Date",
             "accessorKey": "dueDate",
             "isSortable": true,
             "sortingParams": [
                "compDeadline"
             ],
             "shouldRedirect": false,
             "enabled": {
                "CREATED_TASKS": true,
                "MY_TASKS": true,
                "SUBSTITUTED_TO_OTHERS": true,
                "MY_COMPLETED_TASKS": true,
                "ADMIN_TASKS": true,
                "ADMIN_COMPLETED_TASKS": true,
                "MY_OUTBOX": true,
                "DRAFT": false
             }
          },
          {
             "width": "10%",
             "label": "Status",
             "accessorKey": "status",
             "isSortable": true,
             "sortingParams": [
                "itmStatus",
                "technicalStatus"
             ],
             "shouldRedirect": false,
             "enabled": {
                "CREATED_TASKS": true,
                "MY_TASKS": true,
                "SUBSTITUTED_TO_OTHERS": true,
                "MY_COMPLETED_TASKS": true,
                "ADMIN_TASKS": true,
                "ADMIN_COMPLETED_TASKS": true,
                "MY_OUTBOX": false,
                "DRAFT": false
             },
             "systemHiddenColumn": [
                {
                   "ECC": "MY_TASKS"
                },
                {
                   "Flowable": "MY_OUTBOX"
                }
             ]
          },
          {
             "width": "6%",
             "label": "Actions",
             "accessorKey": "actions",
             "isSortable": false,
             "sortingParams": [],
             "shouldRedirect": false,
             "enabled": {
                "CREATED_TASKS": true,
                "MY_TASKS": true,
                "SUBSTITUTED_TO_OTHERS": true,
                "MY_COMPLETED_TASKS": false,
                "ADMIN_TASKS": true,
                "ADMIN_COMPLETED_TASKS": false,
                "MY_OUTBOX": false,
                "DRAFT": false
             }
          }
       ],
       "exportColumns": {
          "ADMIN_TASKS": [
             {
                "header": "SYSTEM NAME",
                "key": "systemName"
             },
             {
                "header": "PROCESS DISPLAY NAME",
                "key": "processDisplayName"
             },
             {
                "header": "ID",
                "key": "referenceId"
             },
             {
                "header": "TASK DESC",
                "key": "taskDesc"
             },
             {
                "header": "STATUS",
                "key": "itmStatus"
             },
             {
                "header": "CREATED BY",
                "key": "createdByName"
             },
             {
                "header": "ASSIGNED TO",
                "key": "assignedTo"
             },
             {
                "header": "SLA STATUS",
                "key": "taskSla"
             },
             {
                "header": "ATTACHMENT COUNT",
                "key": "attachmentCount"
             }
          ],
          "ADMIN_COMPLETED_TASKS": [
             {
                "header": "SYSTEM NAME",
                "key": "systemName"
             },
             {
                "header": "PROCESS DISPLAY NAME",
                "key": "processDisplayName"
             },
             {
                "header": "ID",
                "key": "referenceId"
             },
             {
                "header": "TASK DESC",
                "key": "taskDesc"
             },
             {
                "header": "STATUS",
                "key": "itmStatus"
             },
             {
                "header": "CREATED BY",
                "key": "createdByName"
             },
             {
                "header": "ASSIGNED TO",
                "key": "assignedTo"
             },
             {
                "header": "SLA STATUS",
                "key": "taskSla"
             },
             {
                "header": "ATTACHMENT COUNT",
                "key": "attachmentCount"
             }
          ],
          "DEFAULT": [
             {
                "header": "SYSTEM NAME",
                "key": "systemName"
             },
             {
                "header": "PROCESS DISPLAY NAME",
                "key": "processDisplayName"
             },
             {
                "header": "ID",
                "key": "referenceId"
             },
             {
                "header": "TASK DESC",
                "key": "taskDesc"
             },
             {
                "header": "STATUS",
                "key": "itmStatus"
             },
             {
                "header": "CREATED BY",
                "key": "createdByName"
             },
             {
                "header": "SLA STATUS",
                "key": "taskSla"
             },
             {
                "header": "ATTACHMENT COUNT",
                "key": "attachmentCount"
             }
          ]
       }
    },
    "taskPermissions": {
       "attachments": {
          "ADMIN_TASKS": {
             "upload": false,
             "enabled": true
          },
          "ADMIN_COMPLETED_TASKS": {
             "upload": false,
             "enabled": true
          },
          "CREATED_TASKS": {
             "upload": true,
             "enabled": true
          },
          "MY_TASKS": {
             "upload": true,
             "enabled": true
          },
          "MY_OUTBOX": {
             "upload": true,
             "enabled": true
          },
          "DRAFT": {
             "upload": true,
             "enabled": true
          },
          "SUBSTITUTED_TO_OTHERS": {
             "upload": false,
             "enabled": true
          },
          "MY_FILTERS": {
             "upload": true,
             "enabled": true
          },
          "MY_COMPLETED_TASKS": {
             "upload": false,
             "enabled": true
          },
          "DEFAULT": {
             "upload": true,
             "enabled": true
          }
       },
       "collaboration": {
          "ADMIN_TASKS": {
             "postComment": false,
             "enabled": true
          },
          "ADMIN_COMPLETED_TASKS": {
             "postComment": false,
             "enabled": true
          },
          "CREATED_TASKS": {
             "postComment": true,
             "enabled": true
          },
          "MY_TASKS": {
             "postComment": true,
             "enabled": true
          },
          "MY_OUTBOX": {
             "postComment": true,
             "enabled": true
          },
          "DRAFT": {
             "postComment": true,
             "enabled": true
          },
          "SUBSTITUTED_TO_OTHERS": {
             "postComment": true,
             "enabled": true
          },
          "MY_FILTERS": {
             "postComment": true,
             "enabled": true
          },
          "MY_COMPLETED_TASKS": {
             "postComment": true,
             "enabled": true
          }
       },
       "acitivityLogs": {
          "ADMIN_TASKS": {
             "enabled": true
          },
          "ADMIN_COMPLETED_TASKS": {
             "enabled": true
          },
          "CREATED_TASKS": {
             "enabled": true
          },
          "MY_TASKS": {
             "enabled": true
          },
          "MY_OUTBOX": {
             "enabled": true
          },
          "DRAFT": {
             "enabled": true
          },
          "SUBSTITUTED_TO_OTHERS": {
             "enabled": true
          },
          "MY_FILTERS": {
             "enabled": true
          },
          "MY_COMPLETED_TASKS": {
             "enabled": true
          }
       },
       "workflow": {
          "ADMIN_TASKS": {
             "enabled": true
          },
          "ADMIN_COMPLETED_TASKS": {
             "enabled": true
          },
          "CREATED_TASKS": {
             "enabled": true
          },
          "MY_TASKS": {
             "enabled": true
          },
          "MY_OUTBOX": {
             "enabled": true
          },
          "DRAFT": {
             "enabled": true
          },
          "SUBSTITUTED_TO_OTHERS": {
             "enabled": true
          },
          "MY_FILTERS": {
             "enabled": true
          },
          "MY_COMPLETED_TASKS": {
             "enabled": true
          }
       },
       "taskForms": {
          "ADMIN_TASKS": {
             "enabled": true
          },
          "ADMIN_COMPLETED_TASKS": {
             "enabled": true
          },
          "CREATED_TASKS": {
             "enabled": true
          },
          "MY_TASKS": {
             "enabled": true
          },
          "MY_OUTBOX": {
             "enabled": true
          },
          "DRAFT": {
             "enabled": true
          },
          "SUBSTITUTED_TO_OTHERS": {
             "enabled": true
          },
          "MY_FILTERS": {
             "enabled": true
          },
          "MY_COMPLETED_TASKS": {
             "enabled": true
          }
       },
       "primaryActions": {
          "ADMIN_TASKS": {
             "enabled": false
          },
          "ADMIN_COMPLETED_TASKS": {
             "enabled": false
          },
          "CREATED_TASKS": {
             "enabled": true
          },
          "MY_TASKS": {
             "enabled": true
          },
          "MY_OUTBOX": {
             "enabled": true
          },
          "DRAFT": {
             "enabled": true
          },
          "SUBSTITUTED_TO_OTHERS": {
             "enabled": true
          },
          "MY_FILTERS": {
             "enabled": true
          },
          "MY_COMPLETED_TASKS": {
             "enabled": false
          }
       },
       "secondaryActions": {
          "ADMIN_TASKS": {
             "enabled": true
          },
          "ADMIN_COMPLETED_TASKS": {
             "enabled": false
          },
          "CREATED_TASKS": {
             "enabled": true
          },
          "MY_TASKS": {
             "enabled": true
          },
          "MY_OUTBOX": {
             "enabled": true
          },
          "DRAFT": {
             "enabled": true
          },
          "SUBSTITUTED_TO_OTHERS": {
             "enabled": true
          },
          "MY_FILTERS": {
             "enabled": true
          },
          "MY_COMPLETED_TASKS": {
             "enabled": false
          }
       },
       "tertiaryActions": {
          "ADMIN_TASKS": {
             "enabled": true
          },
          "ADMIN_COMPLETED_TASKS": {
             "enabled": false
          },
          "CREATED_TASKS": {
             "enabled": true
          },
          "MY_TASKS": {
             "enabled": true
          },
          "MY_OUTBOX": {
             "enabled": true
          },
          "DRAFT": {
             "enabled": true
          },
          "SUBSTITUTED_TO_OTHERS": {
             "enabled": true
          },
          "MY_FILTERS": {
             "enabled": true
          },
          "MY_COMPLETED_TASKS": {
             "enabled": false
          }
       },
       "multiSelect": {
          "MY_TASKS": {
             "enabled": true,
             "permittedActions": {
                "APPROVE": true,
                "REJECT": true,
                "FORWARD": true,
                "CLAIM": true,
                "RELEASE": true
             }
          },
          "MY_OUTBOX": {
             "enabled": false,
             "permittedActions": {
                "APPROVE": true,
                "REJECT": true,
                "FORWARD": true,
                "CLAIM": true,
                "RELEASE": true
             }
          },
          "DRAFT": {
             "enabled": false,
             "permittedActions": {
                "APPROVE": true,
                "REJECT": true,
                "FORWARD": true,
                "CLAIM": true,
                "RELEASE": true
             }
          },
          "ADMIN_TASKS": {
             "enabled": true,
             "permittedActions": {
                "APPROVE": false,
                "REJECT": false,
                "FORWARD": true,
                "CLAIM": false,
                "RELEASE": false
             }
          },
          "MY_FILTERS": {
             "enabled": true,
             "permittedActions": {
                "APPROVE": true,
                "REJECT": true,
                "FORWARD": true,
                "CLAIM": true,
                "RELEASE": true
             }
          },
          "CREATED_TASKS": {
             "enabled": true,
             "permittedActions": {
                "APPROVE": false,
                "REJECT": false,
                "FORWARD": false,
                "CLAIM": false,
                "RELEASE": false
             }
          },
          "SUBSTITUTED_TO_OTHERS": {
             "enabled": true,
             "permittedActions": {
                "APPROVE": true,
                "REJECT": true,
                "FORWARD": true,
                "CLAIM": true,
                "RELEASE": true
             }
          }
       },
       "pin": {
          "ADMIN_TASKS": {
             "enabled": false
          },
          "ADMIN_COMPLETED_TASKS": {
             "enabled": false
          },
          "CREATED_TASKS": {
             "enabled": true
          },
          "MY_TASKS": {
             "enabled": true
          },
          "MY_OUTBOX": {
             "enabled": true
          },
          "DRAFT": {
             "enabled": true
          },
          "SUBSTITUTED_TO_OTHERS": {
             "enabled": false
          },
          "MY_FILTERS": {
             "enabled": true
          },
          "MY_COMPLETED_TASKS": {
             "enabled": false
          }
       },
       "SLABand": {
          "ADMIN_TASKS": {
             "enabled": true
          },
          "ADMIN_COMPLETED_TASKS": {
             "enabled": false
          },
          "CREATED_TASKS": {
             "enabled": true
          },
          "MY_TASKS": {
             "enabled": true
          },
          "MY_OUTBOX": {
             "enabled": true
          },
          "DRAFT": {
             "enabled": true
          },
          "SUBSTITUTED_TO_OTHERS": {
             "enabled": true
          },
          "MY_FILTERS": {
             "enabled": true
          },
          "MY_COMPLETED_TASKS": {
             "enabled": false
          }
       }
    },
    "btpServerSideEventUrlMap": {
       "notificationUrl": "https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com",
       "cachingUrl": "https://cherryworkproducts-itm-java-dev.cfapps.eu10.hana.ondemand.com",
       "collaborationUrl": "https://cherryworkproducts-itm-java-dev.cfapps.eu10.hana.ondemand.com"
    }
 }
  
  export default configData;