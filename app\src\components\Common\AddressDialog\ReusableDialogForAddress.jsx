import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  Slide,
  Typography,
  CircularProgress,
  Box,
  Stack,
} from '@mui/material';

// Transition component for smooth modal animation
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const AddressModal = ({
  isPOBox,
  open,
  onClose,
  addresses,
  onSelect,
  loading,
  correctedAddr = {},
  handleCorrectedAddress = () => {},
}) => {
  // Check if the corrected address is valid
  const isCorrectedAddressAvailable = (correctedAddr?.addr_info_code === '' || correctedAddr?.addr_info_code === '5020');

  return (
    <Dialog
      open={open}
      onClose={onClose}
      TransitionComponent={Transition}
      keepMounted
      PaperProps={{
        style: {
          borderRadius: 12,
          minWidth: 900,
          padding: '16px',
          boxShadow: '0 6px 20px rgba(0,0,0,0.15)',
        },
      }}
      aria-labelledby="address-dialog-title"
    >
      <DialogTitle id="address-dialog-title">
        <Typography variant="h6" style={{ fontWeight: 'bold' }}>
          You may choose from the following suggested addresses.
        </Typography>
      </DialogTitle>
      <DialogContent dividers style={{ padding: '8px 16px' }}>
        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="100px">
            <CircularProgress />
          </Box>
        ) : addresses?.length === 0 ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="100px">
            {isCorrectedAddressAvailable ? (
              <Stack justifyContent="center" alignItems="center" spacing={2}>
                <Typography variant="body1" color="black">
                Some fields in your address appear to be incorrect. Please select the corrected address from the suggestions below if you'd like to replace it.
                </Typography>
                
                <Button
                  variant="outlined"
                  onClick={handleCorrectedAddress}
                  color="primary"
                  style={{ borderRadius: 6 }}
                >
                  {!isPOBox ? 
                  `${correctedAddr?.std_addr_prim_number_full} ${correctedAddr?.std_addr_prim_name1_4}, ${correctedAddr?.std_addr_locality_full}, ${correctedAddr?.std_addr_region_code}, ${correctedAddr?.std_addr_postcode_full}`
                  :`PO BOX ${correctedAddr?.std_addr_po_box_number}, ${correctedAddr?.std_addr_po_box_locality_full}, ${correctedAddr?.std_addr_po_box_region_code}, ${correctedAddr?.std_addr_po_box_postcode_full}`}
                </Button>
              </Stack>
            ) : (
              <Typography variant="body1" color="black">
                Please verify the address you entered, as it seems to be incorrect.
              </Typography>
            )}
          </Box>
        ) : (
          <List>
            {addresses?.map((address, index) => (
              <ListItem
                button
                key={index}
                onClick={() => onSelect(address)}
                style={{
                  borderBottom: '1px solid #e0e0e0',
                  borderRadius: 6,
                  marginBottom: 10,
                  backgroundColor: '#f7f7f7',
                  transition: 'background-color 0.3s ease',
                }}
                onMouseEnter={(e) =>
                  (e.currentTarget.style.backgroundColor = '#ececec')
                }
                onMouseLeave={(e) =>
                  (e.currentTarget.style.backgroundColor = '#f7f7f7')
                }
                aria-label={`Suggested address ${index + 1}`}
              >
                <ListItemText
                  primary={address?.sugg_addr_single_address}
                  primaryTypographyProps={{
                    style: { fontSize: '16px', fontWeight: '500' },
                  }}
                />
              </ListItem>
            ))}
          </List>
        )}
      </DialogContent>
      <DialogActions style={{ padding: '8px 16px' }}>
        <Button
          onClick={onClose}
          color="primary"
          variant="contained"
          style={{ borderRadius: 6 }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddressModal;