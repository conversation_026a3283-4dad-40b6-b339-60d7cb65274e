import React, { useState, useEffect } from "react";
import {
  Paper,
  IconButton,
  Tooltip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Switch,
  Typography,
  Checkbox,
  BottomNavigation,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import CloseIcon from "@mui/icons-material/Close";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import {
  Add,
  Refresh,
  CheckBoxOutlineBlank,
  CheckBox,
  GetApp,
  Publish,
} from "@mui/icons-material";
import { useDispatch, useSelector } from "react-redux";
import RolesDetail from "./RolesDetail";
import { getAllRoles } from "../../Action/action";
import {
  setRoles,
  setResponseMessage,
  setRoleTemplates,
} from "../../../../../app/userManagementSlice";
import Loading from "../Loading";
import { findApplicationById } from "../../Utility/basic";
import { Autocomplete } from "@mui/material";
import { downloadFile, roleFileHeading } from "../../Utility/file";
import UploadFile from "../UploadFile";
import { applicationIds } from "../../Utility/config";
import { appHeaderHeight, rolePageHeaderHeight } from "../../Data/cssConstant";
import ReusableTable from "../../../../common/ReusableTable";
import ReusablePromptBox from "../../../../common/ReusablePromptBox/ReusablePromptBox";
import {
  destination_IWA,
  destination_IWA_NPI,
  // destination_IWA_SCP,
} from "../../../../../destinationVariables";
import { iconButton_SpacingSmall } from "../../../../common/commonStyles";

const useStyle = makeStyles((theme) => ({
  newRoleDialogTitle: {
    height: "3rem",
    display: "flex",
    alignItems: "center",
    borderBottom: "1px solid #d9d9d9",
    margin: 0,
    padding: 0,
    paddingLeft: 10,
  },
  newRoleDialogContentSwitchContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-evenly",
    marginTop: 6,
  },
  newRoleDialogActions: {
    height: "3rem",
    borderTop: "1px solid #d9d9d9",
  },
  rolesHeaderContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end",
    position: "sticky",
    top: 0,
    zIndex: 99,
    height: rolePageHeaderHeight,
  },
  roleHeadeTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: theme.palette.text.primary,
  },
  rolesHeaderDropdown: {
    width: 150,
    marginRight: 6,
  },
  rolesHeaderAddButton: {
    marginLeft: 10,
    textTransform: "capitalize",
  },
  rolesTableContainer: {
    height: "100%",
    width: "100%",
  },
  rolesTableHead: {
    position: "sticky",
    top: 0,
    zIndex: 99,
    backgroundColor: "#F1F5FE",
  },
  rolesTableHeadCell: {
    whiteSpace: "nowrap",
    fontSize: 9,
    fontWeight: "bold",
    borderLeft: "1px solid #E5E5E5",
    borderRight: "1px solid #E5E5E5",
  },
  rolesTableBody: {
    height: "100%",
  },
  rolesTableBodyRow: {
    cursor: "pointer",
    "&:hover": {
      backgroundColor: theme.palette.action.hover,
    },
  },
  rolesTableBodyRowSelected: {
    backgroundColor: theme.palette.action.selected,
  },
  rolesTableBodyCell: {
    whiteSpace: "nowrap",
    fontSize: 10,
    backgroundColor: "white",
    borderLeft: "1px solid #E5E5E5",
    borderRight: "1px solid #E5E5E5",
  },
  rolesTableBodyTextHide: {
    overflow: "hidden",
    maxWidth: 180,
    textOverflow: "ellipsis",
  },
}));

const NewRole = ({ open, onClose, handleRefresh, handleOpenPromptBox }) => {
  const classes = useStyle();
  const basicReducerState = useSelector((state) => state.userManagement);
  const [load, setLoad] = useState(false);
  const initialRole = {
    name: "",
    label: "",
    description: "",
    applicationId: basicReducerState?.applications[0]?.id,
    roleTemplate: "",
    active: true,
    hasExpiry: true,
    noOfExpiryDays: 0,
    expiryMailTriggerDays: 0,
    isComposite: false,
    isDefault: false,
    associateRoles: "",
    status: "Active",
    isActive: 1,
    isDeleted: 0,
    userType: ""
  };
  const [newRole, setNewRole] = useState(initialRole);
  const [associatedRoles, setAssociatedRoles] = useState([]);
  const [fRoleTemplates, setFRoleTemplates] = useState(null);
  const [roleTemplates, setRoleTemplates] = useState([]);
  const [fAppIdentifier, setFRoleAppIdentifier] = useState(null);
  const [appIdentifiers, setAppIdentifiers] = useState([]);
  const dispatch = useDispatch();

  const insertNewRole = () => {
    setLoad(true);
    const insertRoleUrl = `/${destination_IWA_NPI}/api/v1/rolesMDG/createRoleMDG`;
    const insertRolePayload = {
      ...newRole,
      status: newRole?.status,
      userType: newRole?.userType,
      hasExpiry: newRole?.hasExpiry ? 1 : 0,
      isComposite: newRole?.isComposite ? 1 : 0,
      isDefault: newRole?.isDefault ? 1 : 0,
      // associateRoles: newRole?.associateRoles
      //   ?.map((role) => role?.id)
      //   ?.join(","),
      roleTemplateName: newRole?.roleTemplate?.name || "",
      roleTemplateAppId: newRole?.roleTemplate?.appId || "",
      roleTemplateAppName: newRole?.roleTemplate?.appName || "",
      label:
        newRole?.applicationId === applicationIds.SAP_BTP &&
        !newRole?.isComposite
          ? newRole?.roleTemplate?.name || ""
          : newRole?.label,
    };
    const insertRoleRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(insertRolePayload),
    };
    fetch(insertRoleUrl, insertRoleRequestParam)
      .then((response) => response.json())
      .then((role_data) => {
        setLoad(false);
        setNewRole(initialRole);
        getAllRoles(
          () => {
            setLoad(true);
          },
          (data) => {
            dispatch(setRoles(data?.data || []));
            setLoad(false);
            onClose();
            setNewRole(initialRole);
            setFRoleTemplates(null);
            setFRoleAppIdentifier(null);

            dispatch(
              setResponseMessage({
                open: true,
                status: role_data?.status ? "success" : "error",
                message: role_data?.status
                  ? "Role created successfully"
                  : "Something went wrong",
              })
            );
          },
          (err) => {
            setLoad(false);
            onClose();

            dispatch(
              setResponseMessage({
                open: true,
                status: "error",
                message: "Something went wrong",
              })
            );
          }
        );
      })
      .catch((err) => {
        setLoad(false);
      });
  };

  useEffect(() => {
    let newRoleTemplates = basicReducerState?.roleTemplates;
    if (fAppIdentifier) {
      newRoleTemplates = newRoleTemplates?.filter(
        (template) => template?.appName === fAppIdentifier
      );
    }
    setRoleTemplates(newRoleTemplates);

    let newAppIdentifiers = basicReducerState?.roleTemplates;
    if (fRoleTemplates) {
      newAppIdentifiers = newAppIdentifiers?.filter(
        (appIdentifier) => appIdentifier?.name === fRoleTemplates?.name
      );
    }
    newAppIdentifiers = newAppIdentifiers?.map((template) => template?.appName);
    newAppIdentifiers = new Set(newAppIdentifiers);
    newAppIdentifiers = [...newAppIdentifiers];
    setAppIdentifiers(newAppIdentifiers);
  }, [basicReducerState?.roleTemplates, fAppIdentifier, fRoleTemplates]);

  useEffect(() => {
    let roles = basicReducerState?.roles?.filter(
      (role) => role?.isComposite !== 1
    );
    if (newRole?.applicationId) {
      roles = roles?.filter(
        (role) => role?.applicationId === newRole?.applicationId
      );
    }
    if (fRoleTemplates) {
      roles = roles?.filter((role) => role?.label === fRoleTemplates?.name);
    }
    if (fAppIdentifier) {
      roles = roles?.filter((role) =>
        basicReducerState?.roleTemplates
          ?.filter((template) => template?.appName === fAppIdentifier)
          ?.map((template) => template?.name)
          ?.includes(role?.label)
      );
    }
    setAssociatedRoles(roles);
  }, [
    basicReducerState?.roles,
    basicReducerState?.roleTemplates,
    newRole?.applicationId,
    fRoleTemplates,
    fAppIdentifier,
  ]);
  
  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose}>
      <DialogTitle
        sx={{
          height: "3rem",
          display: "flex",
          margin: 0,
          justifyContent: "space-between",
          alignItems: "center",
          padding: ".5rem",
          paddingLeft: "1rem",
          backgroundColor: "#EAE9FF40",
        }}
      >
        <Typography variant="h6">New Role</Typography>
        <IconButton
          sx={{ width: "max-content" }}
          onClick={onClose}
          children={<CloseIcon />}
        />
      </DialogTitle>

      <DialogContent sx={{ padding: "1rem 1rem" }}>
        <Loading load={load} />

        <Grid
          container
          sx={{
            display: "flex",
            flexDirection: "column",
          }}
        >
          

          <Grid
            item
            xs
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Typography variant="body1">Name<span style={{ color: "red" }}>*</span></Typography>
            <TextField
              variant="outlined"
              fullWidth
              placeholder="Enter Role Name"
              required
              size="small"
              value={newRole?.name}
              onChange={(e) => {
                setNewRole({ ...newRole, name: e.target.value });
              }}
              error={basicReducerState?.roles?.find(
                (role) =>
                  role?.name === newRole?.name &&
                  role?.applicationId === newRole?.applicationId
              )}
              helperText={
                basicReducerState?.roles?.find(
                  (role) =>
                    role?.name === newRole?.name &&
                    role?.applicationId === newRole?.applicationId
                ) && "Role name already exists."
              }
            />
          </Grid>

          {(newRole?.applicationId !== applicationIds.SAP_BTP ||
            (newRole?.applicationId === applicationIds.SAP_BTP &&
              newRole?.isComposite)) && (
            <Grid
              item
              xs
              sx={{
                marginTop: ".5rem",
              }}
            >
              <Typography variant="body1">Label<span style={{ color: "red" }}>*</span></Typography>
              <TextField
                variant="outlined"
                fullWidth
                placeholder="Enter Label"
                required
                size="small"
                value={newRole?.label}
                onChange={(e) => {
                  setNewRole({ ...newRole, label: e.target.value });
                }}
              />
            </Grid>
          )}

          <Grid
            item
            xs
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Typography variant="body1">Description<span style={{ color: "red" }}>*</span></Typography>
            <TextField
              variant="outlined"
              fullWidth
              required
              placeholder="Enter Description"
              size="small"
              value={newRole?.description}
              onChange={(e) => {
                setNewRole({ ...newRole, description: e.target.value });
              }}
            />
          </Grid>

          <Grid
            item
            xs
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Typography variant="body1">Role Type<span style={{ color: "red" }}>*</span></Typography>
            <Autocomplete
              size="small"
              style={{ fontSize: 12 }}
              value={newRole?.userType}
              
              onChange={(e,value)=>{
                setNewRole({ ...newRole, userType: value });

              }}
              // options={["Internal", "External"]}
              options={["Internal"]}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  style={{ fontSize: 12 }}
                  placeholder="Select Role Type"
                />
              )}
            />
          </Grid>
                  
        </Grid>
      </DialogContent>

      <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
        <Button
          key={"CANCEL"}
          size="small"
          variant="outlined"
          onClick={() => {
            onClose();
            setNewRole(initialRole);
            setFRoleTemplates(null);
            setFRoleAppIdentifier(null);
          }}
        >
          Cancel
        </Button>

        <Button
          key={"ADD"}
          size="small"
          variant={
            load ||
            newRole?.name?.length === 0 ||
            newRole?.label?.length === 0 ||
            newRole?.description?.length === 0 ||
            newRole?.applicationId?.length === 0 ||
            newRole?.userType?.length === 0  ||
            (newRole?.isComposite && newRole?.associateRoles?.length === 0) ||
            basicReducerState?.roles?.find(
              (role) =>
                role?.name === newRole?.name &&
                role?.applicationId === newRole?.applicationId
            ) ||
            (newRole?.applicationId === applicationIds.SAP_BTP &&
              !newRole?.isComposite &&
              newRole?.roleTemplate?.length === 0)
              ? "outlined"
              : "contained"
          }
          className="btn-ml"
          onClick={insertNewRole}
          disabled={
            load ||
            newRole?.name?.length === 0 ||
            newRole?.label?.length === 0 ||
            newRole?.description?.length === 0 ||
            newRole?.applicationId?.length === 0 ||
            newRole?.userType?.length === 0  ||
            (newRole?.isComposite && newRole?.associateRoles?.length === 0) ||
            basicReducerState?.roles?.find(
              (role) =>
                role?.name === newRole?.name &&
                role?.applicationId === newRole?.applicationId
            ) ||
            (newRole?.applicationId === applicationIds.SAP_BTP &&
              !newRole?.isComposite &&
              newRole?.roleTemplate?.length === 0)
          }
        >
          Add
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const Roles = () => {
  const classes = useStyle();
  const basicReducerState = useSelector((state) => state.userManagement);
  const [load, setLoad] = useState(true);
  const [selectAplication, setSelectApplication] = useState(-1);
  const [roles, setroles] = useState([]);
  const [filteredRoles, setFilteredRoles] = useState([]);
  const [openNewRoleDialog, setOpenNewRoleDialog] = useState(false);
  const [file, setFile] = useState(null);
  const [openRoleFileDialog, setOpenRoleFileDialog] = useState(false);
  const [selectingFileRoleApplications, setSelectingFileRoleApplications] =
    useState([]);
  const [deletingRole, setDeletingRole] = useState(null);
  const [params, setParams] = useState({ roleId: null });
  const dispatch = useDispatch();
  const [openDeletionDialog, setOpenDeletionDialog] = useState(false);
  const [promptType, setPromptType] = useState("dialog");
  const [promptMessage, setPromptMessage] = useState("");

  const refreshData = () => {
    getRoles();
    getRoleTemplates();
  };

  //<-- Functions and variables for ReusablePromptBox *promptAction_Functions -->
  const [promptBoxState, setPromptBoxState] = useState({
    open: false,
    type: "",
    redirectOnClose: true,
    message: "",
    title: "",
    severity: "",
  });
  const [promptBoxScenario, setPromptBoxScenario] = useState("");

  const promptAction_Functions = {
    handleClosePromptBox: () => {
      setPromptBoxState((prev) => ({
        open: false,
        type: "",
        redirectOnClose: false,
        message: "",
        title: "",
        severity: "",
      }));
      getRoles();
      getRoleTemplates();
      setPromptBoxScenario("");
    },
    handleOpenPromptBox: (ref, data = {}) => {
      // SUCCESS,FAILURE
      let initialData = {
        open: true,
        title: "",
        message: "",
        okButton: true,
        cancelButton: true,
        okButtonText: "Ok",
        cancelText: "Cancel",
        type: "dialog",
      };
      if (ref === "SUCCESS") {
        initialData.type = "snackbar";
      }
      setPromptBoxScenario(ref);
      setPromptBoxState(Object.assign(initialData, data));
    },
    handleCloseAndRedirect: () => {
      promptAction_Functions.handleClosePromptBox();
      // navigate("/purchaseOrder/management");
    },
    getCancelFunction: () => {
      switch (promptBoxScenario) {
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getCloseFunction: () => {
      switch (promptBoxScenario) {
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getOkFunction: () => {
      switch (promptBoxScenario) {
        case "DELETE":
          return deleteRole;
        default:
          return promptAction_Functions.handleClosePromptBox;
      }
    },
    getCloseAndRedirectFunction: () => {
      if (!promptBoxState.redirectOnClose) {
        return promptAction_Functions.handleClosePromptBox;
      }
      return promptAction_Functions.handleCloseAndRedirect;
    },
  };

  const getApplicationNameById = (applicationId) => {
    const application = findApplicationById(
      Number(applicationId),
      basicReducerState.applications
    );
    return application?.name || "-";
  };
  const deleteRole = () => {
    // setLoad(true);
    const disableRoleUrl = `/${destination_IWA_NPI}/api/v1/rolesMDG/deactivateRoleMDG?id=${deletingRole}`;
    // const deletingRolePayload = {
    //   id: deletingRole,
    // };
    const disableRoleRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      // body: JSON.stringify(deletingRolePayload),
    };
    fetch(disableRoleUrl, disableRoleRequestParam)
      .then((res) => res.json())
      .then((data) => {
        // setLoad(false);
        if (data.statusCode === 200) {
          promptAction_Functions.handleOpenPromptBox("SUCCESS", {
            message: `Role deleted successfully`,
            redirectOnClose: false,
          });
        } else {
          promptAction_Functions.handleOpenPromptBox("ERROR", {
            title: "Failed",
            message: `Role deletion failed`,
            severity: "danger",
            cancelButton: false,
          });
        }

        dispatch(
          setRoles(
            basicReducerState?.roles?.filter(
              (role) => role?.id !== Number(deletingRole)
            ) || []
          )
        );
        setroles(
          roles?.filter((role) => role?.id !== Number(deletingRole)) || []
        );
        setFilteredRoles(
          filteredRoles?.filter((role) => role?.id !== Number(deletingRole)) ||
            []
        );
        setDeletingRole(null);

        // dispatch(
        //   setResponseMessage({
        //     open: true,
        //     status: data?.status ? "success" : "error",
        //     message: data?.status
        //       ? "Role deleted successfully"
        //       : "Something went wrong",
        //   })
        // );
      })
      .catch((err) => {
        setLoad(false);
      });
  };
  const editRole = (roleId) => {
    setParams({ roleId: roleId });
  };
  const getRoles = () => {
    const getAllRolesUrl = `/${destination_IWA_NPI}/api/v1/rolesMDG/getRolesMDG`;
    const getAllRolesRequestParams = {
      headers: {
        "Content-Type": "application/json",
      },
    };
    fetch(getAllRolesUrl, getAllRolesRequestParams)
      .then((res) => res.json())
      .then((data) => {
        dispatch(setRoles(data?.data || []));
        setLoad(false);
      });
    // (data) => {
    //   dispatch(setRoles(data?.data || []));
    //   setLoad(false);
    // },
    // (err) => {
    //   setLoad(false);
    // }
  };
  const getRoleTemplates = () => {
    const getGroupsUrl = `/${destination_IWA}/api/v1/roles/roleTemplates`;
    const getGroupsRequestParam = {
      headers: {
        "Content-Type": "application/json",
      },
    };
    fetch(getGroupsUrl, getGroupsRequestParam)
      .then((res) => res.json())
      .then((data) => {
        dispatch(setRoleTemplates(data?.data || []));
        setLoad(false);
      });
  };
  const updateDefaultRole = (roleDet) => {
    setLoad(true);
    const oldRoleId = basicReducerState?.roles?.find(
      (role) =>
        Number(role?.applicationId) === Number(roleDet?.applicationId) &&
        Number(role?.isDefault) === 1
    );
    const updateDefRoleUrl = `/${destination_IWA}/api/v1/roles/enableDefaultRole/modify?oldId=${
      oldRoleId?.id || 0
    }&newId=${roleDet?.id}&applicationId=${roleDet?.applicationId}`;
    const updateDefRoleRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({}),
    };
    fetch(updateDefRoleUrl, updateDefRoleRequestParam)
      .then((res) => res.json())
      .then((data) => {
        setLoad(false);
        setroles(
          basicReducerState?.roles
            ?.filter((role) => role?.isActive === 1)
            ?.map((role) =>
              Number(role?.id) === Number(roleDet?.id)
                ? { ...role, isDefault: 1 }
                : Number(role?.id) === Number(oldRoleId?.id)
                ? { ...role, isDefault: 0 }
                : { ...role }
            )
        );
        if (selectAplication === -1) {
          setFilteredRoles(
            basicReducerState?.roles?.filter((role) => role?.isActive === 1)
          );
        } else {
          setFilteredRoles(
            roles
              ?.filter(
                (role) =>
                  role?.applicationId === selectAplication &&
                  role?.isActive === 1
              )
              ?.map((role) =>
                Number(role?.id) === Number(roleDet?.id)
                  ? { ...role, isDefault: 1 }
                  : Number(role?.id) === Number(oldRoleId?.id)
                  ? { ...role, isDefault: 0 }
                  : { ...role }
              ) || []
          );
        }
        dispatch(
          setRoles(
            basicReducerState?.roles
              ?.filter((role) => role?.isActive === 1)
              ?.map((role) =>
                Number(role?.id) === Number(roleDet?.id)
                  ? { ...role, isDefault: 1 }
                  : Number(role?.id) === Number(oldRoleId?.id)
                  ? { ...role, isDefault: 0 }
                  : { ...role }
              )
          )
        );

        dispatch(
          setResponseMessage({
            open: true,
            status: data?.status ? "success" : "error",
            message: data?.status
              ? "Default role updated successfully"
              : "Something went wrong",
          })
        );
      })
      .catch((err) => {
        setLoad(true);
      });
  };
  const uploadRolesFile = () => {
    if (!file) {
      console.log("no file found");
      return;
    }
    setLoad(true);
    const applicationName = selectingFileRoleApplications?.id;
    const url = `/${destination_IWA}/api/v1/roles/addRolesUsingCsv?applicationId=${applicationName}`;
    let formData = new FormData();
    formData.append("file", file);
    formData.append("name", file.name);
    const requestParam = {
      method: "POST",
      headers: {},
      body: formData,
    };
    fetch(url, requestParam)
      .then((res) => {
        console.log(res);
      })
      .then((data) => {
        setLoad(false);
        setOpenRoleFileDialog(false);
        setFile(null);
        setSelectingFileRoleApplications([]);
        getRoles();

        dispatch(
          setResponseMessage({
            open: true,
            status: "success",
            message: "Role file uploaded successfully",
          })
        );
      })
      .catch((err) => {
        setLoad(false);
      });
  };
  const columns = [
    {
      field: "id",
      headerName: "Role ID",
      hide: true,
    },
    {
      field: "name",
      headerName: "Role Name",
      width: 250,
    },
    {
      field: "userType",
      headerName: "Role Type",
      flex: 1,
    },
    {
      field: "getUsersCountPerRole",
      headerName: "User Count",
      flex: 1,
      type: "number",
    },
    // {
    //   field: "isDefault",
    //   headerName: "Is Default",
    //   flex: 1,
    //   headerAlign: "center",
    //   align: "center",
    //   renderCell: (data) => {
    //     return (
    //       <>
    //         {!(
    //           data.row.applicationId === applicationIds?.SAP_BTP &&
    //           data.row.isComposite === 0
    //         ) && (
    //           <Switch
    //             color="primary"
    //             size="small"
    //             checked={data.row.isDefault === 1 ? true : false}
    //             onClick={(e) => {
    //               e.stopPropagation();
    //               if (data.row.isDefault !== 1) {
    //                 updateDefaultRole(data.row);
    //               }
    //             }}
    //             disabled={load}
    //           />
    //         )}
    //       </>
    //     );
    //   },
    // },
    // {
    //   field: "status",
    //   headerName: "Status",
    //   flex: 1,
    // },
    {
      field: "actionItem",
      headerName: "Action",
      flex: 1,
      headerAlign: "center",
      align: "center",
      renderCell: (data) => {
        return (
          <Tooltip title="Delete">
            <IconButton
              aria-label="Delete"
              onClick={(e) => {
                e.stopPropagation();
                promptAction_Functions.handleOpenPromptBox("DELETE", {
                  title: "Confirm Delete",
                  message: `Do you want to delete the role?`,
                  severity: "warning",
                  cancelButton: true,
                  okButton: true,
                  okButtonText: "Delete",
                });
                setDeletingRole(data.row.id);
                // deleteRole(role.id);
                // setDeletingRole(data.row.id);
                // setOpenDeletionDialog(true);
                // setPromptType("dialog");
                // setPromptMessage("Do you want to delete the role?");
              }}
              disabled={load || data.row.isDefault === 1}
            >
              <DeleteOutlinedIcon
                color={data.row.isDefault === 1 ? "gray" : "danger"}
              />
            </IconButton>
          </Tooltip>
        );
      },
    },
  ];

  useEffect(() => {
    setroles(basicReducerState.roles);
    if (selectAplication === -1) {
      setFilteredRoles(basicReducerState.roles);
    } else {
      setFilteredRoles(
        basicReducerState?.roles?.filter(
          (role) => role?.applicationId === selectAplication
        ) || []
      );
    }
    setLoad(false);
  }, [basicReducerState.roles, selectAplication]);

  useEffect(() => {
    getRoles();
    getRoleTemplates();
  }, []);

  return (
    <div>
      <Loading load={load} />
      <>
        <NewRole
          open={openNewRoleDialog}
          handleOpenPromptBox={promptAction_Functions.handleOpenPromptBox}
          onClose={() => {
            setOpenNewRoleDialog(false);
            setSelectApplication(-1);
          }}
          handleRefresh={refreshData}
        />

        <UploadFile
          open={openRoleFileDialog}
          onClose={() => {
            setOpenRoleFileDialog(false);
            setFile(null);
          }}
          onUpload={() => {
            uploadRolesFile();
          }}
          file={file}
          setFile={setFile}
          disableCondition={
            !file || selectingFileRoleApplications?.length === 0
          }
          load={load}
        >
          <Autocomplete
            multiple={false}
            size="small"
            style={{ fontSize: 12 }}
            disableCloseOnSelect
            filterSelectedOptions
            value={selectingFileRoleApplications}
            onChange={(e, applications) => {
              setSelectingFileRoleApplications(applications);
            }}
            options={basicReducerState?.applications}
            getOptionLabel={(option) => option?.name}
            renderOption={(props, option, { selected }) => (
              <li {...props}>
                <Checkbox
                  icon={<CheckBoxOutlineBlank fontSize="small" />}
                  checkedIcon={<CheckBox color="primary" fontSize="small" />}
                  checked={selected}
                />

                <Typography style={{ fontSize: 12 }}>{option?.name}</Typography>
              </li>
            )}
            renderInput={(params) => (
              <TextField
                {...params}
                variant="standard"
                label="Applications"
                style={{ fontSize: 12 }}
              />
            )}
          />

          <Typography
            color="primary"
            style={{
              fontSize: 12,
              marginTop: 6,
              textAlign: "center",
            }}
          >
            !!! If no default role is set for application, then first role in
            sheet will be considered as default !!!
          </Typography>
        </UploadFile>

        <ReusablePromptBox
          type={promptBoxState.type}
          promptState={promptBoxState.open}
          setPromptState={promptAction_Functions.handleClosePromptBox}
          onCloseAction={promptAction_Functions.getCloseFunction()}
          promptMessage={promptBoxState.message}
          dialogSeverity={promptBoxState.severity}
          dialogTitleText={promptBoxState.title}
          handleCancelButtonAction={promptAction_Functions.getCancelFunction()}
          cancelButtonText={promptBoxState.cancelText} //Cancel button display text
          showCancelButton={promptBoxState.cancelButton} //Enable Cancel button
          handleSnackBarPromptClose={promptAction_Functions.getCloseAndRedirectFunction()}
          handleOkButtonAction={promptAction_Functions.getOkFunction()}
          okButtonText={promptBoxState.okButtonText}
          showOkButton={promptBoxState.okButton}
        />

        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0, zIndex: 1 }}
          elevation={2}
        >
          <BottomNavigation
            showLabels
            className="container_BottomNav"
            sx={{
              display: "flex",
              justifyContent: "flex-end",
            }}
          >
            <Button
              size="small"
              variant="contained"
              sx={{ marginLeft: "auto" }}
              onClick={() => setOpenNewRoleDialog(true)}
              startIcon={<Add />}
              disabled={load}
            >
              Create Role
            </Button>
          </BottomNavigation>
        </Paper>
        <div style={{ height: "38px", marginTop: ".5rem" }}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "end",
            }}
          >
            <Tooltip title="Download template">
              <IconButton
                sx={{ ...iconButton_SpacingSmall }}
                disabled={load}
                onClick={(e) => {
                  downloadFile({
                    data: roleFileHeading,
                    fileName: "cw_roles.csv",
                    fileType: "text/csv",
                  });
                }}
              >
                <GetApp />
              </IconButton>
            </Tooltip>

            <Tooltip title="Upload file">
              <IconButton
                sx={{ ...iconButton_SpacingSmall }}
                disabled={load}
                onClick={() => {
                  setOpenRoleFileDialog(true);
                }}
              >
                <Publish />
              </IconButton>
            </Tooltip>

            <Tooltip title="Refresh">
              <IconButton
                sx={{ ...iconButton_SpacingSmall }}
                disabled={load}
                onClick={() => {
                  getRoles();
                  getRoleTemplates();
                }}
              >
                <Refresh />
              </IconButton>
            </Tooltip>
          </div>
        </div>

        <Grid
          container
          spacing={2}
          style={{
            height: `calc(100vh - ${appHeaderHeight} - ${rolePageHeaderHeight})`,
          }}
        >
          <Grid item xs={params?.roleId ? 6 : 12} style={{ height: "100%" }}>
            <ReusableTable
              width="100%"
              stopPropagation_Column={["actionItem", "isDefault"]}
              status_onRowSingleClick={true}
              title={`List of Roles (${filteredRoles?.length ?? 0})`}
              rows={
                filteredRoles
                //     [
                //     {
                //         "id": 1,
                //         "name": "Buyer Admin",
                //         "label": "Buyer Admin",
                //         "description": "Buyer Admin",
                //         "applicationId": 4,
                //         "status": "Active",
                //         "isDeleted": 0,
                //         "isActive": 1,
                //         "createdBy": "<EMAIL>",
                //         "createdOn": "2023-02-08 12:04:21.000000000",
                //         "updatedBy": "<EMAIL>",
                //         "updatedOn": "2023-02-08 12:04:21.000000000",
                //         "isComposite": 0,
                //         "associateRoles": "",
                //         "isDefault": 1,
                //         "roleTemplateName": null,
                //         "roleTemplateAppId": null,
                //         "roleTemplateAppName": null
                //     }
                // ]
              }
              columns={columns}
              getRowIdValue={"id"}
              hideFooter={false}
              checkboxSelection={false}
              noOfColumns={5}
              rowsPerPageOptions={[5, 10, 15]}
              disableSelectionOnClick={true}
              callback_onRowSingleClick={(params) => {
                editRole(params.row.id || null);
              }}
            />
          </Grid>

          {params?.roleId && (
            <Grid
              item
              xs={params?.roleId ? 6 : false}
              sx={{ marginTop: "30px", paddingBottom: "4rem" }}
            >
              <RolesDetail params={params} setParams={setParams} />
            </Grid>
          )}
        </Grid>
      </>
    </div>
  );
};

export default Roles;
