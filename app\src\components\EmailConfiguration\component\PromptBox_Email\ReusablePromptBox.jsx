import featureConfig from "../../../../data/featureConfig.json";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  Snackbar,
  TextField,
  Typography,
} from "@mui/material";
import { Stack } from "@mui/system";
import React from "react";

import { useSelector } from "react-redux";
import ReusableIcon from "../../../common/ReusableIcon";

export function ReusableDialog({
  promptState, //Dialog open state
  setPromptState, //SetState for dialog state
  handlePromptClose, //Function Call to close the dialog
  onCloseAction, //Function call on close of the dialog
  promptFullWidth,
  /* Dialog Content Props */
  promptMessage, //Text to display in the dialog content
  promptMaxWidth, //width of dialog
  showInputText, //Enable Input Text Field
  inputText, //State to store input text
  setInputText, //SetState function for input text
  dialogInputPlaceholder, // Placeholder text for input text

  DialogMessageContent, // Pre formatted content to be displayed

  /* Dialog Header Content Props */
  dialogSeverity, //Dialog severity
  dialogTitleText, // Dialog Header display text

  /* Dialog Control Buttons */
  handleCancelButtonAction, //Function call on click of Cancel Button
  cancelButtonText, //Cancel button display text
  showCancelButton, //Enable Cancel button

  handleOkButtonAction, //Function Call on click of Ok Button
  okButtonText, //Ok button display text
  showOkButton, //Enable Ok button

  handleExtraButtonAction, //Function call on click of Extra Button
  extraButtonText, //Extra button display text
  showExtraButton, //Enable extra button
}) {
  const handleDefaultDialogClose = () => {
    setPromptState(false);
  };
  return (
    <>
      <Dialog
        hideBackdrop={false}
        elevation={2}
        PaperProps={{
          sx: { boxShadow: "none", minWidth: 450 },
        }}
        open={promptState}
        onClose={() => {
          if (onCloseAction) {
            onCloseAction();
          } else if (handlePromptClose) {
            handlePromptClose();
          } else {
            handleDefaultDialogClose();
          }
        }}
        fullWidth={promptFullWidth}
      >
        <Grid
          container
          sx={{ display: "flex", justifyContent: "space-between" }}
        >
          {dialogTitleText && (
            <Grid item>
              <DialogTitle
                id="alert-dialog-title"
                sx={{
                  fontWeight: 600,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  textAlign: "center",
                  fontSize: "16px",
                }}
              >
                {dialogSeverity && (
                  <span style={{ display: "flex", alignItems: "center" }}>
                    <ReusableIcon
                      iconName={
                        featureConfig["severityIcons"]?.[
                          dialogSeverity.toUpperCase()
                        ]?.iconName ?? ""
                      }
                      iconColor={
                        featureConfig["severityIcons"]?.[
                          dialogSeverity.toUpperCase()
                        ]?.iconColor ?? ""
                      }
                    />
                    &nbsp;&nbsp;
                  </span>
                )}
                {dialogTitleText}
              </DialogTitle>
            </Grid>
          )}
          <Grid item sx={{ padding: "12px" }}>
            <IconButton
              onClick={(e) => {
                e.stopPropagation();
                if (handlePromptClose) handlePromptClose();
                else handleDefaultDialogClose();
              }}
            >
              <ReusableIcon iconName="Close" />
            </IconButton>
          </Grid>
        </Grid>

        <DialogContent sx={{ paddingTop: 0 }}>
          <Stack>
            <Grid container>
              <Grid
                item
                md={12}
                sx={{
                  padding: "0px 20px 20px 0px",
                  textAlign: "left",
                }}
              >
                {DialogMessageContent && <DialogMessageContent />}
                {promptMessage && <Typography>{promptMessage}</Typography>}
              </Grid>
            </Grid>
            {showInputText && (
              <FormControl sx={{ height: "auto" }} fullWidth>
                <TextField
                  sx={{ backgroundColor: "#F5F5F5" }}
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  multiline
                  placeholder={dialogInputPlaceholder}
                ></TextField>
              </FormControl>
            )}
          </Stack>
        </DialogContent>

        {(showCancelButton || showOkButton || showExtraButton) && (
          <DialogActions
            sx={{
              paddingRight: "1.5rem",
            }}
          >
            {showCancelButton && (
              <Button
                variant="outlined"
                sx={{
                  height: 40,
                  minWidth: "4rem",
                  textTransform: "none",
                  borderColor: "#3B30C8",
                  color: "#3B30C8",
                }}
                onClick={() => {
                  if (handleCancelButtonAction) handleCancelButtonAction();
                  else if (handlePromptClose) handlePromptClose();
                  else handleDefaultDialogClose();
                }}
              >
                {cancelButtonText ?? "Cancel"}
              </Button>
            )}
            {showOkButton && (
              <Button
                variant="contained"
                style={{
                  height: 40,
                  minWidth: "4rem",
                  backgroundColor: "#3B30C8",
                  textTransform: "none",
                }}
                onClick={() => {
                  if (handleOkButtonAction) handleOkButtonAction();
                  else if (handlePromptClose) handlePromptClose();
                  else handleDefaultDialogClose();
                }}
              >
                {okButtonText ?? "Ok"}
              </Button>
            )}
            {showExtraButton && (
              <Button
                variant="contained"
                style={{
                  height: 40,
                  minWidth: "4rem",
                  backgroundColor: "#3B30C8",
                  textTransform: "none",
                }}
                onClick={() => {
                  if (handleExtraButtonAction) handleExtraButtonAction();
                  if (handlePromptClose) handlePromptClose();
                  else handleDefaultDialogClose();
                }}
              >
                {extraButtonText ?? "Ok"}
              </Button>
            )}
          </DialogActions>
        )}
      </Dialog>
    </>
  );
}

export function ReusableSnackBar({
  promptState,
  setPromptState,
  handleSnackBarPromptClose,
  promptMessage,
}) {
  const handleDefaultSnackBarClose = () => {
    setPromptState(false);
  };
  return (
    <Stack spacing={2} sx={{ width: "100%" }}>
      <Snackbar
        autoHideDuration={5000}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
        open={promptState}
        onClose={() => {
          if (handleSnackBarPromptClose) handleSnackBarPromptClose();
          else handleDefaultSnackBarClose();
        }}
        message={promptMessage}
        sx={{ height: "150px" }}
      ></Snackbar>
    </Stack>
  );
}

export default function ReusablePromptBox(props) {
  switch (props?.type) {
    case "dialog":
      return <ReusableDialog {...props} />;
      break;
    case "snackbar":
      return <ReusableSnackBar {...props} />;
      break;
  }
}
