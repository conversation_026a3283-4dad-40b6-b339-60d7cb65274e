import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  validatedRowsStatus: {},
  dropdownDataForExtendedCode:{},
  selecteddropdownDataForExtendedCode:{},
  selectedOptionsForTemplate:[],
  payload: {
    requestHeaderData: {},
    rowsHeaderData: [
    ],
    rowsBodyData: {},
  },
  isOpenDialog: true,
  selectedRowId: null,
  generalLedgerTypeDescription: {},
  generalLedgerControlData: {},
  generalLedgerCreateBankInterest: {},
  generalLedgerKeywordTranslation: {},
  generalLedgerInformation: {},
  singleGLPayload: {},
  singleGLPayloadGI:{},
  generalLedgerViewData:[],
  requiredFields: [],
  errorFields: [],
  MultipleGLRequestBench:[],
  handleMassMode: "",
  MultipleGLData:[],
  editMultipleGlData:[],
  editMultipleGlExtend:[],
  ArtifactId:[],
  glGIPayload:[],
  requiredFieldsGI: [],
  massfercInfo:[],
  MultipleGlDataForExtend:[],
  companyCodeListExtend:[],
  activeStepInPayload:0,
  shortTextFor5161Series:[],
  longTextFor5161Series:[],
  generalLedgerData: {},
  generalLedgerTabs: [],
  generalLedgerConfig: {},
  fetchReqBenchData: [],
  originalReqBenchData: [],
  changedFieldsMap: {},
  fetchedGeneralLedgerData: [],
  originalGeneralLedgerData: [],
  fetcheGeneralLedgerData: [],


};

export const generalLedgerTabSlice = createSlice({
  name: "generalLedger",
  initialState,
  reducers: {
     setGLPayload: (state, action) => {
            state.payload = action.payload;
    },
    setGLRows: (state, action) => {
      state.payload.rowsHeaderData = action.payload;
    },
    resetPayloadDataGL: (state) => {
      state.payload = {
        requestHeaderData: {},
        rowsHeaderData: [
        ],
        rowsBodyData: {},
      },
      state.generalLedgerTabs = []
    },
    setRequestHeaderPayloadSingleField: (state, action) => {
      state.payload.requestHeaderData[action.payload.keyName] = action.payload.data;
      return state;
    },
    setRequestHeaderPayloadData: (state, action) => {
      state.payload.requestHeaderData = action.payload;
      return state;
    },
     updateGeneralLedgerRowGL: (state, action) => {
      const updatedRow = action.payload;
      state.fetchedGeneralLedgerData = state.fetchedGeneralLedgerData.map((row) =>
        row.id === updatedRow.id ? updatedRow : row
      );

      const originalRow = state.originalGeneralLedgerData.find(
        (row) => row.id === updatedRow.id
      );

      const changedFields = {};
      if (originalRow) {
        Object.keys(updatedRow).forEach((key) => {
          if (updatedRow[key] !== originalRow[key]) {
            changedFields[key] = true;
          }
        });
      }

      state.changedFieldsMap[updatedRow.id] = changedFields;
    },
     updateModuleFieldDataGL: (state, action) => {
      const { uniqueId, viewID, keyName, data } = action.payload;
            if (uniqueId) {
        if (!state.payload.rowsBodyData[uniqueId]) {
          state.payload.rowsBodyData[uniqueId] = {};
        }
        if (keyName) {
          state.payload.rowsBodyData[uniqueId][keyName] =
            data?.code ? data.code : data ? data : "";
        }

      } else {
        state.payload.requestHeaderData[action.payload.keyName] =
          action?.payload?.data?.code
            ? action?.payload?.data?.code
            : action?.payload?.data
              ? action?.payload?.data
              : "";
      }
    
      return state;
    },
    setdropdownDataForExtendedCode: (state, action) => {
      const { uniqueId, data } = action.payload;

      if (uniqueId) {
        state.dropdownDataForExtendedCode[uniqueId] = Array.isArray(data) ? data : [data];
      }

      return state;
    },
    
    setSelecteddropdownDataForExtendedCode: (state, action) => {
      const { uniqueId, data } = action.payload;

      if (uniqueId) {
        state.selecteddropdownDataForExtendedCode[uniqueId] = Array.isArray(data) ? data : [data];
      }

      return state;
    },
    setgeneralLedgerTypeDescription: (state, action) => {
      state.generalLedgerTypeDescription = action.payload;
    },
    setgeneralLedgerControlData: (state, action) => {
      state.generalLedgerControlData = action.payload;
    },
    setgeneralLedgerCreateBankIntrest: (state, action) => {
      state.generalLedgerCreateBankInterest = action.payload;
    },
    setgeneralLedgerKeywordTranslation: (state, action) => {
      state.generalLedgerKeywordTranslation = action.payload;
    },
    setgeneralLedgerInformation: (state, action) => {
      state.generalLedgerInformation = action.payload;
    },
  
    setSinglegeneralLedgerPayload: (state, action) => {
      state.singleGLPayload[action.payload.keyName] = action.payload.data;
      return state;
    },
    setSinglegeneralLedgerPayloadGI: (state, action) => {
      state.singleGLPayloadGI[action.payload.keyName] = action.payload.data;
      return state;
    },
    setActiveStepInPayload: (state, action) => {
      state.activeStepInPayload = action.payload;
      return state;
    },
    setGeneralLedgerViewData: (state, action) => {
      state.generalLedgerViewData = action.payload;
    },
    setMultipleGLRequestBench(state, action) {
      state.MultipleGLRequestBench = action.payload;
      return state;
    },
    setMultipleGLData(state, action) {
      state.MultipleGLData = action.payload;
    },
    setMultipleGlDataForExtend(state, action) {
      state.MultipleGlDataForExtend = action.payload;
    },
    setCompanyCodeListExtend: (state, action) => {
      state.companyCodeListExtend = action.payload;
    },
    setMassfercInfo(state,action){
      state.massfercInfo  = action.payload;
    },
    setSelectedRowIdGL: (state, action) => {
      state.selectedRowId = action.payload;
    },
    setSelectedOptionsForTemplate: (state, action) => {
      state.selectedOptionsForTemplate= action?.payload?.values;
    },
    setlongTextFor5161Series(state,action){
      state.longTextFor5161Series  = action.payload;
    },
    setshortTextFor5161Series(state,action){
      state.shortTextFor5161Series  = action.payload;
    },
    setArtifactId(state, action) {
      state.ArtifactId = [...state.ArtifactId,action.payload];
    },
    setGlGIPayload(state, action)  {
      state.glGIPayload = [...state.glGIPayload,action.payload];
    },
    setEditMultipleGlData(state,action){
      state.editMultipleGlData =action.payload;
    },
    setEditMultipleGlExtend(state,action){
      state.editMultipleGlExtend =action.payload;
    },
    setOpenDialog: (state, action) => {
      state.isOpenDialog = action.payload;
    },
    clearProfitCenterPayload: (state) => {
      state.singleGLPayload = {};
    },
    setGLRequiredFields: (state, action) => {
      if (
        state.requiredFields.findIndex((item) => item == action.payload) == -1
      ) {
        state.requiredFields.push(action.payload);
      }
      return state;
    },
    setGLRequiredFieldsGI: (state, action) => {
      if (
        state.requiredFieldsGI.findIndex((item) => item == action.payload) == -1
      ) {
        state.requiredFieldsGI.push(action.payload);
      }
      return state;
    },
    setGLErrorFields: (state, action) => {
      state.errorFields = action.payload;
      return state;
    },
    setHandleMassMode(state, action) {
      state.handleMassMode = action.payload;
    },
    setValidatedStatus: (state, action) => {
      const { rowId, status } = action.payload;
      state.validatedRowsStatus[rowId] = status;
    },
    setGeneralLedgerConfig: (state, action) => {
      state.generalLedgerConfig = action.payload;
    },
    setGeneralLedgerTabs: (state, action) => {
          if (!Array.isArray(state.generalLedgerTabs)) {
            state.generalLedgerTabs = [];
          }
          action.payload.forEach(({ tab, data }) => {
            const existingTabIndex = state.generalLedgerTabs.findIndex(t => t.tab === tab);
        
            if (existingTabIndex !== -1) {
              state.generalLedgerTabs[existingTabIndex].data = data;
            } else {
              state.generalLedgerTabs.push({ tab, data });
            }
          });
    },
    setGeneralLedgerData: (state, action) => {
      state.generalLedgerData = action.payload;
    },
    clearGeneralLedger: (state) => {
      state.massfercInfo=[]
      state.singleGLPayload = {}
      state.requiredFields = []
      state.requiredFieldsGI =[]
      state.errorFields = []
      state.MultipleGLData =[]
      state.activeStepInPayload=0
      state.shortTextFor5161Series=[],
      state.longTextFor5161Series =[],
      state.singleGLPayloadGI={}
    },
    cleareditMultipleGlExtend: (state)=>{
      
      state.editMultipleGlExtend=[]
    },
    resetGLStateGL: () => initialState,
    resetValidationStatus: (state) => {
      state.validatedRowsStatus = {};
    },

    clearSingleGLPayloadGI: (state) => {
      state.singleGLPayloadGI = {};
    },

    clearRequiredFields: (state) => {
      state.requiredFields = []
    },
     setChangedFieldsMapGL: (state, action) => {
      state.changedFieldsMap = action.payload;
    },
    setFetchedGeneralLedgerDataGL: (state, action) => {
      state.fetchedGeneralLedgerData = action.payload;
    },
     updateReqBenchRowGL: (state, action) => {
      const updatedRow = action.payload;
      state.fetchReqBenchData = state.fetchReqBenchData.map((row) =>
        row.id === updatedRow.id ? updatedRow : row
      );

      const originalRow = state.originalReqBenchData.find(
        (row) => row.id === updatedRow.id
      );

      const changedFields = {};
      if (originalRow) {
        Object.keys(updatedRow).forEach((key) => {
          if (updatedRow[key] !== originalRow[key]) {
            changedFields[key] = true;
          }
        });
      }

      state.changedFieldsMap[updatedRow.id] = changedFields;
    },

    setFetchReqBenchDataGL: (state, action) => {
      state.fetchReqBenchData = action.payload;
    },
    setOriginalReqBenchDataGL: (state, action) => {
      state.originalReqBenchData = action.payload;
    },
    setFetchedGeneralLedgerGL: (state, action) => {
      state.fetchedGeneralLedgerData = action.payload;
    },
    setOriginalGeneralLedgerDataGL: (state, action) => {
      state.originalGeneralLedgerData = action.payload;
    },


  },
});
export const {
  setGLRows,
  setGLPayload,
  setOpenDialog,
  setRequestHeaderPayloadSingleField,
  setRequestHeaderPayloadData,
  setdropdownDataForExtendedCode,
  updateModuleFieldDataGL,
  setgeneralLedgerTypeDescription,
  setgeneralLedgerControlData,
  setgeneralLedgerCreateBankIntrest,
  setgeneralLedgerKeywordTranslation,
  setgeneralLedgerInformation,
  setSinglegeneralLedgerPayload,
  setSinglegeneralLedgerPayloadGI,
  setGeneralLedgerViewData,
  setHandleMassMode,
  setMultipleGLRequestBench,
  setMultipleGLData,
  setEditMultipleGlData,
  setGLRequiredFields,
  setGLErrorFields,
  clearGeneralLedger,
  cleareditMultipleGlExtend,
  setEditMultipleGlExtend,
  setArtifactId,
  setGlGIPayload,
  setGLRequiredFieldsGI,
  setMassfercInfo,
  setMultipleGlDataForExtend,
  setActiveStepInPayload,
  setCompanyCodeListExtend,
  setlongTextFor5161Series,
  setshortTextFor5161Series,
  clearSingleGLPayloadGI,
  clearRequiredFields,
  setGeneralLedgerTabs,
  setGeneralLedgerData,
  setGeneralLedgerConfig,
  resetPayloadDataGL,
  setValidatedStatus,
  setSelectedRowIdGL,
  setSelecteddropdownDataForExtendedCode,
  resetGLStateGL,
  resetValidationStatus,
  setFetchReqBenchDataGL,
  setOriginalReqBenchDataGL,
  setChangedFieldsMapGL,
  setOriginalGeneralLedgerDataGL,
  setFetchedGeneralLedgerGL,
  updateReqBenchRowGL,
  setFetchedGeneralLedgerDataGL,
  updateGeneralLedgerRowGL,
  setSelectedOptionsForTemplate
  
  
} = generalLedgerTabSlice.actions;

export const generalLedgerReducer = generalLedgerTabSlice.reducer;
