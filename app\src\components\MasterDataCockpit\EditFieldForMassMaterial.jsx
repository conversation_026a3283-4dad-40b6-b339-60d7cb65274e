import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Grid,
  Autocomplete,
  IconButton,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import SaveIcon from "@mui/icons-material/Save";
import { DateRangePicker } from "rsuite";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { setPayload } from "../../app/editPayloadSlice";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import {
  setEditMultipleMaterial,
  setMultipleMaterial,
} from "../../app/initialDataSlice";
import { useLocation } from "react-router-dom";

function transformApiData(apiValue, dropDownData) {
  if (Array.isArray(dropDownData)) {
    const matchingOption = dropDownData.find(
      (option) => option.code === apiValue
    );
    return matchingOption || "";
  } else {
    return "";
  }
}
const EditableFieldForMassMaterial = ({
  label,
  value,
  fieldGroup,
  units,
  onSave,
  isEditMode,
  isExtendMode,
  selectedRowData,
  options = [],
  type,
}) => {
  const [editedValue, setEditedValue] = useState(value);
  const [changeStatus, setChangeStatus] = useState(false);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  const dispatch = useDispatch();
  const transformedValue = transformApiData(editedValue, dropDownData);
  console.log("dropdownData", editedValue);
  console.log("value e", value);
  console.log("label", label);
  console.log("units", units);
  console.log("transformedValue", transformedValue);
  const location = useLocation();
  const MultipleMaterial = useSelector(
    (state) => state.initialData.MultipleMaterial
  );
  const editField = useSelector((state) => state.edit.payload);
  const description = useSelector(
    (state) => state.initialData.MultipleMaterial[0]["Description"]
  );
  let activeRow = {};
  let activeIndex = -1;
  for (let index = 0; index < MultipleMaterial.length; index++) {
    if (MultipleMaterial[index].Description === selectedRowData) {
      activeRow = MultipleMaterial[index];
      activeIndex = index;
      break;
    }
  }
  console.log("editField", editField);
  const fieldData = {
    label,
    value: editedValue,
    units,
    type,
  };
  console.log("fieldData", fieldData);
  const handleSave = () => {
    onSave(fieldData);
  };
  let key = label
    .replaceAll("(", "")
    .replaceAll(")", "")
    .replaceAll("/", "")
    .replaceAll("-", "")
    .replaceAll(".", "")
    .split(" ")
    .join("");
  useEffect(() => {
    setEditedValue(value);
  }, [value]);

  const onEdit = (label, newValue) => {
    // dispatch(
    //   setPayload({
    //     keyname: key
    //       .replaceAll("(", "")
    //       .replaceAll(")", "")
    //       .replaceAll("/", "")
    //       .replaceAll("-", "")
    //       .replaceAll(".", "")
    //       .split(" ")
    //       .join(""),
    //     data: newValue,
    //   })
    // );
    // dispatch(
    //   setMultipleMaterial({
    //     [location?.state?.description]: { [label]: newValue },
    //   })
    // );
    // [activeIndex]["Basic Data"][fieldGroup].label;
    dispatch(
      setMultipleMaterial(
        MultipleMaterial.map((item, index) => {
          if (index == activeIndex) {
            let temp = item["Basic Data"];
            let temp2 = item["Basic Data"][fieldGroup];
            return {
              ...item,
              ["Basic Data"]: {
                ...temp,
                [fieldGroup]: temp2.map((innerItem) => {
                  if (innerItem.fieldName === label) {
                    return { ...innerItem, value: newValue };
                  } else {
                    return innerItem;
                  }
                }),
              },
            };
          } else {
            return item;
          }
        })
      )
    );

    // dispatch(setMultipleMaterial);
  };
  useEffect(() => {
    console.log("lkey", key);
    console.log("data", value);
    dispatch(
      setPayload({
        keyname: key
          .replaceAll("(", "")
          .replaceAll(")", "")
          .replaceAll("/", "")
          .replaceAll("-", "")
          .replaceAll(".", "")
          .split(" ")
          .join(""),
        data: value ? value : "",
      })
    );
  }, []);
  console.log("editedValue[key] ", dropDownData[key]);
  console.log("editedValue[key] ", editedValue);
  return (
    <Grid item>
      <Stack>
        {isEditMode || isExtendMode ? (
          <>
            <Typography variant="body2" color="#777">
              {label}
            </Typography>
            {type === "Drop Down" ? (
              <Autocomplete
                options={dropDownData[key] ?? []}
                value={
                  (editedValue &&
                    dropDownData[key]?.filter((x) => x.code === editedValue)) ||
                  ""
                }
                onChange={(event, newValue) => {
                  onEdit(label, newValue.code);
                  console.log("newValue", newValue);
                  setEditedValue(newValue.code);
                  setChangeStatus(true);
                  console.log("keys", key);
                }}
                getOptionLabel={(option) => {
                  console.log("optionoptionoption", option);
                  return option === ""
                    ? ""
                    : `${option && option[0]?.code} - ${
                        option && option[0]?.desc
                      }`;
                }}
                // isOptionEqualToValue={(a,b)=>{ return a.code===b.code}}
                renderOption={(props, option) => {
                  console.log("option vakue", option);
                  return (
                    <li {...props}>
                      <Typography style={{ fontSize: 12 }}>
                        {`${option?.code} - ${option?.desc}`}
                      </Typography>
                    </li>
                  );
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    size="small"
                    label={null}
                  />
                )}
              />
            ) : type === "Input" ? (
              <TextField
                variant="outlined"
                size="small"
                value={editedValue}
                onChange={(event) => {
                  const newValue = event.target.value;
                  onEdit(label, newValue);
                  setEditedValue(newValue);
                }}
              />
            ) : type === "Calendar" ? (
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  slotProps={{ textField: { size: "small" } }}
                  // value={valueFromPayload[props?.keyName]}
                  placeholder="Select Date Range"
                  // onChange={(newValue) =>
                  //   dispatch(
                  //     setPayload({
                  //       keyName: props.keyName,
                  //       data: "/Date(" + Date.parse(newValue) + ")/",
                  //     })
                  //   )
                  // }
                  // required={
                  //   props.details.visibility === "0" ||
                  //   props.details.visibility === "Required"
                  // }
                />
              </LocalizationProvider>
            ) : type === "Radio Button" ? (
              <Checkbox
                sx={{ borderRadius: "0 !important" }}
                checked={editedValue}
                onChange={(event, newValue) => {
                  onEdit(label, newValue);
                  setEditedValue(newValue);
                }}
              />
            ) : (
              ""
            )}
          </>
        ) : (
          <>
            <>
              <Typography variant="body2" color="#777">
                {label}
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {editedValue} {units}
              </Typography>
            </>
          </>
        )}
      </Stack>
    </Grid>
  );
};

export default EditableFieldForMassMaterial;
