import {
  LinearProgress,
  Stack,
  Box,
  Typography,
  Select,
  MenuItem,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  container_table,
  container_tableHeader,
  primary_Color,
} from "../common/commonStyles";
import { useSelector } from "react-redux";
import moment from "moment/moment";
import { DataGrid } from "@mui/x-data-grid";
import styled from "@emotion/styled";
import { useNavigate } from "react-router-dom";

export const reqparamBottle = "create";

export default function OpenTaskTableBottle({
  tableRow,
  loader,
  reqparamBottle,
  setreqparamBottle,
}) {
  const StyledGridOverlay = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "100%",
  }));

  function CustomNoRowsOverlay() {
    return (
      <StyledGridOverlay>
        <Box sx={{ mt: 1 }}>No Data Available</Box>
      </StyledGridOverlay>
    );
  }

  const handleOptionChange = (event) => {
    setreqparamBottle(event.target.value);
  };

  const appSettings = useSelector((state) => state.appSettings);
  const navigate = useNavigate();

  const columns = [
    {
      field: "name",
      headerName: "Average by Review",
      width: 390,
      headerAlign: "left",
      align: "left",
    },
    {
      field: "days",
      headerName: "Days",
      editable: false,
      flex: 1,
      align: "left",
      headerAlign: "left",
    },
  ];

  return (
    <>
      <Box sx={{ container_table, marginTop: 0, marginRight: 2 }} mb={2}>
        <div className="reusable-table" style={{ position: "relative" }}>
          <Stack
            justifyContent="space-between"
            direction="row"
            sx={container_tableHeader}
          >
            <Typography fontSize={"12px"} fontWeight={"bold"} mt={2}>
              List of Onboarding Status
            </Typography>
            <Typography fontSize={"10px"}>
              <Select
                value={reqparamBottle}
                onChange={handleOptionChange}
                style={{ minWidth: "100px" }}
                size="small"
              >
                <MenuItem value="create" style={{ width: "80px" }}>
                  Create
                </MenuItem>
                <MenuItem value="change" style={{ width: "80px" }}>
                  Change
                </MenuItem>
                <MenuItem value="extend" style={{ width: "80px" }}>
                  Extend
                </MenuItem>
              </Select>
            </Typography>
          </Stack>
          <DataGrid
            loading={loader}
            getRowId={(row) => row.name}
            rows={tableRow ?? []}
            width="100%"
            autoHeight
            rowHeight={50}
            disableSelectionOnClick
            columns={columns}
            marginRight={"10px"}
            disableExtendRowFullWidth={false}
            hideFooter={true}
            sx={{
              "& .MuiDataGrid-row:hover": {
                backgroundColor: "#EAE9FF40",
              },
              backgroundColor: "#fff",
            }}
            components={{
              LoadingOverlay: LinearProgress,
              NoRowsOverlay: CustomNoRowsOverlay,
            }}
          />
        </div>
      </Box>
    </>
  );
}
