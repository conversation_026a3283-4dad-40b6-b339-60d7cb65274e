const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {

  app.use(
    '/WorkObjectsJavaServices',
    createProxyMiddleware({
      target: 'https://modellingapi.cfapps.eu10.hana.ondemand.com',
      changeOrigin: true,
      pathRewrite: {'^/WorkObjectsJavaServices' : ''}
    })
  );
  
  app.use(
    '/WorkObjectsDataJavaServices',
    createProxyMiddleware({
      target: 'https://hrapps.cfapps.eu10.hana.ondemand.com',
      changeOrigin: true,
      pathRewrite: {'^/WorkObjectsDataJavaServices' : ''}
    })
  );
  app.use(
    '/workbox-mail-configuration',
    createProxyMiddleware({
      // target: 'https://workbox-email-configuration.cfapps.eu10.hana.ondemand.com',
      target: 'https://wutilsservicesdev.cherryworkproducts.com',
      changeOrigin: true,
      pathRewrite: {'^/workbox-mail-configuration' : '/rest'}
    })
  );
  app.use(
    '/workbox-field-catalog',
    createProxyMiddleware({
      // target: 'https://workbox-field-catalog.cfapps.eu10.hana.ondemand.com',
      target: 'https://wutilsservicesdev.cherryworkproducts.com',
      changeOrigin: true,
       pathRewrite: {'^/workbox-field-catalog' : '/rest'}
    })
  );
  app.use(
    '/CW_Worktext',
    createProxyMiddleware({
      target: 'https://cwworktext.cfapps.eu10.hana.ondemand.com',
      changeOrigin: true,
      pathRewrite: {'^/CW_Worktext' : ''}
    })
  );

};