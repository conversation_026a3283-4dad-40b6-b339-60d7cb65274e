{"accessItems": [{"id": 6, "iwaName": "Home", "name": "Home", "type": "module", "componentName": "Home", "itemCode": "", "isSideOption": true, "displayName": "Home", "description": "View latest Announcements and Videos here", "icon": "Home", "routePath": "/", "importPath": "Home/Home", "isAccessible": true, "childItems": []}, {"id": 1, "iwaName": "Dashboard", "name": "Dashboard", "type": "module", "componentName": "Dashboard", "itemCode": "", "isSideOption": true, "displayName": "Dashboard", "description": "View KPIs and download reports here", "icon": "Dashboard", "routePath": "/dashboard", "importPath": "Dashboard/Dashboard", "isAccessible": true, "childItems": []}, {"id": 2, "iwaName": "Workspace", "name": "Workspace", "type": "module", "componentName": "Workspace", "itemCode": "", "isSideOption": true, "displayName": "Workspace", "description": "Tasks can be actioned from this section", "icon": "GroupWork", "routePath": "/workspace/", "importPath": "", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "My Tasks", "name": "MyTasks", "type": "submodule", "componentName": "MyTasks", "itemCode": "", "isSideOption": true, "displayName": "Open Tasks", "description": "This section allows us to take action on our assigned tasks", "icon": "PendingActions", "routePath": "/workspace/MyTasks", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 2, "iwaName": "Completed Tasks", "name": "CompletedTasks", "type": "submodule", "componentName": "CompletedTasks", "itemCode": "", "isSideOption": true, "displayName": "Completed Tasks", "description": "This section shows our completed tasks", "icon": "Task", "routePath": "/workspace/completedtasks", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 3, "iwaName": "Admin Tasks", "name": "AdminTasks", "type": "submodule", "componentName": "AdminTasks", "itemCode": "", "isSideOption": true, "displayName": "Admin Tasks", "description": "This section allows the Super User to take action on all tasks", "icon": "AssignmentInd", "routePath": "/workspace/AdminTasks", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 4, "iwaName": "Admin Completed Tasks", "name": "AdminCompletedTasks", "type": "submodule", "componentName": "AdminCompletedTasks", "itemCode": "", "isSideOption": true, "displayName": "Admin Completed Tasks", "description": "This section allows the Super User to view all completed tasks", "icon": "HowToReg", "routePath": "/workspace/AdminCompletedTasks", "importPath": "", "isAccessible": true, "childItems": []}]}, {"id": 3, "iwaName": "Master Data", "name": "MasterData", "type": "module", "componentName": "MasterData", "itemCode": "", "isSideOption": true, "displayName": "Master Data", "description": "View all master data objects available in the system here", "icon": "Dataset", "routePath": "/masterDataCockpit/", "importPath": "", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "Material", "name": "Material", "type": "submodule", "componentName": "Material", "itemCode": "", "isSideOption": true, "displayName": "Material", "description": "Here we can Search, Display, Create, Change and Extend Material", "icon": "ShoppingCart", "routePath": "/masterDataCockpit/materialMaster/material", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 2, "iwaName": "BOM", "name": "BOM", "type": "submodule", "componentName": "BillOfMaterial", "itemCode": "", "isSideOption": true, "displayName": "BOM", "description": "Here we can Search, Display, Create and Change Bill of Material", "icon": "Mediation", "routePath": "/masterDataCockpit/billOfMaterial", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 3, "iwaName": "Cost Center", "name": "CostCenter", "type": "submodule", "componentName": "CostCenter", "itemCode": "", "isSideOption": true, "displayName": "Cost Center", "description": "Here we can Search, Display, Create and Change Cost Center", "icon": "TrendingDown", "routePath": "/masterDataCockpit/costCenter", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 4, "iwaName": "Profit Center", "name": "ProfitCenter", "type": "submodule", "componentName": "ProfitCenter", "itemCode": "", "isSideOption": true, "displayName": "Profit Center", "description": "Here we can Search, Display, Create and Change Profit Center", "icon": "TrendingUp", "routePath": "/masterDataCockpit/profitCenter", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 5, "iwaName": "General <PERSON><PERSON>", "name": "GeneralLedger", "type": "submodule", "componentName": "GeneralLedger", "itemCode": "", "isSideOption": true, "displayName": "General <PERSON><PERSON>", "description": "Here we can Search, Di<PERSON>lay, Create Change and Extend General Ledger", "icon": "Assignment", "routePath": "/masterDataCockpit/generalLedger", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 6, "iwaName": "Cost Center Hierarchy", "name": "Cost Center Hierarchy", "type": "submodule", "componentName": "GeneralLedger", "itemCode": "", "isSideOption": true, "displayName": "Cost Center Hierarchy", "description": "Here we can Search, Display, Create and Change Cost Center Hierarchy", "icon": "<PERSON><PERSON><PERSON>", "routePath": "/masterDataCockpit/groupNode/hierarchyNodeCostCenter", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 7, "iwaName": "Profit Center Hierarchy", "name": "Profit Center Hierarchy", "type": "submodule", "componentName": "GeneralLedger", "itemCode": "", "isSideOption": true, "displayName": "Profit Center Hierarchy", "description": "Here we can Search, Display, Create and Change Profit Center Hierarchy", "icon": "MergeType", "routePath": "/masterDataCockpit/groupNode/hierarchyNodeProfitCenter", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 8, "iwaName": "Internal Order", "name": "Internal Order", "type": "submodule", "componentName": "GeneralLedger", "itemCode": "", "isSideOption": true, "displayName": "Internal Order", "description": "Here we can Search, Display, Create and Change Internal Order", "icon": "ReceiptLong", "routePath": "/masterDataCockpit/internalOrder", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 9, "iwaName": "General Ledger Hierarchy", "name": "General Ledger Hierarchy", "type": "submodule", "componentName": "GeneralLedger", "itemCode": "", "isSideOption": true, "displayName": "General Ledger Hierarchy", "description": "Here we can Search, Di<PERSON>lay, Create and Change General Ledger Hierarchy", "icon": "Account<PERSON>ree", "routePath": "/masterDataCockpit/groupNode/hierarchyNodeGeneralLedger", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 9, "iwaName": "Bank Key", "name": "BankKey", "type": "submodule", "componentName": "BankKey", "itemCode": "", "isSideOption": true, "displayName": "Bank Key", "description": "Here we can Search, Display, Create and Change Bill of Material", "icon": "AccountBalance", "routePath": "/masterDataCockpit/bankKey", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 10, "iwaName": "Article", "name": "Article", "type": "submodule", "componentName": "Article", "itemCode": "", "isSideOption": true, "displayName": "Article", "description": "Here we can Search, Display and Create Articles", "icon": "ShoppingBag", "routePath": "/masterDataCockpit/articleMaster/article", "importPath": "", "isAccessible": true, "childItems": []}]}, {"id": 4, "iwaName": "Request Bench", "name": "RequestBench", "type": "module", "componentName": "RequestBench", "itemCode": "", "isSideOption": true, "displayName": "Request Bench", "description": "Access active/completed requests and scheduler management.", "icon": "Task", "routePath": "/requestBench", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 5, "iwaName": "Data Cleanse", "name": "DataCheck", "type": "module", "componentName": "DataCheck", "itemCode": "", "isSideOption": true, "displayName": "Data Cleanse", "description": "Identify inconsistencies in accordance to business rules to ensure optimal data.", "icon": "CleaningServices", "routePath": "/dataCheck", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 6, "iwaName": "Document Management", "name": "DocumentManagement", "type": "module", "componentName": "DocumentManagement", "itemCode": "", "isSideOption": true, "displayName": "Document Management", "description": "All uploaded documents in the DMS are available for viewing here", "icon": "FolderOpen", "routePath": "/documentManagement", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 7, "iwaName": "Config <PERSON>", "name": "ConfigCockpit", "type": "module", "componentName": "ConfigCockpit", "itemCode": "", "isSideOption": true, "displayName": "Config <PERSON>", "description": "Administrative responsibilities can be managed here", "icon": "Settings", "routePath": "/configCockpit", "importPath": "UserManagement/Components/MasterData", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "Business Rules", "name": "BusinessRules", "type": "module", "componentName": "Business Rules", "itemCode": "", "isSideOption": true, "displayName": "Business Rules", "description": "Modify the business rules as needed", "icon": "Article", "routePath": "/configCockpit/businessRules", "importPath": "", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "Authoring", "name": "Authoring", "type": "module", "componentName": "Authoring", "description": "Modify the business rules as needed", "itemCode": "", "isSideOption": true, "displayName": "Authoring", "icon": "Person", "routePath": "/configCockpit/businessRules/authoring", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 2, "iwaName": "Modelling", "name": "Modelling", "type": "submodule", "componentName": "Modelling", "description": "Modify the business rules as needed", "itemCode": "", "isSideOption": true, "displayName": "Modelling", "icon": "Account<PERSON>ree", "routePath": "/configCockpit/businessRules/modelling", "importPath": "", "isAccessible": true, "childItems": []}]}, {"id": 2, "iwaName": "Field Configurations", "name": "FieldConfigurations", "type": "submodule", "componentName": "FieldConfigurations", "itemCode": "", "isSideOption": true, "displayName": "Field Configurations", "icon": "ShoppingCart", "routePath": "/configCockpit", "importPath": "", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "Material", "name": "Material", "type": "submodule", "componentName": "Material", "itemCode": "", "isSideOption": true, "displayName": "Material", "icon": "ShoppingCart", "routePath": "/configCockpit/fieldSelection", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 2, "iwaName": "Cost Center", "name": "CostCenter", "type": "submodule", "componentName": "CostCenter", "itemCode": "", "isSideOption": true, "displayName": "Cost Center", "icon": "TrendingDown", "routePath": "/configCockpit/fieldConfiguration/costCenter", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 3, "iwaName": "Profit Center", "name": "ProfitCenter", "type": "submodule", "componentName": "ProfitCenter", "itemCode": "", "isSideOption": true, "displayName": "Profit Center", "icon": "TrendingUp", "routePath": "/configCockpit/fieldConfiguration/profitCenter", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 4, "iwaName": "Bank Key", "name": "BankKey", "type": "submodule", "componentName": "BankKey", "itemCode": "", "isSideOption": true, "displayName": "Bank Key", "icon": "AccountBalance", "routePath": "/configCockpit/fieldConfiguration/bankKey", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 5, "iwaName": "General <PERSON><PERSON>", "name": "GeneralLedger", "type": "submodule", "componentName": "GeneralLedger", "itemCode": "", "isSideOption": true, "displayName": "General <PERSON><PERSON>", "icon": "Assignment", "routePath": "/configCockpit/fieldConfiguration/generalLedger", "importPath": "", "isAccessible": true, "childItems": []}]}, {"id": 3, "iwaName": "SLA Configurations", "name": "SLAConfigurations", "type": "submodule", "componentName": "SLAConfigurations", "itemCode": "", "isSideOption": true, "displayName": "SLA Configurations", "icon": "AccessAlarm", "routePath": "/configCockpit/SLAManagement", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 4, "iwaName": "Email Template Configurations", "name": "EmailTemplateConfigurations", "type": "submodule", "componentName": "EmailTemplateConfigurations", "itemCode": "", "isSideOption": true, "displayName": "Email Template Configurations", "description": "Oversee email communications and template configurations", "icon": "MailOutline", "routePath": "/configCockpit/EmailTemplateConfig", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 5, "iwaName": "User Management", "name": "UserManagement", "type": "module", "componentName": "UserManagement", "itemCode": "", "isSideOption": true, "displayName": "User Management", "description": "Administer user accounts within the application", "icon": "Person", "routePath": "/configCockpit/userManagement?", "importPath": "", "isAccessible": true, "childItems": [{"id": 1, "iwaName": "Users", "name": "Users", "type": "module", "componentName": "IwaUsersSummary", "description": "User details", "itemCode": "", "isSideOption": true, "displayName": "users", "icon": "Boy", "routePath": "/configCockpit/userManagement/UsersSummary", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 2, "iwaName": "Roles", "name": "Roles", "type": "submodule", "componentName": "IwaRolesSummary", "description": "Modify and view Roles", "itemCode": "", "isSideOption": true, "displayName": "Roles", "icon": "AdminPanelSettings", "routePath": "/configCockpit/userManagement/RolesSummary", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 3, "iwaName": "Groups", "name": "Groups", "type": "submodule", "componentName": "GroupsSummary", "description": "Modify and edit Groups", "itemCode": "", "isSideOption": true, "displayName": "Groups", "icon": "Groups2", "routePath": "/configCockpit/userManagement/GroupsSummary", "importPath": "", "isAccessible": true, "childItems": []}]}, {"id": 6, "iwaName": "Application Configuration", "name": "ApplicationConfiguration", "type": "submodule", "componentName": "ApplicationConfiguration", "itemCode": "", "isSideOption": true, "displayName": "Application Configuration", "icon": "AppSettingsAlt", "routePath": "/configCockpit/applicationConfiguration?", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 8, "iwaName": "Broadcast Configurations", "name": "BroadcastConfigurations", "type": "submodule", "componentName": "BroadcastConfigurations", "itemCode": "", "isSideOption": true, "displayName": "Broadcast Configurations", "description": "Create and Edit your Broadcasts as per your requirements", "icon": "AppSettingsAlt", "routePath": "/configCockpit/broadcastConfigurations", "importPath": "", "isAccessible": true, "childItems": []}, {"id": 9, "iwaName": "Document Configurations", "name": "DocumentConfigurations", "type": "submodule", "componentName": "DocumentConfigurations", "itemCode": "", "isSideOption": true, "displayName": "Document Configurations", "description": "Upload your documents for Chatbot as per your requirements", "icon": "FolderOpen", "routePath": "/configCockpit/documentConfigurations", "importPath": "", "isAccessible": true, "childItems": []}]}], "faviconLink": "/favicon.ico", "appHeaderLogoClientName": "scp_AppheaderImg", "sideNavMoreOptions": 6, "system": "CHWSCP"}