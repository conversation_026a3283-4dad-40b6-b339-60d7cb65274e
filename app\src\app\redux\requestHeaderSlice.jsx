// src/redux/requestHeaderSlice.js
import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  requestId: "",
  reqCreatedBy: "",
  reqCreatedOn: "",
  reqUpdatedOn: "",
  requestType: "",
  requestDesc: "",
  requestStatus: "Draft",
  requestPriority: "",
  requestPrefix: "",
  division: "",
  region: "",
  leadingCat: "",
  firstProd: null,
  launchDate: null,
  fieldName: "",
  templateName: "", // selected change category/template name
  changeTemplateConfig: {}, // added: full change template DT config
  isBifurcated: false,
  screenName: null,
};

const requestHeaderSlice = createSlice({
  name: "requestHeader",
  initialState,
  reducers: {
    setRequestHeaderData: (state, action) => {
      return { ...state, ...action.payload };
    },
    resetRequestHeaderData: () => initialState,

    // ✅ Set only the selected template name
    setTemplateName: (state, action) => {
      state.templateName = action.payload;
    },

    // ✅ Set the fetched change template DT config object
    setChangeTemplateConfig: (state, action) => {
      state.changeTemplateConfig = action.payload;
    },
  },
});

export const {
  setRequestHeaderData,
  resetRequestHeaderData,
  setTemplateName,
  setChangeTemplateConfig,
} = requestHeaderSlice.actions;

export default requestHeaderSlice.reducer;
