import CustomDialog from "@components/Common/ui/CustomDialog";
import { DialogContent, DialogActions, Button, Typography } from "@mui/material";
import DescriptionIcon from "@mui/icons-material/Description";
import { colors } from "@constant/colors";
import SingleSelectDropdown from '@components/Common/ui/dropdown/SingleSelectDropdown';
import { useState } from "react";
import { useDispatch } from "react-redux";
import { UpdateMaterialFieldsValue } from "@app/payloadSlice";
import { HEADINGS, MATERIAL_VIEWS } from "@constant/enum";

const OrgDataCopyModal = ({ open, onClose, title, lengthOfOrgRow, selectedMaterialPayload, materialID, orgRows }) => {
    const [selectedOption, setSelectedOption] = useState({});
    const dispatch = useDispatch();

    const generateOptions = () => {
        const options = [];
        if (orgRows && orgRows.length > 0) {
            orgRows?.forEach((row, index) => {
                if (index !== lengthOfOrgRow?.copyFor) {
                    const plantCode = row.plant?.value?.code;
                    const plantDesc = row.plant?.value?.desc || plantCode;
                    const salesOrgCode = row.salesOrg?.code;
                    const salesOrgDesc = row.salesOrg?.desc || salesOrgCode;
                    const dcCode = row.dc?.value?.code;
                    const dcDesc = row.dc?.value?.desc || dcCode;
                    const warehouseCode = row.warehouse?.value?.code;
                    const warehouseDesc = row.warehouse?.value?.desc || warehouseCode;
                    
                    if (plantCode) {
                        let displayDesc = `Plant: ${plantDesc || 'N/A'}`;
                        if (salesOrgCode) displayDesc += ` | SalesOrg: ${salesOrgDesc || 'N/A'}`;
                        if (dcCode) displayDesc += ` | DC: ${dcDesc || 'N/A'}`;
                        if (warehouseCode) displayDesc += ` | Warehouse: ${warehouseDesc || 'N/A'}`;
                        let codeString = plantCode;
                        if (salesOrgCode) codeString += `-${salesOrgCode}`;
                        if (dcCode) codeString += `-${dcCode}`;
                        if (warehouseCode) codeString += `-${warehouseCode}`;
                        
                        options?.push({
                            code: codeString,
                            desc: displayDesc,
                            index: index,
                            plant: plantCode,
                            salesOrg: salesOrgCode,
                            dc: dcCode,
                            warehouse: warehouseCode
                        });
                    }
                }
            });
        }
        return options;
    };

    const handleOk = () => {
        if (!selectedOption.code) {
            return;
        }

        const targetRow = orgRows[lengthOfOrgRow.copyFor];
        const targetPlantCode = targetRow?.plant?.value?.code;
        const targetSalesOrgCode = targetRow?.salesOrg?.code;
        const targetDcCode = targetRow?.dc?.value?.code;
        const targetWarehouseCode = targetRow?.warehouse?.value?.code;
        
        if (!targetPlantCode) {
            return;
        }

        const updatedPayload = JSON.parse(JSON.stringify(selectedMaterialPayload));
        
        Object.keys(updatedPayload)?.forEach(section => {
            const sectionData = updatedPayload[section];
            if (section === MATERIAL_VIEWS.BASIC_DATA || 
                section === MATERIAL_VIEWS.SALES_GENERAL || 
                section === MATERIAL_VIEWS.PURCHASING_GENERAL || 
                section === MATERIAL_VIEWS.TAX_DATA) {
                return;
            }
            
            if (typeof sectionData === 'object') {
                const keys = Object.keys(sectionData);

                if (section === MATERIAL_VIEWS.WAREHOUSE) {
                    const sourceKey = keys?.find(key => key.includes(selectedOption.warehouse));
                    const targetKey = keys?.find(key => key.includes(targetWarehouseCode));
                    
                    if (sourceKey && targetKey && targetKey !== sourceKey) {
                        const sourceData = JSON.parse(JSON.stringify(sectionData[sourceKey]));
                        delete sourceData.WarehouseId;
                        updatedPayload[section][targetKey] = {
                            ...JSON.parse(JSON.stringify(updatedPayload[section][targetKey] || {})),
                            ...sourceData,
                        };
                    }
                } else if (section === MATERIAL_VIEWS.SALES) {
                    const sourceSalesPattern = `${selectedOption.salesOrg}-${selectedOption.dc}`;
                    const targetSalesPattern = `${targetSalesOrgCode}-${targetDcCode}`;
                    
                    const sourceKey = keys?.find(key => key === sourceSalesPattern);
                    const targetKey = keys?.find(key => key === targetSalesPattern);
                    
                    if (sourceKey && targetKey && targetKey !== sourceKey) {
                        const sourceData = JSON.parse(JSON.stringify(sectionData[sourceKey]));
                        delete sourceData.SalesId;
                        updatedPayload[section][targetKey] = {
                            ...JSON.parse(JSON.stringify(updatedPayload[section][targetKey] || {})),
                            ...sourceData,
                        };
                    }
                } else {
                    const sourceKey = keys?.find(key => key.includes(selectedOption.plant));
                    const targetKey = keys?.find(key => key.includes(targetPlantCode));
                    
                    if (sourceKey && targetKey && targetKey !== sourceKey) {
                        const sourceData = JSON.parse(JSON.stringify(sectionData[sourceKey]));
                        if (sourceData) {
                            // Remove any ID fields from sourceData
                            delete sourceData.SalesId;
                            delete sourceData.PlantId;
                            delete sourceData.StorageLocationId;
                            delete sourceData.AccountingId;
                            
                            if (targetKey) {
                                updatedPayload[section][targetKey] = {
                                    ...JSON.parse(JSON.stringify(updatedPayload[section][targetKey] || {})),
                                    ...sourceData,
                                };
                            }
                        }
                    }
                }
            }
        });
        
        dispatch(UpdateMaterialFieldsValue({
            materialID: materialID,
            data: updatedPayload
        }));
        onClose();
    };
    
    return (
        <CustomDialog 
            isOpen={open} 
            titleIcon={<DescriptionIcon size="small" sx={{ color: colors?.primary?.dark, fontSize: "20px" }} />} 
            Title={title} 
            handleClose={() => onClose()}
        >
            <DialogContent sx={{ mt: 2 }}>
                <Typography sx={{ mb: 2 }}>{HEADINGS.COPY_ORG_DATA_VALUES}</Typography>
                <SingleSelectDropdown
                    options={generateOptions()}
                    placeholder="SELECT SOURCE ORGANIZATION"
                    onChange={(e) => setSelectedOption(e)}
                    value={selectedOption}
                />
            </DialogContent>
            <DialogActions>
                <Button variant="contained" size="small" onClick={() => handleOk()}>
                    Ok
                </Button>
            </DialogActions>
        </CustomDialog>
    );
}

export default OrgDataCopyModal
