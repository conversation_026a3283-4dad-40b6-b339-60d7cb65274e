import React from 'react';
import { TextField, CircularProgress } from '@mui/material';
import { colors } from '@constant/colors';

const FilterTextField = ({
  param,
  mandatory = false,
  selectedValues,
  handleSelectionChange,
  errors,
  isLoading = false,
  singleSelect = false,
}) => {
  
  const getValue = () => {
    const currentValue = selectedValues[param.key];
    if (Array.isArray(currentValue) && currentValue.length > 0) {
      return currentValue[0] || '';
    }
    return currentValue || '';
  };

  return (
    <TextField
      key={param.key}
      fullWidth
      label={
        mandatory ? (
          <>
            <strong>Enter {param.key}</strong> <span style={{ color: colors?.error?.dark }}>*</span>
          </>
        ) : (
          `Enter ${param?.label ? param?.label : param?.key}`
        )
      }
      variant="outlined"
      error={!!errors[param.key]}
      helperText={errors[param.key]}
      inputProps={{ maxLength:param?.length }}
      value={getValue()}
      onChange={(event) => {
        const newValue = event.target.value;
        handleSelectionChange(param.key, newValue
                                        ?.replace(param?.characterAllowed,"")
                                        ?.toUpperCase(),);
      }}
      InputProps={{
        endAdornment: (
          <>
            {isLoading ? (
              <CircularProgress size={20} sx={{ mr: 1 }} />
            ) : null}
          </>
        ),
      }}
      sx={{
        "& .MuiOutlinedInput-root": {
          borderRadius: "8px",
          height: 50,
          boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
        },
        "& .MuiInputLabel-root": {
          fontWeight: 500,
        },
      }}
    />
  );
};

export default FilterTextField;