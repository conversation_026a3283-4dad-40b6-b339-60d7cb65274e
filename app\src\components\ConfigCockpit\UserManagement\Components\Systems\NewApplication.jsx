import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  TextField,
  Dialog,
  DialogContent,
  DialogActions,
  DialogTitle,
  Checkbox,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  IconButton,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import CloseIcon from "@mui/icons-material/Close";
import { useDispatch, useSelector } from "react-redux";
import { getAllApplications } from "../../Action/action";
import {
  setApplications,
  setResponseMessage,
} from "../../../../../app/userManagementSlice";
import Loading from "../Loading";
import { Autocomplete } from "@mui/material";
import { CheckBox, CheckBoxOutlineBlank } from "@mui/icons-material";
import { APPLICATION_NAME_EXISTS, toastMessage } from "../../Utility/config";
import { destination_IWA } from "../../../../../destinationVariables";

const useStyle = makeStyles((theme) => ({
  newApplicationDialogTitle: {
    height: "3rem",
    display: "flex",
    alignItems: "center",
    borderBottom: "1px solid #d9d9d9",
    margin: 0,
    padding: 0,
    paddingLeft: 10,
  },
  newApplicationDialogActionsContainer: {
    height: "3rem",
    borderTop: "1px solid #d9d9d9",
  },
}));

const NewApplication = ({
  open,
  onClose,
  title,
  update,
  updatingApplication,
}) => {
  const classes = useStyle();
  const basicReducerState = useSelector((state) => state.userManagement);
  const dispatch = useDispatch();

  const [load, setLoad] = useState(false);
  const initialState = {
    name: "",
    label: "",
    description: "",
    applicationType: "",
    applications: [],
    applicationOwnerEmails: [],
    uniqueIds: [],
  };
  const [newApplication, setNewApplication] = useState(initialState);
  const [error, setError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const getApplicationIdByName = (appName) => {
    const application = basicReducerState?.applications?.find(
      (application) =>
        application?.name?.toLowerCase() === appName?.toLowerCase()
    );
    return application?.id || "-1";
  };

  const handleCreateApplication = () => {
    setLoad(true);
    const insertApplicationUrl = `/${destination_IWA}/api/v1/applications`;
    const insertApplicationPayload = {
      description: newApplication?.description,
      isActive: 1,
      isDeleted: 0,
      label: newApplication?.label,
      name: newApplication?.name,
      status: "Active",
      applications: newApplication?.applications
        ?.map((applicationName) => getApplicationIdByName(applicationName))
        ?.filter((application) => application !== "-1"),
      applicationType: newApplication?.applicationType,
      applicationOwnerEmails: newApplication?.applicationOwnerEmails,
      uniqueIds: newApplication?.uniqueIds,
    };
    const insertApplicationRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(insertApplicationPayload),
    };
    fetch(insertApplicationUrl, insertApplicationRequestParam)
      .then((res) => res.json())
      .then((app_data) => {
        setNewApplication(initialState);
        setLoad(false);
        getAllApplications(
          () => {
            setLoad(true);
          },
          (data) => {
            dispatch(setApplications(data?.data || []));
            setLoad(false);
            onClose();
            setError(false);
            setErrorMessage("");

            dispatch(
              setResponseMessage({
                open: true,
                status: app_data?.status ? "success" : "error",
                message: app_data?.status
                  ? toastMessage?.APPLICATION_CREATED
                  : toastMessage?.SOMETHING_WENT_WRONG,
              })
            );
          },
          (err) => {
            setLoad(false);
          }
        );
      })
      .catch((err) => {
        setLoad(false);
      });
  };
  const handleUpdateApplication = () => {
    setLoad(true);
    const updateApplicationUrl = `/${destination_IWA}/api/v1/applications/modify`;
    const updateApplicationPayload = {
      id: newApplication?.id,
      description: newApplication?.description,
      isActive: 1,
      isDeleted: 0,
      label: newApplication?.label,
      name: newApplication?.name,
      status: "Active",
      applications: newApplication?.applications
        ?.map((applicationName) => getApplicationIdByName(applicationName))
        ?.filter((application) => application !== "-1"),
      applicationType: newApplication?.applicationType,
      applicationOwnerEmails: newApplication?.applicationOwnerEmails,
      uniqueIds: newApplication?.uniqueIds,
    };
    const updateApplicationRequestParam = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateApplicationPayload),
    };
    fetch(updateApplicationUrl, updateApplicationRequestParam)
      .then((res) => res.json())
      .then((data) => {
        setNewApplication(initialState);
        setLoad(false);
        dispatch(
          setApplications(
            basicReducerState?.applications?.map((application) =>
              Number(application?.id) === Number(newApplication?.id)
                ? {
                    ...application,
                    name: newApplication?.name,
                    label: newApplication.label,
                    description: newApplication.description,
                    applications: updateApplicationPayload?.applications,
                    applicationType: newApplication?.applicationType,
                    applicationOwnerEmails:
                      newApplication?.applicationOwnerEmails,
                    uniqueIds: newApplication?.uniqueIds,
                  }
                : { ...application }
            ) || []
          )
        );
        onClose();
        setError(false);
        setErrorMessage("");

        dispatch(
          setResponseMessage({
            open: true,
            status: data?.status ? "success" : "error",
            message: data?.status
              ? toastMessage?.APPLICATION_UPDATED
              : toastMessage?.SOMETHING_WENT_WRONG,
          })
        );
      })
      .catch((err) => {
        setLoad(false);
      });
  };

  useEffect(() => {
    if (updatingApplication) {
      setNewApplication(updatingApplication);
    } else {
      setNewApplication(initialState);
    }
  }, [updatingApplication]);

  return (
    <Dialog
      open={open}
      onClose={() => {
        onClose();
        setNewApplication(initialState);
        setError(false);
        setErrorMessage("");
      }}
      fullWidth
      maxWidth="sm"
    >
      <DialogTitle sx={{
          height: "3rem",
          display: "flex",
          margin: 0,
          justifyContent: "space-between",
          alignItems: "center",
          padding: ".5rem",
          paddingLeft: "1rem",
          backgroundColor: "#EAE9FF40",
        }}>
        <Typography variant="h6">{title}</Typography>
        <IconButton
            sx={{ width: "max-content" }}
            onClick={onClose}
            children={<CloseIcon />}
          />
      </DialogTitle>

      <DialogContent sx={{ padding: "1rem 1rem" }}>
        <Loading load={load} />

        <Grid
          container
          sx={{
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Grid
            item
            xs
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Typography variant="body1">Name<span style={{ color: "red" }}>*</span>
            </Typography>
            <TextField
            placeholder="Enter Application Name"
              variant="outlined"
              fullWidth
              required
              size="small"
              value={newApplication?.name}
              onChange={(e) => {
                setNewApplication({ ...newApplication, name: e.target.value });
                if (update) {
                  const applications = basicReducerState?.applications?.filter(
                    (app) =>
                      app?.name?.toLowerCase() ===
                        e.target.value?.toLowerCase() &&
                      app?.id !== newApplication?.id
                  );
                  if (applications?.length > 0) {
                    setError(true);
                    setErrorMessage(APPLICATION_NAME_EXISTS);
                  } else {
                    setError(false);
                    setErrorMessage("");
                  }
                } else {
                  const applications = basicReducerState?.applications?.filter(
                    (app) =>
                      app?.name?.toLowerCase() === e.target.value?.toLowerCase()
                  );
                  if (applications?.length > 0) {
                    setError(true);
                    setErrorMessage(APPLICATION_NAME_EXISTS);
                  } else {
                    setError(false);
                    setErrorMessage("");
                  }
                }
              }}
              error={error}
              helperText={error && errorMessage}
            />
          </Grid>
          <Grid
            item
            xs
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Typography variant="body1">Label<span style={{ color: "red" }}>*</span></Typography>
            <TextField
            placeholder="Enter Label"
              variant="outlined"
              fullWidth
              required
              size="small"
              value={newApplication?.label}
              onChange={(e) =>
                setNewApplication({ ...newApplication, label: e.target.value })
              }
            />
          </Grid>

          <Grid
            item
            xs
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Typography variant="body1">Description<span style={{ color: "red" }}>*</span></Typography>
            <TextField
            placeholder="Enter Description"
              variant="outlined"
              fullWidth
              required
              size="small"
              value={newApplication?.description}
              onChange={(e) =>
                setNewApplication({
                  ...newApplication,
                  description: e.target.value,
                })
              }
            />
          </Grid>

          <Grid
            item
            xs
            sx={{
              marginTop: ".5rem",
            }}
          >
            <Typography variant="body1">Application Type<span style={{ color: "red" }}>*</span></Typography>
            <FormControl variant="outlined" size="small" fullWidth required>
              <Select
              placeholder="Select Application Type"
                labelId="applicationType"
                size="small"
                value={newApplication?.applicationType}
                onChange={(e) =>
                  setNewApplication({
                    ...newApplication,
                    applicationType: e.target.value,
                  })
                }
              >
                <MenuItem value={"CW"} style={{ fontSize: 12 }}>
                  Cherrywork System
                </MenuItem>
                <MenuItem value={"PROD"} style={{ fontSize: 12 }}>
                  Production System
                </MenuItem>
                <MenuItem value={"DEV"} style={{ fontSize: 12 }}>
                  Non-Production System
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* <Grid
            item
            xs
            sx={{
              marginTop: "0.5rem",
            }}
          >
            <Typography variant="body1">Allow Applications</Typography>
            <Autocomplete
              disableCloseOnSelect
              filterSelectedOptions
              multiple
              size="small"
              style={{ fontSize: 12 }}
              value={newApplication?.applications}
              onChange={(e, applications) => {
                setNewApplication({
                  ...newApplication,
                  applications: applications,
                });
              }}
              options={
                update
                  ? basicReducerState?.applications
                      ?.filter(
                        (application) => application?.id !== newApplication?.id
                      )
                      ?.map((application) => application?.name)
                  : basicReducerState?.applications?.map(
                      (application) => application?.name
                    )
              }
              getOptionLabel={(option) => option}
              renderOption={(props, option, { selected }) => (
                <li {...props}>
                  <Checkbox
                    icon={<CheckBoxOutlineBlank fontSize="small" />}
                    checkedIcon={<CheckBox color="primary" fontSize="small" />}
                    checked={selected}
                  />
                  <Typography style={{ fontSize: 12 }}>{option}</Typography>
                </li>
              )}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  style={{ fontSize: 12 }}
                />
              )}
            />
          </Grid> */}

          <Grid
            item
            xs
            sx={{
              marginTop: "0.5rem",
            }}
          >
            <Typography variant="body1">Owners Email Id</Typography>
          <Autocomplete
            filterSelectedOptions
            disableCloseOnSelect
            multiple
            size="small"
            style={{ fontSize: 12 }}
            value={newApplication?.applicationOwnerEmails}
            onChange={(e, applicationOwnerEmails) => {
              setNewApplication({
                ...newApplication,
                applicationOwnerEmails: applicationOwnerEmails,
              });
            }}
            options={basicReducerState?.users?.map((user) => user?.emailId)}
            getOptionLabel={(option) => option}
            renderOption={(props, option, { selected }) => (
              <li {...props}>
                <Checkbox
                  icon={<CheckBoxOutlineBlank fontSize="small" />}
                  checkedIcon={<CheckBox color="primary" fontSize="small" />}
                  checked={selected}
                />
                <Typography style={{ fontSize: 12 }}>{option}</Typography>
              </li>
            )}
            renderInput={(params) => (
              <TextField
                {...params}
                variant="outlined"
                style={{ fontSize: 12 }}
                placeholder={newApplication?.applicationOwnerEmails[0] ? `` : `Select Owners Email Id`}
              />
            )}
          />
          </Grid>

          <Grid
            item
            xs
            sx={{
              marginTop: "0.5rem",
            }}
          >
            <Typography variant="body1">Unique Ids</Typography>
          <Autocomplete
            filterSelectedOptions
            disableCloseOnSelect
            multiple
            size="small"
            style={{ fontSize: 12 }}
            value={newApplication?.uniqueIds}
            onChange={(e, uniqueIds) => {
              setNewApplication({
                ...newApplication,
                uniqueIds: uniqueIds,
              });
            }}
            options={["UserId", "EmailId", "Username"]}
            getOptionLabel={(option) => option}
            renderOption={(props, option, { selected }) => (
              <li {...props}>
                <Checkbox
                  icon={<CheckBoxOutlineBlank fontSize="small" />}
                  checkedIcon={<CheckBox color="primary" fontSize="small" />}
                  checked={selected}
                />
                <Typography style={{ fontSize: 12 }}>{option}</Typography>
              </li>
            )}
            renderInput={(params) => (
              <TextField
                {...params}
                variant="outlined"
                style={{ fontSize: 12 }}
                placeholder={newApplication?.uniqueIds[0] ? `` : `Select Unique Ids`}
              />
            )}
          />
          </Grid> 
        </Grid>
      </DialogContent>

      <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
        <Button
          size="small"
          variant="outlined"
          onClick={() => {
            setNewApplication(initialState);
            setError(false);
            setErrorMessage("");
            onClose();
          }}
          style={{ textTransform: "capitalize" }}
        >
          Cancel
        </Button>

        <Button
          size="small"
          variant={
            newApplication?.name?.length === 0 ||
            newApplication?.label?.length === 0 ||
            newApplication?.description?.length === 0 ||
            newApplication?.applicationType?.length === 0 ||
            load ||
            error
              ? "outlined"
              : "contained"
          }
          className="btn-ml"
          onClick={() => {
            if (update) {
              handleUpdateApplication();
            } else {
              handleCreateApplication();
            }
          }}
          disabled={
            newApplication?.name?.length === 0 ||
            newApplication?.label?.length === 0 ||
            newApplication?.description?.length === 0 ||
            newApplication?.applicationType?.length === 0 ||
            load ||
            error
          }
        >
          Submit
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default NewApplication;
