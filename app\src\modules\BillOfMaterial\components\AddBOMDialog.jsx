import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Grid
} from "@mui/material";
import { useSelector } from "react-redux";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";

const AddBOMDialog = ({ open, onClose, withReference, setWithReference, onProceed, t }) => {
  const allDropDownData = useSelector((state) => state.bom.dropDownData || {});
  const [material, setMaterial] = useState("");
  const [plant, setPlant] = useState("");
  const [bom, setBom] = useState("");

  return (
    <Dialog
      fullWidth
      open={open}
      maxWidth="lg"
      sx={{
        "&::webkit-scrollbar": {
          width: "1px",
        },
      }}
    >
      <DialogTitle
        sx={{
          justifyContent: "space-between",
          alignItems: "center",
          height: "max-content",
          padding: ".5rem",
          paddingLeft: "1rem",
          backgroundColor: "#EAE9FF",
          display: "flex",
        }}
      >
        <Typography variant="h6">{t("Add New BOM")}</Typography>
      </DialogTitle>
      <DialogContent
        sx={{
          padding: ".5rem 1rem",
          alignItems: "center",
          justifyContent: "center",
          margin: "0px 25px",
        }}
      >
        <FormControl component="fieldset" sx={{ paddingBottom: "2%" }}>
          <FormLabel
            component="legend"
            sx={{
              padding: "15px 0px",
              fontWeight: "600",
              fontSize: "15px",
            }}
          >
            {t("Do You Want To Continue")}
          </FormLabel>
          <RadioGroup
            row
            aria-label="profit-center-number"
            name="profit-center-number"
            value={withReference}
            onChange={(event) => setWithReference(event.target.value)}
          >
            <FormControlLabel
              value="yes"
              control={<Radio />}
              label={t("With Reference")}
            />
            <FormControlLabel
              value="no"
              control={<Radio />}
              label={t("Without Reference")}
            />
          </RadioGroup>
        </FormControl>
        <Grid container spacing={2} sx={{ marginTop: 1 }}>
          <Grid item xs={4}>
            <SingleSelectDropdown
              options={allDropDownData["Material"] || []}
              value={material}
              onChange={setMaterial}
              disabled={withReference === "no"}
              placeholder={t("SELECT MATERIAL")}
              minWidth={180}
              listWidth={266}
            />
          </Grid>
          <Grid item xs={4}>
            <SingleSelectDropdown
              options={allDropDownData["Plant"] || []}
              value={plant}
              disabled={withReference === "no"}
              onChange={setPlant}
              placeholder={t("SELECT PLANT")}
              minWidth={180}
              listWidth={266}
            />
          </Grid>
          <Grid item xs={4}>
            <SingleSelectDropdown
              options={allDropDownData["BOM"] || []}
              value={bom}
              disabled={withReference === "no"}
              onChange={setBom}
              placeholder={t("SELECT BOM")}
              minWidth={180}
              listWidth={266}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
        <Button
          sx={{
            width: "max-content",
            textTransform: "capitalize",
          }}
          onClick={onClose}
          variant="outlined"
        >
          {t("Cancel")}
        </Button>
        <Button
          className="button_primary--normal"
          type="save"
          disabled={withReference === "yes"}
          onClick={onProceed}
          variant="contained"
        >
          {t("Proceed")}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddBOMDialog; 